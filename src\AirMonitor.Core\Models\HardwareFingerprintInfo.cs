using System.Text.Json.Serialization;

namespace AirMonitor.Core.Models;

/// <summary>
/// 硬件指纹信息模型
/// 用于存储和传输硬件指纹相关信息
/// </summary>
public class HardwareFingerprintInfo
{
    /// <summary>
    /// CPU序列号
    /// </summary>
    [JsonPropertyName("cpuId")]
    public string CpuId { get; set; } = string.Empty;

    /// <summary>
    /// 主板序列号
    /// </summary>
    [JsonPropertyName("motherboardId")]
    public string MotherboardId { get; set; } = string.Empty;

    /// <summary>
    /// MAC地址（备用）
    /// </summary>
    [JsonPropertyName("macAddress")]
    public string MacAddress { get; set; } = string.Empty;

    /// <summary>
    /// 硬盘序列号（备用）
    /// </summary>
    [JsonPropertyName("diskId")]
    public string DiskId { get; set; } = string.Empty;

    /// <summary>
    /// 计算机名称
    /// </summary>
    [JsonPropertyName("computerName")]
    public string ComputerName { get; set; } = string.Empty;

    /// <summary>
    /// 用户名
    /// </summary>
    [JsonPropertyName("userName")]
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 操作系统信息
    /// </summary>
    [JsonPropertyName("osInfo")]
    public string OSInfo { get; set; } = string.Empty;

    /// <summary>
    /// 生成的硬件指纹
    /// </summary>
    [JsonPropertyName("fingerprint")]
    public string Fingerprint { get; set; } = string.Empty;

    /// <summary>
    /// 指纹生成时间
    /// </summary>
    [JsonPropertyName("generatedAt")]
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否有效
    /// </summary>
    [JsonPropertyName("isValid")]
    public bool IsValid { get; set; } = true;

    /// <summary>
    /// 备注信息
    /// </summary>
    [JsonPropertyName("remarks")]
    public string Remarks { get; set; } = string.Empty;

    /// <summary>
    /// 获取硬件信息摘要
    /// </summary>
    /// <returns>硬件信息摘要字符串</returns>
    public string GetSummary()
    {
        return $"计算机: {ComputerName}, CPU: {CpuId[..Math.Min(8, CpuId.Length)]}..., " +
               $"主板: {MotherboardId[..Math.Min(8, MotherboardId.Length)]}..., " +
               $"指纹: {Fingerprint}";
    }

    /// <summary>
    /// 验证硬件指纹是否匹配
    /// </summary>
    /// <param name="targetFingerprint">目标指纹</param>
    /// <returns>true表示匹配，false表示不匹配</returns>
    public bool MatchesFingerprint(string targetFingerprint)
    {
        return string.Equals(Fingerprint, targetFingerprint, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 检查硬件信息是否完整
    /// </summary>
    /// <returns>true表示完整，false表示不完整</returns>
    public bool IsComplete()
    {
        return !string.IsNullOrWhiteSpace(CpuId) &&
               !string.IsNullOrWhiteSpace(MotherboardId) &&
               !string.IsNullOrWhiteSpace(Fingerprint);
    }
}
