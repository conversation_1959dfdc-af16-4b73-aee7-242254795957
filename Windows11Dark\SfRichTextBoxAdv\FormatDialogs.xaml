<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:richtextboxadv="clr-namespace:Syncfusion.Windows.Controls.RichTextBoxAdv;assembly=Syncfusion.SfRichTextBoxAdv.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:tools_controls_shared="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.shared.WPF"
					xmlns:Syncfusion="http://schemas.syncfusion.com/wpf"
                    
                    xmlns:resources="clr-namespace:Syncfusion.Windows.Controls.RichTextBoxAdv;assembly=Syncfusion.SfRichTextBoxAdv.WPF">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfRichTextBoxAdv/SfRichTextBoxCommon.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/PrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatPrimaryButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <richtextboxadv:LineSpacingAtMaxValueConverter x:Key="LineSpacingAtMaxValueConverter"/>
    <richtextboxadv:LineSpacingAtMinValueConverter x:Key="LineSpacingAtMinValueConverter"/>

    <Style TargetType="richtextboxadv:ParagraphDialog">
        <Setter Property="Width" Value="500"/>
        <Setter Property="Height" Value="Auto"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="richtextboxadv:ParagraphDialog">
                    <Grid x:Name="PART_Root" Background="{StaticResource ContentBackground}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="12"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="11"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="12"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="16"/>
                            <RowDefinition Height="40"/>
                        </Grid.RowDefinitions>
                        <Grid Grid.Row="1" Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="60"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogGeneralHeader}" VerticalAlignment="Center"/>
                            <StackPanel Grid.Row="1">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogAlignment}" VerticalAlignment="Center"/>
                                <ComboBox Margin="5" Padding="4 2 2 2" x:Name="PART_TextAlignmentComboBox" Height="22" 
                                   ItemsSource="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphAlignmentColletion}"/>
                            </StackPanel>
                            <Grid Grid.Row="2" Grid.ColumnSpan="3">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogDirection}" VerticalAlignment="Center"></Label>
                                <RadioButton x:Name="PART_RtlRadioButton" GroupName="PART_ParagraphBidi" Grid.Column="1" Margin="5" 
                                             Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DirectionRightToLeft}"  HorizontalAlignment="Center" VerticalAlignment="Center" 
                                             ></RadioButton>
                                <RadioButton x:Name="PART_LtrRadioButton" GroupName="PART_ParagraphBidi" Grid.Column="2" Margin="5" 
                                             Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DirectionLeftToRight}"  HorizontalAlignment="Center" VerticalAlignment="Center" 
                                             ></RadioButton>
                            </Grid>
                            <Border Grid.Row="3" Grid.ColumnSpan="4" Height="1" Margin="5 12 5 5" Padding="4 2 2 2" BorderThickness="0 0 0 1" Opacity="0.5" VerticalAlignment="Center" Background="{StaticResource BorderAlt}"/>
                            <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogIndentHeader}" Grid.Row="4" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                            <StackPanel Grid.Row="5">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogLeftIndentValue}" VerticalAlignment="Center" />
                                <shared:UpDown x:Name="PART_LeftIndentButton" Margin="5" Padding="4 1 1 2" Height="25" Step="1"  MinValue="-1584" MaxValue="1584"/>
                            </StackPanel>
                            <StackPanel Grid.Row="5" Grid.Column="2">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogRightIndent}" VerticalAlignment="Center" />
                                <shared:UpDown x:Name="PART_RightIndentButton" Margin="5" Padding="4 1 1 2" Height="25" Step="1" MinValue="-1584" MaxValue="1584"/>
                            </StackPanel>
                            <StackPanel Grid.Row="6">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogSpecialIndent}" VerticalAlignment="Center" />
                                <ComboBox Margin="5" Padding="4 2 2 2" x:Name="PART_SpecialIndentComboBox" Height="22" >
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogNoIndent}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogFirstLineIndent}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogHangingIndent}"></ComboBoxItem>
                                </ComboBox>
                            </StackPanel>
                            <StackPanel Grid.Row="6" Grid.Column="2">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogSpecialIndentBy}" VerticalAlignment="Center" />
                                <shared:UpDown x:Name="PART_FirstLineIndentButton" Margin="5" Padding="4 1 1 2"  Height="25" Step="1" MinValue="0" MaxValue="1584"/>
                            </StackPanel>
                            <Border Grid.Row="7" Grid.ColumnSpan="4" Height="1" Margin="5 12 5 5" Padding="4 2 2 2" BorderThickness="0 0 0 1" Opacity="0.5" VerticalAlignment="Center" />
                            <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogSpacingHeader}" Grid.Row="7" VerticalAlignment="Center" />
                            <StackPanel Grid.Row="9">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogBeforeSpacing}" VerticalAlignment="Center" />
                                <shared:UpDown x:Name="PART_BeforeSpacingButton" Margin="5" Padding="4 1 1 2" Height="25" Step="6" MinValue="0" MaxValue="1584"/>
                            </StackPanel>
                            <StackPanel Grid.Row="9"  Grid.Column="2">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogAfterSpacing}" VerticalAlignment="Center" />
                                <shared:UpDown x:Name="PART_AfterSpacingButton" Margin="5" Padding="4 1 1 2" Height="25" Step="6"  MinValue="0" MaxValue="1584"/>
                            </StackPanel>
                            <StackPanel Grid.Row="10">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogLineSpacingType}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                <ComboBox x:Name="PART_LineSpacingTypeComboBox" Margin="5" Padding="4 2 2 2" Height="22"
                                    ItemsSource="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=LineSpacingColletion}">
                                </ComboBox>
                            </StackPanel>
                            <StackPanel Grid.Row="10"  Grid.Column="2">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ParagraphDialogLineSpacingAt}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                <shared:UpDown x:Name="PART_LinespacingButton" Margin="5" Padding="4 1 1 2" Height="25" Step="0.5" MinValue="{Binding SelectedItem, ElementName=PART_LineSpacingTypeComboBox, Converter={StaticResource LineSpacingAtMinValueConverter}}" MaxValue="{Binding SelectedItem, ElementName=PART_LineSpacingTypeComboBox, Converter={StaticResource LineSpacingAtMaxValueConverter}}"/>
                            </StackPanel>
                        </Grid>
                        <Border Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="3" Grid.RowSpan="2"  Width="Auto" BorderBrush="{StaticResource BorderAlt}" 
                                Background="{StaticResource PopupBackground}" BorderThickness="0 1 0 0">
                            <StackPanel Grid.Row="3" Grid.ColumnSpan="3" Orientation="Horizontal" HorizontalAlignment="Right">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button  Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOk}"
                                        x:Name="PART_ApplyParagraphFormatButton"
                                        Grid.Column="0" 
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 5 9 5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="52"
                                        IsDefault="True" 
                                        TabIndex="10"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        Style="{StaticResource WPFPrimaryButtonStyle}"/>
                                    <Button  Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxCancel}" 
                                        x:Name="PART_CancelButton"
                                        Grid.Column="1" 
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 5 9 5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="69" 
                                        TabIndex="11"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        />
                                </Grid>
                            </StackPanel>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="richtextboxadv:ListDialog">
        <Setter Property="Width" Value="500"/>
        <Setter Property="Height" Value="Auto"/>
        <Setter Property="Focusable"  Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="richtextboxadv:ListDialog">
                    <Grid Background="{StaticResource ContentBackground}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="12"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="11"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="12"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="40"/>
                        </Grid.RowDefinitions>
                        <Grid Grid.Row="1" Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="16"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="12"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="60"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListDialogListLevel}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                            <StackPanel Grid.Row="1">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListDialogChooseLevel}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                <ComboBox Margin="5" Padding="4 2 2 2" x:Name="PART_ListLevelComboBox" Height="22" >
                                    <ComboBox.Items>
                                        <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListLevelOne}"></ComboBoxItem>
                                        <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListLevelTwo}"></ComboBoxItem>
                                        <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListLevelThree}"></ComboBoxItem>
                                        <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListLevelFour}"></ComboBoxItem>
                                        <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListLevelFive}"></ComboBoxItem>
                                        <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListLevelSix}"></ComboBoxItem>
                                        <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListLevelSeven}"></ComboBoxItem>
                                        <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListLevelEight}"></ComboBoxItem>
                                        <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListLevelNine}"></ComboBoxItem>
                                    </ComboBox.Items>
                                </ComboBox>
                            </StackPanel>
                            <Border Grid.Row="2" Grid.ColumnSpan="4" Height="1" Margin="5 12 5 5" Padding="4 2 2 2" BorderThickness="0 0 0 1" Opacity="0.5" VerticalAlignment="Center" Background="{TemplateBinding BorderBrush}"/>
                            <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListDialogNumberFormat}" Grid.Row="3" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                            <StackPanel Grid.Row="4">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListDialogNumberStyle}" VerticalAlignment="Center"/>
                                <ComboBox Margin="5" Padding="4 2 2 2" x:Name="PART_ListLevelPatternComboBox" Height="22">
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ArabicList}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=UpRomanList}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=LowRomanList}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=UpLetterList}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=LowLetterList}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=NumberList}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=LeadingZeroList}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BulletList}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=OrdinalList}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=OrdinalTextList}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=SpecialList}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FarEastList}"></ComboBoxItem>
                                </ComboBox>
                            </StackPanel>
                            <StackPanel Grid.Row="4" Grid.Column="2">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListDialogFormattingNumber}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                <Border BorderBrush="{TemplateBinding BorderBrush}" Background="{TemplateBinding Background}" BorderThickness="1" Margin="5" Height="25">
                                    <Grid>
                                        <ComboBox x:Name="PART_BulletsComboBox"  BorderThickness="0" Padding="4 2 2 2" Visibility="Visible">
                                            <TextBlock FontFamily="Wingdings" Text="&#xf076;" FontSize="{StaticResource Windows11Dark.HeaderTextStyle}"/>
                                            <TextBlock FontFamily="Wingdings" Text="&#xf0d8;" FontSize="{StaticResource Windows11Dark.HeaderTextStyle}"/>
                                            <TextBlock FontFamily="Wingdings" Text="&#xf0a7;" FontSize="{StaticResource Windows11Dark.HeaderTextStyle}"/>
                                            <TextBlock FontFamily="Wingdings" Text="&#xf0fc;" FontSize="{StaticResource Windows11Dark.HeaderTextStyle}"/>
                                            <TextBlock FontFamily="Symbol" Text="&#xf0b7;" FontSize="{StaticResource Windows11Dark.HeaderTextStyle}"/>
                                            <TextBlock FontFamily="Symbol" Text="&#xf06f;&#x0020;" FontSize="{StaticResource Windows11Dark.HeaderTextStyle}"/>
                                            <TextBlock FontFamily="Symbol" Text="&#xf0a8;" FontSize="{StaticResource Windows11Dark.HeaderTextStyle}"/>
                                            <TextBlock FontFamily="Symbol" Text="&#xf0ae;" FontSize="{StaticResource Windows11Dark.HeaderTextStyle}"/>
                                            <TextBlock FontFamily="Symbol" Text="&#xf0a7;" FontSize="{StaticResource Windows11Dark.HeaderTextStyle}"/>
                                        </ComboBox>
                                        <TextBox x:Name="PART_NumberFormatTextBox" Foreground="{TemplateBinding Foreground}" BorderThickness="1" Padding="4 1 1 2"
                                                 FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                 Margin="0 0 25 0" Text=""/>
                                    </Grid>
                                </Border>
                            </StackPanel>
                            <StackPanel Grid.Row="5">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListDialogStartAt}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                <shared:UpDown x:Name="PART_StartAtButton" Margin="5" Padding="4 1 1 2" Height="25" NumberDecimalDigits="0" Step="1"  MinValue="0" MaxValue="50"/>
                            </StackPanel>
                            <StackPanel Grid.Row="5" Grid.Column="2">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListDialogRestart}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                <ComboBox Margin="5" Padding="4 2 2 2" x:Name="PART_RestartLevelComboBox" Height="22"  />
                            </StackPanel>
                            <Border Grid.Row="6" Grid.ColumnSpan="4" Height="1" Margin="5 12 5 5" Padding="4 2 2 2" BorderThickness="0 0 0 1" Opacity="0.5" VerticalAlignment="Center" Background="{TemplateBinding BorderBrush}"/>
                            <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListDialogIndents}" Grid.Row="7" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                            <StackPanel Grid.Row="8">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListDialogLeftIndents}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                <shared:UpDown x:Name="PART_LeftIndentButton" Margin="5" Padding="4 1 1 2" Height="25" Step="6" MinValue="-1583" MaxValue="1584" NumberDecimalDigits="0"/>
                            </StackPanel>
                            <StackPanel Grid.Row="8"  Grid.Column="2">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListDialogFirstLineIndent}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                <shared:UpDown x:Name="PART_FirstLineIndentButton" Margin="5" Padding="4 1 1 2" Height="25" NumberDecimalDigits="0" Step="6" MinValue="-1583" MaxValue="1584"/>
                            </StackPanel>
                            <StackPanel Grid.Row="9">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ListDialogNumberFollow}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                <ComboBox Margin="5" Padding="4 2 2 2" x:Name="PART_FollowCharacterComboBox" Height="22"  >
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FollowNumberWithTab}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FollowNumberWithSpace}"></ComboBoxItem>
                                    <ComboBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FollowNumberWithNone}"></ComboBoxItem>
                                </ComboBox>
                            </StackPanel>
                        </Grid>
                        <Border Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3"  Width="Auto" BorderBrush="{StaticResource BorderAlt}" Background="{StaticResource PopupBackground}" BorderThickness="0 1 0 0">
                            <Grid Grid.Row="2" 
                                      Grid.Column="0"
                                      Grid.ColumnSpan="3"
                                      Height="33">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="250"/>
                                    <ColumnDefinition Width="250"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0" Margin="3 0 -4 0" Orientation="Horizontal" HorizontalAlignment="Left">
                                    <Button x:Name="PART_FontButton" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogFontText}" 
                                                    Height="24"
                                                    HorizontalContentAlignment="Center"
                                                    HorizontalAlignment="Left" 
                                                    FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                    FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                                    Padding="12 2 12 2"
                                                    Margin="6 -5 6 0"
                                                    Width="70"/>
                                </StackPanel>
                                <StackPanel Grid.Column="1"  Orientation="Horizontal" HorizontalAlignment="Right" Margin="0 0 4 0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <Button x:Name="PART_ApplyListButton" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOk}" 
                                        Grid.Column="0"
                                        Width="52" 
                                        IsDefault="True" 
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 -5 6 0"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        TabIndex="10"
                                        Style="{StaticResource WPFPrimaryButtonStyle}"/>
                                        <Button x:Name="PART_CancelButton" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxCancel}" 
                                        Grid.Column="1"
                                        Width="69" 
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 -4 6 0"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        TabIndex="11"
                                        />
                                    </Grid>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="richtextboxadv:FontDialog">
        <Setter Property="Width" Value="400"/>
        <Setter Property="Height" Value="Auto"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="richtextboxadv:FontDialog">
                    <Grid x:Name="PART_Root" Background="{StaticResource ContentBackground}">
                        <Grid.Resources>
                            <richtextboxadv:StringFontFamilyConverter x:Key="StringFontFamilyConverter"/>
                            <richtextboxadv:BrushOpacityConverter x:Key="BrushOpacityConverter"/>
                        </Grid.Resources>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="12"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="11"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="12"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="40"/>
                        </Grid.RowDefinitions>
                        <Grid Grid.Row="1" Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="12"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="12"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="12"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="12"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel>
                                <Label    Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogFontText}"
                                          VerticalAlignment="Center"
                                          Padding="1 5 5 5"
                                          FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                          FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <TextBox  x:Name="PART_FontFamilyTextBox" 
                                          Text="{Binding ElementName=PART_FontFamilyListBox, Path=SelectedValue, Mode=OneWay}"
                                          Width="150" 
                                          Height="22"  
                                          FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                          FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <ListBox x:Name="PART_FontFamilyListBox"
                                          BorderThickness="1" 
                                          Width="150"
                                          Height="121" 
                                          TabIndex="1">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock x:Name="PART_ListBoxItem" 
                                                       FontSize="{StaticResource Windows11Dark.TitleTextStyle}"
                                                       Text="{TemplateBinding Content}" 
                                                       FontFamily="{Binding ElementName=PART_ListBoxItem, 
                                                       Path=Text, 
                                                       Converter={StaticResource StringFontFamilyConverter}}"/>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>
                            </StackPanel>
                            <StackPanel Grid.Column="2">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogFontStyle}" 
                                       VerticalAlignment="Center" Padding="1 5 5 5"  
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <TextBox x:Name="PART_FontStyleTextBox" 
                                         Text="{Binding ElementName=PART_FontStyleListBox, Path=SelectedValue, Mode=OneWay}" 
                                         Width="129"
                                         Height="22"
                                         FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                         FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <ListBox x:Name="PART_FontStyleListBox" 
                                         Width="129"
                                         Height="121" 
                                         BorderThickness="1"  
                                         SelectedValue="{Binding FontStyle}" 
                                         TabIndex="2">
                                    <ListBox.Items>
                                        <sys:String>Regular</sys:String>
                                        <sys:String>Bold</sys:String>
                                        <sys:String>Italic</sys:String>
                                        <sys:String>BoldItalic</sys:String>
                                    </ListBox.Items>
                                </ListBox>
                            </StackPanel>
                            <StackPanel Grid.Column="4">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogFontSize}" 
                                       VerticalAlignment="Center" 
                                       Padding="1 5 5 5"
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <TextBox x:Name="PART_FontSizeTextBox"
                                       Text="{Binding ElementName=PART_FontSizeListBox, Path=SelectedValue, Mode=OneWay}"
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                       Width="74"
                                       Height="22"/>
                                <ListBox x:Name="PART_FontSizeListBox"
                                       BorderThickness="1" 
                                       Width="74"
                                       Height="121"
                                       TabIndex="3">
                                    <ListBox.Items>
                                        <sys:Double>8</sys:Double>
                                        <sys:Double>9</sys:Double>
                                        <sys:Double>10</sys:Double>
                                        <sys:Double>11</sys:Double>
                                        <sys:Double>12</sys:Double>
                                        <sys:Double>14</sys:Double>
                                        <sys:Double>16</sys:Double>
                                        <sys:Double>18</sys:Double>
                                        <sys:Double>20</sys:Double>
                                        <sys:Double>24</sys:Double>
                                        <sys:Double>26</sys:Double>
                                        <sys:Double>28</sys:Double>
                                        <sys:Double>36</sys:Double>
                                        <sys:Double>48</sys:Double>
                                        <sys:Double>72</sys:Double>
                                        <sys:Double>96</sys:Double>
                                    </ListBox.Items>
                                </ListBox>
                            </StackPanel>
                            <StackPanel Grid.Row="2" Grid.Column="0">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogFontColor}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"
                                       Padding="0" 
                                       Height="14"                           
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"/>
                                <tools_controls_shared:ColorPickerPalette x:Name="PART_ColorPicker"       
                                                                              Width="129" 
                                                                              Height="24" 
                                                                              BorderThickness="1"
                                                                              BorderBrush="{StaticResource BorderAlt}"
                                                                              Margin="0 8 5 5" 
                                                                              Padding="3"
                                                                              HorizontalAlignment="Left"
                                                                              TabIndex="4"
                                                                              VerticalAlignment="Center" 
                                                                              MoreColorOptionVisibility="Collapsed"
                                                                              BlackWhiteVisibility="Both">
                                </tools_controls_shared:ColorPickerPalette>
                            </StackPanel>
                            <StackPanel Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2"  >
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogUnderline}"
                                          VerticalAlignment="Center"
                                          Padding="0"
                                          Margin="4 0 0 0"
                                          Height="14"
                                          Foreground="{TemplateBinding Foreground}"
                                          FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                          FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <ComboBox x:Name="PART_UnderlineStyleComboBox"  
                                          Width="129" 
                                          Height="24" 
                                          HorizontalAlignment="Left"
                                          Margin="4 7 0 0" 
                                          HorizontalContentAlignment="Left" 
                                          Padding="4 2 2 2"
                                          Focusable="False" 
                                          TabIndex="5">
                                    <ComboBox.Items>
                                        <ComboBoxItem Height="20" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogNoUnderline}"></ComboBoxItem>
                                        <ComboBoxItem Height="20" Padding="6 8" VerticalContentAlignment="Center">
                                            <Border Height="2" Width="160"
                                                    VerticalAlignment="Center" 
                                                    Background="{TemplateBinding Foreground}"/>
                                        </ComboBoxItem>
                                    </ComboBox.Items>
                                </ComboBox>
                            </StackPanel>
                            <Border Grid.Row="3" Grid.ColumnSpan="5" Height="1" Margin="5 5 5 5" Padding="4 2 2 2" BorderThickness="0 0 0 1" Opacity="0.5" VerticalAlignment="Center"/>
                            <StackPanel Grid.Row="4" Grid.ColumnSpan="5" >
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogEffects}" Margin="1 0 0 8" VerticalAlignment="Center"
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                       Foreground="{TemplateBinding Foreground}"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <StackPanel>
                                        <CheckBox  x:Name="PART_SingleStrikeCheckBox" 
                                                   Margin="0 5 5 5"  
                                                   Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogStrikeThrough}"
                                                   FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                   FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                                   TabIndex="6"/>
                                        <CheckBox  x:Name="PART_DoubleStrikeCheckBox"                                                  
                                                   Margin="0 5 5 5"  
                                                   Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogDoubleStrikeThrough}"
                                                   FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                   FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                                   TabIndex="7"/>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1" >
                                        <CheckBox x:Name="PART_SuperScriptCheckBox" 
                                                  Margin="4" 
                                                  Height="24"
                                                  Width="142"
                                                  Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogSuperscript}"
                                                  FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                  FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                                  TabIndex="8"/>
                                        <CheckBox x:Name="PART_SubScriptCheckBox" 
                                                  Margin="4 0 4 4" 
                                                  Height="24"
                                                  Width="142"
                                                  Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogSubscript}"
                                                  FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                  FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                                  TabIndex="9"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                            <StackPanel Grid.Row="6" Grid.ColumnSpan="5" >
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=FontDialogPreview}" HorizontalAlignment="Left"  Foreground="{TemplateBinding Foreground}"
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <Border Width="376" BorderBrush="{StaticResource BorderAlt}" Background="{TemplateBinding Background}" BorderThickness="1" >
                                    <Canvas Width="376" Height="56" HorizontalAlignment="Stretch" VerticalAlignment="Stretch"  Margin="0" >
                                        <TextBlock x:Name="PART_PreviewTextBlock" ClipToBounds="True" Height="Auto" Width="Auto" HorizontalAlignment="Center" VerticalAlignment="Center" Padding="6 0 6 0" Margin="-40 0 0 0"/>
                                    </Canvas>
                                </Border>
                            </StackPanel>
                        </Grid>
                        <Border Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3" Grid.RowSpan="2" Width="Auto" BorderBrush="{StaticResource BorderAlt}" Background="{StaticResource PopupBackground}" BorderThickness="0 1 0 0">
                            <StackPanel Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3" Width="Auto" Orientation="Horizontal" HorizontalAlignment="Right">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyFontFormatButton" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOk}"
                                        Grid.Column="0"
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 5 9 5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="52"
                                        IsDefault="True" 
                                        TabIndex="9"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        Style="{StaticResource WPFPrimaryButtonStyle}"/>
                                    <Button x:Name="PART_CancelButton" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxCancel}"
                                        Grid.Column="1"
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 5 9 5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="69" 
                                        TabIndex="10"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        />
                                </Grid>
                            </StackPanel>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="richtextboxadv:BulletsAndNumberingDialog">
        <Setter Property="Width" Value="322"/>
        <Setter Property="Height" Value="Auto"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="richtextboxadv:BulletsAndNumberingDialog">
                    <Grid x:Name="PART_Root" Background="{StaticResource ContentBackground}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="12"></RowDefinition>
                            <RowDefinition Height="*"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="20"></RowDefinition>
                            <RowDefinition Height="40"></RowDefinition>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20"></ColumnDefinition>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="20"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <TabControl Grid.Row="1" Grid.Column="1" Height="220">
                            <TabItem Header="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNumberingToolTip}">
                                <Grid Background="{StaticResource ContentBackground}">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="12"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="12"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="12"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="12"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid Grid.Row="2" Grid.Column="1" Background="{StaticResource ContentBackground}">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <ToggleButton x:Name="PART_NoNumberingButton" 
                                                      Grid.Column="0" 
                                                      BorderThickness="0" 
                                                      Background="Transparent" 
                                                      Padding="4" 
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                      HorizontalContentAlignment="Left" >
                                            <ToggleButton.Content>
                                                <Grid x:Name="Numbering_None" 
                                              HorizontalAlignment="Left" 
                                              Height="76"
                                              VerticalAlignment="Top" 
                                              Width="76">
                                                    <Path  
                                                  Fill="Transparent" 
                                                  Margin="0.5" 
                                                  Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path Fill="{StaticResource IconColor}"
                                                  Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L76,0 76,76 0,76 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path Fill="{StaticResource IconColor}"
                                                  Margin="24.436,33.863,23.809,33.228" 
                                                  Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M11.200999,3.0420207 C10.604999,3.0420207 10.159999,3.2690196 9.8609991,3.7240174 9.5639982,4.1790152 9.4159985,4.7560122 9.4159985,5.4560087 L9.4159985,5.6010079 C9.4159985,6.3130043 9.5639982,6.8920014 9.8609991,7.3389995 10.159999,7.7859972 10.608999,8.0089962 11.212999,8.0089962 11.808999,8.0089962 12.257999,7.7849972 12.560999,7.3359993 12.860999,6.8870018 13.011999,6.3090045 13.011999,5.6010079 L13.011999,5.4560087 C13.011999,4.7560122 12.860999,4.1790152 12.556999,3.7240174 12.251999,3.2690196 11.801,3.0420207 11.200999,3.0420207 z M25.095003,3.0420025 C24.671,3.0420024 24.315996,3.2120464 24.022997,3.5520124 23.731996,3.8920395 23.545999,4.3330188 23.463998,4.8760488 L26.640002,4.8760488 26.640002,4.7200429 C26.640002,4.2320056 26.511004,3.8310043 26.253001,3.5160019 25.994998,3.1990459 25.609004,3.0420024 25.095003,3.0420025 z M25.095003,2.1370339 C25.979998,2.1370336 26.644,2.3960059 27.089,2.9150494 27.534,3.4340314 27.755001,4.1440539 27.755001,5.0450559 L27.755001,5.7750366 23.429002,5.7750366 C23.445001,6.4430053 23.611001,6.9820073 23.926996,7.393018 24.240999,7.8040292 24.693003,8.0090463 25.275004,8.0090463 25.681002,8.0090463 26.032,7.9520395 26.328998,7.84004 26.624003,7.7270029 26.890002,7.5680063 27.128002,7.3620121 L27.570003,8.1050546 C27.331005,8.3390028 27.027003,8.5300429 26.654002,8.6820204 26.282,8.8320448 25.824,8.9080336 25.275004,8.9080336 24.359003,8.9080336 23.633996,8.6110365 23.102998,8.0180185 22.572,7.4240239 22.306001,6.6370361 22.306,5.6550412 L22.306,5.3830075 C22.306001,4.434032 22.578996,3.6550399 23.124002,3.0480449 23.669,2.4400123 24.324999,2.1370336 25.095003,2.1370339 z M11.200999,2.1370253 C12.118999,2.1370253 12.835999,2.4440237 13.353999,3.0570209 13.870999,3.6700176 14.129,4.4700136 14.129,5.4560087 L14.129,5.6010079 C14.129,6.5900033 13.870999,7.3889992 13.355,7.9969962 12.841999,8.6049931 12.126999,8.9089916 11.212999,8.9089916 10.296999,8.9089916 9.579999,8.6049931 9.0639982,7.9969962 8.5509987,7.3889992 8.2929983,6.5900033 8.2929983,5.6010079 L8.2929983,5.4560087 C8.2929983,4.4700136 8.5509987,3.6700176 9.0639982,3.0570209 9.579999,2.4440237 10.292999,2.1370253 11.200999,2.1370253 z M18.642,2.136981 C19.355,2.1369811 19.900001,2.3379822 20.279001,2.7399844 20.656001,3.1429867 20.845001,3.7839901 20.845001,4.6649947 L20.845001,8.7870176 19.722,8.7870176 19.722,4.6899955 C19.722001,4.0899919 19.609,3.6689895 19.382,3.4279879 19.156,3.1869869 18.798,3.0659862 18.31,3.0659861 17.944999,3.0659862 17.627999,3.1529868 17.360999,3.3249875 17.092999,3.4979886 16.875999,3.7389899 16.711998,4.0499917 L16.711998,8.7870176 15.588998,8.7870176 15.588998,2.2569817 16.596999,2.2569817 16.674999,3.2349871 16.692999,3.239987 C16.905999,2.8909853 17.176999,2.6189838 17.504999,2.4259828 17.832999,2.2329816 18.211999,2.1369811 18.642,2.136981 z M0,0 L1.1169987,0 5.4800014,6.9160097 5.4980011,6.9160097 5.4980011,0 6.6150017,0 6.6150017,8.7870123 5.4980011,8.7870123 1.1350002,1.8640026 1.1169987,1.8640026 1.1169987,8.7870123 0,8.7870123 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                        <ToggleButton x:Name="PART_NumberDotNumberingButton" 
                                                      Grid.Column="1" 
                                                      BorderThickness="0"
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                      Background="Transparent" 
                                                      Padding="4" 
                                                      HorizontalContentAlignment="Left" >
                                            <ToggleButton.Content>
                                                <Grid x:Name="Numbering_Number_Dot" 
                                                      HorizontalAlignment="Left" 
                                                      Height="76" 
                                                      VerticalAlignment="Top" 
                                                      Width="76">
                                                    <Path  
                                                        Fill="Transparent" 
                                                        Margin="0.5" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L76,0 76,76 0,76 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        HorizontalAlignment="Left" 
                                                        Margin="6.925,12.339,0,17.007" 
                                                        Stretch="Fill" 
                                                        Width="8.244">
                                                        <Path.Data>
                                                            <PathGeometry>M7.1420118,45.376981 L8.2440114,45.376981 8.2440114,46.530981 7.1420118,46.530981 z M2.7070026,37.877029 C3.5110023,37.877029 4.1420021,38.083028 4.5990024,38.495026 5.0560019,38.907024 5.2850019,39.508022 5.285002,40.297019 5.2850019,40.660017 5.1780019,41.014015 4.963002,41.357013 4.7480021,41.701012 4.4220021,41.967011 3.9840024,42.15401 4.5110021,42.32201 4.883002,42.584008 5.0970023,42.939007 5.3120018,43.295005 5.4200019,43.701003 5.420002,44.158001 5.4200019,44.950998 5.1700019,45.564995 4.6700022,46.000993 4.1700022,46.436991 3.5170023,46.65399&#xd;&#xa;2.7130027,46.65399 1.9240026,46.65399 1.275003,46.445991 0.76700258,46.029993 0.26000309,45.613995 0.0060033798,45.028998 0.0060033798,44.275001 L1.0950031,44.275001 C1.0950031,44.739999 1.2380028,45.106997 1.5230026,45.376996 1.8080029,45.645995 2.2050028,45.780994 2.7130027,45.780994 3.2320025,45.780994 3.6330023,45.648995 3.9140022,45.385996 4.1950021,45.121997 4.3360021,44.720999 4.3360023,44.182001 4.3360021,43.639004 4.2070022,43.242005 3.949002,42.992007 3.6910024,42.742008 3.2810025,42.617008 2.7190032,42.617008 L1.7400026,42.617008 1.7400026,41.750012 2.7190032,41.750012 C3.2580025,41.750012 3.6390023,41.621012 3.8640022,41.363014 4.0890021,41.105015 4.2010021,40.742016 4.2010024,40.273019 4.2010021,39.77002 4.0780022,39.391022 3.8320022,39.137024 3.5860023,38.883025 3.2110023,38.756025 2.7070026,38.756025 2.2340026,38.756025 1.8590026,38.889025 1.5820031,39.154024 1.3040028,39.420022 1.1660032,39.779021 1.1660028,40.232018 L0.082003593,40.232018 C0.082003117,39.557022 0.32200336,38.995024 0.80300379,38.548026 1.2830029,38.101028 1.9180026,37.877029 2.7070026,37.877029 z M7.1420013,26.377007 L8.2439969,26.377007 8.2439969,27.531015 7.1420013,27.531015 z M2.8469973,18.876986 C3.6209977,18.876986 4.2309973,19.094986 4.6789968,19.532986 5.1259966,19.969986 5.3489965,20.542986 5.3489965,21.249987 5.3489965,21.721987 5.2129966,22.185987 4.9389961,22.640989 4.665997,23.095989 4.2729971,23.60499 3.761997,24.16799 L1.4709992,26.657991 5.6599964,26.657991 5.6599964,27.530993 0.16400003,27.530993 0.16400003,26.762993 2.9589972,23.651989 C3.4739978,23.081989 3.8199975,22.633989 3.9959974,22.306988 4.1719973,21.980988 4.2599974,21.641987 4.2599976,21.290987 4.2599974,20.844986 4.1339974,20.477987 3.8819973,20.188986 3.6299977,19.899986 3.2849979,19.755985 2.8469973,19.755985 2.2379985,19.755985 1.7919989,19.905987 1.5089989,20.206985 1.2249994,20.507986 1.0839992,20.940987 1.0839996,21.507988 L0,21.507988 C0,20.745987 0.24799967,20.116985 0.74399948,19.620985 1.2399993,19.124985 1.9409986,18.876986 2.8469973,18.876986 z M7.142002,7.3770065 L8.2439969,7.3770065 8.2439969,8.5310135 7.142002,8.5310135 z M3.539005,0 L3.539005,8.5309944 2.4550076,8.5309944 2.4550076,1.2419968 0.71501112,1.2889977 0.71501112,0.59199905 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Margin="18,16,6,20" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,38 L52,38 52,40 0,40 z M0,19 L52,19 52,21 0,21 z M0,0 L52,0 52,2 0,2 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                        <ToggleButton x:Name="PART_LowLetterDotNumberingButton" 
                                                      Grid.Column="2" 
                                                      BorderThickness="0" 
                                                      Background="Transparent" 
                                                      Padding="4" 
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                      HorizontalContentAlignment="Left" >
                                            <ToggleButton.Content>
                                                <Grid 
                                                    x:Name="Numbering_Lowletter_Dot" 
                                                    HorizontalAlignment="Right" 
                                                    Height="76"
                                                    VerticalAlignment="Top" 
                                                    Width="76">
                                                    <Path Fill="Transparent" 
                                                  Margin="0.5" 
                                                  Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L76,0 76,76 0,76 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        HorizontalAlignment="Left" 
                                                        Margin="6.948,14.413,0,17.013" 
                                                        Stretch="Fill" 
                                                        Width="8.28">
                                                        <Path.Data>
                                                            <PathGeometry>M6.7260109,43.302991 L7.8280107,43.302991 7.8280107,44.456992 6.7260109,44.456992 z M2.8240008,37.99999 C3.5700009,37.99999 4.177001,38.214991 4.6430008,38.643993 5.1100011,39.073995 5.3450011,39.624997 5.3490012,40.296999 L4.318001,40.296999 C4.3140008,39.889998 4.174001,39.552996 3.8960011,39.282995 3.6190009,39.013994 3.2610006,38.878994 2.8240008,38.878994 2.1950006,38.878994 1.7490005,39.096994 1.4850001,39.531996 1.2210002,39.967998 1.0900002,40.512 1.0900006,41.164003 L1.0900006,41.410003 C1.0900002,42.078006 1.2200003,42.627008 1.4819999,43.05601 1.7440004,43.486012 2.1910005,43.701012 2.8240008,43.701012 3.2260008,43.701012 3.5750008,43.584012 3.8700008,43.349011 4.1650009,43.11501 4.3140008,42.818009 4.318001,42.459008 L5.3490012,42.459008 C5.3450011,43.03701 5.0950011,43.534012 4.5960011,43.950013 4.098001,44.366015 3.5080009,44.574016 2.8240008,44.574016 1.8940005,44.574016 1.1910005,44.278015 0.71499968,43.686012 0.23800039,43.09501 0,42.336007 0,41.410003 L0,41.164003 C0,40.241999 0.23800039,39.483996 0.71499968,38.889994 1.1910005,38.296991 1.8940005,37.99999 2.8240008,37.99999 z M7.1780095,24.303017 L8.2799587,24.303017 8.2799587,25.457026 7.1780095,25.457026 z M2.9070091,19.90303 C2.5280094,19.90303 2.2140098,19.99103 1.9660091,20.16703 1.7180099,20.34203 1.52001,20.58503 1.3720102,20.89303 L1.3720102,23.676031 C1.5240102,23.989033 1.72401,24.233032 1.9720106,24.409033 2.2200098,24.585032 2.5360093,24.672031 2.9190087,24.672031 3.4850085,24.672031 3.9010081,24.479033 4.1670079,24.092031 4.4320078,23.706032 4.5650077,23.182032 4.5650079,22.522032 L4.5650079,22.399031 C4.5650077,21.649031 4.4300077,21.045031 4.1610081,20.58803 3.8910081,20.131031 3.4730086,19.90303 2.9070091,19.90303 z M0.28201103,16.317028 L1.3720102,16.317028 1.3720102,19.87903 1.38901,19.885031 C1.5850101,19.596029 1.82901,19.377029 2.1220098,19.22603 2.4150095,19.076029 2.7660091,19.00103 3.1760087,19.00103 3.9690082,19.00103 4.5800076,19.30703 5.0070069,19.920031 5.435007,20.534031 5.6490068,21.360031 5.6490072,22.399031 L5.6490072,22.522032 C5.6490068,23.460032 5.435007,24.203032 5.0070069,24.752033 4.5800076,25.300032 3.9730082,25.575033 3.188009,25.575033 2.7620091,25.575033 2.3970094,25.495033 2.0920095,25.335033 1.7880101,25.174032 1.5320101,24.934032 1.3250098,24.614033 L1.2190108,25.458033 0.28201103,25.458033 z M6.9549709,5.3030167 L8.0569813,5.3030167 8.0569813,6.4570236 6.9549709,6.4570236 z M2.8069746,3.5040283 C2.3029714,3.5040283 1.9069686,3.6220284 1.6179676,3.8590279 1.3289652,4.0950317 1.1839643,4.3790321 1.1839643,4.7110367 1.1839643,5.0040398 1.2749648,5.2330399 1.4569664,5.3970413 1.6379671,5.5610428 1.9109688,5.6430435 2.2739706,5.6430435 2.6799741,5.6430435 3.0449762,5.5490417 3.3669782,5.3620415 3.6889806,5.1740379 3.921982,4.9440384 4.0669835,4.6700363 L4.0669835,3.5040283 z M2.7489743,0 C3.4789791,0 4.0619829,0.18000031 4.4979858,0.53900528 4.9329886,0.89900589 5.1509901,1.4260101 5.1509905,2.1220169 L5.1509905,5.1800385 C5.1509901,5.4070396 5.1629902,5.6230431 5.1859901,5.8300438 5.2099905,6.038044 5.2529907,6.2470474 5.3149912,6.4570503 L4.1959839,6.4570503 C4.1569836,6.258049 4.1279833,6.0940475 4.1079834,5.9650459 4.0879831,5.8360443 4.0749831,5.7020416 4.0669835,5.5610428 3.8399816,5.8580437 3.5589797,6.1010475 3.2229779,6.2900467 2.8869753,6.4800491 2.5179729,6.5750504 2.1159701,6.5750504 1.4479656,6.5750504 0.9449625,6.4100494 0.6069603,6.0800476 0.26895809,5.7490425 0.099956989,5.2860413 0.099956512,4.6880341 0.099956989,4.0710297 0.34195852,3.5940285 0.82696152,3.2580261 1.3109651,2.9220238 1.9829693,2.7540207&#xd;&#xa;2.841975,2.7540207 L4.0669835,2.7540207 4.0669835,2.1100159 C4.066983,1.7230148 3.9479823,1.4210129 3.7099807,1.2050095 3.470979,0.98800659 3.1309769,0.87900543 2.6899738,0.87900543 2.2799711,0.87900543 1.9489689,0.97600937 1.6969681,1.1690102 1.4449658,1.3630104 1.318965,1.598011 1.3189645,1.8750153 L0.23495817,1.8750153 C0.2349577,1.3870125 0.46695948,0.95300674 0.93196249,0.57200623 1.3969655,0.19100189 2.0029693,0 2.7489743,0 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Margin="17,16,6,20" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,38 L53,38 53,40 0,40 z M1,19 L53,19 53,21 1,21 z M1,0 L53,0 53,2 1,2 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                    </Grid>
                                    <Grid Grid.Row="3" Grid.Column="1">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <ToggleButton x:Name="PART_UpLetterNumberingButton" 
                                                      Grid.Column="0"
                                                      BorderThickness="0" 
                                                      Background="Transparent"
                                                      Padding="4"
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                      HorizontalContentAlignment="Left" >
                                            <ToggleButton.Content>
                                                <Grid 
                                                    x:Name="Numbering_Upletter" 
                                                    HorizontalAlignment="Right" 
                                                    Height="76"
                                                    VerticalAlignment="Top"
                                                    Width="76">
                                                    <Path Fill="Transparent" 
                                                          Margin="0.5" 
                                                          Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L4.2189999,0 4.8439999,0 76,0 76,76 0,76 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        HorizontalAlignment="Left" 
                                                        Margin="5.608,12.339,0,17.007" 
                                                        Stretch="Fill" 
                                                        Width="9.756">
                                                        <Path.Data>
                                                            <PathGeometry>M8.396025,45.377015 L9.4980357,45.377015 9.4980357,46.531025 8.396025,46.531025 z M3.798023,37.877038 C4.7550271,37.877038 5.4960306,38.124035 6.0210326,38.618035 6.547035,39.112034 6.856036,39.812031 6.9500363,40.719025 L5.8660315,40.719025 C5.7680314,40.070027 5.5600308,39.581032 5.2420295,39.25103 4.9240282,38.921032 4.442026,38.756035 3.798023,38.756035 3.1260205,38.756035 2.5960184,39.015034 2.2070163,39.532033 1.818015,40.05003 1.6240142,40.707024 1.6240142,41.504021 L1.6240142,43.021016 C1.6240142,43.826012 1.818015,44.487008 2.2070163,45.005005 2.5960184,45.522004 3.1260205,45.781003 3.798023,45.781003 4.4460261,45.781003 4.9290279,45.621003 5.2450293,45.301007 5.5610307,44.980008 5.7680314,44.49001 5.8660315,43.830014 L6.9500363,43.830014 C6.856036,44.689007 6.5430348,45.375004 6.0100325,45.887002 5.4760302,46.397999 4.7390273,46.654 3.798023,46.654 2.8130191,46.654 2.024016,46.318001 1.4300135,45.646004 0.83701092,44.975007 0.54000967,44.10001 0.54000955,43.021016 L0.54000955,41.516022 C0.54000967,40.441025 0.83701092,39.566029 1.4300135,38.891034 2.024016,38.215035 2.8130191,37.877038 3.798023,37.877038 z M8.3550103,26.377017 L9.4570211,26.377017 9.4570211,27.531024 8.3550103,27.531024 z M1.9220321,23.517018 L1.9220321,26.658001 3.9550276,26.658001 C4.506027,26.658001 4.9310263,26.528002 5.2300256,26.268004 5.5280254,26.008006 5.6780251,25.632006 5.6780251,25.140009 5.6780251,24.628012 5.5520251,24.230013 5.3000258,23.945015 5.0480259,23.660018 4.6540268,23.517018 4.1190279,23.517018 z M1.9220321,19.878037 L1.9220321,22.644022 3.8670285,22.644022 C4.3170274,22.632021 4.6730269,22.510024 4.9370263,22.278023 5.2000259,22.045025 5.3320258,21.712027 5.3320258,21.279028 5.3320258,20.802031 5.185026,20.450034 4.8900259,20.221034 4.5950268,19.993035 4.1560276,19.878037 3.5740295,19.878037 z M0.83803361,18.999041 L3.5740295,18.999041 C4.4810274,18.999041 5.1810258,19.18804 5.6750253,19.565037 6.1690238,19.942036 6.4160235,20.521033 6.4160235,21.302029 6.4160235,21.693026 6.298024,22.037026 6.0620239,22.333024 5.8250249,22.630023 5.514025,22.849021 5.1270254,22.990021 L5.1270254,23.00702 C5.639025,23.11302 6.0390242,23.360018 6.3280237,23.748017 6.6170233,24.137015 6.7620228,24.597012 6.7620228,25.12801 6.7620228,25.921006 6.5140231,26.520003 6.0180243,26.924 5.5220254,27.328999 4.8340266,27.530997 3.9550276,27.530997 L0.83803361,27.530997 z M8.6540301,7.3770161 L9.7560094,7.3770161 9.7560094,8.5310245 8.6540301,8.5310245 z M3.7560075,1.3419791 L2.2680033,5.3849449 5.2210024,5.3849449 z M3.292996,0 L4.2309831,0 7.471,8.5309997 6.3639763,8.5309997 5.537988,6.2579918 1.9459817,6.2579918 1.1080008,8.5309997 0,8.5309997 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Margin="18,16,7,20" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M1.9999999,38 L51,38 51,40 1.9999999,40 z M1,19 L51,19 51,21 1,21 z M0,0 L51,0 51,2 0,2 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                        <ToggleButton x:Name="PART_LowRomanNumberingButton" 
                                                      Grid.Column="1" 
                                                      BorderThickness="0"
                                                      Padding="4" 
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                      HorizontalContentAlignment="Left" >
                                            <ToggleButton.Content>
                                                <Grid 
                                                    x:Name="Numbering_LowRoman" 
                                                    HorizontalAlignment="Left"
                                                    Height="76"
                                                    VerticalAlignment="Top"
                                                    Width="76">
                                                    <Path Fill="Transparent" 
                                                          Margin="0.5" 
                                                          Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L76,0 76,76 0,76 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Margin="14,16,7,20" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M6,38 L55,38 55,40 6,40 z M2.9999995,19 L55,19 55,21 2.9999995,21 z M0,0 L55,0 55,2 0,2 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        HorizontalAlignment="Left" 
                                                        Margin="7,12,0,17" 
                                                        Stretch="Fill"
                                                        Width="10">
                                                        <Path.Data>
                                                            <PathGeometry>M9.0000002,46 L10,46 10,47 9.0000002,47 z M6.0000002,40 L7.0000006,40 7.0000006,47 6.0000002,47 z M3.0000004,40 L4.0000002,40 4.0000002,47 3.0000004,47 z M0,40 L0.99999985,40 0.99999985,47 0,47 z M6.0000002,38 L6.9999997,38 6.9999997,39 6.0000002,39 z M3.0000004,38 L4.0000002,38 4.0000002,39 3.0000004,39 z M0,38 L0.99999985,38 0.99999985,39 0,39 z M6.0000002,27 L7.0000006,27 7.0000006,28 6.0000002,28 z M3.0000004,21 L4.0000002,21 4.0000002,28 3.0000004,28 z M0,21 L1.0000006,21 1.0000006,28 0,28 z M3.0000004,19 L4.0000002,19 4.0000002,20 3.0000004,20 z M0,19 L1.0000006,19 1.0000006,20 0,20 z M3.0000004,7.9999995 L4.0000002,7.9999995 4.0000002,9 3.0000004,9 z M0,1.9999998 L1.0000006,1.9999998 1.0000006,9 0,9 z M0,0 L1.0000006,0 1.0000006,0.99999988 0,0.99999988 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                        <ToggleButton x:Name="PART_UpRomanNumberingButton" 
                                                      Grid.Column="2"
                                                      BorderThickness="0"
                                                      Padding="4" 
                                                      HorizontalContentAlignment="Left" 
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}">
                                            <ToggleButton.Content>
                                                <Grid 
                                                    x:Name="Numbering_Uproman" 
                                                    HorizontalAlignment="Right" 
                                                    Height="76"
                                                    VerticalAlignment="Top" 
                                                    Width="76">
                                                    <Path 
                                                        Fill="Transparent" 
                                                        Margin="0.5" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L76,0 76,76 0,76 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Margin="14,16,7,20" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M6,38 L55,38 55,40 6,40 z M2.9999995,19 L55,19 55,21 2.9999995,21 z M0,0 L55,0 55,1.9999997 0,1.9999997 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        HorizontalAlignment="Left" 
                                                        Margin="7,12,0,17"
                                                        Stretch="Fill" 
                                                        Width="10">
                                                        <Path.Data>
                                                            <PathGeometry>M9.0000003,46 L10,46 10,47 9.0000003,47 z M6,38 L6.9999998,38 6.9999998,40 6.9999998,41 6.9999998,47 6,47 6,41 6,40 z M3.0000005,38 L4,38 4,40 4,41 4,47 3.0000005,47 3.0000005,41 3.0000005,40 z M0,38 L1,38 1,40 1,41 1,47 0,47 0,41 0,40 z M6,27 L6.9999998,27 6.9999998,28 6,28 z M3.0000005,19 L4,19 4,21 4,22 4,28 3.0000005,28 3.0000005,22 3.0000005,21 z M0,19 L1,19 1,21 1,22 1,28 0,28 0,22 0,21 z M3.0000005,8 L4,8 4,9 3.0000005,9 z M0,0 L1,0 1,2 1,3 1,9 0,9 0,3 0,2 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                    </Grid>
                                </Grid>
                            </TabItem>
                            <TabItem Header="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarBulletToolTip}">
                                <Grid Background="{StaticResource ContentBackground}">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="12"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="12"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="12"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="12"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid Grid.Row="2" Grid.Column="1">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <ToggleButton x:Name="PART_NoBulletButton" 
                                                      Grid.Column="0" 
                                                      BorderThickness="0" 
                                                      Margin="0,0,7,0" 
                                                      Background="Transparent" 
                                                      Padding="4"
                                                      Height="50"
                                                      Width="50"
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                      HorizontalContentAlignment="Left" >
                                            <ToggleButton.Content>
                                                <Grid x:Name="Bullets_None" 
                                                      HorizontalAlignment="Left" 
                                                      Height="40"
                                                      VerticalAlignment="Top" 
                                                      Width="40">
                                                    <Path 
                                                        Fill="Transparent" 
                                                        Margin="0.5" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L39,0 39,39 0,39 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Margin="7.449,16.465,7.849,15.607" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M22.333007,2.7070129 C21.957,2.707013 21.640006,2.8580138 21.380004,3.160992 21.120002,3.4640006 20.954001,3.8559983 20.883002,4.3400065 L23.708008,4.3400065 23.708008,4.1999923 C23.708008,3.7670091 23.594002,3.410015 23.364006,3.1290095 23.135002,2.848004 22.791,2.707013 22.333007,2.7070129 z M9.9689951,2.7069989 C9.4389954,2.7069988 9.0409956,2.9089988 8.7759953,3.3139987 8.5119953,3.7179987 8.3789959,4.2319986 8.3789959,4.8549985 L8.3789959,4.9839984 C8.3789959,5.6179985 8.5119953,6.1329983 8.7759953,6.5309981 9.0409956,6.9279979 9.4419956,7.1269983 9.9799957,7.1269983 10.509995,7.1269983 10.908996,6.926998 11.176995,6.5279983 11.445995,6.1289982 11.579995,5.6139983 11.579995,4.9839984 L11.579995,4.8549985 C11.579995,4.2319986 11.444995,3.7179987 11.174995,3.3139987 10.903996,2.9089988 10.501995,2.7069988 9.9689951,2.7069989 z M16.590996,1.9020022 C17.225996,1.9020023 17.709996,2.0800021 18.046996,2.4390017 18.383996,2.7970013 18.551996,3.3670007 18.551996,4.1529998 L18.551996,7.8209957 17.552996,7.8209957 17.552996,4.1739999 C17.552996,3.6400003 17.451996,3.2660007 17.249996,3.0510012 17.046996,2.8360013 16.729996,2.7290013 16.295996,2.7290014 15.969996,2.7290013 15.688997,2.8060013 15.450997,2.9590012 15.211997,3.113001 15.019997,3.3280007 14.872997,3.6040006 L14.872997,7.8209957 13.874998,7.8209957 13.874998,2.0090022 14.770997,2.0090022 14.840997,2.8790012 14.856997,2.885001 C15.045997,2.5720015 15.287997,2.3310018 15.579997,2.1590019 15.871997,1.9880022 16.207996,1.9020023 16.590996,1.9020022 z M22.333007,1.9010142 C23.121002,1.9010143 23.713005,2.1330086 24.108002,2.5940064 24.504005,3.0570186 24.702004,3.6879992 24.702004,4.4899999 L24.702004,5.1410002 20.851005,5.1410002 C20.865004,5.734017 21.012999,6.2150038 21.294005,6.5799936 21.575004,6.9450139 21.975005,7.1269902 22.494003,7.1269902 22.855003,7.1269902 23.168007,7.0770024 23.432006,6.9769963 23.694008,6.8769907 23.932007,6.7349924 24.144005,6.5530161 L24.535004,7.2129887 C24.324005,7.4209961 24.053001,7.5919861 23.722008,7.7269953 23.391006,7.859991 22.981003,7.9280146 22.494003,7.9280146 21.678001,7.9280146 21.033004,7.6640071 20.561003,7.1359929 20.088004,6.6070017 19.852004,5.9060138 19.852003,5.0329987 L19.852003,4.7909948 C19.852004,3.9459945 20.094,3.2530023 20.579001,2.7129943 21.064002,2.1720101 21.649001,1.9010143 22.333007,1.9010142 z M9.9689951,1.9009991 C10.784995,1.900999 11.423995,2.1739989 11.883995,2.7199987 12.343995,3.2659987 12.572995,3.9779986 12.572995,4.8549985 L12.572995,4.9839984 C12.572995,5.8649982 12.344995,6.5759982 11.885995,7.1169981 11.427995,7.656998 10.791995,7.926998 9.9799957,7.926998 9.1629953,7.926998 8.5249958,7.656998 8.0669956,7.1169981 7.6089954,6.5759982 7.3799953,5.8649982 7.3799953,4.9839984 L7.3799953,4.8549985 C7.3799953,3.9779986 7.6089954,3.2659987 8.0669956,2.7199987 8.5249958,2.1739989 9.1589956,1.900999 9.9689951,1.9009991 z M0,0 L0.99299812,0 4.8769979,6.1550182 4.8939981,6.1550182 4.8939981,0 5.8869982,0 5.8869982,7.8200229 4.8939981,7.8200229 1.0099983,1.6600049 0.99299812,1.6600049 0.99299812,7.8200229 0,7.8200229 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                        <ToggleButton x:Name="PART_DotBulletButton" 
                                                      Margin="0,0,7,0" 
                                                      Grid.Column="1" 
                                                      BorderThickness="0" 
                                                      Background="Transparent"
                                                      Padding="4" 
                                                      Height="50"
                                                      Width="50"
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                      HorizontalContentAlignment="Left">
                                            <ToggleButton.Content>
                                                <Grid x:Name="Bullets_Dot" 
                                                      HorizontalAlignment="Left"
                                                      Height="40"
                                                      VerticalAlignment="Top" 
                                                      Width="40">
                                                    <Path 
                                                        Fill="Transparent" 
                                                        Margin="0.5" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L39,0 39,39 0,39 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Margin="14,18,15,11" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M5.5,0 C8.5370026,0 11,2.4630127 11,5.5 11,8.5369873 8.5370026,11 5.5,11 2.4629974,11 0,8.5369873 0,5.5 0,2.4630127 2.4629974,0 5.5,0 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                        <ToggleButton x:Name="PART_CircleBulletButton" 
                                                      Margin="0,0,7,0" 
                                                      Grid.Column="2"
                                                      BorderThickness="0"
                                                      Background="Transparent"
                                                      Padding="4" 
                                                      Height="50"
                                                      Width="50"
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                      HorizontalContentAlignment="Left">
                                            <ToggleButton.Content>
                                                <Grid x:Name="Bullets_Circle"
                                                      HorizontalAlignment="Left" 
                                                      Height="40"
                                                      VerticalAlignment="Top" 
                                                      Width="40">
                                                    <Path 
                                                        Fill="Transparent" 
                                                        Margin="0.5" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L39,0 39,39 0,39 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Margin="13,14,12,11" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M7.5,0.99999991 C3.9160004,1 1,3.6910096 0.99999994,7 1,10.308991 3.9160004,13 7.5,13 11.084,13 14,10.308991 14,7 14,3.6910096 11.084,1 7.5,0.99999991 z M7.5,0 C11.636002,2.9802322E-08 15,3.1409912 15,7 15,10.859009 11.636002,14 7.5,14 3.3639984,14 0,10.859009 0,7 0,3.1409912 3.3639984,2.9802322E-08 7.5,0 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                        <ToggleButton x:Name="PART_SquareBulletButton"
                                                      Margin="0,0,7,0" 
                                                      Grid.Column="3" 
                                                      BorderThickness="0" 
                                                      Background="Transparent" 
                                                      Padding="4" 
                                                      Height="50"
                                                      Width="50"
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                      HorizontalContentAlignment="Left">
                                            <ToggleButton.Content>
                                                <Grid x:Name="Bullets_Square" 
                                              HorizontalAlignment="Left" 
                                              Height="40"
                                              VerticalAlignment="Top" 
                                              Width="40">
                                                    <Path Fill="Transparent" 
                                                  Margin="0.5" 
                                                  Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L39,0 39,39 0,39 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path Fill="{StaticResource IconColor}"
                                                  Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path Fill="{StaticResource IconColor}"
                                                  Margin="16,15,15,16" 
                                                  Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L9,0 9,9 0,9 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                    </Grid>
                                    <Grid Grid.Row="3" Grid.Column="1">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <ToggleButton x:Name="PART_FlowerBulletButton" 
                                                      Margin="0,5,7,0" 
                                                      Grid.Column="0"
                                                      BorderThickness="0"
                                                      Background="Transparent" 
                                                      Padding="4"
                                                      Height="50"
                                                      Width="50"
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                      HorizontalContentAlignment="Left">
                                            <ToggleButton.Content>
                                                <Grid x:Name="Bullets_Flower" 
                                                      Height="40"
                                                      VerticalAlignment="Top">
                                                    <Path  
                                                        Fill="Transparent" 
                                                        Margin="0.5" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L39,0 39,39 0,39 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Margin="9,8,8,9" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M11.5,14.328001 L15.835999,18.664002 11.5,23.000001 7.1639996,18.664002 z M18.664,7.1640015 L23,11.500002 18.664,15.836002 14.328,11.500002 z M4.3360004,7.1640015 L8.6720003,11.500002 4.3360004,15.836002 0,11.500002 z M11.5,0 L15.836,4.3360004 11.5,8.6720014 7.1640003,4.3360004 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                        <ToggleButton x:Name="PART_ArrowBulletButton" 
                                                      Margin="0,5,7,0" 
                                                      Grid.Column="1"
                                                      Height="50"
                                                      Width="50"
                                                      BorderThickness="0"
                                                      Background="Transparent" 
                                                      Padding="4" 
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                      HorizontalContentAlignment="Left">
                                            <ToggleButton.Content>
                                                <Grid x:Name="Bullets_Arrow"
                                                      HorizontalAlignment="Right" 
                                                      Height="40" 
                                                      VerticalAlignment="Top"
                                                      Width="40">
                                                    <Path 
                                                        Fill="Transparent" 
                                                        Margin="0.5" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L39,0 39,39 0,39 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Margin="9.181,7.2,9.502,8.146" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M2.6369915,2.600007 L7.8879943,11.789005 7.8822666,11.799983 19.284758,11.799983 z M0,0 L21.316999,11.78101 0.049011266,24.654001 6.7489877,11.811009 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                        <ToggleButton x:Name="PART_TickBulletButton" 
                                                      Margin="0,5,7,0" 
                                                      Grid.Column="2" 
                                                      BorderThickness="0" 
                                                      Background="Transparent"
                                                      Padding="4" 
                                                      Height="50"
                                                      Width="50"
                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                      HorizontalContentAlignment="Left">
                                            <ToggleButton.Content>
                                                <Grid x:Name="Bullets_TIck"
                                                      HorizontalAlignment="Right" 
                                                      Height="40"
                                                      VerticalAlignment="Top" 
                                                      Width="40">
                                                    <Path 
                                                        Fill="Transparent" 
                                                        Margin="0.5" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0,0 L39,0 39,39 0,39 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    <Path 
                                                        Fill="{StaticResource IconColor}"
                                                        Margin="10.512,6.54,8.468,13.165" 
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M21.019999,0 C21.019999,-1.0224994E-07 16.818007,4.3180208 13.771014,8.1249947 10.075002,12.741996 7.2249994,16.943012 5.0140028,20.295 5.0140028,20.295 2.0540142,12.986991 0,12.020988 L2.4770179,10.33001 C2.4770179,10.33001 4.2520099,12.261009 6.0039921,14.797994 6.0039921,14.797994 11.145009,5.3760032 21.019999,0 z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                    </Grid>
                                </Grid>
                            </TabItem>
                        </TabControl>
                        <Border Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="3" Width="Auto" BorderThickness="0 0 0 1" Opacity="0.5"  BorderBrush="{StaticResource BorderAlt}" Background="{StaticResource PopupBackground}"/>
                        <StackPanel  Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="3" Orientation="Horizontal" HorizontalAlignment="Right">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Button x:Name="PART_OkButton" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOk}" 
                                        Grid.Column="0" 
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 5 9 5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="52"
                                        IsDefault="True" 
                                        TabIndex="9"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        Style="{StaticResource WPFPrimaryButtonStyle}"/>
                                <Button x:Name="PART_CancelButton" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxCancel}" 
                                        Grid.Column="1"
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 5 9 5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="69" 
                                        TabIndex="10"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        />
                            </Grid>
                        </StackPanel>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
