using System.Text.Json;
using System.Text.Json.Serialization;

namespace AirMonitor.Core.Utilities;

/// <summary>
/// JSON序列化工具类
/// 提供统一的JSON序列化和反序列化功能
/// </summary>
public static class JsonHelper
{
    /// <summary>
    /// 默认JSON序列化选项
    /// </summary>
    public static readonly JsonSerializerOptions DefaultOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = true,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
        Converters =
        {
            new JsonStringEnumConverter(JsonNamingPolicy.CamelCase)
        }
    };

    /// <summary>
    /// 紧凑JSON序列化选项（不格式化）
    /// </summary>
    public static readonly JsonSerializerOptions CompactOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
        Converters =
        {
            new JsonStringEnumConverter(JsonNamingPolicy.CamelCase)
        }
    };

    /// <summary>
    /// 序列化对象到JSON字符串
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="obj">要序列化的对象</param>
    /// <param name="options">序列化选项</param>
    /// <returns>JSON字符串</returns>
    public static string Serialize<T>(T obj, JsonSerializerOptions? options = null)
    {
        try
        {
            return JsonSerializer.Serialize(obj, options ?? DefaultOptions);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"序列化对象失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 异步序列化对象到JSON字符串
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="obj">要序列化的对象</param>
    /// <param name="options">序列化选项</param>
    /// <returns>JSON字符串</returns>
    public static async Task<string> SerializeAsync<T>(T obj, JsonSerializerOptions? options = null)
    {
        try
        {
            using var stream = new MemoryStream();
            await JsonSerializer.SerializeAsync(stream, obj, options ?? DefaultOptions);
            stream.Position = 0;
            using var reader = new StreamReader(stream);
            return await reader.ReadToEndAsync();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"异步序列化对象失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 反序列化JSON字符串到对象
    /// </summary>
    /// <typeparam name="T">目标对象类型</typeparam>
    /// <param name="json">JSON字符串</param>
    /// <param name="options">反序列化选项</param>
    /// <returns>反序列化的对象</returns>
    public static T? Deserialize<T>(string json, JsonSerializerOptions? options = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(json))
            {
                return default;
            }

            return JsonSerializer.Deserialize<T>(json, options ?? DefaultOptions);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"反序列化JSON失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 异步反序列化JSON字符串到对象
    /// </summary>
    /// <typeparam name="T">目标对象类型</typeparam>
    /// <param name="json">JSON字符串</param>
    /// <param name="options">反序列化选项</param>
    /// <returns>反序列化的对象</returns>
    public static async Task<T?> DeserializeAsync<T>(string json, JsonSerializerOptions? options = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(json))
            {
                return default;
            }

            using var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(json));
            return await JsonSerializer.DeserializeAsync<T>(stream, options ?? DefaultOptions);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"异步反序列化JSON失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 从文件读取并反序列化JSON
    /// </summary>
    /// <typeparam name="T">目标对象类型</typeparam>
    /// <param name="filePath">文件路径</param>
    /// <param name="options">反序列化选项</param>
    /// <returns>反序列化的对象</returns>
    public static async Task<T?> DeserializeFromFileAsync<T>(string filePath, JsonSerializerOptions? options = null)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"文件不存在: {filePath}");
            }

            var json = await File.ReadAllTextAsync(filePath);
            return await DeserializeAsync<T>(json, options);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"从文件反序列化JSON失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 序列化对象并保存到文件
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="obj">要序列化的对象</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="options">序列化选项</param>
    /// <returns>是否保存成功</returns>
    public static async Task<bool> SerializeToFileAsync<T>(T obj, string filePath, JsonSerializerOptions? options = null)
    {
        try
        {
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var json = await SerializeAsync(obj, options);
            await File.WriteAllTextAsync(filePath, json);
            return true;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"序列化对象到文件失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 验证JSON字符串格式是否正确
    /// </summary>
    /// <param name="json">JSON字符串</param>
    /// <returns>true表示格式正确，false表示格式错误</returns>
    public static bool IsValidJson(string json)
    {
        if (string.IsNullOrWhiteSpace(json))
        {
            return false;
        }

        try
        {
            using var document = JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 格式化JSON字符串
    /// </summary>
    /// <param name="json">未格式化的JSON字符串</param>
    /// <returns>格式化后的JSON字符串</returns>
    public static string FormatJson(string json)
    {
        try
        {
            using var document = JsonDocument.Parse(json);
            return JsonSerializer.Serialize(document, DefaultOptions);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"格式化JSON失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 压缩JSON字符串（移除格式化）
    /// </summary>
    /// <param name="json">格式化的JSON字符串</param>
    /// <returns>压缩后的JSON字符串</returns>
    public static string CompactJson(string json)
    {
        try
        {
            using var document = JsonDocument.Parse(json);
            return JsonSerializer.Serialize(document, CompactOptions);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"压缩JSON失败: {ex.Message}", ex);
        }
    }
}
