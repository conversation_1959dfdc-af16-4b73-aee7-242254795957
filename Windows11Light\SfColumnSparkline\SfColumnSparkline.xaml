<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib"
                    xmlns:columnSparkline="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

  <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="AxisLineStyle" TargetType="Line">
        <Setter Property="Stroke" Value="{StaticResource Series5}" />
        <Setter Property="StrokeThickness" Value="2"></Setter>
    </Style>

    <Style x:Key="SyncfusionSfColumnSparklineStyle"
           TargetType="columnSparkline:SfColumnSparkline">
        <Setter Property="Background" Value="{StaticResource ContentBackground}"></Setter>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="BorderThickness" Value="0"></Setter>
        <Setter Property="Interior" Value="{StaticResource Series1}"></Setter>
        <Setter Property="RangeBandBrush" Value="{StaticResource Series10}"></Setter>
        <Setter Property="AxisStyle" Value="{StaticResource AxisLineStyle}"></Setter>
    </Style>

    <Style TargetType="columnSparkline:SfColumnSparkline"
           BasedOn="{StaticResource SyncfusionSfColumnSparklineStyle}" />

</ResourceDictionary>
