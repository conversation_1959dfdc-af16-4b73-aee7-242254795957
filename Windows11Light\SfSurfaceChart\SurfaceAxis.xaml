<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:surfaceChart="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF" 
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <!--AxisLabel-->

    <!--AxisGridLines-->

    <!--AxisLine-->

    <!--AxisLabel-->

    <!--AxisHeader-->

    <Style x:Key="SyncfusionLineStyle" TargetType="Line">
        <Setter Property="Stroke" Value="{StaticResource BorderAlt4}"/>
        <Setter Property="StrokeThickness" Value="2"/>
    </Style>

    <DataTemplate x:Key="SyncfusionSurfaceAxisLabelTemplate">
        <Grid Background="Transparent">
            <TextBlock Text="{Binding LabelContent}"
                       Foreground="{StaticResource ContentForeground}"
                       FontSize="{StaticResource Windows11Light.CaptionText}" 
                       FontWeight="{StaticResource Windows11Light.FontWeightNormal}"
                       FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"/>
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="SyncfusionSurfaceAxisHeaderTemplate">
        <Grid Background="Transparent">
            <TextBlock Text="{Binding}"
                Foreground="{StaticResource ContentForeground}"
                FontSize="{StaticResource Windows11Light.CaptionText}" 
                FontWeight="{StaticResource Windows11Light.FontWeightNormal}"
                FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"/>
        </Grid>
    </DataTemplate>

    <Style x:Key="SyncfusionSurfaceAxisStyle" TargetType="surfaceChart:SurfaceAxis">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="MajorTickLineStyle" Value="{StaticResource SyncfusionLineStyle}"/>
        <Setter Property="MinorTickLineStyle" Value="{StaticResource SyncfusionLineStyle}"/>
        <Setter Property="AxisLineStyle" Value="{StaticResource SyncfusionLineStyle}"/>
        <Setter Property="GridLineStroke" Value="{StaticResource BorderAlt1}"/>
        <Setter Property="HeaderTemplate" Value="{StaticResource SyncfusionSurfaceAxisHeaderTemplate}" />
        <Setter Property="LabelTemplate" Value="{StaticResource SyncfusionSurfaceAxisLabelTemplate}" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSurfaceAxisStyle}" TargetType="surfaceChart:SurfaceAxis"/>

</ResourceDictionary>
