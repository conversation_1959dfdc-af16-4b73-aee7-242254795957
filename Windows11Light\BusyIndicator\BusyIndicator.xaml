<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	
    xmlns:local="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:resources="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:theme="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:vsm="clr-namespace:Syncfusion.Windows;assembly=Syncfusion.Shared.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ProgressBar.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphPrimaryToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml" />
    </ResourceDictionary.MergedDictionaries>
    <shared:BooleanToVisibilityConverterEx x:Key="VisibilityConverter" />

    <Style
        x:Key="BusyIndicatorProgressBarStyle"
        BasedOn="{StaticResource WPFProgressBarStyle}"
        TargetType="{x:Type ProgressBar}">
        <Setter Property="MinWidth" Value="50" />
        <Setter Property="Height" Value="5" />
        <Setter Property="Margin" Value="5" />
    </Style>

    <Style
        x:Key="BusyIndicatorCloseButtonStyle"
        BasedOn="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
        TargetType="{x:Type ToggleButton}">
        <Setter Property="Content">
            <Setter.Value>
                <Path
                    x:Name="path"
                    Width="12"
                    Height="12"
                    Margin="3"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Stroke="{StaticResource ContentForeground}"
                    StrokeThickness="1">
                    <Path.Data>
                        <PathGeometry>M1,1 L11,11 M1,11 L11,1</PathGeometry>
                    </Path.Data>
                </Path>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionBusyIndicatorStyle" TargetType="local:BusyIndicator">
        <Setter Property="Background" Value="{StaticResource PopupBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="DescriptionPlacement" Value="Bottom" />
        <Setter Property="CloseButtonStyle" Value="{StaticResource BusyIndicatorCloseButtonStyle}" />
        <Setter Property="ProgressBarStyle" Value="{StaticResource BusyIndicatorProgressBarStyle}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:BusyIndicator">
                    <Grid>
                        <ContentPresenter />
                        <Grid Visibility="{Binding Busy, Converter={StaticResource VisibilityConverter}, RelativeSource={RelativeSource TemplatedParent}}">
                            <Border
                                x:Name="PART_Border"
                                Width="{TemplateBinding Width}"
                                Height="{TemplateBinding Height}"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                CornerRadius="8"
                                Effect="{StaticResource Default.ShadowDepth2}"
                                SnapsToDevicePixels="True">

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="26" />
                                    </Grid.RowDefinitions>
                                    <ContentPresenter
                                        x:Name="PART_Header"
                                        Margin="7"
                                        HorizontalAlignment="{TemplateBinding HeaderAlignment}"
                                        Content="{TemplateBinding Header}"
                                        ContentTemplate="{TemplateBinding HeaderTemplate}"
                                        TextElement.FontWeight="{StaticResource Windows11Light.FontWeightMedium}"
                                        TextElement.FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                        TextElement.Foreground="{TemplateBinding Foreground}" >
                                        <ContentPresenter.Resources>
                                            <Style BasedOn="{x:Null}" TargetType="TextBlock"/>
                                        </ContentPresenter.Resources>
                                    </ContentPresenter>
                                    <ToggleButton
                                        x:Name="PART_Close"
                                        Grid.Column="1"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Cursor="Hand"
                                        Margin="5"
                                        Style="{TemplateBinding CloseButtonStyle}"
                                        Visibility="{TemplateBinding CloseButtonVisibility}">
                                    </ToggleButton>
                                    <Grid
                                        x:Name="PART_DescGrid"
                                        Grid.Row="1"
                                        Grid.ColumnSpan="2"
                                        Margin="4">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="*" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <ContentControl
                                            x:Name="PART_Description"
                                            Grid.Row="0"
                                            Grid.ColumnSpan="2"
                                            Margin="5"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Content="{TemplateBinding LoadingDescription}"
                                            ContentTemplate="{TemplateBinding LoadingDescriptionTemplate}"
                                            Foreground="{TemplateBinding Foreground}" />
                                        <ProgressBar
                                            x:Name="PART_ProgressBar"
                                            Grid.Row="1"
                                            Grid.ColumnSpan="2"
                                            IsIndeterminate="{TemplateBinding IsIndeterminate}"
                                            Style="{TemplateBinding ProgressBarStyle}"
                                            Value="{TemplateBinding ProgressValue}" />
                                    </Grid>
                                    <Button
                                        x:Name="PART_Cancel"
                                        Grid.Row="3"
                                        Grid.ColumnSpan="2"
                                        Padding="5"
                                        Height="30"
                                        Background="Transparent"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Bottom"
                                        Content="{resources:SharedLocalizationResourceExtension ResourceName=CancelText}"
                                        Style="{StaticResource WPFButtonStyle}"
                                        Visibility="{TemplateBinding CancelButtonVisibility}">
                                        <Button.Margin>
                                            <Thickness>0,-5,5,0</Thickness>
                                        </Button.Margin>
                                    </Button>
                                </Grid>
                            </Border>
                        </Grid>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="DescriptionPlacement" Value="Bottom">
                            <Setter TargetName="PART_ProgressBar" Property="Margin" Value="5,15,5,5"/>
                        </Trigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="PART_ProgressBar" Property="Margin" Value="5,0,5,5"/>
                            <Setter TargetName="PART_Cancel" Property="Margin" Value="0,-5,5,-2"/>
                            <Setter TargetName="PART_Header" Property="VerticalAlignment" Value="Center"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionBusyIndicatorStyle}" TargetType="local:BusyIndicator" />

</ResourceDictionary>
