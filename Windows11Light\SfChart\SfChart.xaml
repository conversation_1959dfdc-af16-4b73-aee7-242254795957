<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:charts="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/SfChart/ChartArea.xaml"></ResourceDictionary>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/SfChart/ChartToolbar.xaml"></ResourceDictionary>
        <ResourceDictionary Source="/Syncfusion.SfChart.WPF;component/Sparkline/Themes/Sparkline.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/SfChart/Resizer.xaml" />
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="SyncfusionChartPrintDialogStyle" TargetType="charts:ChartPrintDialog">
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="Background"
                Value="{StaticResource PopupBackground}"></Setter>
    </Style>

    <Style TargetType="charts:ChartPrintDialog" BasedOn="{StaticResource SyncfusionChartPrintDialogStyle}"></Style>

    <Style x:Key="SyncfusionShapeAnnotationStyle" TargetType="charts:ShapeAnnotation">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt3}"></Setter>
        <Setter Property="Fill"
                Value="{StaticResource Series2}" />
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"></Setter>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"></Setter>
    </Style>

    <Style TargetType="charts:ShapeAnnotation" BasedOn="{StaticResource SyncfusionShapeAnnotationStyle}"></Style>
    
    <Style x:Key="SyncfusionHorizontalLineAnnotationStyle" TargetType="charts:HorizontalLineAnnotation">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt3}"></Setter>
        <Setter Property="Fill"
                Value="{StaticResource Series2}" />
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"></Setter>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"></Setter>
    </Style>

    <Style TargetType="charts:HorizontalLineAnnotation" BasedOn="{StaticResource SyncfusionHorizontalLineAnnotationStyle}"></Style>
    
    <Style x:Key="SyncfusionVerticalLineAnnotationStyle" TargetType="charts:VerticalLineAnnotation">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt3}"></Setter>
        <Setter Property="Fill"
                Value="{StaticResource Series2}" />
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"></Setter>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"></Setter>
    </Style>

    <Style TargetType="charts:VerticalLineAnnotation" BasedOn="{StaticResource SyncfusionVerticalLineAnnotationStyle}"></Style>
    
    <Style x:Key="SyncfusionEllipseAnnotationStyle" TargetType="charts:EllipseAnnotation">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt3}"></Setter>
        <Setter Property="Fill"
                Value="{StaticResource Series2}" />
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"></Setter>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"></Setter>
    </Style>

    <Style TargetType="charts:EllipseAnnotation" BasedOn="{StaticResource SyncfusionEllipseAnnotationStyle}"></Style>
    
    <Style x:Key="SyncfusionRectangleAnnotationStyle" TargetType="charts:RectangleAnnotation">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt3}"></Setter>
        <Setter Property="Fill"
                Value="{StaticResource Series2}" />
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"></Setter>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"></Setter>
        <Setter Property="Opacity"
                Value="0.4" />
    </Style>

    <Style TargetType="charts:RectangleAnnotation" BasedOn="{StaticResource SyncfusionRectangleAnnotationStyle}"></Style>
    
</ResourceDictionary>
