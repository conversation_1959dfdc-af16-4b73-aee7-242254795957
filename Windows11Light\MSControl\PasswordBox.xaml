<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                     xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="PasswordBox.Static.Border" Color="#c8c8c8"/>

    <SolidColorBrush x:Key="PasswordBox.MouseOver.Border" Color="#179bd7"/>

    <SolidColorBrush x:Key="PasswordBox.Focused.Border" Color="#686969"/>

    <LinearGradientBrush x:Key="PasswordBoxBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="PasswordBoxBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="PasswordBoxBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="WPFPasswordBoxStyle" TargetType="{x:Type PasswordBox}">
        <Setter Property="PasswordChar" Value="●"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PasswordBoxBorderBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="SelectionBrush"
                Value="{StaticResource PrimaryBackground}"/>
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None"/>
        <Setter Property="AllowDrop" Value="true"/>
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type PasswordBox}">
                    <Grid>
                        <Border x:Name="border" 
                                Padding="10,0,0,0" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                Background="{TemplateBinding Background}"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                SnapsToDevicePixels="True">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                          Focusable="false" 
                                          Background="Transparent"
                                          HorizontalScrollBarVisibility="Hidden" 
                                          VerticalScrollBarVisibility="Hidden"/>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource PasswordBoxBorderBrushHovered}"/>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundAlt5}"/>
                            <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="CaretBrush" Value="{StaticResource ContentForegroundAlt1}"/>
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource PasswordBoxBorderBrushFocused}"/>
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                            <Setter Property="Padding" Value="0,0,0,-1"/>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundAlt6}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource WPFPasswordBoxStyle}" TargetType="{x:Type PasswordBox}"/>
</ResourceDictionary>
