﻿using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using AirMonitor.Core.Interfaces;
using AirMonitor.LicenseGenerator.Services;
using AirMonitor.LicenseGenerator.ViewModels;
using AirMonitor.LicenseGenerator.Views;

namespace AirMonitor.LicenseGenerator;

/// <summary>
/// License Generator应用程序主类
/// 配置依赖注入和应用程序启动
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    /// <summary>
    /// 应用程序启动时调用
    /// </summary>
    protected override async void OnStartup(StartupEventArgs e)
    {
        try
        {
            // 配置Serilog日志
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.File("Logs/license-generator-.log",
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30,
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
                .CreateLogger();

            // 创建主机
            _host = CreateHostBuilder().Build();
            await _host.StartAsync();

            // 获取主窗口并显示
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序启动失败");
            MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown(1);
        }
    }

    /// <summary>
    /// 应用程序退出时调用
    /// </summary>
    protected override async void OnExit(ExitEventArgs e)
    {
        try
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }

            Log.CloseAndFlush();
        }
        catch (Exception ex)
        {
            Log.Error(ex, "应用程序退出时发生错误");
        }
        finally
        {
            base.OnExit(e);
        }
    }

    /// <summary>
    /// 创建主机构建器
    /// </summary>
    private static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                // 注册服务
                services.AddSingleton<ICryptoService, CryptoService>();
                services.AddSingleton<IHardwareFingerprintService, HardwareFingerprintService>();
                services.AddSingleton<ILicenseGeneratorService, LicenseGeneratorService>();

                // 注册ViewModels
                services.AddSingleton<MainViewModel>();
                services.AddSingleton<LicenseGeneratorViewModel>();
                services.AddSingleton<LicenseValidatorViewModel>();
                services.AddSingleton<TemplateManagerViewModel>();

                // 注册Views
                services.AddSingleton<MainWindow>();

                // 配置日志
                services.AddLogging(builder =>
                {
                    builder.ClearProviders();
                    builder.AddSerilog();
                });
            });
    }
}

