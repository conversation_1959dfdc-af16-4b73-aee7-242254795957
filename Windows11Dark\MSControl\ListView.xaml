<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:Microsoft_Windows_Aero="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ScrollViewer.xaml" />
    </ResourceDictionary.MergedDictionaries>
    
    <Style x:Key="ListViewItemFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="{x:Static GridView.GridViewScrollViewerStyleKey}"
       TargetType="ScrollViewer">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollViewer">
                    <Grid Background="{TemplateBinding Background}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <DockPanel Margin="{TemplateBinding Padding}">
                            <ScrollViewer DockPanel.Dock="Top"
                                          HorizontalScrollBarVisibility="Hidden"
                                          VerticalScrollBarVisibility="Hidden"
                                          Focusable="false">
                                <GridViewHeaderRowPresenter Margin="2,0,2,0"
                                          Columns="{Binding Path=TemplatedParent.View.Columns, RelativeSource={RelativeSource TemplatedParent}}"
                                          ColumnHeaderContainerStyle="{Binding Path=TemplatedParent.View.ColumnHeaderContainerStyle, RelativeSource={RelativeSource TemplatedParent}}"
                                          ColumnHeaderTemplate="{Binding Path=TemplatedParent.View.ColumnHeaderTemplate, RelativeSource={RelativeSource TemplatedParent}}"
                                          ColumnHeaderTemplateSelector="{Binding Path=TemplatedParent.View.ColumnHeaderTemplateSelector, RelativeSource={RelativeSource TemplatedParent}}"
                                          AllowsColumnReorder="{Binding Path=TemplatedParent.View.AllowsColumnReorder, RelativeSource={RelativeSource TemplatedParent}}"
                                          ColumnHeaderContextMenu="{Binding Path=TemplatedParent.View.ColumnHeaderContextMenu, RelativeSource={RelativeSource TemplatedParent}}"
                                          ColumnHeaderToolTip="{Binding Path=TemplatedParent.View.ColumnHeaderToolTip, RelativeSource={RelativeSource TemplatedParent}}"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                            </ScrollViewer>

                            <ScrollContentPresenter Name="PART_ScrollContentPresenter"
                                                    KeyboardNavigation.DirectionalNavigation="Local"
                                                    CanContentScroll="True"
                                                    CanHorizontallyScroll="False"
                                                    CanVerticallyScroll="False" />
                        </DockPanel>

                        <ScrollBar Name="PART_HorizontalScrollBar"
                                   Orientation="Horizontal"
                                   Grid.Row="1"
                                   Maximum="{TemplateBinding ScrollableWidth}"
                                   ViewportSize="{TemplateBinding ViewportWidth}"
                                   Value="{TemplateBinding HorizontalOffset}"
                                   Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}" />

                        <ScrollBar Name="PART_VerticalScrollBar"
                                   Grid.Column="1"
                                   Maximum="{TemplateBinding ScrollableHeight}"
                                   ViewportSize="{TemplateBinding ViewportHeight}"
                                   Value="{TemplateBinding VerticalOffset}"
                                   Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}" />

                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact">
                            <Setter Property="Grid.ColumnSpan" TargetName="PART_ScrollContentPresenter" Value="2"/>
                            <Setter Property="Grid.RowSpan" TargetName="PART_ScrollContentPresenter" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="GridViewColumnHeaderGripper" TargetType="{x:Type Thumb}">
        <Setter Property="Width" Value="18" />
        <Setter Property="Background" Value="{StaticResource BorderAlt}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border Padding="{TemplateBinding Padding}" >
                        <Rectangle HorizontalAlignment="Center"
                                   Width="1"
                                   Fill="{TemplateBinding Background}" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="{x:Type GridViewColumnHeader}" TargetType="{x:Type GridViewColumnHeader}">
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" Value="0,1,0,1"/>
        <Setter Property="Padding" Value="2,0,2,0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type GridViewColumnHeader}">
                    <Grid>
                        <Border x:Name="HeaderBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter x:Name="HeaderContent"
                                              Margin="0,0,0,1"
                                              RecognizesAccessKey="True"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                        </Border>
                        <Thumb x:Name="PART_HeaderGripper"
                               HorizontalAlignment="Right"
                               Margin="0,0,-9,0"
                               Style="{StaticResource GridViewColumnHeaderGripper}" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="HeaderBorder" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HeaderBorder" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" TargetName="HeaderBorder" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HeaderBorder" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Role" Value="Floating">
                <Setter Property="Opacity" Value="0.7" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type GridViewColumnHeader}">
                            <Canvas Name="PART_FloatingHeaderCanvas">
                                <Rectangle Fill="{StaticResource ContentBackground}"
                                           Width="{TemplateBinding ActualWidth}"
                                           Height="{TemplateBinding ActualHeight}" />
                            </Canvas>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="Role" Value="Padding">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type GridViewColumnHeader}">
                            <Border Name="HeaderBorder"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Padding="{TemplateBinding Padding}">
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="{x:Static GridView.GridViewStyleKey}"
           TargetType="{x:Type ListView}">
        <Setter Property="Background"
                Value="Transparent"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Dark.BorderThickness}"/>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility"
                Value="Auto"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility"
                Value="Auto"/>
        <Setter Property="ScrollViewer.CanContentScroll"
                Value="true"/>
        <Setter Property="VerticalContentAlignment"
                Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListView}">
                    <Border Name="Bd"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            Background="{TemplateBinding Background}"
                            SnapsToDevicePixels="true">
                        <ScrollViewer Style="{StaticResource {x:Static GridView.GridViewScrollViewerStyleKey}}"
                                      Padding="{TemplateBinding Padding}">
                            <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                        </ScrollViewer>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsGrouping" Value="true">
                            <Setter Property="ScrollViewer.CanContentScroll" Value="false" />
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter 
                                    Property="Opacity"
                                    Value="0.5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="{x:Static GridView.GridViewItemContainerStyleKey}"
           TargetType="{x:Type ListViewItem}">
        <Setter Property="FocusVisualStyle"
                Value="{StaticResource ListViewItemFocusVisual}"/>
        <Setter Property="Background"
                Value="Transparent"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Dark.BorderThickness}"/>
        <Setter Property="Padding" Value="4,1"/>
        <Setter Property="MinHeight" Value="{StaticResource Windows11Dark.MinHeight}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListViewItem}">
                    <Border x:Name="Bd" 
                            BorderBrush="{TemplateBinding BorderBrush}" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            Background="{TemplateBinding Background}" 
                            Padding="{TemplateBinding Padding}" 
                            SnapsToDevicePixels="true"
                            >
                        <GridViewRowPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" >
                            <GridViewRowPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </GridViewRowPresenter.Resources>
                        </GridViewRowPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="true">
                            <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource HoveredForeground}"/>
                        </Trigger>
                        <Trigger Property="IsSelected"
                                 Value="true">
                            <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource SelectedForeground}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected"
                                           Value="true"/>
                                <Condition Property="Selector.IsSelectionActive"
                                           Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundInactive}"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundInactive}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected"
                                           Value="true"/>
                                <Condition Property="IsMouseOver"
                                           Value="true"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource SelectedForeground}"/>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter Property="Background" TargetName="Bd"
                                    Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="Transparent"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Foreground" 
                                    Value="{StaticResource HoveredForeground}"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="true">
                <Setter Property="Foreground" Value="{StaticResource SelectedForeground}"/>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsSelected" Value="true"/>
                    <Condition Property="Selector.IsSelectionActive" Value="false"/>
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
            </MultiTrigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="WPFListViewItemStyle"
           TargetType="{x:Type ListViewItem}">
        <Setter Property="FocusVisualStyle"
                Value="{StaticResource ListViewItemFocusVisual}"/>
        <Setter Property="Background"
                Value="Transparent"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Dark.BorderThickness}"/>
        <Setter Property="Padding" Value="4,1"/>
        <Setter Property="MinHeight" Value="{StaticResource Windows11Dark.MinHeight}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListViewItem}">
                    <Border x:Name="Bd" 
                            BorderBrush="{TemplateBinding BorderBrush}" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            Background="{TemplateBinding Background}" 
                            Padding="{TemplateBinding Padding}" 
                            SnapsToDevicePixels="true"
                            >
                        <GridViewRowPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" >
                            <GridViewRowPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </GridViewRowPresenter.Resources>
                       </GridViewRowPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource HoveredForeground}"/>
                        </Trigger>
                        <Trigger Property="IsSelected"
                                 Value="true">
                            <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource SelectedForeground}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected"
                                           Value="true"/>
                                <Condition Property="IsMouseOver"
                                           Value="true"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource SelectedForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected"
                                           Value="true"/>
                                <Condition Property="Selector.IsSelectionActive"
                                           Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundInactive}"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundInactive}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter Property="Background" TargetName="Bd"
                                    Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="Transparent"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="GridView.ColumnCollection" Value="{x:Null}">
                <Setter Property="FocusVisualStyle"
                Value="{StaticResource ListViewItemFocusVisual}"/>
                <Setter Property="Background"
                Value="Transparent"/>
                <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"/>
                <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
                <Setter Property="BorderThickness" 
                        Value="{StaticResource Windows11Dark.BorderThickness}"/>
                <Setter Property="Padding"
                        Value="7,1"/>
                <Setter Property="Margin"
                        Value="4,2"/>
                <Setter Property="MinHeight" Value="{StaticResource Windows11Dark.MinHeight}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type ListViewItem}">
                            <Grid>
                                <Border x:Name="SelectionIndicator"
                                        HorizontalAlignment="Left"            
                                        CornerRadius="1.5"
                                        Height="12"
                                        Width="2"
                                        Visibility="Collapsed"     
                                        Background="{StaticResource PrimaryBackground}" />
                                
                                <Border x:Name="Bd" 
                                    CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                    BorderBrush="{TemplateBinding BorderBrush}" 
                                    BorderThickness="{TemplateBinding BorderThickness}" 
                                    Background="{TemplateBinding Background}" 
                                    Padding="{TemplateBinding Padding}" 
                                    SnapsToDevicePixels="true"
                                    >
                                    <ContentPresenter x:Name="content"
                                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                                        <ContentPresenter.Resources>
                                            <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                        </ContentPresenter.Resources>
                                    </ContentPresenter>                                    
                                </Border>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <EventTrigger SourceName="Bd" RoutedEvent="Border.MouseDown">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="SelectionIndicator"
                                                             Storyboard.TargetProperty="Height"
                                                             From="12"
                                                             To="7"
                                                             Duration="0:0:0.167" />
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <EventTrigger SourceName="Bd" RoutedEvent="Border.MouseUp">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="SelectionIndicator"
                                                             Storyboard.TargetProperty="Height"
                                                             From="7"
                                                             To="12"
                                                             Duration="0:0:0.167" />
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundHovered}"/>
                                    <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource BorderAlt}"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="true">
                                    <Setter Property="Background" TargetName="Bd" Value="{StaticResource ContentBackgroundSelected}"/>
                                    <Setter Property="BorderBrush" TargetName="Bd" Value="{StaticResource ContentBackgroundSelected}"/>
                                    <Setter Property="Visibility" TargetName="SelectionIndicator" Value="Visible"/>
                                </Trigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsSelected" Value="true"/>
                                        <Condition Property="Selector.IsSelectionActive" Value="false"/>
                                    </MultiTrigger.Conditions>
                                    <Setter Property="Background" TargetName="Bd" Value="{StaticResource ContentBackgroundInactive}"/>
                                    <Setter Property="BorderBrush" TargetName="Bd" Value="{StaticResource ContentBackgroundInactive}"/>
                                </MultiTrigger>
                                <Trigger Property="IsEnabled" Value="false">
                                    <Setter Property="Background" TargetName="Bd" Value="Transparent"/>
                                    <Setter Property="BorderBrush" TargetName="Bd" Value="Transparent"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Foreground" 
                                    Value="{StaticResource HoveredForeground}"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="true">
                <Setter Property="Foreground" Value="{StaticResource SelectedForeground}"/>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsSelected" Value="true"/>
                    <Condition Property="Selector.IsSelectionActive" Value="false"/>
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
            </MultiTrigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource WPFListViewItemStyle}" TargetType="{x:Type ListViewItem}"/>

    <Style x:Key="WPFListViewStyle"
           TargetType="{x:Type ListView}">
        <Setter Property="Background"
                Value="Transparent"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness}"/>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility"
                Value="Auto"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility"
                Value="Auto"/>
        <Setter Property="ScrollViewer.CanContentScroll"
                Value="true"/>
        <Setter Property="VerticalContentAlignment"
                Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListView}">
                    <Border Name="Bd"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            Background="{TemplateBinding Background}"
                            SnapsToDevicePixels="true">
                        <ScrollViewer x:Name="scrollviewer" 
                                      Focusable="false"
                                      Style="{StaticResource {x:Static GridView.GridViewScrollViewerStyleKey}}"
                                      Padding="{TemplateBinding Padding}">
                            <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                        </ScrollViewer>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="View" Value="{x:Null}">
                            <Setter TargetName="scrollviewer" Property="Style" Value="{StaticResource WPFScrollViewerStyle}" />
                        </Trigger>
                        <Trigger Property="IsGrouping"
                                 Value="true">
                            <Setter Property="ScrollViewer.CanContentScroll" Value="false"/>
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter
                                    Property="Opacity"
                                    Value="0.5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource WPFListViewStyle}" TargetType="{x:Type ListView}"/>

</ResourceDictionary>
