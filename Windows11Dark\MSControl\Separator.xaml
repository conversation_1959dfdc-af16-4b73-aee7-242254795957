<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

	<ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>
    
    <Style x:Key="WPFSeparatorStyle"
           TargetType="{x:Type Separator}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="BorderThickness" Value="1,1,0,0"/>
        <Setter Property="Background"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="Margin"
                Value="2"/>
        <Setter Property="Focusable"
                Value="false"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Separator}">
                    <Border x:Name="Bd" Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="true"
                            Height="{TemplateBinding Height}"/>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="2"/>
                        </Trigger>                         
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource WPFSeparatorStyle}" TargetType="{x:Type Separator}"/>
</ResourceDictionary>
