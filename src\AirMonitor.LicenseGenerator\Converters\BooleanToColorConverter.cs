using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace AirMonitor.LicenseGenerator.Views;

/// <summary>
/// 布尔值到颜色转换器
/// </summary>
public class BooleanToColorConverter : IValueConverter
{
    public static readonly BooleanToColorConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return boolValue 
                ? new SolidColorBrush(Color.FromRgb(16, 124, 16))  // 成功绿色
                : new SolidColorBrush(Color.FromRgb(209, 52, 56)); // 错误红色
        }
        return new SolidColorBrush(Colors.Gray);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
