<ResourceDictionary 
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:microsoftWindowsThemes="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Luna"
             xmlns:system="clr-namespace:System;assembly=mscorlib"
             xmlns:xlsIo="clr-namespace:Syncfusion.XlsIO;assembly=Syncfusion.XlsIO.Base"
             xmlns:spreadsheet="clr-namespace:Syncfusion.UI.Xaml.Spreadsheet;assembly=Syncfusion.SfSpreadsheet.WPF"
             xmlns:tools="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
             xmlns:sharedt="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
             xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
             xmlns:shared="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF">

    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.SfSpreadsheet.WPF;component/Themes/RibbonIcons.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfSpreadsheet/SfSpreadsheetIcons.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBlock.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MaskedTextBox/MaskedTextBox.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <spreadsheet:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
    <spreadsheet:VisibilityToBoolConverter x:Key="VisibilityToBoolConverter"/>
    <spreadsheet:NullToStringConverter x:Key="NullToStringConverter"/>
    <spreadsheet:DateToStringConverter x:Key="DateToStringConverter"/>
    <spreadsheet:NullToBoolConverter x:Key="NullToBoolConverter"/>
    <spreadsheet:ColorConverter x:Key="colorConverter" />
    
    <system:String x:Key="Accounting">$* #,##0.00;$* (#,##0.00);$* -??;@</system:String>
    <system:String x:Key="Percentage">0%</system:String>
    <system:String x:Key="Comma">* #,##0.00;* (#,##0.00);* -??;@</system:String>
    <system:Boolean x:Key="True">true</system:Boolean>
    <system:Boolean x:Key="False">false</system:Boolean>

    <Style x:Key="SyncfusionSpreadsheetMaskedTextBoxStyle" TargetType="{x:Type sharedt:MaskedTextBox}" BasedOn="{StaticResource SyncfusionMaskedTextBoxStyle}">
        <Setter Property="ContextMenu">
            <Setter.Value>
                <ContextMenu>
                    <MenuItem Command="ApplicationCommands.Cut" Header="Cut"/>
                    <MenuItem Command="ApplicationCommands.Copy" Header="Copy"/>
                    <MenuItem Command="ApplicationCommands.Paste" Header="Paste"/>
                </ContextMenu>
            </Setter.Value>
        </Setter>
    </Style>

    <!--DataBarsMenuStyle-->
    <Style x:Key="SyncfusionDataBarsRibbonMenuItemStyle" TargetType="tools:RibbonMenuItem">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="tools:RibbonMenuItem">
                    <Grid>
                        <tools:RibbonMenuGroup Width="126" IconBarEnabled="True">
                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=GradientFill}" Style="{StaticResource WPFTextBlockStyle}" FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" Padding="5,2,2,2" />
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingDataBars}"
                                                           CommandParameter="GradientBlue" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon >
                                        <Image Source="{StaticResource GradientBlueDataBarIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingDataBars}"
                                                           CommandParameter="GradientGreen" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource GradientGreenDataBarIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingDataBars}"
                                                           CommandParameter="GradientRed" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource GradientRedDataBarIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingDataBars}"
                                                           CommandParameter="GradientYellow" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource GradientYellowDataBarIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingDataBars}"
                                                           CommandParameter="GradientLightBlue" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource GradientLightBlueDataBarIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingDataBars}"
                                                           CommandParameter="GradientPurple" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource GradientPurpleDataBarIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=SolidFill}" Style="{StaticResource WPFTextBlockStyle}" FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" Padding="5,2,2,2"/>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingDataBars}"
                                                           CommandParameter="SolidBlue" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource SolidBlueDataBarIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingDataBars}"
                                                           CommandParameter="SolidGreen" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource SolidGreenDataBarIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingDataBars}"
                                                           CommandParameter="SolidRed" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource SolidRedDataBarIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingDataBars}"
                                                           CommandParameter="SolidYellow" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource SolidYellowDataBarIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingDataBars}"
                                                           CommandParameter="SolidLightBlue" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource SolidLightBlueDataBarIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingDataBars}"
                                                           CommandParameter="SolidPurple" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource SolidPurpleDataBarIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                        </tools:RibbonMenuGroup>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--IconSetsMenuStyle-->
    <Style x:Key="SyncfusionIconSetsRibbonMenuItemStyle" TargetType="tools:RibbonMenuItem">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="tools:RibbonMenuItem">
                    <Grid>
                        <tools:RibbonMenuGroup Width="254" IconBarEnabled="True">
                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CF_IconSets_Directional}" Style="{StaticResource WPFTextBlockStyle}" FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" Padding="5,2,2,2"/>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GreenUpArrowIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowSideArrowIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource RedDownArrowIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>ThreeArrows</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GrayUpArrowIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GraySideArrowIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GrayDownArrowIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>ThreeArrowsGray</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GreenUpArrowIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowUpInclineArrowIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowDownInclineArrowIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource RedDownArrowIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>FourArrows</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GrayUpArrowIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GrayUpInclineArrowIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GrayDownInclineArrowIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GrayDownArrowIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>FourArrowsGray</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GreenUpArrowIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowUpInclineArrowIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowSideArrowIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowDownInclineArrowIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource RedDownArrowIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>FiveArrows</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GrayUpArrowIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GrayUpInclineArrowIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GraySideArrowIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GrayDownInclineArrowIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GrayDownArrowIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>FiveArrowsGray</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CF_IconSets_Shapes}" Style="{StaticResource WPFTextBlockStyle}" FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" Padding="5,2,2,2"/>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GreenCircleIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowCircleIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource RedCircleWithBorderIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>ThreeTrafficLights1</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GreenTrafficLightIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowTrafficLightIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource RedTrafficLightIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>ThreeTrafficLights2</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GreenCircleIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowTriangleIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource RedDiamondIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>ThreeSigns</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GreenCircleIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowCircleIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource RedCircleWithBorderIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource BlackCircleWithBorderIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>FourTrafficLights</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource RedCircleIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource PinkCircleIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GrayCircleIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource BlackCircleIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>FourRedToBlack</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CF_IconSets_Indicators}" FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" Style="{StaticResource WPFTextBlockStyle}" Padding="5,2,2,2"/>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GreenCheckSymbolIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowExclamationSymbolIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource RedCrossSymbolIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>ThreeSymbols</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GreenCheckIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowExclamationIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource RedCrossIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>ThreeSymbols2</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource GreenFlagIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource YellowFlagIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource RedFlagIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>ThreeFlags</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CF_IconSets_Ratings}" FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" Style="{StaticResource WPFTextBlockStyle}" Padding="5,2,2,2"/>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource BlackCircleIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource CircleWithOneWhiteQuarterIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource CircleWithTwoWhiteQuartersIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource CircleWithThreeWhiteQuartersIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource CircleWithAllWhiteQuartersIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>FiveQuarters</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource SignalWithNoFillBarsIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource SignalWithOneFillBarIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource SignalWithTwoFillBarsIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource SignalWithThreeFillBarsIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource SignalWithFourFillBarsIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>FiveRating</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="127" Height="26" Padding="2,0,0,0" IconSize="110,16"
                                                           Command="{Binding Commands.ConditionalFormattingIconSets}" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <StackPanel Orientation="Horizontal">
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource SignalWithOneFillBarIcon}" />
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource SignalWithTwoFillBarsIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource SignalWithThreeFillBarsIcon}"/>
                                            <Image Height="16" Width="16" Margin="2,0,5,0" Source="{StaticResource SignalWithFourFillBarsIcon}"/>
                                        </StackPanel>
                                    </tools:RibbonMenuItem.Icon>
                                    <tools:RibbonMenuItem.CommandParameter>
                                        <xlsIo:ExcelIconSetType>FourRating</xlsIo:ExcelIconSetType>
                                    </tools:RibbonMenuItem.CommandParameter>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                        </tools:RibbonMenuGroup>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--ColorScalesMenuStyle-->
    <Style x:Key="SyncfusionColorScalesRibbonMenuItemStyle" TargetType="tools:RibbonMenuItem">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="tools:RibbonMenuItem">
                    <Grid>
                        <tools:RibbonMenuGroup Width="166" IconBarEnabled="True">
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingColorScales}"
                                                           CommandParameter="GreenYellowRed" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource GreenYellowRedIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingColorScales}"
                                                           CommandParameter="RedYellowGreen" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource RedYellowGreenIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingColorScales}"
                                                           CommandParameter="GreenWhiteRed" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource GreenWhiteRedIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingColorScales}"
                                                           CommandParameter="RedWhiteGreen" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource RedWhiteGreenIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingColorScales}"
                                                           CommandParameter="BlueWhiteRed" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource BlueWhiteRedIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingColorScales}"
                                                           CommandParameter="RedWhiteBlue" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource RedWhiteBlueIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingColorScales}"
                                                           CommandParameter="WhiteRed" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource WhiteRedIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingColorScales}"
                                                           CommandParameter="RedWhite" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource RedWhiteIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingColorScales}"
                                                           CommandParameter="GreenWhite" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource GreenWhiteIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingColorScales}"
                                                           CommandParameter="WhiteGreen" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource WhiteGreenIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingColorScales}"
                                                           CommandParameter="GreenYellow" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource GreenYellowIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                                <tools:RibbonMenuItem Width="42" Height="42" IconSize="30,26" Padding="2,0,0,0"
                                                           Command="{Binding Commands.ConditionalFormattingColorScales}"
                                                           CommandParameter="YellowGreen" IconBarEnabled="True">
                                    <tools:RibbonMenuItem.Icon>
                                        <Image Source="{StaticResource YellowGreenIcon}" />
                                    </tools:RibbonMenuItem.Icon>
                                </tools:RibbonMenuItem>
                            </StackPanel>
                        </tools:RibbonMenuGroup>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSpreadsheetRibbonButtonStyle" TargetType="tools:RibbonButton">
        <Setter Property="FocusVisualStyle"  Value="{x:Null}"/>
        <Setter Property="CornerRadius" Value="0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="Label" Value=""/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
        <Setter Property="VerticalContentAlignment" Value="Stretch"/>
        <Style.Triggers>
            <Trigger Property="SizeForm" Value="Small">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="tools:RibbonButton">
                            <Grid VerticalAlignment="{TemplateBinding VerticalAlignment}" HorizontalAlignment="{TemplateBinding HorizontalAlignment}">
                                <Border CornerRadius="{TemplateBinding CornerRadius}"
                                        BorderThickness="{TemplateBinding BorderThickness}" Padding="{TemplateBinding  Padding}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        Background="{TemplateBinding Background}"
                                        Opacity="{TemplateBinding Opacity}"
                                        IsEnabled="{TemplateBinding IsEnabled}"
                                        MinHeight="24" Name="Bd">
                                    <Grid  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition />
                                        </Grid.ColumnDefinitions>
                                        <Border Name="PART_ImageBorder" Grid.Column="0">

                                            <ContentControl Margin="2" Width="16" Height="16"   
                                                   RenderOptions.BitmapScalingMode="NearestNeighbor"
                                                   ContentTemplate="{Binding Path=IconTemplate, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                        </Border>
                                        <TextBlock Grid.Column="1" 
                                                    Text="{TemplateBinding Label}" 
                                                    Foreground="{TemplateBinding Foreground}"
                                                    FontFamily="{TemplateBinding FontFamily}"
                                                    FontSize="{TemplateBinding FontSize}"
                                                    Margin="10,2,4,0"/>
                                    </Grid>
                                </Border>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsSmallImageVisible" Value="False">
                                    <Setter TargetName="PART_ImageBorder" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="Bd" Property="BorderBrush" Value="{StaticResource SecondaryBackgroundHovered}"/>
                                    <Setter TargetName="Bd" Property="Background" Value="{StaticResource SecondaryBackgroundHovered}"/>
                                </Trigger>
                                <Trigger Property="IsKeyboardFocused" Value="True">
                                    <Setter TargetName="Bd" Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
                                    <Setter TargetName="Bd" Property="Background" Value="{StaticResource SecondaryBackgroundSelected}"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter TargetName="PART_ImageBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
                                    <Setter TargetName="PART_ImageBorder" Property="Background" Value="{StaticResource SecondaryBackgroundSelected}"/>
                                    <Setter TargetName="PART_ImageBorder" Property="BorderThickness" Value="1"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="Bd" Property="BorderBrush" Value="{StaticResource SecondaryBackgroundSelected}"/>
                                    <Setter TargetName="Bd" Property="Background" Value="{StaticResource SecondaryBackgroundSelected}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.8"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--Link Button Style-->
    <Style x:Key="LinkButton" TargetType="Button">
        <Setter Property="Foreground" Value="#336699" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Width" Value="120"/>
        <Setter Property="HorizontalAlignment" Value="Left"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <TextBlock x:Name="Underline">
                        <ContentPresenter />
                    </TextBlock>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="TextDecorations" TargetName="Underline" Value="Underline"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfSpreadsheetRibbonStyle" TargetType="spreadsheet:SfSpreadsheetRibbon">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:SfSpreadsheetRibbon">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Busy">
                                    <Storyboard>
                                        <DoubleAnimation Duration="0"
                                                         Storyboard.TargetName="Ribbon"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.5" />
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Normal" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="ExcelVersion">
                                <VisualState x:Name="Excel2010">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TableFormatStyle2010"
                                                                       Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Visible</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CellStyle2010"
                                                                       Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Visible</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TableFormatStyle2013"
                                                                       Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CellStyle2013"
                                                                       Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Excel2013"/>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <tools:Ribbon x:Name="Ribbon" MinWidth="200" BackStageHeader="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=File}" FocusVisualStyle="{x:Null}">
                            <tools:Ribbon.BackStage>
                                <tools:Backstage Name="RibbonBackStage" FocusVisualStyle="{x:Null}">
                                    <tools:BackstageTabItem x:Name="File_Info" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Info}">
                                        <Grid x:Name="File_InfoGrid" MinWidth="1000">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="4*" />
                                                <ColumnDefinition Width="7*" />
                                            </Grid.ColumnDefinitions>
                                            <StackPanel>
                                                <TextBlock Opacity="0.6" Margin="25,20,0,0"
                                                            Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Info}" />
                                                <Grid Margin="25,20,0,0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto" />
                                                        <ColumnDefinition />
                                                    </Grid.ColumnDefinitions>

                                                    <tools:DropDownButton x:Name="File_ProtectWorkbook" Grid.Column="0"
                                                                               Width="94"
                                                                               BorderBrush="{StaticResource BorderAlt}"
                                                                               Height="75" BorderThickness="1"
                                                                               HorizontalAlignment="Left"
                                                                               VerticalAlignment="Top"
                                                                               VerticalContentAlignment="Center"
                                                                               Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectWorkbook}"
                                                                               IconTemplate="{StaticResource PermissionIcon}"
                                                                               SizeForm="Large">

                                                        <tools:RibbonMenuGroup Width="300" Height="195" IconBarEnabled="True">
                                                            <tools:SimpleMenuButton x:Name="File_Encrypt" Command="{Binding Commands.FileEncrypt}" ClickMode="Press" Height="65" Width="300" IconTemplate="{StaticResource PermissionIcon}" IconSize="32,32" Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=EncryptWithPassword}"
                                                                                         Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=EncryptWithPasswordDesc}"/>

                                                            <tools:SimpleMenuButton x:Name="File_ProtectCurrentSheet" Command="{Binding Commands.ProtectSheet}" Height="65" Width="300" IconTemplate="{StaticResource ProtectSheetLargeIcon}" IconSize="32,32" Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectCurrentSheet}"
                                                                                         Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectCurrentSheetDesc}"/>

                                                            <tools:SimpleMenuButton x:Name="File_ProtectWorkbookStructure" Command="{Binding Commands.ProtectWorkbook}" Height="65" Width="300" IconTemplate="{StaticResource ProtectWorkbookIcon}" IconSize="32,32" Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectWorkbookStructure}"
                                                                                         Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectWorkbookStructureDesc}"/>
                                                        </tools:RibbonMenuGroup>
                                                    </tools:DropDownButton>

                                                    <StackPanel Grid.Column="1"
                                                                Margin="3"
                                                                VerticalAlignment="Top">
                                                        <TextBlock Margin="3" FontSize="18"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectWorkbook}" />
                                                        <TextBlock Margin="3" TextWrapping="Wrap"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectWorkbookText}"/>
                                                    </StackPanel>
                                                </Grid>
                                            </StackPanel>
                                            <Border Grid.Column="1" Margin="10,68,0,0"
                                                    Width="Auto"
                                                    HorizontalAlignment="Left"
                                                    BorderBrush="#FFBCBCBC"
                                                    BorderThickness="0">
                                                <Grid x:Name="File_PropertiesGrid" MinWidth="210" Width="Auto" HorizontalAlignment="Center"
                                                          Margin="55,5,5,5"
                                                          VerticalAlignment="Top">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="2*" />
                                                        <ColumnDefinition Width="3*" />
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="40" />
                                                        <RowDefinition Height="26" />
                                                        <RowDefinition Height="26" />
                                                        <RowDefinition Height="0" />
                                                        <RowDefinition Height="26" />
                                                        <RowDefinition Height="0" />
                                                        <RowDefinition Height="0" />
                                                        <RowDefinition Height="40" />
                                                        <RowDefinition Height="26" />
                                                        <RowDefinition Height="26" />
                                                        <RowDefinition Height="26" />
                                                        <RowDefinition Height="40" />
                                                        <RowDefinition Height="0" />
                                                        <RowDefinition Height="26" />
                                                        <RowDefinition Height="26" />
                                                        <RowDefinition Height="0" />
                                                        <RowDefinition Height="0" />
                                                        <RowDefinition Height="26" />
                                                    </Grid.RowDefinitions>
                                                    <!--  Document properties  -->

                                                    <TextBlock Margin="0,10,0,0"
                                                                   HorizontalAlignment="Left"
                                                                   FontSize="15"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperties}" />
                                                    <TextBlock Grid.Row="1"
                                                                   Grid.Column="0" Margin="0,3,0,3"
                                                                   HorizontalAlignment="Left"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_Title}" />

                                                    <sharedt:MaskedTextBox Grid.Row="1" Margin="15,3,0,3" Style="{StaticResource SyncfusionSpreadsheetMaskedTextBoxStyle}"
                                                                             Grid.Column="1"
                                                                             Width="180"
                                                                             HorizontalAlignment="Left"
                                                                             BorderBrush="Transparent"
                                                                             Text="{Binding Path=Workbook.BuiltInDocumentProperties.Title,
                                                                                            Mode=TwoWay}" 
                                                                             WatermarkText="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentMask_Title}"></sharedt:MaskedTextBox>
                                                    <TextBlock Grid.Row="2"
                                                                   Grid.Column="0" Margin="0,3,0,3"
                                                                   HorizontalAlignment="Left"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_Tag}" />
                                                    <sharedt:MaskedTextBox Grid.Row="2" Margin="15,3,0,3" Style="{StaticResource SyncfusionSpreadsheetMaskedTextBoxStyle}"
                                                                             Grid.Column="1"
                                                                             Width="180"
                                                                             HorizontalAlignment="Left"
                                                                             BorderBrush="Transparent"
                                                                             WatermarkText="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentMask_Tag}"
                                                                             Text="{Binding Path=Workbook.BuiltInDocumentProperties.Keywords,
                                                                                            Mode=TwoWay}" />
                                                    <TextBlock Grid.Row="3" Margin="0,3,0,3"
                                                                   Grid.Column="0"
                                                                   HorizontalAlignment="Left"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_Comments}" />
                                                    <sharedt:MaskedTextBox Grid.Row="3" Margin="15,3,0,3"
                                                                             Grid.Column="1" Style="{StaticResource SyncfusionSpreadsheetMaskedTextBoxStyle}"
                                                                             Width="180"
                                                                             HorizontalAlignment="Left"
                                                                             BorderBrush="Transparent"
                                                                             WatermarkText="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentMask_Comments}"
                                                                             Text="{Binding Path=Workbook.BuiltInDocumentProperties.Comments,
                                                                                            Mode=TwoWay}" />
                                                    <TextBlock Grid.Row="4" Margin="0,3,0,3"
                                                                   Grid.Column="0"
                                                                   HorizontalAlignment="Left"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_Category}" />
                                                    <sharedt:MaskedTextBox Margin="15,3,0,3"
                                                                             Grid.Row="4" Style="{StaticResource SyncfusionSpreadsheetMaskedTextBoxStyle}"
                                                                             Grid.Column="1"
                                                                             Width="180"
                                                                             HorizontalAlignment="Left"
                                                                             BorderBrush="Transparent" 
                                                                             WatermarkText="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentMask_Category}"
                                                                             Text="{Binding Path=Workbook.BuiltInDocumentProperties.Category,
                                                                                            Mode=TwoWay}" />
                                                    <TextBlock Grid.Row="5" Margin="0,3,0,3"
                                                                   Grid.Column="0"
                                                                   HorizontalAlignment="Left"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_Subject}" />
                                                    <sharedt:MaskedTextBox Style="{StaticResource SyncfusionSpreadsheetMaskedTextBoxStyle}"
                                                                             Grid.Row="5" Margin="15,3,0,3"
                                                                             Grid.Column="1"
                                                                             Width="180"
                                                                             HorizontalAlignment="Left"
                                                                             BorderBrush="Transparent" 
                                                                             WatermarkText="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentMask_Subject}"
                                                                             Text="{Binding Path=Workbook.BuiltInDocumentProperties.Subject,
                                                                                            Mode=TwoWay}"/>

                                                    <TextBlock Grid.Row="6" Margin="0,3,0,3"
                                                                   Grid.Column="0"
                                                                   HorizontalAlignment="Left"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_Company}" />
                                                    <sharedt:MaskedTextBox Margin="15,3,0,3" Style="{StaticResource SyncfusionSpreadsheetMaskedTextBoxStyle}"
                                                                             Grid.Row="6"
                                                                             Grid.Column="1"
                                                                             Width="180"
                                                                             HorizontalAlignment="Left"
                                                                             BorderBrush="Transparent"
                                                                             WatermarkText="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentMask_Company}"
                                                                             Text="{Binding Path=Workbook.BuiltInDocumentProperties.Company,
                                                                                            Mode=TwoWay}"/>

                                                    <TextBlock Grid.Row="7"
                                                                   Grid.ColumnSpan="2"
                                                                   Margin="0,10,0,0"
                                                                   HorizontalAlignment="Left"
                                                                   FontSize="15"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_RelatedDates}" />
                                                    <TextBlock Grid.Row="8" Margin="0,3,0,3"
                                                                   Grid.Column="0"
                                                                   HorizontalAlignment="Left"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_LastModified}" />
                                                    <TextBlock Grid.Row="8" Margin="15,3,0,3"
                                                                             Grid.Column="1"
                                                                             Width="220"
                                                                             HorizontalAlignment="Left"
                                                                             Text="{Binding Path=Workbook.BuiltInDocumentProperties.LastSaveDate,
                                                                                            Mode=TwoWay,Converter={StaticResource DateToStringConverter}}" />
                                                    <TextBlock Grid.Row="9" Margin="0,3,0,3"
                                                                   Grid.Column="0"
                                                                   HorizontalAlignment="Left"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_Created}" />
                                                    <TextBlock Grid.Row="9" Margin="15,3,0,3"
                                                                             Grid.Column="1"
                                                                             Width="220"
                                                                             HorizontalAlignment="Left"
                                                                             Foreground="#ADADAD"
                                                                             Text="{Binding Path=Workbook.BuiltInDocumentProperties.CreationDate,
                                                                                            Mode=TwoWay,Converter={StaticResource DateToStringConverter}}" />
                                                    <TextBlock Grid.Row="10" Margin="0,3,0,3"
                                                                   Grid.Column="0"
                                                                   HorizontalAlignment="Left"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_LastPrinted}" />
                                                    <TextBlock Grid.Row="10" Margin="15,3,0,3"
                                                                             Grid.Column="1"
                                                                             Width="220"
                                                                             HorizontalAlignment="Left"
                                                                             Text="{Binding Path=Workbook.BuiltInDocumentProperties.LastPrinted,
                                                                                            Mode=TwoWay,Converter={StaticResource DateToStringConverter}}" />
                                                    <TextBlock Grid.Row="11"
                                                                   Grid.ColumnSpan="2"
                                                                   Margin="0,10,0,0"
                                                                   HorizontalAlignment="Left"
                                                                   FontSize="15"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_RelatedPeople}" />

                                                    <TextBlock Grid.Row="12" Margin="0,3,0,3"
                                                                   Grid.Column="0"
                                                                   HorizontalAlignment="Left"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_Manager}" />
                                                    <sharedt:MaskedTextBox Margin="15,3,0,3" Style="{StaticResource SyncfusionSpreadsheetMaskedTextBoxStyle}"
                                                                             Grid.Row="12"
                                                                             Grid.Column="1"
                                                                             Width="180"
                                                                             HorizontalAlignment="Left"
                                                                             BorderBrush="Transparent" 
                                                                             WatermarkText="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentMask_Manager}"
                                                                             Text="{Binding Path=Workbook.BuiltInDocumentProperties.Manager,
                                                                                            Mode=TwoWay}"/>
                                                    <TextBlock Grid.Row="13" Margin="0,3,0,3"
                                                                   Grid.Column="0"
                                                                   HorizontalAlignment="Left"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_Author}" />
                                                    <sharedt:MaskedTextBox Margin="15,3,0,3" Style="{StaticResource SyncfusionSpreadsheetMaskedTextBoxStyle}"
                                                                             Grid.Row="13"
                                                                             Grid.Column="1"
                                                                             Width="180"
                                                                             HorizontalAlignment="Left"
                                                                             BorderBrush="Transparent" 
                                                                             WatermarkText="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentMask_Author}"
                                                                             Text="{Binding Path=Workbook.Author,
                                                                                            Mode=TwoWay}" />
                                                    <TextBlock Grid.Row="14" Margin="0,3,0,3"
                                                                   Grid.Column="0"
                                                                   HorizontalAlignment="Left"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_LastModifiedBy}" />
                                                    <TextBlock Grid.Row="14" Margin="17,3,0,3"
                                                                             Grid.Column="1"
                                                                             Width="220"
                                                                             HorizontalAlignment="Left"
                                                                             Text="{Binding Path=Workbook.BuiltInDocumentProperties.LastAuthor,
                                                                                            Mode=TwoWay,Converter={StaticResource NullToStringConverter}}" />
                                                    <TextBlock Grid.Row="15"
                                                                   Grid.ColumnSpan="2"
                                                                   Margin="0,10,0,0"
                                                                   HorizontalAlignment="Left"
                                                                   FontSize="15"
                                                                   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DocumentProperty_RelatedDocuments}"/>
                                                    <Button Grid.Row="16" x:Name="File_OpenFileLocation">
                                                        <StackPanel Orientation="Horizontal">
                                                            <Image Width="16" Height="16" Source="{StaticResource OpenFileLocationIcon}" />
                                                            <TextBlock Margin="5,0,0,0" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=OpenFileLocation}"/>
                                                        </StackPanel>
                                                    </Button>

                                                    <Button x:Name="File_ShowPropertiesButton"
                                                                Grid.Row="17"
                                                                Grid.ColumnSpan="2"
                                                                Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ShowAllProperties}"
                                                                Style="{StaticResource LinkButton}" Width="200"/>
                                                </Grid>
                                            </Border>
                                        </Grid>
                                    </tools:BackstageTabItem>

                                    <tools:BackstageTabItem x:Name="File_New" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=New}">
                                        <Grid>
                                            <StackPanel >
                                                <Grid Margin="25,20,0,0">
                                                    <TextBlock FontFamily="Segoe UI"
                                                               FontSize="36"
                                                               FontWeight="Normal"
                                                               Foreground="{StaticResource ContentForeground}"
                                                               Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=New}" />
                                                </Grid>
                                                <Grid Margin="25,20,0,0" HorizontalAlignment="Left" Width="300">
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="20"/>
                                                    </Grid.RowDefinitions>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto" />
                                                        <ColumnDefinition />
                                                    </Grid.ColumnDefinitions>
                                                    <Button x:Name="Fie_BlackWorkbook" Grid.Column="0" BorderThickness="0"
                                                            Width="221" Height="218" Style="{StaticResource WPFFlatButtonStyle}"
                                                            HorizontalAlignment="Left" VerticalAlignment="Center" VerticalContentAlignment="Bottom" Command="{Binding Commands.FileNew}">
                                                        <StackPanel Margin="0,0,0,30" Background="Transparent"  >
                                                            <Image Width="202" Height="156" Margin="0,0,1,0" Source="{StaticResource BlankWorkbookIcon}"/>
                                                            <TextBlock Width="150" Height="20" Opacity="0.7" FontWeight="SemiBold" Margin="0,5,0,0" HorizontalAlignment="Left" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=BlankWorkbook}"/>
                                                        </StackPanel>
                                                    </Button>
                                                </Grid>
                                            </StackPanel>
                                        </Grid>
                                    </tools:BackstageTabItem>

                                    <tools:BackStageCommandButton x:Name="File_Open"
                                                                       Command="{Binding Commands.FileOpen}"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Open}"/>
                                    <tools:BackStageCommandButton x:Name="File_Save"
                                                                       Command="{Binding Commands.FileSave}"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Save}"/>
                                    <tools:BackStageCommandButton x:Name="File_SaveAs"
                                                                       Command="{Binding Commands.FileSaveAs}"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=SaveAs}"/>
                                    <tools:BackStageCommandButton x:Name="File_Close"
                                                                       Command="{Binding Commands.FileClose}"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Close}"/>
                                </tools:Backstage>
                            </tools:Ribbon.BackStage>
                            <!--HomeRibbonTab-->
                            <tools:RibbonTab x:Name="HomeRibbonTab" IsChecked="True"
                                                  Caption="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Home}">
                                <!--  Clipboard  -->
                                <tools:RibbonBar IconTemplate="{StaticResource CollapsePasteIcon}"
                                                      Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Clipboard}" IsLauncherButtonVisible="False">
                                    <tools:RibbonButton Margin="4,2,0,0" IconStretch="Uniform"
                                                             Command="{Binding Path=Commands.Paste}"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Paste}"
                                                             IconTemplate="{StaticResource PasteIcon}"
                                                             SizeForm="Large">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=PasteToolTipHeader}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=PasteToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                    <tools:RibbonButton Margin="0,2,4,0"
                                                             Command="{Binding Path=Commands.Cut}" IconStretch="Uniform"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cut}"
                                                             SizeForm="Small"
                                                             IconTemplate="{StaticResource CutIcon}">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CutToolTipHeader}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CutToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                    <tools:RibbonButton Margin="0,2,4,0"
                                                             Command="{Binding Path=Commands.Copy}" IconStretch="Uniform"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Copy}"
                                                             SizeForm="Small"
                                                             IconTemplate="{StaticResource CopyIcon}">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CopyToolTipHeader}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CopyToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                </tools:RibbonBar>

                                <!--  Font  -->
                                <tools:RibbonBar IconTemplate="{StaticResource CollapseFontIcon}"
                                                      Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Font}"
                                                      BorderThickness="0"
                                                      IsLargeButtonPanel="False" 
                                                      tools:RibbonBar.LauncherCommand="{Binding Path=Commands.FormatCells}"
                                                      tools:RibbonBar.LauncherCommandParameter="2"
                                                      IsLauncherButtonVisible="True">
                                    <tools:RibbonBar.ToolTip>
                                        <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FontSettingsToolTipHeader}">
                                            <StackPanel>
                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FontSettingsToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                            </StackPanel>
                                        </tools:ScreenTip>
                                    </tools:RibbonBar.ToolTip>

                                    <tools:ButtonPanel Height="24" SeparatorVisibility="Collapsed">
                                        <tools:RibbonComboBox x:Name="Font_FontFamily" MaxDropDownHeight="500"
                                                                   IsEditable="True" Height="20"
                                                                   Width="117" ItemsSource="{TemplateBinding FontFamilyNames}">
                                            <tools:RibbonComboBox.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FontToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FontToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonComboBox.ToolTip>
                                            <tools:RibbonComboBox.ItemTemplate>
                                                <DataTemplate>
                                                    <TextBlock Padding="2" FontSize="13" Margin="2" Text="{Binding}" />
                                                </DataTemplate>
                                            </tools:RibbonComboBox.ItemTemplate>
                                            <tools:RibbonComboBox.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <VirtualizingStackPanel/>
                                                </ItemsPanelTemplate>
                                            </tools:RibbonComboBox.ItemsPanel>
                                        </tools:RibbonComboBox>

                                        <tools:RibbonComboBox x:Name="Font_FontSize" MaxDropDownHeight="400" 
                                                                   IsEditable="True" Focusable="False" Height="20"
                                                                   
                                                                   Width="43"
                                                                   ItemsSource="{Binding Path=FontSizeSource,
                                                                                         Mode=TwoWay,
                                                                                         RelativeSource={RelativeSource TemplatedParent}}">
                                            <tools:RibbonComboBox.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FontSizeToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FontSizeToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonComboBox.ToolTip>
                                        </tools:RibbonComboBox>

                                        <tools:RibbonButton x:Name="Font_IncreaseFontSize"
                                                                 IconTemplate="{StaticResource GrowFontIcon}" 
                                                                 Command="{Binding Commands.GrowFontSize}" CommandParameter="{Binding ElementName=Font_FontSize,Path=ItemsSource}"
                                                                 SizeForm="ExtraSmall"  Height="24" Width="24">
                                            <tools:RibbonButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=IncreaseFontSizeToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=IncreaseFontSizeToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonButton.ToolTip>
                                        </tools:RibbonButton>
                                        <tools:RibbonButton x:Name="Font_DecreaseFontSize"
                                                                 IconTemplate="{StaticResource ShrinkFontIcon}" 
                                                                 Command="{Binding Commands.ShrinkFontSize}" CommandParameter="{Binding ElementName=Font_FontSize,Path=ItemsSource}"
                                                                 SizeForm="ExtraSmall"  Height="24" Width="24">
                                            <tools:RibbonButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DecreaseFontSizeToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DecreaseFontSizeToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonButton.ToolTip>
                                        </tools:RibbonButton>

                                    </tools:ButtonPanel>

                                    <tools:ButtonPanel Height="24">
                                        <tools:RibbonButton x:Name="Font_Bold" Width="24" 
                                                                 Height="24"
                                                                 Command="{Binding Commands.FormatFontBold}"
                                                                 IsToggle="True"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource BoldIcon}">
                                            <tools:RibbonButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=BoldToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=BoldToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonButton.ToolTip>
                                        </tools:RibbonButton>
                                        <tools:RibbonButton x:Name="Font_Italic" Width="24"
                                                                 Height="24"
                                                                 Command="{Binding Commands.FormatFontItalic}"
                                                                 IsToggle="True"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource ItalicIcon}">
                                            <tools:RibbonButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ItalicToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ItalicToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonButton.ToolTip>
                                        </tools:RibbonButton>
                                        <tools:RibbonButton x:Name="Font_Underline" Width="24"
                                                                 Height="24"
                                                                 Command="{Binding Commands.FormatFontUnderline}"
                                                                 IsToggle="True"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource UnderlineIcon}">
                                            <tools:RibbonButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnderLineToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnderLineToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonButton.ToolTip>
                                        </tools:RibbonButton>
                                    </tools:ButtonPanel>

                                    <tools:ButtonPanel Height="24">
                                        <tools:SplitButton x:Name="Font_Border"
                                                                Command="{Binding Path=Commands.FormatBorder}"
                                                                CommandParameter="BottomBorder"
                                                                Height="24"
                                                                Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Border}" 
                                                                SizeForm="ExtraSmall"  
                                                                IconTemplate="{StaticResource BottomBorderIcon}">
                                            <tools:SplitButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=BottomBorderToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=BottomBorderToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:SplitButton.ToolTip>
                                            <tools:RibbonMenuGroup Width="225"
                                                                        Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Border}"
                                                                        IconBarEnabled="True">
                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.FormatBorder}"
                                                                           CommandParameter="BottomBorder"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=BottomBorder}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="Bottomborder">
                                                            <Path Data="M0,12 L13,12 13,13 0,13 z M12,10 L13,10 13,11 12,11 z M6,10 L7,10 7,11 6,11 z M0,10 L1,10 1,11 0,11 z M12,8 L13,8 13,9.0000001 12,9.0000001 z M6,8 L7,8 7,9.0000001 6,9.0000001 z M0,8 L1,8 1,9.0000001 0,9.0000001 z M12,6 L13,6 13,7 12,7 z M10,6 L11,6 11,7 10,7 z M8,6 L9,6 9,7 8,7 z M6,6 L7,6 7,7 6,7 z M4,6 L5,6 5,7 4,7 z M2,6 L3,6 3,7 2,7 z M0,6 L1,6 1,7 0,7 z M12,4 L13,4 13,5 12,5 z M6,4 L7,4 7,5 6,5 z M0,4 L1,4 1,5 0,5 z M12,2 L13,2 13,3 12,3 z M6,2 L7,2 7,3 6,3 z M0,2 L1,2 1,3 0,3 z M12,0 L13,0 13,1 12,1 z M10,0 L11,0 11,1 10,1 z M8,0 L9,0 9,1 8,1 z M6,0 L7,0 7,1 6,1 z M4,0 L5,0 5,1 4,1 z M2,0 L3,0 3,1 2,1 z M0,0 L1,0 1,1 0,1 z" Fill="{StaticResource IconColor}" Height="13" Stretch="Fill" Width="13"/>
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>

                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.FormatBorder}"
                                                                           CommandParameter="TopBorder"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TopBorder}" IconBarEnabled="True"
                                                                           >
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="TopBorderIcon">
                                                            <Path x:Name="TopBorder" Data="M12,12 L13,12 13,13 12,13 z M9.9999996,12 L11,12 11,13 9.9999996,13 z M5.9999995,12 L6.9999995,12 6.9999995,13 5.9999995,13 z M3.9999995,12 L4.9999995,12 4.9999995,13 3.9999995,13 z M1.9999995,12 L2.9999995,12 2.9999995,13 1.9999995,13 z M0,12 L0.99999952,12 0.99999952,13 0,13 z M8.0000011,11.999992 L9.0000008,11.999992 9.0000008,12.999992 8.0000011,12.999992 z M12,10 L13,10 13,11 12,11 z M5.9999995,10 L6.9999995,10 6.9999995,11 5.9999995,11 z M0,10 L0.99999952,10 0.99999952,11 0,11 z M12,8.0000002 L13,8.0000002 13,9.0000002 12,9.0000002 z M5.9999995,8.0000002 L6.9999995,8.0000002 6.9999995,9.0000002 5.9999995,9.0000002 z M0,8.0000002 L0.99999952,8.0000002 0.99999952,9.0000002 0,9.0000002 z M12,6 L13,6 13,7 12,7 z M9.9999996,6 L11,6 11,7 9.9999996,7 z M7.9999997,6 L8.9999996,6 8.9999996,7 7.9999997,7 z M5.9999995,6 L6.9999995,6 6.9999995,7 5.9999995,7 z M3.9999995,6 L4.9999995,6 4.9999995,7 3.9999995,7 z M1.9999995,6 L2.9999995,6 2.9999995,7 1.9999995,7 z M0,6 L0.99999952,6 0.99999952,7 0,7 z M12,4 L13,4 13,5 12,5 z M5.9999995,4 L6.9999995,4 6.9999995,5 5.9999995,5 z M0,4 L0.99999952,4 0.99999952,5 0,5 z M12,2 L13,2 13,3 12,3 z M5.9999995,2 L6.9999995,2 6.9999995,3 5.9999995,3 z M0,2 L0.99999952,2 0.99999952,3 0,3 z M0,0 L13,0 13,1 0,1 z" Fill="{StaticResource IconColor}" Height="13" Stretch="Fill" Width="13"/>
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.FormatBorder}"
                                                                           CommandParameter="LeftBorder"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LeftBorder}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="LeftBorderIcon">
                                                            <Path x:Name="LeftBorder" Data="M12,12 L13,12 13,13 12,13 z M10,12 L11,12 11,13 10,13 z M8,12 L9,12 9,13 8,13 z M6,12 L7,12 7,13 6,13 z M4,12 L5,12 5,13 4,13 z M2,12 L3,12 3,13 2,13 z M12,10 L13,10 13,11 12,11 z M6,10 L7,10 7,11 6,11 z M12,8 L13,8 13,9 12,9 z M6,8 L7,8 7,9 6,9 z M12,6 L13,6 13,7 12,7 z M10,6 L11,6 11,7 10,7 z M8,6 L9,6 9,7 8,7 z M6,6 L7,6 7,7 6,7 z M4,6 L5,6 5,7 4,7 z M2,6 L3,6 3,7 2,7 z M12,4 L13,4 13,5 12,5 z M6,4 L7,4 7,5 6,5 z M12,2 L13,2 13,3 12,3 z M6,2 L7,2 7,3 6,3 z M12,0 L13,0 13,0.99999994 12,0.99999994 z M10,0 L11,0 11,0.99999994 10,0.99999994 z M8,0 L9,0 9,0.99999994 8,0.99999994 z M6,0 L7,0 7,0.99999994 6,0.99999994 z M4,0 L5,0 5,0.99999994 4,0.99999994 z M2,0 L3,0 3,0.99999994 2,0.99999994 z M0,0 L1,0 1,13 0,13 z" Fill="{StaticResource IconColor}" Height="13" Stretch="Fill" Width="13"/>
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.FormatBorder}"
                                                                           CommandParameter="RightBorder"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RightBorder}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="RightBorderIcon">
                                                            <Path Data="M10,12 L11,12 11,13 10,13 z M8,12 L9.0000001,12 9.0000001,13 8,13 z M6,12 L7,12 7,13 6,13 z M4,12 L5,12 5,13 4,13 z M2,12 L3,12 3,13 2,13 z M0,12 L1,12 1,13 0,13 z M6,10 L7,10 7,11 6,11 z M0,10 L1,10 1,11 0,11 z M6,7.9999999 L7,7.9999999 7,9 6,9 z M0,7.9999999 L1,7.9999999 1,9 0,9 z M10,6 L11,6 11,7 10,7 z M8,6 L9.0000001,6 9.0000001,7 8,7 z M6,6 L7,6 7,7 6,7 z M4,6 L5,6 5,7 4,7 z M2,6 L3,6 3,7 2,7 z M0,6 L1,6 1,7 0,7 z M6,4 L7,4 7,5.0000001 6,5.0000001 z M0,4 L1,4 1,5.0000001 0,5.0000001 z M6,2 L7,2 7,3 6,3 z M0,2 L1,2 1,3 0,3 z M12,0 L13,0 13,13 12,13 z M10,0 L11,0 11,1 10,1 z M8,0 L9.0000001,0 9.0000001,1 8,1 z M6,0 L7,0 7,1 6,1 z M4,0 L5,0 5,1 4,1 z M2,0 L3,0 3,1 2,1 z M0,0 L1,0 1,1 0,1 z" Fill="{StaticResource IconColor}" Height="13" Stretch="Fill" Width="13"/>
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>

                                                <sharedt:MenuItemSeparator />

                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.FormatBorder}"
                                                                           CommandParameter="NoBorder"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NoBorder}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="NoBorderIcon">
                                                            <Path Data="M12,12 L13,12 13,13 12,13 z M10,12 L11,12 11,13 10,13 z M8,12 L8.9999999,12 8.9999999,13 8,13 z M6,12 L7,12 7,13 6,13 z M4,12 L5,12 5,13 4,13 z M2,12 L3,12 3,13 2,13 z M0,12 L1,12 1,13 0,13 z M12,10 L13,10 13,11 12,11 z M6,10 L7,10 7,11 6,11 z M0,10 L1,10 1,11 0,11 z M12,7.9999999 L13,7.9999999 13,9 12,9 z M6,7.9999999 L7,7.9999999 7,9 6,9 z M0,7.9999999 L1,7.9999999 1,9 0,9 z M8,6 L9.0000001,6 9.0000001,7 8,7 z M6,6 L7,6 7,7 6,7 z M4,6 L5,6 5,7 4,7 z M2,6 L3,6 3,7 2,7 z M0,6 L1,6 1,7 0,7 z M12,5.9999998 L13,5.9999998 13,7 12,7 z M9.9999998,5.9999998 L11,5.9999998 11,7 9.9999998,7 z M12,4 L13,4 13,5.0000001 12,5.0000001 z M6,4 L7,4 7,5.0000001 6,5.0000001 z M0,4 L1,4 1,5.0000001 0,5.0000001 z M12,2 L13,2 13,3 12,3 z M6,2 L7,2 7,3 6,3 z M0,2 L1,2 1,3 0,3 z M12,0 L13,0 13,1 12,1 z M10,0 L11,0 11,1 10,1 z M8,0 L9.0000001,0 9.0000001,1 8,1 z M6,0 L7,0 7,1 6,1 z M4,0 L5,0 5,1 4,1 z M2,0 L3,0 3,1 2,1 z M0,0 L1,0 1,1 0,1 z" Fill="{StaticResource IconColor}" Height="13" Stretch="Fill" Width="13"/>
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                                <tools:RibbonMenuItem x:Name="AllBorder" Command="{Binding Path=Commands.FormatBorder}"
                                                                           CommandParameter="AllBorder"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=AllBorder}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="AllBorderIcon">
                                                            <Path Data="M7,7 L7,12 12,12 12,7 z M0.99999994,7 L0.99999994,12 6,12 6,7 z M7,1 L7,6 12,6 12,1 z M0.99999994,1 L0.99999994,6 6,6 6,1 z M0,0 L13,0 13,13 7,13 6,13 0,13 z" Fill="{StaticResource IconColor}" Height="13" Stretch="Fill" Width="13"/>
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                                <tools:RibbonMenuItem x:Name="OutSideBorder"   Command="{Binding Path=Commands.FormatBorder}"
                                                                           CommandParameter="OutSideBorder"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=OutSideBorder}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="OutSideBorderIcon">
                                                            <Path Data="M6,10 L7,10 7,11 6,11 z M6,8 L7,8 7,9 6,9 z M10,6 L11,6 11,7 10,7 z M8,6 L9,6 9,7 8,7 z M6,6 L7,6 7,7 6,7 z M4,6 L5,6 5,7 4,7 z M2,6 L3,6 3,7 2,7 z M6,4 L7,4 7,5 6,5 z M6,2 L7,2 7,3 6,3 z M0.99999994,0.99999994 L0.99999994,12 12,12 12,0.99999994 z M0,0 L13,0 13,13 0,13 z" Fill="{StaticResource IconColor}" Height="13" Stretch="Fill" Width="13" />
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.FormatBorder}"
                                                                           CommandParameter="ThickBoxBorder"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ThickBoxBorder}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="ThickBoxBorderIcon">
                                                            <Path x:Name="ThickBoxBorder" Data="M6.9900098,11.063012 L7.9900098,11.063012 7.9900098,12.063012 6.9900098,12.063012 z M6.9900098,9.0630121 L7.9900098,9.0630121 7.9900098,10.063012 6.9900098,10.063012 z M10.990009,7.0630116 L11.990009,7.0630116 11.990009,8.0630121 10.990009,8.0630121 z M8.9900093,7.0630116 L9.9900093,7.0630116 9.9900093,8.0630121 8.9900093,8.0630121 z M6.9900098,7.0630116 L7.9900098,7.0630116 7.9900098,8.0630121 6.9900098,8.0630121 z M4.9900098,7.0630116 L5.9900098,7.0630116 5.9900098,8.0630121 4.9900098,8.0630121 z M2.9900098,7.0630116 L3.9900098,7.0630116 3.9900098,8.0630121 2.9900098,8.0630121 z M6.9900098,5.0630116 L7.9900098,5.0630116 7.9900098,6.0630116 6.9900098,6.0630116 z M6.9900098,3.0630116 L7.9900098,3.0630116 7.9900098,4.0630116 6.9900098,4.0630116 z M2,2 L2,13 13,13 13,2 z M0,0 L15,0 15,15 0,15 z" Fill="{StaticResource IconColor}" Height="15" Stretch="Fill" Width="15" />
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>

                                                <sharedt:MenuItemSeparator />

                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.FormatBorder}"
                                                                           CommandParameter="ThickBottomBorder"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ThickBottomBorder}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="ThickBottomBorderIcon">
                                                            <Path Data="M0,12 L13,12 13,14 0,14 z M12,10 L13,10 13,11 12,11 z M6.0000001,10 L7.0000001,10 7.0000001,11 6.0000001,11 z M0,10 L1.0000002,10 1.0000002,11 0,11 z M12,8 L13,8 13,9 12,9 z M6.0000001,8 L7.0000001,8 7.0000001,9 6.0000001,9 z M0,8 L1.0000002,8 1.0000002,9 0,9 z M12,6 L13,6 13,7 12,7 z M10,6 L11,6 11,7 10,7 z M8.0000001,6 L9.0000001,6 9.0000001,7 8.0000001,7 z M6.0000001,6 L7.0000001,6 7.0000001,7 6.0000001,7 z M4.0000001,6 L5.0000001,6 5.0000001,7 4.0000001,7 z M2.0000001,6 L3.0000001,6 3.0000001,7 2.0000001,7 z M0,6 L1.0000002,6 1.0000002,7 0,7 z M12,4 L13,4 13,5 12,5 z M6.0000001,4 L7.0000001,4 7.0000001,5 6.0000001,5 z M0,4 L1.0000002,4 1.0000002,5 0,5 z M12,2 L13,2 13,3 12,3 z M6.0000001,2 L7.0000001,2 7.0000001,3 6.0000001,3 z M0,2 L1.0000002,2 1.0000002,3 0,3 z M12,0 L13,0 13,1 12,1 z M10,0 L11,0 11,1 10,1 z M8.0000001,0 L9.0000001,0 9.0000001,1 8.0000001,1 z M6.0000001,0 L7.0000001,0 7.0000001,1 6.0000001,1 z M4.0000001,0 L5.0000001,0 5.0000001,1 4.0000001,1 z M2.0000001,0 L3.0000001,0 3.0000001,1 2.0000001,1 z M0,0 L1.0000002,0 1.0000002,1 0,1 z" Fill="{StaticResource IconColor}" Height="14" Stretch="Fill" Width="13" />
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.FormatBorder}"
                                                                           CommandParameter="TopAndBottomBorder"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TopAndBottomBorder}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="TopAndBottomBorderIcon">
                                                            <Path x:Name="TopAndBottomBorder" Data="M0,13 L13,13 13,15 0,15 z M12,11 L13,11 13,12 12,12 z M6.0000001,11 L7.0000001,11 7.0000001,12 6.0000001,12 z M0,11 L0.99999997,11 0.99999997,12 0,12 z M12,9.0000001 L13,9.0000001 13,10 12,10 z M6.0000001,9.0000001 L7.0000001,9.0000001 7.0000001,10 6.0000001,10 z M0,9.0000001 L0.99999997,9.0000001 0.99999997,10 0,10 z M12,7.0000001 L13,7.0000001 13,8.0000001 12,8.0000001 z M10,7.0000001 L11,7.0000001 11,8.0000001 10,8.0000001 z M8.0000001,7.0000001 L9.0000001,7.0000001 9.0000001,8.0000001 8.0000001,8.0000001 z M6.0000001,7.0000001 L7.0000001,7.0000001 7.0000001,8.0000001 6.0000001,8.0000001 z M4.0000001,7.0000001 L5.0000001,7.0000001 5.0000001,8.0000001 4.0000001,8.0000001 z M2.0000001,7.0000001 L3.0000001,7.0000001 3.0000001,8.0000001 2.0000001,8.0000001 z M0,7.0000001 L0.99999997,7.0000001 0.99999997,8.0000001 0,8.0000001 z M12,5.0000001 L13,5.0000001 13,6.0000001 12,6.0000001 z M6.0000001,5.0000001 L7.0000001,5.0000001 7.0000001,6.0000001 6.0000001,6.0000001 z M0,5.0000001 L0.99999997,5.0000001 0.99999997,6.0000001 0,6.0000001 z M12,3.0000001 L13,3.0000001 13,4.0000001 12,4.0000001 z M6.0000001,3.0000001 L7.0000001,3.0000001 7.0000001,4.0000001 6.0000001,4.0000001 z M0,3.0000001 L0.99999997,3.0000001 0.99999997,4.0000001 0,4.0000001 z M0,0 L13,0 13,1.9999999 0,1.9999999 z" Fill="{StaticResource IconColor}" Height="15" Stretch="Fill" Width="13" />
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.FormatBorder}"
                                                                           CommandParameter="TopAndThickBottomBorder"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TopandThickBottomBorder}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="TopAndThickBottomBorderIcon">
                                                            <Path x:Name="TopAndThickBottomBorder" Data="M0,12 L13,12 13,14 0,14 z M12,10 L13,10 13,11 12,11 z M6.0000001,10 L7.0000001,10 7.0000001,11 6.0000001,11 z M0,10 L1.0000002,10 1.0000002,11 0,11 z M12,8 L13,8 13,9 12,9 z M6.0000001,8 L7.0000001,8 7.0000001,9 6.0000001,9 z M0,8 L1.0000002,8 1.0000002,9 0,9 z M12,6 L13,6 13,7 12,7 z M10,6 L11,6 11,7 10,7 z M8.0000001,6 L9.0000001,6 9.0000001,7 8.0000001,7 z M6.0000001,6 L7.0000001,6 7.0000001,7 6.0000001,7 z M4.0000001,6 L5.0000001,6 5.0000001,7 4.0000001,7 z M2.0000001,6 L3.0000001,6 3.0000001,7 2.0000001,7 z M0,6 L1.0000002,6 1.0000002,7 0,7 z M12,4 L13,4 13,5 12,5 z M6.0000001,4 L7.0000001,4 7.0000001,5 6.0000001,5 z M0,4 L1.0000002,4 1.0000002,5 0,5 z M12,2 L13,2 13,3 12,3 z M6.0000001,2 L7.0000001,2 7.0000001,3 6.0000001,3 z M0,2 L1.0000002,2 1.0000002,3 0,3 z M0,0 L13,0 13,1 0,1 z" Fill="{StaticResource IconColor}" Height="14" Stretch="Fill" Width="13" />
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>

                                                <sharedt:MenuItemSeparator />

                                                <tools:RibbonMenuItem x:Name="MoreBorders" Command="{Binding Path=Commands.FormatCells}"
                                                                           CommandParameter="3"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=MoreBorders}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="MoreBorderIcon">
                                                            <Path Data="M7,7 L7,12 12,12 12,7 z M0.99999994,7 L0.99999994,12 6,12 6,7 z M7,1 L7,6 12,6 12,1 z M0.99999994,1 L0.99999994,6 6,6 6,1 z M0,0 L13,0 13,13 7,13 6,13 0,13 z" Fill="{StaticResource IconColor}" Height="13" Margin="1,0,0,0" Stretch="Fill" Width="13"/>
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                            </tools:RibbonMenuGroup>
                                        </tools:SplitButton>
                                    </tools:ButtonPanel>

                                    <tools:ButtonPanel Height="24" SeparatorVisibility="Collapsed">
                                        <tools:SplitButton x:Name="Font_FillColor"
                                                                Height="24"
                                                                DataContext="{Binding ElementName=Font_FillColorPicker}"
                                                                SizeForm="ExtraSmall">
                                            <tools:SplitButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid Height="16" Width="16">
                                                        <Path Data="M7.9999695,0 C8.5529652,0 8.9999628,0.44699955 8.9999628,1 L8.9999628,6 10.999949,3.5 11.002948,6.414 6.7919779,10.625 2.5420079,6.375 6.9999766,1.9169998 6.9999766,1 C6.9999766,0.44699955 7.4469733,0 7.9999695,0 z" Fill="Transparent" Margin="3.042,0.5,4.497,4.875" Stretch="Fill"/>
                                                        <Path Data="M6.1649812,0 C6.9919994,0 7.6649669,0.6729753 7.6649669,1.5000038 L7.6649669,6.5000162 6.6649764,6.5000162 6.6649764,1.5000038 C6.6649764,1.2249787 6.4400027,1.0000025 6.1649812,1.0000025 5.8899591,1.0000025 5.6649859,1.2249787 5.6649859,1.5000038 L5.6649859,2.6240301 1.4140491,6.8750172 4.9569843,10.418056 8.6680091,6.7069869 8.6649573,4.0009866 9.6649478,3.9990335 9.6679995,7.1209893 4.9569843,11.832 0,6.8750172 4.664995,2.2100275 4.664995,1.5000038 C4.664995,0.6729753 5.3379624,0 6.1649812,0 z" Fill="{StaticResource IconColor}" Margin="2,0,3.997,4.168" Stretch="Fill"/>
                                                        <Path Data="M0,0 C1.1040039,0 2,0.89599609 2,2 L2,6 1,6 C1,6 1.1300049,2.6829834 0.68994144,2.1170044 0.53698733,1.9199829 2.9802322E-08,2 0,2 z" Fill="#FF135C9A" HorizontalAlignment="Right" Margin="0,4,2,6" Stretch="Fill" Width="2" />
                                                        <Path Data="M0,0 L16,0 16,4.0000001 0,4.0000001 z" Fill="{Binding Path=Color,ElementName=Font_FillColorPicker, Converter={StaticResource colorConverter}}" Height="4" Stretch="Fill" VerticalAlignment="Bottom"/>
                                                    </Grid>
                                                </DataTemplate>
                                            </tools:SplitButton.IconTemplate>
                                            <shared:ColorPickerPalette x:Name="Font_FillColorPicker"
                                                                       IsExpanded="True" 
                                                                       MoreColorOptionVisibility="Collapsed"
                                                                       Color="Yellow" />
                                            <tools:SplitButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FillColorToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FillColorToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:SplitButton.ToolTip>
                                        </tools:SplitButton>
                                        <tools:SplitButton x:Name="Font_FontColor" 
                                                                Height="24"
                                                                DataContext="{Binding ElementName=Font_FontColorPicker}"
                                                                SizeForm="ExtraSmall">
                                            <tools:SplitButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FontColorToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FontColorToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:SplitButton.ToolTip>
                                            <tools:SplitButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid Height="16" Width="16">
                                                        <Path Data="M0,0 L16,0 16,4 0,4 z" Fill="{Binding Path=Color,ElementName=Font_FontColorPicker, Converter={StaticResource colorConverter}}" Height="4" Stretch="Fill" VerticalAlignment="Bottom" />
                                                        <Path Data="M4.2089906,1.3530103 C4.1709962,1.5930002 4.1290035,1.7840095 4.081976,1.9230017 L2.8319743,5.5539936 5.6179891,5.5539936 4.3549867,1.9230017 C4.3159852,1.8050056 4.2759767,1.6150034 4.2339845,1.3530103 z M3.4090014,0 L5.1099925,0 8.494,9.1030001 6.8369847,9.1030001 6.0170069,6.7860021 2.4369848,6.7860021 1.6499963,9.1030001 0,9.1030001 z" Fill="{StaticResource IconColor}" Margin="3.344,0,3.352,5" Stretch="Fill" />
                                                    </Grid>
                                                </DataTemplate>
                                            </tools:SplitButton.IconTemplate>
                                            <shared:ColorPickerPalette x:Name="Font_FontColorPicker"
                                                                       IsExpanded="True"
                                                                       MoreColorOptionVisibility="Collapsed"
                                                                       Color="Red"/>
                                        </tools:SplitButton>
                                    </tools:ButtonPanel>

                                </tools:RibbonBar>

                                <!--  Alignment  -->
                                <tools:RibbonBar x:Name="Alignment_RibbonBar"
													  IconTemplate ="{StaticResource CenterAlignIcon}"
                                                      Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Alignment}"
                                                      IsLargeButtonPanel="False"
                                                      tools:RibbonBar.LauncherCommand="{Binding Path=Commands.FormatCells}"
                                                      tools:RibbonBar.LauncherCommandParameter="1"
                                                      IsLauncherButtonVisible="True">
                                    <tools:RibbonBar.ToolTip>
                                        <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=AlignmentSettingsToolTipHeader}">
                                            <StackPanel>
                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=AlignmentSettingsToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                            </StackPanel>
                                        </tools:ScreenTip>
                                    </tools:RibbonBar.ToolTip>
                                    <tools:ButtonPanel Height="75" SeparatorVisibility="Collapsed">
                                        <StackPanel Orientation="Vertical">
                                            <tools:ButtonPanel Height="32">
                                                <tools:RibbonButton x:Name="Alignment_TopAlign" Width="24"
                                                                 Height="24"
                                                                 Command="{Binding Path=Commands.FormatVerticalAlignment}"
                                                                 CommandParameter="VerticalAlignment.Top"
                                                                 Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TopAlign}"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource TopAlignIcon}">
                                                    <tools:RibbonButton.ToolTip>
                                                        <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TopAlignToolTipHeader}">
                                                            <StackPanel>
                                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TopAlignToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                            </StackPanel>
                                                        </tools:ScreenTip>
                                                    </tools:RibbonButton.ToolTip>
                                                </tools:RibbonButton>
                                                <tools:RibbonButton x:Name="Alignment_MiddleAlign" Width="24"
                                                                 Height="24"
                                                                 Command="{Binding Path=Commands.FormatVerticalAlignment}"
                                                                 CommandParameter="VerticalAlignment.Center"
                                                                 Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=MiddleAlign}"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource MiddleAlignIcon}">
                                                    <tools:RibbonButton.ToolTip>
                                                        <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=MiddleAlignToolTipHeader}">
                                                            <StackPanel>
                                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=MiddleAlignToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                            </StackPanel>
                                                        </tools:ScreenTip>
                                                    </tools:RibbonButton.ToolTip>
                                                </tools:RibbonButton>
                                                <tools:RibbonButton x:Name="Alignment_BottomAlign" Width="24"
                                                                 Height="24"
                                                                 Command="{Binding Path=Commands.FormatVerticalAlignment}"
                                                                 CommandParameter="VerticalAlignment.Bottom"
                                                                 Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=BottomAlign}"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource BottomAlignIcon}">
                                                    <tools:RibbonButton.ToolTip>
                                                        <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=BottomAlignToolTipHeader}">
                                                            <StackPanel>
                                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=BottomAlignToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                            </StackPanel>
                                                        </tools:ScreenTip>
                                                    </tools:RibbonButton.ToolTip>
                                                </tools:RibbonButton>
                                            </tools:ButtonPanel>
                                            <tools:ButtonPanel Height="32" Margin="0,5,0,0">
                                                <tools:RibbonButton x:Name="Alignment_AlignLeft" Width="24"
                                                                 Height="24"
                                                                 Command="{Binding Path=Commands.FormatHorizontalAlignment}"
                                                                 CommandParameter="HorizontalAlignment.Left"
                                                                 IsToggle="True"
                                                                 Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LeftAlign}"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource LeftAlignIcon}">
                                                    <tools:RibbonButton.ToolTip>
                                                        <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LeftAlignToolTipHeader}">
                                                            <StackPanel>
                                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LeftAlignToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                            </StackPanel>
                                                        </tools:ScreenTip>
                                                    </tools:RibbonButton.ToolTip>
                                                </tools:RibbonButton>
                                                <tools:RibbonButton x:Name="Alignment_Center"  Width="24"
                                                                 Height="24"
                                                                 Command="{Binding Path=Commands.FormatHorizontalAlignment}"
                                                                 CommandParameter="HorizontalAlignment.Center"
                                                                 IsToggle="True"
                                                              
                                                                 Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CenterAlign}"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource CenterAlignIcon}">
                                                    <tools:RibbonButton.ToolTip>
                                                        <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CenterAlignToolTipHeader}">
                                                            <StackPanel>
                                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CenterAlignToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                            </StackPanel>
                                                        </tools:ScreenTip>
                                                    </tools:RibbonButton.ToolTip>
                                                </tools:RibbonButton>
                                                <tools:RibbonButton x:Name="Alignment_AlignRight" Width="24"
                                                                 Height="24"
                                                                 Command="{Binding Path=Commands.FormatHorizontalAlignment}"
                                                                 CommandParameter="HorizontalAlignment.Right"
                                                                 IsToggle="True"
                                                                
                                                                 Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RightAlign}"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource RightAlignIcon}">
                                                    <tools:RibbonButton.ToolTip>
                                                        <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RightAlignToolTipHeader}">
                                                            <StackPanel>
                                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RightAlignToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                            </StackPanel>
                                                        </tools:ScreenTip>
                                                    </tools:RibbonButton.ToolTip>
                                                </tools:RibbonButton>
                                            </tools:ButtonPanel>
                                        </StackPanel>

                                        <tools:ButtonPanel Height="75">
                                            <StackPanel Orientation="Vertical">
                                                <tools:ButtonPanel Margin="5,0,0,0" SeparatorVisibility="Collapsed">
                                                    <tools:DropDownButton x:Name="Orientation"                                                               
                                                                SizeForm="ExtraSmall"
                                                                CollapseLabel="True" 
                                                                Width="32"
                                                                IconTemplate="{StaticResource CounterclockwiseIcon}">
                                                        <tools:DropDownButton.ToolTip>
                                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=OrientationToolTipHeader}">
                                                                <StackPanel>
                                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=OrientationToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                                </StackPanel>
                                                            </tools:ScreenTip>
                                                        </tools:DropDownButton.ToolTip>
                                                        <tools:RibbonButton x:Name="AngleCounterclockwise" SizeForm="Small" 
                                                                     IsToggle="True" Padding="4,0,0,0"
                                                                     Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=AngleCounterclockwise}"
                                                                     IconTemplate="{StaticResource CounterclockwiseIcon}"
                                                                     Command="{Binding Commands.Orientation}" CommandParameter="Counterclockwise"/>
                                                        <tools:RibbonButton x:Name="AngleClockwise" SizeForm="Small" 
                                                                     IsToggle="True" Padding="4,0,0,0"
                                                                     Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=AngleClockwise}"
                                                                     IconTemplate="{StaticResource ClockwiseIcon}"
                                                                     Command="{Binding Commands.Orientation}" CommandParameter="Clockwise"/>
                                                        <tools:RibbonButton x:Name="RotateTextUp" SizeForm="Small" 
                                                                     IsToggle="True" Padding="4,0,0,0"
                                                                     Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RotateTextUp}"
                                                                     IconTemplate="{StaticResource RotateTextUpIcon}"
                                                                     Command="{Binding Commands.Orientation}" CommandParameter="RotateTextUp"/>
                                                        <tools:RibbonButton x:Name="RotateTextDown" SizeForm="Small" 
                                                                     IsToggle="True" Padding="4,0,0,0"
                                                                     Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RotateTextDown}"
                                                                     IconTemplate="{StaticResource RotateTextDownIcon}"
                                                                     Command="{Binding Commands.Orientation}" CommandParameter="RotateTextDown"/>
                                                        <sharedt:MenuItemSeparator />
                                                        <tools:RibbonMenuItem Command="{Binding Path=Commands.FormatCells}" CommandParameter="1" Margin="0,-7,0,0"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCellAlignment}" IconBarEnabled="True">
                                                            <tools:RibbonMenuItem.Icon>
                                                                <Grid x:Name="FormatCellAlignmentIcon">
                                                                    <Path x:Name="Orientation_FormatCellAlign" Data="M11.578993,12.416993 L12.578993,12.416993 12.578993,13.416993 11.578993,13.416993 z M9.5789933,12.416993 L10.578993,12.416993 10.578993,13.416993 9.5789933,13.416993 z M13.578993,11.416993 L14.578993,11.416993 14.578993,12.416993 15.578993,12.416993 15.578993,13.416993 14.578993,13.416993 14.578993,14.416993 13.578993,14.416993 z M4.0139985,6.9619401 L3.1429987,8.1140206 C2.8749989,8.4700454 2.717999,8.7810672 2.6699989,9.0500863 2.622999,9.3191045 2.740999,9.596124 3.0219989,9.8781436 3.2279989,10.083158 3.4689987,10.178165 3.7439989,10.161164 4.0219986,10.146162 4.2809985,10.017154 4.5239983,9.7741368 4.8579981,9.4411137 5.0149982,9.0480855 4.9969981,8.5970548 4.9809983,8.1460231 4.7919981,7.7399943 4.4339983,7.3819692 z M2.5311239,4.8756693 C3.0392488,4.8907955 3.5549986,5.1598142 4.0779984,5.6828506 L6.8149977,8.4190419 6.1429977,9.0910886 5.4859979,8.4340432 5.468998,8.450044 C5.6809978,9.2460997 5.5019979,9.929147 4.9349983,10.496187 4.5179985,10.913216 4.0799985,11.130231 3.6209986,11.144232 3.1639988,11.158233 2.750999,10.982221 2.384999,10.614195 1.5979994,9.8281405 1.6019994,8.9100764 2.3949993,7.8580029 L3.475999,6.4239023 C2.761999,5.7098525 2.1159992,5.640848 1.5389994,6.2168882 1.0349996,6.7219231 0.75099969,7.3499673 0.68999946,8.1020196 L0,7.4109714 C0.14899993,6.7349241 0.49199975,6.1298821 1.0299997,5.5918444 1.5224994,5.0993102 2.0229992,4.8605435 2.5311239,4.8756693 z M12.578993,3.4169937 L15.578993,3.4169937 15.578993,6.4169934 14.578993,6.4169934 14.578993,5.1249955 6.9329805,12.771009 6.2259784,12.064006 13.872991,4.4169934 12.578993,4.4169934 z M7.4068985,0.95916477 C6.9974418,0.96162978 6.631146,1.1230636 6.3100157,1.4468195 5.9199986,1.8368454 5.7420192,2.2858639 5.7770231,2.7968657 5.8119965,3.3058837 6.0350199,3.766896 6.4450259,4.1769121 L7.0330114,4.7649424 C7.3820128,5.113952 7.7890282,5.295964 8.2560105,5.3139394 8.7230239,5.329962 9.1310158,5.1649483 9.4780331,4.8189295 9.8840413,4.410928 10.048044,3.9379218 9.965035,3.393899 9.8840413,2.8508835 9.5660462,2.3018556 9.0090361,1.7448322 8.5410161,1.2778381 8.0660377,1.0188277 7.5850167,0.96783117 7.5247669,0.96170471 7.4653926,0.95881257 7.4068985,0.95916477 z M7.4867249,3.989663E-05 C7.5696702,-0.0004628788 7.6537619,0.0037863447 7.7390094,0.012785591 8.420013,0.084778619 9.0780368,0.43680945 9.7100291,1.0698244 10.414043,1.7748318 10.806043,2.5088628 10.888045,3.2739003 10.970046,4.0359166 10.71504,4.7159298 10.123025,5.3079579 9.5680299,5.8629673 8.9140344,6.0459869 8.1580181,5.8589695 L8.1420269,5.8739541 8.751039,6.482981 8.0790381,7.1549981 1.8499986,0.92580733 2.5219997,0.25379026 5.2830017,3.0148898 5.2990234,2.9988675 C5.0700183,2.1108404 5.273999,1.3468404 5.9100194,0.70979771 6.3816481,0.2399194 6.906106,0.0035584112 7.4867249,3.989663E-05 z" Fill="{StaticResource IconColor}" HorizontalAlignment="Left" Height="14.417" Stretch="Fill" VerticalAlignment="Top" Width="15.579"/>
                                                                </Grid>
                                                            </tools:RibbonMenuItem.Icon>
                                                        </tools:RibbonMenuItem>
                                                    </tools:DropDownButton>
                                                </tools:ButtonPanel>
                                                <StackPanel Orientation="Horizontal" Margin="3,7,0,0">
                                                    <tools:RibbonButton Command="{Binding Path=Commands.IncreaseIndent}"
                                                                 IsToggle="False"
                                                                 Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=IncreaseIndent}"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource IncreaseIndentIcon}">
                                                        <tools:RibbonButton.ToolTip>
                                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=IncreaseIndentToolTipHeader}">
                                                                <StackPanel>
                                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=IncreaseIndentToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                                </StackPanel>
                                                            </tools:ScreenTip>
                                                        </tools:RibbonButton.ToolTip>
                                                    </tools:RibbonButton>
                                                    <tools:RibbonButton Command="{Binding Path=Commands.DecreaseIndent}"
                                                                 IsToggle="False"
                                                                 Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DecreaseIndent}"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource DecreaseIndentIcon}">
                                                        <tools:RibbonButton.ToolTip>
                                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DecreaseIndentToolTipHeader}">
                                                                <StackPanel>
                                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DecreaseIndentToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                                </StackPanel>
                                                            </tools:ScreenTip>
                                                        </tools:RibbonButton.ToolTip>
                                                    </tools:RibbonButton>
                                                </StackPanel>
                                            </StackPanel>
                                        </tools:ButtonPanel>
                                        <StackPanel Orientation="Vertical" Height="75">
                                            <tools:ButtonPanel SeparatorVisibility="Collapsed" Margin="4,0,0,0">
                                                <tools:RibbonButton x:Name="Alignment_WrapText" Command="{Binding Path=Commands.WrapText}" 
                                                                 IsToggle="True"
                                                                 Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=WrapText}"
                                                                 SizeForm="Small"
                                                                 IconTemplate="{StaticResource WrapTextIcon}">
                                                    <tools:RibbonButton.ToolTip>
                                                        <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=WrapText}">
                                                            <StackPanel>
                                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=WrapTextToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                            </StackPanel>
                                                        </tools:ScreenTip>
                                                    </tools:RibbonButton.ToolTip>
                                                </tools:RibbonButton>
                                            </tools:ButtonPanel>
                                            <tools:SplitButton x:Name="MergeAndCenterButton"                                                               
                                                                Command="{Binding Path=Commands.MergeCells}"
                                                                CommandParameter="MergeAndCenter"
                                                                Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=MergeAndCenter}"
                                                                SizeForm="Small"
                                                                    Margin="3,9,0,0"
                                                                IconTemplate="{StaticResource MergeAndCenterIcon}">
                                                <tools:SplitButton.ToolTip>
                                                    <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=MergeAndCenter}">
                                                        <StackPanel>
                                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=MergeAndCenterToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                        </StackPanel>
                                                    </tools:ScreenTip>
                                                </tools:SplitButton.ToolTip>
                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.MergeCells}"
                                                                       CommandParameter="MergeAndCenter"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=MergeAndCenter}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="MergeandCenter" Height="15" Width="15">
                                                            <Path Data="M0,0 L14,0 14,14 0,14 z" Fill="Transparent" Margin="0.5" Stretch="Fill" />
                                                            <Path Data="M8,12 L8,14 14,14 14,12 z M0.99999994,12 L0.99999994,14 7,14 7,12 z M0.99999994,4 L0.99999994,11 14,11 14,4 8,4 7,4 z M8,0.99999994 L8,3 14,3 14,0.99999994 z M0.99999994,0.99999994 L0.99999994,3 7,3 7,0.99999994 z M0,0 L15,0 15,15 8,15 7,15 0,15 z" Fill="{StaticResource IconColor}" Stretch="Fill" />
                                                            <Path Data="M1,0 L2,0 2,1 8.9999999,1 8.9999999,0 10,0 10,1 11,1 11,2.0000001 10,2.0000001 10,3.0000001 8.9999999,3.0000001 8.9999999,2.0000001 2,2.0000001 2,3.0000001 1,3.0000001 1,2.0000001 0,2.0000001 0,1 1,1 z" Fill="#FF4A7DB1" Margin="2,6" Stretch="Fill"/>
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>

                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.MergeCells}"
                                                                       CommandParameter="MergeAcross"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=MergeAcross}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="MergeAcrossIcon">
                                                            <Grid x:Name="MergeandAcross" Height="15" Width="15">
                                                                <Path Data="M0,0 L14,0 14,14 0,14 z" Fill="Transparent" Margin="0.5" Stretch="Fill" />
                                                                <Path Data="M8,12 L8,14 14,14 14,12 z M1,12 L1,14 7,14 7,12 z M1,4 L1,11 14,11 14,4 8,4 7,4 z M8,1 L8,3 14,3 14,1 z M1,1 L1,3 7,3 7,1 z M0,0 L15,0 15,15 8,15 7,15 0,15 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                                <Path Data="M7,0 L9,0 9,1 10,1 10,2.0000001 11,2.0000001 11,3.0000001 10,3.0000001 10,4.0000001 9,4.0000001 9,5.0000001 7,5.0000001 7,4.0000001 8,4.0000001 8,3.0000001 0,3.0000001 0,2.0000001 8,2.0000001 8,1 7,1 z" Fill="#FF4A7DB1" Margin="2,5" Stretch="Fill" />
                                                            </Grid>
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>

                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.MergeCells}"
                                                                       CommandParameter="MergeCells"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=MergeCells}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="MergeCellsIcon" HorizontalAlignment="Left" Height="15" VerticalAlignment="Top" Width="13">
                                                            <Path Data="M0,0 L12,0 12,14 0,14 z" Fill="Transparent" Margin="0.5" Stretch="Fill" />
                                                            <Path Data="M1.0000002,0.99999994 L1.0000002,14 12,14 12,0.99999994 z M0,0 L13,0 13,15 0,15 z" Fill="{StaticResource IconColor}" Stretch="Fill" />
                                                            <Path Data="M0.99999994,1.0000002 L0.99999994,4 12,4 12,1.0000002 z M0,0 L13,0 13,5 0,5 z" Fill="#FF4A7DB1" Margin="0,5" Stretch="Fill" />
                                                            <Path Data="M0,9 L0.99999997,9 0.99999997,13 0,13 z M0,0 L0.99999997,0 0.99999997,4 0,4 z" Fill="{StaticResource IconColor}" Margin="6,1" Stretch="Fill" />
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>

                                                <tools:RibbonMenuItem Command="{Binding Path=Commands.MergeCells}"
                                                                       CommandParameter="UnMerge"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnMergeCells}" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="UnmergeCellIcon" Height="15" Width="13">
                                                            <Path Data="M0,0 L12,0 12,14 0,14 z" Fill="Transparent" Margin="0.5" Stretch="Fill" />
                                                            <Path Data="M1.0000002,0.99999994 L1.0000002,14 12,14 12,0.99999994 z M0,0 L13,0 13,15 0,15 z" Fill="{StaticResource IconColor}" Stretch="Fill" />
                                                            <Path Data="M0,9 L0.99999997,9 0.99999997,13 0,13 z M0,0 L0.99999997,0 0.99999997,4 0,4 z" Fill="{StaticResource IconColor}" Margin="6,1" Stretch="Fill" />
                                                            <Path Data="M10,1.0000001 L10,4 12,4 12,1.0000001 z M7,1.0000001 L7,4 9,4 9,1.0000001 z M3.9999999,1.0000001 L3.9999999,4 6,4 6,1.0000001 z M1,1.0000001 L1,4 3,4 3,1.0000001 z M0,0 L13,0 13,5 10,5 9,5 7,5 6,5 3.9999999,5 3,5 0,5 z" Fill="#FF4A7DB1" Margin="0,5" Stretch="Fill"/>
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                            </tools:SplitButton>
                                        </StackPanel>
                                    </tools:ButtonPanel>
                                </tools:RibbonBar>

                                <!--  Number  -->
                                <tools:RibbonBar x:Name="Number_RibbonBar"
                                                      IconTemplate="{StaticResource PercentStyleIcon}"
                                                      Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Number}"
                                                      IsLargeButtonPanel="False" 
                                                      tools:RibbonBar.LauncherCommand="{Binding Path=Commands.FormatCells}"
                                                      tools:RibbonBar.LauncherCommandParameter="0"
                                                      IsLauncherButtonVisible="True">
                                    <tools:RibbonBar.ToolTip>
                                        <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NumberFormatToolTipHeader}">
                                            <StackPanel>
                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NumberSettingsToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                            </StackPanel>
                                        </tools:ScreenTip>
                                    </tools:RibbonBar.ToolTip>

                                    <tools:ButtonPanel Height="24"
                                                            SeparatorVisibility="Collapsed">
                                        <tools:RibbonComboBox x:Name="Number_NumberFormat" MaxDropDownHeight="300"
                                                                   Width="130" Margin="2,0"
                                                                   ItemsSource="{Binding Path=NumberFormatSource,
                                                                                         Mode=TwoWay,
                                                                                         RelativeSource={RelativeSource TemplatedParent}}"
                                                                   SelectedIndex="0"
                                                                   DisplayMemberPath="Name"
                                                                   SelectedValuePath="Format">
                                            <tools:RibbonComboBox.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NumberFormatToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NumberFormatToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonComboBox.ToolTip>

                                        </tools:RibbonComboBox>
                                    </tools:ButtonPanel>
                                    <tools:ButtonPanel Height="24"
                                                            SeparatorVisibility="Visible">
                                        <tools:RibbonButton Width="25"
                                                                 Height="24"
                                                                 IconStretch="Uniform"
                                                                 Command="{Binding Commands.FormatNumber}"
                                                                 CommandParameter="{StaticResource Accounting}"
                                                                 IsToggle="False"
                                                                 Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Accounting}"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource AccountingNumberFormatIcon}">
                                            <tools:RibbonButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=AccountingNumberFormatToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=AccountingNumberFormatToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonButton.ToolTip>
                                        </tools:RibbonButton>
                                        <tools:RibbonButton Width="25"
                                                                 Height="24"
                                                                 IconStretch="Uniform"
                                                                 Command="{Binding Commands.FormatNumber}"
                                                                 CommandParameter="{StaticResource Percentage}"
                                                                 IsToggle="False"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource PercentStyleIcon}">
                                            <tools:RibbonButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=PercentageFormatToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=PercentageStyleToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonButton.ToolTip>
                                        </tools:RibbonButton>
                                        <tools:RibbonButton Width="25"
                                                                 Height="24"
                                                                 IconStretch="Uniform"
                                                                 Command="{Binding Commands.FormatNumber}"
                                                                 CommandParameter="{StaticResource Comma}"
                                                                 IsToggle="False"
                                                                 Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CommaStyle}"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource CommaStyleIcon}">
                                            <tools:RibbonButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CommaStyleToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CommaStyleToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonButton.ToolTip>
                                        </tools:RibbonButton>
                                    </tools:ButtonPanel>

                                    <tools:ButtonPanel Height="24" HorizontalAlignment="Left"
                                                            SeparatorVisibility="Collapsed">
                                        <tools:RibbonButton Width="25"
                                                                 Height="24"
                                                                 IconStretch="Uniform"
                                                                 Command="{Binding Commands.IncreaseDecreaseDecimalCommand}"
                                                                 CommandParameter="{StaticResource True}"
                                                                 IsToggle="False"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource IncreaseDecmialIcon}">
                                            <tools:RibbonButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=IncreaseDecimalToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=IncreaseDecimalToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonButton.ToolTip>
                                        </tools:RibbonButton>
                                        <tools:RibbonButton Width="25"
                                                                 Height="24"
                                                                 IconStretch="Uniform"
                                                                 Command="{Binding Commands.IncreaseDecreaseDecimalCommand}"
                                                                 CommandParameter="{StaticResource False}"
                                                                 IsToggle="False"
                                                                 SizeForm="ExtraSmall"
                                                                 IconTemplate="{StaticResource DecreaseDecmialIcon}">
                                            <tools:RibbonButton.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DecreaseDecimalToolTipHeader}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DecreaseDecimalToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonButton.ToolTip>
                                        </tools:RibbonButton>
                                    </tools:ButtonPanel>

                                </tools:RibbonBar>

                                <!--  Style  -->
                                <tools:RibbonBar x:Name="Styles_RibbonBar"
														IconTemplate="{StaticResource CollapseStylesIcon}"
                                    Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Styles}"
                                                      IsLauncherButtonVisible="False"
                                                      IsRibbonGalleryPresent="True">

                                    <tools:DropDownButton Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ConditionalFormatting}"
                                                               IconTemplate="{StaticResource ConditionalFormatIcon}"
                                                               Width="Auto"
                                                               SizeForm="Large" 
                                                               >
                                        <tools:DropDownButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ConditionalFormatting}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ConditionalFormattingToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:DropDownButton.ToolTip>
                                        <tools:RibbonMenuItem Height="38" Width="200" HorizontalAlignment="Right" HorizontalContentAlignment="Right"
                                                                   Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HighlightCellRules}"
                                                                   FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" StaysOpenOnClick="True"
                                                                   IconSize="32,32" IconBarEnabled="True">
                                            <tools:RibbonMenuItem.Icon>
                                                <Grid x:Name="HighlightCellRulesIcon" Height="25" Width="28">
                                                    <Path Data="M15.500003,13.500001 L26.500003,13.500001 26.500003,23.500001 15.500003,23.500001 z M0,0 L24,0 24,12 14,12 14,19 0,19 z" Fill="Transparent" Margin="1,1,0.5,0.5" Stretch="Fill"/>
                                                    <Path Data="M17.000002,15.000001 L17.000002,24.000001 27.000002,24.000001 27.000002,15.000001 z M16.000002,14.000001 L28.000002,14.000001 28.000002,25.000001 16.000002,25.000001 z M0,0 L25.999999,0 25.999999,13 24.999999,13 24.999999,1 0.99999905,1 0.99999905,20 15,20 15,21 0,21 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                    <Path Data="M16.000011,10.002001 L17.000011,10.002001 17.000011,12.001 16.000011,12.001 z M7.0000114,10.001 L8.0000114,10.001 8.0000114,14.000999 14,14.000999 14,15.000999 8.0000114,15.000999 8.0000114,19.001 7.0000114,19.001 7.0000114,15.000999 0,15.000999 0,14.000999 7.0000114,14.000999 z M17.000011,8.9999995 L24.000011,8.9999995 24.000011,10.001 17.000011,10.001 z M1.1086469E-05,8.9999995 L7.0000114,8.9999995 7.0000114,10.001 1.1086469E-05,10.001 z M17.000011,4.0010004 L24.000011,4.0010004 24.000011,5.0010004 17.000011,5.0010004 z M1.1086469E-05,4.0010004 L7.0000114,4.0010004 7.0000114,5.0010004 1.1086469E-05,5.0010004 z M16.000011,0.0019989014 L17.000011,0.0019989014 17.000011,4.0010004 16.000011,4.0010004 z M7.0000114,0 L8.0000114,0 8.0000114,4 7.0000114,4 z" Fill="{StaticResource IconColor}" Margin="1,0.999,3,5" Stretch="Fill"/>
                                                    <Path Data="M0,0 L8,0 8,4 0,4 z" Fill="#FFE8C981" Height="4" Margin="9,6,11,0" Stretch="Fill" VerticalAlignment="Top" />
                                                    <Path Data="F1M376.8262,41.4248L374.8262,42.4248L376.8262,43.4248" Fill="Transparent" Height="2" Stretch="Fill" Width="2" HorizontalAlignment="Right" Margin="0,0,7.5,6.5" VerticalAlignment="Bottom"/>
                                                    <Path Data="F1M379.5498,46.4248L381.5498,45.4248L379.5498,44.4248" Fill="Transparent" Height="2" Stretch="Fill" Width="2" HorizontalAlignment="Right" Margin="0,0,2.776,3.5" VerticalAlignment="Bottom"/>
                                                    <Path Data="M1.1277992E-05,5.9480011 L3.0000112,5.9480011 3.0000112,6.9480011 1.1277992E-05,6.9480011 z M0,3.9480004 L3,3.9480004 3,4.9480004 0,4.9480004 z M5.447011,3.0000001 L7.4469943,3.9999999 C7.6159931,4.0849999 7.722992,4.2579999 7.722992,4.4479998 7.722992,4.6369998 7.6159931,4.8099998 7.4469943,4.8949998 L5.447011,5.8949996 5.0000143,4.9999998 6.1050053,4.4479998 5.0000143,3.8949999 z M2.2769993,0 L2.7240005,0.89499998 1.6189973,1.4479997 2.7240005,1.9999998 2.2769993,2.8949996 0.27699322,1.8949997 C0.10799278,1.8099997 0.00099246732,1.6369998 0.00099246325,1.4479997 0.00099246732,1.2579999 0.10799278,1.0849998 0.27699322,1 z" Fill="{StaticResource IconColor}" HorizontalAlignment="Right" Height="6.948" Margin="0,0,2.277,2" Stretch="Fill" VerticalAlignment="Bottom" Width="7.723"/>
                                                </Grid>
                                            </tools:RibbonMenuItem.Icon>
                                            <tools:RibbonMenuItem.Items>
                                                <tools:RibbonMenuItem Height="38"
                                                                           Command="{Binding Path=Commands.ConditonalFormattingHighlightCellsRules}"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=GreaterThan}" IconBarEnabled="True"
                                                                           IconSize="32,32">
                                                    <tools:RibbonMenuItem.CommandParameter>
                                                        <xlsIo:ExcelComparisonOperator>Greater</xlsIo:ExcelComparisonOperator>
                                                    </tools:RibbonMenuItem.CommandParameter>
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="GreaterThanIcon" Height="24" Width="27">
                                                            <Path Data="M14.500003,12.499997 L25.500003,12.499997 25.500003,22.499997 14.500003,22.499997 z M0,0 L24,0 24,11 13,11 13,19 0,19 z" Fill="Transparent" Margin="1,1,0.5,0.5" Stretch="Fill"/>
                                                            <Path Data="M19.777008,16.084003 L23.401007,18.500004 19.777008,20.916002 19.222008,20.084003 21.598007,18.500004 19.222008,16.916002 z M15.999996,13.999997 L15.999996,22.999996 25.999996,22.999996 25.999996,13.999997 z M14.999996,12.999997 L26.999996,12.999997 26.999996,23.999996 14.999996,23.999996 z M0,0 L26,0 26,12 25,12 25,1.0000001 1.0000001,1.0000001 1.0000001,20 14,20 14,21 0,21 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                            <Path Data="M8.0000002,5 L8.0000002,9 16,9 16,5 z M7.0000002,0 L8.0000002,0 8.0000002,4 16,4 16,0.0010004044 17,0.0010004044 17,4 24,4 24,5 17,5 17,9 24,9 24,10 17,10 16,10 8.0000002,10 8.0000002,14.000003 13.000004,14.000003 13.000004,15.000003 8.0000002,15.000003 8.0000002,18.998999 7.0000002,18.998999 7.0000002,15.000003 3.9339066E-06,15.000003&#xd;&#xa;3.9339066E-06,14.000003 7.0000002,14.000003 7.0000002,10 0,10 0,9 7.0000002,9 7.0000002,5 0,5 0,4 7.0000002,4 z" Fill="{StaticResource IconColor}" Margin="1,1,2,4.001" Stretch="Fill"/>
                                                            <Path Data="M0,0 L8,0 8,4 0,4 z" Fill="#FFE8C981" Height="4" Margin="9,6,10,0" Stretch="Fill" VerticalAlignment="Top" />
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                                <tools:RibbonMenuItem Height="38"
                                                                           Command="{Binding Path=Commands.ConditonalFormattingHighlightCellsRules}"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LessThan}" IconBarEnabled="True"
                                                                           IconSize="32,32">
                                                    <tools:RibbonMenuItem.CommandParameter>
                                                        <xlsIo:ExcelComparisonOperator>Less</xlsIo:ExcelComparisonOperator>
                                                    </tools:RibbonMenuItem.CommandParameter>
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="LessThanIcon" Height="24" Width="27">
                                                            <Path Data="M14.499996,12.499997 L25.499996,12.499997 25.499996,22.499996 14.499996,22.499996 z M0,0 L24,0 24,11 13,11 13,19 0,19 z" Fill="Transparent" Margin="1,1,0.5,0.5" Stretch="Fill"/>
                                                            <Path Data="M22.846994,16.084003 L23.401995,16.916002 21.025995,18.500004 23.401995,20.084003 22.846994,20.916002 19.222996,18.500004 z M15.999996,13.999997 L15.999996,22.999996 25.999996,22.999996 25.999996,13.999997 z M14.999996,12.999997 L26.999996,12.999997 26.999996,23.999996 14.999996,23.999996 z M0,0 L26,0 26,12 25,12 25,1.0000001 1.0000001,1.0000001 1.0000001,20 14,20 14,21 0,21 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                            <Path Data="M8.0000002,5 L8.0000002,9 16,9 16,5 z M7.0000002,0 L8.0000002,0 8.0000002,4 16,4 16,0.0010004044 17,0.0010004044 17,4 24,4 24,5 17,5 17,9 24,9 24,10 17,10 16,10 8.0000002,10 8.0000002,14.000003 13.000004,14.000003 13.000004,15.000003 8.0000002,15.000003 8.0000002,18.998999 7.0000002,18.998999 7.0000002,15.000003 3.9339066E-06,15.000003&#xd;&#xa;3.9339066E-06,14.000003 7.0000002,14.000003 7.0000002,10 0,10 0,9 7.0000002,9 7.0000002,5 0,5 0,4 7.0000002,4 z" Fill="{StaticResource IconColor}" Margin="1,1,2,4.001" Stretch="Fill"/>
                                                            <Path Data="M0,0 L8,0 8,4 0,4 z" Fill="#FFE8C981" Height="4" Margin="9,6,10,0" Stretch="Fill" VerticalAlignment="Top" />
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                                <tools:RibbonMenuItem Height="38"
                                                                           Command="{Binding Path=Commands.ConditonalFormattingHighlightCellsRules}"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Between}" IconBarEnabled="True"
                                                                           IconSize="32,32">
                                                    <tools:RibbonMenuItem.CommandParameter>
                                                        <xlsIo:ExcelComparisonOperator>Between</xlsIo:ExcelComparisonOperator>
                                                    </tools:RibbonMenuItem.CommandParameter>
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="BetweenIcon" Height="21" Width="27">
                                                            <Path Data="M0,0 L26,0 26,20 0,20 z" Fill="Transparent" Margin="0.5" Stretch="Fill" />
                                                            <Path Data="M0.99999982,1.0000002 L0.99999982,20 26,20 26,1.0000002 z M0,0 L27,0 27,21 0,21 z" Fill="{StaticResource IconColor}" Stretch="Fill" />
                                                            <Path Data="M8.0010042,9.9999995 L8.0010042,14.001 17.001003,14.001 17.001003,9.9999995 z M8.0010042,5.0010004 L8.0010042,8.9999995 17.001003,8.9999995 17.001003,5.0010004 z M7.0010042,0 L8.0010042,0 8.0010042,4.0010004 17.001003,4.0010004 17.001003,0.0010004044 18.001003,0.0010004044 18.001003,4.0010004 25.000011,4.0010004 25.000011,5.0010004 18.001003,5.0010004 18.001003,8.9999995 25.000011,8.9999995 25.000011,9.9999995 18.001003,9.9999995 18.001003,14.001 25,14.001 25,15.000999 18.001003,15.000999 18.001003,19 17.001003,19 17.001003,15.000999 8.0010042,15.000999 8.0010042,18.998999 7.0010042,18.998999&#xd;&#xa;7.0010042,15.000999 0,15.000999 0,14.001 7.0010042,14.001 7.0010042,9.9999995 1.1175876E-05,9.9999995 1.1175876E-05,8.9999995 7.0010042,8.9999995 7.0010042,5.0010004 1.1175876E-05,5.0010004 1.1175876E-05,4.0010004 7.0010042,4.0010004 z" Fill="{StaticResource IconColor}" Margin="1,0.999,1,1.001" Stretch="Fill" />
                                                            <Path Data="M0,0 L9,0 9,4 0,4 z" Fill="#FFE8C981" Height="4" Margin="9,0,9,6" Stretch="Fill" VerticalAlignment="Bottom" />
                                                            <Path Data="M0,7.4505772E-07 L7,7.4505772E-07 7,4.000001 0,4.000001 z M17.999988,0 L24.999989,0 24.999989,4.0000001 17.999988,4.0000001 z" Fill="#FF577EC2" Height="4" Margin="1,6,1,0" Stretch="Fill" VerticalAlignment="Top"/>
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                                <tools:RibbonMenuItem Height="38"
                                                                           Command="{Binding Path=Commands.ConditonalFormattingHighlightCellsRules}"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=EqualTo}" IconBarEnabled="True"
                                                                           IconSize="32,32">
                                                    <tools:RibbonMenuItem.CommandParameter>
                                                        <xlsIo:ExcelComparisonOperator>Equal</xlsIo:ExcelComparisonOperator>
                                                    </tools:RibbonMenuItem.CommandParameter>
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="EqualToIcon" Height="24" Width="27">
                                                            <Path Data="M14.500003,12.499997 L25.500003,12.499997 25.500003,22.499997 14.500003,22.499997 z M0,0 L24,0 24,11 13,11 13,19 0,19 z" Fill="Transparent" Margin="1,1,0.5,0.5" Stretch="Fill"/>
                                                            <Path Data="M19.000007,18.999994 L23.000007,18.999994 23.000007,19.999994 19.000007,19.999994 z M19.000007,16.999994 L23.000007,16.999994 23.000007,17.999994 19.000007,17.999994 z M16.000003,13.999997 L16.000003,22.999997 26.000003,22.999997 26.000003,13.999997 z M15.000003,12.999997 L27.000003,12.999997 27.000003,23.999997 15.000003,23.999997 z M0,0 L26,0 26,12 25,12 25,1 1,1 1,20 14,20 14,21 0,21 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                            <Path Data="M7.9999963,4.9999971 L7.9999963,8.9999971 16,8.9999971 16,4.9999971 z M6.9999962,0 L7.9999963,0 7.9999963,3.9999969 16,3.9999969 16,0.00099742413 17,0.00099742413 17,3.9999969 24,3.9999969 24,4.9999971 17,4.9999971 17,8.9999971 24,8.9999971 24,9.9999971 17,9.9999971 16,9.9999971 7.9999963,9.9999971 7.9999963,13.999997 13,13.999997 13,14.999997 7.9999963,14.999997 7.9999963,18.999001 6.9999962,18.999001 6.9999962,14.999997 0,14.999997&#xd;&#xa;0,13.999997 6.9999962,13.999997 6.9999962,9.9999971 0,9.9999971 0,8.9999971 6.9999962,8.9999971 6.9999962,4.9999971 0,4.9999971 0,3.9999969 6.9999962,3.9999969 z" Fill="{StaticResource IconColor}" Margin="1,1,2,4.001" Stretch="Fill"/>
                                                            <Path Data="M0,0 L8,0 8,4 0,4 z" Fill="#FFE8C981" Height="4" Margin="9,6,10,0" Stretch="Fill" VerticalAlignment="Top" />
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                                <tools:RibbonMenuItem Height="38"
                                                                           Command="{Binding Path=Commands.ConditonalFormattingHighlightCellsRules}"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TextContains}" IconBarEnabled="True"
                                                                           IconSize="32,32">
                                                    <tools:RibbonMenuItem.CommandParameter>
                                                        <xlsIo:ExcelComparisonOperator>ContainsText</xlsIo:ExcelComparisonOperator>
                                                    </tools:RibbonMenuItem.CommandParameter>
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="TextContainsIcon" Height="24" Width="27">
                                                            <Path Data="M14.500011,12.499997 L25.500011,12.499997 25.500011,22.499996 14.500011,22.499996 z M0,0 L24,0 24,11 13,11 13,19 0,19 z" Fill="Transparent" Margin="1,1,0.5,0.5" Stretch="Fill"/>
                                                            <Path Data="M16.000004,13.999999 L16.000004,22.999998 26,22.999998 26,13.999999 z M15.000005,12.999999 L27,12.999999 27,23.999998 15.000005,23.999998 z M0,0 L25.999989,0 25.999989,12.000002 24.99999,12.000002 24.99999,0.99999984 0.9999994,0.99999984 0.9999994,20.000002 13.999994,20.000002 13.999994,21.000002 0,21.000002 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                            <Path Data="M8.0000114,5 L8.0000114,9 16.000011,9 16.000011,5 z M7.0000114,0 L8.0000114,0 8.0000114,4 16.000011,4 16.000011,0.0010004044 17.000011,0.0010004044 17.000011,4 24.000011,4 24.000011,5 17.000011,5 17.000011,9 24.000011,9 24.000011,10 17.000011,10 16.000011,10 8.0000114,10 8.0000114,14.000003 13,14.000003 13,15.000003 8.0000114,15.000003 8.0000114,18.998999 7.0000114,18.998999 7.0000114,15.000003 0,15.000003&#xd;&#xa;0,14.000003 7.0000114,14.000003 7.0000114,10 1.1086469E-05,10 1.1086469E-05,9 7.0000114,9 7.0000114,5 1.1086469E-05,5 1.1086469E-05,4 7.0000114,4 z" Fill="{StaticResource IconColor}" Margin="1,1,2,4.001" Stretch="Fill"/>
                                                            <Path Data="M0,0 L8,0 8,4 0,4 z" Fill="#FFE8C981" Height="4" Margin="9,6,10,0" Stretch="Fill" VerticalAlignment="Top" />
                                                            <Path Data="M2.4920011,3.9569913 L1.6519753,4.0739957 C1.3909893,4.1079923 1.1959824,4.1709958 1.063994,4.2639903 0.93200562,4.3559932 0.86700324,4.5189949 0.8670033,4.7499901 0.86700324,4.9189962 0.9279773,5.0579962 1.0489793,5.16599 1.1699814,5.2739917 1.331999,5.3279926 1.5350017,5.3279926 1.8110023,5.3279926 2.0399754,5.2309924 2.220975,5.0369924 2.4009979,4.8429922 2.4920011,4.5989968 2.4920011,4.3049906 z M6.4449997,2.5510006 C6.1219979,2.5510005 5.8619967,2.6650006 5.6659961,2.8950005 5.4699951,3.1240007 5.3709946,3.4100007 5.3709946,3.7540008 L5.3709946,4.2500008 C5.3709946,4.5420009 5.463995,4.7890009 5.6499958,4.9920009 5.8369966,5.195001 6.0729976,5.297001 6.3589993,5.297001 6.6950007,5.297001 6.9590021,5.166001 7.150003,4.9040011 7.3420039,4.643001 7.4370041,4.2770008 7.4370041,3.8090007 7.4370041,3.4150007 7.3490038,3.1080006 7.170003,2.8850006 6.9910021,2.6620006 6.750001,2.5510005 6.4449997,2.5510006 z M1.8509802,1.8279954 C2.8619957,1.8279954 3.3669999,2.3249966 3.3669999,3.3199909 L3.3669999,5.9219936 2.4880033,5.9219936 2.4880033,5.2969943 2.4719816,5.2969943 C2.1959811,5.7759901 1.7919898,6.0159954 1.257994,6.0159954 0.863982,6.0159954 0.55599902,5.9089931 0.33398394,5.6949888 0.11099229,5.4809922 0,5.1989948 0,4.8479894 0,4.0929929 0.43398992,3.6519914 1.3049908,3.5269915 L2.4920011,3.3589923 C2.4920011,2.7889931 2.220975,2.5039973 1.6799905,2.5039973 1.2030013,2.5039973 0.7729787,2.6679987 0.3899836,2.9959937 L0.3899836,2.2029951 C0.81198011,1.9529953 1.299986,1.8279954 1.8509802,1.8279954 z M4.4729906,0 L5.3789945,0 5.3789945,2.6250005 5.3939948,2.6250005 C5.7039962,2.0940005 6.1569982,1.8280004 6.7540012,1.8280004 7.2590032,1.8280004 7.6540051,2.0070004 7.9390064,2.3650005 8.2250081,2.7240006 8.3670082,3.2030007 8.3670082,3.8050008 8.3670082,4.4740008 8.2070074,5.0100011 7.8870063,5.412001 7.5660048,5.8150011 7.1290026,6.0160011 6.5739999,6.0160011 6.0539976,6.0160011 5.6599961,5.7920011 5.3939948,5.3440012 L5.3789945,5.3440012 5.3789945,5.9220012 4.4729906,5.9220012 z" Fill="{StaticResource IconColor}" HorizontalAlignment="Right" Height="6.016" Margin="0,0,1.838,2.713" Stretch="Fill" VerticalAlignment="Bottom" Width="8.367"/>
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                                <tools:RibbonMenuItem Height="38"
                                                                           Command="{Binding Path=Commands.ConditonalFormattingHighlightCellsRules}"
                                                                           CommandParameter="DateOccuring"
                                                                           Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DateOccuring}" IconBarEnabled="True"
                                                                           IconSize="32,32">
                                                    <tools:RibbonMenuItem.Icon>
                                                        <Grid x:Name="DateOccuringIcon" Height="23" Width="26">
                                                            <Path Data="M14.500001,12.499997 L24.500001,12.499997 24.500001,21.499997 14.500001,21.499997 z M0,0 L24.000002,0 24.000002,11 13.000002,11 13.000002,19 0,19 z" Fill="Transparent" Margin="1,1,0.5,0.5" Stretch="Fill"/>
                                                            <Path Data="M7.0000001,15.000007 L7.9999999,15.000007 7.9999999,18.999007 7.0000001,18.999007 z M0,14 L6.9999998,14 6.9999998,15 0,15 z M17,9 L24,9 24,10 17,10 z M0,9 L6.9999998,9 6.9999998,10 0,10 z M6.9999998,0 L7.9999999,0 7.9999999,4 16,4 16,0.0010004044 17,0.0010004044 17,4 24,4 24,5 17,5 17,9 16,9 16,5 7.9999999,5 7.9999999,8.9989991 6.9999998,8.9989991 6.9999998,5 0,5 0,4 6.9999998,4 z" Fill="{StaticResource IconColor}" Margin="1,1,1,3.001" Stretch="Fill"/>
                                                            <Path Data="M6.0000002,3.9999998 L6.0000002,5.0000029 7.0000002,5.0000029 7.0000002,3.9999998 z M4,3.9999998 L4,5.0000029 5.0000002,5.0000029 5.0000002,3.9999998 z M2,3.9999998 L2,5.0000029 3,5.0000029 3,3.9999998 z M6.0000002,1.9999998 L6.0000002,2.9999998 7.0000002,2.9999998 7.0000002,1.9999998 z M4,1.9999998 L4,2.9999998 5.0000002,2.9999998 5.0000002,1.9999998 z M2,1.9999998 L2,2.9999998 3,2.9999998 3,1.9999998 z M1.0000001,0 L2,0 2,1 3,1 3,0 4,0 4,1 5.0000002,1 5.0000002,0 6.0000002,0 6.0000002,1 7.0000002,1 7.0000002,0 8.0000002,0 8.0000002,1 9.0000002,1 9.0000002,1.9999998 8.0000002,1.9999998 8.0000002,2.9999998 9.0000002,2.9999998 9.0000002,3.9999998 8.0000002,3.9999998 8.0000002,5.0000029 9.0000002,5.0000029&#xd;&#xa;9.0000002,6.0000027 8.0000002,6.0000027 8.0000002,6.9999998 7.0000002,6.9999998 7.0000002,6.0000027 6.0000002,6.0000027 6.0000002,6.9999998 5.0000002,6.9999998 5.0000002,6.0000027 4,6.0000027 4,6.9999998 3,6.9999998 3,6.0000027 2,6.0000027 2,6.9999998 1.0000001,6.9999998 1.0000001,6.0000027 2.6822091E-07,6.0000027 2.6822091E-07,5.0000029 1.0000001,5.0000029 1.0000001,3.9999998 0,3.9999998 0,2.9999998&#xd;&#xa;1.0000001,2.9999998 1.0000001,1.9999998 0,1.9999998 0,1 1.0000001,1 z" Fill="{StaticResource IconColor}" HorizontalAlignment="Right" Height="7" Margin="0,0,1,1" Stretch="Fill" VerticalAlignment="Bottom" Width="9" />
                                                            <Path Data="M1,1 L1,2 2,2 2,1 z M0,0 L3,0 3,3 0,3 z" Fill="#FFF69220" HorizontalAlignment="Right" Height="3" Margin="0,0,2,4" Stretch="Fill" VerticalAlignment="Bottom" Width="3" />
                                                            <Path Data="M0,0 L7.9999999,0 7.9999999,0.99999998 5.0000014,0.99999998 5.0000014,4 0,4 z" Fill="#FFE8C981" Margin="9,11,9,8" Stretch="Fill" />
                                                            <Path Data="M15.999001,13.999997 L15.999001,21.999996 25.000001,21.999996 25.000001,14.999997 16.000001,14.999997 16.000001,13.999997 z M15.000001,12.999997 L26.000001,12.999997 26.000001,22.999996 15.000001,22.999996 z M0,0 L25.999999,0 25.999999,12 24.999999,12 24.999999,0.99999996 1.0000012,0.99999996 1.0000012,20 14.000001,20 14.000001,21 0,21 z" Fill="{StaticResource IconColor}" Stretch="Fill" />
                                                        </Grid>
                                                    </tools:RibbonMenuItem.Icon>
                                                </tools:RibbonMenuItem>
                                            </tools:RibbonMenuItem.Items>
                                        </tools:RibbonMenuItem>
                                        <tools:RibbonMenuItem Height="38" 
                                                                       Width="200"
                                                                       HorizontalAlignment="Right" 
                                                                       HorizontalContentAlignment="Right"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DataBars}" IconBarEnabled="True"
                                                                       FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" 
                                                                       StaysOpenOnClick="True"
                                                                       IconSize="32,32">
                                            <tools:RibbonMenuItem.Icon>
                                                <Grid x:Name="DataBarsIcon" Height="26" Width="28">
                                                    <Path Data="M0,0 L27,0 27,25 0,25 z" Fill="Transparent" Margin="0.5" Stretch="Fill" />
                                                    <Path Data="M17.000011,22 L20.000011,22 20.000011,23 17.000011,23 z M17.000011,16.999001 L20.000011,16.999001 20.000011,17.999001 17.000011,17.999001 z M17.000011,12.000001 L20.000011,12.000001 20.000011,13.000001 17.000011,13.000001 z M17.000011,7.0000011 L20.000011,7.0000011 20.000011,8.0000011 17.000011,8.0000011 z M17.000011,2.0000008 L20.000011,2.0000008 20.000011,3.0000006 17.000011,3.0000006 z M0.99999994,0.99999994 L0.99999994,25 27,25 27,0.99999994 z M0,0 L28,0 28,26 0,26 z" Fill="{StaticResource IconColor}" Stretch="Fill" />
                                                    <Path Data="M5.9999884,15.000001 L5.9999884,19 20,19 20,15.000001 z M5.9999884,10.000001 L5.9999884,14.000001 20,14.000001 20,10.000001 z M5.9999884,5.0000011 L5.9999884,8.9990007 20,8.9990007 20,5.0000011 z M4.9999885,0 L5.9999884,0 5.9999884,4.0000011 20,4.0000011 20,7.7486038E-07 21,7.7486038E-07 21,4.0000011 26,4.0000011 26,5.0000011 21,5.0000011 21,8.9990007 26,8.9990007 26,10.000001 21,10.000001 21,14.000001 26,14.000001 26,15.000001 21,15.000001 21,19 26,19 26,20 21,20 21,24 20,24&#xd;&#xa;20,20 5.9999884,20 5.9999884,24 4.9999885,24 4.9999885,20 0,20 0,19 4.9999885,19 4.9999885,15.000001 0,15.000001 0,14.000001 4.9999885,14.000001 4.9999885,10.000001 0,10.000001 0,8.9990007 4.9999885,8.9990007 4.9999885,5.0000011 0,5.0000011 0,4.0000011 4.9999885,4.0000011 z" Fill="{StaticResource IconColor}" Margin="1" Stretch="Fill"/>
                                                    <Path Data="M1.1444091E-05,20 L6.0000115,20 6.0000115,24 1.1444091E-05,24 z M1.1444091E-05,15.000001 L8.0000115,15.000001 8.0000115,19 1.1444091E-05,19 z M1.1444091E-05,10.000001 L6.0000115,10.000001 6.0000115,14.000001 1.1444091E-05,14.000001 z M1.1444091E-05,5.000001 L3.000011,5.000001 3.000011,9.000001 1.1444091E-05,9.000001 z M0,0 L9.0000001,0 9.0000001,4 0,4 z" Fill="#FF4D82B8" Margin="7,1,12,1" Stretch="Fill" />
                                                </Grid>
                                            </tools:RibbonMenuItem.Icon>
                                            <tools:RibbonMenuItem.Items>
                                                <tools:RibbonMenuItem Style="{StaticResource SyncfusionDataBarsRibbonMenuItemStyle}"/>
                                            </tools:RibbonMenuItem.Items>
                                        </tools:RibbonMenuItem>
                                        <tools:RibbonMenuItem Height="38"
                                                                       Width="200"
                                                                       HorizontalAlignment="Right" 
                                                                       HorizontalContentAlignment="Right"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ColorScales}"
                                                                       FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" 
                                                                       StaysOpenOnClick="True"
                                                                       IconSize="32,32"
                                                                        IconBarEnabled="True">
                                            <tools:RibbonMenuItem.Icon>
                                                <Grid x:Name="ColorScalesIcon" Height="26" Width="28">
                                                    <Path Data="M0,0 L27,0 27,25 0,25 z" Fill="Transparent" Margin="0.5" Stretch="Fill" />
                                                    <Path Data="M0.99999994,0.99999994 L0.99999994,25 27,25 27,0.99999994 z M0,0 L28,0 28,26 0,26 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                    <Path Data="M5.999999,15.000001 L5.999999,18.999001 20.000996,18.999001 20.000996,15.000001 z M5.999999,9.9990006 L5.999999,14.000001 20.000996,14.000001 20.000996,9.9990006 z M5.999999,5.000001 L5.999999,8.9990006 20.000996,8.9990006 20.000996,5.000001 z M20.000996,0 L21.000996,0 21.000996,4.000001 25.999999,4.000001 25.999999,5.000001 21.000996,5.000001 21.000996,8.9990006 25.999999,8.9990006 25.999999,9.9990006 21.000996,9.9990006 21.000996,14.000001 25.999999,14.000001 25.999999,15.000001 21.000996,15.000001 21.000996,18.999001 25.999999,18.999001 25.999999,20 21.000996,20 21.000996,24 20.000996,24 20.000996,20 5.999999,20 5.999999,24 4.999999,24&#xd;&#xa;4.999999,20 0,20 0,18.999001 4.999999,18.999001 4.999999,15.000001 0,15.000001 0,14.000001 4.999999,14.000001 4.999999,9.9990006 0,9.9990006 0,8.9990006 4.999999,8.9990006 4.999999,5.000001 0,5.000001 0,4.000001 4.999999,4.000001 4.999999,7.7486037E-07 5.999999,7.7486037E-07 5.999999,4.000001 20.000996,4.000001 z" Fill="{StaticResource IconColor}" Margin="1" Stretch="Fill"/>
                                                    <Path Data="M0,0 L14,0 14,4.0000001 0,4.0000001 z" Fill="#FF4D82B8" Height="4" Margin="7,1,7,0" Stretch="Fill" VerticalAlignment="Top" />
                                                    <Path Data="M0,0 L14,0 14,4.0000001 0,4.0000001 z" Fill="#FF76A797" Height="4" Margin="7,6,7,0" Stretch="Fill" VerticalAlignment="Top" />
                                                    <Path Data="M0,0 L14,0 14,4.0000001 0,4.0000001 z" Fill="#FFEDC87E" Margin="7,11" Stretch="Fill" />
                                                    <Path Data="M0,0 L14,0 14,4.0000001 0,4.0000001 z" Fill="#FFE78E46" Height="4" Margin="7,0,7,6" Stretch="Fill" VerticalAlignment="Bottom" />
                                                    <Path Data="M0,0 L14,0 14,4.0000001 0,4.0000001 z" Fill="#FFD86344" Height="4" Margin="7,0,7,1" Stretch="Fill" VerticalAlignment="Bottom" />
                                                    <Path Data="M0,20 L5.9989995,20 5.9989995,21 0,21 z M5.9455633E-06,14.999 L5.9990052,14.999 5.9990052,15.999 5.9455633E-06,15.999 z M5.9455633E-06,10.000001 L5.9990052,10.000001 5.9990052,11.000001 5.9455633E-06,11.000001 z M5.9455633E-06,5.000001 L5.9990052,5.000001 5.9990052,6.000001 5.9455633E-06,6.000001 z M5.9455633E-06,0 L5.9990052,0 5.9990052,1 5.9455633E-06,1 z" Fill="{StaticResource IconColor}" HorizontalAlignment="Right" Margin="0,3,8.001,2" Stretch="Fill" Width="5.999" />
                                                </Grid>
                                            </tools:RibbonMenuItem.Icon>
                                            <tools:RibbonMenuItem.Items>
                                                <tools:RibbonMenuItem Style="{StaticResource SyncfusionColorScalesRibbonMenuItemStyle}"/>
                                            </tools:RibbonMenuItem.Items>
                                        </tools:RibbonMenuItem>
                                        <tools:RibbonMenuItem Height="38" 
                                                                       Width="200"
                                                                       HorizontalAlignment="Right" 
                                                                       HorizontalContentAlignment="Right"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=IconSets}"
                                                                       FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" 
                                                                       StaysOpenOnClick="True"
                                                                       IconBarEnabled="True"
                                                                       IconSize="32,32">
                                            <tools:RibbonMenuItem.Icon>
                                                <Grid x:Name="IconSetsIcon" Height="26" Width="28">
                                                    <Path Data="M0,0 L27,0 27,25 0,25 z" Fill="Transparent" Margin="0.5" Stretch="Fill" />
                                                    <Path Data="M0.99999994,0.99999994 L0.99999994,25 27,25 27,0.99999994 z M0,0 L28,0 28,26 0,26 z" Fill="{StaticResource IconColor}" Stretch="Fill" />
                                                    <Path Data="M6.0000114,15.000001 L6.0000114,19.000004 20.000011,19.000004 20.000011,15.000001 z M6.0000114,10.000001 L6.0000114,14.000001 20.000011,14.000001 20.000011,10.000001 z M6.0000114,5.000001 L6.0000114,9.000001 20.000011,9.000001 20.000011,5.000001 z M5.0000114,0 L6.0000114,0 6.0000114,4.000001 20.000011,4.000001 20.000011,0 21.000011,0 21.000011,4.000001 26.000011,4.000001 26.000011,5.000001 21.000011,5.000001 21.000011,9.000001 26.000011,9.000001 26.000011,10.000001 21.000011,10.000001 21.000011,14.000001 26.000011,14.000001 26.000011,15.000001 21.000011,15.000001 21.000011,19.000004 26,19.000004 26,20.000004 21.000011,20.000004 21.000011,24.000001 20.000011,24.000001&#xd;&#xa;20.000011,20.000004 6.0000114,20.000004 6.0000114,24.000001 5.0000114,24.000001 5.0000114,20.000004 0,20.000004 0,19.000004 5.0000114,19.000004 5.0000114,15.000001 1.123548E-05,15.000001 1.123548E-05,14.000001 5.0000114,14.000001 5.0000114,10.000001 1.123548E-05,10.000001 1.123548E-05,9.000001 5.0000114,9.000001 5.0000114,5.000001 1.123548E-05,5.000001 1.123548E-05,4.000001 5.0000114,4.000001 z" Fill="{StaticResource IconColor}" Margin="1" Stretch="Fill" />
                                                    <Path Data="M0,20.000004 L5.9999999,20.000004 5.9999999,21.000004 0,21.000004 z M1.126528E-05,15.000001 L6.0000114,15.000001 6.0000114,16.000001 1.126528E-05,16.000001 z M1.126528E-05,10.000001 L6.0000114,10.000001 6.0000114,11.000001 1.126528E-05,11.000001 z M1.126528E-05,5.000001 L6.0000114,5.000001 6.0000114,6.000001 1.126528E-05,6.000001 z M1.126528E-05,0 L6.0000114,0 6.0000114,1 1.126528E-05,1 z" Fill="{StaticResource IconColor}" HorizontalAlignment="Right" Margin="0,2,8,3" Stretch="Fill" Width="6" />
                                                    <Path Data="M1.2500022,9.9999971 C1.9400048,9.9999971 2.5000023,10.560002 2.5000023,11.249997 2.5000023,11.94 1.9400048,12.499997 1.2500022,12.499997 0.55999988,12.499997 2.3257967E-06,11.94 2.2351736E-06,11.249997 2.3257967E-06,10.560002 0.55999988,9.9999971 1.2500022,9.9999971 z M1.2500001,0 C1.9399999,0 2.4999999,0.56000042 2.4999999,1.25 2.4999999,1.9400005 1.9399999,2.5 1.2500001,2.5 0.55999994,2.5 -6.6781467E-08,1.9400005 0,1.25 -6.6781467E-08,0.56000042 0.55999994,0 1.2500001,0 z" Fill="#FFE3751C" HorizontalAlignment="Left" Margin="8.75,6.75,0,6.75" Stretch="Fill" Width="2.5" />
                                                    <Path Data="M1.2500024,19.999996 C1.9400048,19.999996 2.5000024,20.560001 2.5000021,21.249996 2.5000024,21.939999 1.9400048,22.499996 1.2500024,22.499996 0.55999993,22.499996 2.3701691E-06,21.939999 2.3469324E-06,21.249996 2.3701691E-06,20.560001 0.55999993,19.999996 1.2500024,19.999996 z M1.2500001,9.999999 C1.9399999,9.999999 2.5,10.559999 2.5,11.249999 2.5,11.94 1.9399999,12.499999 1.2500001,12.499999 0.55999999,12.499999 -2.240904E-08,11.94 0,11.249999 -2.240904E-08,10.559999 0.55999999,9.999999 1.2500001,9.999999 z M1.2499997,0 C1.9399999,0 2.5,0.55999947 2.5,1.25 2.5,1.9399986 1.9399999,2.5 1.2499997,2.5 0.55999999,2.5 -2.240904E-08,1.9399986 0,1.25 -2.240904E-08,0.55999947 0.55999999,0 1.2499997,0 z" Fill="#FF68A490" HorizontalAlignment="Left" Margin="8.75,1.75,0,1.75" Stretch="Fill" Width="2.5" />
                                                </Grid>
                                            </tools:RibbonMenuItem.Icon>
                                            <tools:RibbonMenuItem.Items>
                                                <tools:RibbonMenuItem Style="{StaticResource SyncfusionIconSetsRibbonMenuItemStyle}"/>
                                            </tools:RibbonMenuItem.Items>
                                        </tools:RibbonMenuItem>
                                        <sharedt:MenuItemSeparator Padding="0,0,30,0"/>

                                        <tools:RibbonMenuItem Height="25" StaysOpenOnClick="True" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ClearRules}"
                                                                   IconBarEnabled="True">
                                            <tools:RibbonMenuItem.Icon>
                                                <Grid x:Name="ClearRulesIcon" Height="15.08" Width="14.257">
                                                    <Path Data="M0,0 L12,0 12,5.7960007 12,10.703997 8.3090001,14.323999 6.0979996,11.999999 0,11.999999 z" Fill="Transparent" Margin="0,0,2.257,0.756" Stretch="Fill" />
                                                    <Path Data="M0,0 L13,0 13,5 12,5 12,0.99999994 0.99999994,0.99999994 0.99999994,12 3.2369995,12 3.2369995,13 0,13 z" Fill="{StaticResource IconColor}" Margin="0,0,1.257,2.08" Stretch="Fill" />
                                                    <Path Data="M3.0000002,6.9999991 L4.0000002,6.9999991 4.0000002,7.9999999 4.9989994,7.9999999 4.9989994,8.9999998 4.0000002,8.9999998 4.0000002,9.999999 3.0000002,9.999999 3.0000002,8.9999998 0,8.9999998 0,7.9999999 3.0000002,7.9999999 z M0,4.9999996 L1.9999999,4.9999996 1.9999999,5.9999999 0,5.9999999 z M9.0000002,1.9989991 L11,1.9989991 11,2.9989991 9.0000002,2.9989991 z M0,1.9989991 L1.9999999,1.9989991 1.9999999,2.9989991 0,2.9989991 z M7,0 L8.0000002,0 8.0000002,1 7,1 z M3,0 L4.0000001,0 4.0000001,1 3,1 z" Fill="{StaticResource IconColor}" Margin="1,1,2.257,4.08" Stretch="Fill"/>
                                                    <Path Data="M0,0 L5,0 5,4 0,4 z" Fill="#FFEAC282" Height="4" Margin="4,3,5.257,0" Stretch="Fill" VerticalAlignment="Top" />
                                                    <Path Data="M1.1469994,5.4980008 L3.9899998,8.3249984 2.835001,9.4790008 0,6.6449994 z M6.683002,0 L9.5410013,2.8630002 4.7510018,7.6530001 1.8850019,4.7980002 z" Fill="#FFE68497" Margin="4.716,5.601,0,0" Stretch="Fill"/>
                                                </Grid>
                                            </tools:RibbonMenuItem.Icon>
                                            <tools:RibbonMenuItem Command="{Binding Commands.ConditonalFormattingClearRules}"
                                                                       CommandParameter="SelectedCells"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ClearRules_FromSelectedCells}" IconBarEnabled="True" />
                                            <tools:RibbonMenuItem Command="{Binding Commands.ConditonalFormattingClearRules}"
                                                                       CommandParameter="EntireSheet"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ClearRules_FromEntireSheet}" IconBarEnabled="True" />
                                        </tools:RibbonMenuItem>
                                    </tools:DropDownButton>

                                    <tools:RibbonGallery ExpandHeight="510" Height="68" Width="56" 
                                              x:Name="TableFormatStyle2010"
                                              ExpandWidth="504"
                                              ItemWidth="67"
                                              Visibility="Collapsed"
                                              MenuIconBarEnabled="True"
                                              ResizeDirection="HorizontalAndVertical"
                                              SizeForm="Large"
                                              BorderBrush="{x:Null}"
                                              IconTemplate="{StaticResource FormatAsTableIcon}"
                                              Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatAsTable}"
                                              VisualMode="DropDown">
                                        <tools:RibbonGallery.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatAsTable}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatAsTableToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonGallery.ToolTip>
                                        <tools:RibbonGallery.GalleryFilters>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=All}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatLight}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatMedium}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatDark}"/>
                                        </tools:RibbonGallery.GalleryFilters>

                                        <tools:RibbonGallery.GalleryGroups>
                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatLight}" tools:RibbonGallery.FilterIndexes="0,1">
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight1</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light1Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight2</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light2Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight3</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light3Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight4</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light4Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight5</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light5Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight6</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light6Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight7</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light7Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight8</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light8Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight9</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light9Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight10</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light10Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight11</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light11Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight12</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light12Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight13</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light13Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight14</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light14Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight15</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light15Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight16</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light16Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight17</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light17Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight18</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light18Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight19</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light19Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight20</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light20Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight21</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light21Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>

                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatMedium}" tools:RibbonGallery.FilterIndexes="0,2">
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium1</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium1Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium2</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium2Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium3</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium3Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium4</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium4Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium5</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium5Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium6</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium6Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium7</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium7Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium8</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium8Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium9</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium9Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium10</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium10Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium11</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium11Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium12</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium12Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium13</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium13Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium14</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium14Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium15</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium15Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium16</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium16Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium17</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium17Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium18</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium18Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium19</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium19Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Margin="3" Width="68" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium20</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium20Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium21</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium21Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium22</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium22Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium23</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium23Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium24</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium24Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium25</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium25Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium26</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium26Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium27</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium27Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium28</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium28Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>

                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatDark}" tools:RibbonGallery.FilterIndexes="0,3">
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark1</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark1Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark2</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark2Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark3</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark3Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark4</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark4Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark5</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark5Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark6</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark6Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark7</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark7Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark8</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark8Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark9</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark9Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark10</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark10Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark11</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark11Icon_2010}" />
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>
                                        </tools:RibbonGallery.GalleryGroups>
                                    </tools:RibbonGallery>

                                    <tools:RibbonGallery ExpandHeight="510" Height="68" Width="56" 
                                              x:Name="TableFormatStyle2013"
                                              ExpandWidth="504"
                                              ItemWidth="67"
                                              MenuIconBarEnabled="True"
                                              ResizeDirection="HorizontalAndVertical"
                                              SizeForm="Large"
                                              BorderBrush="{x:Null}"
                                              IconTemplate="{StaticResource FormatAsTableIcon}"
                                              Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatAsTable}"
                                              VisualMode="DropDown">
                                        <tools:RibbonGallery.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatAsTable}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatAsTableToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonGallery.ToolTip>
                                        <tools:RibbonGallery.GalleryFilters>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=All}" />
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatLight}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatMedium}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatDark}"/>
                                        </tools:RibbonGallery.GalleryFilters>

                                        <tools:RibbonGallery.GalleryGroups>
                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatLight}" tools:RibbonGallery.FilterIndexes="0,1">
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight1</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light1Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight2</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light2Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight3</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light3Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight4</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light4Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight5</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light5Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight6</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light6Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight7</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light7Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight8</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light8Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight9</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light9Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight10</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light10Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight11</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light11Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight12</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light12Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight13</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light13Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight14</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light14Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight15</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light15Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight16</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light16Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight17</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light17Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight18</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light18Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight19</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light19Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight20</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light20Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleLight21</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Light21Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>

                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatMedium}" tools:RibbonGallery.FilterIndexes="0,2">
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium1</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium1Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium2</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium2Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium3</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium3Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium4</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium4Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium5</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium5Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium6</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium6Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium7</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium7Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium8</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium8Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium9</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium9Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium10</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium10Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium11</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium11Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium12</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium12Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium13</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium13Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium14</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium14Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium15</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium15Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium16</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium16Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium17</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium17Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium18</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium18Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium19</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium19Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Margin="3" Width="68" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium20</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium20Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium21</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium21Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium22</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium22Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium23</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium23Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium24</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium24Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium25</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium25Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium26</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium26Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium27</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium27Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleMedium28</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Medium28Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>

                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatDark}" tools:RibbonGallery.FilterIndexes="0,3">
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark1</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark1Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark2</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark2Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark3</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark3Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark4</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark4Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark5</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark5Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark6</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark6Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark7</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark7Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark8</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark8Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark9</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark9Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark10</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark10Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Width="67" Command="{Binding Path=Commands.FormatAsTable}">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:TableBuiltInStyles>TableStyleDark11</xlsIo:TableBuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Image Stretch="Uniform" Source="{StaticResource Dark11Icon_2013}" />
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>
                                        </tools:RibbonGallery.GalleryGroups>
                                    </tools:RibbonGallery>

                                    <tools:RibbonGallery x:Name="CellStyle2010"
                                                  ItemHeight="25" BorderThickness="0" BorderBrush="{x:Null}"
                                                  ItemWidth="105" ExpandWidth="650" ExpandHeight="290" ScrollViewer.VerticalScrollBarVisibility="Auto"
                                                  ResizeDirection="NoResize"
                                                  VisualMode="DropDown"
                                                  Visibility="Collapsed"            
                                                  MenuIconBarEnabled="True"
                                                  SizeForm="Large"
                                                  Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles}"
                                                  IconTemplate="{StaticResource CellStylesIcon}">
                                        <tools:RibbonGallery.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStylesToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonGallery.ToolTip>
                                        <tools:RibbonGallery.GalleryFilters>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=All}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_GoodBadNeutral}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_TitlesAndHeadings}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_ThemedCellStyles}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_NumberFormat}"/>
                                        </tools:RibbonGallery.GalleryFilters>

                                        <tools:RibbonGallery.GalleryGroups>
                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_GoodBadNeutral}" tools:RibbonGallery.FilterIndexes="0, 1">
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Normal</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Normal}"  Margin="5,0,0,0" HorizontalAlignment="Left"  FontFamily="Calibri" VerticalAlignment="Center" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Bad</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#F8CBD1" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Bad}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center"  FontFamily="Calibri" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Good</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#C8EFCB" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Good}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center"  FontFamily="Calibri" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Neutral</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FDED96" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Neutral}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center"  FontFamily="Calibri" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>

                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_TitlesAndHeadings}" tools:RibbonGallery.FilterIndexes="0, 2">
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Heading1</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="0,-2,0,0">
                                                        <StackPanel Orientation="Vertical">
                                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Heading1}" FontWeight="Bold" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="18" Foreground="#003366"/>
                                                            <Border BorderBrush="#486CB4" BorderThickness="0,0,0,3"/>
                                                        </StackPanel>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Heading2</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Heading2}" Margin="5,0,0,0" FontWeight="Bold" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="16" Foreground="#003366"/>
                                                        <Border BorderBrush="#A5B7D7" BorderThickness="0,0,0,3"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Heading3</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Heading3}" Margin="5,0,0,0" HorizontalAlignment="Left" FontWeight="Bold" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="#003366"/>
                                                        <Border BorderBrush="#A5B7D7" BorderThickness="0,0,0,2"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Heading4</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Heading4}" Margin="5,0,0,0" HorizontalAlignment="Left" FontWeight="Bold" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="#003366"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Title</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Title}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri Light" FontSize="21" Foreground="#003366"/>
                                                    </Grid>
                                                    <!--<Image Source="{StaticResource TitleIcon}"/>-->
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Total</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="2">
                                                        <Border BorderBrush="#486CB4" VerticalAlignment="Top" BorderThickness="0,0,0,1" Margin="0,0,0,10"/>
                                                        <TextBlock Text="Total" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontWeight="Bold" FontFamily="Calibri" FontSize="14" Foreground="Black"/>
                                                        <Border BorderBrush="#486CB4" BorderThickness="0,0,0,1" Margin="0,0,0,2"/>
                                                        <Border BorderBrush="#486CB4" BorderThickness="0,0,0,1"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>

                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_ThemedCellStyles}" tools:RibbonGallery.FilterIndexes="0, 3">
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent1_20</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ffdce6f1" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent1_20}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent2_20</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#fff2dcdb" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent2_20}" HorizontalAlignment="Left" Margin="3" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent3_20</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ffebf1de" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent3_20}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent4_20</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ffe4dfec" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent4_20}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent5_20</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ffdaeef3" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent5_20}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent6_20</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#fffde9d9" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent6_20}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent1_40</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ffb8cce4" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent1_40}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent2_40</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ffe6b8b7" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent2_40}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent3_40</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ffd8e4bc" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent3_40}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent4_40</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ffccc0da" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent4_40}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent5_40</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ffb7dee8" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent5_40}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent6_40</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#fffcd5b4" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent6_40}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>

                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent1_60</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ff95b3d7" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent1_60}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent2_60</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ffda9694" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent2_60}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent3_60</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ffc4d79b" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent3_60}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent4_60</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ffb1a0c7" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent4_60}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent5_60</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#ff92cddc" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent5_60}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent6_60</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#fffabf8f" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent6_60}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <Grid Background="#4f81bd" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent1}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent1</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent2</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#c0504d" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent2}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent3</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#9bbb59" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent3}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent4</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#8064a2" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent4}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent5</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#4bacc6" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent5}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent6</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#f79646" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent6}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>

                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NumberFormat}" tools:RibbonGallery.FilterIndexes="0, 4">
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Comma</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Comma}" Margin="5,0,0,0" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Comma0</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_CommaZero}" Margin="5,0,0,0" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Currency</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Currency}" Margin="5,0,0,0" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Currency0</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_CurrencyZero}" Margin="5,0,0,0" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Percent</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Percent}" Margin="5,0,0,0" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>
                                        </tools:RibbonGallery.GalleryGroups>
                                    </tools:RibbonGallery>

                                    <tools:RibbonGallery x:Name="CellStyle2013"
                                                  ItemHeight="25" BorderThickness="0" BorderBrush="{x:Null}"
                                                  ItemWidth="105" ExpandWidth="650" ExpandHeight="300" ScrollViewer.VerticalScrollBarVisibility="Auto"
                                                  ResizeDirection="NoResize"
                                                  VisualMode="DropDown" 
                                                  MenuIconBarEnabled="True"
                                                  SizeForm="Large"
                                                  Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles}"
                                                  IconTemplate="{StaticResource CellStylesIcon}">
										<tools:RibbonGallery.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStylesToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonGallery.ToolTip>
                                        <tools:RibbonGallery.GalleryFilters>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=All}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_GoodBadNeutral}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_TitlesAndHeadings}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_ThemedCellStyles}"/>
                                            <tools:RibbonGalleryFilter Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_NumberFormat}"/>
                                        </tools:RibbonGallery.GalleryFilters>

                                        <tools:RibbonGallery.GalleryGroups>
                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_GoodBadNeutral}" tools:RibbonGallery.FilterIndexes="0, 1">
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Normal</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Normal}" Foreground="Black"  Margin="5,0,0,0" HorizontalAlignment="Left"  FontFamily="Calibri" VerticalAlignment="Center" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Bad</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#F8CBD1" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Bad}" Margin="5,0,0,0" Foreground="Black" HorizontalAlignment="Left" VerticalAlignment="Center"  FontFamily="Calibri" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Good</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#C8EFCB" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Good}" Margin="5,0,0,0" Foreground="Black" HorizontalAlignment="Left" VerticalAlignment="Center"  FontFamily="Calibri" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Neutral</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FDED96" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Neutral}" Margin="5,0,0,0" Foreground="Black" HorizontalAlignment="Left" VerticalAlignment="Center"  FontFamily="Calibri" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>

                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_TitlesAndHeadings}" tools:RibbonGallery.FilterIndexes="0, 2">
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Heading1</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="0,-2,0,0">
                                                        <StackPanel Orientation="Vertical">
                                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Heading1}" FontWeight="Bold" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="18" Foreground="#003366"/>
                                                            <Border BorderBrush="#486CB4" BorderThickness="0,0,0,3"/>
                                                        </StackPanel>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Heading2</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Heading2}" Margin="5,0,0,0" FontWeight="Bold" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="16" Foreground="#003366"/>
                                                        <Border BorderBrush="#A5B7D7" BorderThickness="0,0,0,3"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Heading3</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Heading3}" Margin="5,0,0,0" HorizontalAlignment="Left" FontWeight="Bold" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="#003366"/>
                                                        <Border BorderBrush="#A5B7D7" BorderThickness="0,0,0,2"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Heading4</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Heading4}" Margin="5,0,0,0" HorizontalAlignment="Left" FontWeight="Bold" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="#003366"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Title</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Title}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri Light" FontSize="21" Foreground="#003366"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Total</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="White" Margin="2">
                                                        <Border BorderBrush="#486CB4" VerticalAlignment="Top" BorderThickness="0,0,0,1" Margin="0,0,0,10"/>
                                                        <TextBlock Text="Total" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontWeight="Bold" FontFamily="Calibri" FontSize="14" Foreground="Black"/>
                                                        <Border BorderBrush="#486CB4" BorderThickness="0,0,0,1" Margin="0,0,0,2"/>
                                                        <Border BorderBrush="#486CB4" BorderThickness="0,0,0,1"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>

                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_ThemedCellStyles}" tools:RibbonGallery.FilterIndexes="0, 3">
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent1_20</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFD9E1F2" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent1_20}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent2_20</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFFCE4D6" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent2_20}" HorizontalAlignment="Left" Margin="3" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent3_20</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFEDEDED" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent3_20}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent4_20</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFFFF2CC" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent4_20}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent5_20</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFDDEBF7" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent5_20}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent6_20</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFE2EFDA" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent6_20}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent1_40</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFB4C6E7" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent1_40}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent2_40</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFF8CBAD" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent2_40}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent3_40</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFDBDBDB" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent3_40}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent4_40</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFFFE699" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent4_40}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent5_40</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFBDD7EE" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent5_40}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent6_40</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFC6E0B4" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent6_40}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="Black" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>

                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent1_60</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FF8EA9DB" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent1_60}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent2_60</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFF4B084" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent2_60}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent3_60</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFC9C9C9" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent3_60}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent4_60</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFFFD966" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent4_60}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent5_60</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FF9BC2E6" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent5_60}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Height="26" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent6_60</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFA9D08E" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent6_60}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <Grid Background="#FF4472C4" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent1}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent1</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent2</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFED7D31" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent2}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent3</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFA5A5A5" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent3}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent4</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FFFFC000" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent4}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent5</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FF5B9BD5" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent5}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Accent6</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Background="#FF70AD47" Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Accent6}" Margin="5,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontFamily="Calibri" FontSize="14" Foreground="White" />
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>

                                            <tools:RibbonGalleryGroup Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_NumberFormat}" tools:RibbonGallery.FilterIndexes="0, 4">
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Comma</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Comma}" Margin="5,0,0,0" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Comma0</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_CommaZero}" Margin="5,0,0,0" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Currency</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Currency}" Margin="5,0,0,0" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Currency0</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_CurrencyZero}" Margin="5,0,0,0" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                                <tools:RibbonGalleryItem Command="{Binding Commands.FormatCellStyle}" Width="105">
                                                    <tools:RibbonGalleryItem.CommandParameter>
                                                        <xlsIo:BuiltInStyles>Percent</xlsIo:BuiltInStyles>
                                                    </tools:RibbonGalleryItem.CommandParameter>
                                                    <Grid Margin="2">
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellStyles_Percent}" Margin="5,0,0,0" FontSize="14"/>
                                                    </Grid>
                                                </tools:RibbonGalleryItem>
                                            </tools:RibbonGalleryGroup>
                                        </tools:RibbonGallery.GalleryGroups>
                                    </tools:RibbonGallery>
                                </tools:RibbonBar>

                                <!--  Cells  -->
                                <tools:RibbonBar IconTemplate="{StaticResource CollapseCellsIcon}"
                                                      Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cells}"
                                                      IsLargeButtonPanel="True"
                                                      IsLauncherButtonVisible="False">
                                    <tools:DropDownButton 
                                                               Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cells}"
                                                               IconTemplate="{StaticResource CellsSmallIcon}"
                                                               SizeForm="Large">
                                        <tools:DropDownButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cells}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellsToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:DropDownButton.ToolTip>
                                        <StackPanel Orientation="Horizontal">
                                            <tools:DropDownButton x:Name="Cells_Insert"
                                                               Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Insert}"
                                                               IconTemplate="{StaticResource InsertIcon}"
                                                               SizeForm="Large">
                                                <tools:DropDownButton.ToolTip>
                                                    <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Insert}">
                                                        <StackPanel>
                                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=InsertCellsToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                        </StackPanel>
                                                    </tools:ScreenTip>
                                                </tools:DropDownButton.ToolTip>
                                                <tools:RibbonMenuGroup Width="176"
                                                                    IconBarEnabled="True">
                                                    <tools:RibbonMenuItem x:Name="Insert_InsertCells" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=InsertCells}"
                                                                       Command="{Binding Commands.InsertCells}"
                                                                       CommandParameter="InsertCells" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid x:Name="InsertCellsIcon" Height="16" Width="16">
                                                                <Path Data="F1M356.5,335L356.5,332.5L348.5,332.5L348.5,335.5L357,335.5" Fill="#FFC0D2E4" Stretch="Fill" Margin="7.5,5.5,0,7.5"/>
                                                                <Path Data="M0,9.0000001 L7,9.0000001 7,14 0,14 z M0,0 L7,0 7,2 0,2 z" Fill="Transparent" HorizontalAlignment="Left" Margin="1,1,0,1" Stretch="Fill" Width="7" />
                                                                <Path Data="M5,13 L5,15 8,15 8,13 z M1,13 L1,15 4,15 4,13 z M5,9 L7,9 7,10 5,10 z M0,9 L1,9 1,12 4,12 4,10 5,10 5,12 8,12 8,10 9,10 9,12 9,13 9,16 5,16 4,16 0,16 z M12,6 L12,8 15,8 15,6 z M8,6 L8,8 11,8 11,6 z M7,5 L16,5 16,8 16,9 12,9 11,9 7,9 z M5,1 L5,3 8,3 8,1 z M0,0 L9,0 9,4 4,4 4,1 1,1 1,3 2,3 2,4 0,4 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                                <Path Data="M2,0 L4,0 4,1 3,1 3,2 6,2 6,4 3,4 3,5 4,5 4,6 2,6 2,5 1,5 1,4 0,4 0,2 1,2 1,1 2,1 z" Fill="#FF4A7DB1" HorizontalAlignment="Left" Margin="0,4,0,6" Stretch="Fill" Width="6"/>
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>
                                                    <Separator Margin="32,0,0,0"/>
                                                    <tools:RibbonMenuItem x:Name="Insert_InsertSheetRows"
                                                                       Command="{Binding Commands.InsertSheetRows}" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=InsertRow}" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid x:Name="InsertRowIcon" Height="16" Width="13">
                                                                <Path Data="M0,12 L4.5000001,12 4.5000001,15 0,15 z M0,0 L4.5000001,0 4.5000001,3 0,3 z" Fill="Transparent" HorizontalAlignment="Left" Margin="1,0.5,0,0.5" Stretch="Fill" Width="4.5" />
                                                                <Path Data="M0,0 L5.5,0 5.5,3 0,3 z" Fill="#FFC1D3E6" HorizontalAlignment="Right" Margin="0,6.5" Stretch="Fill" Width="5.5" />
                                                                <Path Data="M0,0 L6,0 6,0.99999997 1,0.99999997 1,3 6,3 6,4 0,4 z" Fill="#FF727272" HorizontalAlignment="Right" Margin="0,6" Stretch="Fill" Width="6" />
                                                                <Path Data="M0,12 L5,12 5,16 0,16 0,15 4,15 4,13 0,13 z M0,0 L5,0 5,4 0,4 0,3 4,3 4,1 0,1 z" Fill="{StaticResource IconColor}" HorizontalAlignment="Left" Margin="1,0,0,0" Stretch="Fill" Width="5" />
                                                                <Path Data="M2,0 L4,0 4,1 3,1 3,2.0000001 6,2.0000001 6,4 3,4 3,5 4,5 4,6 2,6 2,5 1,5 1,4 0,4 0,2.0000001 1,2.0000001 1,1 2,1 z" Fill="#FF4A7DB1" HorizontalAlignment="Left" Margin="0,5" Stretch="Fill" Width="6" />
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>
                                                    <tools:RibbonMenuItem x:Name="Insert_InsertSheetColumns" 
                                                                       Command="{Binding Commands.InsertSheetColumns}" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=InsertColumn}" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid x:Name="InsertColumnIcon" Height="13" Width="16">
                                                                <Path Data="M0,0 L3,0 3,5.5 0,5.5 z" Fill="#FFC1D3E6" Height="5.5" Margin="6.5,0" Stretch="Fill" VerticalAlignment="Bottom" />
                                                                <Path Data="M0,0 L4,0 4,6 3,6 3,1 0.99999997,1 0.99999997,6 0,6 z" Fill="#FF727272" Height="6" Margin="6,0" Stretch="Fill" VerticalAlignment="Bottom" />
                                                                <Path Data="M12,0 L15,0 15,4.5000001 12,4.5000001 z M0,0 L3,0 3,4.5000001 0,4.5000001 z" Fill="Transparent" Height="4.5" Margin="0.5,1,0.5,0" Stretch="Fill" VerticalAlignment="Top"/>
                                                                <Path Data="M12,0 L13,0 13,4 15,4 15,0 16,0 16,5 12,5 z M0,0 L1,0 1,4 3,4 3,0 4,0 4,5 0,5 z" Fill="{StaticResource IconColor}" Height="5" Margin="0,1,0,0" Stretch="Fill" VerticalAlignment="Top"/>
                                                                <Path Data="M2,0 L4,0 4,1 5,1 5,2 6,2 6,4 5,4 5,3 4,3 4,6 2,6 2,3 1,3 1,4 0,4 0,2.0000001 1,2.0000001 1,1 2,1 z" Fill="#FF4A7DB1" Height="6" Margin="5,0" Stretch="Fill" VerticalAlignment="Top"/>
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>
                                                    <Separator Margin="32,0,0,0"/>
                                                    <tools:RibbonMenuItem x:Name="Insert_InsertSheet" Command="{Binding Commands.InsertSheet}" 
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=InsertSheet}" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid x:Name="InsertSheetIcon" Height="16" Width="16">
                                                                <Path Data="M0,10 L5,10 5,11 0,11 z M0,0 L13,0 13,9.0000001 0,9.0000001 z" Fill="Transparent" Margin="1,1,2,4" Stretch="Fill" />
                                                                <Path Data="M7,12 L10,12 10,13 7,13 z M0.99999994,12 L5,12 5,13 0.99999994,13 z M0,0 L15,0 15,10 14,10 14,1.0000001 0.99999994,1.0000001 0.99999994,10 11,10 11,11 7,11 7,12 6,12 6,11 0.99999994,11 0.99999994,12 0,12 0,11 z" Fill="{StaticResource IconColor}" Margin="0,0,1,3" Stretch="Fill" />
                                                                <Path Data="M2,0 L4,0 4,1 3,1 3,2 6,2 6,3 3,3 3,4 4,4 4,5 2,5 2,4 1,4 1,3 0,3 0,2 1,2 1,1 2,1 z" Fill="#FF4A7DB1" HorizontalAlignment="Right" Height="5" Stretch="Fill" VerticalAlignment="Bottom" Width="6"/>
                                                                <Path Data="M7,3 L7,5.9999999 10,5.9999999 10,3 z M3,3 L3,5.9999999 6,5.9999999 6,3 z M2,0 L3,0 3,1.9999999 6,1.9999999 6,0 7,0 7,1.9999999 10,1.9999999 10,0 11,0 11,1.9999999 13,1.9999999 13,3 11,3 11,5.9999999 13,5.9999999 13,6.9999999 11,6.9999999 11,8.9999999 10,8.9999999 10,6.9999999 7,6.9999999 7,8.9999999 6,8.9999999&#xd;&#xa;6,6.9999999 3,6.9999999 3,8.9999999 2,8.9999999 2,6.9999999 0,6.9999999 0,5.9999999 2,5.9999999 2,3 0,3 0,1.9999999 2,1.9999999 z" Fill="#FFB3B3B3" Margin="1,1,2,6" Stretch="Fill" />
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>
                                                </tools:RibbonMenuGroup>
                                            </tools:DropDownButton>

                                            <tools:DropDownButton x:Name="Cells_Delete" 
                                                               Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Delete}"
                                                               IconTemplate="{StaticResource DeleteIcon}"
                                                               SizeForm="Large">
                                                <tools:DropDownButton.ToolTip>
                                                    <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Delete}">
                                                        <StackPanel>
                                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DeleteCellsToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                        </StackPanel>
                                                    </tools:ScreenTip>
                                                </tools:DropDownButton.ToolTip>
                                                <tools:RibbonMenuGroup Width="176" FontSize="11.5" IconBarEnabled="True">
                                                    <tools:RibbonMenuItem x:Name="Delete_DeleteCells" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DeleteCells}"
                                                                       Command="{Binding Commands.DeleteCells}" CommandParameter="DeleteCells" IconBarEnabled="True">

                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid>
                                                                <Grid x:Name="DeleteCellsIcon" Height="16" Width="16.001">
                                                                    <Path Data="M1.3285368,2.259016E-05 C1.4572943,-0.0010185242 1.5851028,0.033921063 1.6964085,0.10055006 2.8628214,0.80184205 3.8364358,1.5798782 4.6435535,2.3601614 L4.720475,2.4355625 4.7347477,2.4266483 C6.6033781,1.2686002 8.1410044,0.62799957 8.1410044,0.62799953 7.4053333,1.2120525 6.4481652,2.0747133 5.4797786,2.9848157 L5.359443,3.0983535 5.3961461,3.1384345 C7.4466694,5.4073795 8.1400135,7.4819921 8.1400135,7.4819921 7.2698791,6.437867 5.8696721,5.2169746 4.5136822,4.1352595 L4.382119,4.0306891 4.3257878,4.0849158 C2.6270976,5.72484 1.1645806,7.2188834 1.1645806,7.2188834 0.38053623,7.5928692 0.064507405,7.086882 0.0075123791,6.6998953 -0.012493141,6.5628895 0.0045061872,6.409892 0.094507965,6.3009008 1.047769,5.1423105 2.2417865,4.1332663 3.4159305,3.3003696 L3.4296856,3.2907013 3.2912879,3.1854869 C1.8208169,2.0706924 0.64447305,1.2706201 0.64447299,1.2706201 0.21549934,0.41456884 0.79546377,0.068548143 1.2394365,0.0065444708 1.2690597,0.0024191737 1.2988235,0.00026279688 1.3285368,2.259016E-05 z" Fill="#FFD86344" Margin="7.86,3.285,0,5.233" Stretch="Fill" />
                                                                    <Path Data="M0,0 L4,0 4,2.5 3.5,3 0,3 z" Fill="#FFC0D2E4" HorizontalAlignment="Left" Margin="3.5,5.5,0,7.5" Stretch="Fill" Width="4" />
                                                                    <Path Data="M5,13 L5,15 8,15 8,13 z M1,13 L1,15 4,15 4,13 z M0,9 L3,9 3,10 1,10 1,12 4,12 4,10 5,10 5,12 9,12 9,16 5,16 4,16 0,16 z M3,5 L8,5 8,8 7,8 7,6 4,6 4,8 7,8 7,9 3,9 z M1,1 L1,3 4,3 4,1 z M0,0 L9,0 9,2 8,2 8,1 5,1 5,3 7,3 7,4 0,4 z" Fill="{StaticResource IconColor}" Margin="0,0,7.001,0" Stretch="Fill"/>
                                                                </Grid>
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>
                                                    <Separator Margin="32,0,0,0"/>
                                                    <tools:RibbonMenuItem x:Name="Delete_DeleteSheetRows"
                                                                       Command="{Binding Commands.DeleteSheetRows}" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DeleteRow}" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid x:Name="DeleteRowIcon" Height="12" Width="15.415">
                                                                <Path Data="M1.6312299,2.3084639E-05 C1.7898771,-0.0011460588 1.946839,0.042115199 2.083333,0.12499425 3.5164806,0.98608763 4.7127167,1.9415538 5.7043678,2.8998539 L5.7988201,2.9924032 5.8163143,2.981476 C8.1116768,1.5588663 10.000016,0.771998 10.000016,0.77199806 9.0965801,1.4895063 7.9209763,2.5493037 6.731536,3.6673905 L6.5840293,3.8065927 6.6290218,3.8557088 C9.1483256,6.6424307 10.000012,9.1909942 10.000012,9.1909942 8.9312121,7.908463 7.2110273,6.4087414 5.5451018,5.0799641 L5.3838083,4.9517808 5.3140994,5.0188975 C3.2275814,7.0335979 1.4310293,8.8690919 1.4310293,8.8690919 0.4680308,9.3280973 0.079031408,8.7070903 0.010031462,8.2310848 -0.015968502,8.0610828 0.0050314665,7.8750805 0.11503133,7.7410788 1.286764,6.317656 2.7538239,5.0780215 4.196247,4.0548181 L4.2135359,4.0426659 4.0432914,3.9132597 C2.2366815,2.5438356 0.79137667,1.5609998 0.79137659,1.5609998 0.26539651,0.50799014 0.97737393,0.085001006 1.5213587,0.0079898545 1.5579183,0.002928696 1.594619,0.00029279829 1.6312299,2.3084639E-05 z" Fill="#FFD86344" Margin="5.415,1.411,0,1.398" Stretch="Fill"/>
                                                                <Path Data="M3.0000001,9 L4.0000001,9 4.0000001,12 0,12 0,11 3.0000001,11 z M0,0 L4.0000001,0 4.0000001,3 3.0000001,3 3.0000001,1 0,1 z" Fill="{StaticResource IconColor}" HorizontalAlignment="Left" Stretch="Fill" Width="4" />
                                                                <Path Data="M0,3 L5,3 5,4 0,4 z M0,0 L6,0 6,1 0,1 z" Fill="#FFB3B3B3" HorizontalAlignment="Left" Margin="0,4" Stretch="Fill" Width="6"/>
                                                                <Path Data="M0,0 L7,0 7,1 6,1 6,2 0,2 0,1 z" Fill="#FFC1D3E6" HorizontalAlignment="Left" Margin="0,5" Stretch="Fill" Width="7" />
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>
                                                    <tools:RibbonMenuItem x:Name="Delete_DeleteSheetColumns"
                                                                       Command="{Binding Commands.DeleteSheetColumns}" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DeleteColumn}" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid x:Name="DeleteColumnIcon" Height="14.662" Width="12">
                                                                <Path Data="M1.6308488,2.43783E-05 C1.7895063,-0.0011752844 1.9464721,0.041937947 2.0829728,0.12400037 3.5161844,0.98532768 4.7124742,1.9409724 5.704171,2.8994093 L5.7984255,2.9917731 5.8162492,2.9806392 C8.1116412,1.557871 10.000005,0.77100514 10.000005,0.77100511 9.0965589,1.488509 7.9209407,2.5483034 6.7314842,3.6663878 L6.5835889,3.8059547 6.6288675,3.855387 C9.1482881,6.6424313 10.000019,9.1909909 10.000019,9.1909909 8.9311698,7.9084611 7.2109063,6.4085751 5.5449055,5.0796022 L5.3833225,4.9511752 5.3140276,5.0178928 C3.22748,7.0325904 1.4309006,8.8680844 1.4309006,8.8680844 0.46789155,9.3270736 0.078894511,8.7060652 0.0098933723,8.2310848 -0.016107862,8.0610695 0.0058955794,7.8750634 0.11489043,7.7410593 1.2866388,6.3173146 2.7537182,5.0774641 4.1961601,4.0541167 L4.21324,4.04211 4.0430268,3.9127119 C2.2363351,2.5430546 0.79096521,1.5599989 0.79096515,1.559999 0.2659621,0.50800002 0.97696633,0.085000455 1.5209696,0.0080005527 1.5575323,0.0029380322 1.5942356,0.00030130148 1.6308488,2.43783E-05 z" Fill="#FFD86344" Margin="0.437,5.471,1.563,0" Stretch="Fill" />
                                                                <Path Data="M8,0 L10,0 10,3.0000001 8,3.0000001 z M0,0 L2,0 2,3.0000001 0,3.0000001 z" Fill="Transparent" Height="3" Margin="1,0" Stretch="Fill" VerticalAlignment="Top"/>
                                                                <Path Data="M4,1.7881393E-07 L5,1.7881393E-07 5,5.0000001 4,5.0000001 z M11,0 L12,0 12,4.0000001 9,4.0000001 9,3.0000001 11,3.0000001 z M7,0 L8,0 8,6.0000001 7,6.0000001 z M0,0 L0.99999994,0 0.99999994,3.0000001 3,3.0000001 3,4.0000001 0,4.0000001 z" Fill="{StaticResource IconColor}" Height="6" Stretch="Fill" VerticalAlignment="Top"/>
                                                                <Path Data="M0,0 L1,0 2,0 2,7 1,7 1,6 0,6 z" Fill="#FFC0D2E4" Height="7" Margin="5,0" Stretch="Fill" VerticalAlignment="Top"/>
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>
                                                    <Separator Margin="32,0,0,0"/>
                                                    <tools:RibbonMenuItem x:Name="Delete_DeleteSheet" Command="{Binding Commands.DeleteSheet}" 
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DeleteSheet}" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid x:Name="DeleteSheetIcon" Height="15.049" Width="15.384">
                                                                <Path Data="M0,0 L4,0 13,0 13,4.0000001 4,4.0000001 4,11 0,11 0,4.0000001 z" Fill="Transparent" Margin="1,1,1.384,3.049" Stretch="Fill"/>
                                                                <Path Data="M1.630293,2.4302675E-05 C1.7891979,-0.0011735623 1.9468247,0.041934271 2.0833188,0.12399512 3.516228,0.98532446 4.7122822,1.9409712 5.7037954,2.8994099 L5.7982464,2.9919837 5.8164048,2.9806395 C8.1116352,1.5578688 10,0.77100195 10,0.7710019 9.0965514,1.4885089 7.9210296,2.5483044 6.7317076,3.6663891 L6.5834126,3.8063489 6.6283245,3.8553894 C9.1473031,6.6424385 9.9989972,9.1910015 9.9989972,9.1910015 8.930202,7.9084707 7.210186,6.4087463 5.5444589,5.0799666 L5.3831282,4.951738 5.3144259,5.0178934 C3.2281559,7.03259 1.4318957,8.8680807 1.4318958,8.8680807 0.46788396,9.3270851 0.078879237,8.7060791 0.0098783374,8.2310749 -0.016121924,8.0610729 0.0058783293,7.8750711 0.11587968,7.7410699 1.2873002,6.3173219 2.7541599,5.077469 4.1964548,4.0541192 L4.2129083,4.0425516 4.042836,3.9132589 C2.2364614,2.5438314 0.79137674,1.5609924 0.79137677,1.5609923 0.26539618,0.50799807 0.97734365,0.084993643 1.5203218,0.0079977653 1.5568843,0.0029365976 1.5936227,0.00030069987 1.630293,2.4302675E-05 z" Fill="#FFD86344" Margin="5.384,5.858,0,0" Stretch="Fill"/>
                                                                <Path Data="M1,12 L4.9999999,12 4.9999999,13 1,13 z M1,8.0000001 L1,10 3,10 3,8.0000001 z M1,4.0000001 L1,7.0000001 3,7.0000001 3,4.0000001 z M12,1.0000002 L12,3.0000001 14,3.0000001 14,1.0000002 z M8,1.0000002 L8,3.0000001 11,3.0000001 11,1.0000002 z M4,1.0000002 L4,3.0000001 7,3.0000001 7,1.0000002 z M1,1.0000002 L1,3.0000001 3,3.0000001 3,1.0000002 z M0,0 L15,0 15,5.0000001 14,5.0000001 14,4.0000001 12,4.0000001 12,6.0000001 11,6.0000001 11,4.0000001 8,4.0000001 7,4.0000001 4,4.0000001 4,7.0000001 4.9999999,7.0000001 4.9999999,8.0000001 4,8.0000001 4,10 6,10 6,11 1,11 1,12 0,12 z" Fill="{StaticResource IconColor}" Margin="0,0,0.384,2.049" Stretch="Fill"/>
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>
                                                </tools:RibbonMenuGroup>
                                            </tools:DropDownButton>

                                            <tools:DropDownButton x:Name="Cells_Format" Width="Auto" 
                                                               Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Format}"
                                                               IconTemplate="{StaticResource FormatIcon}"
                                                               SizeForm="Large">
                                                <tools:DropDownButton.ToolTip>
                                                    <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Format}">
                                                        <StackPanel>
                                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCellsToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                        </StackPanel>
                                                    </tools:ScreenTip>
                                                </tools:DropDownButton.ToolTip>
                                                <tools:RibbonMenuGroup Width="180" IconBarEnabled="True">
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=CellSize}" FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" Padding="5,2,2,2" Style="{StaticResource WPFTextBlockStyle}"/>
                                                    <tools:RibbonMenuItem Padding="2"
                                                                       Command="{Binding Commands.FormatRowHeight}" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RowHeight}" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid x:Name="RowHeightIcon" Height="16" Width="15">
                                                                <Path Data="M0,0 L7.5,0 7.5,13 0,13 z" Fill="Transparent" HorizontalAlignment="Right" Margin="0,1.5" Stretch="Fill" Width="7.5" />
                                                                <Path Data="M0,0 L8,0 8,0.99999994 0.99999997,0.99999994 0.99999997,13 8,13 8,14 0,14 z" Fill="{StaticResource IconColor}" Margin="7,1,0,1" Stretch="Fill" />
                                                                <Path Data="M2,9 L3.0000001,9 3.0000001,13 4.0000001,13 4.0000001,12 4.9999999,12 4.9999999,14 4.0000001,14 4.0000001,15 3.0000001,15 3.0000001,16 2,16 2,15 1,15 1,14 0,14 0,12 1,12 1,13 2,13 z M2,0 L2.9999999,0 2.9999999,1 4.0000001,1 4.0000001,2 4.9999999,2 4.9999999,4 4.0000001,4 4.0000001,3 2.9999999,3 2.9999999,7 2,7 2,3 1,3 1,4 0,4 0,2 1,2 1,1 2,1 z" Fill="#FF4A7DB1" HorizontalAlignment="Left" Stretch="Fill" Width="5"/>
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>

                                                    <tools:RibbonMenuItem Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=AutoFitRowHeight}"
                                                                       Command="{Binding Commands.AutoFitCommand}"
                                                                       CommandParameter="RowHeight" IconBarEnabled="True"
                                                                       Padding="2"/>
                                                    <Separator Margin="33,0,0,0" />
                                                    <tools:RibbonMenuItem Command="{Binding Commands.FormatColumnWidth}" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ColumnWidth}"
                                                                       Padding="2" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid x:Name="ColumnWidthIcon" Height="14" Width="16">
                                                                <Path Data="M0,0 L13,0 13,7.5 0,7.5 z" Fill="Transparent" Margin="1.5,0.5,1.5,6" Stretch="Fill" />
                                                                <Path Data="M0,0 L14,0 14,8 13,8 13,0.99999997 0.99999994,0.99999997 0.99999994,8 0,8 z" Fill="{StaticResource IconColor}" Margin="1,0,1,6" Stretch="Fill" />
                                                                <Path Data="M12,0 L14,0 14,1 15,1 15,2 16,2 16,3.0000001 15,3.0000001 15,4.0000001 14,4.0000001 14,5.0000001 12,5.0000001 12,4.0000001 13,4.0000001 13,3.0000001 9,3.0000001 9,2 13,2 13,1 12,1 z M2,0 L4,0 4,1 3,1 3,2 7,2 7,3.0000001 3,3.0000001 3,4.0000001 4,4.0000001 4,5.0000001 2,5.0000001 2,4.0000001 1,4.0000001 1,3.0000001 0,3.0000001 0,2 1,2 1,1 2,1 z" Fill="#FF4A7DB1" Height="5" Stretch="Fill" VerticalAlignment="Bottom"/>
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>

                                                    <tools:RibbonMenuItem Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=AutoFitColumnWidth}"
                                                                       Command="{Binding Commands.AutoFitCommand}"
                                                                       CommandParameter="ColumnWidth" IconBarEnabled="True"
                                                                       Padding="2,2,0,2"/>
                                                    <tools:RibbonMenuItem Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DefaultWidth}"
                                                                       Command="{Binding Commands.FormatDefaultColumnWidth}"
                                                                       CommandParameter="DefaultWidth" IconBarEnabled="True"
                                                                       Padding="2,2,0,2"/>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Visibility}" FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" Padding="7,2,0,2" Style="{StaticResource WPFTextBlockStyle}"/>
                                                    <tools:RibbonMenuItem Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HideAndUnhide}" StaysOpenOnClick="True" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.Items>
                                                            <tools:RibbonMenuItem Command="{Binding Commands.HideRows}" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HideRows}" IconBarEnabled="True"
                                                                               Padding="2">
                                                                <tools:RibbonMenuItem.CommandParameter>
                                                                    <system:Boolean>true</system:Boolean>
                                                                </tools:RibbonMenuItem.CommandParameter>
                                                            </tools:RibbonMenuItem>
                                                            <tools:RibbonMenuItem Command="{Binding Commands.HideColumns}" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HideColumns}"
                                                                               Padding="2" IconBarEnabled="True">
                                                                <tools:RibbonMenuItem.CommandParameter>
                                                                    <system:Boolean>true</system:Boolean>
                                                                </tools:RibbonMenuItem.CommandParameter>
                                                            </tools:RibbonMenuItem>
                                                            <tools:RibbonMenuItem Command="{Binding Commands.HideSheet}"
                                                                               CommandParameter="{StaticResource True}"
                                                                               Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HideSheet}" 
                                                                               Padding="2" IconBarEnabled="True"/>
                                                            <Separator Width="70"/>
                                                            <tools:RibbonMenuItem Command="{Binding Commands.HideRows}" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnHideRows}"
                                                                               Padding="2" IconBarEnabled="True">
                                                                <tools:RibbonMenuItem.CommandParameter>
                                                                    <system:Boolean>false</system:Boolean>
                                                                </tools:RibbonMenuItem.CommandParameter>
                                                            </tools:RibbonMenuItem>

                                                            <tools:RibbonMenuItem Command="{Binding Commands.HideColumns}" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnHideColumns}"
                                                                               Padding="2" IconBarEnabled="True">
                                                                <tools:RibbonMenuItem.CommandParameter>
                                                                    <system:Boolean>false</system:Boolean>
                                                                </tools:RibbonMenuItem.CommandParameter>
                                                            </tools:RibbonMenuItem>
                                                            <tools:RibbonMenuItem Command="{Binding Commands.HideSheet}"
                                                                               CommandParameter="{StaticResource False}"
                                                                               Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnHideSheet}" 
                                                                               Padding="2" IconBarEnabled="True"/>
                                                        </tools:RibbonMenuItem.Items>
                                                    </tools:RibbonMenuItem>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=OrganizeSheets}" Padding="5,0,0,0" FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" Style="{StaticResource WPFTextBlockStyle}"/>
                                                    <tools:RibbonMenuItem Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RenameSheet}"
                                                                        Command="{Binding Commands.RenameSheetCommand}"
                                                                        Padding="2" IconBarEnabled="True">
                                                    </tools:RibbonMenuItem>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Protection}" Padding="5,0,0,0" FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" Style="{StaticResource WPFTextBlockStyle}"/>
                                                    <tools:RibbonMenuItem x:Name="Format_ProtectSheet" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectSheet}"
                                                                       Command="{Binding Commands.ProtectSheet}" IconSize="16,16"
                                                                       Padding="2" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.ToolTip>
                                                            <ToolTip>
                                                                <StackPanel>
                                                                    <TextBlock Padding="4" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectWorksheetToolTip}" MaxWidth="150" TextWrapping="Wrap"/>
                                                                </StackPanel>
                                                            </ToolTip>
                                                        </tools:RibbonMenuItem.ToolTip>
                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid Height="16" Width="16">
                                                                <Path Data="M0,0 L14,0 14,3.9999999 8,3.9999999 8,6 8,13 0,13 0,6 0,3 z" Fill="Transparent" Margin="1,1,1,2" Stretch="Fill"/>
                                                                <Path Data="M7,13 L7,14 8.1919975,14 8.6914177,13 z M0,0 L15,0 15,5 14,5 14,0.99999994 0.99999994,0.99999994 0.99999994,12 10,12 10,13 9.8098774,13 8.8089981,15 6.9999967,15 6.9999967,14 6,14 6,13 0.99999994,13 0.99999994,14 5,14 5,15 0,15 z" Fill="{StaticResource IconColor}" Margin="0,0,1,1" Stretch="Fill" />
                                                                <Path Data="F1M187.5,506L187.5,503.5C187.5,502.396,188.396,501.5,189.5,501.5C190.604,501.5,191.5,502.396,191.5,503.5L191.5,506" Fill="Transparent" Stretch="Fill" Width="4" HorizontalAlignment="Right" Margin="0,6.5,1.5,5"/>
                                                                <Path Data="M3.4999999,0.99999994 C2.6730041,1 1.9999999,1.6730042 1.9999999,2.5 L1.9999999,4 4.9999999,4 4.9999999,2.5 C4.9999999,1.6730042 4.3269958,1 3.4999999,0.99999994 z M3.4999999,0 C4.8789977,0 5.9999999,1.1210022 5.9999999,2.5 L5.9999999,4 6.9999999,4 6.9999999,10 0,10 0,4 0.99999994,4 0.99999994,2.5 C0.99999994,1.1210022 2.1210021,0 3.4999999,0 z" Fill="#FFEBC689" HorizontalAlignment="Right" Margin="0,6,0,0" Stretch="Fill" Width="7"/>
                                                                <Path Data="M0,13 L1,13 1,14 0,14 z M5,12 L6,12 6,13 7,13 7,14 6,14 5,14 z M4,3.0000001 L4,7.0000001 7,7.0000001 7,3.0000001 z M3,0 L4,0 4,2.0000001 7,2.0000001 7,2.0861626E-07 8,2.0861626E-07 8,2.0000001 11,2.0000001 11,0 12,0 12,2.0000001 14,2.0000001 14,3.0000001 12,3.0000001 12,4.0000001 11,4.0000001 11,3.0000001 8,3.0000001 8,7.0000001 9,7.0000001 9,8.0000001 8,8.0000001 8,11 7,11&#xd;&#xa;7,8.0000001 4,8.0000001 4,11 3,11 3,8.0000001 1,8.0000001 1,7.0000001 3,7.0000001 3,3.0000001 1,3.0000001 1,2.0000001 3,2.0000001 z" Fill="{StaticResource IconColor}" Margin="0,1,2,1" Stretch="Fill"/>
                                                                <Path Data="M0.75,0 C1.164,0 1.5,0.336 1.5,0.75000001 1.5,0.9699375 1.4051719,1.1678613 1.2541983,1.3050888 L1.25,1.3087258 1.25,3.75 0.24999997,3.75 0.24999997,1.3087258 0.2458017,1.3050888 C0.094828129,1.1678613 0,0.9699375 0,0.75000001 0,0.336 0.336,0 0.75,0 z" Fill="Transparent" HorizontalAlignment="Right" Height="3.75" Margin="0,0,2.75,1" Stretch="Fill" VerticalAlignment="Bottom" Width="1.5"/>
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>
                                                    <tools:RibbonMenuItem x:Name="Format_UnProtectSheet" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnProtectSheet}"
                                                                       Command="{Binding Commands.ProtectSheet}" IconSize="16,16"
                                                                       Padding="2" Visibility="Collapsed" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.ToolTip>
                                                            <ToolTip>
                                                                <StackPanel>
                                                                    <TextBlock Padding="4" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectWorksheetToolTip}" MaxWidth="150" TextWrapping="Wrap"/>
                                                                </StackPanel>
                                                            </ToolTip>
                                                        </tools:RibbonMenuItem.ToolTip>
                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid Height="16" Width="16">
                                                                <Path Data="M0,0 L14,0 14,3.9999999 8,3.9999999 8,6 8,13 0,13 0,6 0,3 z" Fill="Transparent" Margin="1,1,1,2" Stretch="Fill"/>
                                                                <Path Data="M7,13 L7,14 8.1919975,14 8.6914177,13 z M0,0 L15,0 15,5 14,5 14,0.99999994 0.99999994,0.99999994 0.99999994,12 10,12 10,13 9.8098774,13 8.8089981,15 6.9999967,15 6.9999967,14 6,14 6,13 0.99999994,13 0.99999994,14 5,14 5,15 0,15 z" Fill="#FF727272" Margin="0,0,1,1" Stretch="Fill" />
                                                                <Path Data="F1M187.5,506L187.5,503.5C187.5,502.396,188.396,501.5,189.5,501.5C190.604,501.5,191.5,502.396,191.5,503.5L191.5,506" Fill="Transparent" Stretch="Fill" Width="4" HorizontalAlignment="Right" Margin="0,6.5,1.5,5"/>
                                                                <Path Data="M3.4999999,0.99999994 C2.6730041,1 1.9999999,1.6730042 1.9999999,2.5 L1.9999999,4 4.9999999,4 4.9999999,2.5 C4.9999999,1.6730042 4.3269958,1 3.4999999,0.99999994 z M3.4999999,0 C4.8789977,0 5.9999999,1.1210022 5.9999999,2.5 L5.9999999,4 6.9999999,4 6.9999999,10 0,10 0,4 0.99999994,4 0.99999994,2.5 C0.99999994,1.1210022 2.1210021,0 3.4999999,0 z" Fill="#FFEBC689" HorizontalAlignment="Right" Margin="0,6,0,0" Stretch="Fill" Width="7"/>
                                                                <Path Data="M0,13 L1,13 1,14 0,14 z M5,12 L6,12 6,13 7,13 7,14 6,14 5,14 z M4,3.0000001 L4,7.0000001 7,7.0000001 7,3.0000001 z M3,0 L4,0 4,2.0000001 7,2.0000001 7,2.0861626E-07 8,2.0861626E-07 8,2.0000001 11,2.0000001 11,0 12,0 12,2.0000001 14,2.0000001 14,3.0000001 12,3.0000001 12,4.0000001 11,4.0000001 11,3.0000001 8,3.0000001 8,7.0000001 9,7.0000001 9,8.0000001 8,8.0000001 8,11 7,11&#xd;&#xa;7,8.0000001 4,8.0000001 4,11 3,11 3,8.0000001 1,8.0000001 1,7.0000001 3,7.0000001 3,3.0000001 1,3.0000001 1,2.0000001 3,2.0000001 z" Fill="#FFB0AEB0" Margin="0,1,2,1" Stretch="Fill"/>
                                                                <Path Data="M0.75,0 C1.164,0 1.5,0.336 1.5,0.75000001 1.5,0.9699375 1.4051719,1.1678613 1.2541983,1.3050888 L1.25,1.3087258 1.25,3.75 0.24999997,3.75 0.24999997,1.3087258 0.2458017,1.3050888 C0.094828129,1.1678613 0,0.9699375 0,0.75000001 0,0.336 0.336,0 0.75,0 z" Fill="Transparent" HorizontalAlignment="Right" Height="3.75" Margin="0,0,2.75,1" Stretch="Fill" VerticalAlignment="Bottom" Width="1.5"/>
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>
                                                    <tools:RibbonButton x:Name="LockCellToggleButton"
                                                                     SizeForm="Small" 
                                                                     IsToggle="True"
                                                                     Padding="4,0,0,0"
                                                                     Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LockCell}"
                                                                     IconTemplate="{StaticResource LockCellIcon}"
                                                                     Command="{Binding Commands.LockCell}"
                                                                     Style="{StaticResource SyncfusionSpreadsheetRibbonButtonStyle}">
                                                        <tools:RibbonButton.ToolTip>
                                                            <ToolTip>
                                                                <StackPanel>
                                                                    <TextBlock Padding="4" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LockCellToolTip}" MaxWidth="150" TextWrapping="Wrap"/>
                                                                </StackPanel>
                                                            </ToolTip>
                                                        </tools:RibbonButton.ToolTip>
                                                    </tools:RibbonButton>

                                                    <sharedt:MenuItemSeparator/>

                                                    <tools:RibbonMenuItem x:Name="FormatCells" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_FormatCells}"
                                                                       Command="{Binding Commands.FormatCells}" IconSize="16,16"
                                                                       CommandParameter="0"
                                                                       Padding="2" IconBarEnabled="True">
                                                        <tools:RibbonMenuItem.Icon>
                                                            <Grid x:Name="FormatCellsIcon" Height="16" Width="14">
                                                                <Path Data="M0,0 L14,0 14,2.9999999 0,2.9999999 z" Fill="#FF4A7DB1" Height="3" Stretch="Fill" VerticalAlignment="Top" />
                                                                <Path Data="M0,0 L13,0 13,12.5 0,12.5 z" Fill="Transparent" Margin="0.5,3,0.5,0.5" Stretch="Fill" />
                                                                <Path Data="M4,9.0000001 L4.9999999,9.0000001 4.9999999,10 4,10 z M7,7.9999999 L11,7.9999999 11,9.0000001 7,9.0000001 z M4.9999999,7.9999999 L6,7.9999999 6,9.0000001 4.9999999,9.0000001 z M3,7.9999999 L4,7.9999999 4,9.0000001 3,9.0000001 z M4,7 L4.9999999,7 4.9999999,7.9999999 4,7.9999999 z M7,3 L11,3 11,4 7,4 z M4,2 L4.9999999,2 4.9999999,3 6,3 6,4 4.9999999,4 4.9999999,5 4,5 4,4 3,4 3,3 4,3 z M0,0 L1,0 1,12 13,12 13,0 14,0 14,13 0,13 z" Fill="{StaticResource IconColor}" Margin="0,3,0,0" Stretch="Fill"/>
                                                            </Grid>
                                                        </tools:RibbonMenuItem.Icon>
                                                    </tools:RibbonMenuItem>
                                                </tools:RibbonMenuGroup>
                                            </tools:DropDownButton>
                                        </StackPanel>

                                    </tools:DropDownButton>
                                </tools:RibbonBar>

                                <!--Find and Replace-->
                                <tools:RibbonBar Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Editing}" 
														IconTemplate="{StaticResource CollapseEditingIcon}"
                                                      IsLauncherButtonVisible="False"
                                                      IsLargeButtonPanel="True">
                                    <tools:DropDownButton x:Name="FindAndSelect" 
                                                               Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_RibbonFindAndSelect}"
                                                               IconTemplate="{StaticResource FindAndSelectIcon}"
                                                               SizeForm="Large">
                                        <tools:DropDownButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_RibbonFindAndSelect}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FindAndSelectToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:DropDownButton.ToolTip>
                                        <tools:RibbonMenuGroup FontSize="11.5"
                                                                    IconBarEnabled="True">
                                            <tools:RibbonMenuItem x:Name="Find" Command="{Binding Commands.FindAndReplace}"
                                                                       CommandParameter="Find"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_RibbonFind}"
                                                                       IconBarEnabled="True">
                                                <tools:RibbonMenuItem.ToolTip>
                                                    <ToolTip>
                                                        <StackPanel>
                                                            <TextBlock Padding="4" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FindToolTip}" MaxWidth="150" TextWrapping="Wrap"/>
                                                        </StackPanel>
                                                    </ToolTip>
                                                </tools:RibbonMenuItem.ToolTip>
                                                <tools:RibbonMenuItem.Icon>
                                                    <Grid x:Name="FindIcon" Height="12" Width="12">
                                                        <Path Data="M4,0 C6.2089996,0 8,1.7910004 8,4 8,6.2089996 6.2089996,8 4,8 1.7910004,8 0,6.2089996 0,4 0,1.7910004 1.7910004,0 4,0 z" Fill="Transparent" Margin="3.5,0.5,0.5,3.5" Stretch="Fill" />
                                                        <Path Data="M7.5000001,1 C5.5700003,1 4.0000001,2.5700002 4.0000001,4.5 4.0000001,6.43 5.5700003,8 7.5000001,8 9.4300004,8 11,6.43 11,4.5 11,2.5700002 9.4300004,1 7.5000001,1 z M7.5000001,0 C9.9810001,0 12,2.0190001 12,4.5 12,6.981 9.9810001,9 7.5000001,9 6.4533282,9 5.4888827,8.6406614 4.7235695,8.038891 L4.6934081,8.014587 0.85416841,11.854004 C0.75617087,11.951 0.6281926,12.000001 0.50019908,12.000001 0.37220564,12.000001 0.24422742,11.951 0.14622971,11.854004 -0.048743237,11.658015 -0.048743237,11.342037 0.14622971,11.146048 L3.9854518,7.3066402 3.9611093,7.2764306 C3.3593386,6.5111177 3.0000001,5.5466719 3.0000001,4.5 3.0000001,2.0190001 5.0190002,0 7.5000001,0 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                    </Grid>
                                                </tools:RibbonMenuItem.Icon>
                                            </tools:RibbonMenuItem>
                                            <tools:RibbonMenuItem x:Name="Replace" Command="{Binding Commands.FindAndReplace}"
                                                                       CommandParameter="Replace"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_RibbonReplace}" 
                                                                       IconBarEnabled="True">
                                                <tools:RibbonMenuItem.ToolTip>
                                                    <ToolTip>
                                                        <StackPanel>
                                                            <TextBlock Padding="4" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ReplaceToolTip}" MaxWidth="150" TextWrapping="Wrap"/>
                                                        </StackPanel>
                                                    </ToolTip>
                                                </tools:RibbonMenuItem.ToolTip>
                                                <tools:RibbonMenuItem.Icon>
                                                    <Grid x:Name="ReplaceIcon" Height="16" Width="15.396">
                                                        <Path Data="M2.9869998,2.8119999 C2.3950046,2.8119999 1.918999,3.0179974 1.5579989,3.4299999 1.1989977,3.8429981 1.0189974,4.3609995 1.0189973,4.9829973 L1.0189973,5.874 C1.0189974,6.3999971 1.1890032,6.8469995 1.5309984,7.2130002 1.8730014,7.5810002 2.3060001,7.7639989 2.8310016,7.7639989 3.4479986,7.7639989 3.9310003,7.5279988 4.2819985,7.0549992 4.6310051,6.5829996 4.8060006,5.9239992 4.8060006,5.0829996 4.8060006,4.3730005 4.6419991,3.8180004 4.3160027,3.4150006 3.988999,3.0130001 3.5449987,2.8119999 2.9869998,2.8119999 z M0,0 L1.0189973,0 1.0189973,3.2219998 1.0429995,3.2219998 C1.5439989,2.3769985 2.2770007,1.9539983 3.242005,1.9539983 4.0560001,1.9539983 4.6959999,2.2389981 5.1569985,2.8089978 5.6190041,3.3789974 5.8489998,4.1429971 5.8489998,5.1009973 5.8489998,6.1679987 5.5900048,7.0199994 5.0739983,7.6619977 4.5550011,8.3019971 3.8480001,8.623 2.9500049,8.623 2.1090015,8.623 1.4739992,8.2659978 1.0429995,7.5529965 L1.0189973,7.5529965 1.0189973,8.4729985 0,8.4729985 z" Fill="#FF9868B9" Margin="6.824,0,2.723,7.377" Stretch="Fill" />
                                                        <Path Data="M8.5819995,10.674995 L7.0869961,10.881995 C6.6279963,10.945998 6.280996,11.060996 6.0469947,11.223998 5.813001,11.388 5.6959966,11.678 5.6959966,12.093993 5.6959966,12.396995 5.8039984,12.644996 6.0189948,12.835998 6.2349983,13.028998 6.522001,13.124 6.8820019,13.124 7.3739989,13.124 7.7799973,12.950995 8.1009965,12.606994 8.4219959,12.259994 8.5819995,11.822997 8.5819995,11.293998 z M7.3899977,7.4629974 C8.8459997,7.4629973 9.5729976,8.2350004 9.5729976,9.7789989 L9.5729976,13.815993 8.5819995,13.815993 8.5819995,12.846 8.5579975,12.846 C8.125998,13.588996 7.4899962,13.960997 6.6519983,13.960997 6.0350013,13.960997 5.5519993,13.796996 5.2030002,13.471 4.8529939,13.142997 4.6799973,12.707999 4.6799973,12.165999 4.6799973,11.006995 5.3609974,10.332 6.7249963,10.141998 L8.5819995,9.8809962 C8.5819995,8.8269955 8.1559968,8.2999952 7.3059981,8.2999951 6.5599954,8.2999952 5.8859992,8.5540006 5.2839938,9.0629956 L5.2839938,8.044998 C5.8939948,7.6559981 6.5949991,7.4629973 7.3899977,7.4629974 z M3.9019961,3.2130013 L2.4079976,3.4190016 C1.947998,3.4840016 1.6009984,3.5980015 1.3679986,3.7620018 1.1329987,3.9260018 1.0159988,4.216002 1.0159988,4.6320021 1.0159988,4.9350023 1.1249988,5.1820023 1.3389986,5.3740025 1.5549984,5.5660026 1.8419981,5.6620027 2.2019978,5.6620027 2.6939973,5.6620027 3.0999968,5.4890026 3.4209965,5.1440024 3.7419962,4.7980022 3.9019961,4.360002 3.9019961,3.8320017 z M2.7099972,0 C4.1659958,0 4.8939951,0.77300024 4.8939951,2.3170009 L4.8939951,6.353003 3.9019961,6.353003 3.9019961,5.3830025 3.8779961,5.3830025 C3.4459965,6.1270028 2.8099971,6.4990031 1.971998,6.4990031 1.3549986,6.4990031 0.87199903,6.335003 0.52299929,6.0080029 0.17399979,5.6810026 0,5.2460024 0,4.7040021 0,3.5440016 0.68099928,2.8690014 2.0449979,2.6800013 L3.9019961,2.4190011 C3.9019961,1.3640003 3.4759965,0.83700037 2.6259973,0.83700037 1.879998,0.83700037 1.2059987,1.0920005 0.60499907,1.6010008 L0.60499907,0.58300018 C1.2139988,0.19399977 1.915998,0 2.7099972,0 z" Fill="{StaticResource IconColor}" Margin="0,2.039,5.823,0" Stretch="Fill"/>
                                                        <Path Data="M2,1.6830002 L2.9999995,1.6830002 2.9999995,2.6830004 3.9999995,2.6830004 3.9999995,3.6830004 2.9999995,3.6830004 2.9999995,4.6830004 2,4.6830004 2,3.6830004 1,3.6830004 1,2.6830004 2,2.6830004 z M0,0.68300032 L1,0.68300032 1,2.6830004 0,2.6830004 z M13.915006,0 C14.475004,5.0620599E-08 14.969007,0.10300441 15.396001,0.31200385 L15.396001,1.3589998 C14.923002,1.026 14.417005,0.85999983 13.877004,0.85999971 13.227003,0.85999983 12.693006,1.0940008 12.276006,1.5620028 11.859007,2.0289975 11.651006,2.6430029 11.651006,3.4029967 11.651006,4.1519965 11.846006,4.742 12.238004,5.1749982 12.630002,5.6059974 13.155004,5.8230003 13.816007,5.8230003 14.371,5.8230003 14.894002,5.6369957 15.383001,5.2680005 L15.383001,6.2409991 C14.894002,6.5359968 14.314001,6.683 13.641004,6.683 12.733007,6.683 12.000005,6.3869952 11.442007,5.7950004 10.884,5.202998 10.605001,4.4350008 10.605001,3.4910017 10.605001,2.4390007 10.905004,1.5949998 11.507001,0.95699983 12.109006,0.319 12.911001,5.0620599E-08 13.915006,0 z" Fill="#FF4A7DB1" Height="6.683" Stretch="Fill" VerticalAlignment="Bottom"/>
                                                    </Grid>
                                                </tools:RibbonMenuItem.Icon>
                                            </tools:RibbonMenuItem>
                                            <tools:RibbonMenuItem x:Name="GoTo" Command="{Binding Commands.FindAndReplace}"
                                                                       CommandParameter="GoTo"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_RibbonGoTo}"
                                                                       IconBarEnabled="True">
                                                <tools:RibbonMenuItem.ToolTip>
                                                    <ToolTip>
                                                        <StackPanel>
                                                            <TextBlock Padding="4" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=GoToToolTip}" MaxWidth="150" TextWrapping="Wrap"/>
                                                        </StackPanel>
                                                    </ToolTip>
                                                </tools:RibbonMenuItem.ToolTip>
                                                <tools:RibbonMenuItem.Icon>
                                                    <Grid x:Name="GoToIcon">
                                                        <Path Data="M5,0 L8,0 8,1.0000001 9,1.0000001 9,2 10,2 10,3 11,3 11,5 10,5 10,6 9,6 9,7 8,7 8,8 5,8 5,7 6,7 6,6 7,6 7,5 0,5 0,3 7,3&#xd;&#xa;7,2 6,2 6,1.0000001 5,1.0000001 z" Fill="#FF4A7DB1" Height="8" Stretch="Fill" Width="11" />
                                                    </Grid>
                                                </tools:RibbonMenuItem.Icon>
                                            </tools:RibbonMenuItem>
                                            <Separator Margin="33,0,0,0" />
                                            <tools:RibbonMenuItem x:Name="Fomrulas" Command="{Binding Commands.FindAndReplace}"
                                                                       CommandParameter="Formulas"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_RibbonFormulas}"
                                                                       IconBarEnabled="True">
                                                <tools:RibbonMenuItem.ToolTip>
                                                    <ToolTip>
                                                        <StackPanel>
                                                            <TextBlock Padding="4" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=GoToFormulasToolTip}" MaxWidth="150" TextWrapping="Wrap"/>
                                                        </StackPanel>
                                                    </ToolTip>
                                                </tools:RibbonMenuItem.ToolTip>
                                            </tools:RibbonMenuItem>
                                            <tools:RibbonMenuItem x:Name="Comments" Command="{Binding Commands.FindAndReplace}" Visibility="Collapsed"
                                                                       CommandParameter="Comments"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_RibbonComments}" 
                                                                       IconBarEnabled="True">
                                            </tools:RibbonMenuItem>
                                            <tools:RibbonMenuItem x:Name="ConditionalFormatting" Command="{Binding Commands.FindAndReplace}"
                                                                       CommandParameter="ConditionalFormatting"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ConditionalFormatting}"
                                                                       IconBarEnabled="True">
                                                <tools:RibbonMenuItem.ToolTip>
                                                    <ToolTip>
                                                        <StackPanel>
                                                            <TextBlock Padding="4" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=GoToConditionalFormattingToolTip}" MaxWidth="150" TextWrapping="Wrap"/>
                                                        </StackPanel>
                                                    </ToolTip>
                                                </tools:RibbonMenuItem.ToolTip>
                                            </tools:RibbonMenuItem>
                                            <tools:RibbonMenuItem x:Name="Constants" Command="{Binding Commands.FindAndReplace}"
                                                                       CommandParameter="Constants"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_RibbonConstants}"
                                                                       IconBarEnabled="True">
                                                <tools:RibbonMenuItem.ToolTip>
                                                    <ToolTip>
                                                        <StackPanel>
                                                            <TextBlock Padding="4" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=GoToConstantsToolTip}" MaxWidth="150" TextWrapping="Wrap"/>
                                                        </StackPanel>
                                                    </ToolTip>
                                                </tools:RibbonMenuItem.ToolTip>
                                            </tools:RibbonMenuItem>
                                            <tools:RibbonMenuItem x:Name="DataValidation" Command="{Binding Commands.FindAndReplace}"
                                                                       CommandParameter="DataValidation"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DataValidation}" 
                                                                       IconBarEnabled="True">
                                                <tools:RibbonMenuItem.ToolTip>
                                                    <ToolTip>
                                                        <StackPanel>
                                                            <TextBlock Padding="4" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=GoToDataValidationToolTip}" MaxWidth="150" TextWrapping="Wrap"/>
                                                        </StackPanel>
                                                    </ToolTip>
                                                </tools:RibbonMenuItem.ToolTip>
                                            </tools:RibbonMenuItem>
                                        </tools:RibbonMenuGroup>
                                    </tools:DropDownButton>
                                    <tools:DropDownButton x:Name="Fill" 
                                                                Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FillSeries}"
                                                                IconTemplate="{StaticResource FillSeriesIcon}"
                                                               SizeForm="Large">
                                        <tools:DropDownButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FillSeries}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FillToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:DropDownButton.ToolTip>
                                        <tools:RibbonMenuItem x:Name="Down"
                                                                       Command="{Binding Commands.Fill}"
                                                                       CommandParameter="Down"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Fill_Down}"
                                                                       IconBarEnabled="True">
                                            <tools:RibbonMenuItem.Icon>
                                                <Grid Height="14" Width="16">
                                                    <Path Data="M1,2.9990001 L1,13 15,13 15,2.9990001 z M1,1 L1,1.9990001 15,1.9990001 15,1 z M0,0 L16,0 16,14 0,14 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                    <Path Data="M2.8550017,0 L3.8550017,0 3.8550017,5.8149195 6.0110009,3.7060032 6.7100008,4.4210029 3.3550004,7.7020011 0,4.4210029 0.69900012,3.7060032 2.8550017,5.8149219 z" Fill="{StaticResource IconColor}" Margin="4.145,3.999,5.145,2.299" Stretch="Fill"/>
                                                </Grid>
                                            </tools:RibbonMenuItem.Icon>
                                        </tools:RibbonMenuItem>
                                        <tools:RibbonMenuItem x:Name="Right"
                                                                       Command="{Binding Commands.Fill}"
                                                                       CommandParameter="Right"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Fill_Right}"
                                                                       IconBarEnabled="True">
                                            <tools:RibbonMenuItem.Icon>
                                                <Grid Height="16" Width="14">
                                                    <Path Data="M2.9999985,1 L2.9999985,15 13,15 13,1 z M1.0000001,1 L1.0000001,15 1.9999984,15 1.9999984,1 z M0,0 L14,0 14,16 0,16 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                    <Path Data="M4.4199996,0 L7.7009978,3.3550005 4.4199996,6.710001 3.7049999,6.0110009 5.8139172,3.855001 0,3.855001 0,2.8550009 5.8139181,2.8550009 3.7049999,0.69900012 z" Fill="{StaticResource IconColor}" Margin="4,4.145,2.299,5.145" Stretch="Fill"/>
                                                </Grid>
                                            </tools:RibbonMenuItem.Icon>
                                        </tools:RibbonMenuItem>
                                        <tools:RibbonMenuItem x:Name="Up"
                                                                       Command="{Binding Commands.Fill}"
                                                                       CommandParameter="Up"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Fill_Up}"
                                                                       IconBarEnabled="True">
                                            <tools:RibbonMenuItem.Icon>
                                                <Grid Height="14" Width="16">
                                                    <Path Data="M1,2.9989924 L1,13 15,13 15,2.9989924 z M1,1 L1,1.9989924 15,1.9989924 15,1 z M0,0 L16,0 16,14 0,14 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                    <Path Data="M3.3549968,0 L6.7099934,3.2809981 6.0109942,3.9959978 3.8549967,1.8870807 3.8549967,7.9989978 2.8549966,7.9989978 2.8549966,1.887081 0.6989994,3.9959978 0,3.2809981 z" Fill="{StaticResource IconColor}" Margin="4.145,4,5.145,2.001" Stretch="Fill"/>
                                                </Grid>
                                            </tools:RibbonMenuItem.Icon>
                                        </tools:RibbonMenuItem>
                                        <tools:RibbonMenuItem x:Name="Left"
                                                                       Command="{Binding Commands.Fill}"
                                                                       CommandParameter="Left"
                                                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Fill_Left}"
                                                                       IconBarEnabled="True">
                                            <tools:RibbonMenuItem.Icon>
                                                <Grid Height="16" Width="14">
                                                    <Path Data="M1,1.0000074 L1,15.000008 13,15.000008 13,1.0000074 11.999998,1.0000074 11.999998,15 10.999998,15 10.999998,1.0000074 z M10.999998,0 L11.999998,0 14,7.3909796E-06 14,16.000008 0,16.000008 0,7.3909796E-06 z" Fill="{StaticResource IconColor}" Stretch="Fill"/>
                                                    <Path Data="M3.2809983,0 L3.9959977,0.69899917 1.8861058,2.8559933 7.7010003,2.8559933 7.7010003,3.8559933 1.8880556,3.8559933 3.9959977,6.010994 3.2809983,6.7099934 0,3.3549966 z" Fill="{StaticResource IconColor}" Margin="2.299,4.144,4,5.146" Stretch="Fill"/>
                                                </Grid>
                                            </tools:RibbonMenuItem.Icon>
                                        </tools:RibbonMenuItem>
                                    </tools:DropDownButton>
                                </tools:RibbonBar>
                            </tools:RibbonTab>
                            <!--Data Tab-->
                            <tools:RibbonTab Caption="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Data}">
                                <tools:RibbonBar IsLauncherButtonVisible="False" IconTemplate="{StaticResource CollapseHyperlinkIcon}"
                                                      Margin="4,2,0,0" Width="80" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Links}">
                                    <tools:RibbonButton HorizontalAlignment="Center" Margin="10,0,0,0" Command="{Binding Path=Commands.InsertHyperlink}"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Hyperlinks}"
                                                             IconTemplate="{StaticResource HyperlinkIcon}"
                                                             SizeForm="Large">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Hyperlinks}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HyperLinksToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                </tools:RibbonBar>

                                <!--<Syncfusion:RibbonBar HorizontalAlignment="Center" Visibility="Collapsed"
                                                      VerticalAlignment="Center"
                                                      Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Illustrations}"
                                                      IsLauncherButtonVisible="False">
                                    <Syncfusion:RibbonButton Margin="12,0,0,0" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center"
                                                             IsToggle="False"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Picture}"
                                                             LargeIcon="{StaticResource PictureIcon}"
                                                             SizeForm="Large" />
                                </Syncfusion:RibbonBar>-->

                                <tools:RibbonBar IsLauncherButtonVisible="True" IconTemplate="{StaticResource CollapseOutlineIcon}"
                                                      tools:RibbonBar.LauncherCommand="{Binding Commands.OutlineSettings}"
                                                      Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Outline}">
                                    <tools:RibbonBar.ToolTip>
                                        <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Outline}">
                                            <StackPanel>
                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=OutlineToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                            </StackPanel>
                                        </tools:ScreenTip>
                                    </tools:RibbonBar.ToolTip>
                                    <tools:RibbonButton Margin="3" Command="{Binding Commands.Group}"
                                                             IsToggle="False"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Group}"
                                                             IconTemplate="{StaticResource LargeGroupIcon}"
                                                             SizeForm="Large">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Group}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=GroupToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                    <tools:RibbonButton Margin="3" Command="{Binding Commands.Ungroup}"
                                                             IsToggle="False"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ungroup}"
                                                             IconTemplate="{StaticResource LargeUngroupIcon}"
                                                             SizeForm="Large">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ungroup}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UngroupToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                </tools:RibbonBar>

                                <tools:RibbonBar IsLauncherButtonVisible="False" 
														IconTemplate="{StaticResource CollapseDataToolsIcon}"
                                                      Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Datatools}">
                                    <tools:RibbonButton Command="{Binding Commands.DataValidation}"
                                                             IsToggle="False" Width="65" Height="68"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DataValidation}"
                                                             IconTemplate="{StaticResource DataValidationIcon}"
                                                             SizeForm="Large">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DataValidation}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DataValidationToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                </tools:RibbonBar>

                                <tools:RibbonBar HorizontalAlignment="Center"
                                                      VerticalAlignment="Center" 
													IconTemplate="{StaticResource CollapseFormulasIcon}"
                                                      Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Formulas}"
                                                      IsLauncherButtonVisible="False">
                                    <tools:RibbonButton Margin="0,0,4,0"
                                                             HorizontalAlignment="Center"
                                                             VerticalAlignment="Center"
                                                             Command="{Binding Path=Commands.NameManager}"
                                                             IsToggle="False"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NameManager}"
                                                             IconTemplate="{StaticResource NameManagerIcon}"
                                                             SizeForm="Large"
                                                             Visibility="Visible">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NameManager}">
                                                <StackPanel>
                                                    <TextBlock  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NameManagerToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>

                                </tools:RibbonBar>
                                <tools:RibbonBar Name="SortAndFilterBar"
														IconTemplate="{StaticResource CollapseFilteringIcon}"
                                                      Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=SortAndFilter}"
                                                      IsLauncherButtonVisible="False"
                                                      IsLargeButtonPanel="True">
                                    <tools:RibbonButton x:Name="FilterButton" 
                                                             Margin="12,4,0,0"
                                                             Command="{Binding Commands.Filter}"
                                                             IsToggle="False"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Filter}"
                                                             IconTemplate="{StaticResource DataFilteringIcon}"
                                                             SizeForm="Large">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Filter}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FilterToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                </tools:RibbonBar>
                            </tools:RibbonTab>
                            <!--View Tab-->
                            <tools:RibbonTab x:Name="ViewTab" Caption="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=View}">

                                <tools:RibbonBar IsLauncherButtonVisible="False"
														IconTemplate="{StaticResource CollapseCommentsIcon}"
                                                      Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Comments}">
                                    <tools:RibbonButton x:Name="Comments_NewComment" Command="{Binding Commands.NewComment}"
                                                             IsToggle="False"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NewComment}"
                                                             IconTemplate="{StaticResource NewCommentIcon}"
                                                             SizeForm="Large">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NewComment}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NewCommentToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                    <tools:RibbonButton x:Name="Comments_EditComment" Command="{Binding Commands.EditComment}"
                                                             IsToggle="False"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=EditComment}"
                                                             IconTemplate="{StaticResource EditCommentIcon}"
                                                             SizeForm="Large">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=EditComment}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=EditCommentToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                    <tools:RibbonButton x:Name="Comments_DeleteComment" Command="{Binding Commands.DeleteComment}"
                                                             IsToggle="False"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DeleteComment}"
                                                             IconTemplate="{StaticResource DeleteCommentIcon}"
                                                             SizeForm="Large">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DeleteComment}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DeleteCommentToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                </tools:RibbonBar>

                                <tools:RibbonBar IconTemplate="{StaticResource CollapseShowIcon}"
														Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Show}"
                                                      IsLauncherButtonVisible="False">
                                    <StackPanel Orientation="Vertical">
                                        <tools:RibbonCheckBox x:Name="View_ShowGridLines" Margin="5,0,5,3" Command="{Binding Commands.ShowGridLines}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Gridlines}" IsChecked="{Binding Path=ActiveSheet.IsGridLinesVisible, Mode=OneWay}">
                                            <tools:RibbonCheckBox.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Gridlines}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=GridlinesToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonCheckBox.ToolTip>
                                        </tools:RibbonCheckBox>
                                        <tools:RibbonCheckBox x:Name="View_ShowFormulaBar" Margin="5,0,5,0" Command="{Binding Commands.ShowFormulaBar}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormulaBar}" 
                                                                   IsChecked="{Binding Path=FormulaBarVisibility, Mode=TwoWay, Converter={StaticResource VisibilityToBoolConverter}}">
                                            <tools:RibbonCheckBox.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormulaBar}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormulaBarToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonCheckBox.ToolTip>
                                        </tools:RibbonCheckBox>
                                        <tools:RibbonCheckBox x:Name="View_ShowHeadings" Margin="5,1,5,0" Command="{Binding Commands.ShowHeadings}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Headings}" IsChecked="{Binding Path=ActiveSheet.IsRowColumnHeadersVisible, Mode=OneWay}">
                                            <tools:RibbonCheckBox.ToolTip>
                                                <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Headings}">
                                                    <StackPanel>
                                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ShowHeadingsToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </tools:ScreenTip>
                                            </tools:RibbonCheckBox.ToolTip>
                                        </tools:RibbonCheckBox>
                                    </StackPanel>
                                </tools:RibbonBar>

                                <tools:RibbonBar IconTemplate="{StaticResource CollapseWindowIcon}"
														Visibility="Visible" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Window}"
                                                      IsLauncherButtonVisible="False">
                                    <tools:DropDownButton Margin="10,0,0,0" x:Name="Window_FreezePanes"
                                                               Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FreezePanes}"
                                                               IconTemplate="{StaticResource FreezePanesIcon}"
                                                               SizeForm="Large">
                                        <tools:DropDownButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FreezePanes}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FreezePanesToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:DropDownButton.ToolTip>
                                        <tools:RibbonMenuGroup Width="310" IconBarEnabled="True">
                                            <tools:SimpleMenuButton x:Name="FreezePanes" Height="65" Width="310"
                                                                         Command="{Binding Commands.FreezePanes}"
                                                                    CommandParameter="FreezePanes"
                                                                    IconTemplate="{StaticResource FreezePanesIcon}"     
                                                                    Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FreezePanes}" IconSize="32,32"
                                                                    Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FreezePanesDescription}"/>
                                            <tools:SimpleMenuButton x:Name="UnFreezePanes" Height="65" Width="310"
                                                                         Command="{Binding Commands.UnFreezePanes}"
                                                                    CommandParameter="UnFreezePanes"
                                                                    IconTemplate="{StaticResource FreezePanesIcon}"     
                                                                    Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnfreezePanes}" IconSize="32,32"
                                                                    Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnfreezePanesDescription}"/>
                                            <tools:SimpleMenuButton x:Name="FreezeTopRow" Height="65" Width="310"
                                                                         Command="{Binding Commands.FreezeTopRow}"
                                                                    CommandParameter="FreezeTopRow"
                                                                    IconTemplate="{StaticResource FreezeTopRowIcon}"     
                                                                    Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FreezeTopRow}" IconSize="32,32"
                                                                    Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FreezeTopRowDescription}"/>
                                            <tools:SimpleMenuButton x:Name="FreezeFirstColumn" Height="65" Width="310"
                                                                         Command="{Binding Commands.FreezeFirstColumn}"
                                                                    CommandParameter="FreezeFirstColumn"
                                                                    IconTemplate="{StaticResource FreezeFirstColumnIcon}"     
                                                                    Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FreezeFirstColumn}" IconSize="32,32"
                                                                    Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FreezeFirstColumnDescription}"/>
                                        </tools:RibbonMenuGroup>
                                    </tools:DropDownButton>
                                </tools:RibbonBar>

                                <tools:RibbonBar IconTemplate="{StaticResource CollapseSheetIcon}"
														Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Sheet}"
                                                      IsLauncherButtonVisible="False">
                                    <tools:RibbonButton x:Name="Changes_ProtectSheet" Width="60"
                                                             Command="{Binding Commands.ProtectSheet}"
                                                             IsToggle="False"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectSheetLarge}"
                                                             IconTemplate="{StaticResource ProtectSheetLargeIcon}"
                                                             SizeForm="Large">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectSheetLarge}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectWorksheetToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                    <tools:RibbonButton x:Name="Changes_UnProtectSheet" Width="60"
                                                             Command="{Binding Commands.ProtectSheet}"
                                                             IsToggle="False"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnProtectSheet}"
                                                             IconTemplate="{StaticResource ProtectSheetLargeIcon}"
                                                             SizeForm="Large">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnProtectSheet}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectWorksheetToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>
                                    <tools:RibbonButton x:Name="Changes_ProtectWorkbook" Width="60"
                                                             Command="{Binding Commands.ProtectWorkbook}"
                                                             Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectWorkbook}"
                                                             IconTemplate="{StaticResource ProtectWorkbookIcon}"
                                                             SizeForm="Large">
                                        <tools:RibbonButton.ToolTip>
                                            <tools:ScreenTip Description="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectWorkbook}">
                                                <StackPanel>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectWorkBookToolTip}" MaxWidth="300" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </tools:ScreenTip>
                                        </tools:RibbonButton.ToolTip>
                                    </tools:RibbonButton>

                                </tools:RibbonBar>
                            </tools:RibbonTab>
                        </tools:Ribbon>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
	<Style BasedOn="{StaticResource SyncfusionSfSpreadsheetRibbonStyle}" TargetType="spreadsheet:SfSpreadsheetRibbon"/>
</ResourceDictionary>
    
