using System.Text.Json.Serialization;
using AirMonitor.Core.Enums;

namespace AirMonitor.Core.Models;

/// <summary>
/// 许可证信息模型
/// 主程序和注册机共享的许可证数据结构
/// </summary>
public class LicenseInfo
{
    /// <summary>
    /// 许可证唯一标识
    /// </summary>
    [JsonPropertyName("licenseId")]
    public string LicenseId { get; set; } = string.Empty;

    /// <summary>
    /// 产品名称
    /// </summary>
    [JsonPropertyName("productName")]
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// 产品版本
    /// </summary>
    [JsonPropertyName("productVersion")]
    public string ProductVersion { get; set; } = string.Empty;

    /// <summary>
    /// 客户名称（部门名称）
    /// </summary>
    [JsonPropertyName("customerName")]
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// 客户邮箱（部门邮箱）
    /// </summary>
    [JsonPropertyName("customerEmail")]
    public string CustomerEmail { get; set; } = string.Empty;

    /// <summary>
    /// 许可证类型
    /// </summary>
    [JsonPropertyName("licenseType")]
    public LicenseType LicenseType { get; set; }

    /// <summary>
    /// 授权功能列表
    /// </summary>
    [JsonPropertyName("authorizedFeatures")]
    public List<string> AuthorizedFeatures { get; set; } = new();

    /// <summary>
    /// 硬件指纹
    /// </summary>
    [JsonPropertyName("hardwareFingerprint")]
    public string HardwareFingerprint { get; set; } = string.Empty;

    /// <summary>
    /// 签发日期
    /// </summary>
    [JsonPropertyName("issuedDate")]
    public DateTime IssuedDate { get; set; }

    /// <summary>
    /// 过期日期
    /// </summary>
    [JsonPropertyName("expiryDate")]
    public DateTime ExpiryDate { get; set; }

    /// <summary>
    /// 最大设备数量（-1表示无限制）
    /// </summary>
    [JsonPropertyName("maxDeviceCount")]
    public int MaxDeviceCount { get; set; } = -1;

    /// <summary>
    /// 是否为试用版
    /// </summary>
    [JsonPropertyName("isTrial")]
    public bool IsTrial { get; set; } = false;

    /// <summary>
    /// 试用天数
    /// </summary>
    [JsonPropertyName("trialDays")]
    public int TrialDays { get; set; } = 0;

    /// <summary>
    /// 数字签名
    /// </summary>
    [JsonPropertyName("signature")]
    public string Signature { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 更新时间
    /// </summary>
    [JsonPropertyName("updatedAt")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 检查许可证是否有效
    /// </summary>
    /// <returns>true表示有效，false表示无效</returns>
    public bool IsValid()
    {
        var now = DateTime.UtcNow;
        
        // 检查是否已过期
        if (ExpiryDate != DateTime.MaxValue && now > ExpiryDate)
        {
            return false;
        }

        // 检查试用期
        if (IsTrial && TrialDays > 0)
        {
            var trialEndDate = IssuedDate.AddDays(TrialDays);
            if (now > trialEndDate)
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 检查是否为永久许可证
    /// </summary>
    /// <returns>true表示永久，false表示有期限</returns>
    public bool IsPermanent()
    {
        return ExpiryDate == DateTime.MaxValue;
    }

    /// <summary>
    /// 获取剩余天数
    /// </summary>
    /// <returns>剩余天数，-1表示永久</returns>
    public int GetRemainingDays()
    {
        if (IsPermanent())
        {
            return -1;
        }

        var now = DateTime.UtcNow;
        var remaining = (ExpiryDate - now).Days;
        return Math.Max(0, remaining);
    }

    /// <summary>
    /// 检查是否授权指定功能
    /// </summary>
    /// <param name="featureCode">功能代码</param>
    /// <returns>true表示已授权，false表示未授权</returns>
    public bool IsFeatureAuthorized(string featureCode)
    {
        return AuthorizedFeatures.Contains(featureCode, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 检查是否授权指定功能
    /// </summary>
    /// <param name="featureCode">功能代码枚举</param>
    /// <returns>true表示已授权，false表示未授权</returns>
    public bool IsFeatureAuthorized(FeatureCode featureCode)
    {
        return IsFeatureAuthorized(featureCode.ToString());
    }
}
