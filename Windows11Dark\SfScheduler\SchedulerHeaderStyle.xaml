<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib"
					xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:scheduler="clr-namespace:Syncfusion.UI.Xaml.Scheduler;assembly=Syncfusion.SfScheduler.WPF"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF"
                    xmlns:tools="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF">
    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/DropDownButtonAdv/DropDownButtonAdv.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="SfScheduler.HeaderCell.AllowedViewSelectedButton.Static.Background" Color="#F5F5F5"/>
    <CornerRadius x:Key="SfScheduler.HeaderCell.DropDownButton.CornerRadius">16</CornerRadius>

    <Thickness x:Key="SfScheduler.HeaderCell.AllowedViewButton.Padding">14, 0, 14, 0</Thickness>
    <Thickness x:Key="SfScheduler.HeaderCell.AllowedViewButton.Margin">6, 0, 6, 0</Thickness>
    <CornerRadius x:Key="SfScheduler.HeaderCell.AllowedViewButton.CornerRadius">16</CornerRadius>

    <Style x:Key="SyncfusionSchedulerKeyboardFocusVisualStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border Height="28"
                            SnapsToDevicePixels="True"
                            CornerRadius="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.CornerRadius}"
                            BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                            BorderBrush="{StaticResource IconColor}"
                            Background="{StaticResource ContentBackgroundHovered}">
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionPreviousButtonStyle" TargetType="Button" BasedOn="{StaticResource WPFGlyphButtonStyle}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        x:Name="PART_Border"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Background="{TemplateBinding Background}"
                        Effect="{TemplateBinding Effect}"
                        CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                        <Path x:Name="PART_Previous"
                              Width="6"
                              Height="10" 
                              Stretch="Fill" 
                              VerticalAlignment="Center" 
                              HorizontalAlignment="Center">
                            <Path.Data>
                                <PathGeometry>M4.85355 0.646447C5.04882 0.841709 5.04882 1.15829 4.85355 1.35355L1.70711 4.5L4.85355 7.64645C5.04882 7.84171 5.04882 8.15829 4.85355 8.35355C4.65829 8.54882 4.34171 8.54882 4.14645 8.35355L0.646447 4.85355C0.451184 4.65829 0.451184 4.34171 0.646447 4.14645L4.14645 0.646447C4.34171 0.451184 4.65829 0.451184 4.85355 0.646447Z</PathGeometry>
                            </Path.Data>
                            <Path.Style>
                                <Style TargetType="Path">
                                    <Setter Property=" Fill" Value="{StaticResource IconColor}"/>
                                </Style>
                            </Path.Style>
                        </Path>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinmanager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="Default"/>
                                <Condition Property="IsFocused" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Previous" Property=" Fill" Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver"  Value="True">
                            <Setter TargetName="PART_Previous" Property=" Fill" Value="{StaticResource IconColor}"/>
                            <Setter TargetName="PART_Border" Property="Background"  Value="{StaticResource ContentBackgroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="PART_Previous" Property=" Fill" Value="{StaticResource IconColorSelected}"/>
                            <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundSelected}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="PART_Previous" Property=" Fill" Value="{StaticResource IconColorDisabled}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionNextButtonStyle" TargetType="Button" BasedOn="{StaticResource WPFGlyphButtonStyle}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        x:Name="PART_Border"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Background="{TemplateBinding Background}"
                        Effect="{TemplateBinding Effect}"
                        CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                        <Path x:Name="PART_Next"
                              Width="6" 
                              Height="10"
                              Stretch="Fill" 
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center" >
                            <Path.Data>
                                <PathGeometry>M0.646447 0.646447C0.451184 0.841709 0.451184 1.15829 0.646447 1.35355L3.79289 4.5L0.646447 7.64645C0.451184 7.84171 0.451184 8.15829 0.646447 8.35355C0.841709 8.54882 1.15829 8.54882 1.35355 8.35355L4.85355 4.85355C5.04882 4.65829 5.04882 4.34171 4.85355 4.14645L1.35355 0.646447C1.15829 0.451184 0.841709 0.451184 0.646447 0.646447Z</PathGeometry>
                            </Path.Data>
                            <Path.Style>
                                <Style TargetType="Path">
                                    <Setter Property=" Fill" Value="{StaticResource IconColor}"/>
                                </Style>
                            </Path.Style>
                        </Path>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinmanager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="Default"/>
                                <Condition Property="IsFocused" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Next" Property=" Fill" Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver"  Value="True">
                            <Setter TargetName="PART_Next" Property=" Fill" Value="{StaticResource IconColor}"/>
                            <Setter TargetName="PART_Border" Property="Background"  Value="{StaticResource ContentBackgroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="PART_Next" Property=" Fill" Value="{StaticResource IconColorSelected}"/>
                            <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundSelected}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="PART_Next" Property=" Fill" Value="{StaticResource IconColorDisabled}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSchedulerAllowedViewsButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="PART_Border"                             
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Height="28"
                            SnapsToDevicePixels="True"
                            CornerRadius="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.CornerRadius}" >
                        <ContentPresenter x:Name="PART_ContentPresenter" 
                                          Focusable="False"
                                          Margin="{TemplateBinding Padding}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          RecognizesAccessKey="True">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="PART_Border" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextBlock.Foreground" TargetName="PART_ContentPresenter" Value="{StaticResource PopupHoveredForeground}"/>
                            <Setter Property="Cursor" Value="Hand"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="PART_Border" Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="TextBlock.Foreground" TargetName="PART_ContentPresenter" Value="{StaticResource SelectedForeground}"/>
                        </Trigger>
                        <Trigger Property="skinmanager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="Default">
                <Setter Property="FocusVisualStyle" Value="{StaticResource SyncfusionSchedulerKeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource SyncfusionSchedulerKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionSchedulerDropDownButtonStyle" BasedOn="{StaticResource SyncfusionDropDownButtonAdvStyle}" TargetType="{x:Type shared:DropDownButtonAdv}">
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="IsHitTestVisible" Value="True"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:DropDownButtonAdv}">
                    <Grid>
                        <Border
                            Name="PART_Border"
                            MinHeight="{StaticResource Windows11Dark.MinHeight}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            SnapsToDevicePixels="True">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="20" />
                                </Grid.ColumnDefinitions>
                                <Grid HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock
                                        x:Name="PART_Text"
                                        Grid.Column="1"
                                        HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        FontFamily="{TemplateBinding FontFamily}"
                                        FontSize="{TemplateBinding FontSize}"
                                        Foreground="{TemplateBinding Foreground}"
                                        FontWeight="{TemplateBinding FontWeight}"
                                        Text="{TemplateBinding Label}" >
                                        <TextBlock.Margin>
                                            <Thickness>5,0,5,0</Thickness>
                                        </TextBlock.Margin>
                                    </TextBlock>
                                </Grid>
                                <Path
                                    x:Name="PART_Expander"
                                    Grid.Column="1"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Opacity="1"           
                                    Height="5.5"
                                    Width="9"   
                                    Stretch="Fill" >
                                    <Path.Data>
                                        <PathGeometry>M1 0.5L4.5 4L8 0.5</PathGeometry>
                                    </Path.Data>
                                    <Path.Margin>
                                        <Thickness>0</Thickness>
                                    </Path.Margin>
                                    <Path.Style>
                                        <Style TargetType="Path">
                                            <Setter Property="Stroke" Value="{StaticResource IconColor}"/>
                                        </Style>
                                    </Path.Style>
                                </Path>
                            </Grid>
                        </Border>
                        <Popup
                            x:Name="PART_DropDown"
                            AllowsTransparency="True"
                            Placement="Bottom"
                            StaysOpen="{TemplateBinding StaysOpen}">
                            <Border 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding  BorderBrush}"
                                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                Effect="{StaticResource Default.ShadowDepth4}">
                                <Border.Margin>
                                    <Thickness>6</Thickness>
                                </Border.Margin>
                                <Border.Padding>
                                    <Thickness>0,4,0,4</Thickness>
                                </Border.Padding>
                                <tools:CalendarEdit 
                                             x:Name="PART_Calendar" 
                                             MinMaxHidden="False" 
                                             BorderThickness="0"
                                             MinHeight="250"
                                             MinWidth="250"
                                             MonthChangeDirection="Horizontal"
                                             AllowMultiplySelection="False">
                                </tools:CalendarEdit>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsDropDownOpen" Value="True">
                            <Setter TargetName="PART_Expander" Property="Data" Value="M1 7L7 1L13 7" />
                        </Trigger>
                        <Trigger Property="IsDropDownOpen" Value="False">
                            <Setter TargetName="PART_Expander" Property="Data" Value="M1 1L7 7L13 1"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="PART_Border" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="Foreground" TargetName="PART_Text" Value="{StaticResource PopupHoveredForeground}"/>
                            <Setter Property="Stroke" TargetName="PART_Expander" Value="{StaticResource PopupHoveredForeground}"/>
                            <Setter Property="Cursor" Value="Hand"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="PART_Border" Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="Foreground" TargetName="PART_Text" Value="{StaticResource SelectedForeground}"/>
                            <Setter Property="Stroke" TargetName="PART_Expander" Value="{StaticResource SelectedForeground}"/>
                        </Trigger>
                        <Trigger Property="skinmanager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="Default">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionSchedulerAllowedViewsListItemStyle" TargetType="{x:Type ListViewItem}">
        <Setter Property="Background" Value="{StaticResource PopupBackground}"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Padding">
            <Setter.Value>
                <Thickness>7,2,7,2</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="Margin">
            <Setter.Value>
                <Thickness>0,2,0,2</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListViewItem}">
                    <Grid>
                        <Border x:Name="PART_SelectionIndicator"
                                HorizontalAlignment="Left" 
                                CornerRadius="1.5"
                                Height="12"
                                Width="2"
                                Visibility="Collapsed"  
                                Background="{StaticResource PrimaryColorForeground}" />
                        <Border  x:Name="PART_Border"        
                                 Background="{TemplateBinding Background}"
                                 BorderBrush="Transparent" 
                                 SnapsToDevicePixels="True"
                                 BorderThickness="0"
                                 Padding="6,0,6,0"
                                 CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" 
                                 >
                            <ContentPresenter   x:Name="PART_ContentPresenter"
                                                Focusable="True"
                                                Margin="{TemplateBinding Padding}" 
                                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                SnapsToDevicePixels="True">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                            <Border.Triggers>
                                <!--Animation the selection Indicator-->
                                <EventTrigger RoutedEvent="Border.MouseDown">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="PART_SelectionIndicator" 
                                                             Storyboard.TargetProperty="Height"
                                                             From="12" 
                                                             To="7"               
                                                             Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="Border.MouseUp">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="PART_SelectionIndicator" 
                                                             Storyboard.TargetProperty="Height"
                                                             From="7" 
                                                             To="12" 
                                                             Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </Border.Triggers>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background"  TargetName="PART_Border"  Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter Property="BorderBrush"  TargetName="PART_Border"  Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter Property="TextBlock.Foreground"  TargetName="PART_ContentPresenter" Value="{StaticResource PopupHoveredForeground}"/>
                            <Setter Property="Cursor" Value="Hand"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background"  TargetName="PART_Border"  Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="BorderBrush"  TargetName="PART_Border"  Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="TextBlock.Foreground" TargetName="PART_ContentPresenter" Value="{StaticResource PrimaryColorForeground}" />
                            <Setter Property="Visibility" TargetName="PART_SelectionIndicator" Value="Visible" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="IsSelected" Value="True"/>
                            </MultiTrigger.Conditions>
                            <MultiTrigger.Setters>
                                <Setter Property="Background"  TargetName="PART_Border"  Value="{StaticResource PopupHoveredBackground}"/>
                                <Setter Property="TextBlock.Foreground"  TargetName="PART_ContentPresenter" Value="{StaticResource PopupHoveredForeground}"/>
                                <Setter Property="Cursor" Value="Hand"/>
                            </MultiTrigger.Setters>
                        </MultiTrigger>
                        <Trigger Property="skinmanager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSchedulerHeaderControlStyle" TargetType="scheduler:SchedulerHeaderControl">
        <Setter Property="Padding" Value="5,0,0,0" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.TitleTextStyle}" />
        <Setter Property="Foreground" Value="{StaticResource SecondaryForeground}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="BorderThickness">
            <Setter.Value>
                <Thickness>0,0,0,1</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:SchedulerHeaderControl">
                    <Border    Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}" 
                               BorderThickness="{TemplateBinding BorderThickness}"
                               HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                               VerticalAlignment="{TemplateBinding VerticalAlignment}">
                        <Grid  x:Name="PART_LayoutRoot"
                               Background="{TemplateBinding Background}">
                            <ContentPresenter x:Name="PART_ContentPresenter"
                                              HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                              VerticalAlignment="Center"
                                              TextBlock.FontFamily="{TemplateBinding FontFamily}"
                                              TextBlock.FontSize="{TemplateBinding FontSize}"
                                              TextBlock.FontWeight="{TemplateBinding FontWeight}"
                                              TextBlock.Foreground="{TemplateBinding Foreground}" 
                                              Visibility="Collapsed" />
                            <Grid x:Name="PART_Grid" 
                                  Background="{TemplateBinding Background}"
                                  HorizontalAlignment="Stretch"
                                  VerticalAlignment="Stretch"
                                  Visibility="Visible">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <shared:DropDownButtonAdv 
                                          x:Name="PART_DropDownButton" 
                                          Grid.Column="1"   
                                          VerticalAlignment="Center" 
                                          Label="{TemplateBinding Content}"
                                          BorderThickness="0" 
                                          BorderBrush="Transparent" 
                                          Background="Transparent" 
                                          DropDirection="BottomLeft"                
                                          Height="28" 
                                          Style="{StaticResource SyncfusionSchedulerDropDownButtonStyle}"
                                          FontFamily="{TemplateBinding FontFamily}" 
                                          FontSize="{TemplateBinding FontSize}"
                                          FontWeight="{TemplateBinding FontWeight}"
                                          Foreground="{TemplateBinding Foreground}" 
                                          Visibility="Collapsed"/>
                                <TextBlock x:Name="PART_TextBlock"
                                           Grid.Column="1"
                                           Text="{TemplateBinding Content}"
                                           Margin="{TemplateBinding Padding}"
                                           HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                           VerticalAlignment="Center"
                                           FontFamily="{TemplateBinding FontFamily}"
                                           FontSize="{TemplateBinding FontSize}"
                                           FontWeight="{TemplateBinding FontWeight}"
                                           Foreground="{TemplateBinding Foreground}"
                                           Visibility="Visible"/>
                                <Button  x:Name="PART_PreviousNavigationButton"
                                         Grid.Column="0"
                                         Width="28"
                                         Height="28" 
                                         Margin="10,0,0,0"
                                         HorizontalAlignment="Left"  
                                         VerticalAlignment="Center" 
                                         IsTabStop="False"
                                         Style="{StaticResource SyncfusionPreviousButtonStyle}"
                                         Visibility="Visible" />
                                <Button  x:Name="PART_NextNavigationButton"
                                         Grid.Column="0"
                                         Width="28"
                                         Height="28"
                                         Margin="40,0,0,0"
                                         HorizontalAlignment="Right"
                                         VerticalAlignment="Center"
                                         IsTabStop="False"
                                         Style="{StaticResource SyncfusionNextButtonStyle}"
                                         Visibility="Visible" />
                                <StackPanel x:Name="PART_StackPanel" 
                                            Grid.Column="2"
                                            Orientation="Horizontal" 
                                            Background="Transparent"
                                            Width="Auto"
                                            Height="Auto" 
                                            HorizontalAlignment="Right"
                                            VerticalAlignment="Center">
                                    <Button x:Name="PART_TodayButton"     Style="{StaticResource SyncfusionSchedulerAllowedViewsButtonStyle}" Foreground="{TemplateBinding Foreground}" Height="{TemplateBinding Height}" FontSize="{StaticResource Windows11Dark.BodyTextStyle}" FontFamily="{TemplateBinding FontFamily}" FontWeight="{TemplateBinding FontWeight}" Margin="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Margin}" Padding="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Padding}" BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" BorderBrush="{StaticResource BorderAlt}" Background="{TemplateBinding Background}" Visibility="Collapsed"/>
                                    <Border x:Name="PART_Border"          Height="20"  BorderThickness="{StaticResource Windows11Dark.BorderThickness1000}" BorderBrush="{StaticResource BorderAlt}" Margin="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Margin}" UseLayoutRounding="True" SnapsToDevicePixels="True" Visibility="Collapsed" />
                                    <Button x:Name="PART_ViewTypeButton1" Style="{StaticResource SyncfusionSchedulerAllowedViewsButtonStyle}" Foreground="{TemplateBinding Foreground}" Height="{TemplateBinding Height}" FontSize="{StaticResource Windows11Dark.BodyTextStyle}" FontFamily="{TemplateBinding FontFamily}" FontWeight="{TemplateBinding FontWeight}" Margin="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Margin}" Padding="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Padding}" BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" BorderBrush="{StaticResource BorderAlt}" Background="{TemplateBinding Background}" Visibility="Collapsed"/>
                                    <Button x:Name="PART_ViewTypeButton2" Style="{StaticResource SyncfusionSchedulerAllowedViewsButtonStyle}" Foreground="{TemplateBinding Foreground}" Height="{TemplateBinding Height}" FontSize="{StaticResource Windows11Dark.BodyTextStyle}" FontFamily="{TemplateBinding FontFamily}" FontWeight="{TemplateBinding FontWeight}" Margin="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Margin}" Padding="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Padding}" BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" BorderBrush="{StaticResource BorderAlt}" Background="{TemplateBinding Background}" Visibility="Collapsed"/>
                                    <Button x:Name="PART_ViewTypeButton3" Style="{StaticResource SyncfusionSchedulerAllowedViewsButtonStyle}" Foreground="{TemplateBinding Foreground}" Height="{TemplateBinding Height}" FontSize="{StaticResource Windows11Dark.BodyTextStyle}" FontFamily="{TemplateBinding FontFamily}" FontWeight="{TemplateBinding FontWeight}" Margin="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Margin}" Padding="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Padding}" BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" BorderBrush="{StaticResource BorderAlt}" Background="{TemplateBinding Background}" Visibility="Collapsed"/>
                                    <Button x:Name="PART_ViewTypeButton4" Style="{StaticResource SyncfusionSchedulerAllowedViewsButtonStyle}" Foreground="{TemplateBinding Foreground}" Height="{TemplateBinding Height}" FontSize="{StaticResource Windows11Dark.BodyTextStyle}" FontFamily="{TemplateBinding FontFamily}" FontWeight="{TemplateBinding FontWeight}" Margin="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Margin}" Padding="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Padding}" BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" BorderBrush="{StaticResource BorderAlt}" Background="{TemplateBinding Background}" Visibility="Collapsed"/>
                                    <Button x:Name="PART_ViewTypeButton5" Style="{StaticResource SyncfusionSchedulerAllowedViewsButtonStyle}" Foreground="{TemplateBinding Foreground}" Height="{TemplateBinding Height}" FontSize="{StaticResource Windows11Dark.BodyTextStyle}" FontFamily="{TemplateBinding FontFamily}" FontWeight="{TemplateBinding FontWeight}" Margin="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Margin}" Padding="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Padding}" BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" BorderBrush="{StaticResource BorderAlt}" Background="{TemplateBinding Background}" Visibility="Collapsed"/>
                                    <Button x:Name="PART_ViewTypeButton6" Style="{StaticResource SyncfusionSchedulerAllowedViewsButtonStyle}" Foreground="{TemplateBinding Foreground}" Height="{TemplateBinding Height}" FontSize="{StaticResource Windows11Dark.BodyTextStyle}" FontFamily="{TemplateBinding FontFamily}" FontWeight="{TemplateBinding FontWeight}" Margin="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Margin}" Padding="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Padding}" BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" BorderBrush="{StaticResource BorderAlt}" Background="{TemplateBinding Background}" Visibility="Collapsed"/>
                                    <Button x:Name="PART_ViewTypeButton7" Style="{StaticResource SyncfusionSchedulerAllowedViewsButtonStyle}" Foreground="{TemplateBinding Foreground}" Height="{TemplateBinding Height}" FontSize="{StaticResource Windows11Dark.BodyTextStyle}" FontFamily="{TemplateBinding FontFamily}" FontWeight="{TemplateBinding FontWeight}" Margin="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Margin}" Padding="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Padding}" BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" BorderBrush="{StaticResource BorderAlt}" Background="{TemplateBinding Background}" Visibility="Collapsed"/>
                                    <Button x:Name="PART_ViewTypeButton8" Style="{StaticResource SyncfusionSchedulerAllowedViewsButtonStyle}" Foreground="{TemplateBinding Foreground}" Height="{TemplateBinding Height}" FontSize="{StaticResource Windows11Dark.BodyTextStyle}" FontFamily="{TemplateBinding FontFamily}" FontWeight="{TemplateBinding FontWeight}" Margin="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Margin}" Padding="{StaticResource SfScheduler.HeaderCell.AllowedViewButton.Padding}" BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" BorderBrush="{StaticResource BorderAlt}" Background="{TemplateBinding Background}" Visibility="Collapsed"/>
                                </StackPanel>
                                <ToggleButton 
                                    x:Name="PART_ViewTypeComboBox" 
                                    Background="Transparent"
                                    Grid.Column="3" 
                                    Width="26" 
                                    Height="28" 
                                    BorderThickness="0"
                                    Margin="2,0,6,0" 
                                    HorizontalContentAlignment="Center"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center" 
                                    FontSize="12"                       
                                    Foreground="{TemplateBinding Foreground}"
                                    FontWeight="{TemplateBinding FontWeight}"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    Visibility="{Binding ElementName=PART_ViewTypeListView,Path=Visibility}">
                                    <ToggleButton.Style>
                                        <Style TargetType="ToggleButton">
                                            <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                                            <Setter Property="IsHitTestVisible" Value="True"/>
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="{x:Type ToggleButton}">
                                                        <Border x:Name="PART_Border"
                                                                Focusable="False"
                                                                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" 
                                                                Background="Transparent"
                                                                BorderBrush="Transparent"
                                                                BorderThickness="0">
                                                            <Path x:Name="PART_Path"
                                                                Height="14"
                                                                Width="3"
                                                                HorizontalAlignment="Center"
                                                                VerticalAlignment="Center" 
                                                                Fill="{StaticResource IconColor}"
                                                                Stretch="Fill" >
                                                                <Path.Data>
                                                                    <PathGeometry>M2.4 1.2C2.4 1.86274 1.86274 2.4 1.2 2.4C0.537258 2.4 0 1.86274 0 1.2C0 0.537258 0.537258 0 1.2 0C1.86274 0 2.4 0.537258 2.4 1.2ZM2.4 6.2C2.4 6.86274 1.86274 7.4 1.2 7.4C0.537258 7.4 0 6.86274 0 6.2C0 5.53726 0.537258 5 1.2 5C1.86274 5 2.4 5.53726 2.4 6.2ZM1.2 12.4C1.86274 12.4 2.4 11.8627 2.4 11.2C2.4 10.5373 1.86274 10 1.2 10C0.537258 10 0 10.5373 0 11.2C0 11.8627 0.537258 12.4 1.2 12.4Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <DataTrigger Binding="{Binding ElementName=PART_Popup,Path=IsOpen}" Value="True">
                                                                <Setter Property="IsHitTestVisible" Value="False"/>
                                                            </DataTrigger>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundHovered}"/>
                                                                <Setter Property="Fill" TargetName="PART_Path" Value="{StaticResource PopupHoveredForeground}"/>
                                                                <Setter Property="Cursor" Value="Hand"/>
                                                            </Trigger>
                                                            <Trigger Property="IsChecked" Value="True">
                                                                <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundSelected}"/>
                                                                <Setter TargetName="PART_Path" Property="Fill" Value="{StaticResource SelectedForeground}"/>
                                                            </Trigger>
                                                            <Trigger Property="skinmanager:SfSkinManager.SizeMode" Value="Touch">
                                                                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                            <Style.Triggers>
                                                <Trigger Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="Default">
                                                    <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
                                                </Trigger>
                                                <Trigger Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                                                    <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ToggleButton.Style>
                                    <Popup 
                                        x:Name="PART_Popup" 
                                        PopupAnimation="Fade" 
                                        Placement="Bottom"
                                        AllowsTransparency="True"
                                        PlacementTarget="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType=ToggleButton}}" 
                                        StaysOpen="False"  
                                        IsOpen="{Binding ElementName=PART_ViewTypeComboBox,Path=IsChecked,Mode=TwoWay}" 
                                        UseLayoutRounding="True" 
                                        SnapsToDevicePixels="True">
                                        <Border 
                                            Background="{StaticResource PopupBackground}"
                                            SnapsToDevicePixels="True"
                                            BorderBrush="{StaticResource BorderAlt}"
                                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                            BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                            Effect="{StaticResource Default.ShadowDepth4}">
                                            <Border.Margin>
                                                <Thickness>6</Thickness>
                                            </Border.Margin>
                                            <Border.Padding>
                                                <Thickness>0,4,0,4</Thickness>
                                            </Border.Padding>
                                            <ListView x:Name="PART_ViewTypeListView" 
                                                      ItemContainerStyle="{StaticResource SyncfusionSchedulerAllowedViewsListItemStyle}"
                                                      Background="{StaticResource PopupBackground}" 
                                                      Visibility="Collapsed">
                                                <ListView.Resources>
                                                    <Style TargetType="ListBox">
                                                        <Setter Property="ItemContainerStyle" Value="{StaticResource SyncfusionSchedulerAllowedViewsListItemStyle}" />
                                                    </Style>
                                                </ListView.Resources>
                                            </ListView>
                                        </Border>
                                    </Popup>
                                </ToggleButton>
                            </Grid>
                        </Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="SelectionStates">
                                <VisualState x:Name="UnSelected"/>
                                <VisualState x:Name="PART_ViewTypeButton1Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton1" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton1" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton1" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{TemplateBinding Background}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="PART_ViewTypeButton2Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton2" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton2" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton2" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{TemplateBinding Background}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="PART_ViewTypeButton3Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton3" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton3" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton3" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{TemplateBinding Background}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="PART_ViewTypeButton4Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton4" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton4" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton4" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{TemplateBinding Background}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="PART_ViewTypeButton5Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton5" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton5" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton5" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{TemplateBinding Background}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="PART_ViewTypeButton6Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton6" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton6" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton6" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{TemplateBinding Background}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="PART_ViewTypeButton7Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton7" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton7" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton7" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{TemplateBinding Background}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="PART_ViewTypeButton8Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton8" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton8" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ViewTypeButton8" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{TemplateBinding Background}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSchedulerHeaderControlStyle}" TargetType="scheduler:SchedulerHeaderControl" />

</ResourceDictionary>
