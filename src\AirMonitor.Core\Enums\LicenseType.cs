namespace AirMonitor.Core.Enums;

/// <summary>
/// 许可证类型枚举
/// 定义4种内部使用的许可证类型
/// </summary>
public enum LicenseType
{
    /// <summary>
    /// 普通版 - 基础监控功能，1年有效期
    /// </summary>
    Standard = 1,

    /// <summary>
    /// 售后版 - 售后服务专用功能，1年有效期
    /// </summary>
    AfterSales = 2,

    /// <summary>
    /// 研发版 - 所有功能，永久有效期
    /// </summary>
    Development = 3,

    /// <summary>
    /// 管理版 - 所有功能+管理权限，永久有效期
    /// </summary>
    Management = 4
}
