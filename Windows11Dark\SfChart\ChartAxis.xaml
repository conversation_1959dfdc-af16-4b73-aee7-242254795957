<ResourceDictionary 
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:local="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF">
  
  <ResourceDictionary.MergedDictionaries>

        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfChart/SfChartCommon.xaml"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <!--Grid line style-->
    <Style x:Key="gridLineStyle" TargetType="Line">
        <Setter Property="Stroke" Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="StrokeThickness" Value="1"></Setter>
    </Style>

    <Style x:Key="SyncfusionChartAxisLineStyle" TargetType="Line">
        <Setter Property="Stroke" Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="StrokeThickness" Value="1"></Setter>
    </Style>
    
    <Style x:Key="ticklineStyle"
           TargetType="Line">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt}"></Setter>
    </Style>

    <ControlTemplate x:Key="RepeatButtonTemplate"
                     TargetType="RepeatButton">
        <Grid x:Name="Root"
              Background="Transparent">
            <VisualStateManager.VisualStateGroups>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                </VisualStateGroup>
            </VisualStateManager.VisualStateGroups>
        </Grid>
    </ControlTemplate>

    <ControlTemplate x:Key="HorizontalThumbTemplate"
                     TargetType="Thumb">
        <Grid x:Name="Root"
              Height="10"
              Background="Transparent">
            <Rectangle Height="3"
                       x:Name="Background"
                       Fill="{StaticResource ContentBackgroundAlt2}">
            </Rectangle>
        </Grid>
    </ControlTemplate>

    <Style x:Key="SyncfusionResizableScrollBarStyle"
           TargetType="local:ResizableScrollBar">
        <Setter Property="Background"
                Value="{StaticResource ContentBackgroundAlt2}"></Setter>
        <Setter Property="MinWidth"
                Value="17" />
        <Setter Property="MinHeight"
                Value="17" />
        <Setter Property="IsTabStop"
                Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:ResizableScrollBar">
                    <Grid x:Name="Root">
                        <Grid.Resources>
                            <!-- Horizontal Inc/Dec Templates -->
                            <ControlTemplate x:Key="HorizontalIncrementTemplate"
                                             TargetType="RepeatButton">
                                <Grid x:Name="Root">
                                    <Rectangle x:Name="Background"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackgroundAlt2}"
                                               StrokeThickness="1">
                                    </Rectangle>
                                    <Rectangle x:Name="Highlight"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               IsHitTestVisible="false"
                                               Stroke="#FF686868"
                                               StrokeThickness="1"
                                               Margin="1" />
                                    <TextBlock Text="&#xe70b;"
                                               FontSize="16"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               Foreground="{StaticResource IconColor}"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                    <Rectangle x:Name="DisabledElement"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackgroundAlt2}" />
                                </Grid>
                            </ControlTemplate>
                            <ControlTemplate x:Key="HorizontalDecrementTemplate"
                                             TargetType="RepeatButton">
                                <Grid x:Name="Root">
                                    <Rectangle x:Name="Background"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackgroundAlt2}"
                                               StrokeThickness="1">
                                    </Rectangle>
                                    <TextBlock Text="&#xe70a;"
                                               FontSize="16"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               Foreground="{StaticResource IconColor}"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                    <Rectangle x:Name="DisabledElement"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="9"
                                               Fill="{StaticResource ContentBackgroundAlt2}" />
                                </Grid>
                            </ControlTemplate>

                            <!-- Vertical Inc/Dec Templates -->
                            <ControlTemplate x:Key="VerticalIncrementTemplate"
                                             TargetType="RepeatButton">
                                <Grid x:Name="Root">
                                    <Rectangle x:Name="Background"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackgroundAlt2}"
                                               StrokeThickness="1"></Rectangle>
                                    <Rectangle x:Name="Highlight"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               IsHitTestVisible="false"
                                               Stroke="#FF6DBDD1"
                                               StrokeThickness="1"
                                               Margin="1" />
                                    <TextBlock Text="&#xe708;"
                                               FontSize="16"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               Foreground="{StaticResource IconColor}"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                    <Rectangle x:Name="DisabledElement"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackgroundAlt2}" />
                                </Grid>
                            </ControlTemplate>
                            <ControlTemplate x:Key="VerticalDecrementTemplate"
                                             TargetType="RepeatButton">
                                <Grid x:Name="Root">
                                    <Rectangle x:Name="Background"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackgroundAlt2}"
                                               StrokeThickness="1">
                                    </Rectangle>
                                    <Rectangle x:Name="Highlight"
                                               Opacity="0"
                                               RadiusX="1"
                                               RadiusY="1"
                                               IsHitTestVisible="false"
                                               Stroke="{StaticResource ContentBackgroundAlt2}"
                                               StrokeThickness="1"
                                               Margin="1" />
                                    <TextBlock Text="&#xe709;"
                                               FontSize="16"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               Foreground="{StaticResource IconColor}"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                    <Rectangle x:Name="DisabledElement"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackgroundAlt2}" />
                                </Grid>
                            </ControlTemplate>

                            <!-- Thumb Templates -->
                            <ControlTemplate x:Key="VerticalThumbTemplate"
                                             TargetType="Thumb">
                                <Grid x:Name="ThumbVisual">
                                    <Rectangle x:Name="Background"
                                               Fill="{StaticResource BorderAlt}">
                                    </Rectangle>
                                    <ContentControl HorizontalAlignment="Center"
                                                    VerticalAlignment="Center">
                                        <Grid Height="15"
                                              Width="12">
                                            <TextBlock Text="&#xe7ef;"
                                                       FontSize="22"
                                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                                       Foreground="{StaticResource Border}"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0.795,0,0"/>
                                            <TextBlock Text="&#xe7ef;"
                                                       FontSize="22"
                                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                                       Foreground="{StaticResource ContentBackgroundAlt2}"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,0,0.794"/>
                                        </Grid>
                                    </ContentControl>
                                </Grid>
                            </ControlTemplate>
                            <ControlTemplate x:Key="HorizontalThumbTemplate"
                                             TargetType="Thumb">
                                <Grid x:Name="ThumbVisual">
                                    <Rectangle x:Name="Background"
                                               Fill="{StaticResource BorderAlt}">
                                    </Rectangle>
                                    <ContentControl HorizontalAlignment="Center"
                                                    VerticalAlignment="Center">
                                        <Grid Height="12"
                                              Width="15">
                                            <TextBlock Text="&#xe7f0;"
                                                       FontSize="22"
                                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                                       Foreground="{StaticResource Border}"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,0.795,0"/>
                                            <TextBlock Text="&#xe7f0;"
                                                       FontSize="22"
                                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                                       Foreground="{StaticResource ContentBackgroundAlt2}"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"
                                                       Margin="0.794,0,0,0"/>
                                        </Grid>
                                    </ContentControl>
                                </Grid>
                            </ControlTemplate>
                            <ControlTemplate x:Key="HorizontalNearThumbTemplate"
                                             TargetType="Thumb">
                                <Border CornerRadius="8,0,0,8"
                                        Margin="0,0,-1,0"
                                        Width="18"
                                        Background="{StaticResource BorderAlt}">
                                    <Grid Margin="4,0,0,0">
                                        <ContentControl>
                                            <Ellipse Width="9"
                                                     Height="9"
                                                     Fill="{StaticResource BorderAlt}">
                                            </Ellipse>
                                        </ContentControl>
                                    </Grid>
                                </Border>
                            </ControlTemplate>
                            <ControlTemplate x:Key="HorizontalFarThumbTemplate"
                                             TargetType="Thumb">
                                <Border CornerRadius="0,8,8,0"
                                        Margin="-1,0,0,0"
                                        Width="18"
                                        Background="{StaticResource BorderAlt}">
                                    <Grid Margin="0,0,3,0">
                                        <ContentControl>
                                            <Ellipse Width="9"
                                                     Height="9"
                                                     Fill="{StaticResource BorderAlt}">
                                            </Ellipse>
                                        </ContentControl>
                                    </Grid>
                                </Border>
                            </ControlTemplate>
                            <ControlTemplate x:Key="VerticalNearThumbTemplate"
                                             TargetType="Thumb">
                                <Border CornerRadius="0,0,8,8"
                                        Margin="0,-1,0,0"
                                        Height="18"
                                        Background="#FF686868">
                                    <Grid>
                                        <ContentControl>
                                            <Ellipse Width="9"
                                                     Height="9"
                                                     Fill="{StaticResource BorderAlt}">
                                            </Ellipse>
                                        </ContentControl>
                                    </Grid>
                                </Border>
                            </ControlTemplate>
                            <ControlTemplate x:Key="VerticalFarThumbTemplate"
                                             TargetType="Thumb">
                                <Border CornerRadius="8,8,0,0"
                                        Margin="0,0,0,-1"
                                        Height="18"
                                        Background="#FF686868">
                                    <Grid>
                                        <ContentControl>
                                            <Ellipse Width="9"
                                                     Height="9"
                                                     Fill="{StaticResource BorderAlt}">
                                            </Ellipse>
                                        </ContentControl>
                                    </Grid>
                                </Border>
                            </ControlTemplate>
                        </Grid.Resources>

                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="MouseOver" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="Root"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.5"
                                                         Duration="0" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>

                        <!-- Horizontal Template -->
                        <Grid x:Name="HorizontalRoot"
                              Visibility="Collapsed">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!-- Track Layer -->
                            <Rectangle Grid.ColumnSpan="7"
                                       RadiusX="8"
                                       RadiusY="8"
                                       StrokeThickness="1"
                                       Stroke="{StaticResource ContentBackgroundAlt2}"
                                       Fill="{StaticResource ContentBackgroundAlt2}"></Rectangle>
                            <Rectangle Grid.ColumnSpan="7"
                                       RadiusX="8"
                                       RadiusY="8"
                                       StrokeThickness="1"
                                       Stroke="{StaticResource ContentBackgroundAlt2}"
                                       Fill="{TemplateBinding Background}" />
                            <Rectangle Grid.ColumnSpan="7"
                                       RadiusX="8"
                                       RadiusY="8"
                                       StrokeThickness="1"
                                       Fill="{StaticResource ContentBackgroundAlt2}"></Rectangle>
                            <Rectangle x:Name="ScrollRect"
                                       Grid.ColumnSpan="7"
                                       RadiusX="8"
                                       RadiusY="8"
                                       Margin="1"
                                       Stroke="{StaticResource ContentBackgroundAlt2}">
                            </Rectangle>

                            <!-- Repeat Buttons + Thumb -->
                            <RepeatButton x:Name="HorizontalSmallDecrease"
                                          Visibility="{TemplateBinding Property=ScrollButtonVisibility}"
                                          Grid.Column="0"
                                          MinWidth="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Template="{StaticResource HorizontalDecrementTemplate}"
                                          Margin="1" />
                            <RepeatButton x:Name="HorizontalLargeDecrease"
                                          Grid.Column="1"
                                          Width="0"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          Interval="50"
                                          IsTabStop="False" />
                            <Thumb x:Name="HorizontalThumbHand1"
                                   Cursor="ScrollWE"
                                   Background="{TemplateBinding Background}"
                                   MinWidth="10"
                                   Grid.Column="2"
                                   Template="{StaticResource HorizontalNearThumbTemplate}" />
                            <Thumb x:Name="HorizontalThumb"
                                   Cursor="Hand"
                                   Background="{TemplateBinding Background}"
                                   MinWidth="0"
                                   Width="18"
                                   Grid.Column="3"
                                   Template="{StaticResource HorizontalThumbTemplate}" />
                            <Thumb x:Name="HorizontalThumbHand2"
                                   Cursor="ScrollWE"
                                   Background="{TemplateBinding Background}"
                                   MinWidth="10"
                                   Grid.Column="4"
                                   Template="{StaticResource HorizontalFarThumbTemplate}" />
                            <RepeatButton x:Name="HorizontalLargeIncrease"
                                          Grid.Column="5"
                                          Interval="50"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          IsTabStop="False" />
                            <RepeatButton x:Name="HorizontalSmallIncrease"
                                          Visibility="{TemplateBinding Property=ScrollButtonVisibility}"
                                          Grid.Column="6"
                                          MinWidth="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Template="{StaticResource HorizontalIncrementTemplate}"
                                          Margin="1" />
                        </Grid>

                        <!-- Vertical Template -->
                        <Grid x:Name="VerticalRoot">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <!-- Track Layer -->
                            <Rectangle Grid.RowSpan="7"
                                       RadiusX="8"
                                       RadiusY="8"
                                       StrokeThickness="1"
                                       Stroke="{StaticResource ContentBackgroundAlt2}"
                                       Fill="{StaticResource ContentBackgroundAlt2}"></Rectangle>
                            <Rectangle Grid.RowSpan="7"
                                       RadiusX="8"
                                       RadiusY="8"
                                       StrokeThickness="1"
                                       Fill="{StaticResource ContentBackgroundAlt2}"></Rectangle>
                            <Rectangle Grid.RowSpan="7"
                                       RadiusX="8"
                                       RadiusY="8"
                                       Margin="1"
                                       Stroke="#FFB7B7B7"></Rectangle>

                            <!-- Repeat Buttons + Thumb -->
                            <RepeatButton x:Name="VerticalSmallDecrease"
                                          Visibility="{TemplateBinding Property=ScrollButtonVisibility}"
                                          Grid.Row="6"
                                          MinHeight="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Template="{StaticResource VerticalDecrementTemplate}"
                                          Margin="1"
                                          RenderTransformOrigin="0.5,0.5">
                                <RepeatButton.RenderTransform>
                                    <RotateTransform Angle="180"></RotateTransform>
                                </RepeatButton.RenderTransform>
                            </RepeatButton>
                            <RepeatButton x:Name="VerticalLargeDecrease"
                                          Grid.Row="5"
                                          Height="0"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          Interval="50"
                                          IsTabStop="False" />
                            <Thumb x:Name="VerticalThumbHand1"
                                   MinHeight="18"
                                   Grid.Row="4"
                                   Cursor="ScrollNS"
                                   Template="{StaticResource VerticalNearThumbTemplate}" />
                            <Thumb x:Name="VerticalThumb"
                                   Cursor="Hand"
                                   MinHeight="0"
                                   Height="18"
                                   Grid.Row="3"
                                   Template="{StaticResource VerticalThumbTemplate}" />
                            <Thumb x:Name="VerticalThumbHand2"
                                   MinHeight="18"
                                   Grid.Row="2"
                                   Cursor="ScrollNS"
                                   Template="{StaticResource VerticalFarThumbTemplate}" />
                            <RepeatButton x:Name="VerticalLargeIncrease"
                                          Grid.Row="1"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          Interval="50"
                                          IsTabStop="False" />
                            <RepeatButton x:Name="VerticalSmallIncrease"
                                          Grid.Row="0"
                                          Visibility="{TemplateBinding Property=ScrollButtonVisibility}"
                                          MinHeight="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Template="{StaticResource VerticalIncrementTemplate}"
                                          Margin="1"
                                          RenderTransformOrigin="0.5,0.5">
                                <RepeatButton.RenderTransform>
                                    <RotateTransform Angle="180"></RotateTransform>
                                </RepeatButton.RenderTransform>
                            </RepeatButton>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfChartResizableBarStyle"
           TargetType="local:SfChartResizableBar">
        <Setter Property="Background"
                Value="{StaticResource ContentBackgroundAlt2}"></Setter>
        <Setter Property="MinWidth"
                Value="3" />
        <Setter Property="MinHeight"
                Value="3" />
        <Setter Property="IsTabStop"
                Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate  TargetType="local:SfChartResizableBar">
                    <Grid x:Name="Root">
                        <Grid.Resources>

                            <!-- Thumb Templates -->
                            <ControlTemplate x:Key="VerticalThumbTemplate"
                                             TargetType="Thumb">
                                <Grid x:Name="Root"
                                      Width="10"
                                      Background="Transparent">
                                    <Rectangle Width="3"
                                               x:Name="Background"
                                               Fill="{StaticResource ContentBackgroundAlt2}">
                                    </Rectangle>
                                </Grid>
                            </ControlTemplate>

                            <ControlTemplate x:Key="HorizontalPositionThumbTemplate"
                                             TargetType="Thumb">
                                <Grid x:Name="Root"
                                      Width="35">
                                    <Grid.RowDefinitions>
                                        <RowDefinition></RowDefinition>
                                        <RowDefinition></RowDefinition>
                                    </Grid.RowDefinitions>
                                    <TextBlock Text="&#xe745;"
                                          Foreground="{StaticResource BorderAlt}"
                                          FontSize="18"
                                          FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                          Grid.Row="0"
                                          Margin="0,0,0,-2" />
                                    <TextBlock Text="&#xe708;"
                                          Foreground="{StaticResource BorderAlt}"
                                          FontSize="14"
                                          FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                          UseLayoutRounding="False"
                                          Grid.Row="1">
                                    </TextBlock>
                                    <Grid.RenderTransform>
                                        <RotateTransform Angle="180"></RotateTransform>
                                    </Grid.RenderTransform>
                                </Grid>
                            </ControlTemplate>
                            
                            <ControlTemplate x:Key="VerticalPositionThumbTemplate"
                                             TargetType="Thumb">
                                <Grid x:Name="Root"
                                      Width="62">
                                    <Grid.RowDefinitions>
                                        <RowDefinition></RowDefinition>
                                        <RowDefinition></RowDefinition>
                                    </Grid.RowDefinitions>
                                    <TextBlock Text="&#xe745;"
                                          Foreground="{StaticResource BorderAlt}"
                                          FontSize="18"
                                          FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                          Grid.Row="0" />
                                    <TextBlock Text="&#xe708;"
                                          Foreground="{StaticResource BorderAlt}"
                                          FontSize="14"
                                          FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                          UseLayoutRounding="False"
                                          Grid.Row="1"
                                          Margin="0,-28,0,0"></TextBlock>
                                    <Grid.RenderTransform>
                                        <RotateTransform Angle="270"></RotateTransform>
                                    </Grid.RenderTransform>
                                </Grid>
                            </ControlTemplate>
                            
                        </Grid.Resources>

                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="MouseOver" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="Root"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.5"
                                                         Duration="0" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="TouchMode">
                                <VisualState x:Name="OnView">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0"
                                                                  Value="0.3">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0"
                                                                  Value="3">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0"
                                                                  Value="0.3">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0"
                                                                  Value="3">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="HorizontalThumbHand1">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.6"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="HorizontalThumbHand2">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.6"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="VerticalThumbHand1">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.6"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="VerticalThumbHand2">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.6"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="OnFocus">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0.3">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0.5" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="3">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0.3">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0.5" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="3">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="OnLostFocus">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="HorizontalThumbHand1">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="HorizontalThumbHand2">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="VerticalThumbHand1">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="VerticalThumbHand2">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="OnExit">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>

                        <!-- Horizontal Template -->
                        <Grid x:Name="HorizontalRoot"
                              Visibility="Collapsed">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!-- Track Layer -->
                            <Line Grid.ColumnSpan="7"
                                  Height="5"></Line>
                            <!-- Repeat Buttons + Thumb -->
                            <RepeatButton x:Name="HorizontalSmallDecrease"
                                          Visibility="{TemplateBinding ScrollButtonVisibility}"
                                          Grid.Column="0"
                                          MinWidth="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Margin="1" />
                            <RepeatButton x:Name="HorizontalLargeDecrease"
                                          Grid.Column="1"
                                          Width="0"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          Interval="50"
                                          IsTabStop="False" />
                            <Canvas Grid.Column="2"
                                    Canvas.ZIndex="1">
                                <ContentControl  x:Name="HorizontalNearHandContent"
                                                 Visibility="Collapsed">
                                    <ContentPresenter></ContentPresenter>
                                </ContentControl>
                                <Thumb x:Name="HorizontalThumbHand1"
                                       Background="{TemplateBinding Background}"
                                       MinWidth="10"
                                       Template="{StaticResource HorizontalPositionThumbTemplate}"
                                       Opacity="0"
                                       RenderTransformOrigin="0.5,0.5">
                                    <Thumb.RenderTransform>
                                        <TransformGroup>
                                            <TranslateTransform  X="18"
                                                                 Y="47"></TranslateTransform>
                                        </TransformGroup>
                                    </Thumb.RenderTransform>
                                </Thumb>
                            </Canvas>
                            <Thumb x:Name="HorizontalThumb"
                                   Background="{TemplateBinding Background}"
                                   MinWidth="0"
                                   Width="18"
                                   Grid.Column="3"
                                   Template="{StaticResource HorizontalThumbTemplate}"
                                   RenderTransformOrigin="0.5,0.5">
                                <Thumb.RenderTransform>
                                    <TransformGroup>
                                        <TranslateTransform></TranslateTransform>
                                        <ScaleTransform></ScaleTransform>
                                    </TransformGroup>
                                </Thumb.RenderTransform>
                            </Thumb>
                            <Canvas Grid.Column="4"
                                    Canvas.ZIndex="1">
                                <ContentControl x:Name="HorizontalFarHandContent"
                                                Visibility="Collapsed">
                                    <ContentPresenter></ContentPresenter>
                                </ContentControl>
                                <Thumb x:Name="HorizontalThumbHand2"
                                       Background="{TemplateBinding Background}"
                                       MinWidth="10"
                                       Template="{StaticResource HorizontalPositionThumbTemplate}"
                                       Opacity="0"
                                       RenderTransformOrigin="0.5,0.5">
                                    <Thumb.RenderTransform>
                                        <TransformGroup>
                                            <TranslateTransform  X="17"
                                                                 Y="47"></TranslateTransform>
                                        </TransformGroup>
                                    </Thumb.RenderTransform>
                                </Thumb>
                            </Canvas>
                            <RepeatButton x:Name="HorizontalLargeIncrease"
                                          Grid.Column="5"
                                          Interval="50"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          IsTabStop="False" />
                            <RepeatButton x:Name="HorizontalSmallIncrease"
                                          Visibility="{TemplateBinding ScrollButtonVisibility}"
                                          Grid.Column="6"
                                          MinWidth="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Margin="1" />
                        </Grid>

                        <!-- Vertical Template -->
                        <Grid x:Name="VerticalRoot">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <!-- Track Layer -->
                            <Line Grid.ColumnSpan="7"
                                  Width="5"></Line>

                            <!-- Repeat Buttons + Thumb -->
                            <RepeatButton x:Name="VerticalSmallDecrease"
                                          Visibility="Collapsed"
                                          Grid.Row="6"
                                          MinHeight="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Margin="1" />
                            <RepeatButton x:Name="VerticalLargeDecrease"
                                          Grid.Row="5"
                                          Height="0"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          Interval="50"
                                          IsTabStop="False" />
                            <Canvas Grid.Row="4"
                                    Canvas.ZIndex="1">
                                <ContentControl x:Name="VerticalNearHandContent"
                                                Visibility="Collapsed">
                                    <ContentPresenter>
                                    </ContentPresenter>
                                </ContentControl>
                                <Thumb x:Name="VerticalThumbHand1"
                                       MinHeight="18"
                                       Template="{StaticResource VerticalPositionThumbTemplate}"
                                       Opacity="0"
                                       RenderTransformOrigin="0.5,0.5">
                                    <Thumb.RenderTransform>
                                        <TransformGroup>
                                            <TranslateTransform  X="-32"
                                                                 Y="30"></TranslateTransform>
                                        </TransformGroup>
                                    </Thumb.RenderTransform>
                                </Thumb>
                            </Canvas>
                            <Thumb x:Name="VerticalThumb"
                                   MinHeight="0"
                                   Height="18"
                                   Grid.Row="3"
                                   Template="{StaticResource VerticalThumbTemplate}"
                                   RenderTransformOrigin="0.5,0.5">
                                <Thumb.RenderTransform>
                                    <TransformGroup>
                                        <TranslateTransform></TranslateTransform>
                                        <ScaleTransform></ScaleTransform>
                                    </TransformGroup>
                                </Thumb.RenderTransform>
                            </Thumb>
                            <Canvas Grid.Row="2"
                                    Canvas.ZIndex="1">
                                <ContentControl x:Name="VerticalFarHandContent"
                                                Visibility="Collapsed">
                                    <ContentPresenter>
                                    </ContentPresenter>
                                </ContentControl>
                                <Thumb  x:Name="VerticalThumbHand2"
                                        MinHeight="18"
                                        Template="{StaticResource VerticalPositionThumbTemplate}"
                                        Opacity="0"
                                        RenderTransformOrigin="0.5,0.5">
                                    <Thumb.RenderTransform>
                                        <TranslateTransform  X="-32"
                                                             Y="31"></TranslateTransform>
                                    </Thumb.RenderTransform>
                                </Thumb>
                            </Canvas>
                            <RepeatButton x:Name="VerticalLargeIncrease"
                                          Grid.Row="1"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          Interval="50"
                                          IsTabStop="False"
                                          Opacity="0" />
                            <RepeatButton x:Name="VerticalSmallIncrease"
                                          Grid.Row="0"
                                          Visibility="Collapsed"
                                          MinHeight="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Margin="1" />
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfChartResizableBarOpposedStyle"
           TargetType="local:SfChartResizableBar">
        <Setter Property="Background"
                Value="{StaticResource ContentBackgroundAlt2}"></Setter>
        <Setter Property="MinWidth"
                Value="3" />
        <Setter Property="MinHeight"
                Value="3" />
        <Setter Property="IsTabStop"
                Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate  TargetType="local:SfChartResizableBar">
                    <Grid x:Name="Root">
                        <Grid.Resources>
                            <ControlTemplate x:Key="HorizontalPositionThumbTemplate"
                                             TargetType="Thumb">
                                <Grid x:Name="Root"
                                      Width="35">
                                    <Grid.RowDefinitions>
                                        <RowDefinition></RowDefinition>
                                        <RowDefinition></RowDefinition>
                                    </Grid.RowDefinitions>
                                    <TextBlock Text="&#xe745;"
                                          Foreground="{StaticResource BorderAlt}"
                                          FontSize="18"
                                          FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                          Grid.Row="0"
                                          Margin="0,0,0,-2" />
                                    <TextBlock Text="&#xe708;"
                                               Foreground="{StaticResource BorderAlt}"
                                               FontSize="14"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               UseLayoutRounding="False"
                                               Grid.Row="1">
                                    </TextBlock>
                                </Grid>
                            </ControlTemplate>
                           
                            <ControlTemplate x:Key="VerticalThumbTemplate"
                                             TargetType="Thumb">
                                <Grid x:Name="Root"
                                      Margin="-0.2,0,0,0"
                                      Width="10"
                                      Background="Transparent">
                                    <Rectangle Width="6"
                                               x:Name="Background"
                                               Fill="{StaticResource ContentBackgroundAlt2}">
                                    </Rectangle>
                                </Grid>
                            </ControlTemplate>

                            <ControlTemplate x:Key="VerticalPositionThumbTemplate"
                                             TargetType="Thumb">
                                <Grid x:Name="Root"
                                      Width="62"
                                      RenderTransformOrigin="0.5,0.5">
                                    <Grid.RowDefinitions>
                                        <RowDefinition></RowDefinition>
                                        <RowDefinition></RowDefinition>
                                    </Grid.RowDefinitions>
                                    <TextBlock Text="&#xe745;"
                                          Foreground="{StaticResource BorderAlt}"
                                          FontSize="18"
                                          FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                          Grid.Row="0" />
                                    <TextBlock Text="&#xe708;"
                                          Foreground="{StaticResource BorderAlt}"
                                          FontSize="14"
                                          FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                          UseLayoutRounding="False"
                                          Grid.Row="1"
                                          Margin="0,-28,0,0"></TextBlock>
                                    <Grid.RenderTransform>
                                        <RotateTransform Angle="90"></RotateTransform>
                                    </Grid.RenderTransform>
                                </Grid>
                            </ControlTemplate>
                            
                        </Grid.Resources>

                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="MouseOver" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="Root"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.5"
                                                         Duration="0" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="TouchMode">
                                <VisualState x:Name="OnView">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0"
                                                                  Value="-1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0"
                                                                  Value="4">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0"
                                                                  Value="0.3">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0"
                                                                  Value="3">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="HorizontalThumbHand1">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.6"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="HorizontalThumbHand2">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.6"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="VerticalThumbHand1">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.6"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="VerticalThumbHand2">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.6"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="OnFocus">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="-1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0.5" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="4">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0.3">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0"
                                                                  Value="0.5" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="3">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="OnLostFocus">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="HorizontalThumbHand1">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="HorizontalThumbHand2">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="VerticalThumbHand1">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                       Storyboard.TargetName="VerticalThumbHand2">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="OnExit">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                                                                       Storyboard.TargetName="VerticalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                                                                       Storyboard.TargetName="HorizontalThumb">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                                                                  Value="1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>

                        <Grid x:Name="HorizontalRoot"
                              Visibility="Collapsed">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!-- Track Layer -->
                            <Line Grid.ColumnSpan="7"
                                  Height="5"></Line>
                            <!-- Repeat Buttons + Thumb -->
                            <RepeatButton x:Name="HorizontalSmallDecrease"
                                          Visibility="Collapsed"
                                          Grid.Column="0"
                                          MinWidth="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Margin="1" />
                            <RepeatButton x:Name="HorizontalLargeDecrease"
                                          Grid.Column="1"
                                          Width="0"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          Interval="50"
                                          IsTabStop="False" />
                            <Canvas Grid.Column="2"
                                    Canvas.ZIndex="1">
                                <ContentControl x:Name="HorizontalNearHandContent"
                                                Visibility="Collapsed">
                                    <ContentPresenter></ContentPresenter>
                                </ContentControl>
                                <Thumb x:Name="HorizontalThumbHand1"
                                       Background="{TemplateBinding Background}"
                                       MinWidth="10"
                                       Template="{StaticResource HorizontalPositionThumbTemplate}"
                                       Opacity="0"
                                       RenderTransformOrigin="0.5,0.5">
                                    <Thumb.RenderTransform>
                                        <TransformGroup>
                                            <TranslateTransform  X="-17"
                                                                 Y="-32"></TranslateTransform>
                                        </TransformGroup>
                                    </Thumb.RenderTransform>
                                </Thumb>
                            </Canvas>
                            <Thumb x:Name="HorizontalThumb"
                                   Background="{TemplateBinding Background}"
                                   MinWidth="0"
                                   Width="18"
                                   Grid.Column="3"
                                   Template="{StaticResource HorizontalThumbTemplate}"
                                   RenderTransformOrigin="0.5,0.5">
                                <Thumb.RenderTransform>
                                    <TransformGroup>
                                        <TranslateTransform></TranslateTransform>
                                        <ScaleTransform></ScaleTransform>
                                    </TransformGroup>
                                </Thumb.RenderTransform>
                            </Thumb>
                            <Canvas Grid.Column="4"
                                    Canvas.ZIndex="1">
                                <ContentControl x:Name="HorizontalFarHandContent"
                                                Visibility="Collapsed">
                                    <ContentPresenter></ContentPresenter>
                                </ContentControl>
                                <Thumb x:Name="HorizontalThumbHand2"
                                       Background="{TemplateBinding Background}"
                                       MinWidth="10"
                                       Template="{StaticResource HorizontalPositionThumbTemplate}"
                                       Opacity="0"
                                       RenderTransformOrigin="0.5,0.5">
                                    <Thumb.RenderTransform>
                                        <TransformGroup>
                                            <TranslateTransform  X="-18"
                                                                 Y="-32"></TranslateTransform>
                                        </TransformGroup>
                                    </Thumb.RenderTransform>
                                </Thumb>
                            </Canvas>
                            <RepeatButton x:Name="HorizontalLargeIncrease"
                                          Grid.Column="5"
                                          Interval="50"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          IsTabStop="False" />
                            <RepeatButton x:Name="HorizontalSmallIncrease"
                                          Visibility="Collapsed"
                                          Grid.Column="6"
                                          MinWidth="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Margin="1" />
                        </Grid>

                        <!-- Vertical Template -->
                        <Grid x:Name="VerticalRoot">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <!-- Track Layer -->
                            <Line Grid.ColumnSpan="7"
                                  Width="5"></Line>

                            <!-- Repeat Buttons + Thumb -->
                            <RepeatButton x:Name="VerticalSmallDecrease"
                                          Visibility="Collapsed"
                                          Grid.Row="6"
                                          MinHeight="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Margin="1" />
                            <RepeatButton x:Name="VerticalLargeDecrease"
                                          Grid.Row="5"
                                          Height="0"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          Interval="50"
                                          IsTabStop="False" />
                            <Canvas Grid.Row="4"
                                    Canvas.ZIndex="1">
                                <ContentControl x:Name="VerticalNearHandContent"
                                                Visibility="Collapsed">
                                    <ContentPresenter>
                                    </ContentPresenter>
                                </ContentControl>
                                <Thumb x:Name="VerticalThumbHand1"
                                       MinHeight="10"
                                       Template="{StaticResource VerticalPositionThumbTemplate}"
                                       Opacity="0">
                                    <Thumb.RenderTransform>
                                        <TranslateTransform X="-15"
                                                            Y="-31"></TranslateTransform>
                                    </Thumb.RenderTransform>
                                </Thumb>
                            </Canvas>
                            <Thumb x:Name="VerticalThumb"
                                   MinHeight="0"
                                   Height="18"
                                   Width="4"
                                   Grid.Row="3"
                                   Template="{StaticResource VerticalThumbTemplate}"
                                   RenderTransformOrigin="0.5,0.5">
                                <Thumb.RenderTransform>
                                    <TransformGroup>
                                        <TranslateTransform></TranslateTransform>
                                        <ScaleTransform></ScaleTransform>
                                    </TransformGroup>
                                </Thumb.RenderTransform>
                            </Thumb>
                            <Canvas Grid.Row="2"
                                    Canvas.ZIndex="1">
                                <ContentControl x:Name="VerticalFarHandContent"
                                                Visibility="Collapsed">
                                    <ContentPresenter>
                                    </ContentPresenter>
                                </ContentControl>
                                <Thumb x:Name="VerticalThumbHand2"
                                       MinHeight="10"
                                       Template="{StaticResource VerticalPositionThumbTemplate}"
                                       Opacity="0">
                                    <Thumb.RenderTransform>
                                        <TranslateTransform X="-15"
                                                            Y="-30"></TranslateTransform>
                                    </Thumb.RenderTransform>
                                </Thumb>
                            </Canvas>
                            <RepeatButton x:Name="VerticalLargeIncrease"
                                          Grid.Row="1"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          Interval="50"
                                          IsTabStop="False"
                                          Opacity="0" />
                            <RepeatButton x:Name="VerticalSmallIncrease"
                                          Grid.Row="0"
                                          Visibility="Collapsed"
                                          MinHeight="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Margin="1" />
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionChartAxisBase2DStyle"  TargetType="local:ChartAxisBase2D">
        <Setter Property="MajorTickLineStyle"
                Value="{StaticResource ticklineStyle}"></Setter>
        <Setter Property="MinorTickLineStyle"
                Value="{StaticResource ticklineStyle}"></Setter>
        <Setter Property="MajorGridLineStyle"
                Value="{StaticResource gridLineStyle}"></Setter>
        <Setter Property="MinorGridLineStyle"
                Value="{StaticResource gridLineStyle}"></Setter>
        <Setter Property="AxisLineStyle"
                Value="{StaticResource SyncfusionChartAxisLineStyle}"></Setter>
        <Setter Property="TrackBallLabelTemplate"
                Value="{StaticResource axisTrackBallLabel}"></Setter>
        <Setter Property="LabelStyle">
            <Setter.Value>
                <local:LabelStyle Foreground="{StaticResource ContentForeground}"
                                  FontSize="{StaticResource Windows11Dark.CaptionText}" />
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:ChartAxisBase2D">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="StyleMode">
                                <VisualState x:Name="CommonStyle">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="sfchartResizableBar"
                                                                       Storyboard.TargetProperty="(FrameworkElement.Style)">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{StaticResource SyncfusionResizableScrollBarStyle}"></DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="sfchartResizableBar"
                                                                       Storyboard.TargetProperty="ScrollButtonVisibility">
                                            <DiscreteObjectKeyFrame KeyTime="00:00:00">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Visible</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="TouchModeStyle">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="sfchartResizableBar"
                                                                       Storyboard.TargetProperty="(FrameworkElement.Style)">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{StaticResource SyncfusionSfChartResizableBarStyle}"></DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="sfchartResizableBar"
                                                                       Storyboard.TargetProperty="ScrollButtonVisibility">
                                            <DiscreteObjectKeyFrame KeyTime="00:00:00">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="OpposedTouchModeStyle">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="sfchartResizableBar"
                                                                       Storyboard.TargetProperty="(FrameworkElement.Style)">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{StaticResource SyncfusionSfChartResizableBarOpposedStyle}"></DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="sfchartResizableBar"
                                                                       Storyboard.TargetProperty="ScrollButtonVisibility">
                                            <DiscreteObjectKeyFrame KeyTime="00:00:00">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                VerticalAlignment="Stretch"
                                HorizontalAlignment="Stretch">
                            <local:ChartCartesianAxisPanel x:Name="axisPanel"
                                                           Background="Transparent">
                                <ContentControl x:Name="headerContent"
                                                Content="{TemplateBinding Header}"
                                                ContentTemplate="{TemplateBinding HeaderTemplate}"
                                                RenderTransformOrigin="0.5,0.5"></ContentControl>
                                <Canvas x:Name="axisMultiLevelLabelsPanels"></Canvas>
                                <Canvas x:Name="axisLabelsPanel"></Canvas>
                                <Canvas x:Name="axisElementPanel"></Canvas>
                                <local:SfChartResizableBar EnableTouchMode="{TemplateBinding EnableTouchMode}"
                                                           x:Name="sfchartResizableBar"></local:SfChartResizableBar>
                            </local:ChartCartesianAxisPanel>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionChartAxisBase2DStyle}"
        TargetType="local:NumericalAxis" />
    <Style BasedOn="{StaticResource SyncfusionChartAxisBase2DStyle}"
           TargetType="local:CategoryAxis" />
    <Style BasedOn="{StaticResource SyncfusionChartAxisBase2DStyle}"
           TargetType="local:LogarithmicAxis" />
    <Style BasedOn="{StaticResource SyncfusionChartAxisBase2DStyle}"
           TargetType="local:TimeSpanAxis"/>
    <Style BasedOn="{StaticResource SyncfusionChartAxisBase2DStyle}"
           TargetType="local:DateTimeAxis"/>
    <Style BasedOn="{StaticResource SyncfusionChartAxisBase2DStyle}"
           TargetType="local:DateTimeCategoryAxis"/>
    
</ResourceDictionary>
