<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:Sync_Resources="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:tools_controls_shared="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatPrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/PrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphDropdownExpander.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphEditableDropdownExpander.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="ComboBoxAdv.Static.Border" Color="#c8c8c8"/>

    <SolidColorBrush x:Key="ComboBoxAdv.MouseOver.Border" Color="#179bd7"/>

    <SolidColorBrush x:Key="ComboBoxAdv.Pressed.Border" Color="#0279FF"/>
    
    <BooleanToVisibilityConverter x:Key="VisiblityConverter" />
    <tools_controls_shared:TemplateToVisibilityConverter x:Key="dropDownTemplateConvertor"/>

    <LinearGradientBrush x:Key="ComboBoxAdv.Static.BorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ComboBoxAdv.Static.BorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ComboBoxAdv.Static.BorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="SyncfusionComboBoxItemFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle SnapsToDevicePixels="true" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="CloseTokenButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                     <Border Background="{TemplateBinding Background}">
                        <Path Width="8" x:Name="Token_Path" Height="8" Margin="3,2,3,2" HorizontalAlignment="Right" VerticalAlignment="Center" Fill="{StaticResource SecondaryForeground}" SnapsToDevicePixels="True" Stretch="Fill">
                            <Path.Data>
                                <PathGeometry>M0.70800017,0 L4.498001,3.7964015 8.2880024,0 8.9960006,0.70600033 5.2044057,4.5039992 8.9960006,8.3019981 8.2880024,9.0079994 4.498001,5.2115974 0.70800017,9.0079994 0,8.3019981 3.7915958,4.5039992 0,0.70600033 z</PathGeometry>
                            </Path.Data>
                        </Path>
                    </Border>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding IsMouseOver, ElementName=Token_CloseButton}" Value="True">
                            <Setter Property="Fill" Value="{StaticResource SecondaryForegroundHovered}" TargetName="Token_Path"/>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type tools_controls_shared:ComboBoxTokenItem}">
        <Setter Property="Focusable" Value="False"></Setter>
		<Setter Property="Foreground" Value="{StaticResource SecondaryForeground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource SecondaryBorder}"/>
        <Setter Property="Background" Value="{StaticResource SecondaryBackground}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls_shared:ComboBoxTokenItem}">
                    <Border x:Name="rootBorder" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="1" Background="{TemplateBinding Background}" CornerRadius="4" Margin="2" Padding="3">
                        <Grid HorizontalAlignment="Left">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="24"/>
                            </Grid.ColumnDefinitions>
                            <ContentPresenter x:Name="Token_ContentPresenter" Content="{TemplateBinding Content}" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Center" Margin="4,0,0,0"></ContentPresenter>
                            <Button Grid.Column="1" x:Name="Token_CloseButton" Focusable="False" Background="Transparent" HorizontalAlignment="Right" Style="{StaticResource CloseTokenButtonStyle}" Padding="1"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding IsMouseOver, ElementName=Token_CloseButton}" Value="True">
                            <Setter Property="Cursor" TargetName="Token_CloseButton" Value="Hand"/>
                            <Setter Property="Background" TargetName="Token_CloseButton" Value="{StaticResource SecondaryBackgroundHovered}"/>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="MultiSelectTemplate" TargetType="tools_controls_shared:ComboBoxAdv">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition MinWidth="{StaticResource {x:Static SystemParameters.VerticalScrollBarWidthKey}}" 
                                Width="0" />
            </Grid.ColumnDefinitions>
            <Popup x:Name="PART_Popup" 
                               AllowsTransparency="true" 
                               Grid.ColumnSpan="2" 
                               StaysOpen="False" 
                               IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}" 
                               Placement="Bottom"
                               SnapsToDevicePixels="True">

                <Border x:Name="DropDownBorder" 
                                      MaxHeight="{TemplateBinding MaxDropDownHeight}" 
                                      MinWidth="{TemplateBinding ActualWidth}"
                                      BorderBrush="{StaticResource BorderAlt}" 
                                      CornerRadius="0 0 4 4"
                                      BorderThickness="{StaticResource Windows11Light.BorderThickness1}" 
                                      Background="{StaticResource PopupBackground}"
                                      Effect="{StaticResource Default.ShadowDepth4}">
                    <Border.Margin>
                        <Thickness>16,1,16,16</Thickness>
                    </Border.Margin>
                    <Border.Padding>
                        <Thickness>0,4,0,4</Thickness>
                    </Border.Padding>

                    <Grid>
                        <Grid Visibility="{TemplateBinding DropDownContentTemplate, Converter={StaticResource dropDownTemplateConvertor}, ConverterParameter='Item_Presenter'}">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <ScrollViewer x:Name="DropDownScrollViewer" Background="{StaticResource PopupBackground}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>
                                    <Canvas HorizontalAlignment="Left"
                                                        VerticalAlignment="Top" 
                                                        Height="0" 
                                                        Width="0">
                                        <Rectangle x:Name="OpaqueRect" 
                                                              Fill="{Binding Background, ElementName=DropDownBorder}" 
                                                              Height="{Binding ActualHeight, ElementName=DropDownBorder}" 
                                                              Width="{Binding ActualWidth, ElementName=DropDownBorder}" />
                                    </Canvas>
                                    <ItemsPresenter x:Name="ItemsPresenter" 
                                                                Grid.Row="1"
                                                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" ClipToBounds="True"/>
                                    <tools_controls_shared:ComboBoxItemAdv
                                                               x:Name="PART_SelectAll"
                                                               Grid.Row="0"
                                                               Margin="4 2 4 2"
                                                               Visibility="{TemplateBinding AllowSelectAll, Converter={StaticResource VisiblityConverter}}"
                                                               Content="{Sync_Resources:SharedLocalizationResourceExtension ResourceName=SelectAll}"/>
                                    <Border Grid.RowSpan="1" 
                                            SnapsToDevicePixels="True"
                                            Visibility="Visible"
                                            BorderThickness="0,0,0,1"
                                            BorderBrush="{StaticResource BorderAlt}"/>
                                      <TextBlock x:Name="No_Records" 
                                                               Text="{shared:SharedLocalizationResourceExtension ResourceName=NoRecords}"
                                                               Focusable="False"
                                                               Padding="3"                                                               
                                                               Background="{Binding Background, ElementName=DropDownBorder}"
                                                               Visibility="Collapsed"/>
                                </Grid>
                            </ScrollViewer>
                            <Separator Grid.Row="1"
                                                   Height="1"
                                                   Visibility="{Binding Path=EnableOKCancel, Converter={StaticResource VisiblityConverter}, RelativeSource={RelativeSource AncestorType={x:Type tools_controls_shared:ComboBoxAdv}}}" />
                            <StackPanel Grid.Row="2"
                                                    Orientation="Horizontal"
                                                    Margin="4"
                                                    Visibility="{TemplateBinding EnableOKCancel, Converter={StaticResource VisiblityConverter}}"
                                                    HorizontalAlignment="Right">
                                <Button x:Name="PART_OKButton"
                                                    Content="{Sync_Resources:SharedLocalizationResourceExtension ResourceName=OKText}"
                                                    Margin="0,0,10,0"
                                                    Width="70"
                                                    Style="{StaticResource WPFPrimaryButtonStyle}"
                                                    Height="{StaticResource Windows11Light.MinHeight}"/>
                                <Button x:Name="PART_CancelButton"
                                                    Content="{Sync_Resources:SharedLocalizationResourceExtension ResourceName=CancelText}"
                                                    Width="70" 
                                                    Style="{StaticResource WPFButtonStyle}"
                                                    Height="{StaticResource Windows11Light.MinHeight}"/>
                            </StackPanel>
                        </Grid>
                        <ContentControl 
                                                    Visibility="{TemplateBinding DropDownContentTemplate, Converter={StaticResource dropDownTemplateConvertor}, ConverterParameter='Content_Control'}"
                                                    ContentTemplate="{TemplateBinding DropDownContentTemplate}" />
                    </Grid>
                </Border>
            </Popup>
            <Border 
                                Grid.ColumnSpan="2"
                                Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"/>
            <ToggleButton x:Name="PART_ToggleButton" 
                                      Grid.ColumnSpan="2"
                                      IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"                                      
                                      Style="{StaticResource WPFGlyphDropdownExpanderStyle}"
                                      HorizontalContentAlignment="Right"
                                      Margin="0,0,5,0"/>

            <ContentPresenter x:Name="ContentPresenter" 
                                          ContentTemplate="{TemplateBinding SelectionBoxTemplate}" 
                                          ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"                                
                                          Content="{TemplateBinding SelectionBoxItem}" 
                                          ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}" 
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          IsHitTestVisible="false" 
                                          Margin="{TemplateBinding Padding}" 
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />

            <ItemsControl x:Name="PART_SelectedItems" 
									  Focusable="False"
                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}" 
                                      IsHitTestVisible="false"
                                      Margin="{TemplateBinding Padding}"                          
                                      IsTabStop="False">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <StackPanel Orientation="Horizontal" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
            </ItemsControl>

            <TextBlock x:Name="PART_DefaultText"
                                   Text="{TemplateBinding DefaultText}"
                                   HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                   VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                   IsHitTestVisible="false" 
                                   Margin="{TemplateBinding Padding}"                                       
                                   Opacity="0.5"/>

            <Border x:Name="PART_Border" 
                    CornerRadius="4 0 0 4" 
                    Margin="0 0 0 1" 
                    Background="{StaticResource ContentBackground}" 
                    BorderBrush="Transparent" 
                    BorderThickness="1,1,0,1" 
                    Visibility="Collapsed">
                <ItemsControl  x:Name="PART_TokenItems" Focusable="False" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}" DisplayMemberPath="{Binding DisplayMemberPath, RelativeSource={RelativeSource Mode=TemplatedParent}}">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel Orientation="Horizontal" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.Items>
                        <TextBox  x:Name="PART_Editable" 
					              Focusable="True"
                                  FontSize="{TemplateBinding FontSize}"
                                  FontFamily="{TemplateBinding FontFamily}"
                                  Foreground="{TemplateBinding Foreground}"
                                  FontWeight="{TemplateBinding FontWeight}"
                                  BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                  Margin="0 0 0 0"
                                  Padding="2,0,2,0"
                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                  IsReadOnly="{Binding IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}"
                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                  Style="{StaticResource WPFBorderlessTextBoxStyle}">
                        </TextBox>
                    </ItemsControl.Items>
                </ItemsControl>
            </Border>
            
            <TextBox  x:Name="PART_IsEditDefaultText" Text="{TemplateBinding DefaultText}" 
                      BorderThickness="0" 
                      FontSize="{TemplateBinding FontSize}"
                      FontFamily="{TemplateBinding FontFamily}"
                      Foreground="{TemplateBinding Foreground}"
                      FontWeight="{TemplateBinding FontWeight}"
                      Visibility="Collapsed" Opacity="0.5" 
                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                      Style="{StaticResource WPFBorderlessTextBoxStyle}"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="EnableToken" Value="True"/>
                    <Condition Property="IsEditable" Value="True"/>
                </MultiTrigger.Conditions>
                <Setter TargetName="PART_Editable" 
                                     Property="Visibility" 
                                     Value="Visible" />
                <Setter TargetName="PART_DefaultText" 
                                     Property="Visibility" 
                                     Value="Collapsed" />
                <Setter TargetName="ContentPresenter" 
                                     Property="Visibility" 
                                     Value="Collapsed" />
                <Setter TargetName="PART_IsEditDefaultText"
                                    Property="Visibility"
                                    Value="Visible" />
                <Setter TargetName="PART_SelectedItems" Property="MinHeight" Value="30"/>
                <Setter TargetName="PART_ToggleButton" Property="Style" Value="{StaticResource WPFGlyphEditableDropdownExpanderStyle}"/>
                <Setter TargetName="PART_ToggleButton" Property="Width" Value="20"/>
                <Setter TargetName="PART_ToggleButton" Property="Margin" Value="0"/>
                <Setter TargetName="PART_ToggleButton" Property="Padding">
                    <Setter.Value>
                        <Thickness>0,0,2,2</Thickness>
                    </Setter.Value>
                </Setter>
                <Setter TargetName="PART_ToggleButton" Property="HorizontalAlignment" Value="Right"/>
                <Setter TargetName="PART_ToggleButton" Property="HorizontalContentAlignment" Value="Center"/>
            </MultiTrigger>
            <Trigger Property="EnableToken" Value="True">
                <Setter TargetName="PART_SelectedItems" Property="Focusable" Value="False"/>
                <Setter TargetName="PART_Border" Property="Visibility" Value="Visible"/>
                <Setter TargetName="PART_DefaultText" Property="Visibility" Value="Collapsed"/>
                <Setter TargetName="PART_IsEditDefaultText" Property="Visibility" Value="Visible" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="EnableToken" Value="True"/>
                    <Condition Property="IsEditable" Value="False"/>
                </MultiTrigger.Conditions>
                <Setter TargetName="PART_Border" Property="Visibility" Value="Collapsed"/>
                <Setter TargetName="PART_DefaultText" Property="Visibility" Value="Visible"/>
                <Setter TargetName="PART_IsEditDefaultText" Property="Visibility" Value="Collapsed" />
            </MultiTrigger>
             <Trigger Property="IsMouseOver" 
                                  Value="True">
                            <Setter Property="Foreground"
                                    Value="{StaticResource ContentForeground}" />
                            <Setter Property="BorderBrush" 
                                    Value="{StaticResource ComboBoxAdv.Static.BorderBrushHovered}" />
                            <Setter Property="Background" 
                                    Value="{StaticResource ContentBackgroundAlt5}" />
                        </Trigger>
            <Trigger Property="IsFocused" SourceName="PART_Editable" Value="True">
                <Setter Property="Foreground" 
                                    Value="{StaticResource ContentForeground}" />
                <Setter Property="BorderBrush" 
                                    Value="{StaticResource BorderAlt3}" />
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                <Setter Property="BorderThickness" TargetName="PART_Border" Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant2}"/>
            </Trigger>
            <Trigger Property="IsFocused" 
                                 Value="True">
                <Setter Property="Foreground" 
                                    Value="{StaticResource ContentForeground}" />
                <Setter Property="BorderBrush" 
                                    Value="{StaticResource ComboBoxAdv.Static.BorderBrushFocused}" />
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                <Setter Property="BorderThickness" TargetName="PART_Border" Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant2}"/>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsDropDownOpen" Value="True"/>
                    <Condition SourceName="PART_Border" Property="Visibility" Value="Collapsed"/>
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" 
                                    Value="{StaticResource ContentForeground}" />
                <Setter Property="BorderBrush" 
                                    Value="{StaticResource ComboBoxAdv.Static.BorderBrush}" />
                <Setter Property="Background" 
                                    Value="{StaticResource ContentBackground}" />
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
            </MultiTrigger> 
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SyncfusionComboBoxAdvStyle"
       TargetType="{x:Type tools_controls_shared:ComboBoxAdv}">
        <Setter Property="FocusVisualStyle" 
                Value="{x:Null}" />
        <Setter Property="BorderBrush"
            Value="{StaticResource ComboBoxAdv.Static.BorderBrush}" />
        <Setter Property="Background"
            Value="{StaticResource ContentBackground}" />
        <Setter Property="Foreground"
            Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" 
            Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="BorderThickness"
                Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility"
            Value="Auto" />
        <Setter Property="Padding">
            <Setter.Value>
                <Thickness>6,2,2,2</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility"
            Value="Auto" />
        <Setter Property="VerticalContentAlignment" 
            Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls_shared:ComboBoxAdv}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition MinWidth="{StaticResource {x:Static SystemParameters.VerticalScrollBarWidthKey}}" 
                                Width="0" />
                        </Grid.ColumnDefinitions>

                        <Popup x:Name="PART_Popup" 
                               AllowsTransparency="true" 
                               Grid.ColumnSpan="2" 
                               StaysOpen="False" 
                               IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}" 
                               Placement="Bottom"
                               SnapsToDevicePixels="True">

                            <Border x:Name="DropDownBorder" 
                                      MaxHeight="{TemplateBinding MaxDropDownHeight}" 
                                      MinWidth="{TemplateBinding ActualWidth}"
                                      BorderBrush="{StaticResource BorderAlt}" 
                                      CornerRadius="0 0 4 4"
                                      BorderThickness="{StaticResource Windows11Light.BorderThickness1}" 
                                      Background="{StaticResource PopupBackground}"
                                      Effect="{StaticResource Default.ShadowDepth4}">
                                <Border.Margin>
                                    <Thickness>16,1,16,16</Thickness>
                                </Border.Margin>
                                <Border.Padding>
                                    <Thickness>0,4,0,4</Thickness>
                                </Border.Padding>
                                <Border.RenderTransform>
                                    <TranslateTransform/>
                                </Border.RenderTransform>
                                <Border.Triggers>
                                    <EventTrigger RoutedEvent="Border.Loaded">
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimationUsingKeyFrames
                                                    BeginTime="00:00:00"
                                                    Storyboard.TargetProperty="(RenderTransform).(TranslateTransform.Y)">
                                                    <SplineDoubleKeyFrame
                                                       KeyTime="00:00:00.01"
                                                       Value="-800" />
                                                    <SplineDoubleKeyFrame
                                                       KeyTime="00:00:0.2"
                                                       Value="0"
                                                       KeySpline="0.0, 0.1, 0.0, 1.0" />
                                                </DoubleAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger>
                                </Border.Triggers>
                                <Grid>
                                    <Grid Visibility="{TemplateBinding DropDownContentTemplate, Converter={StaticResource dropDownTemplateConvertor}, ConverterParameter='Item_Presenter'}">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <ScrollViewer x:Name="DropDownScrollViewer" Background="{StaticResource PopupBackground}">
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="*" />
                                                </Grid.RowDefinitions>
                                                <Canvas HorizontalAlignment="Left"
                                                        VerticalAlignment="Top" 
                                                        Height="0" 
                                                        Width="0">
                                                    <Rectangle x:Name="OpaqueRect" 
                                                              Fill="{Binding Background, ElementName=DropDownBorder}" 
                                                              Height="{Binding ActualHeight, ElementName=DropDownBorder}" 
                                                              Width="{Binding ActualWidth, ElementName=DropDownBorder}" />
                                                </Canvas>
                                                <ItemsPresenter x:Name="ItemsPresenter" 
                                                                Grid.Row="1"
                                                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" ClipToBounds="True"/>
                                                <TextBlock x:Name="No_Records" 
                                                               Text="{shared:SharedLocalizationResourceExtension ResourceName=NoRecords}"
                                                               Focusable="False"
                                                               Padding="3"                                                               
                                                               Background="{Binding Background, ElementName=DropDownBorder}"
                                                               Visibility="Collapsed"/>
                                            </Grid>
                                        </ScrollViewer>
                                    </Grid>
                                    <ContentControl 
                                                    Visibility="{TemplateBinding DropDownContentTemplate, Converter={StaticResource dropDownTemplateConvertor}, ConverterParameter='Content_Control'}"
                                                    ContentTemplate="{TemplateBinding DropDownContentTemplate}" />
                                </Grid>
                            </Border>
                        </Popup>
                        <Border 
                                Grid.ColumnSpan="2"
                                Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"/>
                        <ToggleButton x:Name="PART_ToggleButton" 
                                      Grid.ColumnSpan="2"
                                      IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"                                      
                                      Style="{StaticResource WPFGlyphDropdownExpanderStyle}"
                                      HorizontalContentAlignment="Right"
                                      Margin="0,0,5,0"/>

                        <ContentPresenter x:Name="ContentPresenter" 
                                          ContentTemplate="{TemplateBinding SelectionBoxTemplate}" 
                                          ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"                                
                                          Content="{TemplateBinding SelectionBoxItem}" 
                                          ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}" 
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          IsHitTestVisible="false" 
                                          Margin="{TemplateBinding Padding}" 
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" >
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>

                        <TextBlock x:Name="PART_DefaultText"
                                   Text="{TemplateBinding DefaultText}"
                                   HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                   VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                   IsHitTestVisible="false" 
                                   Margin="{TemplateBinding Padding}"                                       
                                   Opacity="0.5"/>

                        <TextBox  x:Name="PART_Editable" 
                                  BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                  Visibility="Collapsed"
                                  FontSize="{TemplateBinding FontSize}"
                                  FontFamily="{TemplateBinding FontFamily}"
                                  Foreground="{TemplateBinding Foreground}"
                                  FontWeight="{TemplateBinding FontWeight}"
                                  Margin="4 0 0 0"
                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                  IsReadOnly="{Binding IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}"
                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                  Style="{StaticResource WPFBorderlessTextBoxStyle}">
                        </TextBox>

                        <TextBox  x:Name="PART_IsEditDefaultText"
                                  Text="{TemplateBinding DefaultText}" 
                                  BorderThickness="0"
                                  FontSize="{TemplateBinding FontSize}"
                                  FontFamily="{TemplateBinding FontFamily}"
                                  Foreground="{TemplateBinding Foreground}"
                                  FontWeight="{TemplateBinding FontWeight}"
                                  Visibility="Collapsed" Opacity="0.5" 
                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                  Style="{StaticResource WPFBorderlessTextBoxStyle}"/>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEditable" 
                                 Value="True">
                            <Setter TargetName="PART_Editable" 
                                     Property="Visibility" 
                                     Value="Visible" />
                            <Setter TargetName="PART_DefaultText" 
                                     Property="Visibility" 
                                     Value="Collapsed" />
                            <Setter TargetName="ContentPresenter" 
                                     Property="Visibility" 
                                     Value="Collapsed" />
                            <Setter TargetName="PART_IsEditDefaultText"
                                    Property="Visibility"
                                    Value="Visible" />
                            <Setter TargetName="PART_ToggleButton" Property="Style" Value="{StaticResource WPFGlyphEditableDropdownExpanderStyle}"/>
                            <Setter TargetName="PART_ToggleButton" Property="Width" Value="20"/>
                            <Setter TargetName="PART_ToggleButton" Property="Margin">
                                <Setter.Value>
                                    <Thickness>3</Thickness>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_ToggleButton" Property="Padding">
                                <Setter.Value>
                                    <Thickness>0,0,0,2</Thickness>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_ToggleButton" Property="HorizontalAlignment" Value="Right"/>
                            <Setter TargetName="PART_ToggleButton" Property="HorizontalContentAlignment" Value="Center"/>
                        </Trigger>                      
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=HasItems, RelativeSource={RelativeSource Self}}" Value="false"></Condition>
                                <Condition Binding="{Binding Path=AutoCompleteMode,RelativeSource={RelativeSource Self}}" Value="Suggest"></Condition>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Height" TargetName="DropDownBorder" Value="{Binding Height, ElementName=No_Records}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=HasItems,RelativeSource={RelativeSource Self}}" Value="false"></Condition>
                                <Condition Binding="{Binding Path=AutoCompleteMode,RelativeSource={RelativeSource Self}}" Value="None"></Condition>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="MinHeight" TargetName="DropDownBorder" Value="95" />
                        </MultiDataTrigger>
                        <Trigger Property="IsGrouping" 
                                 Value="true">
                            <Setter Property="ScrollViewer.CanContentScroll"
                                    Value="false" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" 
                                  Value="True">
                            <Setter Property="Foreground"
                                    Value="{StaticResource ContentForeground}" />
                            <Setter Property="BorderBrush" 
                                    Value="{StaticResource ComboBoxAdv.Static.BorderBrushHovered}" />
                            <Setter Property="Background" 
                                    Value="{StaticResource ContentBackgroundAlt5}" />
                        </Trigger>
                        <Trigger Property="IsFocused" SourceName="PART_Editable" Value="True">
                            <Setter Property="Foreground" 
                                    Value="{StaticResource ContentForeground}" />
                            <Setter Property="BorderBrush" 
                                    Value="{StaticResource ComboBoxAdv.Static.BorderBrushFocused}" />
                            <Setter Property="Background" 
                                    Value="{StaticResource ContentBackgroundAlt5}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" 
                                 Value="True">
                            <Setter Property="Foreground" 
                                    Value="{StaticResource ContentForeground}" />
                            <Setter Property="BorderBrush" 
                                    Value="{StaticResource ComboBoxAdv.Static.BorderBrushFocused}" />
                            <Setter Property="Background" 
                                    Value="{StaticResource ContentBackgroundAlt5}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                Value="false">
                            <Setter Property="Foreground"
                                    Value="{StaticResource DisabledForeground}" />
                            <Setter Property="BorderBrush" 
                                    Value="{StaticResource BorderAlt}" />
                            <Setter Property="Background" 
                                    Value="{StaticResource ContentBackgroundAlt6}" />
                        </Trigger>
                        <Trigger Property="IsDropDownOpen" 
                                  Value="True">
                            <Setter Property="Foreground" 
                                    Value="{StaticResource ContentForeground}" />
                            <Setter Property="BorderBrush" 
                                    Value="{StaticResource ComboBoxAdv.Static.BorderBrush}" />
                            <Setter Property="Background" 
                                    Value="{StaticResource ContentBackground}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="AllowMultiSelect"
                                           Value="True"/>
                                <Condition Property="IsEditable" 
                                           Value="True"/>
                                <Condition Property="EnableToken" 
                                           Value="False"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Editable" 
                                     Property="Visibility" 
                                     Value="Collapsed" />
                            <Setter TargetName="PART_DefaultText" 
                                     Property="Visibility" 
                                     Value="Visible" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>

            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default">
                <Setter Property="FocusVisualStyle" Value="{StaticResource DottedKeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="AllowMultiSelect" Value="True">
                <Setter Property="Template" Value="{StaticResource MultiSelectTemplate}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionComboBoxAdvStyle}" TargetType="{x:Type tools_controls_shared:ComboBoxAdv}" />

    <Style x:Key="SyncfusionComboBoxItemAdvStyle"
         TargetType="{x:Type tools_controls_shared:ComboBoxItemAdv}">
        <Setter Property="FocusVisualStyle" 
            Value="{StaticResource SyncfusionComboBoxItemFocusVisual}" />
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Padding">
            <Setter.Value>
                <Thickness>4,2,4,2</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="Margin">
            <Setter.Value>
                <Thickness>4,2,4,2</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" 
            Value="{StaticResource Windows11Light.BorderThickness}" />
        <Setter Property="Background" 
            Value="{StaticResource PopupBackground}" />
        <Setter Property="BorderBrush"
            Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" 
            Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" 
            Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="MinHeight" Value="{StaticResource Windows11Light.MinHeight1}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls_shared:ComboBoxItemAdv}">
                    <Grid>
                        <Border x:Name="SelectionIndicator"
                                        HorizontalAlignment="Left"            
                                        CornerRadius="1.5"
                                        Height="12"
                                        Width="2"
                                        Visibility="Collapsed"     
                                        Background="{StaticResource PrimaryBackground}" />

                        <Border 
                            BorderBrush="{TemplateBinding BorderBrush}" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            Background="{TemplateBinding Background}" 
                            Padding="{TemplateBinding Padding}" 
                            CornerRadius="4"
                            SnapsToDevicePixels="true">
                            <Grid Margin="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <CheckBox x:Name="PART_CheckBox"
                                       Margin="2 2 10 2" 
                                       Visibility="{Binding Path=AllowMultiSelect, Converter={StaticResource VisiblityConverter}, RelativeSource={RelativeSource AncestorType={x:Type tools_controls_shared:ComboBoxAdv}}}"
                                       IsHitTestVisible="False" />

                                <ContentPresenter Grid.Column="1" 
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                                    <ContentPresenter.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                            </Grid>
                            <Border.Triggers>
                                <!-- Animates the SelectionIndicator -->
                                <EventTrigger RoutedEvent="Border.MouseDown">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="SelectionIndicator" 
                                                     Storyboard.TargetProperty="Height"
                                                     From="12" 
                                                     To="7" 
                                                     Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="Border.MouseUp">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="SelectionIndicator" 
                                                     Storyboard.TargetProperty="Height"
                                                     From="7" 
                                                     To="12" 
                                                     Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </Border.Triggers>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" 
                                 Value="true">
                            <Setter Property="BorderBrush"
                                    Value="{StaticResource BorderAlt}" />
                            <Setter Property="Background" 
                                    Value="{StaticResource PopupHoveredBackground}" />
                            <Setter Property="Foreground" 
                                     Value="{StaticResource PopupHoveredForeground}" />
                        </Trigger>
                        <Trigger Property="IsHighlighted"
                                Value="true">
                            <Setter Property="BorderBrush" 
                                     Value="{StaticResource BorderAlt}" />
                            <Setter Property="Background" 
                                    Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground"
                                    Value="{StaticResource HoveredForeground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" 
                                 Value="true" >
                            <Setter Property="BorderBrush" 
                                    Value="{StaticResource PopupSelectedBackground}" />
                            <Setter Property="Background" 
                                    Value="{StaticResource PopupSelectedBackground}" />
                            <Setter Property="Foreground"
                                     Value="{StaticResource PopupSelectedForeground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter Property="BorderBrush" 
                                    Value="{StaticResource BorderAlt}" />
                            <Setter Property="Background"
                                    Value="Transparent" />
                            <Setter Property="Foreground"
                                    Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                        <Trigger Property="IsFocused"
                                Value="true">
                            <Setter Property="BorderBrush" 
                                     Value="{StaticResource BorderAlt}" />
                            <Setter Property="Background" 
                                    Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground"
                                    Value="{StaticResource HoveredForeground}" />
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=IsSelected,RelativeSource={RelativeSource Self}}" 
                                           Value="true" />
                                <Condition Binding="{Binding Path=IsHighlighted,RelativeSource={RelativeSource Self}}"
                                           Value="false" />
                                <Condition Binding="{Binding MultiSelect,RelativeSource={RelativeSource Self}}"
                                           Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" 
                                    Value="{StaticResource PopupSelectedBackground}" />
                            <Setter Property="BorderBrush"
                                    Value="{StaticResource PopupSelectedBackground}" />
                            <Setter Property="Foreground" 
                                    Value="{StaticResource PopupSelectedForeground}" />
                            <Setter Property="Visibility" TargetName="SelectionIndicator" Value="Visible"/>
                        </MultiDataTrigger>
                        <DataTrigger Binding="{Binding MultiSelect,RelativeSource={RelativeSource Self}}" Value="true">
                            <Setter Property="Visibility" TargetName="SelectionIndicator" Value="Collapsed"/>
                        </DataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=IsSelected,RelativeSource={RelativeSource Self}}" 
                                           Value="true" />
                                <Condition Binding="{Binding Path=IsHighlighted,RelativeSource={RelativeSource Self}}"
                                           Value="true" />
                                <Condition Binding="{Binding MultiSelect,RelativeSource={RelativeSource Self}}"
                                           Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Visibility" TargetName="SelectionIndicator" Value="Visible"/>
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionComboBoxItemAdvStyle}" 
         TargetType="{x:Type tools_controls_shared:ComboBoxItemAdv}" />

</ResourceDictionary>
