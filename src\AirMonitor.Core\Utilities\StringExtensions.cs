using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace AirMonitor.Core.Utilities;

/// <summary>
/// 字符串扩展方法
/// 提供常用的字符串操作扩展功能
/// </summary>
public static class StringExtensions
{
    /// <summary>
    /// 检查字符串是否为空或空白
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>true表示为空或空白，false表示不为空</returns>
    public static bool IsNullOrWhiteSpace(this string? value)
    {
        return string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    /// 检查字符串是否不为空且不为空白
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>true表示不为空且不为空白，false表示为空或空白</returns>
    public static bool IsNotNullOrWhiteSpace(this string? value)
    {
        return !string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    /// 安全截取字符串
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="suffix">超长时的后缀</param>
    /// <returns>截取后的字符串</returns>
    public static string SafeSubstring(this string? value, int maxLength, string suffix = "...")
    {
        if (string.IsNullOrEmpty(value))
        {
            return string.Empty;
        }

        if (value.Length <= maxLength)
        {
            return value;
        }

        var truncateLength = Math.Max(0, maxLength - suffix.Length);
        return value[..truncateLength] + suffix;
    }

    /// <summary>
    /// 转换为标题格式（首字母大写）
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>标题格式的字符串</returns>
    public static string ToTitleCase(this string? value)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            return string.Empty;
        }

        var textInfo = CultureInfo.CurrentCulture.TextInfo;
        return textInfo.ToTitleCase(value.ToLower());
    }

    /// <summary>
    /// 转换为帕斯卡命名法（PascalCase）
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>帕斯卡命名法的字符串</returns>
    public static string ToPascalCase(this string? value)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            return string.Empty;
        }

        var words = value.Split(new[] { ' ', '_', '-' }, StringSplitOptions.RemoveEmptyEntries);
        var result = new StringBuilder();

        foreach (var word in words)
        {
            if (word.Length > 0)
            {
                result.Append(char.ToUpper(word[0]));
                if (word.Length > 1)
                {
                    result.Append(word[1..].ToLower());
                }
            }
        }

        return result.ToString();
    }

    /// <summary>
    /// 转换为驼峰命名法（camelCase）
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>驼峰命名法的字符串</returns>
    public static string ToCamelCase(this string? value)
    {
        var pascalCase = value.ToPascalCase();
        if (string.IsNullOrEmpty(pascalCase))
        {
            return string.Empty;
        }

        return char.ToLower(pascalCase[0]) + pascalCase[1..];
    }

    /// <summary>
    /// 计算字符串的MD5哈希值
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>MD5哈希值</returns>
    public static string ToMD5(this string? value)
    {
        if (string.IsNullOrEmpty(value))
        {
            return string.Empty;
        }

        using var md5 = MD5.Create();
        var inputBytes = Encoding.UTF8.GetBytes(value);
        var hashBytes = md5.ComputeHash(inputBytes);
        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }

    /// <summary>
    /// 计算字符串的SHA256哈希值
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>SHA256哈希值</returns>
    public static string ToSHA256(this string? value)
    {
        if (string.IsNullOrEmpty(value))
        {
            return string.Empty;
        }

        using var sha256 = SHA256.Create();
        var inputBytes = Encoding.UTF8.GetBytes(value);
        var hashBytes = sha256.ComputeHash(inputBytes);
        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }

    /// <summary>
    /// Base64编码
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>Base64编码的字符串</returns>
    public static string ToBase64(this string? value)
    {
        if (string.IsNullOrEmpty(value))
        {
            return string.Empty;
        }

        var bytes = Encoding.UTF8.GetBytes(value);
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// Base64解码
    /// </summary>
    /// <param name="value">Base64编码的字符串</param>
    /// <returns>解码后的字符串</returns>
    public static string FromBase64(this string? value)
    {
        if (string.IsNullOrEmpty(value))
        {
            return string.Empty;
        }

        try
        {
            var bytes = Convert.FromBase64String(value);
            return Encoding.UTF8.GetString(bytes);
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary>
    /// 验证邮箱格式
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>true表示格式正确，false表示格式错误</returns>
    public static bool IsValidEmail(this string? email)
    {
        if (string.IsNullOrWhiteSpace(email))
        {
            return false;
        }

        try
        {
            var pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
            return Regex.IsMatch(email, pattern, RegexOptions.IgnoreCase);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 移除HTML标签
    /// </summary>
    /// <param name="html">HTML字符串</param>
    /// <returns>纯文本字符串</returns>
    public static string RemoveHtmlTags(this string? html)
    {
        if (string.IsNullOrWhiteSpace(html))
        {
            return string.Empty;
        }

        return Regex.Replace(html, "<.*?>", string.Empty);
    }

    /// <summary>
    /// 格式化文件大小
    /// </summary>
    /// <param name="bytes">字节数字符串</param>
    /// <returns>格式化的文件大小</returns>
    public static string FormatAsFileSize(this string? bytes)
    {
        if (string.IsNullOrWhiteSpace(bytes) || !long.TryParse(bytes, out var size))
        {
            return "0 B";
        }

        return FileHelper.FormatFileSize(size);
    }

    /// <summary>
    /// 安全转换为整数
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>转换后的整数</returns>
    public static int ToIntSafe(this string? value, int defaultValue = 0)
    {
        return int.TryParse(value, out var result) ? result : defaultValue;
    }

    /// <summary>
    /// 安全转换为长整数
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>转换后的长整数</returns>
    public static long ToLongSafe(this string? value, long defaultValue = 0)
    {
        return long.TryParse(value, out var result) ? result : defaultValue;
    }

    /// <summary>
    /// 安全转换为双精度浮点数
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>转换后的双精度浮点数</returns>
    public static double ToDoubleSafe(this string? value, double defaultValue = 0.0)
    {
        return double.TryParse(value, out var result) ? result : defaultValue;
    }

    /// <summary>
    /// 安全转换为布尔值
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>转换后的布尔值</returns>
    public static bool ToBoolSafe(this string? value, bool defaultValue = false)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            return defaultValue;
        }

        var lowerValue = value.ToLower().Trim();
        return lowerValue switch
        {
            "true" or "1" or "yes" or "on" or "是" or "真" => true,
            "false" or "0" or "no" or "off" or "否" or "假" => false,
            _ => bool.TryParse(value, out var result) ? result : defaultValue
        };
    }

    /// <summary>
    /// 安全转换为日期时间
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>转换后的日期时间</returns>
    public static DateTime ToDateTimeSafe(this string? value, DateTime? defaultValue = null)
    {
        return DateTime.TryParse(value, out var result) ? result : (defaultValue ?? DateTime.MinValue);
    }

    /// <summary>
    /// 生成随机字符串
    /// </summary>
    /// <param name="length">字符串长度</param>
    /// <param name="includeNumbers">是否包含数字</param>
    /// <param name="includeUppercase">是否包含大写字母</param>
    /// <param name="includeLowercase">是否包含小写字母</param>
    /// <param name="includeSpecialChars">是否包含特殊字符</param>
    /// <returns>随机字符串</returns>
    public static string GenerateRandomString(int length, bool includeNumbers = true, 
        bool includeUppercase = true, bool includeLowercase = true, bool includeSpecialChars = false)
    {
        var chars = new StringBuilder();
        
        if (includeLowercase) chars.Append("abcdefghijklmnopqrstuvwxyz");
        if (includeUppercase) chars.Append("ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        if (includeNumbers) chars.Append("0123456789");
        if (includeSpecialChars) chars.Append("!@#$%^&*()_+-=[]{}|;:,.<>?");

        if (chars.Length == 0)
        {
            throw new ArgumentException("至少需要包含一种字符类型");
        }

        var random = new Random();
        var result = new StringBuilder(length);
        var charArray = chars.ToString();

        for (int i = 0; i < length; i++)
        {
            result.Append(charArray[random.Next(charArray.Length)]);
        }

        return result.ToString();
    }

    /// <summary>
    /// 掩码敏感信息
    /// </summary>
    /// <param name="value">原始字符串</param>
    /// <param name="visibleStart">开始可见字符数</param>
    /// <param name="visibleEnd">结束可见字符数</param>
    /// <param name="maskChar">掩码字符</param>
    /// <returns>掩码后的字符串</returns>
    public static string MaskSensitiveInfo(this string? value, int visibleStart = 3, int visibleEnd = 3, char maskChar = '*')
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            return string.Empty;
        }

        if (value.Length <= visibleStart + visibleEnd)
        {
            return new string(maskChar, value.Length);
        }

        var start = value[..visibleStart];
        var end = value[^visibleEnd..];
        var maskLength = value.Length - visibleStart - visibleEnd;
        var mask = new string(maskChar, maskLength);

        return start + mask + end;
    }
}
