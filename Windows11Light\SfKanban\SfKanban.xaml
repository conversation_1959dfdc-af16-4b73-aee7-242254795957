<ResourceDictionary 
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
	xmlns:local="clr-namespace:Syncfusion.UI.Xaml.Kanban;assembly=Syncfusion.SfKanban.WPF" >

	<ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

	<local:CardCountConverter x:Key="CardCountConverter" />

	<DataTemplate x:Key="DefaultSwimlaneColumnHeaderTemplate">
		<Grid>
			<Grid.RowDefinitions>
				<RowDefinition Height="6*" />
				<RowDefinition Height="4*" />
			</Grid.RowDefinitions>

            <TextBlock x:Name="Header"
                       Text="{Binding Header}"
                       VerticalAlignment="Center"
                       Foreground="{StaticResource ContentForeground}"
                       FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                       FontWeight="{StaticResource Windows11Light.FontWeightMedium}"
                       TextWrapping="NoWrap"
                       Grid.Row="0">
                <TextBlock.Margin>
                    <Thickness>8,8,8,4</Thickness>
                </TextBlock.Margin>
            </TextBlock>

			<StackPanel Grid.Row="1"
                        x:Name="WIPPanel"
                        Orientation="Horizontal">
				<TextBlock Text="{Binding [ItemsCount]}"
                           Margin="8,4,0,8"
                           VerticalAlignment="Center"
						   Foreground="{StaticResource ContentForegroundAlt1}"
                           FontSize="{StaticResource Windows11Light.CaptionText}"
                           FontWeight="{StaticResource Windows11Light.FontWeightNormal}"/>
				<TextBlock Text="{Binding CardCount}"
                           Margin="3,4,0,8"
                           VerticalAlignment="Center"
						   Foreground="{StaticResource ContentForegroundAlt1}"
                           FontSize="{StaticResource Windows11Light.CaptionText}"
                           FontWeight="{StaticResource Windows11Light.FontWeightNormal}"/>
				<TextBlock Text="{Binding Content, RelativeSource={RelativeSource Mode=TemplatedParent},
					ConverterParameter=MinMaxLimit,
                    Converter={StaticResource CardCountConverter}}"
                           Margin="3,4,0,8"
                           VerticalAlignment="Center"
						   Foreground="{StaticResource ContentForegroundAlt1}"
                           FontSize="{StaticResource Windows11Light.CaptionText}"
                           FontWeight="{StaticResource Windows11Light.FontWeightNormal}"/>
			</StackPanel>
		</Grid>
	</DataTemplate>

	<DataTemplate x:Key="DefaultSwimlaneHeaderTemplate">
		<StackPanel x:Name="SwimlaneHeaderPanel" 
					Orientation="Horizontal"
                    Background="Transparent">
            <Border  x:Name="PART_Border" 
                     Margin="2" 
                     Height="24" 
                     Width="24" 
                     CornerRadius="{StaticResource Windows11Light.CornerRadius4}" 
                     Background="Transparent">
                <Grid x:Name="CollapsedIcon" 
					  Background="Transparent">
                    <Path x:Name="ExpandedPath" 
					  IsHitTestVisible="False"
                      Stretch="Uniform" 
					  Fill="Transparent"
                      Stroke="{StaticResource IconColor}"
                      Height="4.5"
                      Width="8"   
					  RenderTransformOrigin="0.5,0.5">
                        <Path.Data>
                            <PathGeometry>M1 0.5L4.5 4L8 0.5</PathGeometry>
                        </Path.Data>
                        <Path.RenderTransform>
                            <TransformGroup>
                                <TransformGroup.Children>
                                    <RotateTransform Angle="0" />
                                    <ScaleTransform ScaleX="1" 
												ScaleY="1" />
                                </TransformGroup.Children>
                            </TransformGroup>
                        </Path.RenderTransform>
                    </Path>
                    <Path x:Name="CollapsedPath" 
						  Visibility="Collapsed" 
					      IsHitTestVisible="False"
					      Stretch="Uniform" 
					      Fill="{StaticResource IconColor}"
                          Stroke="Transparent"
					      Height="8.5"
                          Width="8"   
					      RenderTransformOrigin="0.5,0.5">
                        <Path.Data>
                            <PathGeometry>M0.646447 0.646447C0.451184 0.841709 0.451184 1.15829 0.646447 1.35355L3.79289 4.5L0.646447 7.64645C0.451184 7.84171 0.451184 8.15829 0.646447 8.35355C0.841709 8.54882 1.15829 8.54882 1.35355 8.35355L4.85355 4.85355C5.04882 4.65829 5.04882 4.34171 4.85355 4.14645L1.35355 0.646447C1.15829 0.451184 0.841709 0.451184 0.646447 0.646447Z</PathGeometry>
                        </Path.Data>
                        <Path.RenderTransform>
                            <TransformGroup>
                                <TransformGroup.Children>
                                    <RotateTransform Angle="0" />
                                    <ScaleTransform ScaleX="1" 
												ScaleY="1" />
                                </TransformGroup.Children>
                            </TransformGroup>
                        </Path.RenderTransform>
                    </Path>
                </Grid>
            </Border>

			<TextBlock IsHitTestVisible="False"
					   Foreground="{StaticResource ContentForeground}"
                       FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                       FontWeight="{StaticResource Windows11Light.FontWeightMedium}"
                       TextWrapping="NoWrap"
                       VerticalAlignment="Center"
                       Text="{Binding Title}" />

			<TextBlock IsHitTestVisible="False"
                       Foreground="{StaticResource ContentForegroundAlt1}"
                       FontSize="{StaticResource Windows11Light.CaptionText}"
                       FontWeight="{StaticResource Windows11Light.FontWeightNormal}"
                       TextWrapping="NoWrap"
                       Margin="5,0,0,0"
                       VerticalAlignment="Center"
                       Text="-"/>

			<TextBlock IsHitTestVisible="False"
					   Foreground="{StaticResource ContentForegroundAlt1}"
                       FontSize="{StaticResource Windows11Light.CaptionText}"
                       FontWeight="{StaticResource Windows11Light.FontWeightNormal}"
                       TextWrapping="NoWrap"
                       Margin="5,0,0,0"
                       VerticalAlignment="Center"
                       Text="{Binding ItemsCount, ConverterParameter=SwimlaneCardCount, Converter={StaticResource CardCountConverter}}"/>
		</StackPanel>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding ElementName=CollapsedIcon,Path=IsMouseOver}" Value="True">
                <Setter TargetName="PART_Border" Property="Cursor" Value="Hand"/>
                <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundHovered}"/>
            </DataTrigger>
        </DataTemplate.Triggers>
	</DataTemplate>

	<DataTemplate x:Key="DefaultKanbanHeaderTemplate">
		<Grid>
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="9*" />
				<ColumnDefinition Width="1*" />
			</Grid.ColumnDefinitions>
			<Grid.RowDefinitions>
				<RowDefinition Height="6*" />
				<RowDefinition Height="4*" />
			</Grid.RowDefinitions>

			<TextBlock x:Name="Header"
                       Text="{Binding Header}"
                       VerticalAlignment="Center"
					   Foreground="{StaticResource ContentForeground}"
                       FontSize="{StaticResource Windows11Light.BodyTextStyle}"
					   FontWeight="{StaticResource Windows11Light.FontWeightMedium}"
                       TextWrapping="NoWrap"
                       Grid.Row="0"
                       Margin="0,5,0,5"
                       Grid.ColumnSpan="1" />

			<StackPanel x:Name="WIPPanel"
					       	Grid.Row="1"
                  Grid.Column="0"
                  Orientation="Horizontal">
				<TextBlock Text="{Binding [ItemsCount]}"
                           Margin="0,2,0,5"
						   VerticalAlignment="Center"
						   Foreground="{StaticResource ContentForegroundAlt1}"
						   FontSize="{StaticResource Windows11Light.CaptionText}"
						   FontWeight="{StaticResource Windows11Light.FontWeightNormal}"/>
				<TextBlock Text="{Binding CardCount}"
                           Margin="5,2,0,5"
                           VerticalAlignment="Center"
						   Foreground="{StaticResource ContentForegroundAlt1}"
						   FontSize="{StaticResource Windows11Light.CaptionText}"
						   FontWeight="{StaticResource Windows11Light.FontWeightNormal}"/>
				<TextBlock Text="{Binding Content, RelativeSource={RelativeSource Mode=TemplatedParent},
                    Converter={StaticResource CardCountConverter}}"
                           Margin="0,2,0,5"
                           VerticalAlignment="Center"
						   Foreground="{StaticResource ContentForegroundAlt1}"
						   FontSize="{StaticResource Windows11Light.CaptionText}"
						   FontWeight="{StaticResource Windows11Light.FontWeightNormal}"/>
			</StackPanel>

			<Border x:Name="CollapsedIcon"
                    Background="Transparent"
					Grid.Column="1"
                    Grid.Row="1">
				<Path Data="M172.444,310.778 L158.444,317.111 L171.222,324.222"
                      Stretch="Fill"
                      HorizontalAlignment="Right"
                      Stroke="{StaticResource IconColor}"
                      IsHitTestVisible="False"
                      StrokeThickness="2"
                      StrokeLineJoin="Miter"
                      UseLayoutRounding="False"
                      Width="7"
                      Height="14">
				</Path>
			</Border>
		</Grid>
	</DataTemplate>

	<DataTemplate x:Key="TagsTemplate">
		<Border Background="{Binding CardStyle.TagBackground, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SfKanban}}"
				BorderBrush="{StaticResource BorderAlt}"
                CornerRadius="2">
			<TextBlock Text="{Binding}"
                       HorizontalAlignment="Center"
					   VerticalAlignment="Center"
                       TextTrimming="CharacterEllipsis"
                       Margin="10,1,10,1"
                       FontSize="{StaticResource Windows11Light.BodyTextStyle}"
					   FontWeight="{StaticResource Windows11Light.FontWeightNormal}"
                       Foreground="{Binding CardStyle.TagForeground, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SfKanban}}"/>
		</Border>
	</DataTemplate>

    <Style x:Key="SyncfusionSfKanbanPlaceholderStyle"
           TargetType="local:PlaceholderStyle">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
		<Setter Property="RadiusX"
                Value="6" />
		<Setter Property="RadiusY"
                Value="6" />
		<Setter Property="Fill"
                Value="Transparent"/>
		<Setter Property="FontSize"
				Value="{StaticResource Windows11Light.BodyTextStyle}"/>
		<Setter Property="FontWeight"
				Value="{StaticResource Windows11Light.FontWeightNormal}"/>
		<Setter Property="Foreground"
				Value="{StaticResource ContentForegroundAlt1}"/>
		<Setter Property="Stroke"
						Value="{StaticResource BorderAlt4}"/>
		<Setter Property="StrokeDashArray"
                Value="4,2" />
		<Setter Property="StrokeThickness"
                Value="1" />
		<Setter Property="TextHorizontalAlignment"
                Value="Center" />
		<Setter Property="TextVerticalAlignment"
                Value="Top" />
		<Setter Property="SelectedBackground"
                Value="Transparent"/>
		<Setter Property="SelectedForeground"
				Value="{StaticResource ContentForegroundAlt1}"/>
		<Setter Property="SelectedFontSize"
				Value="{StaticResource Windows11Light.BodyTextStyle}"/>
		<Setter Property="SelectedFontWeight"
				Value="{StaticResource Windows11Light.FontWeightNormal}"/>
		<Setter Property="SelectedStroke"
				Value="{StaticResource BorderAlt4}"/>
        <Setter Property="Effect" 
                Value="{StaticResource Default.ShadowDepth1}" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSfKanbanPlaceholderStyle}"
           TargetType="local:PlaceholderStyle"/>

    <Style  x:Key="SyncfusionSfKanbanCardStyle"
            TargetType="local:KanbanCardStyle">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
		<Setter Property="TitleColor"
		Value="{StaticResource ContentForeground}"/>
		<Setter Property="TitleFontSize"
				Value="{StaticResource Windows11Light.TitleTextStyle}"/>
		<Setter Property="TitleHorizontalAlignment"
                Value="Left" />
		<Setter Property="TitleFontWeight"
				Value="{StaticResource Windows11Light.FontWeightMedium}"/>
        <Setter Property="CornerRadius"
                Value="{StaticResource Windows11Light.CornerRadius4}" />
        <Setter Property="TagVisibility"
						Value="Visible" />
		<Setter Property="IndicatorVisibility"
						Value="Visible" />
		<Setter Property="IconVisibility"
						Value="Visible" />
		<Setter Property="TagBackground"
				Value="{StaticResource PopupBackground}"/>
        <Setter Property="TagForeground"
                Value="{StaticResource ContentForeground}" />
        <Setter Property="FontSize"
				Value="{StaticResource Windows11Light.BodyTextStyle}"/>
		<Setter Property="Foreground"
				Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness">
            <Setter.Value>
                <Thickness>1.5</Thickness>
            </Setter.Value>
        </Setter>

        <Setter Property="Background"
				Value="{StaticResource PopupBackground}"/>
        <Setter Property="Effect" Value="{StaticResource Default.ShadowDepth1}" />
    </Style>

	<Style x:Key="SyncfusionSfKanbanStyle" TargetType="local:SfKanban">
		<Setter Property="Background"
				Value="{StaticResource ContentBackgroundAlt4}"/>
		<Setter Property="BorderBrush"
				Value="{StaticResource BorderAlt}"/>
		<Setter Property="FontFamily"
				Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
		<Setter Property="CardStyle">
			<Setter.Value>
				<local:KanbanCardStyle Style="{StaticResource SyncfusionSfKanbanCardStyle}"/>
			</Setter.Value>
		</Setter>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}"></Setter>
        <Setter Property="ColumnHeaderTemplate"
                Value="{StaticResource DefaultSwimlaneColumnHeaderTemplate}" />
        <Setter Property="SwimlaneHeaderTemplate"
                Value="{StaticResource DefaultSwimlaneHeaderTemplate}" />
        <Setter Property="PlaceholderStyle">
			<Setter.Value>
                <local:PlaceholderStyle Style="{StaticResource SyncfusionSfKanbanPlaceholderStyle}" />
			</Setter.Value>
		</Setter>
	</Style>

    <Style x:Key="SyncfusionSwimlaneStyle"
           TargetType="local:Swimlane">
        <Setter Property="FocusVisualStyle"
                Value="{x:Null}" />
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}" />
        <Setter Property="Background"
                Value="{StaticResource ContentBackgroundAlt2}" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSwimlaneStyle}"
           TargetType="local:Swimlane"></Style>

    <Style x:Key="SyncfusionKanbanColumnStyle"
           TargetType="local:KanbanColumn">
        <Setter Property="FocusVisualStyle"
                Value="{x:Null}" />
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}" />
        <Setter Property="Background"
                Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="Margin"
                Value="0,0,4,0" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionKanbanColumnStyle}"
           TargetType="local:KanbanColumn" />
        
    <Style x:Key="SyncfusionSwinlaneColumnStyle"
           TargetType="local:SwimlaneColumn">
        <Setter Property="FocusVisualStyle"
                Value="{x:Null}" />
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}" />
        <Setter Property="Background"
                Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="Margin"
                Value="0,0,4,0" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSwinlaneColumnStyle}" TargetType="local:SwimlaneColumn"/>

    <Style x:Key="SyncfusionTagsStackPanelStyle" TargetType="local:TagsStackPanel">
		<Setter Property="ContentTemplate"
                Value="{StaticResource TagsTemplate}"/>
	</Style>

    <Style BasedOn="{StaticResource SyncfusionTagsStackPanelStyle}" TargetType="local:TagsStackPanel"/>

    <Style TargetType="local:SfKanban" BasedOn="{StaticResource SyncfusionSfKanbanStyle}"></Style>
</ResourceDictionary>
