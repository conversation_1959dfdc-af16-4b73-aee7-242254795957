using System.Globalization;
using System.Windows.Data;

namespace AirMonitor.LicenseGenerator.Views;

/// <summary>
/// 布尔值到状态文本转换器
/// </summary>
public class BooleanToStatusTextConverter : IValueConverter
{
    public static readonly BooleanToStatusTextConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return boolValue ? "验证通过" : "验证失败";
        }
        return "未验证";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
