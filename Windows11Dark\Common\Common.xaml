<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:po="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options"
                    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <Color x:Key="DropShadow.Static.Background">#CC000000</Color>

    <DropShadowEffect x:Key="Default.ShadowDepth1"
                      BlurRadius="4" 
                      ShadowDepth="0" 
                      Direction="270" 
                      Color="{StaticResource DropShadow.Static.Background}" 
                      Opacity="0.42" 
                      RenderingBias="Performance" 
                      po:Freeze="True" />
    <DropShadowEffect x:Key="Default.ShadowDepth2" 
                      BlurRadius="6" 
                      ShadowDepth="1" 
                      Direction="270"
                      Color="{StaticResource DropShadow.Static.Background}" 
                      Opacity="0.42" 
                      RenderingBias="Performance"
                      po:Freeze="True" />
    <DropShadowEffect x:Key="Default.ShadowDepth3" 
                      BlurRadius="8"
                     ShadowDepth="0" 
                      Direction="270"
                      Color="{StaticResource DropShadow.Static.Background}" 
                      Opacity="0.42" 
                      RenderingBias="Performance"
                      po:Freeze="True" />
    <DropShadowEffect x:Key="Default.ShadowDepth4" 
                      BlurRadius="16" 
                      ShadowDepth="7" 
                      Direction="270"
                      Color="{StaticResource DropShadow.Static.Background}" 
                      Opacity="0.17"
                      RenderingBias="Performance" 
                      po:Freeze="True" />

    <!--Used this shadow for ribbon DropDownButton popup in implemented themes-->
    <DropShadowEffect x:Key="Default.ShadowDepth5" 
                     BlurRadius="64" 
                      ShadowDepth="0"
                      Direction="270"
                      Color="{StaticResource DropShadow.Static.Background}" 
                      Opacity="0.42" 
                      RenderingBias="Performance" 
                      po:Freeze="True" />

    <!--Used this shadow for NavigationDrawer popup in implemented themes-->
    <DropShadowEffect x:Key="Default.ShadowDepth6"
                      BlurRadius="14" 
                      ShadowDepth="4.5"
                      Color="{StaticResource DropShadow.Static.Background}" 
                      Opacity=".42" 
                      RenderingBias="Performance" 
                      po:Freeze="True" />

    <!--Used for MS Button, ComboBox(Non-editable mode), CheckBox, RadioButton, Hyperlink, slider controls-->

<sys:Double x:Key="Windows11Dark.HeaderTextStyle">16</sys:Double>
<sys:Double x:Key="Windows11Dark.SubHeaderTextStyle">14</sys:Double>
<sys:Double x:Key="Windows11Dark.TitleTextStyle">14</sys:Double>
<sys:Double x:Key="Windows11Dark.SubTitleTextStyle">12</sys:Double>
<sys:Double x:Key="Windows11Dark.BodyTextStyle">12</sys:Double>
<sys:Double x:Key="Windows11Dark.CaptionText">10</sys:Double>
<FontFamily x:Key="Windows11Dark.ThemeFontFamily">Microsoft YaHei</FontFamily> 
<FontWeight x:Key="Windows11Dark.FontWeightNormal">Normal</FontWeight> 
<FontWeight x:Key="Windows11Dark.FontWeightMedium">Medium</FontWeight> 
<Thickness x:Key="Windows11Dark.BorderThickness">0,0,0,0</Thickness> 
	<Thickness x:Key="Windows11Dark.BorderThickness1">1</Thickness>
	<Thickness x:Key="Windows11Dark.BorderThickness2">2</Thickness>
	<Thickness x:Key="Windows11Dark.BorderThickness0001">0,0,0,1</Thickness>
	<Thickness x:Key="Windows11Dark.BorderThickness0002">0,0,0,2</Thickness>
	<Thickness x:Key="Windows11Dark.BorderThickness1000">1,0,0,0</Thickness>
	<Thickness x:Key="Windows11Dark.BorderThickness0100">0,1,0,0</Thickness>
	<Thickness x:Key="Windows11Dark.BorderThickness0010">0,0,1,0</Thickness>
	<Thickness x:Key="Windows11Dark.BorderThickness1110">1,1,1,0</Thickness>
	<Thickness x:Key="Windows11Dark.BorderThickness1101">1,1,0,1</Thickness>
	<Thickness x:Key="Windows11Dark.BorderThickness1011">1,0,1,1</Thickness>
	<Thickness x:Key="Windows11Dark.BorderThickness0111">0,1,1,1</Thickness>
	<Thickness x:Key="Windows11Dark.BorderThickness0011">0,0,1,1</Thickness>
	<Thickness x:Key="Windows11Dark.BorderThickness1112">1,1,1,2</Thickness>
<Thickness x:Key="Windows11Dark.ThemeBorderThicknessVariant1">1,1,1,1</Thickness> 
<Thickness x:Key="Windows11Dark.ThemeBorderThicknessVariant2">1,1,1,1</Thickness> 
<Thickness x:Key="Windows11Dark.FocusMargin">2,2,2,2</Thickness> 
<sys:Double x:Key="Windows11Dark.StrokeThickness">0</sys:Double>
	<sys:Double x:Key="Windows11Dark.StrokeThickness1">1</sys:Double>
	<sys:Double x:Key="Windows11Dark.StrokeThickness2">2</sys:Double>
<DoubleCollection x:Key="Windows11Dark.StrokeDashArray">1 2</DoubleCollection> 
<CornerRadius x:Key="Windows11Dark.ThemeCornerRadiusVariant1">0,0,0,0</CornerRadius> 
<CornerRadius x:Key="Windows11Dark.ThemeCornerRadiusVariant2">0,0,0,0</CornerRadius> 
<CornerRadius x:Key="Windows11Dark.CornerRadius2">2,2,2,2</CornerRadius> 
<CornerRadius x:Key="Windows11Dark.CornerRadius4">4,4,4,4</CornerRadius> 
<CornerRadius x:Key="Windows11Dark.CornerRadius6">6,6,6,6</CornerRadius> 
<CornerRadius x:Key="Windows11Dark.CornerRadius7">7,7,7,7</CornerRadius> 
<CornerRadius x:Key="Windows11Dark.CornerRadius8">8,8,8,8</CornerRadius> 
	<sys:Double x:Key="TouchMode.MinHeight">32</sys:Double>
	<sys:Double x:Key="TouchMode.MinWidth">32</sys:Double>
	<sys:Double x:Key="TouchMode.MinSize">24</sys:Double>
<sys:Double x:Key="Windows11Dark.MinHeight">24</sys:Double>
	<sys:Double x:Key="Windows11Dark.MinHeight1">24</sys:Double>
<sys:Double x:Key="Windows11Dark.IconPanelSize">32</sys:Double>
    <Style x:Key="KeyboardFocusVisualStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border Margin="-3"
                            SnapsToDevicePixels="True"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius6}"
                            BorderThickness="{StaticResource Windows11Dark.BorderThickness2}"
                            BorderBrush="#FFFFFFFF">
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--Used for MS List type controls-->
    <Style x:Key="CheckKeyboardFocusVisualStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border Margin="-3,-2,-2,-2"
                            SnapsToDevicePixels="True"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            BorderThickness="{StaticResource Windows11Dark.BorderThickness2}"
                            BorderBrush="#FFFFFFFF">
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--Used for MS DataGrid, Expander type controls-->
    <Style x:Key="FlatKeyboardFocusVisualStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border SnapsToDevicePixels="True"
                            BorderThickness="{StaticResource Windows11Dark.BorderThickness2}"
                            BorderBrush="#FFFFFFFF" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--Used for MS Rounded button type controls-->
    <Style x:Key="CircleKeyboardFocusVisualStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border Margin="-3"
                            SnapsToDevicePixels="True"
                            BorderThickness="{StaticResource Windows11Dark.BorderThickness2}"
                            BorderBrush="#FFFFFFFF"
                            CornerRadius="100">
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--Used for MS Corner Rounded button type controls-->
    <Style x:Key="CurveKeyboardFocusVisualStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border SnapsToDevicePixels="True"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            BorderThickness="{StaticResource Windows11Dark.BorderThickness2}"
                            BorderBrush="#FFFFFFFF" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <Style x:Key="DottedKeyboardFocusVisualStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Stroke="#FFFFFFFF"
                               StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Dark.StrokeDashArray}"
                               SnapsToDevicePixels="true"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="DottedCircleKeyboardFocusVisualStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Stroke="#FFFFFFFF"
                               StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Dark.StrokeDashArray}"
                               SnapsToDevicePixels="true"
                               RadiusX="100"
                               RadiusY="100"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
