<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="NUnit" Version="4.2.2" />
    <PackageReference Include="NUnit.Analyzers" Version="4.3.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.6.0" />

    <!-- Mocking -->
    <PackageReference Include="Moq" Version="4.20.72" />

    <!-- Test Database -->
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="NUnit.Framework" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\AirMonitor.Core\AirMonitor.Core.csproj" />
    <ProjectReference Include="..\..\src\AirMonitor.Services\AirMonitor.Services.csproj" />
  </ItemGroup>

</Project>
