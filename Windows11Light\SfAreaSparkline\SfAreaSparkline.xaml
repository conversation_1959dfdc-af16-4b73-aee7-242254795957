<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:areaSparkline="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="SfAreaSparklineAxisLineStyle" TargetType="Line">
        <Setter Property="Stroke" Value="{StaticResource Series10}" />
        <Setter Property="StrokeThickness" Value="2"></Setter>
    </Style>

    <Style x:Key="SfAreaSparklineTrackBallSymbolStyle" TargetType="Ellipse">
        <Setter Property="Stroke" Value="{StaticResource Series10}"></Setter>
        <Setter Property="StrokeThickness" Value="1.5"></Setter>
        <Setter Property="Fill" Value="{StaticResource ContentBackground}"></Setter>
        <Setter Property="Height" Value="12"></Setter>
        <Setter Property="Width" Value="12"></Setter>
    </Style>

    <Style x:Key="SfAreaSparklineTrackBallLineStyle" TargetType="Line">
        <Setter Property="Stroke" Value="{StaticResource Series10}" />
        <Setter Property="StrokeThickness" Value="1.5"></Setter>
    </Style>

    <Style x:Key="SyncfusionSfAreaSparklineStyle"
           TargetType="areaSparkline:SfAreaSparkline">
        <Setter Property="Background" Value="{StaticResource ContentBackground}"></Setter>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="BorderThickness" Value="0"></Setter>
        <Setter Property="Interior" Value="{StaticResource Series1}"></Setter>
        <Setter Property="RangeBandBrush" Value="{StaticResource Series10}"></Setter>
        <Setter Property="AxisStyle" Value="{StaticResource SfAreaSparklineAxisLineStyle}"></Setter>
        <Setter Property="TrackBallStyle" Value="{StaticResource SfAreaSparklineTrackBallSymbolStyle}"></Setter>
        <Setter Property="LineStyle" Value="{StaticResource SfAreaSparklineTrackBallLineStyle}"></Setter>
    </Style>

    <Style TargetType="areaSparkline:SfAreaSparkline"
           BasedOn="{StaticResource SyncfusionSfAreaSparklineStyle}" />
</ResourceDictionary>
