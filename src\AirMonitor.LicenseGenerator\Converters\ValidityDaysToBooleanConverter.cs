using System.Globalization;
using System.Windows.Data;

namespace AirMonitor.LicenseGenerator.Views;

/// <summary>
/// 有效期天数到布尔值转换器
/// </summary>
public class ValidityDaysToBooleanConverter : IValueConverter
{
    public static readonly ValidityDaysToBooleanConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int days)
        {
            return days == -1; // -1表示永久许可证
        }
        return false;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isPermanent)
        {
            return isPermanent ? -1 : 365; // 永久返回-1，否则返回默认365天
        }
        return 365;
    }
}
