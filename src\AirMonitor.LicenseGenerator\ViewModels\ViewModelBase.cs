using CommunityToolkit.Mvvm.ComponentModel;

namespace AirMonitor.LicenseGenerator.ViewModels;

/// <summary>
/// ViewModel基类
/// 提供通用的属性变更通知和命令功能
/// </summary>
public abstract class ViewModelBase : ObservableObject
{
    private bool _isBusy;
    private string _statusMessage = string.Empty;
    private string _title = string.Empty;

    /// <summary>
    /// 是否忙碌状态
    /// </summary>
    public bool IsBusy
    {
        get => _isBusy;
        set => SetProperty(ref _isBusy, value);
    }

    /// <summary>
    /// 状态消息
    /// </summary>
    public string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }

    /// <summary>
    /// 标题
    /// </summary>
    public string Title
    {
        get => _title;
        set => SetProperty(ref _title, value);
    }

    /// <summary>
    /// 设置忙碌状态
    /// </summary>
    /// <param name="isBusy">是否忙碌</param>
    /// <param name="message">状态消息</param>
    protected void SetBusyState(bool isBusy, string message = "")
    {
        IsBusy = isBusy;
        StatusMessage = message;
    }

    /// <summary>
    /// 执行异步操作并处理忙碌状态
    /// </summary>
    /// <param name="operation">异步操作</param>
    /// <param name="busyMessage">忙碌时的消息</param>
    /// <param name="successMessage">成功时的消息</param>
    /// <param name="errorHandler">错误处理器</param>
    protected async Task ExecuteAsync(
        Func<Task> operation,
        string busyMessage = "正在处理...",
        string successMessage = "操作完成",
        Action<Exception>? errorHandler = null)
    {
        try
        {
            SetBusyState(true, busyMessage);
            await operation();
            StatusMessage = successMessage;
        }
        catch (Exception ex)
        {
            if (errorHandler != null)
            {
                errorHandler(ex);
            }
            else
            {
                StatusMessage = $"操作失败: {ex.Message}";
            }
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// 执行异步操作并返回结果
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">异步操作</param>
    /// <param name="busyMessage">忙碌时的消息</param>
    /// <param name="successMessage">成功时的消息</param>
    /// <param name="errorHandler">错误处理器</param>
    /// <returns>操作结果</returns>
    protected async Task<T?> ExecuteAsync<T>(
        Func<Task<T>> operation,
        string busyMessage = "正在处理...",
        string successMessage = "操作完成",
        Func<Exception, T?>? errorHandler = null)
    {
        try
        {
            SetBusyState(true, busyMessage);
            var result = await operation();
            StatusMessage = successMessage;
            return result;
        }
        catch (Exception ex)
        {
            if (errorHandler != null)
            {
                return errorHandler(ex);
            }
            else
            {
                StatusMessage = $"操作失败: {ex.Message}";
                return default;
            }
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// 清除状态消息
    /// </summary>
    protected void ClearStatus()
    {
        StatusMessage = string.Empty;
    }

    /// <summary>
    /// 设置成功状态
    /// </summary>
    /// <param name="message">成功消息</param>
    protected void SetSuccessStatus(string message)
    {
        StatusMessage = message;
    }

    /// <summary>
    /// 设置错误状态
    /// </summary>
    /// <param name="message">错误消息</param>
    protected void SetErrorStatus(string message)
    {
        StatusMessage = $"错误: {message}";
    }

    /// <summary>
    /// 设置警告状态
    /// </summary>
    /// <param name="message">警告消息</param>
    protected void SetWarningStatus(string message)
    {
        StatusMessage = $"警告: {message}";
    }

    /// <summary>
    /// 设置信息状态
    /// </summary>
    /// <param name="message">信息消息</param>
    protected void SetInfoStatus(string message)
    {
        StatusMessage = $"信息: {message}";
    }
}
