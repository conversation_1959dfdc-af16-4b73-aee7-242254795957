<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="RadioButtonOptionMarkFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="17,0,0,0"
                               Stroke="{StaticResource BorderAlt3}" 
                               StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Light.StrokeDashArray}"
                               SnapsToDevicePixels="true" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="RadioButtonFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="{StaticResource Windows11Light.FocusMargin}"
                               Stroke="{StaticResource BorderAlt3}" 
                               StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Light.StrokeDashArray}"
                               SnapsToDevicePixels="true" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFRadioButtonStyle" TargetType="{x:Type RadioButton}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource RadioButtonFocusVisual}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt1}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RadioButton}">
                    <Grid x:Name="templateRoot" 
                          Background="Transparent"
                          SnapsToDevicePixels="True">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Ellipse x:Name="OverlayCircle"
                                 Height="24"
                                 Width="24"
                                 Visibility="Collapsed"/>
                        <Border x:Name="radioButtonBorder" 
                                UseLayoutRounding="True" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Background="{TemplateBinding Background}" 
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                CornerRadius="8"
                                Width="14" 
                                Height="14"
                                Margin="1,1,2,1" >
                            <Ellipse x:Name="optionMark" 
                                     UseLayoutRounding="True"
                                     Fill="{StaticResource IconColor}"
                                     Width="8"
                                     Height="8"
                                     Opacity="0"/>
                        </Border>
                        <ContentPresenter x:Name="contentPresenter" 
                                          Grid.Column="1" 
                                          Focusable="False" 
                                          Margin="{TemplateBinding Padding}"  
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"                                          
                                          RecognizesAccessKey="True" 
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="HasContent" Value="true">
                            <Setter Property="Padding" Value="4,0,0,0"/>
                            <Setter Property="FocusVisualStyle" Value="{StaticResource RadioButtonOptionMarkFocusVisual}"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="true">
                            <Setter Property="Opacity" TargetName="optionMark" Value="1"/>
                            <Setter Property="Background" TargetName="radioButtonBorder" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="BorderBrush" TargetName="radioButtonBorder" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="Fill" TargetName="optionMark" Value="{StaticResource PrimaryForeground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="BorderThickness" TargetName="radioButtonBorder" Value="{StaticResource Windows11Light.BorderThickness}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition  Property="IsMouseOver" Value="true"/>
                                <Condition Property="IsChecked" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="radioButtonBorder" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="BorderBrush" TargetName="radioButtonBorder" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="Fill" TargetName="optionMark" Value="{StaticResource PrimaryForeground}"/>
                            <Setter Property="Width" TargetName="optionMark" Value="10"/>
                            <Setter Property="Height" TargetName="optionMark" Value="10"/>
                            <Setter Property="BorderThickness" TargetName="radioButtonBorder" Value="{StaticResource Windows11Light.BorderThickness}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition  Property="IsMouseOver" Value="true"/>
                                <Condition Property="IsChecked" Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="radioButtonBorder" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="BorderBrush" TargetName="radioButtonBorder" Value="{StaticResource BorderAlt1}"/>
                            <Setter Property="Fill" TargetName="optionMark" Value="Transparent"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition  Property="IsPressed" Value="true"/>
                                <Condition Property="IsChecked" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="radioButtonBorder" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                            <Setter Property="BorderBrush" TargetName="radioButtonBorder" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                            <Setter Property="Fill" TargetName="optionMark" Value="{StaticResource PrimaryForeground}"/>
                            <Setter Property="Width" TargetName="optionMark" Value="6"/>
                            <Setter Property="Height" TargetName="optionMark" Value="6"/>
                            <Setter Property="BorderThickness" TargetName="radioButtonBorder" Value="{StaticResource Windows11Light.BorderThickness}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition  Property="IsPressed" Value="true"/>
                                <Condition Property="IsChecked" Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="radioButtonBorder" Value="{StaticResource PopupBorder}"/>
                            <Setter Property="BorderBrush" TargetName="radioButtonBorder" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="Fill" TargetName="optionMark" Value="Transparent"/>
                            <Setter Property="Opacity" TargetName="optionMark" Value="1"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <Trigger Property="IsChecked" Value="{x:Null}">
                            <Setter Property="Opacity" TargetName="optionMark" Value="0.56"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="false"/>
                                <Condition Property="IsChecked" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="radioButtonBorder" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="radioButtonBorder" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="Fill" TargetName="optionMark" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource DisabledForeground}"/>
                            <Setter Property="BorderThickness" TargetName="radioButtonBorder" Value="{StaticResource Windows11Light.BorderThickness1}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="false"/>
                                <Condition Property="IsChecked" Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="radioButtonBorder" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="radioButtonBorder" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="Fill" TargetName="optionMark" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource DisabledForeground}"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource WPFRadioButtonStyle}" TargetType="{x:Type RadioButton}"/>

</ResourceDictionary>
