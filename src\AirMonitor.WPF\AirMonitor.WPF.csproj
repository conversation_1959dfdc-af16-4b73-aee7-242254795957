﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\AirMonitor.Services\AirMonitor.Services.csproj" />
    <ProjectReference Include="..\AirMonitor.Core\AirMonitor.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- MVVM Toolkit -->
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />

    <!-- Dependency Injection -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />

    <!-- Logging -->
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
      <PackageReference Include="Syncfusion.SfGrid.WPF" Version="29.2.9" />
</ItemGroup>

  <!-- Syncfusion WPF Controls -->
  <ItemGroup>
    <!-- Chart Controls -->
    <PackageReference Include="Syncfusion.SfChart.WPF" Version="29.2.9" />

    <!-- Data Grid Controls -->

    <!-- Gauge Controls -->
    <PackageReference Include="Syncfusion.SfGauge.WPF" Version="29.2.9" />

    <!-- Scheduler Controls -->
    <PackageReference Include="Syncfusion.SfScheduler.WPF" Version="29.2.9" />

    <!-- Windows 11 Themes -->
    <PackageReference Include="Syncfusion.Themes.Windows11Dark.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.Themes.Windows11Light.WPF" Version="29.2.9" />
      <PackageReference Include="Syncfusion.SfGrid.WPF" Version="29.2.9" />
</ItemGroup>

</Project>
