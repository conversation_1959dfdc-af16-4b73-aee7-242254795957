<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:input="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.Wpf">

    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/PrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatPrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/ColorPicker/ColorPicker.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="SyncfusionAppointmentEditorColorPickerForegroundStyle" TargetType="{x:Type input:ColorPicker}"
           BasedOn="{StaticResource SyncfusionColorPickerStyle}">
        <Setter Property="Brush" Value="{StaticResource PrimaryForeground}" />
    </Style>

    <Style x:Key="SyncfusionAppointmentEditorColorPickerBackgroundStyle" TargetType="{x:Type input:ColorPicker}" 
           BasedOn="{StaticResource SyncfusionColorPickerStyle}">
        <Setter Property="Brush" Value="{StaticResource PrimaryBackground}" />
    </Style>

    <Style x:Key="SyncfusionAppointmentEditorFlatButtonStyle"
            BasedOn="{StaticResource WPFButtonStyle}"
            TargetType="{x:Type Button}" >
    </Style>

    <Style x:Key="SyncfusionAppointmentEditorPrimaryButtonStyle"
            BasedOn="{StaticResource WPFPrimaryButtonStyle}"
            TargetType="{x:Type Button}" >
    </Style>

    <Style x:Key="SyncfusionRecurrenceEditorPrimaryButtonStyle"
            BasedOn="{StaticResource WPFPrimaryButtonStyle}"
            TargetType="{x:Type Button}" >
    </Style>

    <Style x:Key="SyncfusionEditorFlatButtonStyle"
            BasedOn="{StaticResource WPFButtonStyle}"
            TargetType="{x:Type Button}" >
    </Style>

    <Style x:Key="SyncfusionAppointmentEditorGlyphButtonStyle"
            BasedOn="{StaticResource WPFGlyphButtonStyle}"
            TargetType="{x:Type Button}" >
    </Style>

    <Style x:Key="SyncfusionReminderCloseButtonStyle" TargetType="{x:Type Path}">
        <Setter Property="Data">
            <Setter.Value>
                <PathGeometry>M11 1L1 11M1 1L11 11</PathGeometry>
            </Setter.Value>
        </Setter>
        <Setter Property="Stroke" Value="{StaticResource IconColor}"/>
    </Style>

    <DataTemplate x:Key="SyncfusionSchedulerColorPickerHeaderTemplate">
        <StackPanel Orientation="Horizontal">
            <Border BorderBrush="LightGray" 
                    BorderThickness="1"
                    x:Name="selectedColorRect"
                    Width="13"
                    Height="13" 
                    CornerRadius="{StaticResource Windows11Light.CornerRadius2}"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Top"
                    Background="{Binding Brush, RelativeSource={RelativeSource FindAncestor, AncestorLevel=1, AncestorType={x:Type input:ColorPicker}}}" />
        </StackPanel>
    </DataTemplate>

</ResourceDictionary>
