<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    
    xmlns:Microsoft_Windows_Aero="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphRepeatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/RepeatButton.xaml" />

    </ResourceDictionary.MergedDictionaries>
   
    <SolidColorBrush x:Key="IntegerTextBox.Static.Border" Color="#333333" />

    <SolidColorBrush x:Key="IntegerTextBox.MouseOver.Border" Color="#d1eaf5" />

    <sys:Double x:Key="IntegerTextBox.Border.Static.Opacity">1</sys:Double>

    <LinearGradientBrush x:Key="IntegerTextBox.Static.BorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="IntegerTextBox.Static.BorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="IntegerTextBox.Static.BorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <shared:ProgressConverter x:Key="ProgressConverter" />
    <shared:ClipConverter x:Key="ClipConverter" />
    <shared:RadiusConverter x:Key="CornerRadiusConverter" />
    <shared:BooleanToVisibilityConverterEx x:Key="BooleanToVisibilityConverter" />

    <DataTemplate x:Key="WatermarkContentTemplate">
        <TextBlock Style="{x:Null}" Text="{Binding}" />
    </DataTemplate>

    <Style x:Key="SyncfusionIntegerTextBoxStyle" TargetType="{x:Type shared:IntegerTextBox}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}" />
        <Setter Property="SelectionBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="PositiveForeground" Value="{StaticResource ContentForeground}" />
        <Setter Property="NegativeForeground" Value="{StaticResource ContentForeground}" />
        <Setter Property="ZeroColor" Value="{StaticResource ContentForeground}" />
        <Setter Property="RangeAdornerBackground" Value="{StaticResource BorderAlt4}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="BorderBrush" Value="{StaticResource IntegerTextBox.Static.BorderBrush}" />
        <Setter Property="WatermarkTextForeground" Value="{StaticResource PlaceholderForeground}" />
        <Setter Property="WatermarkBackground" Value="{StaticResource ContentBackground}" />
        <Setter Property="WatermarkTemplate" Value="{StaticResource WatermarkContentTemplate}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FocusedBorderBrush" Value="{StaticResource BorderAlt2}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant1}" />
        <Setter Property="CornerRadius" Value="{StaticResource Windows11Dark.ThemeCornerRadiusVariant1}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="WatermarkOpacity" Value="1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:IntegerTextBox}">
                    <Grid x:Name="ContentHost">
                        <Border
                            x:Name="Border"
                            Padding="3 1 1 1"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            SnapsToDevicePixels="True">
                            <Border.Clip>
                                <RectangleGeometry RadiusX="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Border}}, Path=CornerRadius, Converter={StaticResource CornerRadiusConverter}}" RadiusY="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Border}}, Path=CornerRadius, Converter={StaticResource CornerRadiusConverter}}">
                                    <RectangleGeometry.Rect>
                                        <MultiBinding Converter="{StaticResource ClipConverter}">
                                            <Binding Path="ActualWidth" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type Border}}" />
                                            <Binding Path="ActualHeight" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type Border}}" />
                                        </MultiBinding>
                                    </RectangleGeometry.Rect>
                                </RectangleGeometry>
                            </Border.Clip>
                            <Grid x:Name="InnerContentHost">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Border
                                    x:Name="RangeAdornerBorder"
                                    Background="{TemplateBinding RangeAdornerBackground}"
                                    BorderThickness="{StaticResource Windows11Dark.BorderThickness}"
                                    IsHitTestVisible="False"
                                    Visibility="Collapsed"
                                    Opacity="{StaticResource IntegerTextBox.Border.Static.Opacity}">
                                    <Border.RenderTransform>
                                        <ScaleTransform>
                                            <ScaleTransform.ScaleX>
                                                <MultiBinding Converter="{StaticResource ProgressConverter}">
                                                    <Binding Path="Value" RelativeSource="{RelativeSource TemplatedParent}" />
                                                    <Binding Path="ActualWidth" RelativeSource="{RelativeSource TemplatedParent}" />
                                                    <Binding Path="MaxValue" RelativeSource="{RelativeSource TemplatedParent}" />
                                                    <Binding Path="MinValue" RelativeSource="{RelativeSource TemplatedParent}" />
                                                </MultiBinding>
                                            </ScaleTransform.ScaleX>
                                        </ScaleTransform>
                                    </Border.RenderTransform>
                                </Border>

                                <ScrollViewer
                                    x:Name="PART_ContentHost"
                                    VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Background="Transparent"
                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                    Visibility="{TemplateBinding ContentElementVisibility}" />

                                <ContentControl
                                    x:Name="PART_Watermark"
                                    Grid.Row="0"
                                    Padding="{TemplateBinding Padding}"
                                    Margin="4 1 1 1"
                                    VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Background="{TemplateBinding WatermarkBackground}"
                                    Content="{TemplateBinding WatermarkText}"
                                    ContentTemplate="{TemplateBinding WatermarkTemplate}"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                    FontStretch="{TemplateBinding FontStretch}"
                                    FontStyle="{TemplateBinding FontStyle}"
                                    FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                                    Foreground="{TemplateBinding WatermarkTextForeground}"
                                    IsHitTestVisible="False"
                                    IsTabStop="False"
                                    Opacity="{TemplateBinding WatermarkOpacity}"
                                    Visibility="{TemplateBinding WatermarkVisibility}" />
                                <Grid x:Name="spinButtonGrid" HorizontalAlignment="Stretch"  Visibility="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=ShowSpinButton,Converter={StaticResource BooleanToVisibilityConverter}}" VerticalAlignment="Stretch" Grid.Column="1">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="1*"/>
                                        <RowDefinition Height="1*"/>
                                    </Grid.RowDefinitions>

                                    <RepeatButton
                                    x:Name="upbutton"
                                    Width="NaN"
                                    Cursor="Arrow"
                                    Margin="0"
                                    Padding="5 0 5 0"
                                    BorderThickness="{StaticResource Windows11Dark.BorderThickness}"
                                    Background="Transparent"
                                    Focusable="{TemplateBinding Focusable}"
                                    IsTabStop="False"
                                    SnapsToDevicePixels="True"
                                    Style="{StaticResource WPFRepeatButtonStyle}">
                                        <Path
                                        x:Name="upbuttonpath"
                                        Width="10"
                                        Height="6"
                                        HorizontalAlignment="Stretch"
                                        VerticalAlignment="Stretch"
                                        Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                        SnapsToDevicePixels="True"
                                        Stretch="Uniform" >
                                            <Path.Data>
                                                <PathGeometry>M0.5 4.875C0.5 4.77344 0.537109 4.68555 0.611328 4.61133L4.73633 0.486328C4.81055 0.412109 4.89844 0.375 5 0.375C5.10156 0.375 5.18945 0.412109 5.26367 0.486328L9.38867 4.61133C9.46289 4.68555 9.5 4.77344 9.5 4.875C9.5 4.97656 9.46289 5.06445 9.38867 5.13867C9.31445 5.21289 9.22656 5.25 9.125 5.25C9.02344 5.25 8.93555 5.21289 8.86133 5.13867L5 1.2832L1.13867 5.13867C1.06445 5.21289 0.976562 5.25 0.875 5.25C0.773438 5.25 0.685547 5.21289 0.611328 5.13867C0.537109 5.06445 0.5 4.97656 0.5 4.875Z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </RepeatButton>
                                    <RepeatButton
                                    x:Name="downbutton"
                                    Grid.Row="1"
                                    Width="NaN"
                                    Cursor="Arrow"
                                    Margin="0"
                                    Padding="5 0 5 0"
                                    Background="Transparent"
                                    BorderThickness="{StaticResource Windows11Dark.BorderThickness}"
                                    Focusable="{TemplateBinding Focusable}"
                                    IsTabStop="False"
                                    SnapsToDevicePixels="True"
                                    Style="{StaticResource WPFRepeatButtonStyle}">
                                        <Path
                                        x:Name="downbuttonpath"
                                        Width="10"
                                        Height="6"
                                        HorizontalAlignment="Stretch"
                                        VerticalAlignment="Stretch"
                                        Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                        SnapsToDevicePixels="True"
                                        Stretch="Uniform" >
                                            <Path.Data>
                                                <PathGeometry>M0.5 1.125C0.5 1.02344 0.537109 0.935547 0.611328 0.861328C0.685547 0.787109 0.773438 0.75 0.875 0.75C0.976562 0.75 1.06445 0.787109 1.13867 0.861328L5 4.7168L8.86133 0.861328C8.93555 0.787109 9.02344 0.75 9.125 0.75C9.22656 0.75 9.31445 0.787109 9.38867 0.861328C9.46289 0.935547 9.5 1.02344 9.5 1.125C9.5 1.22656 9.46289 1.31445 9.38867 1.38867L5.26367 5.51367C5.18945 5.58789 5.10156 5.625 5 5.625C4.89844 5.625 4.81055 5.58789 4.73633 5.51367L0.611328 1.38867C0.537109 1.31445 0.5 1.22656 0.5 1.125Z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </RepeatButton>
                                </Grid>
                            </Grid>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter Property="MinHeight" Value="16" TargetName="downbutton"/>
                            <Setter Property="MinHeight" Value="16" TargetName="upbutton"/>
                            <Setter Property="VerticalAlignment" Value="Center" TargetName="downbutton"/>
                            <Setter Property="VerticalAlignment" Value="Center" TargetName="upbutton"/>
                        </Trigger>
                        <Trigger Property="EnableRangeAdorner" Value="False">
                            <Setter TargetName="Border" Property="Clip" Value="{x:Null}" />
                        </Trigger>
                        <Trigger Property="IsReadOnly" Value="True">
							<Setter TargetName="upbutton" Property="IsEnabled" Value="False" />
                            <Setter TargetName="downbutton" Property="IsEnabled" Value="False" />
                           
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource IntegerTextBox.Static.BorderBrushFocused}" />
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}"/>
                            <Setter Property="Padding" Value="1,0,0,-1" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="ApplyNegativeForeground" Value="True" />
                                <Condition Property="IsNegative" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter Property="TextElement.Foreground" Value="{Binding RelativeSource={RelativeSource Self}, Path=NegativeForeground}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="ApplyZeroColor" Value="True" />
                                <Condition Property="IsZero" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter Property="TextElement.Foreground" Value="{Binding RelativeSource={RelativeSource Self}, Path=ZeroColor}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsNegative" Value="False" />
                                <Condition Property="IsZero" Value="False" />
                                <Condition Property="IsNull" Value="False" />
                                <Condition Property="EnableRangeAdorner" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter Property="TextElement.Foreground" Value="{Binding RelativeSource={RelativeSource Self}, Path=PositiveForeground}" />
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="EnableRangeAdorner" Value="True" />
                                <Condition Property="IsFocused" Value="False" />
                                <Condition Property="IsKeyboardFocused" Value="False" />
                                <Condition Property="IsKeyboardFocusWithin" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="RangeAdornerBorder" Property="Visibility" Value="Visible" />
                            <Setter TargetName="RangeAdornerBorder" Property="CornerRadius" Value="{StaticResource Windows11Dark.CornerRadius4}"/>
                            <Setter TargetName="RangeAdornerBorder" Property="Margin" Value="-4,-1,-1,-1"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource IntegerTextBox.Static.BorderBrushHovered}" />
                            <Setter TargetName="PART_Watermark" Property="Background" Value="{StaticResource ContentBackgroundAlt5}" />
                            <Setter TargetName="PART_Watermark" Property="Foreground" Value="{StaticResource PlaceholderForeground}" />
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt5}" />
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource IntegerTextBox.Static.BorderBrushFocused}" />
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionIntegerTextBoxStyle}" TargetType="{x:Type shared:IntegerTextBox}" />

</ResourceDictionary>
