<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib" 
                    xmlns:skinManager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:grid="clr-namespace:Syncfusion.UI.Xaml.Grid;assembly=Syncfusion.SfGrid.WPF">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatPrimaryButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/PrimaryButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphDropdownExpander.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphEditableDropdownExpander.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/SfDataGrid/SfDataGrid.xaml" />
        <skinManager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <skinManager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <!--SfMulticolumnDropdown control style Keys-->
    <SolidColorBrush x:Key="MultiColumnDropDown.Static.Border" Color="#9E9E9E " />

    <SolidColorBrush x:Key="MultiColumnDropDown.MouseOver.Border" Color="#757575" />

    <SolidColorBrush x:Key="MultiColumnDropDown.Pressed.Border" Color="#0279FF" />

    <SolidColorBrush x:Key="MultiColumnDropDown.Focused.Border" Color="#0279FF" />

    <LinearGradientBrush x:Key="MultiColumnDropDownBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="MultiColumnDropDownBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="MultiColumnDropDownBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="MultiColumnDropDownBorderBrushPressed" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="SyncfusionMultiColumnDropDownTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource WPFTextBoxStyle}">
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="TextWrapping" Value="NoWrap" />
        <Setter Property="IsReadOnlyCaretVisible" Value="True" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="Margin">
            <Setter.Value>
                <Thickness>4,0,0,0</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">
                    <ScrollViewer x:Name="PART_ContentHost" 
                                  Background="Transparent" 
					              BorderBrush="Transparent" 
                                  Focusable="false" 
                                  Margin="-2,0,0,0" 
                                  HorizontalScrollBarVisibility="Hidden" 
                                  VerticalScrollBarVisibility="Hidden"/>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsReadOnly" Value="True">
                            <Setter TargetName="PART_ContentHost" Property="Background" Value="Transparent"/>
                            <Setter TargetName="PART_ContentHost" Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="PART_ContentHost" Property="Background" Value="Transparent"/>
                            <Setter TargetName="PART_ContentHost" Property="BorderBrush" Value="{StaticResource MultiColumnDropDown.MouseOver.Border}"/>
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="PART_ContentHost" Property="Background" Value="Transparent"/>
                            <Setter TargetName="PART_ContentHost" Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="PopupStyle" TargetType="Popup">
        <Setter Property="AllowsTransparency" Value="False" />
        <Setter Property="Placement" Value="Absolute" />
        <Setter Property="StaysOpen" Value="False" />
        <Setter Property="PopupAnimation" Value="Slide" />
    </Style>

    <Style x:Key="GlyphEditableToggleButtonStyle" TargetType="ToggleButton" BasedOn="{StaticResource WPFGlyphEditableDropdownExpanderStyle}">
        <Setter Property="ClickMode" Value="Release"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
    </Style>

    <Style x:Key="GlyphToggleButtonStyle" TargetType="ToggleButton" BasedOn="{StaticResource WPFGlyphDropdownExpanderStyle}">
        <Setter Property="ClickMode" Value="Release"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Focusable" Value="True"/>
        <Setter Property="IsTabStop" Value="True"/>
    </Style>

    <Style TargetType="{x:Type grid:SfMultiColumnDropDownControl}" x:Key="SyncfusionSfMultiColumnDropDownControlStyle">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Padding" Value="2"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}"/>
        <Setter Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrush}"/>        
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="PopupBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="PopupBorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}"/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="PopupDropDownGridBackground" Value="{StaticResource PopupBackground}" />
        <Setter Property="PopupBackground" Value="{StaticResource PopupBackground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="MinHeight" Value="26"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type grid:SfMultiColumnDropDownControl}">
                    <Border x:Name="PART_RootBorder"
                            Width="{TemplateBinding Width}"
                            MinHeight="{TemplateBinding MinHeight}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="Transparent"
                            BorderThickness="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}" 
                            Grid.Row="0">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="SelectionStates">
                                <VisualState x:Name="Single">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootGrid" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame Value="{x:Static Visibility.Visible}" KeyTime="0:0:0"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_MultiSelectRootGrid" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame Value="{x:Static Visibility.Collapsed}" KeyTime="0:0:0" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame Value="{x:Static Visibility.Collapsed}" KeyTime="0:0:0"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_StackPanel" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame Value="{x:Static Visibility.Collapsed}" KeyTime="0:0:0"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Multiple">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootGrid" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame Value="{x:Static Visibility.Collapsed}" KeyTime="0:0:0"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_MultiSelectRootGrid" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame Value="{x:Static Visibility.Visible}" KeyTime="0:0:0"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame Value="{x:Static Visibility.Visible}" KeyTime="0:0:0"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_StackPanel" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame Value="{x:Static Visibility.Visible}" KeyTime="0:0:0"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid Margin="-1">
                            <Popup x:Name="PART_Popup"
                                   AllowsTransparency="True"
                                   MinWidth="{TemplateBinding PopupMinWidth}"
                                   MinHeight="{TemplateBinding PopupMinHeight}"
                                   MaxWidth="{TemplateBinding PopupMaxWidth}"
                                   MaxHeight="{TemplateBinding PopupMaxHeight}"
                                   IsOpen="{Binding Path=IsDropDownOpen,
                                                    Mode=TwoWay,
                                                    RelativeSource={RelativeSource TemplatedParent}}"
                                   Style="{StaticResource PopupStyle}">
                                <Border Name="PART_PopupBorder"
                                        Background="{TemplateBinding PopupBackground}"
                                        BorderBrush="{TemplateBinding PopupBorderBrush}"
                                        BorderThickness="{TemplateBinding PopupBorderThickness}"
                                        Effect="{StaticResource Default.ShadowDepth4}"
                                        CornerRadius="{StaticResource Windows11Light.CornerRadius4}"> 
                                    <Border.Margin>
                                        <Thickness>14,0,14,14</Thickness>
                                    </Border.Margin>

                                    <Grid Background="{TemplateBinding PopupBackground}" Margin="4">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <ContentPresenter x:Name="PART_ContentPresenter" 
                                                          ContentTemplate="{Binding HeaderTemplate, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" 
                                                          Visibility="Collapsed"/>
                                        <ContentControl x:Name="PART_PopupContent" Grid.Row="1">
                                            <grid:SfDataGrid x:Name="PART_SfDataGrid"
                                                              Margin="0"
                                                              BorderThickness="0"
                                                              AllowDraggingColumns="False"
                                                              AllowEditing="False"
                                                              AllowFiltering="False"
                                                              AllowGrouping="False"
                                                              AllowResizingColumns="False"
                                                              AllowRowHoverHighlighting="True"
                                                              AutoGenerateColumns="{TemplateBinding AutoGenerateColumns}"
                                                              Background="{TemplateBinding PopupDropDownGridBackground}"
                                                              ColumnSizer="{TemplateBinding GridColumnSizer}"
                                                              FocusVisualStyle="{x:Null}"
                                                              Focusable="False"
                                                              ItemsSource="{TemplateBinding ItemsSource}"
                                                              NavigationMode="Row"
                                                              SelectedIndex="{Binding Path=SelectedIndex,
                                                                                      RelativeSource={RelativeSource TemplatedParent},
                                                                                      Mode=TwoWay}">
                                                <grid:SfDataGrid.Style>
                                                    <Style TargetType="{x:Type grid:SfDataGrid}" BasedOn="{StaticResource SyncfusionSfDataGridStyle}">
                                                        <Style.Resources>
                                                            <Style TargetType="{x:Type grid:GridCell}" BasedOn="{StaticResource SyncfusionGridCellStyle}">
                                                                <Setter Property="Padding" Value="4,0,4,0"/>
                                                            </Style>
                                                        </Style.Resources>
                                                    </Style>
                                                </grid:SfDataGrid.Style>
                                            </grid:SfDataGrid>
                                                                  
                                        </ContentControl>
                                        <Grid Grid.Row="2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <StackPanel x:Name="PART_StackPanel"
                                                        HorizontalAlignment="Right"
                                                        Orientation="Horizontal" 
                                                        Visibility="Collapsed">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                               
                                                <Button x:Name="PART_OkButton"
                                                        Grid.Column="0"
                                                        Height="24"
                                                        Width="62"
                                                        HorizontalAlignment="Center"
                                                        Content="{grid:GridLocalizationResourceExtension ResourceName=OK}"
                                                        Style="{StaticResource WPFPrimaryButtonStyle}">
                                                    <Button.Margin>
                                                        <Thickness>0,12,0,12</Thickness>
                                                    </Button.Margin>
                                                </Button>

                                                <Button x:Name="PART_CancelButton"
                                                        Grid.Column="1"
                                                        Height="24"
                                                        Width="62"
                                                        HorizontalAlignment="Center"
                                                        Content="{grid:GridLocalizationResourceExtension ResourceName=Cancel}"
                                                        >
                                                    <Button.Margin>
                                                        <Thickness>12,12,5,12</Thickness>
                                                    </Button.Margin>
                                                </Button>
                                                </Grid>
                                            </StackPanel>
                                            <Thumb x:Name="PART_ThumbGripper" 
                                                   Grid.Column="1"
                                                   Visibility="{TemplateBinding ResizingThumbVisibility}"
                                                   HorizontalAlignment="Right" 
                                                   VerticalAlignment="Bottom"
                                                   Cursor="SizeNWSE">
                                                <Thumb.Margin>
                                                    <Thickness>0,0,2,2</Thickness>
                                                </Thumb.Margin>
                                                <Thumb.Template>
                                                    <ControlTemplate>
                                                        <Grid Background="Transparent">
                                                            <Path Width="8"
                                                                  Height="8"
                                                                  Data="M36.396,36.017 L47.901,36.017 47.901,47.521999 36.396,47.521999 z M18.198,36.017 L29.716,36.017 29.716,47.521999 18.198,47.521999 z M0,36.017 L11.511999,36.017 11.511999,47.521999 0,47.521999 z M36.396,18.191001 L47.901,18.191001 47.901,29.696 36.396,29.696 z M18.198,18.191 L29.716,18.191 29.716,29.696 18.198,29.696 z M36.396,0 L47.901,0 47.901,11.512 36.396,11.512 z"
                                                                  Fill="{StaticResource IconColorDisabled}"
                                                                  Stretch="Fill" />
                                                            
                                                        </Grid>
                                                    </ControlTemplate>
                                                </Thumb.Template>
                                            </Thumb>
                                        </Grid>
                                    </Grid>
                                </Border>
                            </Popup>
                            <Line x:Name="PART_DisabledBorder" 
                              Visibility="Collapsed" 
                              HorizontalAlignment="Stretch" 
                              VerticalAlignment="Bottom"
                              X2="{Binding Path=ActualWidth, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                              StrokeThickness="2"
                              StrokeDashArray="1.5" />
                            <Grid x:Name="PART_RootGrid" SnapsToDevicePixels="True">
                                <Grid.RowDefinitions>
                                    <RowDefinition/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <TextBox x:Name="PART_TextBox"
                                     Padding="{TemplateBinding Padding}"
                                     Grid.Column="0"
                                     FlowDirection="{TemplateBinding FlowDirection}"
                                     FontFamily="{TemplateBinding FontFamily}"
                                     FontSize="{TemplateBinding FontSize}"
                                     FontStretch="{TemplateBinding FontStretch}"
                                     FontStyle="{TemplateBinding FontStyle}"
                                     FontWeight="{TemplateBinding FontWeight}"
                                     Foreground="{TemplateBinding Foreground}"
                                     IsReadOnly="{TemplateBinding ReadOnly}"
                                     Style="{StaticResource SyncfusionMultiColumnDropDownTextBoxStyle}"
                                     TabIndex="{TemplateBinding TabIndex}"
                                     Text="{Binding Path=Text,
                                                    RelativeSource={RelativeSource TemplatedParent},
                                                    Mode=TwoWay}"
                                     TextAlignment="{TemplateBinding TextAlignment}" />
                                <Border x:Name="PART_Border" Grid.ColumnSpan="2"
                                    Margin="0"
                                    BorderThickness="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}"
                                    CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                    BorderBrush="{TemplateBinding BorderBrush}"/>
                                <ToggleButton x:Name="PART_ToggleButton"
                                              Grid.Column="1"
                                              Grid.RowSpan="2"
                                              VerticalContentAlignment="Center"
                                              Width="24"
                                              Height="18"
                                              Style="{StaticResource GlyphEditableToggleButtonStyle}">
                                    <ToggleButton.Margin>
                                        <Thickness>0,3,5,3</Thickness>
                                    </ToggleButton.Margin>
                                </ToggleButton>

                            </Grid>
                            <Grid x:Name="PART_MultiSelectRootGrid" SnapsToDevicePixels="True" Visibility="Collapsed">
                                <Grid.RowDefinitions>
                                    <RowDefinition/>
                                    <RowDefinition  Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition MinWidth="{DynamicResource {x:Static SystemParameters.VerticalScrollBarWidthKey}}" Width="0" />
                                </Grid.ColumnDefinitions>

                                <Border x:Name="PART_MultiSelectBorder" 
                                    Grid.ColumnSpan="2"
                                    Grid.RowSpan="2"
                                    Margin="0"
                                    Background="Transparent"
                                    BorderThickness="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}"
                                    CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                    BorderBrush="{TemplateBinding BorderBrush}"/>
                                <ToggleButton x:Name="PART_MultiSelectToggleButton"
                                              Grid.ColumnSpan="2"
                                              Grid.RowSpan="2"
                                              VerticalContentAlignment="Center"
                                              HorizontalContentAlignment="Right"
                                              Style="{StaticResource GlyphToggleButtonStyle}">
                                    <ToggleButton.Margin>
                                        <Thickness>1,1,4,1</Thickness>
                                    </ToggleButton.Margin>
                                </ToggleButton>

                                <ItemsControl x:Name="PART_ItemsControl"
                                          FocusVisualStyle="{x:Null}"
                                          IsTabStop="False"
                                          IsHitTestVisible="False"
                                          FontFamily="{TemplateBinding FontFamily}"
                                          FontSize="{TemplateBinding FontSize}"
                                          FontWeight="{TemplateBinding FontWeight}"
                                          MaxWidth="{Binding Path=ActualWidth,
                                                             RelativeSource={RelativeSource TemplatedParent}}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.Margin>
                                        <Thickness>6,2,2,2</Thickness>
                                    </ItemsControl.Margin>
                                </ItemsControl>
                            </Grid>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinManager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="34"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="BorderBrush" TargetName="PART_Border" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="BorderBrush" TargetName="PART_MultiSelectBorder" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="BorderThickness" TargetName="PART_Border" Value="1"/>
                            <Setter Property="BorderThickness" TargetName="PART_MultiSelectBorder" Value="1"/>
                            <Setter Property="Visibility" TargetName="PART_DisabledBorder" Value="Hidden"/>
                            <Setter Property="Stroke" TargetName="PART_DisabledBorder" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Opacity" TargetName="PART_ItemsControl" Value="1"/>
                            <Setter Property="TextElement.Foreground" TargetName="PART_ItemsControl" Value="{StaticResource DisabledForeground}"/>
                            <Setter Property="Foreground" TargetName="PART_TextBox" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>

                        <!-- Single Selection Triggers -->

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" SourceName="PART_TextBox" Value="false"/>
                                <Condition Property="IsMouseOver" SourceName="PART_ToggleButton" Value="true"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushHovered}"/>
                            <Setter TargetName="PART_TextBox" Property="Foreground" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="Background" TargetName="PART_RootGrid" Value="{StaticResource ContentBackgroundAlt5}"/>
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" SourceName="PART_TextBox" Value="true"/>
                                <Condition Property="IsFocused" SourceName="PART_TextBox" Value="False"/>
                                <Condition Property="IsMouseOver" SourceName="PART_ToggleButton" Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushHovered}"/>
                            <Setter TargetName="PART_TextBox" Property="Foreground" Value="{StaticResource ContentForeground}"/> 
                            <Setter Property="Background" TargetName="PART_RootGrid" Value="{StaticResource ContentBackgroundAlt5}"/>
                        </MultiTrigger>

                        <Trigger Property="IsPressed" SourceName="PART_ToggleButton" Value="true">
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushPressed}"/>
                            <Setter TargetName="PART_Border" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                            <Setter TargetName="PART_TextBox" Property="Foreground" Value="{StaticResource ContentForeground}"/>
                            <Setter TargetName="PART_TextBox" Property="Background" Value="{StaticResource ContentBackground}"/>
                        </Trigger>

                        <Trigger Property="IsFocused" SourceName="PART_TextBox" Value="true">
                            <Setter TargetName="PART_ToggleButton" Property="BorderBrush" Value="Transparent"/>
                            <Setter TargetName="PART_ToggleButton" Property="Background" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter TargetName="PART_ToggleButton" Property="Foreground" Value="{StaticResource IconColor}"/>
                            <Setter TargetName="PART_TextBox" Property="Background" Value="{StaticResource ContentBackground}"/>
                            <Setter TargetName="PART_TextBox" Property="Foreground" Value="{StaticResource ContentForeground}"/>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushFocused}"/>
                            <Setter TargetName="PART_Border" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" SourceName="PART_TextBox" Value="true"/>
                                <Condition Property="IsFocused" SourceName="PART_ToggleButton" Value="false"/>
                                <Condition Property="IsChecked" SourceName="PART_ToggleButton" Value="true"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushFocused}"/>
                            <Setter TargetName="PART_Border" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                            <Setter TargetName="PART_ToggleButton" Property="Foreground" Value="{StaticResource IconColorHovered}"/>
                            <Setter TargetName="PART_ToggleButton" Property="Background" Value="Transparent"/>
                            <Setter TargetName="PART_ToggleButton" Property="BorderBrush" Value="Transparent"/>
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" SourceName="PART_TextBox" Value="true"/>
                                <Condition Property="IsFocused" SourceName="PART_ToggleButton" Value="false"/>
                                <Condition Property="IsChecked" SourceName="PART_ToggleButton" Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Border" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushFocused}"/>
                            <Setter TargetName="PART_ToggleButton" Property="Foreground" Value="{StaticResource IconColor}"/>
                            <Setter TargetName="PART_ToggleButton" Property="Background" Value="Transparent"/>
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" SourceName="PART_TextBox" Value="true"/>
                                <Condition Property="IsPressed" SourceName="PART_ToggleButton" Value="true"/>
                                <Condition Property="IsFocused" SourceName="PART_ToggleButton" Value="false"/>
                                <Condition Property="IsChecked" SourceName="PART_ToggleButton" Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_ToggleButton" Property="Foreground" Value="{StaticResource IconColorHovered}"/>
                        </MultiTrigger>

                        <!-- Multi Selection Triggers -->

                        <Trigger Property="IsMouseOver" SourceName="PART_MultiSelectToggleButton" Value="true">
                            <Setter TargetName="PART_MultiSelectBorder" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushHovered}"/>
                            <Setter TargetName="PART_ItemsControl" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="Background" TargetName="PART_MultiSelectBorder" Value="{StaticResource ContentBackgroundAlt5}"/>
                        </Trigger>

                        <Trigger Property="IsPressed" SourceName="PART_MultiSelectToggleButton" Value="true">
                            <Setter TargetName="PART_MultiSelectBorder" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushPressed}"/>
                            <Setter TargetName="PART_MultiSelectBorder" Property="Margin" Value="0,-1,0,0"/>
                            <Setter TargetName="PART_MultiSelectBorder" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                        </Trigger>

                        <Trigger Property="IsChecked" SourceName="PART_MultiSelectToggleButton" Value="true">
                            <Setter TargetName="PART_MultiSelectBorder" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushFocused}"/>
                            <Setter TargetName="PART_MultiSelectBorder" Property="Margin" Value="0,-1,0,0"/>
                            <Setter TargetName="PART_MultiSelectBorder" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                        </Trigger>

                        <Trigger Property="IsFocused" SourceName="PART_MultiSelectToggleButton" Value="true">
                            <Setter TargetName="PART_MultiSelectBorder" Property="Margin" Value="0,-1,0,0"/>
                            <Setter TargetName="PART_MultiSelectBorder" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                            <Setter TargetName="PART_MultiSelectBorder" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushFocused}"/>
                        </Trigger>
                        
                        <Trigger Property="IsFocused" SourceName="PART_ItemsControl" Value="true">
                            <Setter TargetName="PART_MultiSelectBorder" Property="Margin" Value="0,-1,0,0"/>
                            <Setter TargetName="PART_MultiSelectBorder" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                            <Setter TargetName="PART_MultiSelectBorder" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushFocused}"/>
                            <Setter TargetName="PART_MultiSelectToggleButton" Property="Foreground" Value="{StaticResource IconColorHovered}"/>
                        </Trigger>
                        
                        <Trigger Property="IsDropDownOpen" Value="true">
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushFocused}"/>
                            <Setter TargetName="PART_Border" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                            <Setter TargetName="PART_MultiSelectBorder" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                            <Setter TargetName="PART_MultiSelectBorder" Property="BorderBrush" Value="{StaticResource MultiColumnDropDownBorderBrushFocused}"/>
                            <Setter TargetName="PART_MultiSelectBorder" Property="Margin" Value="0,-1,0,0"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:SfMultiColumnDropDownControl}" BasedOn="{StaticResource SyncfusionSfMultiColumnDropDownControlStyle}"/>
</ResourceDictionary>
