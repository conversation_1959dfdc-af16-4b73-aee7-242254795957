<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:provider="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    
                    xmlns:fonts="http://schemas.microsoft.com/winfx/2006/xaml/composite-font"
                    xmlns:diagribbon="clr-namespace:Syncfusion.UI.Xaml.DiagramRibbon;assembly=Syncfusion.SfDiagramRibbon.WPF"
                    xmlns:diagribbon_util="clr-namespace:Syncfusion.UI.Xaml.DiagramRibbon.Utility;assembly=Syncfusion.SfDiagramRibbon.WPF"
                    xmlns:diag="clr-namespace:Syncfusion.UI.Xaml.Diagram;assembly=Syncfusion.SfDiagram.WPF"
                    xmlns:sharedutility="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sharedconverters="clr-namespace:Syncfusion.Windows.Converters;assembly=Syncfusion.Shared.WPF"
                    xmlns:syncribbon="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
                    xmlns:syncribbon_control="http://schemas.syncfusion.com/wpf"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:collection="clr-namespace:System.Collections;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <provider:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <provider:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.SfDiagramRibbon.Wpf;component/Themes/Styles.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/Ribbon/Ribbon.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/Ribbon/QATCustomizationDialog.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/Ribbon/QATResetDialog.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/Ribbon/QATAlertDialog.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/ColorPickerPalette/ColorPickerPalette.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <sys:Double x:Key="SfDiagramRibbon.HomeRibbonTab.ArrangeBar.GroupButton.Static.Height">22</sys:Double>

    <diag:GraphConstraints x:Key="UndoableParam">Undoable</diag:GraphConstraints>
    <diag:GraphConstraints x:Key="BridgingParam">Bridging</diag:GraphConstraints>

    <diagribbon_util:PointerToolToBoolConverter x:Key="pointertoolconverter"/>
    <diagribbon_util:DiagramConstraintsToBoolConverter x:Key="graphconstraintstoboolconverter"/>

    <sharedutility:ObjectToBoolConverter x:Key="ConnectorDrawingToolToBoolConverter" TrueValue="{x:Static diag:DrawingTool.Connector}" FalseValue="{x:Null}" CanConvertToTargetType="True"/>
    <sharedutility:ObjectToBoolConverter x:Key="TextNodeDrawingToolToBoolConverter" TrueValue="{x:Static diag:DrawingTool.TextNode}" FalseValue="{x:Null}" CanConvertToTargetType="True"/>
    <sharedutility:ObjectToBoolConverter x:Key="ZoomPanToolToBoolConverter" TrueValue="{x:Static diag:Tool.ZoomPan}" FalseValue="{x:Null}" CanConvertToTargetType="True"/>
    <sharedutility:ObjectToBoolConverter x:Key="PortVisibilityToBoolConverter" TrueValue="{x:Static diag:PortVisibility.Visible}" FalseValue="{x:Null}" CanConvertToTargetType="True"/>
    <sharedutility:ObjectToBoolConverter x:Key="LandscapeOrientationToBoolConverter" TrueValue="{x:Static diag:PageOrientation.Landscape}" FalseValue="{x:Null}" CanConvertToTargetType="True"/>
    <sharedutility:ObjectToBoolConverter x:Key="PortraitOrientationToBoolConverter" TrueValue="{x:Static diag:PageOrientation.Portrait}" FalseValue="{x:Null}" CanConvertToTargetType="True"/>
    <sharedconverters:BrushToColorConverter x:Key="ColorToBrushConverter"></sharedconverters:BrushToColorConverter>

    <diag:FlipParameter x:Key="HorizontalFlipParameter" Flip="HorizontalFlip" FlipMode="Content"/>
    <diag:FlipParameter x:Key="VerticalFlipParameter" Flip="VerticalFlip" FlipMode="Content"/>
    <diag:RotateParameter RotationDirection="AntiClockwise" x:Key="AntiClockwiseRotateParameter"/>
    <diag:ZoomPositionParameter x:Key="ZoomInParameter" ZoomCommand="ZoomIn" ZoomFactor="0.2"/>
    <diag:ZoomPositionParameter x:Key="ZoomOutParameter" ZoomCommand="ZoomOut" ZoomFactor="0.2"/>
    <diag:FitToPageParameter x:Key="FitToPageParameter" FitToPage="FitToPage" CanZoomIn="True" Region="PageSettings"/>
    <diag:FitToPageParameter x:Key="FitToWidthParameter" FitToPage="FitToWidth" CanZoomIn="True" Region="PageSettings"/>
    
    <DataTemplate x:Key="RectangleToolTemplate">
        <Grid>
            <Path x:Name="Rectangle_Draw_Tool_16"
                  Width="15"
                  Height="11"
                  Stretch="Fill"
                  HorizontalAlignment="Center" 
                  VerticalAlignment="Center" 
                  Fill="{StaticResource IconColor}">
                <Path.Data>
                    <PathGeometry>M0 1.5C0 0.671573 0.671573 0 1.5 0H13.5C14.3284 0 15 0.671573 15 1.5V9.5C15 10.3284 14.3284 11 13.5 11H1.5C0.671573 11 0 10.3284 0 9.5V1.5ZM1.5 1C1.22386 1 1 1.22386 1 1.5V9.5C1 9.77614 1.22386 10 1.5 10H13.5C13.7761 10 14 9.77614 14 9.5V1.5C14 1.22386 13.7761 1 13.5 1H1.5Z</PathGeometry>
                </Path.Data>
            </Path>
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="PointerToolTemplate">
        <Grid>
            <Path Height="15"
                  Width="12"
                  Stretch="Fill" 
                  HorizontalAlignment="Center"
                  VerticalAlignment="Center"
                  Fill="{StaticResource IconColor}">
                <Path.Data>
                    <PathGeometry>M4.12787 9.76025C4.41783 9.82626 4.66337 10.0179 4.79784 10.2831L6.65464 13.9458C6.65726 13.951 6.67287 13.9738 6.71424 13.9897C6.75561 14.0056 6.78252 13.9991 6.78792 13.997L8.70575 13.2571L7.03083 9.95323C6.87952 9.65475 6.88753 9.30038 7.05218 9.00905C7.21682 8.71772 7.51627 8.52805 7.85002 8.50371L10.8743 8.28311L1.84793 1.03193C1.82833 1.01618 1.81004 1.00768 1.79432 1.00347L1.00141 11.57C1.00857 11.5774 1.0211 11.588 1.04049 11.5982C1.07139 11.6144 1.09785 11.6177 1.11 11.6172C1.11056 11.6172 1.11106 11.6172 1.11151 11.6171L3.29759 9.94162C3.53362 9.76071 3.83791 9.69424 4.12787 9.76025ZM11.1153 9.26819C12.0108 9.20286 12.3162 8.15869 11.6065 7.58859L2.47421 0.252329C1.82349 -0.270414 0.861917 0.0652087 0.804134 0.835243L0.00273334 11.5149C-0.0616574 12.373 1.02628 12.9424 1.71768 12.4125L3.90591 10.7353L5.76271 14.398C6.01243 14.8906 6.63258 15.1288 7.14785 14.93L9.16501 14.1518C9.68028 13.953 9.89555 13.3925 9.64582 12.8999L7.92277 9.50106L11.1153 9.26819Z</PathGeometry>
                </Path.Data>
            </Path>
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="PanToolTemplete">
        <Grid>
            <Path Height="15" 
                  Width="13" 
                  Stretch="None" 
                  HorizontalAlignment="Center"
                  VerticalAlignment="Center"
                  Fill="{StaticResource IconColor}">
                <Path.Data>
                    <PathGeometry>M5.54976 1.5C5.54976 1.22386 5.77362 1 6.04976 1H6.20126C6.4774 1 6.70126 1.22386 6.70126 1.5V7.82181V8.32181H7.20126H7.20132H7.70132V7.82181V7.50253C7.70133 7.50126 7.70133 7.49998 7.70133 7.49871V2.57289C7.70338 2.29851 7.92645 2.07671 8.20132 2.07671H8.35282C8.62896 2.07671 8.85282 2.30057 8.85282 2.57671V3.63678H8.85283V8.57544C8.85283 8.85158 9.07669 9.07544 9.35283 9.07544C9.62898 9.07544 9.85283 8.85158 9.85283 8.57544V3.64097C9.85938 3.3707 10.0807 3.15347 10.3527 3.15347H10.5042C10.7803 3.15347 11.0042 3.37732 11.0042 3.65347V7.82181V8.32181H11.0044V10.7487C11.0044 12.0864 10.2647 13.2744 9.07855 13.6451C8.42063 13.8508 7.7514 13.9975 7.20134 13.9975C6.57924 13.9975 5.79287 13.7541 5.04557 13.4356C4.4627 13.1871 4.01996 12.7383 3.73956 12.176L3.71397 12.1247L3.67744 12.0805C3.63184 12.0254 3.59003 11.9654 3.55273 11.9008L3.11961 12.1506L3.55273 11.9008L1.06719 7.59175C0.929138 7.35242 1.0112 7.04617 1.25042 6.90793L1.38202 6.83188L1.13185 6.39897L1.38202 6.83188C1.62098 6.69379 1.92633 6.77561 2.06423 7.01468L2.46523 7.70987L3.39834 9.32753V7.46004V2.57671C3.39834 2.30057 3.6222 2.07671 3.89834 2.07671H4.04984C4.32378 2.07671 4.54632 2.29707 4.5498 2.57007L4.55675 2.57003C4.5522 2.59714 4.54983 2.625 4.54983 2.65341V7.49871C4.54983 7.77485 4.77368 7.99871 5.04983 7.99871C5.32597 7.99871 5.54983 7.77485 5.54983 7.49871V2.65341C5.54983 2.6228 5.54708 2.59284 5.54181 2.56375L5.54976 2.5637V1.5ZM12.0044 7.32181H12.0042V3.65347C12.0042 2.82504 11.3326 2.15347 10.5042 2.15347H10.3527C10.1641 2.15347 9.98374 2.18825 9.81752 2.25174C9.66905 1.57955 9.06966 1.07671 8.35282 1.07671H8.20132C8.01271 1.07671 7.83224 1.11152 7.66597 1.17506C7.51751 0.502853 6.91811 0 6.20126 0H6.04976C5.33293 0 4.73354 0.502826 4.58506 1.17501C4.41883 1.1115 4.2384 1.07671 4.04984 1.07671H3.89834C3.06991 1.07671 2.39834 1.74829 2.39834 2.57671V5.97531C1.94717 5.70749 1.36841 5.68478 0.881679 5.96605L0.750079 6.0421C0.0329365 6.45652 -0.212884 7.37394 0.200969 8.09141L2.6865 12.4004C2.74181 12.4963 2.80315 12.5869 2.86984 12.6719C3.24624 13.4011 3.84695 14.0117 4.65341 14.3555C5.42969 14.6864 6.36971 14.9975 7.20134 14.9975C7.90137 14.9975 8.68383 14.8162 9.37688 14.5996C11.0676 14.0711 12.0044 12.4251 12.0044 10.7487V7.82181V7.32181Z</PathGeometry>
                </Path.Data>
            </Path>
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="EllipseToolTemplate">
        <Grid>
            <Path x:Name="Ellipse_Draw_Tool_16"
                  Height="13" 
                  Width="16" 
                  Stretch="Fill" 
                  HorizontalAlignment="Center" 
                  VerticalAlignment="Center"
                  Fill="{StaticResource IconColor}">
                <Path.Data>
                    <PathGeometry>M8 1C4.02412 1 1 3.56078 1 6.5C1 9.43922 4.02412 12 8 12C11.9759 12 15 9.43922 15 6.5C15 3.56078 11.9759 1 8 1ZM0 6.5C0 2.8118 3.6916 0 8 0C12.3084 0 16 2.8118 16 6.5C16 10.1882 12.3084 13 8 13C3.6916 13 0 10.1882 0 6.5Z</PathGeometry>
                </Path.Data>
            </Path>
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="LineToolTemplate">
        <Grid>
            <Path x:Name="Line_Draw_Tool_16" 
                  Width="13" 
                  Height="13" 
                  Stretch="Fill" 
                  VerticalAlignment="Center"
                  HorizontalAlignment="Center"
                  Fill="{StaticResource IconColor}">
                <Path.Data>
                    <PathGeometry>M0.146447 0.146447C0.341709 -0.0488155 0.658291 -0.0488155 0.853553 0.146447L12.8536 12.1464C13.0488 12.3417 13.0488 12.6583 12.8536 12.8536C12.6583 13.0488 12.3417 13.0488 12.1464 12.8536L0.146447 0.853553C-0.0488155 0.658291 -0.0488155 0.341709 0.146447 0.146447Z</PathGeometry>
                </Path.Data>
            </Path>
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="PolyLineToolTemplate">
        <Grid>
            <Path x:Name="Polyline_Draw_Tool_16" 
                  Width="9" 
                  Height="12" 
                  Stretch="Fill"
                  VerticalAlignment="Center" 
                  HorizontalAlignment="Center"
                  Fill="{StaticResource IconColor}">
                <Path.Data>
                    <PathGeometry>M5 1.33317L8.14645 4.39686C8.34171 4.58698 8.65829 4.58698 8.85355 4.39686C9.04882 4.20673 9.04882 3.89848 8.85355 3.70835L5.26326 0.212485C4.79708 -0.241429 4 0.0800529 4 0.721982V10.6668L0.853553 7.60314C0.658291 7.41302 0.341709 7.41302 0.146447 7.60314C-0.0488155 7.79327 -0.0488155 8.10152 0.146447 8.29165L3.73674 11.7875C4.20292 12.2414 5 11.9199 5 11.278V1.33317Z</PathGeometry>
                </Path.Data>
            </Path>
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="FreeHandToolTemplate">
        <Grid>
            <Path x:Name="FreeForm_Draw_Tool_16"  
                  Width="9"
                  Height="13" 
                  Stretch="Fill"
                  HorizontalAlignment="Center"
                  VerticalAlignment="Center"  
                  Fill="{StaticResource IconColor}">
                <Path.Data>
                    <PathGeometry>M0 0.5C0 0.223858 0.223858 0 0.5 0H4.5C5.08001 0 5.9389 0.16989 6.66715 0.694233C7.4209 1.23693 8 2.13704 8 3.5C8 4.08001 7.83011 4.9389 7.30577 5.66715C6.76307 6.4209 5.86296 7 4.5 7C4.08001 7 3.4389 7.13011 2.91715 7.50577C2.4209 7.86307 2 8.46296 2 9.5V9.54138L1.9932 9.5822C1.9248 9.99258 1.97108 10.6039 2.30224 11.0957C2.61248 11.5565 3.23199 12 4.5 12H8.5C8.77614 12 9 12.2239 9 12.5C9 12.7761 8.77614 13 8.5 13H4.5C2.96801 13 2.00419 12.4435 1.47276 11.6543C0.974177 10.9138 0.911291 10.0489 1.00017 9.4596C1.0115 8.11923 1.58657 7.23155 2.33285 6.69423C3.0611 6.16989 3.91999 6 4.5 6C5.53704 6 6.13693 5.5791 6.49423 5.08285C6.86989 4.5611 7 3.91999 7 3.5C7 2.46296 6.5791 1.86307 6.08285 1.50577C5.5611 1.13011 4.91999 1 4.5 1H0.5C0.223858 1 0 0.776142 0 0.5Z</PathGeometry>
                </Path.Data>
            </Path>
        </Grid>
    </DataTemplate>

    <Style TargetType="{x:Type diagribbon:SfDiagramRibbon}" x:Key="SyncfusionSfDiagramRibbonStyle">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type diagribbon:SfDiagramRibbon}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <syncribbon:Ribbon x:Name="Part_Ribbon" Grid.Row="0" EnableSimplifiedLayoutMode="False" ShowCustomizeRibbon="False" ShowDefaultQATKeyTip="False" ShowOptionTab="False" Style="{StaticResource SyncfusionRibbonStyle}">
                            
                            <syncribbon:Ribbon.BackStage>
                                <syncribbon:Backstage x:Name="Part_Backstage" Style="{StaticResource SyncfusionBackstageStyle}">
                                    <syncribbon:BackStageCommandButton Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=NewToolTip}" x:Name="Part_BackstageNew" Style="{StaticResource SyncfusionBackStageCommandButtonStyle}"/>
                                    <syncribbon:BackStageCommandButton Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Open}" x:Name="Part_BackstageOpen" Style="{StaticResource SyncfusionBackStageCommandButtonStyle}"/>
                                    <syncribbon:BackStageCommandButton Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SaveToolTip}" x:Name="Part_BackstageSave" Style="{StaticResource SyncfusionBackStageCommandButtonStyle}"/>
                                    <syncribbon:BackStageCommandButton Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Print}" x:Name="Part_BackstagePrint" Style="{StaticResource SyncfusionBackStageCommandButtonStyle}"/>
                                    <syncribbon:BackStageCommandButton Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Export}" x:Name="Part_BackstageExport" Style="{StaticResource SyncfusionBackStageCommandButtonStyle}"/>
                                    <syncribbon:BackStageCommandButton Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Close}" x:Name="Part_BackstageClose" Style="{StaticResource SyncfusionBackStageCommandButtonStyle}"/>
                                </syncribbon:Backstage>
                            </syncribbon:Ribbon.BackStage>

                            <syncribbon:Ribbon.QuickAccessToolBar>
                                <syncribbon:QuickAccessToolBar Style="{StaticResource SyncfusionQuickAccessToolBarStyle}" QATDropDownVisiblity="Collapsed">
                                    <syncribbon:RibbonButton x:Name="Part_New"
                                                             SizeForm="ExtraSmall"
                                                             IconType="Icon"
                                                             ToolTip="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=NewToolTip}"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Stretch="Fill"
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center"
                                                          Height="16"
                                                          Width="12"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M7 3.5V1H1.5C1.22386 1 1 1.22386 1 1.5V14.5C1 14.7761 1.22386 15 1.5 15H10.5C10.7761 15 11 14.7761 11 14.5V5H8.5C7.67157 5 7 4.32843 7 3.5ZM8 1.41421V3.5C8 3.77614 8.22386 4 8.5 4H10.5858L8 1.41421ZM1.5 0C0.671573 0 0 0.671573 0 1.5V14.5C0 15.3284 0.671573 16 1.5 16H10.5C11.3284 16 12 15.3284 12 14.5V4.62132C12 4.2235 11.842 3.84196 11.5607 3.56066L8.43934 0.43934C8.15804 0.158035 7.7765 0 7.37868 0H1.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                    <syncribbon:RibbonButton x:Name="Part_Load"
                                                             SizeForm="ExtraSmall"
                                                             IconType="Icon"
                                                             ToolTip="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=LoadToolTip}"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Stretch="None"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Height="16"
                                                          Width="15"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M1.15829 1.1451C1.06937 1.26157 1 1.44431 1 1.66667V9.21281L1.62142 5.95664C1.74734 5.2968 2.24256 4.66667 2.97656 4.66667H12.4684V4C12.4684 3.77765 12.399 3.59491 12.3101 3.47843C12.2216 3.3624 12.1349 3.33333 12.0778 3.33333H7.10311C6.68402 3.33333 6.31985 3.11455 6.07592 2.79502L4.81642 1.1451C4.7263 1.02705 4.63742 1 4.58411 1H1.3906C1.33355 1 1.24686 1.02906 1.15829 1.1451ZM13.4684 4.66667V4C13.4684 3.57802 13.3384 3.17743 13.105 2.87165C12.8712 2.56544 12.5126 2.33333 12.0778 2.33333H7.10311C7.04979 2.33333 6.96091 2.30629 6.87079 2.18823L5.61129 0.538318C5.36737 0.218787 5.0032 0 4.58411 0H1.3906C0.955791 0 0.597172 0.232104 0.363417 0.538318C0.129995 0.844095 0 1.24469 0 1.66667V13.3333C0 13.7553 0.129995 14.1559 0.363417 14.4617C0.597172 14.7679 0.955791 15 1.3906 15H12.5231C12.835 15 13.0823 14.8332 13.2354 14.6325C13.3125 14.5316 13.3711 14.4168 13.4102 14.2943C13.5154 14.1141 13.5894 13.9135 13.6282 13.71L14.9641 6.71002C15.0566 6.22523 14.9661 5.73791 14.7404 5.35936C14.5162 4.98307 14.1219 4.66667 13.609 4.66667H13.4684ZM12.2731 14C12.3564 14 12.5736 13.9014 12.6459 13.5226L13.9818 6.52256C14.0298 6.27102 13.9781 6.03352 13.8814 5.87134C13.7834 5.7069 13.6754 5.66667 13.609 5.66667H2.97656C2.89323 5.66667 2.676 5.76523 2.60369 6.1441L1.26779 13.1441C1.21978 13.3957 1.27152 13.6331 1.36818 13.7953C1.46619 13.9598 1.57422 14 1.64066 14H12.2731Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                    <syncribbon:RibbonButton x:Name="Part_Save"
                                                             SizeForm="ExtraSmall"
                                                             IconType="Icon"
                                                             ToolTip="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SaveToolTip}"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Stretch="None"
                                                          Height="16"
                                                          Width="15"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M1.5 1C1.22386 1 1 1.22386 1 1.5V13.5C1 13.7761 1.22386 14 1.5 14H2L2 9.5C2 8.67157 2.67157 8 3.5 8L11.5 8C12.3284 8 13 8.67157 13 9.5V14H13.5C13.7761 14 14 13.7761 14 13.5V3.45993C14 3.31397 13.9362 3.1753 13.8254 3.08031L11.5388 1.12037C11.4482 1.0427 11.3328 1 11.2134 1H11V3.5C11 4.32843 10.3284 5 9.5 5H5.5C4.67157 5 4 4.32843 4 3.5V1H1.5ZM5 1V3.5C5 3.77614 5.22386 4 5.5 4H9.5C9.77614 4 10 3.77614 10 3.5V1H5ZM12 14V9.5C12 9.22386 11.7761 9 11.5 9L3.5 9C3.22386 9 3 9.22386 3 9.5L3 14H12ZM0 1.5C0 0.671574 0.671573 0 1.5 0H11.2134C11.5715 0 11.9177 0.128088 12.1896 0.361115L14.4762 2.32105C14.8087 2.60602 15 3.02205 15 3.45993V13.5C15 14.3284 14.3284 15 13.5 15H1.5C0.671574 15 0 14.3284 0 13.5V1.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                    <syncribbon:RibbonButton Command="diag:DiagramCommands.Undo"
                                                             SizeForm="ExtraSmall" 
                                                             IconType="Icon"
                                                             ToolTip="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=UndoToolTip}"
                                                             IsEnabled="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Constraints, Converter={StaticResource graphconstraintstoboolconverter},ConverterParameter={StaticResource UndoableParam}, Mode=OneWay}"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path HorizontalAlignment="Center"
                                                          VerticalAlignment="Center" 
                                                          Stretch="Fill"                                                          
                                                          Height="16"
                                                          Width="13"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M13 5C13 5.75 12.8411 6.45833 12.5234 7.125C12.2109 7.78646 11.7682 8.35938 11.1953 8.84375L2.82031 15.8828C2.72656 15.9609 2.61979 16 2.5 16C2.36458 16 2.2474 15.9505 2.14844 15.8516C2.04948 15.7526 2 15.6354 2 15.5C2 15.4271 2.01562 15.3568 2.04688 15.2891C2.07812 15.2214 2.1224 15.1641 2.17969 15.1172L10.5547 8.07812C11.0391 7.67188 11.401 7.20052 11.6406 6.66406C11.8802 6.1276 12 5.54688 12 4.92188C12 4.57292 11.9505 4.23438 11.8516 3.90625C11.7578 3.57812 11.6224 3.26823 11.4453 2.97656C11.2734 2.6849 11.0651 2.41927 10.8203 2.17969C10.5807 1.9349 10.3151 1.72656 10.0234 1.55469C9.73177 1.3776 9.42188 1.24219 9.09375 1.14844C8.76562 1.04948 8.42708 1 8.07812 1C7.58854 1 7.11979 1.07552 6.67188 1.22656C6.22917 1.3776 5.82031 1.60938 5.44531 1.92188L1.75 5H5.5C5.63542 5 5.7526 5.04948 5.85156 5.14844C5.95052 5.2474 6 5.36458 6 5.5C6 5.63542 5.95052 5.7526 5.85156 5.85156C5.7526 5.95052 5.63542 6 5.5 6H0.5C0.364583 6 0.247396 5.95052 0.148438 5.85156C0.0494792 5.7526 0 5.63542 0 5.5V0.5C0 0.364583 0.0494792 0.247396 0.148438 0.148438C0.247396 0.0494792 0.364583 0 0.5 0C0.635417 0 0.752604 0.0494792 0.851562 0.148438C0.950521 0.247396 1 0.364583 1 0.5V4.32031L4.80469 1.15625C5.2526 0.78125 5.75 0.494792 6.29688 0.296875C6.84896 0.0989583 7.41667 0 8 0C8.69271 0 9.34115 0.130208 9.94531 0.390625C10.5547 0.651042 11.0859 1.00781 11.5391 1.46094C11.9922 1.91406 12.349 2.44531 12.6094 3.05469C12.8698 3.65885 13 4.30729 13 5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                    <syncribbon:RibbonButton Command="diag:DiagramCommands.Redo" 
                                                             SizeForm="ExtraSmall" 
                                                             IconType="Icon"
                                                             ToolTip="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RedoToolTip}"
                                                             IsEnabled="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Constraints, Converter={StaticResource graphconstraintstoboolconverter},ConverterParameter={StaticResource UndoableParam}, Mode=OneWay}"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Fill="{StaticResource IconColor}" 
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center" 
                                                          Height="16"
                                                          Width="13"
                                                          Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M13 0.5V5.5C13 5.63542 12.9505 5.7526 12.8516 5.85156C12.7526 5.95052 12.6354 6 12.5 6H7.5C7.36458 6 7.2474 5.95052 7.14844 5.85156C7.04948 5.7526 7 5.63542 7 5.5C7 5.36458 7.04948 5.2474 7.14844 5.14844C7.2474 5.04948 7.36458 5 7.5 5H11.25L7.5625 1.92188C7.1875 1.60938 6.77344 1.3776 6.32031 1.22656C5.8724 1.07552 5.40625 1 4.92188 1C4.57292 1 4.23438 1.04948 3.90625 1.14844C3.57812 1.24219 3.26823 1.3776 2.97656 1.55469C2.6849 1.72656 2.41667 1.9349 2.17188 2.17969C1.93229 2.41927 1.72396 2.6849 1.54688 2.97656C1.375 3.26823 1.23958 3.57812 1.14062 3.90625C1.04688 4.23438 1 4.57292 1 4.92188C1 5.54688 1.11979 6.1276 1.35938 6.66406C1.59896 7.20052 1.96094 7.67188 2.44531 8.07812L10.8203 15.1172C10.9401 15.2214 11 15.349 11 15.5C11 15.6354 10.9505 15.7526 10.8516 15.8516C10.7526 15.9505 10.6354 16 10.5 16C10.3802 16 10.2734 15.9609 10.1797 15.8828L1.80469 8.84375C1.22135 8.35417 0.773438 7.78125 0.460938 7.125C0.153646 6.46875 0 5.76042 0 5C0 4.30729 0.130208 3.65885 0.390625 3.05469C0.651042 2.44531 1.00781 1.91406 1.46094 1.46094C1.91406 1.00781 2.44271 0.651042 3.04688 0.390625C3.65625 0.130208 4.30729 0 5 0C5.58333 0 6.14844 0.0989583 6.69531 0.296875C7.2474 0.494792 7.7474 0.78125 8.19531 1.15625L12 4.32031V0.5C12 0.364583 12.0495 0.247396 12.1484 0.148438C12.2474 0.0494792 12.3646 0 12.5 0C12.6354 0 12.7526 0.0494792 12.8516 0.148438C12.9505 0.247396 13 0.364583 13 0.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                </syncribbon:QuickAccessToolBar>
                            </syncribbon:Ribbon.QuickAccessToolBar>

                            <syncribbon:RibbonTab x:Name="Part_HomeRibbonTab" Caption="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=HomeCaption}">

                                <syncribbon:RibbonBar x:Name="Part_ClipboardBar" 
                                                      Header = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ClipboardHeader}"
                                                      IsLauncherButtonVisible ="False"
                                                      IsPanelStateCollapsed="False"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <syncribbon:RibbonButton x:Name="Part_Paste"
                                                             Margin = "0,4,2,4"
                                                             Height="Auto"
                                                             Width="Auto"
                                                             Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Paste}" 
                                                             SizeForm="Large"
                                                             IconType ="Icon"
                                                             HorizontalAlignment="Center"
                                                             VerticalAlignment="Center"
                                                             Command = "diag:DiagramCommands.Paste"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}"
                                                             Padding="{StaticResource Windows11Light.BorderThickness2}">
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PasteScreenTip}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PasteText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path x:Name="paste32"                                                           
                                                          Height="32"
                                                          Width="29"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Stretch="Fill" 
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M15.5 1H9.5C8.39543 1 7.5 1.89543 7.5 3C7.5 4.10457 8.39543 5 9.5 5H15.5C16.6046 5 17.5 4.10457 17.5 3C17.5 1.89543 16.6046 1 15.5 1ZM9.5 0C8.01292 0 6.77855 1.08199 6.5412 2.50167C6.52761 2.50056 6.51387 2.5 6.5 2.5H2.5C1.11929 2.5 0 3.61929 0 5V29C0 30.3807 1.11929 31.5 2.5 31.5H10.5C10.7761 31.5 11 31.2761 11 31C11 30.7239 10.7761 30.5 10.5 30.5H2.5C1.67157 30.5 1 29.8284 1 29V5C1 4.17157 1.67157 3.5 2.5 3.5H6.5C6.51387 3.5 6.52761 3.49944 6.5412 3.49833C6.77855 4.91801 8.01292 6 9.5 6H15.5C16.9871 6 18.2215 4.918 18.4588 3.49832C18.4724 3.49943 18.4861 3.5 18.5 3.5H22.5C23.3284 3.5 24 4.17157 24 5V8.7931C24 9.06925 24.2239 9.2931 24.5 9.2931C24.7761 9.2931 25 9.06925 25 8.7931V5C25 3.61929 23.8807 2.5 22.5 2.5H18.5C18.4861 2.5 18.4724 2.50057 18.4588 2.50168C18.2215 1.082 16.9871 0 15.5 0H9.5ZM14.5 11H26.5C27.0523 11 27.5 11.4477 27.5 12V30C27.5 30.5523 27.0523 31 26.5 31H14.5C13.9477 31 13.5 30.5523 13.5 30V12C13.5 11.4477 13.9477 11 14.5 11ZM12.5 12C12.5 10.8954 13.3954 10 14.5 10H26.5C27.6046 10 28.5 10.8954 28.5 12V30C28.5 31.1046 27.6046 32 26.5 32H14.5C13.3954 32 12.5 31.1046 12.5 30V12Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                    <syncribbon:RibbonButton x:Name="Part_Cut"
                                                             Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Cut}" 
                                                             Margin="2,4,0,4"
                                                             Height="24"
                                                             SizeForm ="Small"
                                                             IconType="Icon"
                                                             Command = "diag:DiagramCommands.Cut"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=CutScreenTip}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=CutText}" MaxWidth="200" TextAlignment="Justify" TextWrapping="Wrap"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Width="14"
                                                          Height="16" 
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center" 
                                                          Stretch="Fill"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M0 12.9375C0 12.5469 0.078125 12.1745 0.234375 11.8203C0.395833 11.4609 0.609375 11.1484 0.875 10.8828C1.14583 10.612 1.45833 10.3984 1.8125 10.2422C2.17188 10.0807 2.54688 10 2.9375 10C3.21875 10 3.48438 10.0312 3.73438 10.0938C3.98958 10.1562 4.23958 10.2552 4.48438 10.3906L6.40625 7.42969L2.07812 0.773438C2.02604 0.700521 2 0.611979 2 0.507812C2 0.372396 2.04688 0.255208 2.14062 0.15625C2.23438 0.0520833 2.35156 0 2.49219 0C2.57552 0 2.65625 0.0208333 2.73438 0.0625C2.8125 0.0989583 2.875 0.153646 2.92188 0.226562L9.51562 10.3906C9.76562 10.2552 10.0156 10.1562 10.2656 10.0938C10.5156 10.0312 10.7812 10 11.0625 10C11.4688 10 11.849 10.0807 12.2031 10.2422C12.5625 10.4036 12.875 10.6224 13.1406 10.8984C13.4062 11.1693 13.6146 11.487 13.7656 11.8516C13.9219 12.2161 14 12.599 14 13C14 13.4167 13.9219 13.8073 13.7656 14.1719C13.6094 14.5365 13.3958 14.8542 13.125 15.125C12.8542 15.3958 12.5365 15.6094 12.1719 15.7656C11.8073 15.9219 11.4167 16 11 16C10.599 16 10.2161 15.9245 9.85156 15.7734C9.48698 15.6172 9.16667 15.4062 8.89062 15.1406C8.61979 14.875 8.40365 14.5651 8.24219 14.2109C8.08073 13.8516 8 13.4688 8 13.0625C8 12.6823 8.0599 12.3203 8.17969 11.9766C8.29948 11.6328 8.48698 11.3151 8.74219 11.0234L7 8.34375L5.25781 11.0234C5.51302 11.3151 5.70052 11.6328 5.82031 11.9766C5.9401 12.3203 6 12.6823 6 13.0625C6 13.4688 5.91927 13.8516 5.75781 14.2109C5.59635 14.5651 5.3776 14.875 5.10156 15.1406C4.83073 15.4062 4.51302 15.6172 4.14844 15.7734C3.78385 15.9245 3.40104 16 3 16C2.56771 16 2.16927 15.9219 1.80469 15.7656C1.4401 15.6042 1.1224 15.3854 0.851562 15.1094C0.585938 14.8281 0.377604 14.5026 0.226562 14.1328C0.0755208 13.7578 0 13.3594 0 12.9375ZM8.19531 6.50781L7.59375 5.59375L11.0781 0.226562C11.125 0.153646 11.1875 0.0989583 11.2656 0.0625C11.3438 0.0208333 11.4245 0 11.5078 0C11.6484 0 11.7656 0.0520833 11.8594 0.15625C11.9531 0.255208 12 0.372396 12 0.507812C12 0.611979 11.974 0.700521 11.9219 0.773438L8.19531 6.50781ZM5 12.9609C5 12.6901 4.94531 12.4375 4.83594 12.2031C4.72656 11.9635 4.58073 11.7552 4.39844 11.5781C4.21615 11.401 4.0026 11.2604 3.75781 11.1562C3.51823 11.0521 3.26562 11 3 11C2.72396 11 2.46354 11.0547 2.21875 11.1641C1.97917 11.2682 1.76823 11.4115 1.58594 11.5938C1.40885 11.7708 1.26562 11.9818 1.15625 12.2266C1.05208 12.4714 1 12.7292 1 13C1 13.276 1.05208 13.5365 1.15625 13.7812C1.26562 14.0208 1.40885 14.2318 1.58594 14.4141C1.76823 14.5911 1.97917 14.7344 2.21875 14.8438C2.46354 14.9479 2.72396 15 3 15C3.28125 15 3.54167 14.9479 3.78125 14.8438C4.02604 14.7344 4.23698 14.5885 4.41406 14.4062C4.59635 14.2188 4.73958 14.0026 4.84375 13.7578C4.94792 13.5078 5 13.2422 5 12.9609ZM13 12.9609C13 12.6901 12.9453 12.4375 12.8359 12.2031C12.7266 11.9635 12.5807 11.7552 12.3984 11.5781C12.2161 11.401 12.0026 11.2604 11.7578 11.1562C11.5182 11.0521 11.2656 11 11 11C10.724 11 10.4635 11.0547 10.2188 11.1641C9.97917 11.2682 9.76823 11.4115 9.58594 11.5938C9.40885 11.7708 9.26562 11.9818 9.15625 12.2266C9.05208 12.4714 9 12.7292 9 13C9 13.276 9.05208 13.5365 9.15625 13.7812C9.26562 14.0208 9.40885 14.2318 9.58594 14.4141C9.76823 14.5911 9.97917 14.7344 10.2188 14.8438C10.4635 14.9479 10.724 15 11 15C11.2812 15 11.5417 14.9479 11.7812 14.8438C12.026 14.7344 12.237 14.5885 12.4141 14.4062C12.5964 14.2188 12.7396 14.0026 12.8438 13.7578C12.9479 13.5078 13 13.2422 13 12.9609Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                    <syncribbon:RibbonButton x:Name="Part_Copy"
                                                             Margin = "2,4,0,4"
                                                             Height="24"
                                                             Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Copy}"
                                                             SizeForm ="Small"
                                                             IconType ="Icon"
                                                             Command = "diag:DiagramCommands.Copy"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=CopyScreenTip}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=CopyText}" MaxWidth="200" TextAlignment="Justify" TextWrapping="Wrap"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="16" 
                                                          Width="{StaticResource Windows11Light.SubTitleTextStyle}" 
                                                          HorizontalAlignment="Left"
                                                          VerticalAlignment="Top"
                                                          Stretch="Fill"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M3.96094 14C3.70052 14 3.45052 13.9479 3.21094 13.8438C2.97656 13.7344 2.76823 13.5911 2.58594 13.4141C2.40885 13.2318 2.26562 13.0234 2.15625 12.7891C2.05208 12.5495 2 12.2995 2 12.0391V1.96094C2 1.70052 2.05208 1.45312 2.15625 1.21875C2.26562 0.979167 2.40885 0.770833 2.58594 0.59375C2.76823 0.411458 2.97656 0.268229 3.21094 0.164062C3.45052 0.0546875 3.70052 0 3.96094 0H10.0391C10.2995 0 10.5469 0.0546875 10.7812 0.164062C11.0208 0.268229 11.2292 0.411458 11.4062 0.59375C11.5885 0.770833 11.7318 0.979167 11.8359 1.21875C11.9453 1.45312 12 1.70052 12 1.96094V12.0391C12 12.2995 11.9453 12.5495 11.8359 12.7891C11.7318 13.0234 11.5885 13.2318 11.4062 13.4141C11.2292 13.5911 11.0208 13.7344 10.7812 13.8438C10.5469 13.9479 10.2995 14 10.0391 14H3.96094ZM10 13C10.1406 13 10.2708 12.974 10.3906 12.9219C10.5104 12.8698 10.6146 12.7995 10.7031 12.7109C10.7969 12.6172 10.8698 12.5104 10.9219 12.3906C10.974 12.2708 11 12.1406 11 12V2C11 1.86458 10.974 1.73698 10.9219 1.61719C10.8698 1.49219 10.7969 1.38542 10.7031 1.29688C10.6146 1.20312 10.5078 1.13021 10.3828 1.07812C10.263 1.02604 10.1354 1 10 1H4C3.85938 1 3.72917 1.02604 3.60938 1.07812C3.48958 1.13021 3.38281 1.20312 3.28906 1.29688C3.20052 1.38542 3.13021 1.48958 3.07812 1.60938C3.02604 1.72917 3 1.85938 3 2V12C3 12.1406 3.02604 12.2734 3.07812 12.3984C3.13021 12.5182 3.20052 12.6224 3.28906 12.7109C3.3776 12.7995 3.48177 12.8698 3.60156 12.9219C3.72656 12.974 3.85938 13 4 13H10ZM3.5 16C3.01562 16 2.5599 15.9089 2.13281 15.7266C1.71094 15.5443 1.34115 15.2943 1.02344 14.9766C0.705729 14.6589 0.455729 14.2891 0.273438 13.8672C0.0911458 13.4401 0 12.9844 0 12.5V4C0 3.64062 0.0885417 3.3099 0.265625 3.00781C0.447917 2.70573 0.692708 2.46354 1 2.28125V12.5C1 12.8438 1.0651 13.1693 1.19531 13.4766C1.32552 13.7786 1.5026 14.0443 1.72656 14.2734C1.95573 14.4974 2.22135 14.6745 2.52344 14.8047C2.83073 14.9349 3.15625 15 3.5 15H9.71875C9.53646 15.3073 9.29427 15.5521 8.99219 15.7344C8.6901 15.9115 8.35938 16 8 16H3.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                </syncribbon:RibbonBar>

                                <syncribbon:RibbonBar x:Name="Part_Fontbar"
                                                      Header = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FontHeader}"
                                                      IsLauncherButtonVisible ="False"
                                                      IsPanelStateCollapsed="False"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <StackPanel Orientation="Horizontal">
                                        <syncribbon:RibbonComboBox x:Name="Part_FontFamily"
                                                                   Width="148"
                                                                   IsEditable="True"
                                                                   Margin="0,4,2,4"
                                                                   ItemsSource="{Binding Source={x:Static fonts:Fonts.SystemFontFamilies}}">
                                            <syncribbon:RibbonComboBox.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FontHeader}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FontToolTipText}" MaxWidth="200" TextAlignment="Justify" TextWrapping="Wrap"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonComboBox.ToolTip>
                                        </syncribbon:RibbonComboBox>
                                        <syncribbon:RibbonComboBox x:Name="Part_FontSize"
                                                                   IsEditable="True"
                                                                   Width="44"
                                                                   Margin = "2,4,2,4">
                                            <syncribbon:RibbonComboBox.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FontSizeScreenTip}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FontSizeToolTipText}" MaxWidth="200" TextAlignment="Justify" TextWrapping="Wrap"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonComboBox.ToolTip>
                                            <syncribbon:RibbonComboBox.ItemsSource>
                                                <collection:ArrayList>
                                                    <sys:Double>6</sys:Double>
                                                    <sys:Double>8</sys:Double>
                                                    <sys:Double>10</sys:Double>
                                                    <sys:Double>12</sys:Double>
                                                    <sys:Double>14</sys:Double>
                                                    <sys:Double>16</sys:Double>
                                                    <sys:Double>18</sys:Double>
                                                    <sys:Double>24</sys:Double>
                                                    <sys:Double>30</sys:Double>
                                                    <sys:Double>36</sys:Double>
                                                    <sys:Double>48</sys:Double>
                                                    <sys:Double>60</sys:Double>
                                                </collection:ArrayList>
                                            </syncribbon:RibbonComboBox.ItemsSource>
                                        </syncribbon:RibbonComboBox>
                                        <syncribbon:RibbonButton x:Name="Part_GrowFont"
                                                                 Margin ="2,4,2,4"
                                                                 IconType ="Icon"
                                                                 SizeForm ="ExtraSmall"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=GrowFontScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=GrowFontTooltipText}" MaxWidth="200" TextAlignment="Justify" TextWrapping="Wrap"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Width="11"
                                                              Height="12"
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Stretch="None"
                                                              Fill="{StaticResource IconColor}" >
                                                            <Path.Data>
                                                                <PathGeometry>M6.5 2C6.36458 2 6.2474 1.95052 6.14844 1.85156C6.04948 1.7526 6 1.63542 6 1.5C6 1.41146 6.02344 1.32552 6.07031 1.24219C6.1224 1.15885 6.1901 1.09635 6.27344 1.05469L8.27344 0.0546875C8.34115 0.0182292 8.41667 0 8.5 0C8.58333 0 8.65885 0.0182292 8.72656 0.0546875L10.7266 1.05469C10.8099 1.09635 10.875 1.15885 10.9219 1.24219C10.974 1.32552 11 1.41146 11 1.5C11 1.63542 10.9505 1.7526 10.8516 1.85156C10.7526 1.95052 10.6354 2 10.5 2C10.4167 2 10.3411 1.98177 10.2734 1.94531L8.5 1.0625L6.72656 1.94531C6.65885 1.98177 6.58333 2 6.5 2ZM0 11.4922C0 11.4245 0.0104167 11.3672 0.03125 11.3203L3.53125 2.32031C3.57292 2.22135 3.63542 2.14323 3.71875 2.08594C3.80208 2.02865 3.89583 2 4 2C4.10417 2 4.19792 2.02865 4.28125 2.08594C4.36458 2.14323 4.42708 2.22135 4.46875 2.32031L7.96875 11.3203C7.98958 11.3672 8 11.4245 8 11.4922C8 11.6276 7.95312 11.7474 7.85938 11.8516C7.76562 11.9505 7.64844 12 7.50781 12C7.40365 12 7.30729 11.9714 7.21875 11.9141C7.13542 11.8568 7.07292 11.7786 7.03125 11.6797L5.99219 9H2.00781L0.96875 11.6797C0.927083 11.7786 0.861979 11.8568 0.773438 11.9141C0.690104 11.9714 0.596354 12 0.492188 12C0.351562 12 0.234375 11.9505 0.140625 11.8516C0.046875 11.7474 0 11.6276 0 11.4922ZM4 3.88281L2.39844 8H5.60156L4 3.88281Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon:RibbonButton x:Name="Part_ShrinkFont"
                                                                 Margin ="2,4,0,4"
                                                                 Height="24"
                                                                 IconType ="Icon"
                                                                 SizeForm ="ExtraSmall"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ShrinkFontScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ShrinkFontTooltipText}" MaxWidth="200" TextAlignment="Justify" TextWrapping="Wrap"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Width="11"
                                                              Height="12"
                                                              Stretch="None"
                                                              VerticalAlignment="Center"
                                                              HorizontalAlignment="Center"
                                                              Fill="{StaticResource IconColor}" >
                                                            <Path.Data>
                                                                <PathGeometry>M6 0.5C6 0.364583 6.04948 0.247396 6.14844 0.148438C6.2474 0.0494792 6.36458 0 6.5 0C6.58333 0 6.65885 0.0182292 6.72656 0.0546875L8.5 0.9375L10.2734 0.0546875C10.3411 0.0182292 10.4167 0 10.5 0C10.6354 0 10.7526 0.0494792 10.8516 0.148438C10.9505 0.247396 11 0.364583 11 0.5C11 0.588542 10.974 0.674479 10.9219 0.757812C10.875 0.841146 10.8099 0.903646 10.7266 0.945312L8.72656 1.94531C8.64323 1.98177 8.56771 2 8.5 2C8.43229 2 8.35677 1.98177 8.27344 1.94531L6.27344 0.945312C6.1901 0.903646 6.1224 0.841146 6.07031 0.757812C6.02344 0.674479 6 0.588542 6 0.5ZM0 11.4922C0 11.4245 0.0104167 11.3672 0.03125 11.3203L3.53125 2.32031C3.57292 2.22135 3.63542 2.14323 3.71875 2.08594C3.80208 2.02865 3.89583 2 4 2C4.10417 2 4.19792 2.02865 4.28125 2.08594C4.36458 2.14323 4.42708 2.22135 4.46875 2.32031L7.96875 11.3203C7.98958 11.3672 8 11.4245 8 11.4922C8 11.6276 7.95312 11.7474 7.85938 11.8516C7.76562 11.9505 7.64844 12 7.50781 12C7.40365 12 7.30729 11.9714 7.21875 11.9141C7.13542 11.8568 7.07292 11.7786 7.03125 11.6797L5.99219 9H2.00781L0.96875 11.6797C0.927083 11.7786 0.861979 11.8568 0.773438 11.9141C0.690104 11.9714 0.596354 12 0.492188 12C0.351562 12 0.234375 11.9505 0.140625 11.8516C0.046875 11.7474 0 11.6276 0 11.4922ZM4 3.88281L2.39844 8H5.60156L4 3.88281Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <syncribbon:RibbonButton x:Name="Part_Bold"
                                                                 Margin = "0,4,2,4"
                                                                 Height="24"
                                                                 Width="24"
                                                                 IsToggle="True"
                                                                 IconType="Icon"
                                                                 SizeForm="ExtraSmall"
                                                                 Command="diag:DiagramCommands.ToggleBold"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BoldScreenTip}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BoldToolTip}" MaxWidth="200" TextAlignment="Justify" TextWrapping="Wrap"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="14"
                                                              Width="10"
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}" 
                                                              Data="M1.27746 14C1.10388 14 0.938432 13.9685 0.781123 13.9056C0.629238 13.8427 0.493626 13.7562 0.374288 13.6461C0.260374 13.536 0.168158 13.4075 0.0976404 13.2607C0.0325468 13.1139 0 12.9566 0 12.7888V1.30562C0 1.13258 0.035259 0.967416 0.105777 0.810112C0.18172 0.652809 0.282072 0.513858 0.406835 0.393258C0.531597 0.272659 0.675346 0.178277 0.83808 0.110112C1.00081 0.0367041 1.17168 0 1.35069 0H4.99593C5.57092 0 6.11066 0.107491 6.61513 0.322472C7.11961 0.53221 7.55899 0.820599 7.93328 1.18764C8.31299 1.54944 8.61134 1.97416 8.82832 2.4618C9.05072 2.94944 9.16192 3.47116 9.16192 4.02697C9.16192 4.47266 9.08869 4.89738 8.94223 5.30112C8.80119 5.69963 8.59778 6.07977 8.33198 6.44157C8.87442 6.85056 9.28668 7.34082 9.56875 7.91236C9.85625 8.48389 10 9.10262 10 9.76854C10 10.1828 9.94847 10.5891 9.8454 10.9876C9.74234 11.3809 9.56875 11.7506 9.32465 12.0966C9.11852 12.3903 8.87714 12.6551 8.60049 12.891C8.32926 13.1217 8.03363 13.321 7.71359 13.4888C7.39354 13.6513 7.05452 13.7772 6.6965 13.8663C6.34391 13.9502 5.9859 13.9921 5.62246 13.9921C4.89558 13.9921 4.17141 13.9948 3.44996 14C2.72851 14 2.00434 14 1.27746 14ZM4.89829 5.53708C5.11527 5.53708 5.31869 5.49775 5.50854 5.4191C5.6984 5.34045 5.86385 5.23296 6.00488 5.09663C6.14592 4.9603 6.25712 4.80037 6.33849 4.61685C6.41985 4.43333 6.46054 4.2367 6.46054 4.02697C6.46054 3.81723 6.41985 3.6206 6.33849 3.43708C6.25712 3.25356 6.14592 3.09363 6.00488 2.9573C5.86385 2.82097 5.6984 2.71348 5.50854 2.63483C5.31869 2.55618 5.11527 2.51685 4.89829 2.51685H2.60374V5.53708H4.89829ZM5.51668 11.5775C5.77163 11.5775 6.00759 11.533 6.22457 11.4438C6.44155 11.3494 6.6287 11.2236 6.786 11.0663C6.94331 10.909 7.06536 10.7255 7.15216 10.5157C7.24437 10.3007 7.29048 10.07 7.29048 9.8236C7.29048 9.59288 7.24166 9.37266 7.14402 9.16292C7.0518 8.94794 6.92433 8.75918 6.76159 8.59663C6.60429 8.42884 6.41714 8.29775 6.20016 8.20337C5.98861 8.10374 5.76078 8.05393 5.51668 8.05393H2.60374V11.5775H5.51668Z"/>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon:RibbonButton x:Name="Part_Italic"
                                                                 Margin = "2,4,2,4"
                                                                 Width="24"
                                                                 Height="24"
                                                                 IsToggle="True"
                                                                 IconType="Icon"
                                                                 SizeForm="ExtraSmall"
                                                                 Command="diag:DiagramCommands.ToggleItalic"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ItalicScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ItalicTooltipText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="14" 
                                                              Width="13"
                                                              Stretch="Fill" 
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center" 
                                                              Fill="{StaticResource IconColor}"
                                                              Data="M12.5 0C12.6354 0 12.7526 0.0494792 12.8516 0.148438C12.9505 0.247396 13 0.364583 13 0.5C13 0.635417 12.9505 0.752604 12.8516 0.851562C12.7526 0.950521 12.6354 1 12.5 1H9.34375L4.72656 13H8C8.13542 13 8.2526 13.0495 8.35156 13.1484C8.45052 13.2474 8.5 13.3646 8.5 13.5C8.5 13.6354 8.45052 13.7526 8.35156 13.8516C8.2526 13.9505 8.13542 14 8 14H0.5C0.364583 14 0.247396 13.9505 0.148438 13.8516C0.0494792 13.7526 0 13.6354 0 13.5C0 13.3646 0.0494792 13.2474 0.148438 13.1484C0.247396 13.0495 0.364583 13 0.5 13H3.65625L8.27344 1H5C4.86458 1 4.7474 0.950521 4.64844 0.851562C4.54948 0.752604 4.5 0.635417 4.5 0.5C4.5 0.364583 4.54948 0.247396 4.64844 0.148438C4.7474 0.0494792 4.86458 0 5 0H12.5Z"/>
                                                    </Grid>                                                                    
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon:RibbonButton x:Name="Part_Underline"
                                                                 Margin = "2,4,2,4"
                                                                 Height="24"
                                                                 Width="24"
                                                                 IsToggle="True"
                                                                 IconType="Icon"
                                                                 SizeForm="ExtraSmall"
                                                                 Command="diag:DiagramCommands.ToggleUnderline"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=UnderlineScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=UnderlineTooltipText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="14"
                                                              Width="10" 
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}" 
                                                              Data="M0 6.95312V0.5C0 0.364583 0.0494792 0.247396 0.148438 0.148438C0.247396 0.0494792 0.364583 0 0.5 0C0.635417 0 0.752604 0.0494792 0.851562 0.148438C0.950521 0.247396 1 0.364583 1 0.5V7.03125C1 7.57292 1.10677 8.08594 1.32031 8.57031C1.53385 9.04948 1.82292 9.46875 2.1875 9.82812C2.55208 10.1875 2.97656 10.474 3.46094 10.6875C3.94531 10.8958 4.45833 11 5 11C5.55729 11 6.07812 10.8932 6.5625 10.6797C7.05208 10.4661 7.47656 10.1771 7.83594 9.8125C8.19531 9.44271 8.47917 9.01302 8.6875 8.52344C8.89583 8.03385 9 7.51042 9 6.95312V0.5C9 0.364583 9.04948 0.247396 9.14844 0.148438C9.2474 0.0494792 9.36458 0 9.5 0C9.63542 0 9.7526 0.0494792 9.85156 0.148438C9.95052 0.247396 10 0.364583 10 0.5V6.95312C10 7.41667 9.9401 7.86198 9.82031 8.28906C9.70573 8.71615 9.53906 9.11719 9.32031 9.49219C9.10677 9.86719 8.84635 10.2083 8.53906 10.5156C8.23698 10.8229 7.90104 11.0859 7.53125 11.3047C7.16146 11.5234 6.76302 11.6953 6.33594 11.8203C5.90885 11.9401 5.46354 12 5 12C4.53646 12 4.09115 11.9401 3.66406 11.8203C3.23698 11.6953 2.83854 11.5234 2.46875 11.3047C2.09896 11.0859 1.76042 10.8229 1.45312 10.5156C1.15104 10.2083 0.890625 9.86719 0.671875 9.49219C0.458333 9.11719 0.291667 8.71615 0.171875 8.28906C0.0572917 7.85677 0 7.41146 0 6.95312ZM0.5 14C0.364583 14 0.247396 13.9505 0.148438 13.8516C0.0494792 13.7526 0 13.6354 0 13.5C0 13.3646 0.0494792 13.2474 0.148438 13.1484C0.247396 13.0495 0.364583 13 0.5 13H9.5C9.63542 13 9.7526 13.0495 9.85156 13.1484C9.95052 13.2474 10 13.3646 10 13.5C10 13.6354 9.95052 13.7526 9.85156 13.8516C9.7526 13.9505 9.63542 14 9.5 14H0.5Z"/>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon:RibbonButton x:Name="Part_StrikeThrough"
                                                                 Margin = "2,4,2,4"
                                                                 Height="24"
                                                                 Width="24"
                                                                 IsToggle="True"
                                                                 IconType="Icon"
                                                                 SizeForm="ExtraSmall"
                                                                 Command="diag:DiagramCommands.ToggleStrikeThrough"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=StrikeScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=StrikeTooltipText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="8" 
                                                              Width="16"                                                              
                                                              Stretch="Fill" 
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center" 
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M6.40526 2.58701C6.4193 2.37922 6.42632 2.15758 6.42632 1.92208V0H5.5V4.5H4.49767C4.47807 3.9976 4.33445 3.62672 4.06682 3.38739C3.77803 3.12913 3.33257 3 2.73041 3C2.41091 3 2.10369 3.04204 1.80876 3.12613C1.51382 3.2042 1.25883 3.3003 1.04378 3.41441L1.29263 4.00901C1.49539 3.91291 1.71352 3.82883 1.947 3.75676C2.18049 3.67868 2.42627 3.63964 2.68433 3.63964C3.00998 3.63964 3.2619 3.71772 3.44009 3.87387C3.58514 4.00099 3.67116 4.2097 3.69813 4.5H0.5C0.223858 4.5 0 4.72386 0 5C0 5.27614 0.223858 5.5 0.5 5.5H1.02271C0.674237 5.75583 0.5 6.11169 0.5 6.56757C0.5 6.89189 0.567588 7.16216 0.702765 7.37838C0.837942 7.58859 1.02227 7.74474 1.25576 7.84685C1.49539 7.94895 1.76267 8 2.0576 8C2.3341 8 2.56759 7.97297 2.75806 7.91892C2.95469 7.86486 3.12673 7.78078 3.27419 7.66667C3.4278 7.54655 3.57527 7.3994 3.71659 7.22522H3.75346L3.91014 7.90991H4.5V5.5H5H5.5V7.8961H6.16316L6.35263 7.18961H6.42632C6.58772 7.41125 6.80877 7.60173 7.08947 7.76104C7.37018 7.92035 7.73158 8 8.17368 8C8.63684 8 9.04386 7.89264 9.39474 7.67792C9.74561 7.4632 10.0158 7.14112 10.2053 6.71169C10.3604 6.37258 10.4543 5.96868 10.487 5.5H11.5001L11.5 5.52698C11.5 6.09652 11.6054 6.56415 11.8163 6.92986C12.034 7.29556 12.3299 7.56535 12.7041 7.73921C13.085 7.91307 13.517 8 14 8C14.3129 8 14.5782 7.97602 14.7959 7.92806C15.0204 7.88609 15.2245 7.82314 15.4082 7.73921V7.03777C15.2177 7.11571 15.0068 7.18165 14.7755 7.23561C14.551 7.28957 14.2891 7.31655 13.9898 7.31655C13.6429 7.31655 13.3537 7.2476 13.1224 7.10971C12.8912 6.97182 12.7177 6.76799 12.602 6.4982C12.4864 6.22842 12.4286 5.90168 12.4286 5.51799L12.4286 5.5H15.5C15.7761 5.5 16 5.27614 16 5C16 4.72386 15.7761 4.5 15.5 4.5H12.6067C12.7224 4.23557 12.8977 4.03533 13.1327 3.89928C13.3707 3.76139 13.6735 3.69245 14.0408 3.69245C14.2313 3.69245 14.4354 3.71643 14.6531 3.76439C14.8707 3.81235 15.0612 3.86331 15.2245 3.91727L15.5 3.26079C15.3367 3.18885 15.1258 3.1289 14.8673 3.08094C14.6088 3.02698 14.3401 3 14.0612 3C13.5714 3 13.1327 3.08693 12.7449 3.26079C12.3639 3.43465 12.0612 3.70743 11.8367 4.07914C11.7599 4.20642 11.6962 4.34671 11.6456 4.5H10.4672C10.3923 3.84389 10.1892 3.33091 9.85789 2.96104C9.43684 2.47619 8.87544 2.23377 8.17368 2.23377C7.73158 2.23377 7.37018 2.31342 7.08947 2.47273C6.80877 2.63203 6.58772 2.82944 6.42632 3.06493H6.37368C6.38772 2.94719 6.39825 2.78788 6.40526 2.58701ZM9.50561 4.5C9.45154 4.09167 9.33739 3.76569 9.16316 3.52208C8.91754 3.16883 8.53509 2.99221 8.01579 2.99221C7.61579 2.99221 7.3 3.0684 7.06842 3.22078C6.83684 3.36623 6.67193 3.59481 6.57368 3.90649C6.51866 4.07717 6.47905 4.27501 6.45484 4.5H9.50561ZM6.43877 5.5C6.47365 6.00436 6.58178 6.40479 6.76316 6.7013C6.99474 7.06147 7.4193 7.24156 8.03684 7.24156C8.54211 7.24156 8.91754 7.05801 9.16316 6.69091C9.36491 6.39775 9.4861 6.00078 9.52673 5.5H6.43877ZM1.68894 5.86486C1.92857 5.6967 2.35561 5.6006 2.97005 5.57658L3.69816 5.54955V5.98198C3.69816 6.45045 3.55991 6.7988 3.28341 7.02703C3.00691 7.24925 2.65975 7.36036 2.24194 7.36036C1.97773 7.36036 1.7596 7.2973 1.58756 7.17117C1.42166 7.04504 1.33871 6.84685 1.33871 6.57658C1.33871 6.27027 1.45545 6.03303 1.68894 5.86486Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>                                                    
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon_control:ColorPickerPalette x:Name="Part_FontColor"
                                                                               Margin = "2,4,0,4"
                                                                               Width="38"
                                                                               Height="24"
                                                                               Style="{StaticResource SyncfusionColorPickerPaletteStyle}">
                                            <syncribbon_control:ColorPickerPalette.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FontColorScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FontColorTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon_control:ColorPickerPalette.ToolTip>
                                            <syncribbon_control:ColorPickerPalette.HeaderTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="13" 
                                                              Width="10"
                                                              Stretch="None"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M8.0357 9.61282C8.12764 9.84642 8.35313 10 8.60418 10C9.03463 10 9.3301 9.56675 9.17294 9.16602L5.78709 0.532229C5.66119 0.211186 5.35153 0 5.00668 0C4.6622 0 4.3528 0.210734 4.22664 0.531275L0.82072 9.18511C0.6667 9.57645 0.955178 10 1.37573 10C1.62169 10 1.84243 9.84902 1.93161 9.61979L2.98276 6.91771H6.97492L8.0357 9.61282ZM5.32445 2.38494C5.39028 2.58019 5.4373 2.71502 5.46552 2.7894L6.59404 5.80195H3.40596L4.54859 2.7894C4.58621 2.67782 4.62853 2.543 4.67555 2.38494C4.73198 2.22687 4.7837 2.05486 4.83072 1.8689L4.85396 1.78782C4.89476 1.64424 5.08888 1.64089 5.12696 1.78522C5.19279 1.98977 5.25862 2.18968 5.32445 2.38494ZM1 11C0.447715 11 0 11.4477 0 12C0 12.5523 0.447715 13 1 13H9C9.55229 13 10 12.5523 10 12C10 11.4477 9.55229 11 9 11H7H6H1Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon_control:ColorPickerPalette.HeaderTemplate>
                                        </syncribbon_control:ColorPickerPalette>
                                    </StackPanel>
                                </syncribbon:RibbonBar>

                                <syncribbon:RibbonBar x:Name="Part_AlignmentBar"
                                                      Header = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignmentHeader}"
                                                      syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                      IsLauncherButtonVisible ="False"
                                                      IsPanelStateCollapsed="False"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <StackPanel Orientation="Horizontal">
                                        <syncribbon:RibbonButton x:Name="Part_VerticalTop"
                                                                 Margin = "0,4,2,4"
                                                                 Height="24"
                                                                 Width="24"
                                                                 SizeForm="ExtraSmall"
                                                                 IconType="Icon"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignTopScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignTopText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="6" 
                                                              Width="16"
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Top" 
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5C16 0.776142 15.7761 1 15.5 1H0.5C0.223858 1 0 0.776142 0 0.5ZM3.5 5.5C3.5 5.22386 3.72386 5 4 5H12C12.2761 5 12.5 5.22386 12.5 5.5C12.5 5.77614 12.2761 6 12 6H4C3.72386 6 3.5 5.77614 3.5 5.5Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon:RibbonButton x:Name="Part_VerticalCenter"
                                                                 Margin = "2,4,2,4"
                                                                 Height="24"
                                                                 Width="24"                                                                 
                                                                 SizeForm="ExtraSmall"
                                                                 IconType="Icon"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignMiddleScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignMiddleText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="6" 
                                                              Width="16"
                                                              Stretch="Fill"                                                              
                                                              VerticalAlignment="Center" 
                                                              HorizontalAlignment="Center"
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5C16 0.776142 15.7761 1 15.5 1H0.5C0.223858 1 0 0.776142 0 0.5ZM0 5.5C0 5.22386 0.223858 5 0.5 5H15.5C15.7761 5 16 5.22386 16 5.5C16 5.77614 15.7761 6 15.5 6H0.5C0.223858 6 0 5.77614 0 5.5Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon:RibbonButton x:Name="Part_VerticalBottom"
                                                                 Margin = "2,4,0,4"
                                                                 Height="24"
                                                                 Width="24"
                                                                 SizeForm="ExtraSmall"
                                                                 IconType="Icon"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignBottomScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignBottomText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Width="16" 
                                                              Height="6" 
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Bottom"                                                               
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M3.5 0.5C3.5 0.223858 3.72386 0 4 0L12 6.85453e-07C12.2761 7.15256e-07 12.5 0.223858 12.5 0.5C12.5 0.776143 12.2761 1 12 1L4 1C3.72386 1 3.5 0.776142 3.5 0.5ZM0 5.5C2.98023e-08 5.22386 0.223858 5 0.5 5L15.5 5C15.7761 5 16 5.22386 16 5.5C16 5.77614 15.7761 6 15.5 6L0.5 6C0.223858 6 0 5.77614 0 5.5Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <syncribbon:RibbonButton x:Name="Part_HorizontalLeft"
                                                                 Margin = "0,4,2,4"
                                                                 Height="24"
                                                                 Width="24"
                                                                 SizeForm="ExtraSmall"
                                                                 IconType="Icon"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignLeftScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignLeftText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Width="{StaticResource Windows11Light.HeaderTextStyle}" 
                                                              Height="{StaticResource Windows11Light.SubTitleTextStyle}"
                                                              Stretch="None"
                                                              HorizontalAlignment="Left"
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M0.5 1C0.364583 1 0.247396 0.950521 0.148438 0.851562C0.0494792 0.752604 0 0.635417 0 0.5C0 0.364583 0.0494792 0.247396 0.148438 0.148438C0.247396 0.0494792 0.364583 0 0.5 0H11.5C11.6354 0 11.7526 0.0494792 11.8516 0.148438C11.9505 0.247396 12 0.364583 12 0.5C12 0.635417 11.9505 0.752604 11.8516 0.851562C11.7526 0.950521 11.6354 1 11.5 1H0.5ZM0.5 6C0.364583 6 0.247396 5.95052 0.148438 5.85156C0.0494792 5.7526 0 5.63542 0 5.5C0 5.36458 0.0494792 5.2474 0.148438 5.14844C0.247396 5.04948 0.364583 5 0.5 5H15.5C15.6354 5 15.7526 5.04948 15.8516 5.14844C15.9505 5.2474 16 5.36458 16 5.5C16 5.63542 15.9505 5.7526 15.8516 5.85156C15.7526 5.95052 15.6354 6 15.5 6H0.5ZM0.5 11C0.364583 11 0.247396 10.9505 0.148438 10.8516C0.0494792 10.7526 0 10.6354 0 10.5C0 10.3646 0.0494792 10.2474 0.148438 10.1484C0.247396 10.0495 0.364583 10 0.5 10H9.5C9.63542 10 9.7526 10.0495 9.85156 10.1484C9.95052 10.2474 10 10.3646 10 10.5C10 10.6354 9.95052 10.7526 9.85156 10.8516C9.7526 10.9505 9.63542 11 9.5 11H0.5Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon:RibbonButton x:Name="Part_HorizontalCenter"
                                                                 Margin = "2,4,2,4"
                                                                 Height="24"
                                                                 Width="24"
                                                                 SizeForm="ExtraSmall"
                                                                 IconType="Icon"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignCenterScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignCenterText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="{StaticResource Windows11Light.SubTitleTextStyle}" 
                                                              Width="16"
                                                              Stretch="None"
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center" 
                                                              Fill="{StaticResource IconColor}" >
                                                            <Path.Data>
                                                                <PathGeometry>M2.5 1C2.36458 1 2.2474 0.950521 2.14844 0.851562C2.04948 0.752604 2 0.635417 2 0.5C2 0.364583 2.04948 0.247396 2.14844 0.148438C2.2474 0.0494792 2.36458 0 2.5 0H13.5C13.6354 0 13.7526 0.0494792 13.8516 0.148438C13.9505 0.247396 14 0.364583 14 0.5C14 0.635417 13.9505 0.752604 13.8516 0.851562C13.7526 0.950521 13.6354 1 13.5 1H2.5ZM0.5 6C0.364583 6 0.247396 5.95052 0.148438 5.85156C0.0494792 5.7526 0 5.63542 0 5.5C0 5.36458 0.0494792 5.2474 0.148438 5.14844C0.247396 5.04948 0.364583 5 0.5 5H15.5C15.6354 5 15.7526 5.04948 15.8516 5.14844C15.9505 5.2474 16 5.36458 16 5.5C16 5.63542 15.9505 5.7526 15.8516 5.85156C15.7526 5.95052 15.6354 6 15.5 6H0.5ZM4.5 11C4.36458 11 4.2474 10.9505 4.14844 10.8516C4.04948 10.7526 4 10.6354 4 10.5C4 10.3646 4.04948 10.2474 4.14844 10.1484C4.2474 10.0495 4.36458 10 4.5 10H11.5C11.6354 10 11.7526 10.0495 11.8516 10.1484C11.9505 10.2474 12 10.3646 12 10.5C12 10.6354 11.9505 10.7526 11.8516 10.8516C11.7526 10.9505 11.6354 11 11.5 11H4.5Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon:RibbonButton x:Name="Part_HorizontalRight"
                                                                 Margin = "2,4,2,4"
                                                                 Height="24"
                                                                 Width="24"
                                                                 SizeForm="ExtraSmall"
                                                                 IconType="Icon"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignRightScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignRightText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="{StaticResource Windows11Light.SubTitleTextStyle}" 
                                                              Width="{StaticResource Windows11Light.HeaderTextStyle}"
                                                              Stretch="None"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M4.5 1C4.36458 1 4.2474 0.950521 4.14844 0.851562C4.04948 0.752604 4 0.635417 4 0.5C4 0.364583 4.04948 0.247396 4.14844 0.148438C4.2474 0.0494792 4.36458 0 4.5 0H15.5C15.6354 0 15.7526 0.0494792 15.8516 0.148438C15.9505 0.247396 16 0.364583 16 0.5C16 0.635417 15.9505 0.752604 15.8516 0.851562C15.7526 0.950521 15.6354 1 15.5 1H4.5ZM0.5 6C0.364583 6 0.247396 5.95052 0.148438 5.85156C0.0494792 5.7526 0 5.63542 0 5.5C0 5.36458 0.0494792 5.2474 0.148438 5.14844C0.247396 5.04948 0.364583 5 0.5 5H15.5C15.6354 5 15.7526 5.04948 15.8516 5.14844C15.9505 5.2474 16 5.36458 16 5.5C16 5.63542 15.9505 5.7526 15.8516 5.85156C15.7526 5.95052 15.6354 6 15.5 6H0.5ZM7.5 11C7.36458 11 7.2474 10.9505 7.14844 10.8516C7.04948 10.7526 7 10.6354 7 10.5C7 10.3646 7.04948 10.2474 7.14844 10.1484C7.2474 10.0495 7.36458 10 7.5 10H15.5C15.6354 10 15.7526 10.0495 15.8516 10.1484C15.9505 10.2474 16 10.3646 16 10.5C16 10.6354 15.9505 10.7526 15.8516 10.8516C15.7526 10.9505 15.6354 11 15.5 11H7.5Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon:RibbonButton x:Name="Part_HorizontalJustify"
                                                                 Margin = "2,4,0,4"
                                                                 Height="24"
                                                                 Width="24"
                                                                 SizeForm="ExtraSmall"
                                                                 IconType="Icon"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=JustifyScreenTipText}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=JustifyTooltipText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="{StaticResource Windows11Light.SubTitleTextStyle}" 
                                                              Width="{StaticResource Windows11Light.HeaderTextStyle}" 
                                                              Stretch="None"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5C16 0.776142 15.7761 1 15.5 1H0.5C0.223858 1 0 0.776142 0 0.5ZM0 5.5C0 5.22386 0.223858 5 0.5 5H15.5C15.7761 5 16 5.22386 16 5.5C16 5.77614 15.7761 6 15.5 6H0.5C0.223858 6 0 5.77614 0 5.5ZM0 10.5C0 10.2239 0.223858 10 0.5 10H15.5C15.7761 10 16 10.2239 16 10.5C16 10.7761 15.7761 11 15.5 11H0.5C0.223858 11 0 10.7761 0 10.5Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                    </StackPanel>
                                </syncribbon:RibbonBar>

                                <syncribbon:RibbonBar x:Name="Part_Toolsbar"
                                                      Header = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ToolsHeader}"
                                                      IsLauncherButtonVisible ="False"
                                                      IsPanelStateCollapsed="False"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <syncribbon:SplitButton x:Name="Part_PointerPanTool"
                                                            HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            Height="{StaticResource Windows11Light.MinHeight}"
                                                            Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PointerTool}"
                                                            IconType="Icon"
                                                            SizeForm="Small"
                                                            syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                            Style="{StaticResource SyncfusionSplitButtonStyle}">
                                        <syncribbon:SplitButton.Margin>
                                            <Thickness Left="0" Top="2" Right="2" Bottom="0"/>
                                        </syncribbon:SplitButton.Margin>
                                        <syncribbon:SplitButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PointerTool}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PointerToolText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:SplitButton.ToolTip>
                                        <syncribbon:SplitButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="15"
                                                          Width="12"
                                                          Stretch="Fill" 
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M4.12787 9.76025C4.41783 9.82626 4.66337 10.0179 4.79784 10.2831L6.65464 13.9458C6.65726 13.951 6.67287 13.9738 6.71424 13.9897C6.75561 14.0056 6.78252 13.9991 6.78792 13.997L8.70575 13.2571L7.03083 9.95323C6.87952 9.65475 6.88753 9.30038 7.05218 9.00905C7.21682 8.71772 7.51627 8.52805 7.85002 8.50371L10.8743 8.28311L1.84793 1.03193C1.82833 1.01618 1.81004 1.00768 1.79432 1.00347L1.00141 11.57C1.00857 11.5774 1.0211 11.588 1.04049 11.5982C1.07139 11.6144 1.09785 11.6177 1.11 11.6172C1.11056 11.6172 1.11106 11.6172 1.11151 11.6171L3.29759 9.94162C3.53362 9.76071 3.83791 9.69424 4.12787 9.76025ZM11.1153 9.26819C12.0108 9.20286 12.3162 8.15869 11.6065 7.58859L2.47421 0.252329C1.82349 -0.270414 0.861917 0.0652087 0.804134 0.835243L0.00273334 11.5149C-0.0616574 12.373 1.02628 12.9424 1.71768 12.4125L3.90591 10.7353L5.76271 14.398C6.01243 14.8906 6.63258 15.1288 7.14785 14.93L9.16501 14.1518C9.68028 13.953 9.89555 13.3925 9.64582 12.8999L7.92277 9.50106L11.1153 9.26819Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:SplitButton.IconTemplate>
                                        <syncribbon:SplitButton.Items>
                                            <syncribbon:RibbonButton x:Name="Part_PointerTool"
                                                                     Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PointerTool}"     
                                                                     SizeForm = "Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     IconType = "Icon"
                                                                     Height="{StaticResource Windows11Light.MinHeight}"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}"
                                                                     IconTemplate="{StaticResource PointerToolTemplate}"
                                                                     IsSelected="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Tool , Mode=TwoWay , Converter={StaticResource pointertoolconverter}}">
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_ZoomPanTool"
                                                                     Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PanTool}"     
                                                                     SizeForm = "Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     IconType = "Icon"
                                                                     Height="{StaticResource Windows11Light.MinHeight}"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}"
                                                                     IconTemplate="{StaticResource PanToolTemplete}"
                                                                     IsSelected="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Tool , Mode=TwoWay , Converter={StaticResource ZoomPanToolToBoolConverter}}">
                                            </syncribbon:RibbonButton>
                                        </syncribbon:SplitButton.Items>
                                    </syncribbon:SplitButton>
                                    <syncribbon:RibbonButton x:Name="Part_ConnectorTool"
                                                             Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Connector}"
                                                             SizeForm = "Small"
                                                             IconType = "Icon"
                                                             Height="{StaticResource Windows11Light.MinHeight}"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}"
                                                             IsSelected="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.DrawingTool , Mode=TwoWay , Converter={StaticResource ConnectorDrawingToolToBoolConverter}}">
                                        <syncribbon:RibbonButton.Margin>
                                            <Thickness Left="0" Top="2" Right="2" Bottom="0"/>
                                        </syncribbon:RibbonButton.Margin>
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Connector}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ConnectorText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="{StaticResource Windows11Light.SubHeaderTextStyle}" 
                                                          Width="13"
                                                          Stretch="None"
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center" 
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M8 1.5C8 0.671573 8.67157 0 9.5 0H11.5C12.3284 0 13 0.671573 13 1.5V3.5C13 4.32843 12.3284 5 11.5 5H11V6C11 6.82843 10.3284 7.5 9.5 7.5H3.5C3.22386 7.5 3 7.72386 3 8L3 9H3.5C4.32843 9 5 9.67157 5 10.5V12.5C5 13.3284 4.32843 14 3.5 14H1.5C0.671573 14 0 13.3284 0 12.5V10.5C0 9.67157 0.671573 9 1.5 9H2V8C2 7.17157 2.67157 6.5 3.5 6.5H9.5C9.77614 6.5 10 6.27614 10 6V5H9.5C8.67157 5 8 4.32843 8 3.5V1.5ZM9.5 1C9.22386 1 9 1.22386 9 1.5V3.5C9 3.77614 9.22386 4 9.5 4H11.5C11.7761 4 12 3.77614 12 3.5V1.5C12 1.22386 11.7761 1 11.5 1H9.5ZM1.5 10C1.22386 10 1 10.2239 1 10.5V12.5C1 12.7761 1.22386 13 1.5 13H3.5C3.77614 13 4 12.7761 4 12.5V10.5C4 10.2239 3.77614 10 3.5 10H1.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                    <syncribbon:RibbonButton x:Name="Part_TextNodeTool"
                                                             Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Text}"
                                                             SizeForm = "Small"
                                                             IconType = "Icon"
                                                             Height="{StaticResource Windows11Light.MinHeight}"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}"
                                                             IsSelected="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.DrawingTool , Mode=TwoWay , Converter={StaticResource TextNodeDrawingToolToBoolConverter}}">
                                        <syncribbon:RibbonButton.Margin>
                                            <Thickness Left="0" Top="2" Right="2" Bottom="0"/>
                                        </syncribbon:RibbonButton.Margin>
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Text}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=TextTooltipText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="{StaticResource Windows11Light.SubTitleTextStyle}" 
                                                          Width="11" 
                                                          Stretch="None"
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M9.90042 12C9.59606 12 9.32232 11.8148 9.20906 11.5323L7.91379 8.30125H3.03448L1.75095 11.5407C1.64111 11.8179 1.37312 12 1.07492 12V12C0.560237 12 0.208453 11.48 0.399955 11.0023L4.55864 0.627919C4.71068 0.248625 5.0782 0 5.48684 0H5.5295C5.93859 0 6.30642 0.249168 6.45816 0.629069L10.5921 10.9789C10.7875 11.4681 10.4272 12 9.90042 12V12ZM6.06897 3.34728C6.03448 3.25802 5.97701 3.09623 5.89655 2.86192C5.81609 2.62761 5.73563 2.38773 5.65517 2.14226V2.14226C5.608 1.96674 5.372 1.97092 5.32147 2.1455C5.31213 2.17778 5.30267 2.21018 5.2931 2.24268C5.23563 2.46583 5.17241 2.67225 5.10345 2.86192C5.04598 3.0516 4.99425 3.21339 4.94828 3.34728L3.55172 6.96234H7.44828L6.06897 3.34728Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                    <syncribbon:SplitButton x:Name="Part_DrawingTool"
                                                            Width="44"
                                                            Height="{StaticResource Windows11Light.MinHeight}" 
                                                            Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Rectangle}"
                                                            IconType="Icon"
                                                            SizeForm="Small"
                                                            HitTestArea="ImageOnly"
                                                            syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                            Style="{StaticResource SyncfusionSplitButtonStyle}">
                                        <syncribbon:SplitButton.Margin>
                                            <Thickness Left="2" Top="2" Right="0" Bottom="0"/>
                                        </syncribbon:SplitButton.Margin>
                                        <syncribbon:SplitButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=DrawingTools}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=DrawingToolsText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:SplitButton.ToolTip>
                                        <syncribbon:SplitButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path x:Name="Rectangle_Draw_Tool_16"
                                                          Width="15"
                                                          Height="11"
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center" 
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M0 1.5C0 0.671573 0.671573 0 1.5 0H13.5C14.3284 0 15 0.671573 15 1.5V9.5C15 10.3284 14.3284 11 13.5 11H1.5C0.671573 11 0 10.3284 0 9.5V1.5ZM1.5 1C1.22386 1 1 1.22386 1 1.5V9.5C1 9.77614 1.22386 10 1.5 10H13.5C13.7761 10 14 9.77614 14 9.5V1.5C14 1.22386 13.7761 1 13.5 1H1.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:SplitButton.IconTemplate>
                                        <syncribbon:SplitButton.Items>
                                            <syncribbon:RibbonButton x:Name="Part_RectangleTool"
                                                                     Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Rectangle}"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     IconType="Icon"
                                                                     Height="24"
                                                                     IconTemplate="{StaticResource RectangleToolTemplate}">
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_EllipseTool"
                                                                     Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Ellipse}"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     IconType="Icon"
                                                                     Height="24"
                                                                     IconTemplate="{StaticResource EllipseToolTemplate}">
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_LineTool"
                                                                     Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Line}"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     IconType="Icon"
                                                                     Height="24"
                                                                     IconTemplate="{StaticResource LineToolTemplate}">
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_PolylineTool"
                                                                     Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Polyline}"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     IconType="Icon"
                                                                     Height="24"
                                                                     IconTemplate="{StaticResource PolyLineToolTemplate}">
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_FreehandTool"
                                                                     Label = "{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FreeHand}"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     IconType="Icon"
                                                                     Height="24"
                                                                     IconTemplate="{StaticResource FreeHandToolTemplate}">
                                            </syncribbon:RibbonButton>
                                        </syncribbon:SplitButton.Items>
                                    </syncribbon:SplitButton>
                                    <syncribbon:RibbonButton x:Name="Part_PortVisibilityTool"
                                                             SizeForm = "ExtraSmall"
                                                             IconType = "Icon"
                                                             Width="24"
                                                             Height="{StaticResource Windows11Light.MinHeight}"
                                                             HorizontalAlignment="Left"
                                                             syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}"
                                                             IsSelected="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.PortVisibility , Mode=TwoWay , Converter={StaticResource PortVisibilityToBoolConverter}}">
                                        <syncribbon:RibbonButton.Margin>
                                            <Thickness Left="2" Top="2" Right="0" Bottom="0"/>
                                        </syncribbon:RibbonButton.Margin>
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PortVisibilityText}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PortVisibilityToolTipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="13" 
                                                          Width="13" 
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Stretch="Fill"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M0.146447 0.146447C0.341709 -0.0488155 0.658291 -0.0488155 0.853553 0.146447L6.5 5.79289L12.1464 0.146447C12.3417 -0.0488155 12.6583 -0.0488155 12.8536 0.146447C13.0488 0.341709 13.0488 0.658291 12.8536 0.853553L7.20711 6.5L12.8536 12.1464C13.0488 12.3417 13.0488 12.6583 12.8536 12.8536C12.6583 13.0488 12.3417 13.0488 12.1464 12.8536L6.5 7.20711L0.853553 12.8536C0.658291 13.0488 0.341709 13.0488 0.146447 12.8536C-0.0488155 12.6583 -0.0488155 12.3417 0.146447 12.1464L5.79289 6.5L0.146447 0.853553C-0.0488155 0.658291 -0.0488155 0.341709 0.146447 0.146447Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                    <syncribbon:RibbonButton x:Name="Part_SelectAnnotation"
                                                             SizeForm = "ExtraSmall"
                                                             IconType = "Icon"
                                                             Width="24"
                                                             Height="{StaticResource Windows11Light.MinHeight}"
                                                             HorizontalAlignment="Left"
                                                             syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                        <syncribbon:RibbonButton.Margin>
                                            <Thickness Left="2" Top="2" Right="0" Bottom="0"/>
                                        </syncribbon:RibbonButton.Margin>
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SelectAnnotation}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SelectAnnotationText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid VerticalAlignment="Center" 
                                                      HorizontalAlignment="Center">
                                                    <Path Height="15" 
                                                          Width="15" 
                                                          Stretch="Fill" 
                                                          HorizontalAlignment="Left"
                                                          VerticalAlignment="Top"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M7.14645 0.146447C7.34171 -0.0488155 7.65829 -0.0488155 7.85355 0.146447L8.78284 1.07574C9.01716 1.31005 9.01716 1.68995 8.78284 1.92426L7.85355 2.85355C7.65829 3.04882 7.34171 3.04882 7.14645 2.85355C6.95118 2.65829 6.95118 2.34171 7.14645 2.14645L7.29289 2H5.5C3.567 2 2 3.567 2 5.5V7.79289L2.14645 7.64645C2.34171 7.45118 2.65829 7.45118 2.85355 7.64645C3.04882 7.84171 3.04882 8.15829 2.85355 8.35355L1.92426 9.28284C1.68995 9.51716 1.31005 9.51716 1.07574 9.28284L0.146447 8.35355C-0.0488155 8.15829 -0.0488156 7.84171 0.146447 7.64645C0.341709 7.45118 0.658291 7.45118 0.853553 7.64645L1 7.79289V5.5C1 3.01472 3.01472 1 5.5 1H7.29289L7.14645 0.853553C6.95118 0.658291 6.95118 0.341709 7.14645 0.146447ZM5.5 5C5.22386 5 5 5.22386 5 5.5C5 5.77614 5.22386 6 5.5 6C5.77614 6 6 5.77614 6 5.5C6 5.22386 5.77614 5 5.5 5ZM4 5.5C4 4.67157 4.67157 4 5.5 4C6.15311 4 6.70873 4.4174 6.91465 5H12.0854C12.2913 4.4174 12.8469 4 13.5 4C14.3284 4 15 4.67157 15 5.5C15 6.15311 14.5826 6.70873 14 6.91465V12.0854C14.5826 12.2913 15 12.8469 15 13.5C15 14.3284 14.3284 15 13.5 15C12.8469 15 12.2913 14.5826 12.0854 14H6.91465C6.70873 14.5826 6.15311 15 5.5 15C4.67157 15 4 14.3284 4 13.5C4 12.8469 4.4174 12.2913 5 12.0854V6.91465C4.4174 6.70873 4 6.15311 4 5.5ZM6 6.91465V12.0854C6.42621 12.236 6.764 12.5738 6.91465 13H12.0854C12.236 12.5738 12.5738 12.236 13 12.0854V6.91465C12.5738 6.764 12.236 6.42621 12.0854 6H6.91465C6.764 6.42621 6.42621 6.764 6 6.91465ZM13.5 5C13.2239 5 13 5.22386 13 5.5C13 5.77614 13.2239 6 13.5 6C13.7761 6 14 5.77614 14 5.5C14 5.22386 13.7761 5 13.5 5ZM5.5 13C5.22386 13 5 13.2239 5 13.5C5 13.7761 5.22386 14 5.5 14C5.77614 14 6 13.7761 6 13.5C6 13.2239 5.77614 13 5.5 13ZM13.5 13C13.2239 13 13 13.2239 13 13.5C13 13.7761 13.2239 14 13.5 14C13.7761 14 14 13.7761 14 13.5C14 13.2239 13.7761 13 13.5 13Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                </syncribbon:RibbonBar>

                                <syncribbon:RibbonBar x:Name="Part_ShapeStylesBar"
                                                      Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ShapeStyles}"
                                                      IsLauncherButtonVisible="False"
                                                      syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <syncribbon:RibbonBar.IconTemplate>
                                        <DataTemplate>
                                            <Grid>
                                                <Path Width="22"
                                                      Height="21"
                                                      Stretch="None"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      Data="M2.5 0C1.11929 0 0 1.11929 0 2.5V18.5C0 19.8807 1.11929 21 2.5 21H6C6.27614 21 6.5 20.7761 6.5 20.5C6.5 20.2239 6.27614 20 6 20H2.5C1.67157 20 1 19.3284 1 18.5V2.5C1 1.67157 1.67157 1 2.5 1H16.5C17.3284 1 18 1.67157 18 2.5V4C18 4.27614 18.2239 4.5 18.5 4.5C18.7761 4.5 19 4.27614 19 4V2.5C19 1.11929 17.8807 0 16.5 0H2.5ZM18.3484 7.61911C19.0549 7.04433 19.7235 6.67245 20.3293 6.53691C20.3182 6.64005 20.2896 6.78686 20.2307 6.98409C20.0368 7.63329 19.5932 8.58084 18.9511 9.69946C17.7827 11.7348 16.0112 14.244 14.0883 16.374C13.9196 16.0419 13.7089 15.7663 13.4617 15.5497C13.2437 15.3587 13.0041 15.22 12.7534 15.1296C13.5194 13.6846 14.6844 11.7596 16.0051 10.0846C16.761 9.12594 17.5573 8.26268 18.3484 7.61911ZM19.8183 10.1973C18.5402 12.4239 16.569 15.1897 14.4468 17.4621C14.58 18.2075 14.4606 18.8551 14.1107 19.3878C13.729 19.969 13.1173 20.3451 12.4476 20.5849C11.1167 21.0615 9.35966 21.071 7.93866 20.8954C7.73574 20.8703 7.56854 20.7242 7.51648 20.5265C7.46442 20.3287 7.53799 20.1192 7.70224 19.9975C7.84941 19.8884 8.10828 19.5853 8.40917 19.0686C8.7004 18.5685 9.00512 17.9132 9.26233 17.1507C9.65273 15.9933 10.6279 15.1639 11.6818 15.0217C12.4604 13.5046 13.7412 11.3408 15.2199 9.46542C16.0015 8.47406 16.849 7.54982 17.7173 6.84339C18.5784 6.14277 19.5035 5.62067 20.438 5.50386C20.4586 5.50129 20.4793 5.5 20.5 5.5C20.6916 5.5 20.8975 5.55669 21.0636 5.71319C21.2224 5.8629 21.2907 6.05348 21.3178 6.21809C21.3691 6.52953 21.2985 6.90324 21.1889 7.27029C20.9618 8.03062 20.4727 9.05726 19.8183 10.1973ZM12.0007 16C12.2877 15.9996 12.5638 16.0926 12.8028 16.302C13.0449 16.5141 13.2754 16.8696 13.418 17.4331C13.5814 18.0789 13.4841 18.5203 13.2749 18.8388C13.0554 19.1729 12.6646 19.445 12.1105 19.6434C11.2483 19.9521 10.1126 20.0388 9.01521 19.9851C9.10233 19.8557 9.18869 19.7172 9.27333 19.5718C9.6008 19.0095 9.93266 18.2921 10.2099 17.4703C10.5231 16.5416 11.3203 16.0009 12.0007 16Z"
                                                      Fill="{StaticResource IconColor}"/> 
                                            </Grid>
                                        </DataTemplate>
                                    </syncribbon:RibbonBar.IconTemplate>
                                    <syncribbon:RibbonGallery x:Name="Part_ShapeStyle" 
                                                              Width="300"
                                                              Margin="0,4,2,4"
                                                              ItemHeight="58"
                                                              VisualMode="InRibbon"
                                                              ExpandWidth="420"
                                                              ExpandHeight="500"
                                                              ItemWidth="55"
                                                              IsEnabled="False"
                                                              Visibility="Visible" 
                                                              Style="{StaticResource SyncfusionRibbonGalleryStyle}">
                                        <syncribbon:RibbonGallery.GalleryFilters>
                                            <syncribbon:RibbonGalleryFilter Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=All}"/>
                                            <syncribbon:RibbonGalleryFilter Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=VariantStyle}"/>
                                            <syncribbon:RibbonGalleryFilter Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ThemeStyle}"/>
                                        </syncribbon:RibbonGallery.GalleryFilters>
                                        <syncribbon:RibbonGallery.GalleryGroups>
                                            <syncribbon:RibbonGalleryGroup Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=VariantStyle}" syncribbon:RibbonGallery.FilterIndexes="0,1" Style="{StaticResource SyncfusionRibbonGalleryGroupStyle}">
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Variant1" x:Name = "Variant0_RibbonItem" Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[0], Mode=TwoWay}" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}">
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Variant1}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Variant2" x:Name = "Variant1_RibbonItem" Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[1]}" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}">
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Variant2}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Variant3" x:Name = "Variant2_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[2]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}">
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Variant3}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Variant4" x:Name = "Variant3_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[3]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}">
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Variant4}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                            </syncribbon:RibbonGalleryGroup>
                                            <syncribbon:RibbonGalleryGroup Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ThemeStyle}" syncribbon:RibbonGallery.FilterIndexes="0,2">
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent1" x:Name = "SubtlyAccent1_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[36]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}">
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent1}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent2" x:Name = "SubtlyAccent2_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[68]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}">
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent2}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent3" x:Name = "SubtlyAccent3_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[100]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}">
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent3}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent4" x:Name = "SubtlyAccent4_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[132]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}">
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent4}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent5" x:Name = "SubtlyAccent5_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[164]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}">
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent5}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent6" x:Name = "SubtlyAccent6_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[196]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent6}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent7" x:Name = "SubtlyAccent7_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[228]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent7}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Refined,Accent1" x:Name = "RefinedAccent1_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[40]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RefinedAccent1}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Refined,Accent2" x:Name = "RefinedAccent2_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[72]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RefinedAccent2}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Refined,Accent3" x:Name = "RefinedAccent3_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[104]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RefinedAccent3}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Refined,Accent4" x:Name = "RefinedAccent4_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[136]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RefinedAccent4}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Refined,Accent5" x:Name = "RefinedAccent5_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[168]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RefinedAccent5}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Refined,Accent6" x:Name = "RefinedAccent6_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[200]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RefinedAccent6}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Refined,Accent7" x:Name = "RefinedAccent7_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[232]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RefinedAccent7}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Balanced,Accent1" x:Name = "BalancedAccent1_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[44]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BalancedAccent1}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Balanced,Accent2" x:Name = "BalancedAccent2_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[76]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BalancedAccent2}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Balanced,Accent3" x:Name = "BalancedAccent3_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[108]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BalancedAccent3}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Balanced,Accent4" x:Name = "BalancedAccent4_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[140]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BalancedAccent4}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Balanced,Accent5" x:Name = "BalancedAccent5_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[172]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BalancedAccent5}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Balanced,Accent6" x:Name = "BalancedAccent6_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[204]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BalancedAccent6}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Balanced,Accent7" x:Name = "BalancedAccent7_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[236]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BalancedAccent7}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent1" x:Name = "ModerateAccent1_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[48]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent1}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent2" x:Name = "ModerateAccent2_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[80]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent2}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent3" x:Name = "ModerateAccent3_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[112]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent3}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent4" x:Name = "ModerateAccent4_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[144]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent4}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent5" x:Name = "ModerateAccent5_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[176]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent5}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent6" x:Name = "ModerateAccent6_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[208]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent6}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent7" x:Name = "ModerateAccent7_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[240]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent7}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Focused,Accent1" x:Name = "FocusedAccent1_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[52]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FocusedAccent1}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Focused,Accent2" x:Name = "FocusedAccent2_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[84]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FocusedAccent2}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Focused,Accent3" x:Name = "FocusedAccent3_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[116]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FocusedAccent3}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Focused,Accent4" x:Name = "FocusedAccent4_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[148]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FocusedAccent4}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Focused,Accent5" x:Name = "FocusedAccent5_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[180]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FocusedAccent5}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Focused,Accent6" x:Name = "FocusedAccent6_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[212]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FocusedAccent6}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Focused,Accent7" x:Name = "FocusedAccent7_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[244]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FocusedAccent7}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent1" x:Name = "IntenseAccent1_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[56]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent1}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent2" x:Name = "IntenseAccent2_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[88]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent2}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent3" x:Name = "IntenseAccent3_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[120]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent3}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent4" x:Name = "IntenseAccent4_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[152]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent4}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent5" x:Name = "IntenseAccent5_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[184]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent5}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent6" x:Name = "IntenseAccent6_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[216]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent6}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent7" x:Name = "IntenseAccent7_RibbonItem" ContentTemplate = "{ StaticResource effectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.NodeStyles[248]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent7}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>

                                            </syncribbon:RibbonGalleryGroup>
                                        </syncribbon:RibbonGallery.GalleryGroups>
                                    </syncribbon:RibbonGallery>
                                    <syncribbon:RibbonGallery x:Name="Part_LineStyle"  
                                                                Width="300"
                                                              Margin="0,4,2,4"
                                                              ItemHeight="58"
                                                               VisualMode="InRibbon"
                                                              ExpandWidth="420"
                                                              ExpandHeight="250"
                                                              ItemWidth="55"
                                                              IsEnabled="False"
                                                              Visibility="Collapsed"
                                                              Style="{StaticResource SyncfusionRibbonGalleryStyle}">
                                        <syncribbon:RibbonGallery.GalleryFilters>
                                            <syncribbon:RibbonGalleryFilter Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ThemeStyle}"/>
                                        </syncribbon:RibbonGallery.GalleryFilters>
                                        <syncribbon:RibbonGallery.GalleryGroups>
                                            <syncribbon:RibbonGalleryGroup Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ThemeStyle}" syncribbon:RibbonGallery.FilterIndexes="0,0" Style="{StaticResource SyncfusionRibbonGalleryGroupStyle}">
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent1" x:Name = "conSubtlyAccent1_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[36]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent1}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent2" x:Name = "conSubtlyAccent2_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[68]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent2}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent3" x:Name = "conSubtlyAccent3_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[100]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent3}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent4" x:Name = "conSubtlyAccent4_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[132]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent4}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent5" x:Name = "conSubtlyAccent5_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[164]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent5}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent6" x:Name = "conSubtlyAccent6_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[196]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent6}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Subtly,Accent7" x:Name = "conSubtlyAccent7_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[228]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SubtlyAccent7}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent1" x:Name = "conModerateAccent1_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[48]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent1}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent2" x:Name = "conModerateAccent2_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[80]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent2}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent3" x:Name = "conModerateAccent3_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[112]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent3}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent4" x:Name = "conModerateAccent4_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[144]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent4}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent5" x:Name = "conModerateAccent5_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[176]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent5}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent6" x:Name = "conModerateAccent6_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[208]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent6}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Moderate,Accent7" x:Name = "conModerateAccent7_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[240]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ModerateAccent7}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent1" x:Name = "conIntenseAccent1_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[56]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent1}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent2" x:Name = "conIntenseAccent2_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[88]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent2}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent3" x:Name = "conIntenseAccent3_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[120]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent3}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent4" x:Name = "conIntenseAccent4_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[152]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent4}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent5" x:Name = "conIntenseAccent5_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[184]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent5}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent6" x:Name = "conIntenseAccent6_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[216]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent6}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                                <diagribbon:DiagramShapeStyleGalleryItem ThemeStyleId="Intense,Accent7" x:Name = "conIntenseAccent7_RibbonItem" ContentTemplate = "{ StaticResource coneffectStyleItemTemplate}" 
                                                              Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorStyles[248]}" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}"
                                                              >
                                                    <diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntenseAccent7}"/>
                                                    </diagribbon:DiagramShapeStyleGalleryItem.ToolTip>
                                                </diagribbon:DiagramShapeStyleGalleryItem>
                                            </syncribbon:RibbonGalleryGroup>
                                        </syncribbon:RibbonGallery.GalleryGroups>
                                    </syncribbon:RibbonGallery>
                                    <syncribbon:DropDownButton x:Name="FillDropDown"
                                                               Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Fill}"
                                                               Height="24"
                                                               HorizontalAlignment="Left"
                                                               ContextMenuService.Placement="Bottom"
                                                               Style="{StaticResource SyncfusionRibbonDropDownButtonStyle}">
                                        <syncribbon:DropDownButton.Margin>
                                            <Thickness Left="2" Top="4" Right="0" Bottom="4"/>
                                        </syncribbon:DropDownButton.Margin>
                                        <syncribbon:DropDownButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Fill}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FillText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:DropDownButton.ToolTip>
                                        <syncribbon:DropDownButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path HorizontalAlignment="Center"
                                                          Height="15" 
                                                          Stretch="Fill" 
                                                          VerticalAlignment="Center" 
                                                          Width="15"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M6.7069 1C6.48793 1 6.29522 1.17821 6.29522 1.41644V2.07975C6.29522 2.47306 6.13649 2.85063 5.8532 3.13115L1.4047 7.53621H11.2132C11.2479 7.37745 11.2026 7.20545 11.0772 7.08127L9.77651 5.79332L10.5094 5.09239L11.8101 6.38034C12.3994 6.96394 12.3994 7.89953 11.8101 8.48313L6.88078 13.3643C6.27675 13.9624 5.28612 13.9624 4.68208 13.3643L0.442019 9.16561C-0.14734 8.58201 -0.147339 7.64643 0.442019 7.06282L5.1203 2.43021C5.21473 2.33671 5.26764 2.21085 5.26764 2.07975V1.41644C5.26764 0.642395 5.90363 5.96046e-08 6.7069 0C7.51018 5.96046e-08 8.14617 0.642395 8.14617 1.41644V4.97499C8.14617 5.25113 7.91613 5.47499 7.63238 5.47499C7.34862 5.47499 7.11859 5.25113 7.11859 4.97499V1.41644C7.11859 1.17821 6.92588 1 6.7069 1ZM10.3157 8.53621H1.24715L5.41498 12.6633C5.61633 12.8627 5.94654 12.8627 6.14788 12.6633L10.3157 8.53621ZM12.1456 9.36423C12.3906 9.01765 12.9165 9.01765 13.1615 9.36423L14.576 11.3653C15.6502 12.8848 14.5642 15 12.6535 15C10.7429 15 9.65685 12.8849 10.731 11.3653L12.1456 9.36423ZM12.6535 10.41L11.5776 11.932C10.9538 12.8146 11.6032 14 12.6535 14C13.7039 14 14.3533 12.8146 13.7294 11.932L12.6535 10.41Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:DropDownButton.IconTemplate>
                                        <StackPanel>
                                            <syncribbon_control:ColorPickerPalette x:Name="Part_FillShape"
                                                                                   Mode="Palette"
                                                                                   GenerateStandardVariants="True"
                                                                                   RecentlyUsedPanelVisibility="Collapsed"
                                                                                   ThemePanelVisibility="Collapsed"
                                                                                   NoColorVisibility="Visible"
                                                                                   BorderThickness="0"
                                                                                   Style="{StaticResource SyncfusionColorPickerPaletteStyle}">
                                            </syncribbon_control:ColorPickerPalette>
                                        </StackPanel>
                                    </syncribbon:DropDownButton>
                                    <syncribbon:DropDownButton x:Name="LineDropDown" 
                                                               Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Line}"
                                                               HorizontalAlignment="Left"
                                                               Height="24"
                                                               ContextMenuService.Placement="Bottom"
                                                               Style="{StaticResource SyncfusionRibbonDropDownButtonStyle}">
                                        <syncribbon:DropDownButton.Margin>
                                            <Thickness Left="2" Top="4" Right="0" Bottom="4"/>
                                        </syncribbon:DropDownButton.Margin>
                                        <syncribbon:DropDownButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Line}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=LineText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:DropDownButton.ToolTip>
                                        <syncribbon:DropDownButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="15" 
                                                          Width="{StaticResource Windows11Light.SubTitleTextStyle}" 
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"                                                          
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M11.8394 2.78063C11.9465 2.51186 12 2.23518 12 1.95059C12 1.67787 11.9504 1.42292 11.8513 1.18577C11.7521 0.948617 11.6133 0.743083 11.4348 0.56917C11.2603 0.391304 11.054 0.252964 10.8161 0.15415C10.5781 0.0513834 10.3223 0 10.0486 0C9.78681 0 9.53099 0.0513834 9.28111 0.15415C9.0352 0.252964 8.81904 0.395257 8.63262 0.581028L0.945959 8.24111C0.898364 8.28854 0.858701 8.34387 0.826971 8.40711C0.79524 8.4664 0.76946 8.52964 0.749628 8.59684L0.0118989 11.5316C0.00396629 11.5711 0 11.6028 0 11.6265C0 11.7292 0.0376797 11.8182 0.113039 11.8933C0.188399 11.9644 0.27764 12 0.380764 12C0.400595 12 0.430342 11.996 0.470005 11.9881L3.41497 11.253C3.4824 11.2332 3.54586 11.2075 3.60535 11.1759C3.66881 11.1443 3.72434 11.1047 3.77194 11.0573L11.3753 3.48024C11.5776 3.27866 11.7323 3.04545 11.8394 2.78063ZM11.1492 1.5C11.2087 1.64625 11.2385 1.80435 11.2385 1.97431C11.2385 2.12451 11.2186 2.25692 11.179 2.37154C11.1433 2.48617 11.0917 2.59289 11.0243 2.6917C10.9608 2.79051 10.8855 2.88538 10.7982 2.97628C10.711 3.06719 10.6177 3.16008 10.5186 3.25494L8.77541 1.51779C8.87457 1.41897 8.96777 1.32411 9.05503 1.2332C9.14229 1.14229 9.23351 1.06126 9.32871 0.990119C9.42786 0.918972 9.53495 0.863636 9.64997 0.824111C9.765 0.780632 9.89787 0.758893 10.0486 0.758893C10.2231 0.758893 10.3837 0.790514 10.5305 0.853755C10.6772 0.913043 10.8022 0.998024 10.9053 1.1087C11.0124 1.21937 11.0937 1.3498 11.1492 1.5ZM3.23054 10.5178L0.904313 11.0988L1.48736 8.78063L8.23996 2.05138L9.98314 3.78854L3.23054 10.5178ZM1 13C0.447715 13 0 13.4477 0 14C0 14.5523 0.447715 15 1 15H7H8H11C11.5523 15 12 14.5523 12 14C12 13.4477 11.5523 13 11 13H1Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:DropDownButton.IconTemplate>
                                        <StackPanel>
                                            <StackPanel.Resources>
                                                <Style TargetType="syncribbon:RibbonMenuItem" BasedOn="{StaticResource SyncfusionRibbonMenuItemStyle}">
                                                    <Setter Property="IconBarEnabled" Value="True"/>
                                                    <Setter Property="Height" Value="24"/>
                                                </Style>
                                            </StackPanel.Resources>
                                            <syncribbon_control:ColorPickerPalette x:Name="Part_FillLine"
                                                                                   Mode="Palette"
                                                                                   NoColorVisibility="Visible"
                                                                                   GenerateStandardVariants="True"
                                                                                   ThemePanelVisibility="Collapsed"
                                                                                   BorderThickness="0"
                                                                                   RecentlyUsedPanelVisibility="Collapsed"
                                                                                   Style="{StaticResource SyncfusionColorPickerPaletteStyle}">
                                            </syncribbon_control:ColorPickerPalette>

                                            <Grid>
                                                <Grid.Margin>
                                                    <Thickness Left="0" Top="-10" Right="0" Bottom="0"/>
                                                </Grid.Margin>
                                                <Border BorderThickness="0">
                                                    <Border.Padding>
                                                        <Thickness Left="7" Top="0" Right="7" Bottom="7"/>
                                                    </Border.Padding>
                                                    <StackPanel>
                                                        <StackPanel.Margin>
                                                            <Thickness Left="6" Top="0" Right="6" Bottom="6"/>
                                                        </StackPanel.Margin>
                                                        <syncribbon:RibbonMenuItem x:Name="Part_LineWeight" 
                                                                       Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Weight}"
                                                                       Height="32" 
                                                                       Width="200" 
                                                                       HorizontalAlignment="Left"
                                                                       Margin="{StaticResource Windows11Light.BorderThickness}">
                                                            <syncribbon:RibbonMenuItem.Padding>
                                                                <Thickness Left="3" Top="0" Right="3" Bottom="0"/>
                                                            </syncribbon:RibbonMenuItem.Padding>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineWeight1" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Path Data="M482.2174,586.4791L567.4014,586.4791" Width="150" Stretch="Fill" Stroke="{StaticResource IconColor}" StrokeThickness="1" />
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineWeight2" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Path Data="M482.2174,586.4791L567.4014,586.4791" Width="150" Stretch="Fill" Stroke="{StaticResource IconColor}" StrokeThickness="2" />
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineWeight3" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Path Data="M482.2174,586.4791L567.4014,586.4791" Width="150" Stretch="Fill" Stroke="{StaticResource IconColor}" StrokeThickness="3" />
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineWeight4" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Path Data="M482.2174,586.4791L567.4014,586.4791" Width="150" Stretch="Fill" Stroke="{StaticResource IconColor}" StrokeThickness="4" />
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineWeight5" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Path Data="M482.2174,586.4791L567.4014,586.4791" Width="150" Stretch="Fill" Stroke="{StaticResource IconColor}" StrokeThickness="5" />
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineWeight6" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Path Data="M482.2174,586.4791L567.4014,586.4791" Width="150" Stretch="Fill" Stroke="{StaticResource IconColor}" StrokeThickness="6" />
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineWeight7" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Path Data="M482.2174,586.4791L567.4014,586.4791" Width="150" Stretch="Fill" Stroke="{StaticResource IconColor}" StrokeThickness="7" />
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineWeight8" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Path Data="M482.2174,586.4791L567.4014,586.4791" Width="150" Stretch="Fill" Stroke="{StaticResource IconColor}" StrokeThickness="8" />
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                        </syncribbon:RibbonMenuItem>

                                                        <syncribbon:RibbonMenuItem x:Name="Part_LineDashes" 
                                                                       Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Dashes}" 
                                                                       Height="32" 
                                                                       Width="200" 
                                                                       HorizontalAlignment="Left">
                                                            <syncribbon:RibbonMenuItem.Margin>
                                                                <Thickness Left="0" Top="3" Right="0" Bottom="0"/>
                                                            </syncribbon:RibbonMenuItem.Margin>
                                                            <syncribbon:RibbonMenuItem.Padding>
                                                                <Thickness Left="3" Top="0" Right="3" Bottom="0"/>
                                                            </syncribbon:RibbonMenuItem.Padding>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineDashes0" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Line X1="0" Y1="7" X2="130" Y2="7"
                                                                  Stretch="Fill" 
                                                                  StrokeThickness="1"
                                                                  Stroke="{StaticResource IconColor}"/>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineDashes1" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Line X1="0" Y1="7" X2="130" Y2="7"
                                                                  Stretch="Fill" 
                                                                  StrokeDashArray="1"
                                                                  StrokeThickness="2"
                                                                  Stroke="{StaticResource IconColor}"/>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineDashes2" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Line X1="0" Y1="7" X2="130" Y2="7"  
                                                                  Stretch="Fill" 
                                                                  StrokeDashArray="2,1"
                                                                  StrokeThickness="2"
                                                                  Stroke="{StaticResource IconColor}"/>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineDashes3" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Line X1="0" Y1="7" X2="130" Y2="7"
                                                                  Stretch="Fill" 
                                                                  StrokeDashArray="2,2"
                                                                  StrokeThickness="2"
                                                                  Stroke="{StaticResource IconColor}"/>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineDashes4" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Line X1="0" Y1="7" X2="130" Y2="7"
                                                                  Stretch="Fill" 
                                                                  StrokeDashArray="3,1"
                                                                  StrokeThickness="2"
                                                                  Stroke="{StaticResource IconColor}"/>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineDashes5" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Line X1="0" Y1="7" X2="130" Y2="7"
                                                                  Stretch="Fill" 
                                                                  StrokeDashArray="4,1"
                                                                  StrokeThickness="2"
                                                                  Stroke="{StaticResource IconColor}"/>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineDashes6" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Line X1="0" Y1="7" X2="130" Y2="7"  
                                                                  Stretch="Fill" 
                                                                  StrokeDashArray="5,2"
                                                                  StrokeThickness="2"
                                                                  Stroke="{StaticResource IconColor}"/>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Part_LineDashes7" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Line X1="0" Y1="7" X2="130" Y2="7"
                                                                  Stretch="Fill" 
                                                                  StrokeDashArray="6,2"
                                                                  StrokeThickness="2"
                                                                  Stroke="{StaticResource IconColor}"/>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                        </syncribbon:RibbonMenuItem>

                                                        <syncribbon:RibbonMenuItem Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Arrows}"
                                                                       Height="32" 
                                                                       Width="200" 
                                                                       HorizontalAlignment="Left">
                                                            <syncribbon:RibbonMenuItem.Margin>
                                                                <Thickness Left="0" Top="3" Right="0" Bottom="3"/>
                                                            </syncribbon:RibbonMenuItem.Margin>
                                                            <syncribbon:RibbonMenuItem.Padding>
                                                                <Thickness Left="3" Top="0" Right="3" Bottom="0"/>
                                                            </syncribbon:RibbonMenuItem.Padding>
                                                            <syncribbon:RibbonMenuItem x:Name="Arrow1" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Grid Margin="0,2,0,2">
                                                                        <Line  X1="0" Y1="7" X2="130" Y2="7" Stroke="{StaticResource IconColor}" StrokeThickness="1" 
                                                                       Stretch="Fill"/>
                                                                        <Path  Data="M0,0 z" 
                                                                       HorizontalAlignment="Left" 
                                                                       VerticalAlignment="Center"
                                                                       RenderTransformOrigin="0.5,0.5" Stretch="Fill" Fill="{StaticResource IconColor}" 
                                                                       Stroke="{StaticResource IconColor}" StrokeThickness="1">
                                                                            <Path.RenderTransform>
                                                                                <RotateTransform Angle="180"/>
                                                                            </Path.RenderTransform>
                                                                        </Path>
                                                                        <Path Data="M0,0 z" HorizontalAlignment="Right" 
                                                                      VerticalAlignment="Center" RenderTransformOrigin="0.5,0.5" Stretch="Fill"
                                                                      Fill="{StaticResource IconColor}" Stroke="{StaticResource IconColor}" StrokeThickness="1" Visibility="Collapsed"/>
                                                                    </Grid>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Arrow2" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Grid Margin="0,2,0,2">
                                                                        <Line  X1="0" Y1="7" X2="130" Y2="7" Stroke="{StaticResource IconColor}" StrokeThickness="1" 
                                                                       Stretch="Fill"/>
                                                                        <Path  Data="M0.511,0L5.099,4.734L0.511,9.468L0,8.941L4.077,4.734L0,0.527z"
                                                                       HorizontalAlignment="Left" 
                                                                       VerticalAlignment="Center"
                                                                       RenderTransformOrigin="0.5,0.5" Stretch="Fill" Fill="{StaticResource IconColor}" 
                                                                       Stroke="{StaticResource IconColor}" StrokeThickness="1">
                                                                            <Path.RenderTransform>
                                                                                <RotateTransform Angle="180"/>
                                                                            </Path.RenderTransform>
                                                                        </Path>
                                                                        <Path Data="M0,0 z" HorizontalAlignment="Right" 
                                                                      VerticalAlignment="Center" RenderTransformOrigin="0.5,0.5" Stretch="Fill"
                                                                      Fill="{StaticResource IconColor}" Stroke="{StaticResource IconColor}" StrokeThickness="1" Visibility="Collapsed"/>
                                                                    </Grid>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Arrow3" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Grid Margin="0,2,0,2">
                                                                        <Line  X1="0" Y1="7" X2="130" Y2="7" Stroke="{StaticResource IconColor}" StrokeThickness="1" 
                                                                       Stretch="Fill"/>
                                                                        <Path  Data="M0.511,0L5.099,4.734L0.511,9.468L0,8.941L4.077,4.734L0,0.527z"
                                                                       HorizontalAlignment="Right" 
                                                                       VerticalAlignment="Center"
                                                                       RenderTransformOrigin="0.5,0.5" Stretch="Fill" Fill="{StaticResource IconColor}"
                                                                       Stroke="{StaticResource IconColor}" StrokeThickness="1">
                                                                            <Path.RenderTransform>
                                                                                <RotateTransform Angle="0"/>
                                                                            </Path.RenderTransform>
                                                                        </Path>
                                                                        <Path Data="M0,0 z" HorizontalAlignment="Right" 
                                                                      VerticalAlignment="Center" RenderTransformOrigin="0.5,0.5" Stretch="Fill"
                                                                      Fill="{StaticResource IconColor}" Stroke="{StaticResource IconColor}" StrokeThickness="1" Visibility="Collapsed"/>
                                                                    </Grid>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Arrow4" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Grid Margin="0,2,0,2">
                                                                        <Line  X1="0" Y1="7" X2="130" Y2="7" Stroke="{StaticResource IconColor}" StrokeThickness="1" 
                                                                       Stretch="Fill"/>
                                                                        <Path  Data="M0.511,0L5.099,4.734L0.511,9.468L0,8.941L4.077,4.734L0,0.527z"
                                                                       HorizontalAlignment="Left" 
                                                                       VerticalAlignment="Center"
                                                                       RenderTransformOrigin="0.5,0.5" Stretch="Fill" Fill="{StaticResource IconColor}" 
                                                                       Stroke="{StaticResource IconColor}" StrokeThickness="1">
                                                                            <Path.RenderTransform>
                                                                                <RotateTransform Angle="180"/>
                                                                            </Path.RenderTransform>
                                                                        </Path>
                                                                        <Path Data="M0.511,0L5.099,4.734L0.511,9.468L0,8.941L4.077,4.734L0,0.527z" 
                                                                      HorizontalAlignment="Right" VerticalAlignment="Center" Stretch="Fill"
                                                                      RenderTransformOrigin="0.5,0.5" Fill="{StaticResource IconColor}" Stroke="{StaticResource IconColor}" 
                                                                      StrokeThickness="1"/>
                                                                    </Grid>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Arrow5" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Grid Margin="0,2,0,2">
                                                                        <Line  X1="0" Y1="7" X2="130" Y2="7" Stroke="{StaticResource IconColor}" StrokeThickness="1" 
                                                                       Stretch="Fill"/>
                                                                        <Path  Data="M0,0L7.583,3.792L0,7.583z"
                                                                       HorizontalAlignment="Left" 
                                                                       VerticalAlignment="Center"
                                                                       RenderTransformOrigin="0.5,0.5" Stretch="Fill" Fill="{StaticResource IconColor}" 
                                                                       Stroke="{StaticResource IconColor}" StrokeThickness="1">
                                                                            <Path.RenderTransform>
                                                                                <RotateTransform Angle="180"/>
                                                                            </Path.RenderTransform>
                                                                        </Path>
                                                                        <Path Data="M0.511,0L5.099,4.734L0.511,9.468L0,8.941L4.077,4.734L0,0.527z" 
                                                                      HorizontalAlignment="Right" VerticalAlignment="Center" Stretch="Fill"
                                                                      RenderTransformOrigin="0.5,0.5" Fill="Transparent" Stroke="Transparent" 
                                                                      StrokeThickness="1" Visibility="Collapsed"/>
                                                                    </Grid>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Arrow6" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Grid Margin="0,2,0,2">
                                                                        <Line  X1="0" Y1="7" X2="130" Y2="7" Stroke="{StaticResource IconColor}" StrokeThickness="1" 
                                                                       Stretch="Fill"/>
                                                                        <Path  Data="M0,0L7.583,3.792L0,7.583z"
                                                                       HorizontalAlignment="Right" 
                                                                       VerticalAlignment="Center"
                                                                       RenderTransformOrigin="0.5,0.5" Stretch="Fill" Fill="{StaticResource IconColor}" 
                                                                       Stroke="{StaticResource IconColor}" StrokeThickness="1">
                                                                            <Path.RenderTransform>
                                                                                <RotateTransform Angle="0"/>
                                                                            </Path.RenderTransform>
                                                                        </Path>
                                                                        <Path Data="M0.511,0L5.099,4.734L0.511,9.468L0,8.941L4.077,4.734L0,0.527z" 
                                                                      HorizontalAlignment="Right" VerticalAlignment="Center" Stretch="Fill"
                                                                      RenderTransformOrigin="0.5,0.5" Fill="Transparent" Stroke="Transparent" 
                                                                      StrokeThickness="1" Visibility="Collapsed"/>
                                                                    </Grid>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Arrow7" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Grid Margin="0,2,0,2">
                                                                        <Line  X1="0" Y1="7" X2="130" Y2="7" Stroke="{StaticResource IconColor}" StrokeThickness="1" 
                                                                       Stretch="Fill"/>
                                                                        <Path  Data="M0,0L7.583,3.792L0,7.583z"
                                                                       HorizontalAlignment="Left" 
                                                                       VerticalAlignment="Center"
                                                                       RenderTransformOrigin="0.5,0.5" Stretch="Fill" Fill="{StaticResource IconColor}"
                                                                       Stroke="{StaticResource IconColor}" StrokeThickness="1">
                                                                            <Path.RenderTransform>
                                                                                <RotateTransform Angle="180"/>
                                                                            </Path.RenderTransform>
                                                                        </Path>
                                                                        <Path Data="M0,0L7.583,3.792L0,7.583z" 
                                                                      HorizontalAlignment="Right" VerticalAlignment="Center" Stretch="Fill"
                                                                      RenderTransformOrigin="0.5,0.5" Fill="{StaticResource IconColor}" Stroke="{StaticResource IconColor}" 
                                                                      StrokeThickness="1"/>
                                                                    </Grid>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Arrow8" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Grid Margin="0,2,0,2">
                                                                        <Line  X1="0" Y1="7" X2="130" Y2="7" Stroke="{StaticResource IconColor}" StrokeThickness="1" 
                                                                       Stretch="Fill"/>
                                                                        <Path  Data="M4.333,0L8.667,4.333L4.333,8.666L0,4.333z"
                                                                       HorizontalAlignment="Left" 
                                                                       VerticalAlignment="Center"
                                                                       RenderTransformOrigin="0.5,0.5" Stretch="Fill" Fill="{StaticResource IconColor}"
                                                                       Stroke="{StaticResource IconColor}" StrokeThickness="1">
                                                                            <Path.RenderTransform>
                                                                                <RotateTransform Angle="180"/>
                                                                            </Path.RenderTransform>
                                                                        </Path>
                                                                        <Path Data="M0,0L7.583,3.792L0,7.583z" 
                                                                      HorizontalAlignment="Right" VerticalAlignment="Center" Stretch="Fill"
                                                                      RenderTransformOrigin="0.5,0.5" Fill="Transparent" Stroke="Transparent" 
                                                                      StrokeThickness="1" Visibility="Collapsed"/>
                                                                    </Grid>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Arrow9" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Grid Margin="0,2,0,2">
                                                                        <Line  X1="0" Y1="7" X2="130" Y2="7" Stroke="{StaticResource IconColor}" StrokeThickness="1" 
                                                                       Stretch="Fill"/>
                                                                        <Path  Data="M4.333,0L8.667,4.333L4.333,8.666L0,4.333z"
                                                                       HorizontalAlignment="Right" 
                                                                       VerticalAlignment="Center"
                                                                       RenderTransformOrigin="0.5,0.5" Stretch="Fill" Fill="{StaticResource IconColor}"
                                                                       Stroke="{StaticResource IconColor}" StrokeThickness="1">
                                                                            <Path.RenderTransform>
                                                                                <RotateTransform Angle="0"/>
                                                                            </Path.RenderTransform>
                                                                        </Path>
                                                                        <Path Data="M0,0L7.583,3.792L0,7.583z" 
                                                                      HorizontalAlignment="Right" VerticalAlignment="Center" Stretch="Fill"
                                                                      RenderTransformOrigin="0.5,0.5" Fill="Transparent" Stroke="Transparent" 
                                                                      StrokeThickness="1" Visibility="Collapsed"/>
                                                                    </Grid>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                            <syncribbon:RibbonMenuItem x:Name="Arrow10" >
                                                                <syncribbon:RibbonMenuItem.Header>
                                                                    <Grid Margin="0,2,0,2">
                                                                        <Line  X1="0" Y1="7" X2="130" Y2="7" Stroke="{StaticResource IconColor}" StrokeThickness="1" 
                                                                       Stretch="Fill"/>
                                                                        <Path Data="M4.333,0L8.667,4.333L4.333,8.666L0,4.333z"
                                                                       HorizontalAlignment="Left" 
                                                                       VerticalAlignment="Center"
                                                                       RenderTransformOrigin="0.5,0.5" Stretch="Fill" Fill="{StaticResource IconColor}"
                                                                       Stroke="{StaticResource IconColor}" StrokeThickness="1">
                                                                            <Path.RenderTransform>
                                                                                <RotateTransform Angle="180"/>
                                                                            </Path.RenderTransform>
                                                                        </Path>
                                                                        <Path Data="M4.333,0L8.667,4.333L4.333,8.666L0,4.333z" 
                                                                      HorizontalAlignment="Right" VerticalAlignment="Center" Stretch="Fill"
                                                                      RenderTransformOrigin="0.5,0.5" Fill="{StaticResource IconColor}" Stroke="{StaticResource IconColor}" 
                                                                      StrokeThickness="1"/>
                                                                    </Grid>
                                                                </syncribbon:RibbonMenuItem.Header>
                                                            </syncribbon:RibbonMenuItem>
                                                        </syncribbon:RibbonMenuItem>
                                                    </StackPanel>
                                                </Border>
                                            </Grid>
                                        </StackPanel>
                                    </syncribbon:DropDownButton>
                                </syncribbon:RibbonBar>

                                <syncribbon:RibbonBar x:Name="Part_ArrangeBar"
                                                      Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Arrange}"                                                      
                                                      IsLauncherButtonVisible="False"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <syncribbon:RibbonBar.IconTemplate>
                                        <DataTemplate>
                                            <Grid>
                                                <Path Height="24" 
                                                      Width="22"
                                                      Stretch="None" 
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M3.5 11C3.77614 11 4 10.7761 4 10.5C4 10.2239 3.77614 10 3.5 10H2C1.44772 10 1 9.55229 1 9V2C1 1.44771 1.44771 1 2 1H9C9.55229 1 10 1.44771 10 2V4.5C10 4.77614 10.2239 5 10.5 5C10.7761 5 11 4.77614 11 4.5V2C11 0.895431 10.1046 0 9 0H2C0.895431 0 0 0.895431 0 2V9C0 10.1046 0.895431 11 2 11H3.5ZM18.5 13C18.2239 13 18 13.2239 18 13.5C18 13.7761 18.2239 14 18.5 14H20C20.5523 14 21 14.4477 21 15V22C21 22.5523 20.5523 23 20 23H13C12.4477 23 12 22.5523 12 22V19.5C12 19.2239 11.7761 19 11.5 19C11.2239 19 11 19.2239 11 19.5V22C11 23.1046 11.8954 24 13 24H20C21.1046 24 22 23.1046 22 22V15C22 13.8954 21.1046 13 20 13H18.5ZM7 6C5.89543 6 5 6.89543 5 8V16C5 17.1046 5.89543 18 7 18H15C16.1046 18 17 17.1046 17 16V8C17 6.89543 16.1046 6 15 6H7Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </DataTemplate>
                                    </syncribbon:RibbonBar.IconTemplate>
                                    <syncribbon:DropDownButton x:Name="Part_Align"
                                                               IconType="Icon"
                                                               SizeForm="Large"
                                                               Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Align}"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Top"
                                                               Width="42"
                                                               Height="Auto"
                                                               Margin="0,4,2,4"
                                                               ContextMenuService.Placement="Bottom"
                                                               syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                               Style="{StaticResource SyncfusionRibbonDropDownButtonStyle}"
                                                               Padding="{StaticResource Windows11Light.BorderThickness2}">
                                        <syncribbon:DropDownButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Align}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:DropDownButton.ToolTip>
                                        <syncribbon:DropDownButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="30" 
                                                          Width="30"
                                                          Stretch="Fill"                                                          
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M1.5 0.5C1.5 0.223858 1.27614 0 1 0C0.723858 0 0.5 0.223858 0.5 0.5V29.5C0.5 29.7761 0.723858 30 1 30C1.27614 30 1.5 29.7761 1.5 29.5V0.5ZM17.5 2H6.5C5.94772 2 5.5 2.44772 5.5 3V9C5.5 9.55229 5.94772 10 6.5 10H17.5C18.0523 10 18.5 9.55228 18.5 9V3C18.5 2.44772 18.0523 2 17.5 2ZM6.5 1C5.39543 1 4.5 1.89543 4.5 3V9C4.5 10.1046 5.39543 11 6.5 11H17.5C18.6046 11 19.5 10.1046 19.5 9V3C19.5 1.89543 18.6046 1 17.5 1H6.5ZM27.5 14H15.5C14.9477 14 14.5 14.4477 14.5 15V25C14.5 25.5523 14.9477 26 15.5 26H27.5C28.0523 26 28.5 25.5523 28.5 25V15C28.5 14.4477 28.0523 14 27.5 14ZM15.5 13C14.3954 13 13.5 13.8954 13.5 15V25C13.5 26.1046 14.3954 27 15.5 27H27.5C28.6046 27 29.5 26.1046 29.5 25V15C29.5 13.8954 28.6046 13 27.5 13H15.5ZM4.76179 20.498L6.90723 22.6435C7.10249 22.8387 7.10249 23.1553 6.90723 23.3506C6.71197 23.5459 6.39538 23.5459 6.20012 23.3506L3.20113 20.3516C3.00587 20.1563 3.00587 19.8398 3.20113 19.6445L6.20012 16.6455C6.39538 16.4502 6.71197 16.4502 6.90723 16.6455C7.10249 16.8408 7.10249 17.1574 6.90723 17.3526L4.76179 19.498H11.5C11.7761 19.498 12 19.7219 12 19.998C12 20.2742 11.7761 20.498 11.5 20.498H4.76179Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:DropDownButton.IconTemplate>
                                        <syncribbon:DropDownButton.Items>
                                            <syncribbon:RibbonButton x:Name="Part_AlignLeft"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignLeftScreenTipText}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.AlignLeft"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="15"
                                                                  Width="{StaticResource Windows11Light.TitleTextStyle}"
                                                                  Stretch="Fill"                                                                  
                                                                  HorizontalAlignment="Left"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M0.5 0C0.776142 0 1 0.223858 1 0.5V14.5C1 14.7761 0.776142 15 0.5 15C0.223858 15 0 14.7761 0 14.5V0.5C0 0.223858 0.223858 0 0.5 0ZM2 2C2 1.17157 2.67157 0.5 3.5 0.5H9.5C10.3284 0.5 11 1.17157 11 2V4C11 4.82843 10.3284 5.5 9.5 5.5H3.5C2.67157 5.5 2 4.82843 2 4V2ZM3.5 1.5C3.22386 1.5 3 1.72386 3 2V4C3 4.27614 3.22386 4.5 3.5 4.5H9.5C9.77614 4.5 10 4.27614 10 4V2C10 1.72386 9.77614 1.5 9.5 1.5H3.5ZM2 8C2 7.17157 2.67157 6.5 3.5 6.5H12.5C13.3284 6.5 14 7.17157 14 8V10C14 10.8284 13.3284 11.5 12.5 11.5H3.5C2.67157 11.5 2 10.8284 2 10V8ZM3.5 7.5C3.22386 7.5 3 7.72386 3 8V10C3 10.2761 3.22386 10.5 3.5 10.5H12.5C12.7761 10.5 13 10.2761 13 10V8C13 7.72386 12.7761 7.5 12.5 7.5H3.5ZM4.35355 12.1464C4.54882 12.3417 4.54882 12.6583 4.35355 12.8536L4.20711 13H12C12.2761 13 12.5 13.2239 12.5 13.5C12.5 13.7761 12.2761 14 12 14H4.20711L4.35355 14.1464C4.54882 14.3417 4.54882 14.6583 4.35355 14.8536C4.15829 15.0488 3.84171 15.0488 3.64645 14.8536L2.71716 13.9243C2.70467 13.9118 2.69285 13.8989 2.6817 13.8856C2.57072 13.7939 2.5 13.6552 2.5 13.5C2.5 13.3448 2.57072 13.2061 2.6817 13.1144C2.69285 13.1011 2.70467 13.0882 2.71716 13.0757L3.64645 12.1464C3.84171 11.9512 4.15829 11.9512 4.35355 12.1464Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_AlignCenter"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignCenterScreenTipText}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.AlignCenter"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="{StaticResource Windows11Light.SubHeaderTextStyle}" 
                                                                  Width="13" 
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Left" 
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M6.5 0C6.77614 0 7 0.223858 7 0.5V1.5H9.5C10.3284 1.5 11 2.17157 11 3V5C11 5.82843 10.3284 6.5 9.5 6.5H7V7.5H11.5C12.3284 7.5 13 8.17157 13 9V11C13 11.8284 12.3284 12.5 11.5 12.5H7V13.5C7 13.7761 6.77614 14 6.5 14C6.22386 14 6 13.7761 6 13.5V12.5H1.5C0.671573 12.5 0 11.8284 0 11V9C0 8.17157 0.671573 7.5 1.5 7.5H6V6.5H3.5C2.67157 6.5 2 5.82843 2 5V3C2 2.17157 2.67157 1.5 3.5 1.5H6V0.5C6 0.223858 6.22386 0 6.5 0ZM3.5 2.5C3.22386 2.5 3 2.72386 3 3V5C3 5.27614 3.22386 5.5 3.5 5.5H9.5C9.77614 5.5 10 5.27614 10 5V3C10 2.72386 9.77614 2.5 9.5 2.5H3.5ZM1.5 8.5C1.22386 8.5 1 8.72386 1 9V11C1 11.2761 1.22386 11.5 1.5 11.5H11.5C11.7761 11.5 12 11.2761 12 11V9C12 8.72386 11.7761 8.5 11.5 8.5H1.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_AlignRight"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignRightScreenTipText}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.AlignRight"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="15" 
                                                                  Width="{StaticResource Windows11Light.TitleTextStyle}"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Left"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M13.5 0C13.7761 0 14 0.223858 14 0.5L14 14.5C14 14.7761 13.7761 15 13.5 15C13.2239 15 13 14.7761 13 14.5L13 0.5C13 0.223858 13.2239 2.98023e-08 13.5 0ZM3 2C3 1.17157 3.67157 0.5 4.5 0.5H10.5C11.3284 0.5 12 1.17157 12 2V4C12 4.82843 11.3284 5.5 10.5 5.5L4.5 5.5C3.67157 5.5 3 4.82843 3 4L3 2ZM4.5 1.5C4.22386 1.5 4 1.72386 4 2L4 4C4 4.27614 4.22386 4.5 4.5 4.5L10.5 4.5C10.7761 4.5 11 4.27614 11 4V2C11 1.72386 10.7761 1.5 10.5 1.5L4.5 1.5ZM1.5 6.5L10.5 6.5C11.3284 6.5 12 7.17157 12 8V10C12 10.8284 11.3284 11.5 10.5 11.5L1.5 11.5C0.671573 11.5 2.38419e-07 10.8284 1.78814e-07 10L0 8C-5.96046e-08 7.17157 0.671572 6.5 1.5 6.5ZM10.5 7.5L1.5 7.5C1.22386 7.5 1 7.72386 1 8L1 10C1 10.2761 1.22386 10.5 1.5 10.5L10.5 10.5C10.7761 10.5 11 10.2761 11 10V8C11 7.72386 10.7761 7.5 10.5 7.5ZM9.64645 12.1464C9.84171 11.9512 10.1583 11.9512 10.3536 12.1464L11.2828 13.0757C11.2953 13.0882 11.3071 13.1011 11.3183 13.1144C11.4293 13.2061 11.5 13.3448 11.5 13.5C11.5 13.6552 11.4293 13.7939 11.3183 13.8856C11.3071 13.8989 11.2953 13.9118 11.2828 13.9243L10.3536 14.8536C10.1583 15.0488 9.84171 15.0488 9.64645 14.8536C9.45118 14.6583 9.45118 14.3417 9.64645 14.1464L9.79289 14L2 14C1.72386 14 1.5 13.7761 1.5 13.5C1.5 13.2239 1.72386 13 2 13L9.79289 13L9.64645 12.8536C9.45118 12.6583 9.45118 12.3417 9.64645 12.1464Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon_control:MenuItemSeparator Margin="-20,2,0,2"/>
                                            <syncribbon:RibbonButton x:Name="Part_AlignTop"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignTopScreenTipText}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.AlignTop"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="{StaticResource Windows11Light.TitleTextStyle}" 
                                                                  Width="15"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Left"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M14.5 1L0.5 1C0.223858 1 2.98023e-08 0.776144 0 0.5C-5.96046e-08 0.22386 0.223858 1.87755e-06 0.5 1.84774e-06L14.5 0C14.7761 -5.96046e-08 15 0.223858 15 0.5C15 0.776142 14.7761 1 14.5 1ZM8 2L10 2C10.8284 2 11.5 2.67157 11.5 3.5L11.5 12.5C11.5 13.3284 10.8284 14 10 14L8 14C7.17158 14 6.5 13.3284 6.5 12.5L6.5 3.5C6.5 2.67157 7.17157 2 8 2ZM10 3L8 3C7.72386 3 7.5 3.22386 7.5 3.5L7.5 12.5C7.5 12.7761 7.72386 13 8 13L10 13C10.2761 13 10.5 12.7761 10.5 12.5L10.5 3.5C10.5 3.22386 10.2761 3 10 3ZM2 2L4 2C4.82843 2 5.5 2.67157 5.5 3.5L5.5 9.5C5.5 10.3284 4.82843 11 4 11H2C1.17157 11 0.5 10.3284 0.5 9.5V3.5C0.5 2.67158 1.17157 2 2 2ZM4 3L2 3C1.72386 3 1.5 3.22386 1.5 3.5L1.5 9.5C1.5 9.77614 1.72386 10 2 10H4C4.27614 10 4.5 9.77614 4.5 9.5L4.5 3.5C4.5 3.22386 4.27614 3 4 3ZM13 4.20711L13 12C13 12.2761 13.2239 12.5 13.5 12.5C13.7761 12.5 14 12.2761 14 12L14 4.20711L14.1464 4.35355C14.3417 4.54882 14.6583 4.54882 14.8536 4.35355C15.0488 4.15829 15.0488 3.84171 14.8536 3.64645L13.9243 2.71716C13.9118 2.70467 13.8989 2.69285 13.8856 2.6817C13.7939 2.57072 13.6552 2.5 13.5 2.5C13.3448 2.5 13.2061 2.57072 13.1144 2.6817C13.1011 2.69285 13.0882 2.70467 13.0757 2.71716L12.1464 3.64645C11.9512 3.84171 11.9512 4.15829 12.1464 4.35356C12.3417 4.54882 12.6583 4.54882 12.8536 4.35355L13 4.20711Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_AlignMiddle"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignMiddleScreenTipText}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.AlignMiddle"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="13" 
                                                                  Width="{StaticResource Windows11Light.SubHeaderTextStyle}"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Left"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M11 1L9 1C8.72386 1 8.5 1.22386 8.5 1.5V6.49951V11.5C8.5 11.7761 8.72386 12 9 12H11C11.2761 12 11.5 11.7761 11.5 11.5L11.5 1.5C11.5 1.22386 11.2761 1 11 1ZM12.5 6V1.5C12.5 0.671573 11.8284 -5.96046e-08 11 0L9 8.9407e-08C8.17157 1.19209e-07 7.5 0.671573 7.5 1.5V6H6.5V3.5C6.5 2.67157 5.82843 2 5 2H3C2.17157 2 1.5 2.67157 1.5 3.5L1.5 6H0.5C0.223858 6 0 6.22386 0 6.5C0 6.77614 0.223858 7 0.5 7H1.5L1.5 9.5C1.5 10.3284 2.17157 11 3 11H5C5.82843 11 6.5 10.3284 6.5 9.5V7H7.5V11.5C7.5 12.3284 8.17157 13 9 13H11C11.8284 13 12.5 12.3284 12.5 11.5V7H13.5C13.7761 7 14 6.77614 14 6.5C14 6.22386 13.7761 6 13.5 6H12.5ZM5.5 3.5C5.5 3.22386 5.27614 3 5 3H3C2.72386 3 2.5 3.22386 2.5 3.5L2.5 9.5C2.5 9.77614 2.72386 10 3 10H5C5.27614 10 5.5 9.77614 5.5 9.5V3.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>                                                            
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_AlignBottom"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=AlignBottomScreenTipText}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.AlignBottom"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="{StaticResource Windows11Light.SubHeaderTextStyle}" 
                                                                  Width="15"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Left"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M8 1C7.72386 1 7.5 1.22386 7.5 1.5L7.5 10.5C7.5 10.7761 7.72386 11 8 11H10C10.2761 11 10.5 10.7761 10.5 10.5L10.5 1.5C10.5 1.22386 10.2761 1 10 1L8 1ZM6.5 1.5C6.5 0.671572 7.17158 -1.19209e-07 8 0L10 2.68221e-07C10.8284 3.57628e-07 11.5 0.671574 11.5 1.5L11.5 10.5C11.5 11.3284 10.8284 12 10 12H8C7.17157 12 6.5 11.3284 6.5 10.5L6.5 1.5ZM13.5 1.5C13.7761 1.5 14 1.72386 14 2L14 9.7929L14.1464 9.64645C14.3417 9.45119 14.6583 9.45119 14.8536 9.64645C15.0488 9.84171 15.0488 10.1583 14.8536 10.3536L13.9243 11.2828C13.9118 11.2953 13.8989 11.3072 13.8856 11.3183C13.7939 11.4293 13.6552 11.5 13.5 11.5C13.3448 11.5 13.2061 11.4293 13.1144 11.3183C13.1011 11.3071 13.0882 11.2953 13.0757 11.2828L12.1464 10.3536C11.9512 10.1583 11.9512 9.84171 12.1464 9.64645C12.3417 9.45119 12.6583 9.45119 12.8536 9.64645L13 9.7929L13 2C13 1.72386 13.2239 1.5 13.5 1.5ZM2 4C1.72386 4 1.5 4.22386 1.5 4.5L1.5 10.5C1.5 10.7761 1.72386 11 2 11H4C4.27614 11 4.5 10.7761 4.5 10.5L4.5 4.5C4.5 4.22386 4.27614 4 4 4L2 4ZM0.5 4.5C0.5 3.67157 1.17157 3 2 3L4 3C4.82843 3 5.5 3.67157 5.5 4.5L5.5 10.5C5.5 11.3284 4.82843 12 4 12H2C1.17157 12 0.5 11.3284 0.5 10.5V4.5ZM0 13.5C2.98023e-08 13.2238 0.223858 13 0.5 13L14.5 13C14.7761 13 15 13.2238 15 13.5C15 13.7761 14.7761 14 14.5 14L0.5 14C0.223858 14 -5.96046e-08 13.7761 0 13.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                        </syncribbon:DropDownButton.Items>
                                    </syncribbon:DropDownButton>
                                    <syncribbon:DropDownButton x:Name="Part_Position"
                                                               IconType="Icon"
                                                               SizeForm="Large"
                                                               Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Position}"
                                                               Height="Auto"
                                                               Margin="2,4,2,4"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Top"
                                                               ContextMenuService.Placement="Bottom"
                                                               syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                               Style="{StaticResource SyncfusionRibbonDropDownButtonStyle}"
                                                               Padding="{StaticResource Windows11Light.BorderThickness2}">
                                        <syncribbon:DropDownButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Position}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PositionText}" MaxWidth="200" TextWrapping="Wrap" TextAlignment="Justify"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:DropDownButton.ToolTip>
                                        <syncribbon:DropDownButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="27" 
                                                          Width="28"
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M12 0C12.2761 0 12.5 0.223858 12.5 0.5V6V7.5L18.5 7.5V6V0.5C18.5 0.223858 18.7239 0 19 0C19.2761 0 19.5 0.223858 19.5 0.5V3.99982C19.9178 3.68597 20.4372 3.5 21 3.5H25C26.3807 3.5 27.5 4.61929 27.5 6V10C27.5 11.3807 26.3807 12.5 25 12.5H21C19.6193 12.5 18.5 11.3807 18.5 10V8.5L12.5 8.5V10C12.5 11.3807 11.3807 12.5 10 12.5H9.00099H9V18H10.5C11.8807 18 13 19.1193 13 20.5V24.5C13 25.8807 11.8807 27 10.5 27H6.5C5.11929 27 4 25.8807 4 24.5V20.5C4 19.9372 4.18597 19.4178 4.49982 19H1C0.723858 19 0.5 18.7761 0.5 18.5C0.5 18.2239 0.723858 18 1 18L6.49741 18L6.5 18H8V12.5H6H1C0.723858 12.5 0.5 12.2761 0.5 12C0.5 11.7239 0.723858 11.5 1 11.5H3.99982C3.68597 11.0822 3.5 10.5628 3.5 10V6C3.5 4.61929 4.61929 3.5 6 3.5H10C10.5628 3.5 11.0822 3.68597 11.5 3.99982V0.5C11.5 0.223858 11.7239 0 12 0ZM19.5 8.5V10C19.5 10.8284 20.1716 11.5 21 11.5H25C25.8284 11.5 26.5 10.8284 26.5 10V6C26.5 5.17157 25.8284 4.5 25 4.5H21C20.1716 4.5 19.5 5.17157 19.5 6V8V8.5ZM6 11.5H8.5L9 11.5L10 11.5C10.8284 11.5 11.5 10.8284 11.5 10V8.5V6C11.5 5.17157 10.8284 4.5 10 4.5H6C5.17157 4.5 4.5 5.17157 4.5 6V10C4.5 10.8284 5.17157 11.5 6 11.5ZM8.5 19L9 19L9.0014 19H10.5C11.3284 19 12 19.6716 12 20.5V24.5C12 25.3284 11.3284 26 10.5 26H6.5C5.67157 26 5 25.3284 5 24.5V20.5C5 19.6722 5.67049 19.0011 6.498 19L6.5 19H8.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>                                                    
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:DropDownButton.IconTemplate>
                                        <syncribbon:DropDownButton.Items>
                                            <syncribbon:RibbonButton x:Name="Part_SpaceAcross"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SpaceAcross}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.SpaceAcross"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="13" 
                                                                  Width="15"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Left"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M5 1.5C5 0.671573 5.67157 0 6.5 0H8.5C9.32843 0 10 0.671573 10 1.5V6H11V4.5C11 3.67157 11.6716 3 12.5 3H13.5C14.3284 3 15 3.67157 15 4.5V8.5C15 9.32843 14.3284 10 13.5 10H12.5C11.6716 10 11 9.32843 11 8.5V7H10V11.5C10 12.3284 9.32843 13 8.5 13H6.5C5.67157 13 5 12.3284 5 11.5V7H4V8.5C4 9.32843 3.32843 10 2.5 10H1.5C0.671573 10 0 9.32843 0 8.5V4.5C0 3.67157 0.671573 3 1.5 3H2.5C3.32843 3 4 3.67157 4 4.5V6H5V1.5ZM6.5 1C6.22386 1 6 1.22386 6 1.5V11.5C6 11.7761 6.22386 12 6.5 12H8.5C8.77614 12 9 11.7761 9 11.5V1.5C9 1.22386 8.77614 1 8.5 1H6.5ZM1.5 4C1.22386 4 1 4.22386 1 4.5V8.5C1 8.77614 1.22386 9 1.5 9H2.5C2.77614 9 3 8.77614 3 8.5V4.5C3 4.22386 2.77614 4 2.5 4H1.5ZM12.5 4C12.2239 4 12 4.22386 12 4.5V8.5C12 8.77614 12.2239 9 12.5 9H13.5C13.7761 9 14 8.77614 14 8.5V4.5C14 4.22386 13.7761 4 13.5 4H12.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_SpaceDown"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SpaceDown}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.SpaceDown"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="15" 
                                                                  Width="13"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Left"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M8.5 1L4.5 1C4.22386 1 4 1.22386 4 1.5V2.5C4 2.77614 4.22386 3 4.5 3L8.5 3C8.77614 3 9 2.77614 9 2.5V1.5C9 1.22386 8.77614 1 8.5 1ZM4.5 1.78814e-07L8.5 0C9.32843 -5.96046e-08 10 0.671573 10 1.5V2.5C10 3.32843 9.32843 4 8.5 4H7V5H11.5C12.3284 5 13 5.67157 13 6.5V8.5C13 9.32843 12.3284 10 11.5 10H7V11H8.5C9.32843 11 10 11.6716 10 12.5V13.5C10 14.3284 9.32843 15 8.5 15H4.5C3.67157 15 3 14.3284 3 13.5V12.5C3 11.6716 3.67157 11 4.5 11H6V10H1.5C0.671573 10 1.19209e-07 9.32843 8.9407e-08 8.5L0 6.5C-5.96046e-08 5.67157 0.671573 5 1.5 5H6V4H4.5C3.67157 4 3 3.32843 3 2.5V1.5C3 0.671573 3.67157 2.08616e-07 4.5 1.78814e-07ZM11.5 6L1.5 6C1.22386 6 1 6.22386 1 6.5L1 8.5C1 8.77614 1.22386 9 1.5 9H11.5C11.7761 9 12 8.77614 12 8.5V6.5C12 6.22386 11.7761 6 11.5 6ZM4.5 12C4.22386 12 4 12.2239 4 12.5V13.5C4 13.7761 4.22386 14 4.5 14H8.5C8.77614 14 9 13.7761 9 13.5V12.5C9 12.2239 8.77614 12 8.5 12H4.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon_control:MenuItemSeparator Margin="-20,2,0,2"/>
                                            <syncribbon:RibbonButton x:Name="Part_FlipHorizontal"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=HorizontalFlip}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.Flip"
                                                                     CommandParameter="{StaticResource HorizontalFlipParameter}"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="{StaticResource Windows11Light.SubTitleTextStyle}" 
                                                                  Width="{StaticResource Windows11Light.SubHeaderTextStyle}"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Left"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M8.5 1L4.5 1C4.22386 1 4 1.22386 4 1.5V2.5C4 2.77614 4.22386 3 4.5 3L8.5 3C8.77614 3 9 2.77614 9 2.5V1.5C9 1.22386 8.77614 1 8.5 1ZM4.5 1.78814e-07L8.5 0C9.32843 -5.96046e-08 10 0.671573 10 1.5V2.5C10 3.32843 9.32843 4 8.5 4H7V5H11.5C12.3284 5 13 5.67157 13 6.5V8.5C13 9.32843 12.3284 10 11.5 10H7V11H8.5C9.32843 11 10 11.6716 10 12.5V13.5C10 14.3284 9.32843 15 8.5 15H4.5C3.67157 15 3 14.3284 3 13.5V12.5C3 11.6716 3.67157 11 4.5 11H6V10H1.5C0.671573 10 1.19209e-07 9.32843 8.9407e-08 8.5L0 6.5C-5.96046e-08 5.67157 0.671573 5 1.5 5H6V4H4.5C3.67157 4 3 3.32843 3 2.5V1.5C3 0.671573 3.67157 2.08616e-07 4.5 1.78814e-07ZM11.5 6L1.5 6C1.22386 6 1 6.22386 1 6.5L1 8.5C1 8.77614 1.22386 9 1.5 9H11.5C11.7761 9 12 8.77614 12 8.5V6.5C12 6.22386 11.7761 6 11.5 6ZM4.5 12C4.22386 12 4 12.2239 4 12.5V13.5C4 13.7761 4.22386 14 4.5 14H8.5C8.77614 14 9 13.7761 9 13.5V12.5C9 12.2239 8.77614 12 8.5 12H4.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>                                                            
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_FlipVertical"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=VerticalFlip}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.Flip"
                                                                     CommandParameter="{StaticResource VerticalFlipParameter}"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="{StaticResource Windows11Light.SubHeaderTextStyle}" 
                                                                  Width="{StaticResource Windows11Light.SubTitleTextStyle}"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Left"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M8.5 1L4.5 1C4.22386 1 4 1.22386 4 1.5V2.5C4 2.77614 4.22386 3 4.5 3L8.5 3C8.77614 3 9 2.77614 9 2.5V1.5C9 1.22386 8.77614 1 8.5 1ZM4.5 1.78814e-07L8.5 0C9.32843 -5.96046e-08 10 0.671573 10 1.5V2.5C10 3.32843 9.32843 4 8.5 4H7V5H11.5C12.3284 5 13 5.67157 13 6.5V8.5C13 9.32843 12.3284 10 11.5 10H7V11H8.5C9.32843 11 10 11.6716 10 12.5V13.5C10 14.3284 9.32843 15 8.5 15H4.5C3.67157 15 3 14.3284 3 13.5V12.5C3 11.6716 3.67157 11 4.5 11H6V10H1.5C0.671573 10 1.19209e-07 9.32843 8.9407e-08 8.5L0 6.5C-5.96046e-08 5.67157 0.671573 5 1.5 5H6V4H4.5C3.67157 4 3 3.32843 3 2.5V1.5C3 0.671573 3.67157 2.08616e-07 4.5 1.78814e-07ZM11.5 6L1.5 6C1.22386 6 1 6.22386 1 6.5L1 8.5C1 8.77614 1.22386 9 1.5 9H11.5C11.7761 9 12 8.77614 12 8.5V6.5C12 6.22386 11.7761 6 11.5 6ZM4.5 12C4.22386 12 4 12.2239 4 12.5V13.5C4 13.7761 4.22386 14 4.5 14H8.5C8.77614 14 9 13.7761 9 13.5V12.5C9 12.2239 8.77614 12 8.5 12H4.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon_control:MenuItemSeparator Margin="-20,2,0,2"/>
                                            <syncribbon:RibbonButton x:Name="Part_RotateClockwise"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RotateClockwise}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.Rotate"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="{StaticResource Windows11Light.SubTitleTextStyle}" 
                                                                  Width="{StaticResource Windows11Light.SubHeaderTextStyle}"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Left"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M7.08654 0.725465C7.08654 -0.0326649 8.17595 -0.281339 8.55054 0.391288L13.8205 9.85435C14.3649 10.8318 13.6053 12 12.4254 12H8.65899C7.79055 12 7.08654 11.3426 7.08654 10.5317V0.725465ZM8.13484 1.76652V10.5317C8.13484 10.802 8.36951 11.0212 8.65899 11.0212H12.4254C12.8187 11.0212 13.0719 10.6318 12.8905 10.3059L8.13484 1.76652ZM3.28886 0.493608C3.46152 0.276647 3.78984 0.231454 4.0222 0.392667L5.20814 1.21549C5.51084 1.4255 5.55215 1.83993 5.28394 2.09683L5.25415 2.06971L5.28394 2.09683L4.25894 3.07859C4.05686 3.27213 3.72501 3.27608 3.51773 3.08739C3.31045 2.89871 3.30623 2.58885 3.5083 2.39531L3.64887 2.26066C3.19252 2.31214 2.74802 2.41747 2.35829 2.6328C1.67944 3.00787 1.0483 3.79417 1.0483 5.63754C1.0483 5.90783 0.813634 6.12696 0.524152 6.12696C0.234671 6.12696 0 5.90783 0 5.63754C0 3.56554 0.731664 2.39416 1.82445 1.79039C2.37357 1.48699 2.98251 1.3486 3.55142 1.28551L3.39697 1.17835C3.16461 1.01714 3.11621 0.71057 3.28886 0.493608ZM4.36095 5.68188L1.52026 10.7828C1.45978 10.8914 1.54418 11.0212 1.67527 11.0212H4.18623C4.28273 11.0212 4.36095 10.9481 4.36095 10.858V5.68188ZM4.10352 4.02245C4.43761 3.42255 5.40925 3.64433 5.40925 4.3205V10.858C5.40925 11.4887 4.86169 12 4.18623 12H1.67527C0.7576 12 0.166818 11.0914 0.590189 10.3312L4.10352 4.02245Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>                                                            
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_RotateAntiClockwise"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RotateCounterClockwise}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.Rotate"
                                                                     CommandParameter="{StaticResource AntiClockwiseRotateParameter}"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="{StaticResource Windows11Light.SubTitleTextStyle}" 
                                                                  Width="{StaticResource Windows11Light.SubHeaderTextStyle}"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Left"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M5.86516 1.76652L1.10954 10.3059C0.928092 10.6318 1.18128 11.0212 1.57457 11.0212H5.34101C5.63049 11.0212 5.86516 10.802 5.86516 10.5317V1.76652ZM5.44946 0.391288C5.82405 -0.281338 6.91347 -0.0326657 6.91347 0.725465V10.5317C6.91347 11.3426 6.20945 12 5.34101 12H1.57457C0.394708 12 -0.364872 10.8318 0.179463 9.85435L5.44946 0.391288ZM10.7111 0.493608C10.8838 0.71057 10.8354 1.01714 10.603 1.17835L10.4486 1.28551C11.0175 1.3486 11.6264 1.48699 12.1756 1.79039C13.2683 2.39416 14 3.56554 14 5.63754C14 5.90783 13.7653 6.12696 13.4758 6.12696C13.1864 6.12696 12.9517 5.90783 12.9517 5.63754C12.9517 3.79417 12.3206 3.00787 11.6417 2.6328C11.252 2.41747 10.8075 2.31214 10.3511 2.26066L10.4917 2.39531C10.6938 2.58885 10.6896 2.89871 10.4823 3.08739C10.275 3.27608 9.94314 3.27213 9.74106 3.07859L8.71606 2.09682C8.44785 1.83993 8.48916 1.4255 8.79186 1.21549L9.9778 0.392667C10.2102 0.231454 10.5385 0.276647 10.7111 0.493608ZM8.59075 4.3205C8.59075 3.64433 9.56239 3.42255 9.89648 4.02245L13.4098 10.3312C13.8332 11.0914 13.2424 12 12.3247 12H9.81377C9.13831 12 8.59075 11.4887 8.59075 10.858V4.3205ZM9.63905 5.68188V10.858C9.63905 10.9481 9.71727 11.0212 9.81377 11.0212H12.3247C12.4558 11.0212 12.5402 10.8914 12.4797 10.7828L9.63905 5.68188Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                        </syncribbon:DropDownButton.Items>
                                    </syncribbon:DropDownButton>
                                    <syncribbon:SplitButton x:Name="Part_BringToFrontSplitButton"  
                                                            Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BringToFront}"
                                                            HorizontalAlignment="Left"
                                                            IconType="Icon"
                                                            SizeForm="Small"
                                                            Height="{StaticResource Windows11Light.MinHeight}"
                                                            Command="diag:DiagramCommands.BringToFront"
                                                            Style="{StaticResource SyncfusionSplitButtonStyle}">
                                        <syncribbon:SplitButton.Margin>
                                            <Thickness Left="0" Top="2" Right="2" Bottom="0"/>
                                        </syncribbon:SplitButton.Margin>
                                        <syncribbon:SplitButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BringToFront}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BringToFrontTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:SplitButton.ToolTip>
                                        <syncribbon:SplitButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="13" 
                                                          Width="13"
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M0 1.5C0 0.671573 0.671573 0 1.5 0H4.5C5.32843 0 6 0.671573 6 1.5V2H9.5C10.3284 2 11 2.67157 11 3.5V7H11.5C12.3284 7 13 7.67157 13 8.5V11.5C13 12.3284 12.3284 13 11.5 13H8.5C7.67157 13 7 12.3284 7 11.5V11H3.5C2.67157 11 2 10.3284 2 9.5V6H1.5C0.671573 6 0 5.32843 0 4.5V1.5ZM2 5V3.5C2 2.67157 2.67157 2 3.5 2H5V1.5C5 1.22386 4.77614 1 4.5 1H1.5C1.22386 1 1 1.22386 1 1.5V4.5C1 4.77614 1.22386 5 1.5 5H2ZM8 11V11.5C8 11.7761 8.22386 12 8.5 12H11.5C11.7761 12 12 11.7761 12 11.5V8.5C12 8.22386 11.7761 8 11.5 8H11V9.5C11 10.3284 10.3284 11 9.5 11H8ZM3.5 3C3.22386 3 3 3.22386 3 3.5V9.5C3 9.77614 3.22386 10 3.5 10H9.5C9.77614 10 10 9.77614 10 9.5V3.5C10 3.22386 9.77614 3 9.5 3H3.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:SplitButton.IconTemplate>
                                        <syncribbon:RibbonButton x:Name="Part_BringForward"
                                                                 Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BringForward}"
                                                                 IconType="Icon"
                                                                 SizeForm="Small"
                                                                 HorizontalAlignment="Stretch"
                                                                 Height="24"
                                                                 Padding="2,0,0,0"
                                                                 Command="diag:DiagramCommands.BringForward"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BringForward}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BringForwardTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="13" 
                                                              Width="13"
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M0 1.5C0 0.671573 0.671573 0 1.5 0H9.5C10.3284 0 11 0.671573 11 1.5V2H11.5C12.3284 2 13 2.67157 13 3.5V11.5C13 12.3284 12.3284 13 11.5 13H3.5C2.67157 13 2 12.3284 2 11.5V11H1.5C0.671573 11 0 10.3284 0 9.5V1.5ZM3 11V11.5C3 11.7761 3.22386 12 3.5 12H11.5C11.7761 12 12 11.7761 12 11.5V3.5C12 3.22386 11.7761 3 11.5 3H11V9.5C11 10.3284 10.3284 11 9.5 11H3ZM1.5 1C1.22386 1 1 1.22386 1 1.5V9.5C1 9.77614 1.22386 10 1.5 10H9.5C9.77614 10 10 9.77614 10 9.5V1.5C10 1.22386 9.77614 1 9.5 1H1.5Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>                                                        
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon:RibbonButton x:Name="Part_BringToFront"
                                                                 Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BringToFront}"
                                                                 IconType="Icon"
                                                                 SizeForm="Small"
                                                                 HorizontalAlignment="Stretch"
                                                                 Height="24"
                                                                 Padding="2,0,0,0"
                                                                 Command="diag:DiagramCommands.BringToFront"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BringToFront}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BringToFrontTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="13" 
                                                              Width="13"
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M0 1.5C0 0.671573 0.671573 0 1.5 0H4.5C5.32843 0 6 0.671573 6 1.5V2H9.5C10.3284 2 11 2.67157 11 3.5V7H11.5C12.3284 7 13 7.67157 13 8.5V11.5C13 12.3284 12.3284 13 11.5 13H8.5C7.67157 13 7 12.3284 7 11.5V11H3.5C2.67157 11 2 10.3284 2 9.5V6H1.5C0.671573 6 0 5.32843 0 4.5V1.5ZM2 5V3.5C2 2.67157 2.67157 2 3.5 2H5V1.5C5 1.22386 4.77614 1 4.5 1H1.5C1.22386 1 1 1.22386 1 1.5V4.5C1 4.77614 1.22386 5 1.5 5H2ZM8 11V11.5C8 11.7761 8.22386 12 8.5 12H11.5C11.7761 12 12 11.7761 12 11.5V8.5C12 8.22386 11.7761 8 11.5 8H11V9.5C11 10.3284 10.3284 11 9.5 11H8ZM3.5 3C3.22386 3 3 3.22386 3 3.5V9.5C3 9.77614 3.22386 10 3.5 10H9.5C9.77614 10 10 9.77614 10 9.5V3.5C10 3.22386 9.77614 3 9.5 3H3.5Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                    </syncribbon:SplitButton>
                                    <syncribbon:SplitButton x:Name="Part_SendToBackSplitButton"                                                            
                                                            Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SendToBack}"
                                                            HorizontalAlignment="Left"
                                                            IconType="Icon"
                                                            SizeForm="Small"
                                                            Height="24"
                                                            Command="diag:DiagramCommands.SendToBack"
                                                            Style="{StaticResource SyncfusionSplitButtonStyle}">
                                        <syncribbon:SplitButton.Margin>
                                            <Thickness Left="0" Top="2" Right="2" Bottom="0"/>
                                        </syncribbon:SplitButton.Margin>
                                        <syncribbon:SplitButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SendToBack}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SendToBackTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:SplitButton.ToolTip>
                                        <syncribbon:SplitButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="13" 
                                                          Width="13"
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Left" 
                                                          VerticalAlignment="Center"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M0 1.5C0 0.671573 0.671573 0 1.5 0H4.5C5.32843 0 6 0.671573 6 1.5V2H9.5C10.3284 2 11 2.67157 11 3.5V7H11.5C12.3284 7 13 7.67157 13 8.5V11.5C13 12.3284 12.3284 13 11.5 13H8.5C7.67157 13 7 12.3284 7 11.5V11H3.5C2.67157 11 2 10.3284 2 9.5V6H1.5C0.671573 6 0 5.32843 0 4.5V1.5ZM3 6V9.5C3 9.77614 3.22386 10 3.5 10H7V8.5C7 7.67157 7.67157 7 8.5 7H10V3.5C10 3.22386 9.77614 3 9.5 3H6V4.5C6 5.32843 5.32843 6 4.5 6H3ZM1.5 1C1.22386 1 1 1.22386 1 1.5V4.5C1 4.77614 1.22386 5 1.5 5H4.5C4.77614 5 5 4.77614 5 4.5V1.5C5 1.22386 4.77614 1 4.5 1H1.5ZM8.5 8C8.22386 8 8 8.22386 8 8.5V11.5C8 11.7761 8.22386 12 8.5 12H11.5C11.7761 12 12 11.7761 12 11.5V8.5C12 8.22386 11.7761 8 11.5 8H8.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:SplitButton.IconTemplate>
                                        <syncribbon:RibbonButton x:Name="Part_SendBackward"
                                                                 Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SendBackward}"
                                                                 IconType="Icon"
                                                                 SizeForm="Small"
                                                                 HorizontalAlignment="Stretch"
                                                                 Height="24"
                                                                 Padding="2,0,0,0"
                                                                 Command="diag:DiagramCommands.SendBackward"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SendBackward}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SendBackwardTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="13" 
                                                              Width="13"
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M1.5 0C0.671573 0 0 0.671573 0 1.5V9.5C0 10.3284 0.671573 11 1.5 11H4V11.5C4 12.3284 4.67157 13 5.5 13H11.5C12.3284 13 13 12.3284 13 11.5V5.5C13 4.67157 12.3284 4 11.5 4H11V1.5C11 0.671573 10.3284 0 9.5 0H1.5ZM10 4V1.5C10 1.22386 9.77614 1 9.5 1H1.5C1.22386 1 1 1.22386 1 1.5V9.5C1 9.77614 1.22386 10 1.5 10H4V5.5C4 4.67157 4.67157 4 5.5 4H10ZM5 5.5C5 5.22386 5.22386 5 5.5 5H10.5H11H11.5C11.7761 5 12 5.22386 12 5.5V11.5C12 11.7761 11.7761 12 11.5 12H5.5C5.22386 12 5 11.7761 5 11.5V5.5Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon:RibbonButton x:Name="Part_SendToBack"
                                                                 Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SendToBack}"
                                                                 IconType="Icon"
                                                                 SizeForm="Small"
                                                                 HorizontalAlignment="Stretch"
                                                                 Height="24"
                                                                 Padding="2,0,0,0"
                                                                 Command="diag:DiagramCommands.SendToBack"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SendToBack}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SendToBackTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="13" 
                                                              Width="13"
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M0 1.5C0 0.671573 0.671573 0 1.5 0H4.5C5.32843 0 6 0.671573 6 1.5V2H9.5C10.3284 2 11 2.67157 11 3.5V7H11.5C12.3284 7 13 7.67157 13 8.5V11.5C13 12.3284 12.3284 13 11.5 13H8.5C7.67157 13 7 12.3284 7 11.5V11H3.5C2.67157 11 2 10.3284 2 9.5V6H1.5C0.671573 6 0 5.32843 0 4.5V1.5ZM3 6V9.5C3 9.77614 3.22386 10 3.5 10H7V8.5C7 7.67157 7.67157 7 8.5 7H10V3.5C10 3.22386 9.77614 3 9.5 3H6V4.5C6 5.32843 5.32843 6 4.5 6H3ZM1.5 1C1.22386 1 1 1.22386 1 1.5V4.5C1 4.77614 1.22386 5 1.5 5H4.5C4.77614 5 5 4.77614 5 4.5V1.5C5 1.22386 4.77614 1 4.5 1H1.5ZM8.5 8C8.22386 8 8 8.22386 8 8.5V11.5C8 11.7761 8.22386 12 8.5 12H11.5C11.7761 12 12 11.7761 12 11.5V8.5C12 8.22386 11.7761 8 11.5 8H8.5Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>                                                        
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                    </syncribbon:SplitButton>
                                    <syncribbon:DropDownButton x:Name="Part_GroupSplitButton"
                                                               Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Group}"
                                                               IconType="Icon"
                                                               SizeForm="Small"
                                                               Height="{StaticResource SfDiagramRibbon.HomeRibbonTab.ArrangeBar.GroupButton.Static.Height}"
                                                               HorizontalAlignment="Left"
                                                               Style="{StaticResource SyncfusionRibbonDropDownButtonStyle}">
                                        <syncribbon:DropDownButton.Margin>
                                            <Thickness Left="0" Top="2" Right="2" Bottom="0"/>
                                        </syncribbon:DropDownButton.Margin>
                                        <syncribbon:DropDownButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=GroupObjects}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=GroupObjectsTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:DropDownButton.ToolTip>
                                        <syncribbon:DropDownButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="{StaticResource Windows11Light.SubHeaderTextStyle}" 
                                                          Width="{StaticResource Windows11Light.SubHeaderTextStyle}"
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M0 1C0 0.447715 0.447715 0 1 0C1.55228 0 2 0.447715 2 1C2 1.55228 1.55228 2 1 2C0.447715 2 0 1.55228 0 1ZM3 0.5C2.72386 0.5 2.5 0.723858 2.5 1C2.5 1.27614 2.72386 1.5 3 1.5H8C8.27614 1.5 8.5 1.72386 8.5 2V4.5H6C5.17157 4.5 4.5 5.17157 4.5 6V8.5H2C1.72386 8.5 1.5 8.27614 1.5 8V3C1.5 2.72386 1.27614 2.5 1 2.5C0.723858 2.5 0.5 2.72386 0.5 3V8C0.5 8.82843 1.17157 9.5 2 9.5H4.5V12C4.5 12.8284 5.17157 13.5 6 13.5H11C11.2761 13.5 11.5 13.2761 11.5 13C11.5 12.7239 11.2761 12.5 11 12.5H6C5.72386 12.5 5.5 12.2761 5.5 12V9.5H8C8.82843 9.5 9.5 8.82843 9.5 8V5.5H12C12.2761 5.5 12.5 5.72386 12.5 6V11C12.5 11.2761 12.7239 11.5 13 11.5C13.2761 11.5 13.5 11.2761 13.5 11V6C13.5 5.17157 12.8284 4.5 12 4.5H9.5V4V2C9.5 1.17157 8.82843 0.5 8 0.5H3ZM8.5 5.5H6C5.72386 5.5 5.5 5.72386 5.5 6V8.5H8C8.27614 8.5 8.5 8.27614 8.5 8V5.5ZM1 12C0.447715 12 0 12.4477 0 13C0 13.5523 0.447715 14 1 14C1.55228 14 2 13.5523 2 13C2 12.4477 1.55228 12 1 12ZM12 1C12 0.447715 12.4477 0 13 0C13.5523 0 14 0.447715 14 1C14 1.55228 13.5523 2 13 2C12.4477 2 12 1.55228 12 1ZM13 12C12.4477 12 12 12.4477 12 13C12 13.5523 12.4477 14 13 14C13.5523 14 14 13.5523 14 13C14 12.4477 13.5523 12 13 12Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:DropDownButton.IconTemplate>
                                        <syncribbon:RibbonButton x:Name="Part_Group"
                                                                 Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Group}"
                                                                 IconType="Icon"
                                                                 SizeForm="Small"
                                                                 HorizontalAlignment="Stretch"
                                                                 Height="24"
                                                                 Padding="2,0,0,0"
                                                                 Command="diag:DiagramCommands.Group"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Group}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=GroupObjectsTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="{StaticResource Windows11Light.SubHeaderTextStyle}" 
                                                              Width="{StaticResource Windows11Light.SubHeaderTextStyle}"
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M0 1C0 0.447715 0.447715 0 1 0C1.55228 0 2 0.447715 2 1C2 1.55228 1.55228 2 1 2C0.447715 2 0 1.55228 0 1ZM3 0.5C2.72386 0.5 2.5 0.723858 2.5 1C2.5 1.27614 2.72386 1.5 3 1.5H8C8.27614 1.5 8.5 1.72386 8.5 2V4.5H6C5.17157 4.5 4.5 5.17157 4.5 6V8.5H2C1.72386 8.5 1.5 8.27614 1.5 8V3C1.5 2.72386 1.27614 2.5 1 2.5C0.723858 2.5 0.5 2.72386 0.5 3V8C0.5 8.82843 1.17157 9.5 2 9.5H4.5V12C4.5 12.8284 5.17157 13.5 6 13.5H11C11.2761 13.5 11.5 13.2761 11.5 13C11.5 12.7239 11.2761 12.5 11 12.5H6C5.72386 12.5 5.5 12.2761 5.5 12V9.5H8C8.82843 9.5 9.5 8.82843 9.5 8V5.5H12C12.2761 5.5 12.5 5.72386 12.5 6V11C12.5 11.2761 12.7239 11.5 13 11.5C13.2761 11.5 13.5 11.2761 13.5 11V6C13.5 5.17157 12.8284 4.5 12 4.5H9.5V4V2C9.5 1.17157 8.82843 0.5 8 0.5H3ZM8.5 5.5H6C5.72386 5.5 5.5 5.72386 5.5 6V8.5H8C8.27614 8.5 8.5 8.27614 8.5 8V5.5ZM1 12C0.447715 12 0 12.4477 0 13C0 13.5523 0.447715 14 1 14C1.55228 14 2 13.5523 2 13C2 12.4477 1.55228 12 1 12ZM12 1C12 0.447715 12.4477 0 13 0C13.5523 0 14 0.447715 14 1C14 1.55228 13.5523 2 13 2C12.4477 2 12 1.55228 12 1ZM13 12C12.4477 12 12 12.4477 12 13C12 13.5523 12.4477 14 13 14C13.5523 14 14 13.5523 14 13C14 12.4477 13.5523 12 13 12Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                        <syncribbon:RibbonButton x:Name="Part_UnGroup"
                                                                 Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=UnGroup}"
                                                                 IconType="Icon"
                                                                 SizeForm="Small"
                                                                 HorizontalAlignment="Stretch"
                                                                 Height="24"
                                                                 Padding="2,0,0,0"
                                                                 Command="diag:DiagramCommands.UnGroup"
                                                                 Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                            <syncribbon:RibbonButton.ToolTip>
                                                <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=UnGroup}">
                                                    <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=UnGroupObjectsTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                                </syncribbon:ScreenTip>
                                            </syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:RibbonButton.IconTemplate>
                                                <DataTemplate>
                                                    <Grid>
                                                        <Path Height="{StaticResource Windows11Light.SubHeaderTextStyle}" 
                                                              Width="{StaticResource Windows11Light.SubHeaderTextStyle}"
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}">
                                                            <Path.Data>
                                                                <PathGeometry>M0 1C0 0.447715 0.447715 0 1 0C1.37245 0 1.69734 0.203615 1.86944 0.505604C1.91246 0.501894 1.95601 0.5 2 0.5H8C8.04399 0.5 8.08754 0.501894 8.13056 0.505604C8.30266 0.203615 8.62755 0 9 0C9.55228 0 10 0.447715 10 1C10 1.37245 9.79638 1.69734 9.4944 1.86944C9.49811 1.91246 9.5 1.95601 9.5 2V4.5H12C12.044 4.5 12.0875 4.50189 12.1306 4.5056C12.3027 4.20362 12.6276 4 13 4C13.5523 4 14 4.44772 14 5C14 5.37245 13.7964 5.69734 13.4944 5.86944C13.4981 5.91246 13.5 5.95601 13.5 6V9V10V12C13.5 12.044 13.4981 12.0875 13.4944 12.1306C13.7964 12.3027 14 12.6276 14 13C14 13.5523 13.5523 14 13 14C12.6276 14 12.3027 13.7964 12.1306 13.4944C12.0875 13.4981 12.044 13.5 12 13.5H6C5.95601 13.5 5.91246 13.4981 5.86944 13.4944C5.69734 13.7964 5.37245 14 5 14C4.44772 14 4 13.5523 4 13C4 12.6276 4.20362 12.3027 4.5056 12.1306C4.50189 12.0875 4.5 12.044 4.5 12V9.5H2C1.95601 9.5 1.91246 9.49811 1.86944 9.4944C1.69734 9.79638 1.37245 10 1 10C0.447715 10 0 9.55228 0 9C0 8.62755 0.203615 8.30266 0.505604 8.13056C0.501894 8.08754 0.5 8.04399 0.5 8V2C0.5 1.95601 0.501894 1.91246 0.505604 1.86944C0.203615 1.69734 0 1.37245 0 1ZM4.5 8.5V6C4.5 5.95601 4.50189 5.91246 4.5056 5.86944C4.20362 5.69734 4 5.37245 4 5C4 4.44772 4.44772 4 5 4C5.37245 4 5.69734 4.20362 5.86944 4.5056C5.91246 4.50189 5.95601 4.5 6 4.5H8.5V2C8.5 1.9489 8.49234 1.8996 8.47809 1.85317C8.34324 1.7705 8.2295 1.65676 8.14683 1.52191C8.1004 1.50766 8.0511 1.5 8 1.5H2C1.9489 1.5 1.8996 1.50766 1.85317 1.52191C1.7705 1.65676 1.65676 1.7705 1.52191 1.85317C1.50766 1.8996 1.5 1.9489 1.5 2V8C1.5 8.0511 1.50766 8.1004 1.52191 8.14683C1.65676 8.2295 1.7705 8.34324 1.85317 8.47809C1.8996 8.49234 1.9489 8.5 2 8.5H4.5ZM5.52191 5.85317C5.65676 5.7705 5.7705 5.65676 5.85317 5.52191C5.8996 5.50766 5.9489 5.5 6 5.5H12C12.0511 5.5 12.1004 5.50766 12.1468 5.52191C12.2295 5.65676 12.3432 5.7705 12.4781 5.85317C12.4923 5.8996 12.5 5.9489 12.5 6V12C12.5 12.0511 12.4923 12.1004 12.4781 12.1468C12.3432 12.2295 12.2295 12.3432 12.1468 12.4781C12.1004 12.4923 12.0511 12.5 12 12.5H6C5.9489 12.5 5.8996 12.4923 5.85317 12.4781C5.7705 12.3432 5.65676 12.2295 5.52191 12.1468C5.50766 12.1004 5.5 12.0511 5.5 12V6C5.5 5.9489 5.50766 5.8996 5.52191 5.85317Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </DataTemplate>
                                            </syncribbon:RibbonButton.IconTemplate>
                                        </syncribbon:RibbonButton>
                                    </syncribbon:DropDownButton>
                                </syncribbon:RibbonBar>

                                <syncribbon:RibbonBar x:Name="Part_EditingBar"
                                                      Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Editing}"
                                                      IsLauncherButtonVisible="False"
                                                      IsPanelStateCollapsed="False"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <syncribbon:DropDownButton x:Name="Part_Select"
                                                               IconType="Icon"
                                                               SizeForm="Large"
                                                               Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Select}"
                                                               Width="42"
                                                               Height="Auto"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Top"
                                                               Style="{StaticResource SyncfusionRibbonDropDownButtonStyle}"
                                                               Padding="{StaticResource Windows11Light.BorderThickness2}">
                                        <syncribbon:DropDownButton.Margin>
                                            <Thickness Left="0" Top="4" Right="0" Bottom="4"/>
                                        </syncribbon:DropDownButton.Margin>
                                        <syncribbon:DropDownButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Select}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SelectText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:DropDownButton.ToolTip>
                                        <syncribbon:DropDownButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="25" 
                                                          Width="21"
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M7.21274 16.9894C7.47377 17.0501 7.69524 17.2249 7.81808 17.4672L10.913 23.5716C11.1133 23.9666 11.6567 24.1851 12.092 24.0172L15.4542 22.7202C15.8167 22.5803 15.9311 22.2426 15.7698 21.9245L12.8978 16.2598C12.7576 15.9832 12.7632 15.6534 12.9128 15.3819C13.0623 15.1104 13.3358 14.9336 13.6407 14.9114L18.9619 14.5233C19.3391 14.4958 19.5126 14.2887 19.5683 14.0983C19.6247 13.9054 19.5905 13.6219 19.2826 13.3746L4.06104 1.14758C3.77069 0.914351 3.42398 0.880809 3.15623 0.974253C2.89512 1.06538 2.76297 1.2467 2.74677 1.46263L1.411 19.2619C1.39133 19.5241 1.54055 19.8072 1.87685 19.9832C2.21282 20.1591 2.57027 20.136 2.81643 19.9474L6.46374 17.1522C6.67769 16.9882 6.95172 16.9286 7.21274 16.9894ZM19.0268 15.4469C20.5194 15.338 21.0285 13.5977 19.8456 12.6476L4.62399 0.420545C3.53938 -0.450686 1.93664 0.10868 1.84032 1.39206L0.504556 19.1913C0.39723 20.6215 2.21059 21.5705 3.36302 20.6873L7.01033 17.892L10.1052 23.9964C10.5215 24.8174 11.5551 25.2144 12.414 24.8831L15.7762 23.5861C16.635 23.2548 16.9938 22.3207 16.5776 21.4997L13.7056 15.835L19.0268 15.4469Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>                                                    
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:DropDownButton.IconTemplate>
                                        <syncribbon:DropDownButton.Items>
                                            <syncribbon:RibbonButton x:Name="Part_SelectAll"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SelectAll}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.SelectAll"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="15" 
                                                                  Width="15"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Center" 
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M1 1.5C1 1.22386 1.22386 1 1.5 1H2.5V0H1.5C0.671573 0 0 0.671573 0 1.5V2.5H1V1.5ZM4.5 1H6.5V0H4.5V1ZM8.5 1H10.5V0H8.5V1ZM12.5 1H13.5C13.7761 1 14 1.22386 14 1.5V2.5H15V1.5C15 0.671573 14.3284 0 13.5 0H12.5V1ZM14 4.5V6.5H15V4.5H14ZM1 6.5V4.5H0V6.5H1ZM14 8.5V10.5H15V8.5H14ZM1 10.5V8.5H0V10.5H1ZM1 13.5V12.5H0V13.5C0 14.3284 0.671573 15 1.5 15H2.5V14H1.5C1.22386 14 1 13.7761 1 13.5ZM14 12.5V13.5C14 13.7761 13.7761 14 13.5 14H12.5V15H13.5C14.3284 15 15 14.3284 15 13.5V12.5H14ZM6.5 14H4.5V15H5.5H6H6.5V14ZM10.5 14H8.5V15H10.5V14ZM8.81661 8.65063L9.62539 10.5H10.5L7.88558 4.5H7.12382L4.5 10.5H5.3558L6.15517 8.65063H8.81661ZM7.7163 5.93096C7.76019 6.04812 7.79154 6.12901 7.81035 6.17364L8.5627 7.98117H6.4373L7.19906 6.17364C7.22414 6.10669 7.25235 6.0258 7.2837 5.93096C7.32132 5.83612 7.3558 5.73291 7.38715 5.62134C7.42477 5.50418 7.45925 5.38982 7.4906 5.27824C7.51567 5.34519 7.54702 5.44282 7.58464 5.57113C7.62853 5.69386 7.67241 5.81381 7.7163 5.93096Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_SelectNode"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SelectNodes}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.SelectByType"
                                                                     CommandParameter="{x:Type diag:NodeViewModel}"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="15" 
                                                                  Width="15"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Center" 
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M0 1.5C0 0.671573 0.671573 0 1.5 0H3.5C4.32843 0 5 0.671573 5 1.5V3.5C5 4.32843 4.32843 5 3.5 5H3V7H7V6.5C7 5.67157 7.67157 5 8.5 5H13.5C14.3284 5 15 5.67157 15 6.5V8.5C15 9.32843 14.3284 10 13.5 10H8.5C7.67157 10 7 9.32843 7 8.5V8H3V10H3.5C4.32843 10 5 10.6716 5 11.5V13.5C5 14.3284 4.32843 15 3.5 15H1.5C0.671573 15 0 14.3284 0 13.5V11.5C0 10.6716 0.671573 10 1.5 10H2V5H1.5C0.671573 5 0 4.32843 0 3.5V1.5ZM1.5 1C1.22386 1 1 1.22386 1 1.5V3.5C1 3.77614 1.22386 4 1.5 4H3.5C3.77614 4 4 3.77614 4 3.5V1.5C4 1.22386 3.77614 1 3.5 1H1.5ZM8.5 6C8.22386 6 8 6.22386 8 6.5V8.5C8 8.77614 8.22386 9 8.5 9H13.5C13.7761 9 14 8.77614 14 8.5V6.5C14 6.22386 13.7761 6 13.5 6H8.5ZM1.5 11C1.22386 11 1 11.2239 1 11.5V13.5C1 13.7761 1.22386 14 1.5 14H3.5C3.77614 14 4 13.7761 4 13.5V11.5C4 11.2239 3.77614 11 3.5 11H1.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_SelectConnector"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SelectConnectors}"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Command="diag:DiagramCommands.SelectByType"
                                                                     CommandParameter="{x:Type diag:ConnectorViewModel}"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="15" 
                                                                  Width="11"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Center" 
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M2.80484 0.146447C3.0037 0.341709 3.0037 0.658291 2.80484 0.853553L1.63729 2H4.48159C5.32527 2 6.0092 2.67157 6.0092 3.5V11.5C6.0092 11.7761 6.23718 12 6.51841 12H9.36271L8.19516 10.8536C7.9963 10.6583 7.9963 10.3417 8.19516 10.1464C8.39402 9.95118 8.71643 9.95118 8.91528 10.1464L10.7793 11.9767C11.0736 12.2657 11.0736 12.7343 10.7793 13.0233L8.91528 14.8536C8.71643 15.0488 8.39402 15.0488 8.19516 14.8536C7.9963 14.6583 7.9963 14.3417 8.19516 14.1464L9.36271 13H6.51841C5.67473 13 4.9908 12.3284 4.9908 11.5V3.5C4.9908 3.22386 4.76282 3 4.48159 3H1.63729L2.80484 4.14645C3.0037 4.34171 3.0037 4.65829 2.80484 4.85355C2.60598 5.04882 2.28357 5.04882 2.08472 4.85355L0.22073 3.02326C-0.0735767 2.73427 -0.073577 2.26573 0.220731 1.97674L2.08472 0.146447C2.28357 -0.0488155 2.60598 -0.0488155 2.80484 0.146447Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                        </syncribbon:DropDownButton.Items>
                                    </syncribbon:DropDownButton>
                                </syncribbon:RibbonBar>

                            </syncribbon:RibbonTab>

                            <syncribbon:RibbonTab x:Name="Part_InsertRibbonTab" Caption="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Insert}">

                                <syncribbon:RibbonBar IsLauncherButtonVisible="False"
                                                      Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=DiagramParts}"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <syncribbon:RibbonButton x:Name="Part_Picture" 
                                                             Width="Auto"
                                                             Height="Auto"
                                                             Margin ="0,4,2,4"
                                                             Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Picture}"
                                                             SizeForm="Large"
                                                             IconType="Icon"
                                                             HorizontalAlignment="Center"
                                                             VerticalAlignment="Top"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}"
                                                             Padding="{StaticResource Windows11Light.BorderThickness2}">
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Data="M3.5 1H21.4705C22.575 1 23.4705 1.89543 23.4705 3V20.9705C23.4705 20.9861 23.4703 21.0017 23.4699 21.0172L15.1152 12.6625C13.7484 11.2957 11.5324 11.2957 10.1656 12.6624L1.52703 21.3001C1.50925 21.1929 1.5 21.0827 1.5 20.9705V3C1.5 1.89543 2.39543 1 3.5 1ZM14.4081 13.3696L23.1282 22.0897C22.7687 22.6211 22.1604 22.9705 21.4705 22.9705H3.5C2.88939 22.9705 2.34268 22.6968 1.97583 22.2655L10.8726 13.3695C11.8489 12.3933 13.4318 12.3933 14.4081 13.3696ZM0.5 3C0.5 1.34315 1.84315 0 3.5 0H21.4705C23.1273 0 24.4705 1.34315 24.4705 3V20.9705C24.4705 22.6273 23.1273 23.9705 21.4705 23.9705H3.5C1.84315 23.9705 0.5 22.6273 0.5 20.9705V3ZM27.1493 6.53711C27.1493 6.26097 26.9254 6.03711 26.6493 6.03711C26.3732 6.03711 26.1493 6.26097 26.1493 6.53711V23.1493C26.1493 24.53 25.03 25.6493 23.6493 25.6493H7.03711C6.76097 25.6493 6.53711 25.8732 6.53711 26.1493C6.53711 26.4254 6.76097 26.6493 7.03711 26.6493H23.6493C25.5823 26.6493 27.1493 25.0823 27.1493 23.1493V6.53711ZM17.933 7.62584C19.1365 7.62584 20.1122 6.65021 20.1122 5.44671C20.1122 4.24321 19.1365 3.26758 17.933 3.26758C16.7295 3.26758 15.7539 4.24321 15.7539 5.44671C15.7539 6.65021 16.7295 7.62584 17.933 7.62584Z" 
                                                          Height="27"
                                                          Width="28"
                                                          Stretch="Fill" 
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Fill="{StaticResource IconColor}"/>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Pictures}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PicturesTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                    </syncribbon:RibbonButton>
                                    <syncribbon:RibbonButton x:Name="Part_Connector" 
                                                             Margin="2,4,0,4"
                                                             Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Connector}"
                                                             SizeForm="Large"                                                             
                                                             Width="Auto"
                                                             Height="Auto"
                                                             HorizontalAlignment="Center"
                                                             VerticalAlignment="Top"
                                                             IconType="Icon"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}"
                                                             Padding="{StaticResource Windows11Light.BorderThickness2}">
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Connectors}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ConnectorTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Data="M20.6494 0C20.9256 0 21.1494 0.223858 21.1494 0.5V3H23.6494C23.9256 3 24.1494 3.22386 24.1494 3.5C24.1494 3.77614 23.9256 4 23.6494 4H21.1494V6.5C21.1494 6.77614 20.9256 7 20.6494 7C20.3733 7 20.1494 6.77614 20.1494 6.5V4H17.6494C17.3733 4 17.1494 3.77614 17.1494 3.5C17.1494 3.22386 17.3733 3 17.6494 3H20.1494V0.5C20.1494 0.223858 20.3733 0 20.6494 0ZM0.149414 19.5C0.149414 18.1193 1.2687 17 2.64941 17H5.14941V16.5C5.14941 14.567 6.71642 13 8.64941 13H15.6494H16.6494H17.6494C19.0301 13 20.1494 11.8807 20.1494 10.5V9.5C20.1494 9.22386 20.3733 9 20.6494 9C20.9256 9 21.1494 9.22386 21.1494 9.5V10.5C21.1494 12.433 19.5824 14 17.6494 14H16.6494H15.6494H8.64941C7.2687 14 6.14941 15.1193 6.14941 16.5V17H7.64941C9.03013 17 10.1494 18.1193 10.1494 19.5V24.5C10.1494 25.8807 9.03013 27 7.64941 27H2.64941C1.2687 27 0.149414 25.8807 0.149414 24.5V19.5ZM2.64941 18C1.82099 18 1.14941 18.6716 1.14941 19.5V24.5C1.14941 25.3284 1.82099 26 2.64941 26H7.64941C8.47784 26 9.14941 25.3284 9.14941 24.5V19.5C9.14941 18.6716 8.47784 18 7.64941 18H2.64941Z" 
                                                          Height="27" 
                                                          Width="25"
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Fill="{StaticResource IconColor}"/>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                </syncribbon:RibbonBar>

                            </syncribbon:RibbonTab>

                            <syncribbon:RibbonTab x:Name="Part_DesignRibbontab" Caption="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Design}">

                                <syncribbon:RibbonBar x:Name="Part_PageSetup"
                                                      Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PageSetup}"
                                                      IsLauncherButtonVisible ="False"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <syncribbon:RibbonBar.IconTemplate>
                                        <DataTemplate>
                                            <Grid>
                                                <Path Height="29" 
                                                      Width="29"
                                                      Stretch="Fill"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center" 
                                                      Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M0.149414 20.5C0.149414 21.8807 1.2687 23 2.64941 23H6.64941V26.5C6.64941 27.6046 7.54484 28.5 8.64941 28.5H18.6494H22.1494H22.9479H23.1494V28.4898C23.5312 28.4512 23.8954 28.3032 24.1972 28.0617L27.8988 25.1005C28.3732 24.7209 28.6494 24.1463 28.6494 23.5388V20.5V14.5C28.6494 13.3954 27.754 12.5 26.6494 12.5L17.1494 12.5V6.20156C17.1494 5.63388 16.9562 5.08311 16.6016 4.63982L13.6403 0.938263C13.1659 0.345226 12.4476 0 11.6882 0H8.64941H2.64941C1.2687 0 0.149414 1.11929 0.149414 2.5V20.5ZM23.1494 27.4795C23.3032 27.4478 23.4484 27.3802 23.5725 27.2809L27.2741 24.3196C27.3843 24.2314 27.4732 24.1222 27.5367 24H23.6494C23.3733 24 23.1494 24.2239 23.1494 24.5V27.4795ZM27.6494 23V20.5V14.5C27.6494 13.9477 27.2017 13.5 26.6494 13.5L8.64941 13.5C8.09713 13.5 7.64941 13.9477 7.64941 14.5L7.64941 26.5C7.64941 27.0523 8.09713 27.5 8.64941 27.5H18.6494H22.1494V24.5C22.1494 23.6716 22.821 23 23.6494 23H27.6494ZM16.1494 12.5V7H12.6494C11.821 7 11.1494 6.32843 11.1494 5.5V1H8.64941H2.64941C1.82099 1 1.14941 1.67157 1.14941 2.5V20.5C1.14941 21.3284 1.82099 22 2.64941 22H6.64941L6.64941 14.5C6.64941 13.3954 7.54484 12.5 8.64941 12.5H16.1494ZM15.8207 5.26452C15.9912 5.4776 16.0995 5.73208 16.1358 6H12.6494C12.3733 6 12.1494 5.77614 12.1494 5.5V1.07267C12.4259 1.16202 12.6738 1.33086 12.8595 1.56296L15.8207 5.26452ZM24.9318 10.2167L26.9657 8.18284C27.2177 7.93086 27.0392 7.5 26.6828 7.5H25.1494C25.1494 3.91015 22.2393 1 18.6494 1H18.1494C17.8733 1 17.6494 1.22386 17.6494 1.5C17.6494 1.77614 17.8733 2 18.1494 2H18.6494C21.687 2 24.1494 4.46243 24.1494 7.5H22.6151C22.2587 7.5 22.0803 7.93086 22.3323 8.18284L24.3661 10.2167C24.5223 10.3729 24.7756 10.3729 24.9318 10.2167Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </DataTemplate>
                                    </syncribbon:RibbonBar.IconTemplate>
                                    <syncribbon:DropDownButton x:Name="Part_Orientation"
                                                               Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Orientation}"
                                                               IconType="Icon"
                                                               Height="Auto"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Top"
                                                               SizeForm="Large"
                                                               Margin="0,4,2,4"
                                                               Style="{StaticResource SyncfusionRibbonDropDownButtonStyle}"
                                                               Padding="{StaticResource Windows11Light.BorderThickness2}">
                                        <syncribbon:DropDownButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PageOrientation}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PageOrientationTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:DropDownButton.ToolTip>
                                        <syncribbon:DropDownButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="29" 
                                                          Width="29"
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center" 
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M0.149414 20.5C0.149414 21.8807 1.2687 23 2.64941 23H6.64941V26.5C6.64941 27.6046 7.54484 28.5 8.64941 28.5H18.6494H22.1494H22.9479H23.1494V28.4898C23.5312 28.4512 23.8954 28.3032 24.1972 28.0617L27.8988 25.1005C28.3732 24.7209 28.6494 24.1463 28.6494 23.5388V20.5V14.5C28.6494 13.3954 27.754 12.5 26.6494 12.5L17.1494 12.5V6.20156C17.1494 5.63388 16.9562 5.08311 16.6016 4.63982L13.6403 0.938263C13.1659 0.345226 12.4476 0 11.6882 0H8.64941H2.64941C1.2687 0 0.149414 1.11929 0.149414 2.5V20.5ZM23.1494 27.4795C23.3032 27.4478 23.4484 27.3802 23.5725 27.2809L27.2741 24.3196C27.3843 24.2314 27.4732 24.1222 27.5367 24H23.6494C23.3733 24 23.1494 24.2239 23.1494 24.5V27.4795ZM27.6494 23V20.5V14.5C27.6494 13.9477 27.2017 13.5 26.6494 13.5L8.64941 13.5C8.09713 13.5 7.64941 13.9477 7.64941 14.5L7.64941 26.5C7.64941 27.0523 8.09713 27.5 8.64941 27.5H18.6494H22.1494V24.5C22.1494 23.6716 22.821 23 23.6494 23H27.6494ZM16.1494 12.5V7H12.6494C11.821 7 11.1494 6.32843 11.1494 5.5V1H8.64941H2.64941C1.82099 1 1.14941 1.67157 1.14941 2.5V20.5C1.14941 21.3284 1.82099 22 2.64941 22H6.64941L6.64941 14.5C6.64941 13.3954 7.54484 12.5 8.64941 12.5H16.1494ZM15.8207 5.26452C15.9912 5.4776 16.0995 5.73208 16.1358 6H12.6494C12.3733 6 12.1494 5.77614 12.1494 5.5V1.07267C12.4259 1.16202 12.6738 1.33086 12.8595 1.56296L15.8207 5.26452ZM24.9318 10.2167L26.9657 8.18284C27.2177 7.93086 27.0392 7.5 26.6828 7.5H25.1494C25.1494 3.91015 22.2393 1 18.6494 1H18.1494C17.8733 1 17.6494 1.22386 17.6494 1.5C17.6494 1.77614 17.8733 2 18.1494 2H18.6494C21.687 2 24.1494 4.46243 24.1494 7.5H22.6151C22.2587 7.5 22.0803 7.93086 22.3323 8.18284L24.3661 10.2167C24.5223 10.3729 24.7756 10.3729 24.9318 10.2167Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:DropDownButton.IconTemplate>
                                        <syncribbon:DropDownButton.Items>
                                            <syncribbon:RibbonButton x:Name="Part_Landscape"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Landscape}"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     IsSelected="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.PageSettings.PageOrientation, Converter={StaticResource LandscapeOrientationToBoolConverter}, Mode=TwoWay}"
                                                                     IconType="Icon"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="11"
                                                                  Width="15"
                                                                  Stretch="None" 
                                                                  HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M1.5 1C1.22386 1 1 1.22386 1 1.5L1 9.5C1 9.77614 1.22386 10 1.5 10H13.5C13.7761 10 14 9.77614 14 9.5L14 5H11.5C10.6716 5 10 4.32842 10 3.5V0.999997L1.5 1ZM11 1.2328V3.5C11 3.77614 11.2239 4 11.5 4H13.5827L11 1.2328ZM8.9407e-08 1.5C1.19209e-07 0.671573 0.671573 0 1.5 0L10.5654 1.19209e-07C10.9812 1.19209e-07 11.3783 0.17257 11.662 0.476522L14.5966 3.62069C14.8558 3.89844 15 4.26423 15 4.64416V9.5C15 10.3284 14.3284 11 13.5 11H1.5C0.671573 11 0 10.3284 0 9.5L8.9407e-08 1.5ZM2 3.5C2 3.22386 2.22386 3 2.5 3L5.5 3C5.77614 3 6 3.22386 6 3.5C6 3.77614 5.77614 4 5.5 4H2.5C2.22386 4 2 3.77614 2 3.5ZM2 5.5C2 5.22386 2.22386 5 2.5 5H8C8.27614 5 8.5 5.22386 8.5 5.5C8.5 5.77614 8.27614 6 8 6H2.5C2.22386 6 2 5.77614 2 5.5ZM2 7.5C2 7.22386 2.22386 7 2.5 7H11.2965C11.5726 7 11.7965 7.22386 11.7965 7.5C11.7965 7.77614 11.5726 8 11.2965 8H2.5C2.22386 8 2 7.77614 2 7.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_Portrait"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Portrait}"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     HorizontalAlignment="Stretch"
                                                                     IsSelected="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.PageSettings.PageOrientation, Converter={StaticResource PortraitOrientationToBoolConverter}, Mode=TwoWay}"
                                                                     IconType="Icon"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Stretch="None" 
                                                                  Width="11"
                                                                  Height="15"
                                                                  VerticalAlignment="Center" 
                                                                  HorizontalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M1.5 1C1.22386 1 1 1.22386 1 1.5L1 13.5C1 13.7761 1.22386 14 1.5 14H9.5C9.77614 14 10 13.7761 10 13.5L10 5H7.49999C6.67157 5 6 4.32843 6 3.5V1L1.5 1ZM7 1.41727V3.5C7 3.77614 7.22385 4 7.49999 4L9.7672 4L7 1.41727ZM4.47035e-07 1.5C4.76837e-07 0.671573 0.671573 0 1.5 0L6.35584 1.19209e-07C6.73578 1.49012e-07 7.10156 0.144178 7.37931 0.403417L10.5235 3.33797C10.8274 3.62166 11 4.01878 11 4.43455V13.5C11 14.3284 10.3284 15 9.5 15H1.5C0.671573 15 -5.96046e-08 14.3284 0 13.5L4.47035e-07 1.5ZM2 5.5C2 5.22386 2.22386 5 2.5 5H5C5.27614 5 5.5 5.22386 5.5 5.5C5.5 5.77614 5.27614 6 5 6H2.5C2.22386 6 2 5.77614 2 5.5ZM2 8.5C2 8.22386 2.22386 8 2.5 8H8.5C8.77614 8 9 8.22386 9 8.5C9 8.77614 8.77614 9 8.5 9H2.5C2.22386 9 2 8.77614 2 8.5ZM2 11.3C2 11.0238 2.22386 10.8 2.5 10.8L8.5 10.8C8.77614 10.8 9 11.0238 9 11.3C9 11.5761 8.77614 11.8 8.5 11.8L2.5 11.8C2.22386 11.8 2 11.5761 2 11.3Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                        </syncribbon:DropDownButton.Items>
                                    </syncribbon:DropDownButton>

                                    <syncribbon:DropDownButton x:Name="Part_PageSize"
                                                               Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Size}"
                                                               IconType="Icon"
                                                               SizeForm="Large"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Top"
                                                               Width="42"
                                                               Height="Auto"
                                                               syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                               Margin="2,4,0,4"
                                                               Style="{StaticResource SyncfusionRibbonDropDownButtonStyle}">
                                        <syncribbon:DropDownButton.Padding>
                                            <Thickness Left="2" Top="2" Right="2" Bottom="2"/>
                                        </syncribbon:DropDownButton.Padding>
                                        <syncribbon:DropDownButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PageSize}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PageSizeTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:DropDownButton.ToolTip>
                                        <syncribbon:DropDownButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Stretch="Fill" 
                                                          Width="23"
                                                          Height="28"
                                                          VerticalAlignment="Center" 
                                                          HorizontalAlignment="Center"
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M6.64941 4.5C6.64941 4.77614 6.87327 5 7.14941 5C7.42556 5 7.64941 4.77614 7.64941 4.5V3H21.6494V4.5C21.6494 4.77614 21.8733 5 22.1494 5C22.4256 5 22.6494 4.77614 22.6494 4.5V3V2V0.5C22.6494 0.223857 22.4256 0 22.1494 0C21.8733 0 21.6494 0.223857 21.6494 0.5V2H7.64941V0.5C7.64941 0.223857 7.42556 0 7.14941 0C6.87327 0 6.64941 0.223857 6.64941 0.5V2V3V4.5ZM2.64941 27L2.64941 7H1.14941C0.873272 7 0.649414 6.77614 0.649414 6.5C0.649414 6.22386 0.873272 6 1.14941 6H2.64941H3.64941H5.14941C5.42556 6 5.64941 6.22386 5.64941 6.5C5.64941 6.77614 5.42556 7 5.14941 7H3.64941L3.64941 27H5.14941C5.42556 27 5.64941 27.2239 5.64941 27.5C5.64941 27.7761 5.42556 28 5.14941 28H3.64941H2.64941H1.14941C0.873272 28 0.649414 27.7761 0.649414 27.5C0.649414 27.2239 0.873272 27 1.14941 27H2.64941ZM7.64941 26V8C7.64941 7.44771 8.09713 7 8.64941 7H14.6494H17.1494V11C17.1494 11.8284 17.821 12.5 18.6494 12.5H21.6494V16V26C21.6494 26.5523 21.2017 27 20.6494 27H8.64941C8.09713 27 7.64941 26.5523 7.64941 26ZM22.6392 11.5C22.6006 11.1182 22.4526 10.754 22.2112 10.4522L19.2499 6.75061C18.8704 6.27618 18.2957 6 17.6882 6H14.6494H8.64941C7.54484 6 6.64941 6.89543 6.64941 8V26C6.64941 27.1046 7.54484 28 8.64941 28H20.6494C21.754 28 22.6494 27.1046 22.6494 26V16V12.5V11.7016V11.5H22.6392ZM21.6289 11.5H18.6494C18.3733 11.5 18.1494 11.2761 18.1494 11V7.11273C18.2716 7.17624 18.3808 7.26507 18.469 7.37531L21.4303 11.0769C21.5296 11.201 21.5972 11.3462 21.6289 11.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>                                                    
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:DropDownButton.IconTemplate>
                                        <syncribbon:DropDownButton.Items>
                                            <syncribbon:RibbonButton x:Name="Part_Letter"
                                                                     IconType="Icon"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Letter}"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Stretch="Fill" 
                                                                  Width="{StaticResource Windows11Light.BodyTextStyle}"
                                                                  Height="14"
                                                                  VerticalAlignment="Center" 
                                                                  HorizontalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M10.5 1H1.5C1.22386 1 1 1.22386 1 1.5V12.5C1 12.7761 1.22386 13 1.5 13H10.5C10.7761 13 11 12.7761 11 12.5V1.5C11 1.22386 10.7761 1 10.5 1ZM1.5 0C0.671573 0 0 0.671573 0 1.5V12.5C0 13.3284 0.671573 14 1.5 14H10.5C11.3284 14 12 13.3284 12 12.5V1.5C12 0.671573 11.3284 0 10.5 0H1.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_Tabloid"
                                                                     IconType="Icon"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Tabloid}"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>                                                        
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Data="M12.5 1H1.5C1.22386 1 1 1.22386 1 1.5V14.5C1 14.7761 1.22386 15 1.5 15H12.5C12.7761 15 13 14.7761 13 14.5V1.5C13 1.22386 12.7761 1 12.5 1ZM1.5 0C0.671573 0 0 0.671573 0 1.5V14.5C0 15.3284 0.671573 16 1.5 16H12.5C13.3284 16 14 15.3284 14 14.5V1.5C14 0.671573 13.3284 0 12.5 0H1.5Z"
                                                              Width="14" 
                                                              Height="16" 
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center" 
                                                              Fill="{StaticResource IconColor}"/>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_Legal"
                                                                     IconType="Icon"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Legal}"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Width="{StaticResource Windows11Light.TitleTextStyle}"
                                                                  Height="15"
                                                                  Stretch="Fill" 
                                                                  VerticalAlignment="Center" 
                                                                  HorizontalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M12.5 1H1.5C1.22386 1 1 1.22386 1 1.5V13.5C1 13.7761 1.22386 14 1.5 14H12.5C12.7761 14 13 13.7761 13 13.5V1.5C13 1.22386 12.7761 1 12.5 1ZM1.5 0C0.671573 0 0 0.671573 0 1.5V13.5C0 14.3284 0.671573 15 1.5 15H12.5C13.3284 15 14 14.3284 14 13.5V1.5C14 0.671573 13.3284 0 12.5 0H1.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>                                                            
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_Statement"                                                                     
                                                                     IconType="Icon"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Statement}"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Data="M8.5 1H1.5C1.22386 1 1 1.22386 1 1.5V14.5C1 14.7761 1.22386 15 1.5 15H8.5C8.77614 15 9 14.7761 9 14.5V1.5C9 1.22386 8.77614 1 8.5 1ZM1.5 0C0.671573 0 0 0.671573 0 1.5V14.5C0 15.3284 0.671573 16 1.5 16H8.5C9.32843 16 10 15.3284 10 14.5V1.5C10 0.671573 9.32843 0 8.5 0H1.5Z" 
                                                              Width="10" 
                                                              Height="16"
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}"/>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_Executive"
                                                                     IconType="Icon"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Executive}"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Data="M10.5 1H1.5C1.22386 1 1 1.22386 1 1.5V14.5C1 14.7761 1.22386 15 1.5 15H10.5C10.7761 15 11 14.7761 11 14.5V1.5C11 1.22386 10.7761 1 10.5 1ZM1.5 0C0.671573 0 0 0.671573 0 1.5V14.5C0 15.3284 0.671573 16 1.5 16H10.5C11.3284 16 12 15.3284 12 14.5V1.5C12 0.671573 11.3284 0 10.5 0H1.5Z"
                                                              Height="16" 
                                                              Width="12"
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Fill="{StaticResource IconColor}"/>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_A3"
                                                                     IconType="Icon"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=A3}"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Data="M11.5 1H1.5C1.22386 1 1 1.22386 1 1.5V14.5C1 14.7761 1.22386 15 1.5 15H11.5C11.7761 15 12 14.7761 12 14.5V1.5C12 1.22386 11.7761 1 11.5 1ZM1.5 0C0.671573 0 0 0.671573 0 1.5V14.5C0 15.3284 0.671573 16 1.5 16H11.5C12.3284 16 13 15.3284 13 14.5V1.5C13 0.671573 12.3284 0 11.5 0H1.5Z"
                                                              Height="16" 
                                                              Width="13"
                                                              Stretch="Fill"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"                                                                          
                                                              Fill="{StaticResource IconColor}" />
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_A4"
                                                                     IconType="VectorImage"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=A4}"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Width="15"
                                                                  Height="16"
                                                                  Stretch="Fill" 
                                                                  HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M13.5 1H1.5C1.22386 1 1 1.22386 1 1.5V14.5C1 14.7761 1.22386 15 1.5 15H13.5C13.7761 15 14 14.7761 14 14.5V1.5C14 1.22386 13.7761 1 13.5 1ZM1.5 0C0.671573 0 0 0.671573 0 1.5V14.5C0 15.3284 0.671573 16 1.5 16H13.5C14.3284 16 15 15.3284 15 14.5V1.5C15 0.671573 14.3284 0 13.5 0H1.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_A5"
                                                                     IconType="VectorImage"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=A5}"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Width="13"
                                                                  Height="15"
                                                                  Stretch="Fill" 
                                                                  HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center"
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M11.5 1H1.5C1.22386 1 1 1.22386 1 1.5V13.5C1 13.7761 1.22386 14 1.5 14H11.5C11.7761 14 12 13.7761 12 13.5V1.5C12 1.22386 11.7761 1 11.5 1ZM1.5 0C0.671573 0 0 0.671573 0 1.5V13.5C0 14.3284 0.671573 15 1.5 15H11.5C12.3284 15 13 14.3284 13 13.5V1.5C13 0.671573 12.3284 0 11.5 0H1.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                        </syncribbon:DropDownButton.Items>
                                    </syncribbon:DropDownButton>
                                </syncribbon:RibbonBar>

                                <syncribbon:RibbonBar Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Themes}"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}"
                                                      IsLauncherButtonVisible ="False"
                                                      IsPanelStateCollapsed="False">
                                    <syncribbon:RibbonGallery x:Name="ThemeStyle" 
                                                              VisualMode="InRibbon"
                                                              ExpandHeight="450"  
                                                              Width="550" 
                                                              ItemWidth="75" 
                                                              ItemHeight="60" 
                                                              Margin="0,4,0,0"
                                                              SelectedItem="{Binding ElementName=Office_ThemeRibbon}"
                                                              Style="{StaticResource SyncfusionRibbonGalleryStyle}">
                                        <syncribbon:RibbonGallery.GalleryFilters>
                                            <syncribbon:RibbonGalleryFilter Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=All}"/>
                                            <syncribbon:RibbonGalleryFilter Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Professional}"/>
                                            <syncribbon:RibbonGalleryFilter Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Modern}"/>
                                            <syncribbon:RibbonGalleryFilter Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Trendy}"/>
                                            <syncribbon:RibbonGalleryFilter Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Custom}"/>
                                        </syncribbon:RibbonGallery.GalleryFilters>
                                        <syncribbon:RibbonGallery.GalleryGroups>
                                            <syncribbon:RibbonGalleryGroup Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Professional}" syncribbon:RibbonGallery.FilterIndexes="0,1" Style="{StaticResource SyncfusionRibbonGalleryGroupStyle}">
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Office_ThemeRibbon" ThemeName="Office_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=OfficeTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Linear_ThemeRibbon" ThemeName="Linear_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=LinearTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Zephyr_ThemeRibbon" ThemeName="Zephyr_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ZephyrTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Integral_ThemeRibbon" ThemeName="Integral_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                        <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IntegralTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Simple_ThemeRibbon" ThemeName="Zephyr_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SimpleTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Whisp_ThemeRibbon" ThemeName="Whisp_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=WhispTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Daybreak_ThemeRibbon" ThemeName="Daybreak_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=DaybreakTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Parallel_ThemeRibbon" ThemeName="Parallel_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ParallelTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Sequence_ThemeRibbon" ThemeName="Sequence_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SequenceTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Slice_ThemeRibbon" ThemeName="Slice_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SliceTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Ion_ThemeRibbon" ThemeName="Ion_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=IonTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                            </syncribbon:RibbonGalleryGroup>
                                            <syncribbon:RibbonGalleryGroup Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Modern}" syncribbon:RibbonGallery.FilterIndexes="0,2" Style="{StaticResource SyncfusionRibbonGalleryGroupStyle}">
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Retrospect_ThemeRibbon" ThemeName="Retrospect_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RetrospectTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Bubble_ThemeRibbon" ThemeName="Bubble_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BubbleTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Clouds_ThemeRibbon" ThemeName="Clouds_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=CloudsTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Gemstone_ThemeRibbon" ThemeName="Gemstone_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=GemstoneTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                            </syncribbon:RibbonGalleryGroup>
                                            <syncribbon:RibbonGalleryGroup Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Trendy}" syncribbon:RibbonGallery.FilterIndexes="0,3" Style="{StaticResource SyncfusionRibbonGalleryGroupStyle}">
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Facet_ThemeRibbon" ThemeName="Facet_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FacetTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Prominence_ThemeRibbon" ThemeName="Prominence_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ProminenceTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Smoke_ThemeRibbon" ThemeName="Smoke_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SmokeTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Radiance_ThemeRibbon" ThemeName="Radiance_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RadianceTheme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                            </syncribbon:RibbonGalleryGroup>
                                            <syncribbon:RibbonGalleryGroup Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Custom}" syncribbon:RibbonGallery.FilterIndexes="0,4" Style="{StaticResource SyncfusionRibbonGalleryGroupStyle}">
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Custom1_ThemeRibbon" ThemeName="Custom1_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Custom1Theme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                                <diagribbon:DiagramThemeGalleryItem x:Name ="Custom2_ThemeRibbon" ThemeName="Custom2_ThemeRibbon" Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                                    <diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                        <syncribbon:ScreenTip Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Custom2Theme}"/>
                                                    </diagribbon:DiagramThemeGalleryItem.ToolTip>
                                                </diagribbon:DiagramThemeGalleryItem>
                                            </syncribbon:RibbonGalleryGroup>
                                        </syncribbon:RibbonGallery.GalleryGroups>
                                        <syncribbon:RibbonGallery.MenuItems>
                                            <syncribbon:RibbonCheckBox x:Name="newItemThemeStyleDecision" IsChecked="True"  Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ApplyThemesShapes}" Style="{StaticResource SyncfusionRibbonCheckBoxStyle}"/>
                                        </syncribbon:RibbonGallery.MenuItems>
                                    </syncribbon:RibbonGallery>
                                </syncribbon:RibbonBar>
                                <syncribbon:RibbonBar Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Variants}" 
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}"
                                                      IsLauncherButtonVisible ="False"
                                                      IsPanelStateCollapsed="False">
                                    <syncribbon:RibbonGallery x:Name="VariantRibbonGallery"
                                                              MaxWidth="350"
                                                              Width="auto"
                                                              VisualMode="InRibbon"
                                                              ItemWidth="75"
                                                              ItemHeight="60"
                                                              Margin="0,4,0,0"
                                                              SelectedItem="{Binding ElementName=VariantGallery0}"
                                                              Style="{StaticResource SyncfusionRibbonGalleryStyle}">
                                        <diagribbon:DiagramThemeVariantGalleryItem x:Name ="VariantGallery0" 
                                                                                   Theme="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme}" 
                                                                                   ContentTemplate ="{ StaticResource ribbonVariantItemTemplate}"
                                                                                   Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.VariantStyles[0]}"
                                                                                   ConnectorVariant="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorVariantStyles[0]}"
                                                                                   Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                        </diagribbon:DiagramThemeVariantGalleryItem>
                                        <diagribbon:DiagramThemeVariantGalleryItem x:Name ="VariantGallery1" 
                                                                                   Theme="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme}"
                                                                                   ContentTemplate ="{ StaticResource ribbonVariantItemTemplate}"
                                                                                   Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.VariantStyles[1]}"
                                                                                   ConnectorVariant="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorVariantStyles[1]}"
                                                                                   Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                        </diagribbon:DiagramThemeVariantGalleryItem>
                                        <diagribbon:DiagramThemeVariantGalleryItem x:Name ="VariantGallery2" 
                                                                                   Theme="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme}"
                                                                                   ContentTemplate ="{ StaticResource ribbonVariantItemTemplate}"
                                                                                   Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.VariantStyles[2]}"
                                                                                   ConnectorVariant="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorVariantStyles[2]}"
                                                                                   Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                        </diagribbon:DiagramThemeVariantGalleryItem>
                                        <diagribbon:DiagramThemeVariantGalleryItem x:Name ="VariantGallery3" 
                                                                                   Theme="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme}" 
                                                                                   ContentTemplate ="{ StaticResource ribbonVariantItemTemplate}"
                                                                                   Content="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.VariantStyles[3]}"
                                                                                   ConnectorVariant="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Theme.ConnectorVariantStyles[3]}"
                                                                                   Style="{StaticResource SyncfusionRibbonGalleryItemStyle}" Padding="{StaticResource Windows11Light.BorderThickness}">
                                        </diagribbon:DiagramThemeVariantGalleryItem>
                                    </syncribbon:RibbonGallery>
                                </syncribbon:RibbonBar>

                                <syncribbon:RibbonBar x:Name="Part_Backgrounds"
                                                      Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Background}"                                                       
                                                      IsLauncherButtonVisible="False"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <syncribbon:RibbonBar.IconTemplate>
                                        <DataTemplate>
                                            <Grid>
                                                <Path Height="23"
                                                      Width="25"
                                                      Stretch="Fill"
                                                      HorizontalAlignment="Center" 
                                                      VerticalAlignment="Center" 
                                                      Fill="{StaticResource IconColor}"
                                                      Data="M8.64941 20.5C8.64941 21.3284 9.32099 22 10.1494 22H22.1494C22.9778 22 23.6494 21.3284 23.6494 20.5V10.5V7H20.1494C19.321 7 18.6494 6.32843 18.6494 5.5V1H16.1494H10.1494C9.32099 1 8.64941 1.67157 8.64941 2.5V3C8.64941 3.27614 8.42556 3.5 8.14941 3.5C7.87327 3.5 7.64941 3.27614 7.64941 3V2.5C7.64941 1.11929 8.7687 0 10.1494 0H16.1494H19.1882C19.9476 0 20.6659 0.345226 21.1403 0.938263L24.1016 4.63982C24.4562 5.08311 24.6494 5.63388 24.6494 6.20156V10.5V20.5C24.6494 21.8807 23.5301 23 22.1494 23H10.1494C8.7687 23 7.64941 21.8807 7.64941 20.5V20C7.64941 19.7239 7.87327 19.5 8.14941 19.5C8.42556 19.5 8.64941 19.7239 8.64941 20V20.5ZM23.3207 5.26452C23.4912 5.4776 23.5995 5.73208 23.6358 6H20.1494C19.8733 6 19.6494 5.77614 19.6494 5.5V1.07267C19.9259 1.16202 20.1738 1.33086 20.3595 1.56296L23.3207 5.26452ZM3.80736 8.17039C6.68624 5.27592 11.4077 5.27632 14.2886 8.17289C17.2517 11.152 17.35 15.5901 15.0039 17.979C13.6459 19.3416 11.4066 19.3406 10.0478 17.9744L8.81473 16.7347C8.55414 16.4662 8.24314 16.2524 7.89981 16.1059C7.55226 15.9576 7.17863 15.8811 6.80115 15.881C6.42367 15.8809 6.04998 15.9573 5.70234 16.1055C5.35847 16.2522 5.04701 16.4663 4.78616 16.7353C4.28168 17.2461 3.98176 17.8819 3.96539 18.6156C3.9596 18.6536 3.94408 18.6751 3.94053 18.6786C3.89916 18.7202 3.86838 18.7239 3.85648 18.724C3.84192 18.7241 3.82561 18.719 3.80984 18.7032C0.929882 15.8076 0.92949 11.0638 3.80736 8.17039ZM14.9977 7.4677C11.726 4.17831 6.36845 4.17739 3.09834 7.4652C-0.167761 10.749 -0.166858 16.123 3.10083 19.4084L3.38055 19.1302L3.10083 19.4084C3.52052 19.8303 4.1995 19.8363 4.64955 19.3838C4.83864 19.1937 4.94115 18.9397 4.96298 18.6912C4.96406 18.6789 4.96468 18.6665 4.96485 18.6542C4.97105 18.1931 5.15077 17.7886 5.49895 17.4367L5.49897 17.4368L5.50289 17.4327C5.67158 17.2583 5.87283 17.12 6.09463 17.0254C6.31827 16.93 6.55846 16.881 6.80095 16.881C7.04344 16.8811 7.28363 16.9302 7.50727 17.0256C7.72891 17.1202 7.93003 17.2585 8.09867 17.4327L8.09864 17.4328L8.10343 17.4376L9.33874 18.6796C11.0884 20.4387 13.9643 20.4403 15.7136 18.6835L15.716 18.6812C18.5176 15.8302 18.2728 10.7605 14.9977 7.4677ZM9.64941 8.63477C9.64941 9.18705 9.2017 9.63477 8.64941 9.63477C8.09713 9.63477 7.64941 9.18705 7.64941 8.63477C7.64941 8.08248 8.09713 7.63477 8.64941 7.63477C9.2017 7.63477 9.64941 8.08248 9.64941 8.63477ZM5.14941 11.5C5.7017 11.5 6.14941 11.0523 6.14941 10.5C6.14941 9.94771 5.7017 9.5 5.14941 9.5C4.59713 9.5 4.14941 9.94771 4.14941 10.5C4.14941 11.0523 4.59713 11.5 5.14941 11.5ZM13.4326 10.459C13.4326 11.0113 12.9849 11.459 12.4326 11.459C11.8803 11.459 11.4326 11.0113 11.4326 10.459C11.4326 9.9067 11.8803 9.45898 12.4326 9.45898C12.9849 9.45898 13.4326 9.9067 13.4326 10.459ZM13.6494 15.5C14.2017 15.5 14.6494 15.0523 14.6494 14.5C14.6494 13.9477 14.2017 13.5 13.6494 13.5C13.0971 13.5 12.6494 13.9477 12.6494 14.5C12.6494 15.0523 13.0971 15.5 13.6494 15.5Z">
                                                </Path>
                                            </Grid>
                                        </DataTemplate>
                                    </syncribbon:RibbonBar.IconTemplate>
                                    <syncribbon:DropDownButton x:Name="Part_Background"
                                                               Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Background}"
                                                               SizeForm="Large"
                                                               IconType="Icon"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Top"
                                                               Height="Auto"
                                                               Margin="0,4,0,4"
                                                               Style="{StaticResource SyncfusionRibbonDropDownButtonStyle}"
                                                               Padding="{StaticResource Windows11Light.BorderThickness2}">
                                        <syncribbon:DropDownButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Backgrounds}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=BackgroundTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:DropDownButton.ToolTip>
                                        <syncribbon:DropDownButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="23" 
                                                          Width="25"
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center" 
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M8.64941 20.5C8.64941 21.3284 9.32099 22 10.1494 22H22.1494C22.9778 22 23.6494 21.3284 23.6494 20.5V10.5V7H20.1494C19.321 7 18.6494 6.32843 18.6494 5.5V1H16.1494H10.1494C9.32099 1 8.64941 1.67157 8.64941 2.5V3C8.64941 3.27614 8.42556 3.5 8.14941 3.5C7.87327 3.5 7.64941 3.27614 7.64941 3V2.5C7.64941 1.11929 8.7687 0 10.1494 0H16.1494H19.1882C19.9476 0 20.6659 0.345226 21.1403 0.938263L24.1016 4.63982C24.4562 5.08311 24.6494 5.63388 24.6494 6.20156V10.5V20.5C24.6494 21.8807 23.5301 23 22.1494 23H10.1494C8.7687 23 7.64941 21.8807 7.64941 20.5V20C7.64941 19.7239 7.87327 19.5 8.14941 19.5C8.42556 19.5 8.64941 19.7239 8.64941 20V20.5ZM23.3207 5.26452C23.4912 5.4776 23.5995 5.73208 23.6358 6H20.1494C19.8733 6 19.6494 5.77614 19.6494 5.5V1.07267C19.9259 1.16202 20.1738 1.33086 20.3595 1.56296L23.3207 5.26452ZM3.80736 8.17039C6.68624 5.27592 11.4077 5.27632 14.2886 8.17289C17.2517 11.152 17.35 15.5901 15.0039 17.979C13.6459 19.3416 11.4066 19.3406 10.0478 17.9744L8.81473 16.7347C8.55414 16.4662 8.24314 16.2524 7.89981 16.1059C7.55226 15.9576 7.17863 15.8811 6.80115 15.881C6.42367 15.8809 6.04998 15.9573 5.70234 16.1055C5.35847 16.2522 5.04701 16.4663 4.78616 16.7353C4.28168 17.2461 3.98176 17.8819 3.96539 18.6156C3.9596 18.6536 3.94408 18.6751 3.94053 18.6786C3.89916 18.7202 3.86838 18.7239 3.85648 18.724C3.84192 18.7241 3.82561 18.719 3.80984 18.7032C0.929882 15.8076 0.92949 11.0638 3.80736 8.17039ZM14.9977 7.4677C11.726 4.17831 6.36845 4.17739 3.09834 7.4652C-0.167761 10.749 -0.166858 16.123 3.10083 19.4084L3.38055 19.1302L3.10083 19.4084C3.52052 19.8303 4.1995 19.8363 4.64955 19.3838C4.83864 19.1937 4.94115 18.9397 4.96298 18.6912C4.96406 18.6789 4.96468 18.6665 4.96485 18.6542C4.97105 18.1931 5.15077 17.7886 5.49895 17.4367L5.49897 17.4368L5.50289 17.4327C5.67158 17.2583 5.87283 17.12 6.09463 17.0254C6.31827 16.93 6.55846 16.881 6.80095 16.881C7.04344 16.8811 7.28363 16.9302 7.50727 17.0256C7.72891 17.1202 7.93003 17.2585 8.09867 17.4327L8.09864 17.4328L8.10343 17.4376L9.33874 18.6796C11.0884 20.4387 13.9643 20.4403 15.7136 18.6835L15.716 18.6812C18.5176 15.8302 18.2728 10.7605 14.9977 7.4677ZM9.64941 8.63477C9.64941 9.18705 9.2017 9.63477 8.64941 9.63477C8.09713 9.63477 7.64941 9.18705 7.64941 8.63477C7.64941 8.08248 8.09713 7.63477 8.64941 7.63477C9.2017 7.63477 9.64941 8.08248 9.64941 8.63477ZM5.14941 11.5C5.7017 11.5 6.14941 11.0523 6.14941 10.5C6.14941 9.94771 5.7017 9.5 5.14941 9.5C4.59713 9.5 4.14941 9.94771 4.14941 10.5C4.14941 11.0523 4.59713 11.5 5.14941 11.5ZM13.4326 10.459C13.4326 11.0113 12.9849 11.459 12.4326 11.459C11.8803 11.459 11.4326 11.0113 11.4326 10.459C11.4326 9.9067 11.8803 9.45898 12.4326 9.45898C12.9849 9.45898 13.4326 9.9067 13.4326 10.459ZM13.6494 15.5C14.2017 15.5 14.6494 15.0523 14.6494 14.5C14.6494 13.9477 14.2017 13.5 13.6494 13.5C13.0971 13.5 12.6494 13.9477 12.6494 14.5C12.6494 15.0523 13.0971 15.5 13.6494 15.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>                                                    
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:DropDownButton.IconTemplate>
                                        <syncribbon:DropDownButton.Items>
                                            <syncribbon_control:ColorPickerPalette x:Name="BackgroundColorPickerPalatte" 
                                                                                   Mode="Palette"
                                                                                   NoColorVisibility="Visible" 
                                                                                   BorderThickness="0" 
                                                                                   Color="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.PageSettings.PageBackground, Converter={StaticResource ColorToBrushConverter}, Mode=TwoWay}" 
                                                                                   Style="{StaticResource SyncfusionColorPickerPaletteStyle}"/>
                                        </syncribbon:DropDownButton.Items>
                                    </syncribbon:DropDownButton>
                                </syncribbon:RibbonBar>

                                <syncribbon:RibbonBar x:Name="Part_ConnectorsDesign"
                                                      Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Connectors}"
                                                      IsLauncherButtonVisible="False"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <syncribbon:RibbonBar.IconTemplate>
                                        <DataTemplate>
                                            <Grid>
                                                <Path Height="31" 
                                                          Width="{StaticResource Windows11Light.IconPanelSize}"
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center" 
                                                          Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M12.6494 0C11.5448 0 10.6494 0.895431 10.6494 2V5C10.6494 6.10457 11.5448 7 12.6494 7H15.6494C16.2829 7 16.8476 6.70549 17.214 6.24593L24.6624 13.2287C24.7759 14.2256 25.6222 15 26.6494 15H29.6494C30.754 15 31.6494 14.1046 31.6494 13V10C31.6494 8.89543 30.754 8 29.6494 8H26.6494C25.5448 8 24.6494 8.89543 24.6494 10V11.8459L17.6318 5.26684C17.6434 5.17955 17.6494 5.09048 17.6494 5V2C17.6494 0.895431 16.754 0 15.6494 0H12.6494ZM16.5732 5.38355C16.6223 5.26544 16.6494 5.13589 16.6494 5V2C16.6494 1.44772 16.2017 1 15.6494 1H12.6494C12.0971 1 11.6494 1.44772 11.6494 2V5C11.6494 5.55228 12.0971 6 12.6494 6H15.6494C15.9953 6 16.3001 5.82441 16.4797 5.55751L16.4427 5.5228L16.5732 5.38355ZM25.6494 13.0009C25.6499 13.5528 26.0974 14 26.6494 14H29.6494C30.2017 14 30.6494 13.5523 30.6494 13V10C30.6494 9.44772 30.2017 9 29.6494 9H26.6494C26.0971 9 25.6494 9.44772 25.6494 10V12.9987M6.64941 11C6.64941 9.89543 7.54484 9 8.64941 9H11.6494C12.754 9 13.6494 9.89543 13.6494 11V12H15.1494C16.5301 12 17.6494 13.1193 17.6494 14.5V15.5V17.5V18.5C17.6494 19.3284 18.321 20 19.1494 20H20.6494V19C20.6494 17.8954 21.5448 17 22.6494 17H25.6494C26.754 17 27.6494 17.8954 27.6494 19V22C27.6494 23.1046 26.754 24 25.6494 24H22.6494C21.5448 24 20.6494 23.1046 20.6494 22V21H19.1494C17.7687 21 16.6494 19.8807 16.6494 18.5V17.5V15.5V14.5C16.6494 13.6716 15.9778 13 15.1494 13H13.6494V14C13.6494 15.1046 12.754 16 11.6494 16H8.64941C7.54484 16 6.64941 15.1046 6.64941 14V11ZM8.64941 10C8.09713 10 7.64941 10.4477 7.64941 11V14C7.64941 14.5523 8.09713 15 8.64941 15H11.6494C12.2017 15 12.6494 14.5523 12.6494 14V11C12.6494 10.4477 12.2017 10 11.6494 10H8.64941ZM0.649414 19C0.649414 17.8954 1.54484 17 2.64941 17H5.64941C6.75398 17 7.64941 17.8954 7.64941 19V20H9.14941C10.5301 20 11.6494 21.1193 11.6494 22.5V23.5V25.5C11.6494 26.3284 12.321 27 13.1494 27H14.6494V26C14.6494 24.8954 15.5448 24 16.6494 24H19.6494C20.754 24 21.6494 24.8954 21.6494 26V29C21.6494 30.1046 20.754 31 19.6494 31H16.6494C15.5448 31 14.6494 30.1046 14.6494 29V28H13.1494C11.7687 28 10.6494 26.8807 10.6494 25.5V23.5V22.5C10.6494 21.6716 9.97784 21 9.14941 21H7.64941V22C7.64941 23.1046 6.75398 24 5.64941 24H2.64941C1.54484 24 0.649414 23.1046 0.649414 22V19ZM2.64941 18C2.09713 18 1.64941 18.4477 1.64941 19V22C1.64941 22.5523 2.09713 23 2.64941 23H5.64941C6.2017 23 6.64941 22.5523 6.64941 22V19C6.64941 18.4477 6.2017 18 5.64941 18H2.64941ZM16.6494 25C16.0971 25 15.6494 25.4477 15.6494 26V29C15.6494 29.5523 16.0971 30 16.6494 30H19.6494C20.2017 30 20.6494 29.5523 20.6494 29V26C20.6494 25.4477 20.2017 25 19.6494 25H16.6494ZM21.6494 19C21.6494 18.4477 22.0971 18 22.6494 18H25.6494C26.2017 18 26.6494 18.4477 26.6494 19V22C26.6494 22.5523 26.2017 23 25.6494 23H22.6494C22.0971 23 21.6494 22.5523 21.6494 22V19Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </DataTemplate>
                                    </syncribbon:RibbonBar.IconTemplate>
                                    <syncribbon:DropDownButton x:Name="Part_Connectors"
                                                               Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Connectors}"
                                                               SizeForm="Large"
                                                               Height="Auto"
                                                               IconType="Icon"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Top"
                                                               Margin="0,4,0,4"
                                                               Style="{StaticResource SyncfusionRibbonDropDownButtonStyle}"
                                                               Padding="{StaticResource Windows11Light.BorderThickness2}">
                                        <syncribbon:DropDownButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Connectors}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ConnectorsText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:DropDownButton.ToolTip>
                                        <syncribbon:DropDownButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Height="31" 
                                                          Width="{StaticResource Windows11Light.IconPanelSize}"
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center" 
                                                          Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M12.6494 0C11.5448 0 10.6494 0.895431 10.6494 2V5C10.6494 6.10457 11.5448 7 12.6494 7H15.6494C16.2829 7 16.8476 6.70549 17.214 6.24593L24.6624 13.2287C24.7759 14.2256 25.6222 15 26.6494 15H29.6494C30.754 15 31.6494 14.1046 31.6494 13V10C31.6494 8.89543 30.754 8 29.6494 8H26.6494C25.5448 8 24.6494 8.89543 24.6494 10V11.8459L17.6318 5.26684C17.6434 5.17955 17.6494 5.09048 17.6494 5V2C17.6494 0.895431 16.754 0 15.6494 0H12.6494ZM16.5732 5.38355C16.6223 5.26544 16.6494 5.13589 16.6494 5V2C16.6494 1.44772 16.2017 1 15.6494 1H12.6494C12.0971 1 11.6494 1.44772 11.6494 2V5C11.6494 5.55228 12.0971 6 12.6494 6H15.6494C15.9953 6 16.3001 5.82441 16.4797 5.55751L16.4427 5.5228L16.5732 5.38355ZM25.6494 13.0009C25.6499 13.5528 26.0974 14 26.6494 14H29.6494C30.2017 14 30.6494 13.5523 30.6494 13V10C30.6494 9.44772 30.2017 9 29.6494 9H26.6494C26.0971 9 25.6494 9.44772 25.6494 10V12.9987M6.64941 11C6.64941 9.89543 7.54484 9 8.64941 9H11.6494C12.754 9 13.6494 9.89543 13.6494 11V12H15.1494C16.5301 12 17.6494 13.1193 17.6494 14.5V15.5V17.5V18.5C17.6494 19.3284 18.321 20 19.1494 20H20.6494V19C20.6494 17.8954 21.5448 17 22.6494 17H25.6494C26.754 17 27.6494 17.8954 27.6494 19V22C27.6494 23.1046 26.754 24 25.6494 24H22.6494C21.5448 24 20.6494 23.1046 20.6494 22V21H19.1494C17.7687 21 16.6494 19.8807 16.6494 18.5V17.5V15.5V14.5C16.6494 13.6716 15.9778 13 15.1494 13H13.6494V14C13.6494 15.1046 12.754 16 11.6494 16H8.64941C7.54484 16 6.64941 15.1046 6.64941 14V11ZM8.64941 10C8.09713 10 7.64941 10.4477 7.64941 11V14C7.64941 14.5523 8.09713 15 8.64941 15H11.6494C12.2017 15 12.6494 14.5523 12.6494 14V11C12.6494 10.4477 12.2017 10 11.6494 10H8.64941ZM0.649414 19C0.649414 17.8954 1.54484 17 2.64941 17H5.64941C6.75398 17 7.64941 17.8954 7.64941 19V20H9.14941C10.5301 20 11.6494 21.1193 11.6494 22.5V23.5V25.5C11.6494 26.3284 12.321 27 13.1494 27H14.6494V26C14.6494 24.8954 15.5448 24 16.6494 24H19.6494C20.754 24 21.6494 24.8954 21.6494 26V29C21.6494 30.1046 20.754 31 19.6494 31H16.6494C15.5448 31 14.6494 30.1046 14.6494 29V28H13.1494C11.7687 28 10.6494 26.8807 10.6494 25.5V23.5V22.5C10.6494 21.6716 9.97784 21 9.14941 21H7.64941V22C7.64941 23.1046 6.75398 24 5.64941 24H2.64941C1.54484 24 0.649414 23.1046 0.649414 22V19ZM2.64941 18C2.09713 18 1.64941 18.4477 1.64941 19V22C1.64941 22.5523 2.09713 23 2.64941 23H5.64941C6.2017 23 6.64941 22.5523 6.64941 22V19C6.64941 18.4477 6.2017 18 5.64941 18H2.64941ZM16.6494 25C16.0971 25 15.6494 25.4477 15.6494 26V29C15.6494 29.5523 16.0971 30 16.6494 30H19.6494C20.2017 30 20.6494 29.5523 20.6494 29V26C20.6494 25.4477 20.2017 25 19.6494 25H16.6494ZM21.6494 19C21.6494 18.4477 22.0971 18 22.6494 18H25.6494C26.2017 18 26.6494 18.4477 26.6494 19V22C26.6494 22.5523 26.2017 23 25.6494 23H22.6494C22.0971 23 21.6494 22.5523 21.6494 22V19Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:DropDownButton.IconTemplate>
                                        <syncribbon:DropDownButton.Items>
                                            <syncribbon:RibbonButton x:Name="Part_Orthogonal"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Orthogonal}"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     IconType="Icon"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="13" 
                                                                  Width="9"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Center" 
                                                                  VerticalAlignment="Center" 
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M0 0.5C0 0.223858 0.223858 0 0.5 0H3.5C4.32843 0 5 0.671573 5 1.5V11.5C5 11.7761 5.22386 12 5.5 12H8.5C8.77614 12 9 12.2239 9 12.5C9 12.7761 8.77614 13 8.5 13H5.5C4.67157 13 4 12.3284 4 11.5V1.5C4 1.22386 3.77614 1 3.5 1H0.5C0.223858 1 0 0.776142 0 0.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>                                                            
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_StraightLine"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=StraightLine}"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     IconType="Icon"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="13" 
                                                                  Width="13"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Center" 
                                                                  VerticalAlignment="Center" 
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M0.146447 0.146447C0.341709 -0.0488155 0.658291 -0.0488155 0.853553 0.146447L12.8536 12.1464C13.0488 12.3417 13.0488 12.6583 12.8536 12.8536C12.6583 13.0488 12.3417 13.0488 12.1464 12.8536L0.146447 0.853553C-0.0488155 0.658291 -0.0488155 0.341709 0.146447 0.146447Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>

                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon:RibbonButton x:Name="Part_Cubic"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Bezier}"
                                                                     SizeForm="Small"
                                                                     HorizontalAlignment="Stretch"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     IconType="Icon"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Height="13" 
                                                                  Width="9"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center" 
                                                                  Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M0 0.5C0 0.223858 0.223858 0 0.5 0H4.5C5.08001 0 5.9389 0.16989 6.66715 0.694233C7.4209 1.23693 8 2.13704 8 3.5C8 3.51026 8 3.52071 8.00001 3.53134C8.00023 4.01943 8.00062 4.88746 7.55375 5.63225C7.06374 6.44892 6.12176 7 4.5 7C4.08001 7 3.4389 7.13011 2.91715 7.50577C2.4209 7.86307 2 8.46296 2 9.5V9.54138L1.9932 9.5822C1.9248 9.99258 1.97108 10.6039 2.30224 11.0957C2.61248 11.5565 3.23199 12 4.5 12H8.5C8.77614 12 9 12.2239 9 12.5C9 12.7761 8.77614 13 8.5 13H4.5C2.96801 13 2.00419 12.4435 1.47276 11.6543C0.974177 10.9138 0.911291 10.0489 1.00017 9.4596C1.0115 8.11923 1.58657 7.23155 2.33285 6.69423C3.0611 6.16989 3.91999 6 4.5 6C5.87824 6 6.43626 5.55108 6.69625 5.11775C6.98965 4.62876 7 4.01843 7 3.5C7 2.46296 6.5791 1.86307 6.08285 1.50577C5.5611 1.13011 4.91999 1 4.5 1H0.5C0.223858 1 0 0.776142 0 0.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                            </syncribbon:RibbonButton>
                                            <syncribbon_control:MenuItemSeparator Margin="-20,0,0,0"/>

                                            <MenuItem x:Name="Part_Bridging"
                                                      Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Bridging}"
                                                      IsCheckable="True"
                                                      IsChecked ="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.Constraints, Converter={StaticResource graphconstraintstoboolconverter},ConverterParameter={StaticResource BridgingParam}, Mode=OneWay}"/>
                                        </syncribbon:DropDownButton.Items>
                                    </syncribbon:DropDownButton>

                                </syncribbon:RibbonBar>

                            </syncribbon:RibbonTab>

                            <syncribbon:RibbonTab x:Name="Part_ViewRibbonTab" Caption="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=View}">

                                <syncribbon:RibbonBar x:Name="Part_Show"
                                                      Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Show}"
                                                      IsLauncherButtonVisible="False"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <syncribbon:RibbonBar.IconTemplate>
                                        <DataTemplate>
                                            <Grid>
                                                <Path Height="13" 
                                                      Width="16"
                                                      Stretch="Fill"
                                                      HorizontalAlignment="Center" 
                                                      VerticalAlignment="Center"
                                                      Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M3.09677 1.5C3.09677 0.671573 3.79001 0 4.64516 0H6.70968C7.56483 0 8.25806 0.671573 8.25806 1.5V2.08579C8.25806 2.48361 8.09493 2.86514 7.80455 3.14645L6.77229 4.14645C6.16761 4.73223 5.18723 4.73223 4.58254 4.14645L3.55029 3.14645C3.25991 2.86514 3.09677 2.48361 3.09677 2.08579V1.5ZM4.64516 1C4.36011 1 4.12903 1.22386 4.12903 1.5V2.08579C4.12903 2.21839 4.18341 2.34557 4.2802 2.43934L5.31246 3.43934C5.51402 3.6346 5.84082 3.6346 6.04238 3.43934L7.07464 2.43934C7.17143 2.34557 7.22581 2.21839 7.22581 2.08579V1.5C7.22581 1.22386 6.99473 1 6.70968 1H4.64516ZM1.54839 3C1.26334 3 1.03226 3.22386 1.03226 3.5V9.5C1.03226 9.77614 1.26334 10 1.54839 10H9.80645C10.0915 10 10.3226 10.2239 10.3226 10.5C10.3226 10.7761 10.0915 11 9.80645 11H1.54839C0.693237 11 0 10.3284 0 9.5V3.5C0 2.67157 0.693237 2 1.54839 2H2.06452C2.34957 2 2.58065 2.22386 2.58065 2.5C2.58065 2.77614 2.34957 3 2.06452 3H1.54839ZM8.77419 2.5C8.77419 2.22386 9.00527 2 9.29032 2H16V3H9.29032C9.00527 3 8.77419 2.77614 8.77419 2.5ZM8.25806 5C8.54311 5 8.77419 5.22386 8.77419 5.5V7.5C8.77419 7.77614 8.54311 8 8.25806 8C7.97301 8 7.74194 7.77614 7.74194 7.5V5.5C7.74194 5.22386 7.97301 5 8.25806 5ZM15.4839 5C15.7689 5 16 5.22386 16 5.5V7.5C16 7.77614 15.7689 8 15.4839 8C15.1988 8 14.9677 7.77614 14.9677 7.5V5.5C14.9677 5.22386 15.1988 5 15.4839 5ZM12.3245 8.85355C12.9292 8.26777 13.9095 8.26777 14.5142 8.85355L15.5465 9.85355C15.8369 10.1349 16 10.5164 16 10.9142V11.5C16 12.3284 15.3068 13 14.4516 13H12.3871C11.5319 13 10.8387 12.3284 10.8387 11.5V10.9142C10.8387 10.5164 11.0018 10.1349 11.2922 9.85355L12.3245 8.85355ZM13.7843 9.56066C13.5828 9.3654 13.256 9.3654 13.0544 9.56066L12.0221 10.5607C11.9253 10.6544 11.871 10.7816 11.871 10.9142V11.5C11.871 11.7761 12.102 12 12.3871 12H14.4516C14.7367 12 14.9677 11.7761 14.9677 11.5V10.9142C14.9677 10.7816 14.9134 10.6544 14.8166 10.5607L13.7843 9.56066Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </DataTemplate>
                                    </syncribbon:RibbonBar.IconTemplate>
                                    <syncribbon:RibbonCheckBox x:Name="Part_Ruler"
                                                               Margin="0,4,2,4"
                                                               Height="20"
                                                               Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Ruler}"
                                                               Style="{StaticResource SyncfusionRibbonCheckBoxStyle}">
                                        <syncribbon:RibbonCheckBox.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Ruler}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=RulerText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonCheckBox.ToolTip>
                                    </syncribbon:RibbonCheckBox>
                                    <syncribbon:RibbonCheckBox x:Name="Part_ShowLines"
                                                               Margin="0,4,2,4"
                                                               Height="20"
                                                               Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=GridLines}"
                                                               Style="{StaticResource SyncfusionRibbonCheckBoxStyle}">
                                        <syncribbon:RibbonCheckBox.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=GridLines}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=GridlinesTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonCheckBox.ToolTip>
                                    </syncribbon:RibbonCheckBox>
                                    <syncribbon:RibbonCheckBox x:Name="Part_SnapToGrid"
                                                               Margin="2,4,2,4"
                                                               Height="20"
                                                               Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SnapToGrid}"
                                                               syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                               Style="{StaticResource SyncfusionRibbonCheckBoxStyle}">
                                        <syncribbon:RibbonCheckBox.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SnapToGrid}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SnapToGridTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonCheckBox.ToolTip>
                                    </syncribbon:RibbonCheckBox>
                                    <syncribbon:RibbonCheckBox x:Name="Part_SnapToObject"
                                                               Margin="2,4,2,4"
                                                               Height="20"
                                                               syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                               Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SnapToObject}"
                                                               Style="{StaticResource SyncfusionRibbonCheckBoxStyle}">
                                        <syncribbon:RibbonCheckBox.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SnapToObject}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=SnapToObjectTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonCheckBox.ToolTip>
                                    </syncribbon:RibbonCheckBox>
                                    <syncribbon:RibbonCheckBox x:Name="Part_PageBreak"
                                                               Margin="2,4,2,4"
                                                               Height="20"
                                                               Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PageBreaks}"
                                                               IsChecked="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.PageSettings.ShowPageBreaks}"                                                               
                                                               Style="{StaticResource SyncfusionRibbonCheckBoxStyle}">
                                        <syncribbon:RibbonCheckBox.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PageBreaks}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PageBreaksTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonCheckBox.ToolTip>
                                    </syncribbon:RibbonCheckBox>
                                    <syncribbon:RibbonCheckBox x:Name="Part_MultiplePage"
                                                               Margin="2,4,2,4"
                                                               Height="20"
                                                               Content="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=MultiplePages}"
                                                               IsChecked="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=DataContext.PageSettings.MultiplePage}"
                                                               Style="{StaticResource SyncfusionRibbonCheckBoxStyle}">
                                        <syncribbon:RibbonCheckBox.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=MultiplePages}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=MultiplePagesTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonCheckBox.ToolTip>
                                    </syncribbon:RibbonCheckBox>

                                    <syncribbon:DropDownButton x:Name="Part_Taskpane"
                                                               Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=TaskPanes}"
                                                               IconType="Icon"
                                                               SizeForm="Large"
                                                               Margin="2,4,0,4"
                                                               Height="Auto"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Top"
                                                               SplitLabelIntoTwoLine="False"
                                                               ContextMenuService.Placement="Bottom"
                                                               syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                               Style="{StaticResource SyncfusionRibbonDropDownButtonStyle}"
                                                               Padding="{StaticResource Windows11Light.BorderThickness2}">
                                        <syncribbon:DropDownButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Data="M9.64941 1H26.6494C27.2017 1 27.6494 1.44772 27.6494 2V26C27.6494 26.5523 27.2017 27 26.6494 27H19.1494V23.5H22.6494C24.0301 23.5 25.1494 22.3807 25.1494 21V5C25.1494 3.61929 24.0301 2.5 22.6494 2.5H15.6494C14.2687 2.5 13.1494 3.61929 13.1494 5V11.5H9.64941V1ZM9.64941 12.5H13.1494H14.1494H16.6494C17.4778 12.5 18.1494 13.1716 18.1494 14V27H9.64941L9.64941 12.5ZM18.1494 28H2.64941C1.54485 28 0.649414 27.1046 0.649414 26V2C0.649414 0.895431 1.54484 0 2.64941 0H26.6494C27.754 0 28.6494 0.895431 28.6494 2V26C28.6494 27.1046 27.754 28 26.6494 28H19.1494H18.1494ZM14.1494 11.5H16.6494C18.0301 11.5 19.1494 12.6193 19.1494 14V22.5H22.6494C23.4778 22.5 24.1494 21.8284 24.1494 21V5C24.1494 4.17157 23.4778 3.5 22.6494 3.5H15.6494C14.821 3.5 14.1494 4.17157 14.1494 5V11.5ZM8.64941 11.5V1H2.64941C2.09713 1 1.64941 1.44772 1.64941 2V26C1.64941 26.5523 2.09713 27 2.64941 27H8.64941L8.64941 12.5V11.5ZM2.64941 5.5C2.64941 5.22386 2.87327 5 3.14941 5H7.14941C7.42556 5 7.64941 5.22386 7.64941 5.5C7.64941 5.77614 7.42556 6 7.14941 6H3.14941C2.87327 6 2.64941 5.77614 2.64941 5.5ZM3.14941 11C2.87327 11 2.64941 11.2239 2.64941 11.5C2.64941 11.7761 2.87327 12 3.14941 12H7.14941C7.42556 12 7.64941 11.7761 7.64941 11.5C7.64941 11.2239 7.42556 11 7.14941 11H3.14941ZM2.64941 16.5C2.64941 16.2239 2.87327 16 3.14941 16H7.14941C7.42556 16 7.64941 16.2239 7.64941 16.5C7.64941 16.7761 7.42556 17 7.14941 17H3.14941C2.87327 17 2.64941 16.7761 2.64941 16.5ZM3.14941 22C2.87327 22 2.64941 22.2239 2.64941 22.5C2.64941 22.7761 2.87327 23 3.14941 23H7.14941C7.42556 23 7.64941 22.7761 7.64941 22.5C7.64941 22.2239 7.42556 22 7.14941 22H3.14941Z"
                                                          Height="28" 
                                                          Width="29"
                                                          Stretch="Fill" 
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Bottom" 
                                                          Fill="{StaticResource IconColor}"/>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:DropDownButton.IconTemplate>
                                        <syncribbon:DropDownButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=TaskPanes}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=TaskPanesTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:DropDownButton.ToolTip>
                                        <syncribbon:DropDownButton.Items>
                                            <syncribbon:RibbonButton x:Name="Part_Overview"
                                                                     SizeForm="Small"
                                                                     IconType="Icon"
                                                                     Height="24"
                                                                     Padding="2,0,0,0"
                                                                     Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PanZoom}"
                                                                     Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                                <syncribbon:RibbonButton.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Data="M6.1 1C3.28335 1 1 3.28335 1 6.1C1 8.91665 3.28335 11.2 6.1 11.2C8.91665 11.2 11.2 8.91665 11.2 6.1C11.2 3.28335 8.91665 1 6.1 1ZM0 6.1C0 2.73106 2.73106 0 6.1 0C9.46894 0 12.2 2.73106 12.2 6.1C12.2 7.60447 11.6554 8.98173 10.7525 10.0454L14.8536 14.1464C15.0488 14.3417 15.0488 14.6583 14.8536 14.8536C14.6583 15.0488 14.3417 15.0488 14.1464 14.8536L10.0454 10.7525C8.98173 11.6554 7.60447 12.2 6.1 12.2C2.73106 12.2 0 9.46894 0 6.1ZM6 2C6.08925 2 6.17303 2.02338 6.24556 2.06436C6.25321 2.06778 6.2608 2.07138 6.26833 2.07515L7.22361 2.55279C7.4706 2.67628 7.57071 2.97662 7.44721 3.22361C7.32372 3.4706 7.02338 3.57071 6.77639 3.44721L6.5 3.30902V4.5C6.5 4.77614 6.27614 5 6 5C5.72386 5 5.5 4.77614 5.5 4.5V3.30902L5.22361 3.44721C4.97662 3.57071 4.67628 3.4706 4.55279 3.22361C4.42929 2.97662 4.5294 2.67628 4.77639 2.55279L5.73167 2.07515C5.7392 2.07138 5.74679 2.06778 5.75444 2.06435C5.82697 2.02338 5.91075 2 6 2ZM2.06435 5.75444C2.02338 5.82697 2 5.91075 2 6C2 6.08925 2.02338 6.17303 2.06435 6.24556C2.06778 6.25321 2.07138 6.2608 2.07515 6.26833L2.55279 7.22361C2.67628 7.4706 2.97662 7.57071 3.22361 7.44721C3.4706 7.32372 3.57071 7.02338 3.44721 6.77639L3.30902 6.5H4.5C4.77614 6.5 5 6.27614 5 6C5 5.72386 4.77614 5.5 4.5 5.5H3.30902L3.44721 5.22361C3.57071 4.97662 3.4706 4.67628 3.22361 4.55279C2.97662 4.42929 2.67628 4.5294 2.55279 4.77639L2.07515 5.73167C2.07138 5.7392 2.06778 5.74679 2.06435 5.75444ZM6 7C6.27614 7 6.5 7.22386 6.5 7.5V8.69098L6.77639 8.55279C7.02338 8.42929 7.32372 8.5294 7.44721 8.77639C7.57071 9.02338 7.4706 9.32372 7.22361 9.44721L6.26833 9.92485C6.2608 9.92862 6.25321 9.93221 6.24557 9.93564C6.17303 9.97662 6.08925 10 6 10C5.91075 10 5.82697 9.97662 5.75443 9.93564C5.74679 9.93221 5.7392 9.92862 5.73167 9.92485L4.77639 9.44721C4.5294 9.32372 4.42929 9.02338 4.55279 8.77639C4.67628 8.5294 4.97662 8.42929 5.22361 8.55279L5.5 8.69098V7.5C5.5 7.22386 5.72386 7 6 7ZM7.5 5.5C7.22386 5.5 7 5.72386 7 6C7 6.27614 7.22386 6.5 7.5 6.5H8H8.5H8.69098L8.55279 6.77639C8.42929 7.02338 8.5294 7.32372 8.77639 7.44721C9.02338 7.57071 9.32372 7.4706 9.44721 7.22361L9.92485 6.26833C9.92862 6.2608 9.93221 6.25321 9.93564 6.24557C9.97662 6.17303 10 6.08925 10 6C10 5.91075 9.97662 5.82697 9.93564 5.75443C9.93221 5.74679 9.92862 5.7392 9.92485 5.73167L9.44721 4.77639C9.32372 4.5294 9.02338 4.42929 8.77639 4.55279C8.5294 4.67628 8.42929 4.97662 8.55279 5.22361L8.69098 5.5H7.5Z" 
                                                                  Width="15"
                                                                  Height="15"
                                                                  Stretch="Fill"
                                                                  HorizontalAlignment="Center" 
                                                                  VerticalAlignment="Center" 
                                                                  Fill="{StaticResource IconColor}"/>
                                                        </Grid>
                                                    </DataTemplate>
                                                </syncribbon:RibbonButton.IconTemplate>
                                                <syncribbon:RibbonButton.ToolTip>
                                                    <StackPanel Orientation="Vertical">
                                                        <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PanZoom}" FontWeight="Bold" TextAlignment="Left"/>
                                                        <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=PanZoomTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" Width="200" Margin="10,8,0,0"/>
                                                    </StackPanel>
                                                </syncribbon:RibbonButton.ToolTip>
                                            </syncribbon:RibbonButton>
                                        </syncribbon:DropDownButton.Items>
                                    </syncribbon:DropDownButton>
                                </syncribbon:RibbonBar>

                                <syncribbon:RibbonBar x:Name="Part_Zoom"
                                                      Header="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=Zoom}"
                                                      IsLauncherButtonVisible="False"
                                                      Style="{StaticResource SyncfusionRibbonBarStyle}">
                                    <syncribbon:RibbonBar.IconTemplate>
                                        <DataTemplate>
                                            <Grid>
                                                <Path Height="{StaticResource Windows11Light.SubHeaderTextStyle}" 
                                                      Width="{StaticResource Windows11Light.SubHeaderTextStyle}"
                                                      Stretch="Fill"
                                                      HorizontalAlignment="Center" 
                                                      VerticalAlignment="Center"
                                                      Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M14 5.5C14 6.00521 13.9349 6.49219 13.8047 6.96094C13.6745 7.42969 13.4896 7.86979 13.25 8.28125C13.0104 8.6875 12.724 9.05729 12.3906 9.39062C12.0573 9.72396 11.6849 10.0104 11.2734 10.25C10.8672 10.4896 10.4297 10.6745 9.96094 10.8047C9.49219 10.9349 9.00521 11 8.5 11C7.85417 11 7.22656 10.8906 6.61719 10.6719C6.01302 10.4531 5.46615 10.138 4.97656 9.72656L0.851562 13.8516C0.752604 13.9505 0.635417 14 0.5 14C0.364583 14 0.247396 13.9505 0.148438 13.8516C0.0494792 13.7526 0 13.6354 0 13.5C0 13.3646 0.0494792 13.2474 0.148438 13.1484L4.27344 9.02344C3.86198 8.53385 3.54688 7.98698 3.32812 7.38281C3.10938 6.77344 3 6.14583 3 5.5C3 4.99479 3.0651 4.50781 3.19531 4.03906C3.32552 3.5651 3.51042 3.125 3.75 2.71875C3.98958 2.3125 4.27604 1.94271 4.60938 1.60938C4.94271 1.27604 5.3125 0.989583 5.71875 0.75C6.125 0.510417 6.5625 0.325521 7.03125 0.195312C7.5 0.0651042 7.98958 0 8.5 0C9.00521 0 9.49219 0.0651042 9.96094 0.195312C10.4297 0.325521 10.8672 0.510417 11.2734 0.75C11.6849 0.989583 12.0573 1.27604 12.3906 1.60938C12.724 1.94271 13.0104 2.3151 13.25 2.72656C13.4896 3.13281 13.6745 3.57031 13.8047 4.03906C13.9349 4.50781 14 4.99479 14 5.5ZM13 5.5C13 4.88021 12.8802 4.29688 12.6406 3.75C12.4062 3.20312 12.0859 2.72656 11.6797 2.32031C11.2734 1.91406 10.7969 1.59375 10.25 1.35938C9.70312 1.11979 9.11979 1 8.5 1C7.88021 1 7.29688 1.11979 6.75 1.35938C6.20312 1.59375 5.72656 1.91406 5.32031 2.32031C4.91406 2.72656 4.59115 3.20312 4.35156 3.75C4.11719 4.29688 4 4.88021 4 5.5C4 6.125 4.11719 6.71094 4.35156 7.25781C4.58594 7.80469 4.90625 8.28125 5.3125 8.6875C5.71875 9.09375 6.19531 9.41406 6.74219 9.64844C7.28906 9.88281 7.875 10 8.5 10C9.125 10 9.71094 9.88281 10.2578 9.64844C10.8047 9.41406 11.2812 9.09375 11.6875 8.6875C12.0938 8.28125 12.4141 7.80469 12.6484 7.25781C12.8828 6.71094 13 6.125 13 5.5Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </DataTemplate>
                                    </syncribbon:RibbonBar.IconTemplate>
                                    <syncribbon:RibbonButton x:Name="Part_ZoomIn"
                                                             Margin="0,4,2,4"
                                                             Height="24"
                                                             Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ZoomIn}"
                                                             SizeForm="Small"
                                                             IconType="Icon"
                                                             Command="diag:DiagramCommands.Zoom"
                                                             CommandParameter="{StaticResource ZoomInParameter}"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ZoomIn}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ZoomInTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Data="M9.02344 9.72656C8.52344 10.138 7.97135 10.4531 7.36719 10.6719C6.76823 10.8906 6.14583 11 5.5 11C4.73438 11 4.01823 10.8568 3.35156 10.5703C2.6849 10.2839 2.10156 9.89323 1.60156 9.39844C1.10677 8.89844 0.716146 8.3151 0.429688 7.64844C0.143229 6.98177 0 6.26562 0 5.5C0 4.99479 0.0651042 4.50781 0.195312 4.03906C0.325521 3.5651 0.510417 3.125 0.75 2.71875C0.989583 2.3125 1.27604 1.94271 1.60938 1.60938C1.94271 1.27604 2.3125 0.989583 2.71875 0.75C3.125 0.510417 3.5625 0.325521 4.03125 0.195312C4.5 0.0651042 4.98958 0 5.5 0C6.00521 0 6.49219 0.0651042 6.96094 0.195312C7.42969 0.325521 7.86719 0.510417 8.27344 0.75C8.6849 0.989583 9.05729 1.27604 9.39062 1.60938C9.72396 1.94271 10.0104 2.3151 10.25 2.72656C10.4896 3.13281 10.6745 3.57031 10.8047 4.03906C10.9349 4.50781 11 4.99479 11 5.5C11 6.14583 10.8906 6.77083 10.6719 7.375C10.4531 7.97396 10.138 8.52344 9.72656 9.02344L13.8516 13.1484C13.9505 13.2474 14 13.3646 14 13.5C14 13.6354 13.9505 13.7526 13.8516 13.8516C13.7526 13.9505 13.6354 14 13.5 14C13.3646 14 13.2474 13.9505 13.1484 13.8516L9.02344 9.72656ZM10 5.5C10 5.08854 9.94531 4.69271 9.83594 4.3125C9.73177 3.92708 9.58073 3.56771 9.38281 3.23438C9.1901 2.90104 8.95573 2.59635 8.67969 2.32031C8.40365 2.04427 8.09896 1.8099 7.76562 1.61719C7.43229 1.41927 7.07292 1.26823 6.6875 1.16406C6.30729 1.05469 5.91146 1 5.5 1C4.88021 1 4.29688 1.11979 3.75 1.35938C3.20312 1.59375 2.72656 1.91406 2.32031 2.32031C1.91406 2.72656 1.59115 3.20312 1.35156 3.75C1.11719 4.29688 1 4.88021 1 5.5C1 6.125 1.11719 6.71094 1.35156 7.25781C1.58594 7.80469 1.90625 8.28125 2.3125 8.6875C2.71875 9.09375 3.19531 9.41406 3.74219 9.64844C4.28906 9.88281 4.875 10 5.5 10C6.11979 10 6.70312 9.88281 7.25 9.64844C7.79688 9.40885 8.27344 9.08594 8.67969 8.67969C9.08594 8.27344 9.40625 7.79688 9.64062 7.25C9.88021 6.70312 10 6.11979 10 5.5ZM5 6H3C2.86458 6 2.7474 5.95052 2.64844 5.85156C2.54948 5.7526 2.5 5.63542 2.5 5.5C2.5 5.36458 2.54948 5.2474 2.64844 5.14844C2.7474 5.04948 2.86458 5 3 5H5V3C5 2.86458 5.04948 2.7474 5.14844 2.64844C5.2474 2.54948 5.36458 2.5 5.5 2.5C5.63542 2.5 5.7526 2.54948 5.85156 2.64844C5.95052 2.7474 6 2.86458 6 3V5H8C8.13542 5 8.2526 5.04948 8.35156 5.14844C8.45052 5.2474 8.5 5.36458 8.5 5.5C8.5 5.63542 8.45052 5.7526 8.35156 5.85156C8.2526 5.95052 8.13542 6 8 6H6V8C6 8.13542 5.95052 8.2526 5.85156 8.35156C5.7526 8.45052 5.63542 8.5 5.5 8.5C5.36458 8.5 5.2474 8.45052 5.14844 8.35156C5.04948 8.2526 5 8.13542 5 8V6Z"
                                                          Height="14"
                                                          Width="14"
                                                          Stretch="Fill"
                                                          VerticalAlignment="Center"
                                                          HorizontalAlignment="Center"
                                                          Fill="{StaticResource IconColor}"/>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                    <syncribbon:RibbonButton x:Name="Part_ZoomOut"
                                                             Margin="0,4,2,4"
                                                             Height="24"
                                                             Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ZoomOut}"
                                                             SizeForm="Small"
                                                             IconType="Icon"
                                                             Command="diag:DiagramCommands.Zoom"
                                                             CommandParameter="{StaticResource ZoomOutParameter}"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ZoomOut}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=ZoomOutTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Data="M14 13.5C14 13.6354 13.9505 13.7526 13.8516 13.8516C13.7526 13.9505 13.6354 14 13.5 14C13.3646 14 13.2474 13.9505 13.1484 13.8516L9.01562 9.71875C8.52604 10.1302 7.97656 10.4479 7.36719 10.6719C6.76302 10.8906 6.14062 11 5.5 11C4.99479 11 4.50781 10.9349 4.03906 10.8047C3.57031 10.6745 3.13281 10.4896 2.72656 10.25C2.32031 10.0104 1.94792 9.72396 1.60938 9.39062C1.27604 9.05208 0.989583 8.67969 0.75 8.27344C0.510417 7.86719 0.325521 7.42969 0.195312 6.96094C0.0651042 6.49219 0 6.00521 0 5.5C0 4.99479 0.0651042 4.50781 0.195312 4.03906C0.325521 3.57031 0.510417 3.13281 0.75 2.72656C0.989583 2.32031 1.27604 1.95052 1.60938 1.61719C1.94792 1.27865 2.32031 0.989583 2.72656 0.75C3.13281 0.510417 3.57031 0.325521 4.03906 0.195312C4.50781 0.0651042 4.99479 0 5.5 0C6.00521 0 6.49219 0.0651042 6.96094 0.195312C7.42969 0.325521 7.86719 0.510417 8.27344 0.75C8.67969 0.989583 9.04948 1.27865 9.38281 1.61719C9.72135 1.95052 10.0104 2.32031 10.25 2.72656C10.4896 3.13281 10.6745 3.57031 10.8047 4.03906C10.9349 4.50781 11 4.99479 11 5.5C11 6.14062 10.888 6.76562 10.6641 7.375C10.4453 7.97917 10.1302 8.52604 9.71875 9.01562L13.8516 13.1484C13.9505 13.2474 14 13.3646 14 13.5ZM10 5.5C10 5.08854 9.94531 4.69271 9.83594 4.3125C9.73177 3.92708 9.58073 3.56771 9.38281 3.23438C9.1901 2.90104 8.95573 2.59635 8.67969 2.32031C8.40365 2.04427 8.09896 1.8099 7.76562 1.61719C7.43229 1.41927 7.07292 1.26823 6.6875 1.16406C6.30729 1.05469 5.91146 1 5.5 1C4.88021 1 4.29688 1.11979 3.75 1.35938C3.20312 1.59375 2.72656 1.91406 2.32031 2.32031C1.91406 2.72656 1.59115 3.20312 1.35156 3.75C1.11719 4.29688 1 4.88021 1 5.5C1 6.125 1.11719 6.71094 1.35156 7.25781C1.58594 7.80469 1.90625 8.28125 2.3125 8.6875C2.71875 9.09375 3.19531 9.41406 3.74219 9.64844C4.28906 9.88281 4.875 10 5.5 10C6.11979 10 6.70312 9.88281 7.25 9.64844C7.79688 9.40885 8.27344 9.08594 8.67969 8.67969C9.08594 8.27344 9.40625 7.79688 9.64062 7.25C9.88021 6.70312 10 6.11979 10 5.5ZM8 5C8.13542 5 8.2526 5.04948 8.35156 5.14844C8.45052 5.2474 8.5 5.36458 8.5 5.5C8.5 5.63542 8.45052 5.7526 8.35156 5.85156C8.2526 5.95052 8.13542 6 8 6H3C2.86458 6 2.7474 5.95052 2.64844 5.85156C2.54948 5.7526 2.5 5.63542 2.5 5.5C2.5 5.36458 2.54948 5.2474 2.64844 5.14844C2.7474 5.04948 2.86458 5 3 5H8Z"
                                                          Height="14" 
                                                          Width="14"
                                                          Stretch="Fill"
                                                          VerticalAlignment="Center"
                                                          HorizontalAlignment="Center"
                                                          Fill="{StaticResource IconColor}"/>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                    <syncribbon:RibbonButton x:Name="Part_FitToPage"
                                                             Margin="2,4,0,4"
                                                             Height="24"
                                                             Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FitToPage}"
                                                             SizeForm="Small"
                                                             IconType="Icon"
                                                             Command="diag:DiagramCommands.FitToPage"
                                                             CommandParameter="{StaticResource FitToPageParameter}"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FitToPage}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FitToPageTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Data="M1.96094 12C1.70052 12 1.45052 11.9479 1.21094 11.8438C0.976562 11.7344 0.768229 11.5911 0.585938 11.4141C0.408854 11.2318 0.265625 11.0234 0.15625 10.7891C0.0520833 10.5495 0 10.2995 0 10.0391V1.96094C0 1.70052 0.0520833 1.45312 0.15625 1.21875C0.265625 0.979167 0.408854 0.770833 0.585938 0.59375C0.768229 0.411458 0.976562 0.268229 1.21094 0.164062C1.45052 0.0546875 1.70052 0 1.96094 0H14.0391C14.2995 0 14.5469 0.0546875 14.7812 0.164062C15.0208 0.268229 15.2292 0.411458 15.4062 0.59375C15.5885 0.770833 15.7318 0.979167 15.8359 1.21875C15.9453 1.45312 16 1.70052 16 1.96094V10.0391C16 10.2995 15.9453 10.5495 15.8359 10.7891C15.7318 11.0234 15.5885 11.2318 15.4062 11.4141C15.2292 11.5911 15.0208 11.7344 14.7812 11.8438C14.5469 11.9479 14.2995 12 14.0391 12H1.96094ZM14 11C14.1406 11 14.2708 10.974 14.3906 10.9219C14.5104 10.8698 14.6146 10.7995 14.7031 10.7109C14.7969 10.6172 14.8698 10.5104 14.9219 10.3906C14.974 10.2708 15 10.1406 15 10V2C15 1.86458 14.974 1.73698 14.9219 1.61719C14.8698 1.49219 14.7969 1.38542 14.7031 1.29688C14.6146 1.20312 14.5078 1.13021 14.3828 1.07812C14.263 1.02604 14.1354 1 14 1H2C1.85938 1 1.72917 1.02604 1.60938 1.07812C1.48958 1.13021 1.38281 1.20312 1.28906 1.29688C1.20052 1.38542 1.13021 1.48958 1.07812 1.60938C1.02604 1.72917 1 1.85938 1 2V10C1 10.1406 1.02604 10.2734 1.07812 10.3984C1.13021 10.5182 1.20052 10.6224 1.28906 10.7109C1.3776 10.7995 1.48177 10.8698 1.60156 10.9219C1.72656 10.974 1.85938 11 2 11H14ZM2 4.5V3.5C2 3.29688 2.03906 3.10417 2.11719 2.92188C2.19531 2.73958 2.30208 2.58073 2.4375 2.44531C2.57812 2.30469 2.73958 2.19531 2.92188 2.11719C3.10417 2.03906 3.29688 2 3.5 2H4.5C4.63542 2 4.7526 2.04948 4.85156 2.14844C4.95052 2.2474 5 2.36458 5 2.5C5 2.63542 4.95052 2.7526 4.85156 2.85156C4.7526 2.95052 4.63542 3 4.5 3H3.5C3.39062 3 3.30208 3.02083 3.23438 3.0625C3.16667 3.09896 3.11458 3.15365 3.07812 3.22656C3.04167 3.29427 3.01823 3.375 3.00781 3.46875C2.9974 3.55729 2.99219 3.65104 2.99219 3.75C2.99219 3.83333 2.99479 3.91667 3 4C3.00521 4.08333 3.00781 4.16667 3.00781 4.25C3.00781 4.35417 3 4.45052 2.98438 4.53906C2.97396 4.6276 2.95052 4.70833 2.91406 4.78125C2.88281 4.84896 2.83333 4.90365 2.76562 4.94531C2.69792 4.98177 2.60938 5 2.5 5C2.36458 5 2.2474 4.95052 2.14844 4.85156C2.04948 4.7526 2 4.63542 2 4.5ZM11 2.5C11 2.36458 11.0495 2.2474 11.1484 2.14844C11.2474 2.04948 11.3646 2 11.5 2H12.5C12.7031 2 12.8958 2.03906 13.0781 2.11719C13.2604 2.19531 13.4193 2.30469 13.5547 2.44531C13.6953 2.58073 13.8047 2.73958 13.8828 2.92188C13.9609 3.10417 14 3.29688 14 3.5V4.5C14 4.63542 13.9505 4.7526 13.8516 4.85156C13.7526 4.95052 13.6354 5 13.5 5C13.3646 5 13.2474 4.95052 13.1484 4.85156C13.0495 4.7526 13 4.63542 13 4.5V3.5C13 3.39062 12.9792 3.30208 12.9375 3.23438C12.901 3.16667 12.8464 3.11719 12.7734 3.08594C12.7057 3.04948 12.625 3.02604 12.5312 3.01562C12.4427 3 12.349 2.99219 12.25 2.99219C12.1667 2.99219 12.0833 2.99479 12 3C11.9167 3.00521 11.8333 3.00781 11.75 3.00781C11.6458 3.00781 11.5495 3.0026 11.4609 2.99219C11.3724 2.98177 11.2917 2.95833 11.2188 2.92188C11.151 2.88542 11.0964 2.83333 11.0547 2.76562C11.0182 2.69792 11 2.60938 11 2.5ZM3.5 10C3.29688 10 3.10417 9.96094 2.92188 9.88281C2.73958 9.80469 2.57812 9.69792 2.4375 9.5625C2.30208 9.42188 2.19531 9.26042 2.11719 9.07812C2.03906 8.89583 2 8.70312 2 8.5V7.5C2 7.36458 2.04948 7.2474 2.14844 7.14844C2.2474 7.04948 2.36458 7 2.5 7C2.63542 7 2.7526 7.04948 2.85156 7.14844C2.95052 7.2474 3 7.36458 3 7.5V8.5C3 8.60938 3.01823 8.69792 3.05469 8.76562C3.09635 8.83333 3.15104 8.88542 3.21875 8.92188C3.29167 8.95833 3.3724 8.98177 3.46094 8.99219C3.54948 9.0026 3.64583 9.00781 3.75 9.00781C3.83333 9.00781 3.91667 9.00521 4 9C4.08333 8.99479 4.16667 8.99219 4.25 8.99219C4.34896 8.99219 4.44271 9 4.53125 9.01562C4.625 9.02604 4.70573 9.04948 4.77344 9.08594C4.84635 9.11719 4.90104 9.16667 4.9375 9.23438C4.97917 9.30208 5 9.39062 5 9.5C5 9.63542 4.95052 9.7526 4.85156 9.85156C4.7526 9.95052 4.63542 10 4.5 10H3.5ZM11.5 10C11.3646 10 11.2474 9.95052 11.1484 9.85156C11.0495 9.7526 11 9.63542 11 9.5C11 9.36458 11.0495 9.2474 11.1484 9.14844C11.2474 9.04948 11.3646 9 11.5 9H12.5C12.6094 9 12.6979 8.98177 12.7656 8.94531C12.8333 8.90365 12.8828 8.84896 12.9141 8.78125C12.9505 8.70833 12.974 8.6276 12.9844 8.53906C13 8.45052 13.0078 8.35417 13.0078 8.25C13.0078 8.16667 13.0052 8.08333 13 8C12.9948 7.91667 12.9922 7.83333 12.9922 7.75C12.9922 7.65104 12.9974 7.55729 13.0078 7.46875C13.0182 7.375 13.0417 7.29427 13.0781 7.22656C13.1146 7.15365 13.1667 7.09896 13.2344 7.0625C13.3021 7.02083 13.3906 7 13.5 7C13.6354 7 13.7526 7.04948 13.8516 7.14844C13.9505 7.2474 14 7.36458 14 7.5V8.5C14 8.70312 13.9609 8.89583 13.8828 9.07812C13.8047 9.26042 13.6953 9.42188 13.5547 9.5625C13.4193 9.69792 13.2604 9.80469 13.0781 9.88281C12.8958 9.96094 12.7031 10 12.5 10H11.5Z" 
                                                          Height="12" 
                                                          Width="16"
                                                          Stretch="Fill" 
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center" 
                                                          Fill="{StaticResource IconColor}"/>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>

                                    <syncribbon:RibbonButton x:Name="Part_FitToWidth"
                                                             Margin="2,4,0,4"
                                                             Height="24"
                                                             Label="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FitToWidth}"
                                                             SizeForm="Small"
                                                             IconType="Icon"
                                                             syncribbon:SimplifiedLayoutSettings.DisplayMode="Normal"
                                                             Command="diag:DiagramCommands.FitToPage"
                                                             CommandParameter="{StaticResource FitToWidthParameter}"
                                                             Style="{StaticResource SyncfusionRibbonButtonStyle}">
                                        <syncribbon:RibbonButton.ToolTip>
                                            <syncribbon:ScreenTip Description="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FitToWidth}">
                                                <TextBlock Text="{diagribbon:DiagramRibbonLocalizationResourceExtension  ResourceName=FitToWidthTooltipText}" TextAlignment="Justify" TextWrapping="Wrap" MaxWidth="200"/>
                                            </syncribbon:ScreenTip>
                                        </syncribbon:RibbonButton.ToolTip>
                                        <syncribbon:RibbonButton.IconTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Path Data="M0 1.5C0 0.639641 0.736604 0 1.57143 0H14.4286C15.2634 0 16 0.639642 16 1.5V11.5C16 12.3604 15.2634 13 14.4286 13H1.57143C0.736605 13 0 12.3604 0 11.5V1.5ZM1.57143 1C1.22279 1 1 1.25579 1 1.5V11.5C1 11.7442 1.22279 12 1.57143 12H14.4286C14.7772 12 15 11.7442 15 11.5V1.5C15 1.25579 14.7772 1 14.4286 1H1.57143ZM2.67857 4C2.67857 3.13964 3.41518 2.5 4.25 2.5H5.32143C5.59757 2.5 5.82143 2.72386 5.82143 3C5.82143 3.27614 5.59757 3.5 5.32143 3.5H4.25C3.90136 3.5 3.67857 3.75579 3.67857 4V9C3.67857 9.24421 3.90136 9.5 4.25 9.5H5.32143C5.59757 9.5 5.82143 9.72386 5.82143 10C5.82143 10.2761 5.59757 10.5 5.32143 10.5H4.25C3.41518 10.5 2.67857 9.86036 2.67857 9V4ZM10.1786 3C10.1786 2.72386 10.4024 2.5 10.6786 2.5H11.75C12.5848 2.5 13.3214 3.13964 13.3214 4V9C13.3214 9.86036 12.5848 10.5 11.75 10.5H10.6786C10.4024 10.5 10.1786 10.2761 10.1786 10C10.1786 9.72386 10.4024 9.5 10.6786 9.5H11.75C12.0986 9.5 12.3214 9.24421 12.3214 9V4C12.3214 3.75579 12.0986 3.5 11.75 3.5H10.6786C10.4024 3.5 10.1786 3.27614 10.1786 3Z" 
                                                          Width="16"
                                                          Height="13" 
                                                          Stretch="Fill"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Fill="{StaticResource IconColor}"/>
                                                </Grid>
                                            </DataTemplate>
                                        </syncribbon:RibbonButton.IconTemplate>
                                    </syncribbon:RibbonButton>
                                </syncribbon:RibbonBar>

                            </syncribbon:RibbonTab>

                        </syncribbon:Ribbon>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type diagribbon:SfDiagramRibbon}" BasedOn="{StaticResource SyncfusionSfDiagramRibbonStyle}"/>
</ResourceDictionary>
