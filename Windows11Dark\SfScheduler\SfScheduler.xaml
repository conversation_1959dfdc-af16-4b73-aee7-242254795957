<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib"
                    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:scheduler="clr-namespace:Syncfusion.UI.Xaml.Scheduler;assembly=Syncfusion.SfScheduler.WPF"
                    xmlns:busyIndicator="clr-namespace:Syncfusion.Windows.Controls.Notification;assembly=Syncfusion.SfBusyIndicator.WPF">

    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfScheduler/AgendaViewStyle.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfScheduler/AllDayAppointmentPanelStyle.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfScheduler/AppointmentEditorWindow.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfScheduler/AppointmentStyle.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfScheduler/MonthViewStyle.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfScheduler/SchedulerHeaderStyle.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfScheduler/TimeSlotViewStyle.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfScheduler/ViewHeaderStyle.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <PathGeometry x:Key="OccurrenceIcon">M13.332998,3.8970015 L15.999998,6.5640016 14.011192,6.5640016 13.998763,6.6953416 C13.850624,8.0406933 13.251793,9.3133421 12.267262,10.297188 9.9141897,12.650268 6.0860715,12.650268 3.7339988,10.297188 L4.8000317,9.2311521 C6.5650864,10.996212 9.4361749,10.996212 11.200229,9.2311521 11.905064,8.5263157 12.346754,7.6242282 12.482341,6.6656411 L12.495133,6.5640016 10.666998,6.5640016 z M7.9999189,0 C9.5450282,0 11.090012,0.58827019 12.266001,1.7648103 L11.200011,2.8308467 C9.4350293,1.0667863 6.5640583,1.0667863 4.7990766,2.8308467 4.0785213,3.55209 3.6344495,4.473599 3.5092549,5.4512309 L3.5040073,5.4980003 5.3329997,5.4980003 2.6669998,8.1650004 0,5.4980003 1.9874191,5.4980003 1.9918499,5.4431361 C2.1242676,4.0713655 2.7259102,2.7720321 3.7330875,1.7648103 4.9095755,0.58827019 6.4548097,0 7.9999189,0 z</PathGeometry>
    <PathGeometry x:Key="ChangedOccurrenceIcon">M7.9999192,0 C9.5450284,0 11.090012,0.58826971 12.266001,1.7648098 L11.200011,2.8308461 C9.6280744,1.2597301 7.1788528,1.0878892 5.4150094,2.3153236 L5.3743703,2.3443701 11.686544,8.656544 11.782608,8.5168743 C12.151227,7.9569926 12.389125,7.3246689 12.48234,6.6656399 L12.495132,6.5639997 10.667,6.5639997 13.333,3.8969997 16,6.5639997 14.011191,6.5639997 13.998763,6.6953402 C13.8784,7.7884393 13.460509,8.8335431 12.779953,9.7147398 L12.764228,9.7342277 14,10.97 12.939,12.031 11.703289,10.795177 11.690159,10.805606 C9.3250544,12.635909 5.9023156,12.466436 3.7339994,10.297189 L4.800032,9.2311516 C6.3720334,10.803161 8.8213556,10.975099 10.584565,9.7469668 L10.625729,9.7175174 4.313171,3.404382 4.3111634,3.4070761 C3.8764154,4.0163516 3.6031511,4.7180063 3.5092549,5.45123 L3.5040073,5.4980001 5.3329998,5.4980001 2.6669998,8.1649995 0,5.4980001 1.9874194,5.4980001 1.9918504,5.4431353 C2.0994397,4.3285719 2.5167905,3.2618282 3.2097413,2.3607326 L3.2361002,2.3272126 2.0010006,1.092 3.0610005,0.031000137 4.2969177,1.2669175 4.310167,1.256393 C5.3924582,0.41879749 6.6962333,0 7.9999192,0 z</PathGeometry>

    <DataTemplate x:Key="DayAppointmentControlTemplate">
        <Border 
            x:Name="PART_AppointmentBorder"
            Background="{Binding AppointmentBackground}"
            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
            <Grid x:Name="PART_AppointmentGrid">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBlock x:Name="PART_AppointmentTextBlock"
            Foreground="{Binding Foreground}"
            Margin="5,5,0,0"
            HorizontalAlignment="Stretch"
            Text="{Binding Subject}" TextTrimming="CharacterEllipsis"
            TextWrapping="Wrap" />
                <Path x:Name="PART_RecurrenceIcon"  
                  Fill="#FFFFFFFF"
                  Grid.Column="1"
                  HorizontalAlignment="Right"
                  VerticalAlignment="Bottom"
                  Stretch="Fill"
                  Margin="3" />
            </Grid>
        </Border>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=IsAllDay}" Value="True">
                <Setter TargetName="PART_RecurrenceIcon" Property="VerticalAlignment" Value="Center" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=IsRecursive}" Value="True">
                <Setter TargetName="PART_RecurrenceIcon" Property="Data" Value="{StaticResource OccurrenceIcon}" />
                <Setter TargetName="PART_RecurrenceIcon" Property="Height" Value="12.062" />
                <Setter TargetName="PART_RecurrenceIcon" Property="Width" Value="16" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=Type}" Value="ChangedOccurrence">
                <Setter TargetName="PART_RecurrenceIcon" Property="Data" Value="{StaticResource ChangedOccurrenceIcon}"/>
                <Setter TargetName="PART_RecurrenceIcon" Property="Height" Value="12.062" />
                <Setter TargetName="PART_RecurrenceIcon" Property="Width" Value="16" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=Subject}" Value="{x:Static system:String.Empty}">
                <Setter TargetName="PART_AppointmentTextBlock" Property="TextBlock.Text" Value="{scheduler:SchedulerLocalizationResourceExtension ResourceName=EmptyAppointmentSubject}"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=AppointmentBackground}" Value="{x:Null}">
                <Setter TargetName="PART_AppointmentBorder" Property="Background" Value="{StaticResource PrimaryBackground}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=Foreground}" Value="{x:Null}">
                <Setter TargetName="PART_AppointmentTextBlock" Property="Foreground" Value="{StaticResource PrimaryForeground}" />
            </DataTrigger>
        </DataTemplate.Triggers>

    </DataTemplate>

    <DataTemplate x:Key="MonthAppointmentControlTemplate">
        <Border 
            x:Name="PART_AppointmentBorder"
            Background="{Binding AppointmentBackground}" 
            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
            <Grid x:Name="PART_AppointmentGrid">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBlock x:Name="PART_AppointmentTextBlock"
            Margin="5,0,0,0"
            Foreground="{Binding Foreground}" 
            HorizontalAlignment="Stretch"
            VerticalAlignment="Center"
            Text="{Binding Subject}" TextTrimming="CharacterEllipsis"
            TextWrapping="Wrap" />
                <Path x:Name="PART_RecurrenceIcon"  
                  Fill="#FFFFFFFF"
                  Grid.Column="1"
                  HorizontalAlignment="Right"
                  VerticalAlignment="Center"
                  Stretch="Fill"
                  Margin="3" />
            </Grid>
        </Border>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=IsRecursive}" Value="True">
                <Setter TargetName="PART_RecurrenceIcon" Property="Data" Value="{StaticResource OccurrenceIcon}" />
                <Setter TargetName="PART_RecurrenceIcon" Property="Height" Value="10.062" />
                <Setter TargetName="PART_RecurrenceIcon" Property="Width" Value="14" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=Type}" Value="ChangedOccurrence">
                <Setter TargetName="PART_RecurrenceIcon" Property="Data" Value="{StaticResource ChangedOccurrenceIcon}"/>
                <Setter TargetName="PART_RecurrenceIcon" Property="Height" Value="10.062" />
                <Setter TargetName="PART_RecurrenceIcon" Property="Width" Value="14" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=Subject}" Value="{x:Static system:String.Empty}">
                <Setter TargetName="PART_AppointmentTextBlock" Property="TextBlock.Text" Value="{scheduler:SchedulerLocalizationResourceExtension ResourceName=EmptyAppointmentSubject}"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=AppointmentBackground}" Value="{x:Null}">
                <Setter TargetName="PART_AppointmentBorder" Property="Background" Value="{StaticResource PrimaryBackground}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=Foreground}" Value="{x:Null}">
                <Setter TargetName="PART_AppointmentTextBlock" Property="Foreground" Value="{StaticResource PrimaryForeground}" />
            </DataTrigger>
        </DataTemplate.Triggers>

    </DataTemplate>

    <!--Generic Xaml-->
    <scheduler:SchedulerAppointmentTemplateSelector x:Key="AppointmentSelector" 
                                                DayViewAppointmentTemplate="{StaticResource DayAppointmentControlTemplate}"
                                                MonthViewAppointmentTemplate="{StaticResource MonthAppointmentControlTemplate}"/>

    <Style x:Key="SyncfusionSfSchedulerStyle" TargetType="scheduler:SfScheduler">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
        <Setter Property="AppointmentTemplateSelector" Value="{StaticResource AppointmentSelector}"/>
        <Setter Property="MinHeight" Value="300" />
        <Setter Property="MinWidth" Value="300" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:SfScheduler">
                    <busyIndicator:SfBusyIndicator x:Name="PART_BusyIndicator" 
                                                   AnimationType="DotCircle"
                                                   IsBusy="False">
                        <Border BorderBrush="{TemplateBinding BorderBrush}" 
                                CornerRadius="{StaticResource Windows11Dark.CornerRadius8}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <Grid Background="{TemplateBinding Background}" 
                                  DataContext="{TemplateBinding DataContext}"
                                  x:Name="PART_Root">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="0.7*" />
                                    <RowDefinition />
                                </Grid.RowDefinitions>

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <Border BorderBrush="{TemplateBinding BorderBrush}"
                                    Grid.Row="1" >
                                    <Border.BorderThickness>
                                        <Thickness>0,0,1,0</Thickness>
                                    </Border.BorderThickness>
                                    <Grid x:Name="PART_ResourceGrid">
                                        <Grid.RowDefinitions>
                                            <RowDefinition />
                                            <RowDefinition Height="*" />
                                        </Grid.RowDefinitions>

                                        <Border 
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="0, 0, 0, 1" />
                                        <ScrollViewer x:Name="PART_ResourceScrollViewer" Grid.Row="1"
                                  HorizontalScrollBarVisibility="Auto" 
                                  IsDeferredScrollingEnabled="False" 
                                  VerticalScrollBarVisibility="Hidden">

                                            <scheduler:ResourceHeaderPanel x:Name="PART_ResourcePanel" />

                                            <ScrollViewer.Resources>
                                                <Style TargetType="{x:Type ScrollBar}">
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="{x:Type ScrollBar}">
                                                                <Border Background="Transparent" />
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </ScrollViewer.Resources>
                                        </ScrollViewer>
                                    </Grid>
                                </Border>
                                <scheduler:SchedulerHeaderControl x:Name="PART_ScheduleHeaderControl" MinHeight="40" Height="{TemplateBinding HeaderHeight}" Grid.ColumnSpan="2" />

                                <scheduler:MonthAgendaView x:Name="PART_AgendaListViewControl"
                                                   Grid.Row="2" Grid.Column="1"/>
                            </Grid>
                        </Border>
                    </busyIndicator:SfBusyIndicator>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinmanager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="PART_ScheduleHeaderControl" Property="MinHeight" Value="40"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSfSchedulerStyle}" TargetType="scheduler:SfScheduler"/>
    <scheduler:ScrollPanel x:Key="scrollPanel" />

	<DataTemplate x:Key="ResoureHeaderControlTemplate">
        <Border x:Name="PART_ResourceBorder"
            	BorderBrush="{StaticResource BorderAlt}"
                Background="{Binding Background}" 
                VerticalAlignment="Stretch" 
                HorizontalAlignment="Stretch">
            <TextBlock 
                x:Name="PART_DisplayTextBlock"
                Text="{Binding Name}" 
                Foreground="{Binding Foreground}"
                FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                TextWrapping="Wrap"
                TextTrimming="CharacterEllipsis"
                VerticalAlignment="Center" 
                HorizontalAlignment="Center" />
        </Border>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=Background}" Value="{x:Null}">
                <Setter TargetName="PART_ResourceBorder" Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=Foreground}" Value="{x:Null}">
                <Setter TargetName="PART_DisplayTextBlock" Property="Foreground" Value="{StaticResource ContentForeground}" />
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <Style x:Key="SyncfusionResourceHeaderControlStyle" TargetType="scheduler:ResourceHeaderControl">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="ContentTemplate" Value="{StaticResource ResoureHeaderControlTemplate}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:ResourceHeaderControl">
                    <Border
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <ContentPresenter x:Name="PART_ResourceContentPresenter" 
                         Content="{TemplateBinding DataContext}"
                         ContentTemplate="{TemplateBinding ContentTemplate}">
                        </ContentPresenter>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionResourceHeaderControlStyle}" TargetType="scheduler:ResourceHeaderControl" />

</ResourceDictionary>
