<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	 
    xmlns:classic="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Classic"
    xmlns:syncfusion="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatPrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Button.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/PrimaryButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <RelativeSource
        x:Key="wizardAncestor"
        AncestorType="{x:Type syncfusion:WizardControl}"
        Mode="FindAncestor" />
    <syncfusion:WizardPageVisibility x:Key="pageVisibilityConverter" />
    <syncfusion:VisibilityResolver x:Key="visibilityResolver" />

    <Style x:Key="SyncfusionWizardPageStyle" TargetType="{x:Type syncfusion:WizardPage}">
        <Setter Property="HelpVisibility">
            <Setter.Value>
                <MultiBinding Converter="{StaticResource visibilityResolver}">
                    <Binding Path="HelpVisible" RelativeSource="{StaticResource wizardAncestor}" />
                    <Binding Path="HelpVisible" RelativeSource="{x:Static RelativeSource.Self}" />
                </MultiBinding>
            </Setter.Value>
        </Setter>
        <Setter Property="CancelVisibility">
            <Setter.Value>
                <MultiBinding Converter="{StaticResource visibilityResolver}">
                    <Binding Path="CancelVisible" RelativeSource="{StaticResource wizardAncestor}" />
                    <Binding Path="CancelVisible" RelativeSource="{x:Static RelativeSource.Self}" />
                </MultiBinding>
            </Setter.Value>
        </Setter>
        <Setter Property="BackVisibility">
            <Setter.Value>
                <MultiBinding Converter="{StaticResource visibilityResolver}">
                    <Binding Path="BackVisible" RelativeSource="{StaticResource wizardAncestor}" />
                    <Binding Path="BackVisible" RelativeSource="{x:Static RelativeSource.Self}" />
                </MultiBinding>
            </Setter.Value>
        </Setter>
        <Setter Property="NextVisibility">
            <Setter.Value>
                <MultiBinding Converter="{StaticResource visibilityResolver}">
                    <Binding Path="NextVisible" RelativeSource="{StaticResource wizardAncestor}" />
                    <Binding Path="NextVisible" RelativeSource="{x:Static RelativeSource.Self}" />
                </MultiBinding>
            </Setter.Value>
        </Setter>
        <Setter Property="FinishVisibility">
            <Setter.Value>
                <MultiBinding Converter="{StaticResource visibilityResolver}">
                    <Binding Path="FinishVisible" RelativeSource="{StaticResource wizardAncestor}" />
                    <Binding Path="FinishVisible" RelativeSource="{x:Static RelativeSource.Self}" />
                </MultiBinding>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionWizardNavigationAreaStyle" TargetType="{x:Type syncfusion:WizardNavigationArea}">
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Background" Value="{StaticResource PopupBackground}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness0100}" />
        <Setter Property="Padding" Value="8,4,8,4" />
        <Setter Property="MinHeight" Value="40"/>
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type syncfusion:WizardNavigationArea}">
                    <classic:ClassicBorderDecorator
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        SnapsToDevicePixels="true">
                        <Grid MinHeight="24" Margin="{TemplateBinding Padding}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Button
                                Name="PART_HelpButton"
                                MinWidth="68"
                                Command="syncfusion:WizardCommands.Help"
                                Content="{Binding RelativeSource={StaticResource wizardAncestor}, Path=HelpText}"
                                Style="{StaticResource WPFButtonStyle}"
                                Visibility="{Binding RelativeSource={StaticResource wizardAncestor}, Path=(syncfusion:WizardControl.SelectedWizardPage).(syncfusion:WizardPage.HelpVisibility)}" >
                                <Button.Margin>
                                    <Thickness>8,8,8,8</Thickness>
                                </Button.Margin>
                            </Button>
                            <StackPanel
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal">
                                <Button
                                    Name="PART_CancelButton"
                                    MinWidth="68"
                                    Command="syncfusion:WizardCommands.Cancel"
                                    Content="{Binding RelativeSource={StaticResource wizardAncestor}, Path=CancelText}"
                                    Style="{StaticResource WPFButtonStyle}"
                                    Visibility="{Binding RelativeSource={StaticResource wizardAncestor}, Path=(syncfusion:WizardControl.SelectedWizardPage).(syncfusion:WizardPage.CancelVisibility)}" >
                                    <Button.Margin>
                                        <Thickness>8,8,8,8</Thickness>
                                    </Button.Margin>
                                </Button>
                                <Button
                                    Name="PART_BackButton"
                                    MinWidth="68"
                                    Command="syncfusion:WizardCommands.Previous"
                                    Content="{Binding RelativeSource={StaticResource wizardAncestor}, Path=BackText}"
                                    Style="{StaticResource WPFButtonStyle}"
                                    Visibility="{Binding RelativeSource={StaticResource wizardAncestor}, Path=(syncfusion:WizardControl.SelectedWizardPage).(syncfusion:WizardPage.BackVisibility)}" >
                                    <Button.Margin>
                                        <Thickness>8,8,8,8</Thickness>
                                    </Button.Margin>
                                </Button>
                                <Button
                                    Name="PART_NextButton"
                                    MinWidth="68"
                                    Command="syncfusion:WizardCommands.Next"
                                    Content="{Binding RelativeSource={StaticResource wizardAncestor}, Path=NextText}"
                                    IsDefault="{Binding RelativeSource={StaticResource wizardAncestor}, Path=NextFocused}"
                                    Style="{StaticResource WPFPrimaryButtonStyle}"
                                    Visibility="{Binding RelativeSource={StaticResource wizardAncestor}, Path=(syncfusion:WizardControl.SelectedWizardPage).(syncfusion:WizardPage.NextVisibility)}" >
                                    <Button.Margin>
                                        <Thickness>8,8,8,8</Thickness>
                                    </Button.Margin>
                                </Button>
                                <Button
                                    Name="PART_FinishButton"
                                    MinWidth="68"
                                    Command="syncfusion:WizardCommands.Finish"
                                    Content="{Binding RelativeSource={StaticResource wizardAncestor}, Path=FinishText}"
                                    IsDefault="{Binding RelativeSource={StaticResource wizardAncestor}, Path=FinishFocused}"
                                    Style="{StaticResource WPFPrimaryButtonStyle}"
                                    Visibility="{Binding RelativeSource={StaticResource wizardAncestor}, Path=(syncfusion:WizardControl.SelectedWizardPage).(syncfusion:WizardPage.FinishVisibility)}" >
                                    <Button.Margin>
                                        <Thickness>8,8,8,8</Thickness>
                                    </Button.Margin>
                                </Button>
                            </StackPanel>
                        </Grid>
                    </classic:ClassicBorderDecorator>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{StaticResource Overlay}" />
                            <Setter Property="BorderBrush" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionWizardNavigationAreaStyle}" TargetType="{x:Type syncfusion:WizardNavigationArea}"/>

    <Style BasedOn="{StaticResource SyncfusionWizardPageStyle}" TargetType="{x:Type syncfusion:WizardPage}">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="BannerBackground" Value="{StaticResource ContentBackground}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="IsTabStop" Value="{Binding IsSelected, RelativeSource={RelativeSource Self}, Mode=OneWay}" />
        <Style.Triggers>
            <Trigger Property="PageType" Value="Interior">
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness}" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type syncfusion:WizardPage}">
                            <Grid Visibility="{TemplateBinding IsSelected, Converter={StaticResource pageVisibilityConverter}}">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Grid
                                    Name="PART_Header"
                                    MinHeight="{Binding RelativeSource={StaticResource wizardAncestor}, Path=InteriorPageHeaderMinHeight}"
                                    Background="{TemplateBinding BannerBackground}"
                                    Row="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0" Margin="12">
                                        <TextBlock
                                            Name="title"
                                            FontSize="{StaticResource Windows11Dark.TitleTextStyle}"
                                            FontWeight="{StaticResource Windows11Dark.FontWeightMedium}"
                                            Text="{TemplateBinding Title}"
                                            TextWrapping="Wrap" />
                                        <TextBlock
                                            Name="description"
                                            Margin="12,8,0,0"
                                            Text="{TemplateBinding Description}"
                                            TextWrapping="Wrap" />
                                    </StackPanel>
                                    <Image
                                        Grid.Column="1"
                                        Width="{TemplateBinding BannerImageWidth}"
                                        Height="{TemplateBinding BannerImageHeight}"
                                        Margin="4"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Source="{TemplateBinding BannerImage}"
                                        Stretch="Fill" />
                                </Grid>
                                <classic:ClassicBorderDecorator
                                    Grid.Row="1"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    SnapsToDevicePixels="true">
                                    <Grid Background="{TemplateBinding Background}">
                                        <ContentPresenter
                                            Margin="14"
                                            Content="{TemplateBinding Content}"
                                            ContentTemplate="{TemplateBinding ContentTemplate}"
                                            ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}" />
                                    </Grid>
                                </classic:ClassicBorderDecorator>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="PageType" Value="Exterior">
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness}" />
                <Setter Property="BannerBackground" Value="{StaticResource ContentBackground}" />
                <Setter Property="TextBlock.Foreground" Value="{StaticResource ContentForeground}" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type syncfusion:WizardPage}">
                            <Grid Background="{TemplateBinding Background}" Visibility="{TemplateBinding IsSelected, Converter={StaticResource pageVisibilityConverter}}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <classic:ClassicBorderDecorator
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    SnapsToDevicePixels="true">
                                    <Grid
                                        Name="PART_Header"
                                        Grid.Column="0"
                                        MinWidth="{Binding RelativeSource={StaticResource wizardAncestor}, Path=ExteriorPageBannerImageMinWidth}"
                                        Background="{TemplateBinding BannerBackground}">
                                        <Image
                                            Width="{TemplateBinding BannerImageWidth}"
                                            Height="{TemplateBinding BannerImageHeight}"
                                            HorizontalAlignment="Right"
                                            VerticalAlignment="Top"
                                            Source="{TemplateBinding BannerImage}"
                                            Stretch="Fill" />
                                    </Grid>
                                </classic:ClassicBorderDecorator>
                                <Grid Column="1">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>
                                    <Grid.Margin>
                                        <Thickness>16,24,16,12</Thickness>
                                    </Grid.Margin>
                                    <TextBlock
                                        Name="PART_TitleTextBlock"
                                        Grid.Row="0"
                                        Margin="0,0,0,12"
                                        FontSize="{StaticResource Windows11Dark.TitleTextStyle}"
                                        FontWeight="{StaticResource Windows11Dark.FontWeightMedium}"
                                        Text="{TemplateBinding Title}"
                                        TextWrapping="Wrap" />
                                    <TextBlock
                                        Name="PART_DescriptionTextBlock"
                                        Grid.Row="1"
                                        Margin="0,0,0,12"
                                        Text="{TemplateBinding Description}"
                                        TextWrapping="Wrap" />
                                    <ContentPresenter
                                        Grid.Row="2"
                                        Content="{TemplateBinding Content}"
                                        ContentTemplate="{TemplateBinding ContentTemplate}"
                                        ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}" />
                                </Grid>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="Title" Value="">
                                    <Setter TargetName="PART_TitleTextBlock" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <Trigger Property="Description" Value="">
                                    <Setter TargetName="PART_DescriptionTextBlock" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>

            <Trigger Property="PageType" Value="Blank">
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type syncfusion:WizardPage}">
                            <Grid Background="{TemplateBinding Background}" Visibility="{TemplateBinding IsSelected, Converter={StaticResource pageVisibilityConverter}}">
                                <ContentPresenter
                                    Content="{TemplateBinding Content}"
                                    ContentTemplate="{TemplateBinding ContentTemplate}"
                                    ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}" />
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionWizardControlStyle" TargetType="{x:Type syncfusion:WizardControl}">
        <Setter Property="Background" Value="{StaticResource PopupBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness}" />
        <Setter Property="ExteriorPageBannerImageMinWidth" Value="0" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <Grid SnapsToDevicePixels="True" />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type syncfusion:WizardControl}">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="8">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <ItemsPresenter Name="PART_PagesPresenter" />
                            <syncfusion:WizardNavigationArea Grid.Row="1" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{StaticResource Overlay}" />
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionWizardControlStyle}" TargetType="{x:Type syncfusion:WizardControl}" />

</ResourceDictionary>
