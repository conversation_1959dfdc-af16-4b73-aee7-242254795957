<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  颜色定义  -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#0078D4" />
    <SolidColorBrush x:Key="SecondaryBrush" Color="#106EBE" />
    <SolidColorBrush x:Key="AccentBrush" Color="#005A9E" />
    <SolidColorBrush x:Key="SuccessBrush" Color="#107C10" />
    <SolidColorBrush x:Key="WarningBrush" Color="#FF8C00" />
    <SolidColorBrush x:Key="ErrorBrush" Color="#D13438" />
    <SolidColorBrush x:Key="InfoBrush" Color="#0078D4" />

    <!--  背景色  -->
    <SolidColorBrush x:Key="BackgroundBrush" Color="#FFFFFF" />
    <SolidColorBrush x:Key="SurfaceBrush" Color="#F9F9F9" />
    <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF" />

    <!--  文本颜色  -->
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#323130" />
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#605E5C" />
    <SolidColorBrush x:Key="TextDisabledBrush" Color="#A19F9D" />

    <!--  边框颜色  -->
    <SolidColorBrush x:Key="BorderBrush" Color="#EDEBE9" />
    <SolidColorBrush x:Key="BorderHoverBrush" Color="#C7C7C7" />
    <SolidColorBrush x:Key="BorderFocusBrush" Color="#0078D4" />

    <!--  通用间距  -->
    <Thickness x:Key="SmallMargin">4</Thickness>
    <Thickness x:Key="MediumMargin">8</Thickness>
    <Thickness x:Key="LargeMargin">16</Thickness>
    <Thickness x:Key="ExtraLargeMargin">24</Thickness>

    <Thickness x:Key="SmallPadding">4</Thickness>
    <Thickness x:Key="MediumPadding">8</Thickness>
    <Thickness x:Key="LargePadding">16</Thickness>
    <Thickness x:Key="ExtraLargePadding">24</Thickness>

    <!--  圆角半径  -->
    <CornerRadius x:Key="SmallCornerRadius">2</CornerRadius>
    <CornerRadius x:Key="MediumCornerRadius">4</CornerRadius>
    <CornerRadius x:Key="LargeCornerRadius">8</CornerRadius>

    <!--  字体大小  -->
    <system:Double xmlns:system="clr-namespace:System;assembly=mscorlib" x:Key="SmallFontSize">10</system:Double>
    <system:Double xmlns:system="clr-namespace:System;assembly=mscorlib" x:Key="NormalFontSize">12</system:Double>
    <system:Double xmlns:system="clr-namespace:System;assembly=mscorlib" x:Key="MediumFontSize">14</system:Double>
    <system:Double xmlns:system="clr-namespace:System;assembly=mscorlib" x:Key="LargeFontSize">16</system:Double>
    <system:Double xmlns:system="clr-namespace:System;assembly=mscorlib" x:Key="ExtraLargeFontSize">20</system:Double>
    <system:Double xmlns:system="clr-namespace:System;assembly=mscorlib" x:Key="TitleFontSize">24</system:Double>

    <!--  卡片样式  -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource CardBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}" />
        <Setter Property="Padding" Value="{StaticResource LargePadding}" />
        <Setter Property="Margin" Value="{StaticResource MediumMargin}" />
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect
                    BlurRadius="8"
                    Opacity="0.1"
                    ShadowDepth="2"
                    Color="Black" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  标题样式  -->
    <Style x:Key="TitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="{StaticResource TitleFontSize}" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="Margin" Value="{StaticResource MediumMargin}" />
    </Style>

    <!--  副标题样式  -->
    <Style x:Key="SubtitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="{StaticResource LargeFontSize}" />
        <Setter Property="FontWeight" Value="Medium" />
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="Margin" Value="{StaticResource SmallMargin}" />
    </Style>

    <!--  正文样式  -->
    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}" />
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="TextWrapping" Value="Wrap" />
        <Setter Property="LineHeight" Value="20" />
    </Style>

    <!--  说明文字样式  -->
    <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}" />
        <Setter Property="TextWrapping" Value="Wrap" />
    </Style>

    <!--  主要按钮样式  -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}" />
        <Setter Property="FontWeight" Value="Medium" />
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}" />
        <Setter Property="Foreground" Value="White" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Padding" Value="16,8" />
        <Setter Property="Margin" Value="{StaticResource SmallMargin}" />
        <Setter Property="MinWidth" Value="80" />
        <Setter Property="MinHeight" Value="32" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        CornerRadius="{StaticResource MediumCornerRadius}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource SecondaryBrush}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource AccentBrush}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{StaticResource TextDisabledBrush}" />
                            <Setter Property="Foreground" Value="White" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  次要按钮样式  -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="16,8" />
        <Setter Property="Margin" Value="{StaticResource SmallMargin}" />
        <Setter Property="MinWidth" Value="80" />
        <Setter Property="MinHeight" Value="32" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{StaticResource MediumCornerRadius}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryBrush}" />
                            <Setter Property="Foreground" Value="White" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource AccentBrush}" />
                            <Setter Property="Foreground" Value="White" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="BorderBrush" Value="{StaticResource TextDisabledBrush}" />
                            <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  输入框样式  -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="Margin" Value="{StaticResource SmallMargin}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Background" Value="White" />
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="MinHeight" Value="36" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{StaticResource MediumCornerRadius}">
                        <ScrollViewer
                            x:Name="PART_ContentHost"
                            Margin="{TemplateBinding Padding}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource BorderHoverBrush}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource BorderFocusBrush}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{StaticResource SurfaceBrush}" />
                            <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  标签样式  -->
    <Style x:Key="ModernLabelStyle" TargetType="Label">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}" />
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="Padding" Value="0,0,0,4" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <!--  分组框样式  -->
    <Style x:Key="ModernGroupBoxStyle" TargetType="GroupBox">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="FontWeight" Value="Medium" />
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="{StaticResource LargePadding}" />
        <Setter Property="Margin" Value="{StaticResource MediumMargin}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GroupBox">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Border
                            Grid.Row="0"
                            Padding="12,8"
                            Background="{StaticResource SurfaceBrush}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="1,1,1,0"
                            CornerRadius="4,4,0,0">
                            <ContentPresenter ContentSource="Header" />
                        </Border>
                        <Border
                            Grid.Row="1"
                            Padding="{TemplateBinding Padding}"
                            Background="White"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="1,0,1,1"
                            CornerRadius="0,0,4,4">
                            <ContentPresenter />
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  状态栏样式  -->
    <Style x:Key="ModernStatusBarStyle" TargetType="StatusBar">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}" />
        <Setter Property="BorderThickness" Value="0,1,0,0" />
        <Setter Property="Height" Value="28" />
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
    </Style>

</ResourceDictionary>
