@echo off
echo Starting AirMonitor License Generator...
echo.

cd /d "%~dp0"

if not exist "src\AirMonitor.LicenseGenerator\AirMonitor.LicenseGenerator.csproj" (
    echo Error: Project file not found!
    echo Please make sure you are running this script from the repository root.
    pause
    exit /b 1
)

echo Building project...
dotnet build src\AirMonitor.LicenseGenerator --configuration Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Starting License Generator...
dotnet run --project src\AirMonitor.LicenseGenerator --configuration Release

pause
