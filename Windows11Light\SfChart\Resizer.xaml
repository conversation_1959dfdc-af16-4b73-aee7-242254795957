<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="SyncfusionResizerStyle" TargetType="local:Resizer">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:Resizer">
                    <Grid x:Name="mainGrid"
                          Background="Transparent">
                        <Grid.Resources>
                           
                            <Style x:Key="ThumbStyle1"
                                   TargetType="Thumb">
                                <Setter Property="IsTabStop"
                                        Value="False" />
                                <Setter Property="Width"
                                        Value="5" />
                                <Setter Property="Height"
                                        Value="5" />
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Thumb">
                                            <Grid>
                                                <VisualStateManager.VisualStateGroups>
                                                    <VisualStateGroup x:Name="CommonStates">
                                                        <VisualState x:Name="Normal" />
                                                        <VisualState x:Name="MouseOver">
                                                            <Storyboard>
                                                                <ObjectAnimationUsingKeyFrames Duration="0"
                                                                                               Storyboard.TargetProperty="Fill"
                                                                                               Storyboard.TargetName="BackgroundGradient">
                                                                    <ObjectAnimationUsingKeyFrames.KeyFrames>
                                                                        <DiscreteObjectKeyFrame Value="{StaticResource PrimaryColorLight1}"></DiscreteObjectKeyFrame>
                                                                    </ObjectAnimationUsingKeyFrames.KeyFrames>
                                                                </ObjectAnimationUsingKeyFrames>
                                                            </Storyboard>
                                                        </VisualState>
                                                        <VisualState x:Name="Pressed">
                                                            <Storyboard>
                                                                <ObjectAnimationUsingKeyFrames Duration="0"
                                                                                               Storyboard.TargetProperty="Fill"
                                                                                               Storyboard.TargetName="BackgroundGradient">
                                                                    <ObjectAnimationUsingKeyFrames.KeyFrames>
                                                                        <DiscreteObjectKeyFrame Value="{StaticResource PrimaryColorDark1}"></DiscreteObjectKeyFrame>
                                                                    </ObjectAnimationUsingKeyFrames.KeyFrames>
                                                                </ObjectAnimationUsingKeyFrames>
                                                            </Storyboard>
                                                        </VisualState>
                                                        <VisualState x:Name="Disabled">
                                                            <Storyboard>
                                                                <DoubleAnimation Duration="0"
                                                                                 To=".55"
                                                                                 Storyboard.TargetProperty="Opacity"
                                                                                 Storyboard.TargetName="DisabledVisualElement" />
                                                            </Storyboard>
                                                        </VisualState>
                                                    </VisualStateGroup>
                                                    <VisualStateGroup x:Name="FocusStates">
                                                        <VisualState x:Name="Focused">
                                                            <Storyboard>
                                                                <DoubleAnimation Duration="0"
                                                                                 To="1"
                                                                                 Storyboard.TargetProperty="Opacity"
                                                                                 Storyboard.TargetName="FocusVisualElement" />
                                                            </Storyboard>
                                                        </VisualState>
                                                        <VisualState x:Name="Unfocused" />
                                                    </VisualStateGroup>
                                                </VisualStateManager.VisualStateGroups>
                                                <Grid>
                                                    <Rectangle x:Name="BackgroundGradient"
                                                               Fill="{StaticResource PrimaryBackground}"
                                                               Stroke="{StaticResource ContentBackground}"
                                                               Height="{TemplateBinding Height}"
                                                               Width="{TemplateBinding Width}" />
                                                </Grid>
                                                <Rectangle x:Name="DisabledVisualElement"
                                                           Fill="#FFFFFFFF"
                                                           IsHitTestVisible="false"
                                                           Opacity="0"
                                                           RadiusY="2"
                                                           RadiusX="2" />
                                                <Rectangle x:Name="FocusVisualElement"
                                                           IsHitTestVisible="false"
                                                           Margin="1"
                                                           Opacity="0"
                                                           RadiusY="1"
                                                           RadiusX="1"
                                                           Stroke="#FF6DBDD1"
                                                           StrokeThickness="1" />
                                            </Grid>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                            <Style x:Key="roundthumbstyle"
                                   TargetType="Thumb">
                                <Setter Property="IsTabStop"
                                        Value="False" />
                                <Setter Property="Width"
                                        Value="10" />
                                <Setter Property="Height"
                                        Value="10" />
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Thumb">
                                            <Grid>
                                                <VisualStateManager.VisualStateGroups>
                                                    <VisualStateGroup x:Name="CommonStates">
                                                        <VisualState x:Name="Normal" />
                                                        <VisualState x:Name="MouseOver">
                                                            <Storyboard>
                                                                <ObjectAnimationUsingKeyFrames Duration="0"
                                                                                               Storyboard.TargetProperty="Fill"
                                                                                               Storyboard.TargetName="BackgroundGradient">
                                                                    <ObjectAnimationUsingKeyFrames.KeyFrames>
                                                                        <DiscreteObjectKeyFrame Value="{StaticResource PrimaryColorLight1}"></DiscreteObjectKeyFrame>
                                                                    </ObjectAnimationUsingKeyFrames.KeyFrames>
                                                                </ObjectAnimationUsingKeyFrames>
                                                            </Storyboard>
                                                        </VisualState>
                                                        <VisualState x:Name="Pressed">
                                                            <Storyboard>
                                                                <ObjectAnimationUsingKeyFrames Duration="0"
                                                                                               Storyboard.TargetProperty="Fill"
                                                                                               Storyboard.TargetName="BackgroundGradient">
                                                                    <ObjectAnimationUsingKeyFrames.KeyFrames>
                                                                        <DiscreteObjectKeyFrame Value="{StaticResource PrimaryColorLight1}"></DiscreteObjectKeyFrame>
                                                                    </ObjectAnimationUsingKeyFrames.KeyFrames>
                                                                </ObjectAnimationUsingKeyFrames>
                                                            </Storyboard>
                                                        </VisualState>
                                                    </VisualStateGroup>
                                                </VisualStateManager.VisualStateGroups>
                                                <Grid>
                                                    <Ellipse x:Name="BackgroundGradient"
                                                             Fill="{StaticResource PrimaryBackground}"
                                                             Stroke="{StaticResource ContentBackground}"
                                                             Width="{TemplateBinding Width}"
                                                             Height="{TemplateBinding Height}" />
                                                </Grid>
                                            </Grid>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Grid.Resources>
                        <Grid x:Name="horizontalResizer">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="0"></RowDefinition>
                                <RowDefinition Height="0"></RowDefinition>
                                <RowDefinition Height="0"></RowDefinition>
                                <RowDefinition Height="*"></RowDefinition>
                                <RowDefinition Height="0"></RowDefinition>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="0"></ColumnDefinition>
                                <ColumnDefinition Width="*"></ColumnDefinition>
                                <ColumnDefinition Width="0"></ColumnDefinition>
                            </Grid.ColumnDefinitions>
                            <Rectangle Grid.Row="2"
                                       Grid.Column="0"
                                       Margin="-0.5"
                                       Grid.RowSpan="3"
                                       Fill="{StaticResource PrimaryBackground}"
                                       Width="1"></Rectangle>
                            <Rectangle Grid.Row="2"
                                       Grid.Column="0"
                                       Margin="-0.5"
                                       Grid.ColumnSpan="3"
                                       Fill="{StaticResource PrimaryBackground}"
                                       Height="1"></Rectangle>
                            <Rectangle Grid.Row="2"
                                       Grid.Column="3"
                                       Margin="-0.5"
                                       Grid.RowSpan="3"
                                       Fill="{StaticResource PrimaryBackground}"
                                       Width="1"></Rectangle>
                            <Rectangle Grid.Row="4"
                                       Grid.Column="0"
                                       Margin="-0.5"
                                       Grid.ColumnSpan="3"
                                       Fill="{StaticResource PrimaryBackground}"
                                       Height="1"></Rectangle>

                            <Thumb x:Name="resizeTopLeft"
                                   Grid.Row="2"
                                   Margin="-5"
                                   Grid.Column="0"
                                   Style="{StaticResource roundthumbstyle}"></Thumb>
                            <Thumb x:Name="resizeMiddleRight"
                                   Grid.Row="3"
                                   Margin="-5"
                                   Grid.Column="0"
                                   Width="10"
                                   Height="10"
                                   Style="{StaticResource ThumbStyle1}"></Thumb>
                            <Thumb x:Name="resizeBottomLeft"
                                   Grid.Row="4"
                                   Margin="-5"
                                   Grid.Column="0"
                                   Width="10"
                                   Height="10"
                                   Style="{StaticResource roundthumbstyle}"></Thumb>
                            <Thumb x:Name="resizeTopMiddle"
                                   Grid.Row="2"
                                   Margin="-5"
                                   Grid.Column="1"
                                   Width="10"
                                   Height="10"
                                   Style="{StaticResource ThumbStyle1}"></Thumb>
                            <Thumb x:Name="resizeBottomMiddle"
                                   Grid.Row="4"
                                   Margin="-5"
                                   Grid.Column="1"
                                   Width="10"
                                   Height="10"
                                   Style="{StaticResource ThumbStyle1}"></Thumb>
                            <Thumb x:Name="resizeBottomRight"
                                   Grid.Row="4"
                                   Margin="-5"
                                   Grid.Column="2"
                                   Width="10"
                                   Height="10"
                                   Style="{StaticResource roundthumbstyle}"></Thumb>
                            <Thumb x:Name="resizeTopRight"
                                   Grid.Row="2"
                                   Margin="-5"
                                   Grid.Column="2"
                                   Width="10"
                                   Height="10"
                                   Style="{StaticResource roundthumbstyle}"></Thumb>
                            <Thumb x:Name="resizeMiddleLeft"
                                   Grid.Row="3"
                                   Margin="-5"
                                   Grid.Column="2"
                                   Width="10"
                                   Height="10"
                                   Style="{StaticResource ThumbStyle1}"></Thumb>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="local:Resizer" BasedOn="{StaticResource SyncfusionResizerStyle}"></Style>
</ResourceDictionary>
