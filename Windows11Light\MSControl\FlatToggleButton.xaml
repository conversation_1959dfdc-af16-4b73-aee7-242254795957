<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:po="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="ToggleButton.MouseOver.Border" Color="#179bd7"/>

    <SolidColorBrush x:Key="ToggleButton.Checked.Border" Color="LightGray"/>

    <LinearGradientBrush x:Key="ButtonBorderBrushHovered"  MappingMode="Absolute"  StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5" />
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5"  Color="{StaticResource SecondaryBorderGradient.Color}" />
            <GradientStop Offset="1.0"   Color="{StaticResource SecondaryBorder.Color}" />
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ButtonBorderBrushChecked"  MappingMode="Absolute"  StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5" />
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5"  Color="{StaticResource SecondaryBorderGradient.Color}" />
            <GradientStop Offset="1.0"   Color="{StaticResource SecondaryBorder.Color}" />
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="WPFFlatToggleButtonStyle"
           TargetType="{x:Type ToggleButton}">
        <Setter Property="FocusVisualStyle" 
                Value="{x:Null}"/>
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="Background"                                
                Value="Transparent"/>
        <Setter Property="BorderBrush"
                Value="Transparent"/>
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Light.BorderThickness2}"/>
        <Setter Property="Foreground"
                Value="{StaticResource SecondaryForeground}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="HorizontalContentAlignment"
                Value="Center"/>
        <Setter Property="VerticalContentAlignment"
                Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border x:Name="border" 
                            SnapsToDevicePixels="true" 
                            Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}">
                        <ContentPresenter x:Name="contentPresenter" Margin="{TemplateBinding Padding}" 
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          RecognizesAccessKey="True" 
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="Button.IsDefaulted" Value="true">
                            <Setter Property="BorderBrush" TargetName="border"  Value="{StaticResource BorderAlt3}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default"/>
                                <Condition Property="IsFocused" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource SecondaryBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt3}"/>
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" TargetName="border"  Value="{StaticResource ButtonBorderBrushHovered}"/>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource SecondaryBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource SecondaryBackgroundSelected}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource SecondaryBorderSelected}"/>
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundSelected}"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource ButtonBorderBrushChecked}"/>
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SecondaryForeground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource SecondaryBackgroundDisabled}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource SecondaryBorderDisabled}"/>
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundDisabled}"/>
                            <Setter Property="Opacity" Value="1"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--Used in SfRichTextBoxAdv to highlight the border around when Ischecked is true-->
    <Style x:Key="WPFHighlightBorderToggleButtonStyle"
           TargetType="{x:Type ToggleButton}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="Background"                                
                Value="Transparent"/>
        <Setter Property="BorderBrush"
                Value="Transparent" />
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="Foreground"
                Value="{StaticResource SecondaryForeground}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="HorizontalContentAlignment"
                Value="Center"/>
        <Setter Property="VerticalContentAlignment"
                Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border x:Name="border" 
                            SnapsToDevicePixels="true" 
                            Background="{TemplateBinding Background}"
                            BorderThickness="2"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            >
                        <ContentPresenter x:Name="contentPresenter" Margin="3" 
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          RecognizesAccessKey="True" 
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinSize}"/>
                        </Trigger>
                        <Trigger Property="Button.IsDefaulted" Value="true">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt3}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default"/>
                                <Condition Property="IsFocused" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt3}"/>
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush"  TargetName="border"   Value="{StaticResource ButtonBorderBrushHovered}" />
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource SecondaryBorderSelected}"/>
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundSelected}"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource ButtonBorderBrushChecked}"/>
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SecondaryForeground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource SecondaryBorderDisabled}"/>
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundDisabled}"/>
                            <Setter Property="Opacity" Value="1"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary>
