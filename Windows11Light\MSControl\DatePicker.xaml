<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"  
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Calendar.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="DatePicker.Static.Border" Color="Green"/>

    <SolidColorBrush x:Key="DatePicker.MouseOver.Border" Color="AliceBlue"/>

    <SolidColorBrush x:Key="DatePicker.Focused.Border" Color="Orange"/>

    <LinearGradientBrush x:Key="DatePickerBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DatePickerBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DatePickerBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="WPFDatepickerCalendarStyle" BasedOn="{StaticResource WPFCalendarStyle}" TargetType="{x:Type Calendar}">
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Calendar}">
                    <StackPanel x:Name="PART_Root" HorizontalAlignment="Center">
                        <Border Effect="{StaticResource Default.ShadowDepth4}"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                BorderBrush="{StaticResource BorderAlt}"
                                Background="{StaticResource PopupBackground}"
                                Margin="12,0,12,16">
                            <CalendarItem x:Name="PART_CalendarItem"
                                      BorderBrush="{TemplateBinding BorderBrush}"
                                      BorderThickness="{TemplateBinding BorderThickness}"
                                      Background="{TemplateBinding Background}"
                                      Style="{TemplateBinding CalendarItemStyle}"/>
                        </Border>
                    </StackPanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFDatePickerTextBoxStyle" TargetType="{x:Type DatePickerTextBox}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="SelectionBrush" Value="{StaticResource PrimaryBackground}"/>
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DatePickerTextBox}">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0"/>
                                    <VisualTransition GeneratedDuration="0:0:0.1" To="MouseOver"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="Disabled">
                                </VisualState>
                                <VisualState x:Name="MouseOver">
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="WatermarkStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Unwatermarked"/>
                                <VisualState x:Name="Watermarked">
                                    <Storyboard>
                                        <DoubleAnimation Duration="0" To="0" Storyboard.TargetProperty="Opacity" Storyboard.TargetName="ContentElement"/>
                                        <DoubleAnimation Duration="0" To="1" Storyboard.TargetProperty="Opacity" Storyboard.TargetName="PART_Watermark"/>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="FocusStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Unfocused"/>
                                <VisualState x:Name="Focused">
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="Border" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                Background="{TemplateBinding Background}" 
                                CornerRadius="{StaticResource Windows11Light.ThemeCornerRadiusVariant1}" 
                                Opacity="1" 
                                Padding="{TemplateBinding Padding}">
                            <Grid x:Name="WatermarkContent" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                <Border x:Name="ContentElement"/>
                                <Border x:Name="watermark_decorator" Padding="{TemplateBinding Padding}">
                                    <ContentControl x:Name="PART_Watermark" TextElement.Foreground="{StaticResource PlaceholderForeground}" TextElement.FontFamily="{StaticResource Windows11Light.ThemeFontFamily}" Focusable="False" IsHitTestVisible="False" Opacity="0" Padding="2"/>
                                </Border>
                                <ScrollViewer x:Name="PART_ContentHost" Background="Transparent" HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="0" VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}">
                                    <ScrollViewer.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                    </ScrollViewer.Resources>
                                </ScrollViewer>
                            </Grid>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource WPFDatePickerTextBoxStyle}" TargetType="{x:Type DatePickerTextBox}"/>

    <Style x:Key="WPFDatePickerStyle" TargetType="{x:Type DatePicker}">
        <Setter Property="IsTodayHighlighted" Value="True"/>
        <Setter Property="CalendarStyle" Value="{StaticResource WPFDatepickerCalendarStyle}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="SelectedDateFormat" Value="Short"/>
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="BorderBrush"  Value="{StaticResource DatePickerBorderBrush}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}"/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DatePicker}">
                    <Border x:Name="focusBorder" BorderBrush="Transparent"
                            BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.ThemeCornerRadiusVariant1}">
                        <Border x:Name="PART_Border" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}" 
                                Background="{TemplateBinding Background}" 
                                Padding="{TemplateBinding Padding}"
                                SnapsToDevicePixels="True">
                            <VisualStateManager.VisualStateGroups>
                                <VisualStateGroup x:Name="CommonStates">
                                    <VisualState x:Name="Normal"/>
                                    <VisualState x:Name="Disabled">
                                    </VisualState>
                                </VisualStateGroup>
                            </VisualStateManager.VisualStateGroups>
                            <Grid x:Name="PART_Root" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="24"/>
                                </Grid.ColumnDefinitions>
                                <Grid.Resources>
                                    <Style x:Key="WPFDatePickerButtonStyle"
                                            BasedOn="{StaticResource WPFGlyphFlatButtonStyle}"
                                            TargetType="{x:Type Button}">
                                        <Setter Property="Content">
                                            <Setter.Value>
                                                <TextBlock Text="&#xe700;" 
                                                           Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                                                           FontSize="14"
                                                           FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Grid.Resources>
                                <Button x:Name="PART_Button" Grid.Column="1" Focusable="False" HorizontalAlignment="Center" Padding="2" Style="{StaticResource WPFDatePickerButtonStyle}" Grid.Row="0" VerticalAlignment="Center">
                                </Button>
                                <DatePickerTextBox x:Name="PART_TextBox" Grid.Column="0" Focusable="{TemplateBinding Focusable}" FontFamily="{TemplateBinding FontFamily}" FontSize="{TemplateBinding FontSize}" FontWeight="{TemplateBinding FontWeight}" Padding="2,0,0,0" Foreground="{TemplateBinding Foreground}" VerticalAlignment="Center" HorizontalContentAlignment="Stretch" Style="{StaticResource WPFDatePickerTextBoxStyle}" Grid.Row="0" VerticalContentAlignment="Stretch">
                                </DatePickerTextBox>
                                <Grid x:Name="PART_DisabledVisual" Grid.ColumnSpan="2" Grid.Column="0" IsHitTestVisible="False" Opacity="0" Grid.Row="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Rectangle Grid.Column="0" Fill="{StaticResource ContentBackgroundAlt6}" RadiusY="1" Grid.Row="0" RadiusX="1"/>
                                    <Rectangle Grid.Column="1" 
                                               Fill="{StaticResource ContentBackgroundAlt6}" 
                                               Height="18" 
                                               RadiusY="1" 
                                               Grid.Row="0" 
                                               RadiusX="1" 
                                               Width="19"
                                               Margin="3,3,3,3"/>
                                    <Popup x:Name="PART_Popup" AllowsTransparency="True" Placement="Bottom" PlacementTarget="{Binding ElementName=focusBorder}" StaysOpen="False">
                                    </Popup>
                                </Grid>
                            </Grid>
                        </Border>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="MinHeight" TargetName="PART_Button" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="MinWidth" TargetName="PART_Button" Value="{StaticResource TouchMode.MinSize}"/>
                        </Trigger>                        
                        <DataTrigger Binding="{Binding Source={x:Static SystemParameters.HighContrast}}" Value="false">
                            <Setter Property="Foreground" TargetName="PART_TextBox" Value="{Binding Foreground, RelativeSource={RelativeSource TemplatedParent}}"/>
                        </DataTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" TargetName="PART_Border" Value="{StaticResource DatePickerBorderBrushHovered}"/>
                            <Setter Property="Background" TargetName="PART_Border" Value="{StaticResource ContentBackgroundAlt5}"/>
                            <Setter Property="Foreground" TargetName="PART_TextBox" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="CaretBrush" TargetName="PART_TextBox" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="BorderThickness" TargetName="PART_Button" Value="{StaticResource Windows11Light.BorderThickness}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="BorderBrush" TargetName="PART_Border" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Background" TargetName="PART_Border" Value="{StaticResource ContentBackgroundAlt6}"/>
                            <Setter Property="Foreground" TargetName="PART_TextBox" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                        <Trigger Property="IsOpen" SourceName="PART_Popup" Value="True">
                            <Setter Property="BorderThickness" TargetName="PART_Border" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                            <Setter Property="Foreground" TargetName="PART_TextBox" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="BorderBrush" TargetName="PART_Border" Value="{StaticResource DatePickerBorderBrushFocused}"/>
                            <Setter Property="Background" TargetName="PART_Border" Value="{StaticResource ContentBackground}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" SourceName="PART_TextBox" Value="True">
                            <Setter Property="BorderThickness" TargetName="PART_Border" Value="{StaticResource Windows11Light.BorderThickness1112}"/>
                            <Setter Property="Foreground" TargetName="PART_TextBox" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="BorderBrush" TargetName="PART_Border" Value="{StaticResource DatePickerBorderBrushFocused}"/>
                            <Setter Property="Background" TargetName="PART_Border" Value="{StaticResource ContentBackground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource WPFDatePickerStyle}" TargetType="{x:Type DatePicker}"/>

</ResourceDictionary>
