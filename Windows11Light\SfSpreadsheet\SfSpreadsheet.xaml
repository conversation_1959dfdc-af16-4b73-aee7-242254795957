<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:spreadsheet="clr-namespace:Syncfusion.UI.Xaml.Spreadsheet;assembly=Syncfusion.SfSpreadsheet.WPF"
	
    xmlns:system="clr-namespace:System;assembly=mscorlib" 
    xmlns:theme="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:tools="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
    xmlns:microsoftWindowsThemes="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Luna"
    xmlns:converter="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/ChromelessWindow/ChromelessWindow.xaml"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/SfSpreadsheet/SfSpreadsheetIcons.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/TabControlExt/TabControlExt.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ToggleButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ComboBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/PrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatPrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GroupBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBlock.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/PasswordBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.SfSpreadsheet.WPF;component/Commands/Windows/CommonResources.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!--Window common resource-->

    <tools:CloseButtonTypeToVisibilityConverter x:Key="CloseButtonTypeToVisibilityConverter"/>
    <converter:BooleanToVisibilityConverterEx x:Key="BooleanToVisibilityConverterEx"/>
    <tools:ImageAlignmentToDockConverter x:Key="ImageAlignmentToDockConverter"/>
    <spreadsheet:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />

    <spreadsheet:ColorConverter x:Key="colorConverter" />
    <spreadsheet:StringToFontStyleConverter x:Key="stringToFontStyleConverter"/>
    <spreadsheet:StringToFontWeightConverter x:Key="stringToFontWeightConverter"/>
    <spreadsheet:StringToFontFamilyConverter x:Key="stringToFontFamilyConverter"/>
    <spreadsheet:StringToFontSizeConverter x:Key="stringToFontSizeConverter"/>
    
    <Style x:Key="SyncfusionNewButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0 0 0 1"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="Foreground" Value="{StaticResource IconColor}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border 
                        x:Name="NewButtonBorder" 
                        CornerRadius="0"
                        Background="{TemplateBinding Background}" 
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Padding="{TemplateBinding Padding}"
                        SnapsToDevicePixels="true">
                        <ContentPresenter x:Name="contentPresenter" Focusable="False"  RecognizesAccessKey="True" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinmanager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource IconColorHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource IconColorSelected}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="NewButtonBorder" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="NewButtonBorder" Property="Background" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionSpreadsheetTabPanelAdvStyle" TargetType="{x:Type tools:TabPanelAdv}"
			BasedOn="{StaticResource SyncfusionTabControlExtTabPanelAdvStyle}">
        <Setter Property="Padding" Value="0,2,0,0"/>
        <Setter Property="Margin" Value="0,2,0,0"/>
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools:TabPanelAdv}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"></RowDefinition>
                            <RowDefinition Height="1"></RowDefinition>
                        </Grid.RowDefinitions>
                        <Border Name="Bord" Grid.Row="0" Background="{Binding TabPanelBackground,RelativeSource={RelativeSource FindAncestor,AncestorType={x:Type tools:TabControlExt}},FallbackValue=Transparent}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
							Padding="{TemplateBinding Padding}"
                            Margin="{TemplateBinding Margin}"
							CornerRadius="{TemplateBinding CornerRadius}"
                            HorizontalAlignment="Stretch" VerticalAlignment="Top">
                            <DockPanel LastChildFill="False" Margin="0,-2,0,0">
                                <Border Name="BottomBorder" Height="0" DockPanel.Dock="Bottom" Background="Transparent"/>
                                <ContentPresenter x:Name="PART_CustomItems"  AllowDrop="False" ContentSource="TabPanelItem"
                                              MaxHeight="{Binding Path=ActualHeight, ElementName=PART_TabItems}" Margin="2,0,0,0" DockPanel.Dock="Right" 
                                              Content="{Binding Path=TabPanelItem, Mode=OneWay, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:TabControlExt}},FallbackValue=''}">
                                </ContentPresenter>

                                <Button Name="PART_CloseButton" Width="16" Height="16" Margin="0,0,8,0"
                                  DockPanel.Dock="Right"
                                  Focusable="False" 
                                  AllowDrop="False"
                                  Visibility="{Binding Path=CloseButtonType, Converter={StaticResource CloseButtonTypeToVisibilityConverter}, Mode=OneWay, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:TabControlExt}},FallbackValue=Collapsed}"
							      Style="{StaticResource TabPanelCaptionButtonsStyle}"                                  
                                  Command="tools:TabControlCommands.CloseCurrentTabItem">
                                    <Button.Content>
                                        <Path
                                        x:Name="CloseButtonPath"
                                        Width="8"
                                        Height="8"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Fill="{Binding Foreground, ElementName=PART_CloseButton}"
                                        SnapsToDevicePixels="True"
                                        Stretch="Fill" >
                                            <Path.Data>
                                                <PathGeometry>M0.70800017,0 L4.498001,3.7964015 8.2880024,0 8.9960006,0.70600033 5.2044057,4.5039992 8.9960006,8.3019981 8.2880024,9.0079994 4.498001,5.2115974 0.70800017,9.0079994 0,8.3019981 3.7915958,4.5039992 0,0.70600033 z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </Button.Content>
                                </Button>
                                <Button Name="PART_MenuButton" Width="16" Height="16" Margin="0,0,8,0"
                                  DockPanel.Dock="Right"
                                  AllowDrop="False"
                                  Focusable="False"
                                  Visibility="{Binding Path=ShowTabListContextMenu, Converter={StaticResource BooleanToVisibilityConverterEx}, Mode=OneWay, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:TabControlExt}},FallbackValue=Collapsed}"
							      Style="{StaticResource TabPanelCaptionButtonsStyle}"
                                  Command="tools:TabControlCommands.OpenContextMenu">
                                    <Button.Content>
                                        <Path
                                        x:Name="MenuButtonPath"
                                        Width="8"
                                        Height="4"
                                        Fill="{Binding Foreground, ElementName=PART_MenuButton}"
                                        RenderTransformOrigin="0.5,0.5"
                                        SnapsToDevicePixels="True"
                                        Stretch="Fill" >
                                            <Path.Data>
                                                <PathGeometry>M0.74499548,0 L5.0119957,4.7700001 9.2630047,0.017000169 10.008001,0.68400005 5.0119957,6.2700001 0,0.66699985 z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </Button.Content>
                                </Button>

                                <Button Name="PART_LastTab" Focusable="False" 
                                        Width="16"
                                        MinHeight="16"
										Margin="0,0,8,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        DockPanel.Dock="Left" Tag="LastTab" 
                                        Visibility="Collapsed" 
                                        Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools:TabControlExt}}}">
                                    <Button.Content>
                                        <Path
                                        Width="8"
                                        Height="8"
                                        Fill="{Binding Foreground, ElementName=PART_LastTab}"
                                        SnapsToDevicePixels="True"
                                        Stretch="Fill" >
                                            <Path.Data>
                                                <PathGeometry>M0.69400204,0.0059965644 L9.0010006,8.0220015 0.72097958,16.012997 0.026977501,15.292996 7.5600173,8.0220015 0,0.72599783 z M10.998004,0 L11.998004,0 11.998004,16 10.998004,16 z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </Button.Content>
                                </Button>
                                <Button Name="PART_PrevTab" 
                                        Focusable="False" 
                                        Width="16"
                                        MinHeight="16"
										Margin="0,0,8,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        DockPanel.Dock="Left" 
                                        Tag="PrevTab" Visibility="Collapsed" 
                                        Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools:TabControlExt}}}">
                                    <Button.Content>
                                        <Path
                                        Width ="4"
                                        Height="8"
                                        Fill="{Binding Foreground, ElementName=PART_PrevTab}"                                       
                                        SnapsToDevicePixels="True"
                                        Stretch="Fill">
                                            <Path.Data>
                                                <PathGeometry>M7.3199828,0 L8.0119999,0.72200069 1.4449779,7.0109903 7.9879826,13.278008 7.2970031,14.001 0,7.0109903 z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </Button.Content>
                                </Button>
                                <Button Name="PART_NextTab" 
                                        Focusable="False" 
                                        Width="16"
                                        MinHeight="16"
										Margin="0,0,8,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        DockPanel.Dock="Left" 
                                        Tag="NextTab" Visibility="Collapsed" 
                                        Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools:TabControlExt}}}">
                                    <Button.Content>
                                        <Path
                                        Width="4"
                                        Height="8"
                                        Fill="{Binding Foreground, ElementName=PART_NextTab}"                                        
                                        SnapsToDevicePixels="True"
                                        Stretch="Fill">
                                            <Path.Data>
                                                <PathGeometry>M0.6929932,0 L8.0000001,7.0109903 0.71600357,14.001 0.02398695,13.279 6.5559998,7.0109903 0,0.72099343 z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </Button.Content>
                                </Button>
                                <Button Name="PART_NextPage" 
                                        Focusable="False" 
                                        Width="16"
                                        MinHeight="16"
										Margin="0,0,8,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        DockPanel.Dock="Left" 
                                        Tag="NextPage" Visibility="Collapsed" 
                                        Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools:TabControlExt}}}">
                                    <Button.Content>
                                        <Path
                                        Width="8"
                                        Height="8"
                                        Fill="{Binding Foreground, ElementName=PART_NextPage}"
                                        SnapsToDevicePixels="True"
                                        Stretch="Fill">
                                            <Path.Data>
                                                <PathGeometry>M8.5059584,0.00034316026 C8.6339595,-0.0042728389 8.7634578,0.039734465 8.8639529,0.13373068 L15.839991,6.6228861 C15.941996,6.7178587 15.999995,6.8508572 15.999995,6.9888605 15.999995,7.1279019 15.941996,7.2609004 15.839991,7.3548964 L8.8399506,13.866024 C8.7439569,13.955993 8.6219622,14 8.4999522,14 8.3659488,14 8.2319606,13.946043 8.1339528,13.841 7.9459482,13.63903 7.9569499,13.322007 8.159954,13.134015 L14.765995,6.9888605 8.182949,0.86574133 C7.979945,0.67774888 7.9689586,0.36072536 8.156948,0.15869454 8.2514538,0.05819801 8.3779575,0.0049589315 8.5059584,0.00034316026 z M0.5059557,0.00032980882 C0.63395643,-0.004295416 0.7634573,0.039705542 0.86395788,0.13370756 L7.8399978,6.6228489 C7.9419985,6.7178511 7.9999988,6.8508538 7.9999988,6.9888572 7.9999988,7.12786 7.9419985,7.2608627 7.8399978,7.354865 L0.83995771,13.866007 C0.74395752,13.956009 0.62195683,14.00001 0.49995613,14.00001 0.36595535,14.00001 0.23195457,13.946009 0.13395405,13.841007 -0.054047108,13.639002 -0.043047428,13.321995 0.15995407,13.133991 L6.7659918,6.9888572 0.18295431,0.86572369 C-0.020046711,0.67771943 -0.031047344,0.36071255 0.15695381,0.15870813 0.25145435,0.058205952 0.37795496,0.0049547881 0.5059557,0.00032980882 z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </Button.Content>
                                </Button>
                                <Button Name="PART_PrevPage" 
                                        Focusable="False" 
                                        Width="16"
                                        MinHeight="16"
										Margin="0,0,8,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        DockPanel.Dock="Left" 
                                        Tag="PrevPage" 
                                        Visibility="Collapsed" 
                                        Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools:TabControlExt}}}">
                                    <Button.Content>
                                        <Path
                                        Width="8"
                                        Height="8"
                                        Fill="{Binding Foreground, ElementName=PART_PrevPage}"
                                        SnapsToDevicePixels="True"
                                        Stretch="Fill" >
                                            <Path.Data>
                                                <PathGeometry>M7.5183804,0.00034170646 C7.6463868,0.0049664303 7.7728932,0.058464798 7.8673979,0.15946161 8.0554075,0.36145556 8.0434069,0.67844587 7.8413967,0.86644011 L1.2350621,7.0112527 7.8173955,13.134066 C8.0204057,13.32206 8.0314063,13.639051 7.8433967,13.841044 7.7453918,13.946041 7.611385,14.000039 7.4773781,14.000039 7.3553721,14.000039 7.2333659,13.956041 7.137361,13.866043 L0.16000795,7.3772414 C0.058002949,7.2822445 0,7.1492484 0,7.0112527 0,6.872257 0.058002949,6.739261 0.16000795,6.645264 L7.1603622,0.13446256 C7.2608674,0.039965357 7.3903739,-0.0042832895 7.5183804,0.00034170646 z M15.518767,0.00032787731 C15.646648,0.0049510942 15.772906,0.058446775 15.867416,0.15942633 16.055413,0.36144655 16.04342,0.6784535 15.841413,0.86643606 L9.235076,7.011268 15.81741,13.134067 C16.020423,13.322049 16.031425,13.639056 15.843412,13.841016 15.7454,13.946054 15.611391,14.000007 15.477396,14.000007 15.355381,14.000007 15.233381,13.956003 15.137368,13.86604 L8.1600245,7.3772238 C8.0580144,7.2822559 8.0000128,7.1492642 8.0000128,7.011268 8.0000128,6.8722342 8.0580144,6.7392428 8.1600245,6.6452516 L15.160379,0.13446375 C15.261382,0.039953693 15.390886,-0.004295447 15.518767,0.00032787731 z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </Button.Content>
                                </Button>
                                <Button Name="PART_FirstTab"
                                        Width="16"
                                        MinHeight="16"
										Margin="0,0,8,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        Focusable="False" DockPanel.Dock="Left" 
                                        Tag="FirstTab" 
                                        Visibility="Collapsed" 
                                        Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools:TabControlExt}}}">
                                    <Button.Content>
                                        <Path
                                        Width="8"
                                        Height="8"
                                        Fill="{Binding Foreground, ElementName=PART_FirstTab}"
                                        SnapsToDevicePixels="True"
                                        Stretch="Fill">
                                            <Path.Data>
                                                <PathGeometry>M0,0.01300294 L1.0000002,0.01300294 1.0000002,16.013003 0,16.013003 z M11.278009,0 L11.972009,0.72000102 4.4380183,7.9909952 11.99801,15.286999 11.30401,16.007 2.9970098,7.9909952 z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </Button.Content>
                                </Button>
                                <DockPanel>
                                    <Grid x:Name="NewButtonPositon" VerticalAlignment="Center" HorizontalAlignment="Center" DockPanel.Dock="Right">
                                        <tools:NewTabLayout>
                                            <ContentPresenter x:Name="PART_ToolBarTray" Content="{Binding Path=ToolBarTray, Mode=OneWay, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:TabControlExt}},FallbackValue=''}" />
                                            <Button BorderThickness="{Binding Path=NewButtonBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:TabControlExt}}}" Name="PART_NewTab"  ContentTemplate="{Binding Path=NewTabButtonTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:TabControlExt}}}" Style="{StaticResource SyncfusionNewButtonStyle}" Background="Transparent" DockPanel.Dock="Top"/>
                                        </tools:NewTabLayout>
                                    </Grid>

                                    <ContentPresenter x:Name="PART_TabItems" ContentSource="TabItems" 
                                              DockPanel.Dock="Right" 
                                              Content="{TemplateBinding Content}"/>
                                        
                                </DockPanel>
                            </DockPanel>
                        </Border>
                        <Border Grid.Row="1" Background="{StaticResource ContentBackground}"></Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinmanager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="PART_MenuButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_CloseButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_PrevPage" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_NextPage" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_PrevTab" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_NextTab" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_LastTab" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_FirstTab" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_PrevPage" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_NextPage" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_PrevTab" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_NextTab" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_LastTab" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_FirstTab" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <DataTrigger Binding="{Binding Path=TabStripPlacement,RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}},FallbackValue=Top}" Value="Bottom">
                            <Setter Property="LayoutTransform" TargetName="PART_CustomItems">
                                <Setter.Value>
                                    <RotateTransform Angle="-180"/>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="LayoutTransform" TargetName="PART_CloseButton">
                                <Setter.Value>
                                    <RotateTransform Angle="-180"/>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="LayoutTransform" TargetName="PART_MenuButton">
                                <Setter.Value>
                                    <RotateTransform Angle="-180"/>
                                </Setter.Value>
                            </Setter>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=IsAllTabsClosed,RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:TabControlExt}},FallbackValue=false}" Value="True">
                            <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Hidden"/>
                            <Setter TargetName="PART_MenuButton" Property="Visibility" Value="Hidden"/>
                            <Setter TargetName="BottomBorder" Property="Height" Value="0"/>
                        </DataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=Items.Count,RelativeSource={RelativeSource FindAncestor,AncestorType={x:Type tools:TabControlExt}},FallbackValue=0}" Value="1" />
                                <Condition Binding="{Binding Path=HideHeaderOnSingleChild,RelativeSource={RelativeSource FindAncestor,AncestorType={x:Type tools:TabControlExt}}}" Value="True" />
                                <Condition Binding="{Binding Path=ShowTabListContextMenu,RelativeSource={RelativeSource FindAncestor,AncestorType={x:Type tools:TabControlExt}}}" Value="False" />
                                <Condition Binding="{Binding ElementName=PART_CloseButton, Path=Visibility}"  Value="Collapsed"/>
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="Bord" Property="Visibility" Value="Collapsed"/>
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSpreadsheetTabPanelAdvStyle}" TargetType="{x:Type tools:TabPanelAdv}"/>

    <Style x:Key="SyncfusionSpreadsheetTabControlExtStyle" TargetType="{x:Type tools:TabControlExt}" BasedOn="{StaticResource SyncfusionTabControlExtStyle}">
        <Setter Property="SelectedItemFontWeight" Value="Bold"/>
        <Setter Property="ShowTabListContextMenu" Value="False"/>
        <Setter Property="CloseButtonType" Value="Hide"/>
        <Setter Property="TabScrollStyle" Value="Normal"/>
        <Setter Property="TabStripPlacement" Value="Bottom"/>
        <Setter Property="ScrollViewer.CanContentScroll" Value="True"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="TabScrollButtonVisibility" Value="Visible"/>
        <Setter Property="EnableLabelEdit" Value="True"/>
        <Setter Property="IsCustomTabItemContextMenuEnabled" Value="True"/>
        <Setter Property="TabPanelStyle" Value="{StaticResource SyncfusionSpreadsheetTabPanelAdvStyle}" />
        <Setter Property="AllowDrop" Value="True"/>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSpreadsheetTabControlExtStyle}" TargetType="{x:Type tools:TabControlExt}"/>

    <DataTemplate x:Key="NewButton" >
        <Border Name="border" Margin="22,0,0,0" Background="Transparent">
            <Grid>
                <Grid Name="backgroundGrid" Width="20"  Height="20" Visibility="Visible">
                    <Ellipse Fill="Transparent" Stroke="{StaticResource IconColor}" Name="Rect" Visibility="Visible" Width="17" Height="17" Margin="0,0,0,1" />
                </Grid>
                <Path Name="Path1" Data="M19.833,0L32.501,0 32.501,19.833999 52.334,19.833999 52.334,32.500999 32.501,32.500999 32.501,52.333 19.833,52.333 19.833,32.500999 0,32.500999 0,19.833999 19.833,19.833999z"  Stretch="Uniform" Fill="{StaticResource IconColor}" Width="9" Height="9" Margin="0,0,0,1">
                    <Path.RenderTransform>
                        <TransformGroup>
                            <TransformGroup.Children>
                                <RotateTransform Angle="0" />
                                <ScaleTransform ScaleX="1" ScaleY="1" />
                            </TransformGroup.Children>
                        </TransformGroup>
                    </Path.RenderTransform>
                </Path>
            </Grid>
        </Border>
    </DataTemplate>

    <Style x:Key="PasteOptionsMenuItemTemplateKey" TargetType="MenuItem">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="MenuItem">
                    <Grid>
                        <ContentPresenter Margin="30,3,6,3" ContentSource="Header" RecognizesAccessKey="True" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
  
    <Style x:Key="SyncfusionSpreadsheetGroupButtonStyle" TargetType="spreadsheet:SpreadsheetGroupButton" BasedOn="{StaticResource WPFToggleButtonStyle}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:SpreadsheetGroupButton">
                    <Border Name="border" Background="Transparent">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CheckStates">
                                <VisualState x:Name="Checked">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames
                                                Storyboard.TargetName="Path1"
                                                Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Visible
                                                    </Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>

                                <VisualState x:Name="Unchecked">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames
                                                Storyboard.TargetName="Path1"
                                                Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed
                                                    </Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid>
                            <Grid Name="backgroundGrid" Width="20" Height="20" Visibility="Visible">
                                <Rectangle Fill="{TemplateBinding Background}" Stroke="{TemplateBinding BorderBrush}" Name="Rect" Visibility="Visible" />
                            </Grid>
                            <Path Name="Path1" Data="M19.833,0L32.501,0 32.501,19.833999 52.334,19.833999 52.334,32.500999 32.501,32.500999 32.501,52.333 19.833,52.333 19.833,32.500999 0,32.500999 0,19.833999 19.833,19.833999z"  Stretch="Uniform" Fill="{TemplateBinding Foreground}" Width="10" Height="10" Margin="0">
                                <Path.RenderTransform>
                                    <TransformGroup>
                                        <TransformGroup.Children>
                                            <RotateTransform Angle="0" />
                                            <ScaleTransform ScaleX="1" ScaleY="1" />
                                        </TransformGroup.Children>
                                    </TransformGroup>
                                </Path.RenderTransform>
                            </Path>

                            <Path Name="Path2" Data="M0,0L53.333,0 53.333,8.888 0,8.888z"  Stretch="Uniform" Fill="{TemplateBinding Foreground}" Width="10" Height="10" Margin="0" >
                                <Path.RenderTransform>
                                    <TransformGroup>
                                        <TransformGroup.Children>
                                            <RotateTransform Angle="0" />
                                            <ScaleTransform ScaleX="1" ScaleY="1" />
                                        </TransformGroup.Children>
                                    </TransformGroup>
                                </Path.RenderTransform>
                            </Path>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSpreadsheetGroupButtonStyle}" TargetType="spreadsheet:SpreadsheetGroupButton"/>

    <Style x:Key="SyncfusionProgressRingStyle" TargetType="spreadsheet:ProgressRing">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:ProgressRing">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="BusyAnimation" RepeatBehavior="Forever">
                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_BusyIndicatorPresenter" Storyboard.TargetProperty="(UIElement.Visibility)">
                                <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}" />
                            </ObjectAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Path11" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0" Value="0.3" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="1" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0.3" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Path10" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="0.3" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="1" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="0.3" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Path9" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0.3" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="1" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0.3" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Path8" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="0.3" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="1" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="0.3" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Path7" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0.3" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="1" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0.3" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Path6" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="0.3" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="1" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0.3" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Path5" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0.3" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="1" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="0.3" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Path4" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0.3" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="1" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="0.3" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Path3" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="0.3" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="1" />
                                <EasingDoubleKeyFrame KeyTime="0:0:1.0" Value="0.3" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Path2" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="0.3" />
                                <EasingDoubleKeyFrame KeyTime="0:0:1.0" Value="1" />
                                <EasingDoubleKeyFrame KeyTime="0:0:1.1" Value="0.3" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Path1" Storyboard.TargetProperty="Opacity">
                                <EasingDoubleKeyFrame KeyTime="0:0:1.0" Value="0.3" />
                                <EasingDoubleKeyFrame KeyTime="0:0:1.1" Value="1" />
                                <EasingDoubleKeyFrame KeyTime="0:0:1.2" Value="0.3" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Border Width="{TemplateBinding Width}"
                            Height="{TemplateBinding Height}"
                            HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalAlignment}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{StaticResource BorderAlt}"
                            BorderThickness="0"
                            CornerRadius="5"
                            Padding="10">
                        <StackPanel>
                            <Viewbox x:Name="PART_BusyIndicatorPresenter"
                                     Width="40"
                                     Height="40"
                                     Visibility="Collapsed">
                                <Grid x:Name="PART_BusyIndicatorPath"
                                      Grid.Row="1"
                                      Canvas.Left="76.686"
                                      Width="47.586"
                                      Height="47.866"
                                      RenderTransformOrigin="0.5,0.5">
                                    <Grid.RenderTransform>
                                        <TransformGroup>
                                            <ScaleTransform />
                                            <SkewTransform />
                                            <RotateTransform />
                                            <TranslateTransform />
                                        </TransformGroup>
                                    </Grid.RenderTransform>
                                    <Path x:Name="Path1"
                                          Margin="21.922,0,16.664,38.866"
                                          Data="F1M0,4.5C0,2.015 2.015,0 4.5,0 6.985,0 9,2.015 9,4.5 9,6.985 6.985,9 4.5,9 2.015,9 0,6.985 0,4.5"
                                          Fill="{TemplateBinding Foreground}"
                                          Opacity="0.3" />
                                    <Path x:Name="Path2"
                                          Margin="11.023,1.852,27.562,37.013"
                                          Data="F1M0.714,6.932C-0.629,4.841 -0.022,2.057 2.07,0.714 4.161,-0.629 6.945,-0.022 8.288,2.07 9.63,4.161 9.023,6.945 6.932,8.288 4.84,9.63 2.057,9.023 0.714,6.932"
                                          Fill="{TemplateBinding Foreground}"
                                          Opacity="0.3" />
                                    <Path x:Name="Path3"
                                          Margin="2.853,9.298,35.731,29.566"
                                          Data="F1M2.628,8.593C0.367,7.558 -0.626,4.888 0.409,2.628 1.444,0.368 4.114,-0.625 6.374,0.41 8.634,1.444 9.626,4.115 8.592,6.375 7.558,8.634 4.888,9.628 2.628,8.593"
                                          Fill="{TemplateBinding Foreground}"
                                          Opacity="0.3" />
                                    <Path x:Name="Path4"
                                          Margin="0,19.978,38.584,18.886"
                                          Data="F1M5.757,8.822C3.37,9.515 0.874,8.143 0.18,5.756 -0.513,3.37 0.859,0.873 3.246,0.179 5.633,-0.514 8.129,0.859 8.823,3.245 9.516,5.632 8.144,8.129 5.757,8.822"
                                          Fill="{TemplateBinding Foreground}"
                                          Opacity="0.3" />
                                    <Path x:Name="Path5"
                                          Margin="3.371,30.507,35.215,8.359"
                                          Data="F1M7.44,7.907C5.56,9.531 2.718,9.322 1.094,7.441 -0.531,5.56 -0.322,2.718 1.56,1.094 3.442,-0.53 6.282,-0.321 7.906,1.56 9.53,3.441 9.322,6.283 7.44,7.907"
                                          Fill="{TemplateBinding Foreground}"
                                          Opacity="0.3" />
                                    <Path x:Name="Path6"
                                          Margin="11.893,37.545,26.691,1.318"
                                          Data="F1M8.816,5.779C8.11,8.162 5.606,9.521 3.223,8.816 0.84,8.11 -0.519,5.606 0.187,3.223 0.892,0.84 3.396,-0.52 5.779,0.186 8.162,0.892 9.522,3.396 8.816,5.779"
                                          Fill="{TemplateBinding Foreground}"
                                          Opacity="0.3" />
                                    <Path x:Name="Path7"
                                          Margin="22.868,38.864,15.716,0"
                                          Data="F1M8.823,3.246C9.516,5.633 8.144,8.129 5.757,8.823 3.371,9.516 0.874,8.144 0.18,5.757 -0.513,3.37 0.86,0.874 3.246,0.18 5.633,-0.513 8.13,0.859 8.823,3.246"
                                          Fill="{TemplateBinding Foreground}"
                                          Opacity="0.3" />
                                    <Path x:Name="Path8"
                                          Margin="32.818,34.046,5.768,4.82"
                                          Data="F1M7.458,1.109C9.331,2.743 9.525,5.585 7.891,7.458 6.257,9.331 3.414,9.525 1.542,7.891 -0.331,6.257 -0.525,3.415 1.109,1.542 2.743,-0.331 5.585,-0.525 7.458,1.109"
                                          Fill="{TemplateBinding Foreground}"
                                          Opacity="0.3" />
                                    <Path x:Name="Path9"
                                          Margin="38.585,24.615,0,14.25"
                                          Data="F1M5.158,0.049C7.617,0.412 9.315,2.699 8.952,5.158 8.589,7.617 6.302,9.316 3.843,8.952 1.385,8.589 -0.314,6.302 0.049,3.843 0.412,1.385 2.699,-0.314 5.158,0.049"
                                          Fill="{TemplateBinding Foreground}"
                                          Opacity="0.3" />
                                    <Path x:Name="Path10"
                                          Margin="38.345,13.563,0.239,25.301"
                                          Data="F1M2.649,0.4C4.914,-0.623 7.579,0.384 8.602,2.649 9.625,4.915 8.618,7.58 6.353,8.603 4.088,9.625 1.422,8.618 0.4,6.353 -0.623,4.088 0.384,1.423 2.649,0.4"
                                          Fill="{TemplateBinding Foreground}"
                                          Opacity="0.3" />
                                    <Path x:Name="Path11"
                                          Margin="32.172,4.394,6.413,34.471"
                                          Data="F1M0.726,2.049C2.08,-0.035 4.867,-0.628 6.951,0.726 9.035,2.079 9.628,4.866 8.274,6.951 6.92,9.035 4.133,9.627 2.049,8.274 -0.035,6.92 -0.628,4.133 0.726,2.049"
                                          Fill="{TemplateBinding Foreground}"
                                          Opacity="0.3" />
                                </Grid>
                            </Viewbox>
                            <TextBlock x:Name="PART_Loading" Padding="3"
                                       HorizontalAlignment="Center"
                                       FontSize="16"
                                       Foreground="{TemplateBinding Foreground}"
                                       Text="{TemplateBinding Text}"
                                       Visibility="{TemplateBinding Text,
                                                                    Converter={StaticResource StringToVisibilityConverter}}" />
                        </StackPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <EventTrigger RoutedEvent="FrameworkElement.Loaded">
                            <BeginStoryboard Storyboard="{StaticResource BusyAnimation}" />
                        </EventTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionProgressRingStyle}" TargetType="spreadsheet:ProgressRing"/>
    
    <Style x:Key="CollapseButtonStyle" TargetType="{x:Type Button}" BasedOn="{StaticResource WPFButtonStyle}"/>

    <Style x:Key="SyncfusionSfSpreadsheetStyle" TargetType="spreadsheet:SfSpreadsheet">
        <Setter Property="SpreadsheetResourceDictionary">
            <Setter.Value>
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/SfSpreadsheet/SfSpreadsheet.xaml"/>
            </Setter.Value>
        </Setter>
		<Setter Property="Background" Value="{StaticResource ContentBackground}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:SfSpreadsheet">

                    <Grid x:Name="MainGrid" 
							Background="{TemplateBinding Background}"
							DataContext="{Binding RelativeSource={RelativeSource TemplatedParent}}">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Busy">
                                    <Storyboard>
                                        <BooleanAnimationUsingKeyFrames Storyboard.TargetName="PART_BusyDecorator" Storyboard.TargetProperty="IsBusy">
                                            <DiscreteBooleanKeyFrame KeyTime="0" Value="True" />
                                        </BooleanAnimationUsingKeyFrames>
                                        <DoubleAnimation Duration="0"
                                                         Storyboard.TargetName="PART_FormulaBar"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.5" />
                                        <DoubleAnimation Duration="0"
                                                         Storyboard.TargetName="Part_Grid"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.5" />
                                        <DoubleAnimation Duration="0"
                                                         Storyboard.TargetName="GridSplitter"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.5" />
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Normal" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="0" MinHeight="30" MaxHeight="480"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <spreadsheet:FormulaBar x:Name="PART_FormulaBar" 
                                          DataContext="{Binding RelativeSource={RelativeSource TemplatedParent}}" 
                                          Visibility="{TemplateBinding FormulaBarVisibility}"/>
                        <GridSplitter x:Name="GridSplitter" HorizontalAlignment="Stretch" Focusable="False" Grid.Row="1" ResizeBehavior="PreviousAndNext" MinHeight="3" Background="Transparent"
                                      Visibility="{TemplateBinding FormulaBarVisibility}"/>
                        <Grid x:Name="Part_Grid" Grid.Row="2">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="0"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="0"/>
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <spreadsheet:OutlinesPanel x:Name="ColumnGroupPanel" Orientation="Horizontal" Grid.Column="1" GroupLineColor="{StaticResource IconColor}"/>
                            <spreadsheet:OutlinesPanel x:Name="RowGroupPanel" Orientation="Vertical"  Grid.Row="1" GroupLineColor="{StaticResource IconColor}"/>
                            <tools:TabControlExt x:Name="PART_TabControlExt" Grid.Row="1" Grid.Column="1" 
                                                      Style="{StaticResource SyncfusionSpreadsheetTabControlExtStyle}" 
                                                      IsNewButtonEnabled="True"
												      BorderThickness="0"													  
                                                      NewTabButtonTemplate="{StaticResource NewButton}"
                                                      SelectedIndex="{Binding Path=SelectedTabIndex,Mode=TwoWay,RelativeSource={RelativeSource TemplatedParent}}" 
                                                      SelectedItem="{Binding Path=SelectedTabItem,Mode=TwoWay,RelativeSource={RelativeSource TemplatedParent}}"
                                                      ShowTabItemContextMenu="{Binding AllowTabItemContextMenu}">
                            </tools:TabControlExt>
                            <Grid.ContextMenu>
                                <ContextMenu x:Name="TabItemContextMenu">
                                    <ContextMenu.Items>
                                        <MenuItem Name="InsertSheetMenuItem" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Insert}"
                                          Icon="{StaticResource InserSheetIcon}">
                                        </MenuItem>
                                        <MenuItem Name="DeleteSheetMenuItem" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Delete}"
                                          Icon="{StaticResource DeleteSheetIcon}">
                                        </MenuItem>
                                        <MenuItem Name="RenameSheetMenuItem" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Rename}">
                                        </MenuItem>
                                        <MenuItem Name="ProtectSheetMenuItem" 
                                          Icon="{StaticResource ProtectsheetIcon}">
                                        </MenuItem>
                                        <MenuItem Name="HideSheetMenuItem" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Hide}">
                                        </MenuItem>
                                        <MenuItem Name="UnhideSheetMenuItem" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnHide}">
                                        </MenuItem>
                                    </ContextMenu.Items>
                                </ContextMenu>
                            </Grid.ContextMenu>

                        </Grid>
                        <spreadsheet:BusyDecorator x:Name="PART_BusyDecorator"
                                             Grid.Row="2"
                                             HorizontalAlignment="Center"
                                             VerticalAlignment="Center" />
                        <Grid.ContextMenu>
                            <ContextMenu x:Name="CellContextMenu">
                                <ContextMenu.Items>
                                    <MenuItem Name="CutMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cut}"
                                          BorderThickness="0"
                                          Icon="{StaticResource CutIcon1}">
                                    </MenuItem>
                                    <MenuItem Name="CopyMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Copy}"
                                          BorderThickness="0"
                                          Icon="{StaticResource CopyIcon1}">
                                    </MenuItem>
                                    <MenuItem Name="GraphicCellPaste" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Paste}"
                                          BorderThickness="0" Visibility="Collapsed"
                                          Icon="{StaticResource PasteOptionsIcon}">
                                    </MenuItem>
                                    <MenuItem Name="PasteTitleMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_PasteOptions}"
                                          BorderThickness="0" FontWeight="Bold" IsHitTestVisible="False"
                                          Icon="{StaticResource PasteOptionsIcon}">
                                    </MenuItem>
                                    <MenuItem Name="PasteOptionsMenuItem"
                                          IsEnabled="True" Style="{StaticResource PasteOptionsMenuItemTemplateKey}"
                                          BorderThickness="0">
                                        <MenuItem.Header>
                                            <StackPanel Name="PasteItemsStackpanel" Orientation="Horizontal">
                                                <Button Name="PasteAllButton" ToolTip="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_PasteAll}"
                                                    BorderThickness="0" Background="Transparent"
                                                    Content="{StaticResource PasteAllIcon}"/>
                                                <Button Name="PasteValueButton" ToolTip="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_PasteValues}"
                                                    BorderThickness="0" Margin="2,0,0,0" Padding="2,0,0,0" Background="Transparent"
                                                    Content="{StaticResource PasteValueIcon}"/>
                                                <Button Name="PasteFormatButton" ToolTip="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_PasteFormat}"
                                                    BorderThickness="0" Margin="2,0,0,0" Padding="2,0,0,0" Background="Transparent"
                                                    Content="{StaticResource PasteFormatIcon}"/>
                                                <Button Name="PasteFormulaButton" ToolTip="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_PasteFormulas}"
                                                    BorderThickness="0" Margin="2,0,0,0" Padding="2,0,0,0" Background="Transparent"
                                                    Content="{StaticResource PasteFormulaIcon}"/>
                                                <Button Name="PasteValueFormatButton" ToolTip="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_ValuesAndFormatting}"
                                                    BorderThickness="0" Margin="2,0,0,0" Padding="2,0,0,0" Background="Transparent"
                                                    Content="{StaticResource PasteValueFormatIcon}"/>
                                                <Button Name="PasteTextOnlyButton" Visibility="Collapsed"
                                                    BorderThickness="0" Margin="2,0,0,0" Padding="2,0,0,0" Background="Transparent"
                                                    Content="{StaticResource PasteTextOnlyIcon}"/>
                                                <Button Name="TempPasteAllButton" Visibility="Collapsed"
                                                    BorderThickness="0" Background="Transparent"
                                                    Content="{StaticResource PasteAllIcon}"/>
                                            </StackPanel>
                                        </MenuItem.Header>
                                    </MenuItem>
                                    <Separator Margin="0" BorderThickness="0"/>
                                    <MenuItem Name="InsertMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_Insert}"
                                          BorderThickness="0">
                                    </MenuItem>
                                    <MenuItem Name="DeleteMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_Delete}"
                                          BorderThickness="0">
                                    </MenuItem>
                                    <MenuItem Name="ClearContentsMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_ClearContents}"
                                          BorderThickness="0">
                                    </MenuItem>
                                    <Separator Margin="0" BorderThickness="0"/>
                                    <MenuItem Name="ColumnWidthMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ColumnWidth}"
                                          BorderThickness="0">
                                    </MenuItem>
                                    <MenuItem Name="HideColumnMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Hide}"
                                          BorderThickness="0">
                                    </MenuItem>
                                    <MenuItem Name="UnhideColumnMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=UnhideSheet_Title}"
                                          BorderThickness="0">
                                    </MenuItem>
                                    <MenuItem Name="InsertCommentMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_InsertComment}"
                                          BorderThickness="0"
                                          Icon="{StaticResource InsertCommentIcon}">
                                    </MenuItem>
                                    <MenuItem Name="EditCommentMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=EditComment}"
                                          BorderThickness="0"
                                          Icon="{StaticResource EditCommentIcon1}">
                                    </MenuItem>
                                    <MenuItem Name="DeleteCommentMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_DeleteComment}"
                                          BorderThickness="0"
                                          Icon="{StaticResource DeleteCommentIcon1}">
                                    </MenuItem>
                                    <Separator Margin="0" BorderThickness="0"/>
                                    <MenuItem Name="FormatCellsMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_FormatCells}"
                                          BorderThickness="0"
                                          Icon="{StaticResource FormatCellsIcon}">
                                    </MenuItem>
                                    <MenuItem Name="DefineNameMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_DefineName}"
                                          BorderThickness="0">
                                    </MenuItem>
                                    <MenuItem Name="InsertHyperlinkMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_Hyperlink}"
                                          BorderThickness="0"
                                          Icon="{StaticResource HyperlinkIcon1}">
                                    </MenuItem>
                                    <MenuItem Name="EditHyperlinkMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_EditHyperlink}"
                                          BorderThickness="0"
                                          Icon="{StaticResource HyperlinkIcon1}">
                                    </MenuItem>
                                    <MenuItem Name="OpenHyperlinkMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_OpenHyperlink}"
                                          BorderThickness="0"
                                          Icon="{StaticResource HyperlinkIcon1}">
                                    </MenuItem>
                                    <MenuItem Name="RemoveHyperlinkMenuItem" Height="26" 
                                          Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_RemoveHyperlink}"
                                          BorderThickness="0"
                                          Icon="{StaticResource RemoveHyperlinkIcon}">
                                    </MenuItem>
                                </ContextMenu.Items>
                            </ContextMenu>
                        </Grid.ContextMenu>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSfSpreadsheetStyle}" TargetType="spreadsheet:SfSpreadsheet"/>

    <Style x:Key="SyncfusionSpreadsheetFormulaBarStyle" TargetType="spreadsheet:FormulaBar">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:FormulaBar">
                    <Grid ShowGridLines="False">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"  MinWidth="120" MaxWidth="900"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*" MinWidth="100"/>
                        </Grid.ColumnDefinitions>
                        <shared:ComboBoxAdv x:Name="NameBox" IsEditable="True" Grid.Column="0" Margin="6,2,2,0" IsTextSearchEnabled="False" 
                                     VerticalContentAlignment="Center" Padding="10,1,1,1"
                                     VerticalAlignment="Top" DisplayMemberPath="Name" 
                                     BorderThickness="1" 
                                     HorizontalAlignment="Stretch" MinHeight="26" IsReadOnly="False"  MinWidth="110"/>
                        <GridSplitter x:Name="Splitter" Grid.Column="1" Width="15" MinHeight="15" Margin="-1,8,0,0" HorizontalAlignment="Left" VerticalAlignment="Top"
                                          BorderThickness="1" Focusable="False"
                                          ResizeBehavior="PreviousAndCurrent" Template="{StaticResource SplitterIcon}">
                        </GridSplitter>
                        <Border x:Name="PART_ButtonsBorder" Background="Transparent"  BorderBrush="{Binding BorderBrush,ElementName=NameBox}" MinHeight="26" Margin="2,2,2,1" 
                                VerticalAlignment="Top" BorderThickness="1" Grid.Column="2">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="30" MinWidth="30"/>
                                    <ColumnDefinition Width="30" MinWidth="30"/>
                                    <ColumnDefinition Width="0" MinWidth="0"/>
                                </Grid.ColumnDefinitions>
                                <shared:ButtonAdv x:Name="CancelEditButton" Grid.Column="0"
                                                      IconTemplate="{StaticResource CancelEditIcon}"
                                                      BorderThickness="0" Focusable="False" 
                                                      SizeMode="Small" IsEnabled="False"
                                                      Background="Transparent" Width="30" 
                                                      MinHeight="26" Opacity="0.8"
                                                      IconStretch="UniformToFill"  IconHeight="18" IconWidth="18"  
                                                      VerticalAlignment="Center" HorizontalContentAlignment="Center">
                                </shared:ButtonAdv>
                                <shared:ButtonAdv x:Name="EndEditButton" Focusable="False" Grid.Column="1" 
                                                      IconTemplate="{StaticResource EndEditIcon}" SizeMode="Small" 
                                                      Background="Transparent" BorderThickness="0" 
                                                      Width="30" MinHeight="26" IsEnabled="False" Opacity="0.8"
                                                      IconStretch="Uniform" IconHeight="18" IconWidth="18"  
                                                      VerticalAlignment="Center" HorizontalAlignment="Center" 
                                                      VerticalContentAlignment="Center" 
                                                      HorizontalContentAlignment="Center"/>
                            </Grid>
                        </Border>
                        <TextBox x:Name="FormulaTextBox" Grid.Column="3" Margin="2,2,6,1" MinHeight="26"
                                 Text="{Binding Path=CurrentCellValue, Mode=OneWay, UpdateSourceTrigger=PropertyChanged}"
                                 ScrollViewer.CanContentScroll="True" 
                                 ScrollViewer.VerticalScrollBarVisibility="Disabled"
                                 TextWrapping="Wrap" HorizontalAlignment="Stretch" Padding="2" 
                                 VerticalContentAlignment="Top" VerticalAlignment="Stretch" 
                                 HorizontalContentAlignment="Stretch"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinmanager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter TargetName="NameBox" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="Splitter" Property="Margin" Value="-1,10,0,0" />
                            <Setter TargetName="PART_ButtonsBorder" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="CancelEditButton" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="EndEditButton" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="FormulaTextBox" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSpreadsheetFormulaBarStyle}" TargetType="spreadsheet:FormulaBar"/>
    
    <Style x:Key="SyncfusionSpreadsheetGridStyle" TargetType="spreadsheet:SpreadsheetGrid">
        <Setter Property="Background" Value="{Binding Path=TabControlExt.TabPanelBackground,RelativeSource={RelativeSource Mode=TemplatedParent},FallbackValue=Transparent}"/>
        <Setter Property="HeaderBackground" Value="{StaticResource ContentBackgroundAlt1}"/>
        <Setter Property="HeaderForeground" Value="{StaticResource ContentForeground}"/>
		<Setter Property="HeaderCellBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="SelectionBorderBrush" Value="{StaticResource PrimaryBackground}"/>
        <Setter Property="ActiveHeaderBackground" Value="{StaticResource ContentBackground}"/>
        <Setter Property="ActiveHeaderForeground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="1,1,1,0" />
        <Setter Property="ScrollViewer.PanningMode" Value="Both" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:SpreadsheetGrid">
                    <Border BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Background="{TemplateBinding Background}"
                            SnapsToDevicePixels="True">
                        <ScrollViewer x:Name="PART_ScrollViewer" FocusVisualStyle="{TemplateBinding FocusVisualStyle}"
                                          IsDeferredScrollingEnabled="False"
                                          CanContentScroll="True"
                                          IsTabStop="False"
                                          PanningMode="{TemplateBinding ScrollViewer.PanningMode}"
                                          HorizontalScrollBarVisibility="{Binding ElementName=PART_VisualContainer, Path=HorizontalScrollBarVisibility}"
                                          VerticalScrollBarVisibility="{Binding ElementName=PART_VisualContainer, Path=VerticalScrollBarVisibility}"
                                          >

                            <spreadsheet:VisualContainerExt x:Name="PART_VisualContainer"
                                                />

                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Resources>
            <Style TargetType="TextBox" BasedOn="{x:Null}"/>
            <Style TargetType="TextBlock" BasedOn="{x:Null}"/>
        </Style.Resources>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSpreadsheetGridStyle}" TargetType="spreadsheet:SpreadsheetGrid"/>
    
    <Style x:Key="SyncfusionPasteDropDownItemStyle" TargetType="spreadsheet:PasteDropDownItem">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:PasteDropDownItem">
                    <Border BorderThickness="1" Background="{StaticResource PopupBackground}" BorderBrush="{StaticResource BorderAlt}">
                        <shared:ButtonAdv x:Name="PART_PasteButton" IconHeight="24" IconWidth="24" IconTemplate="{StaticResource PasteIcon1}"  SizeMode="Small"  BorderThickness="0"  IconStretch="UniformToFill" >
                            <Popup Name="popup" >
                                <ListView Name="FillList" Width="220" 
                                          Background="{StaticResource PopupBackground}"
                                          ScrollViewer.HorizontalScrollBarVisibility="Disabled" 
                                          ScrollViewer.VerticalScrollBarVisibility="Disabled" 
                                          BorderThickness="1">
                                    <ListViewItem Width="250" BorderThickness="0"   Margin="3">
                                        <shared:ButtonAdv x:Name="PasteAllButton" Background="Transparent"  IconHeight="24" IconWidth="24" HorizontalContentAlignment="Left"  Width="250"  BorderThickness="0"  IconStretch="UniformToFill" IconTemplate="{StaticResource PasteSmall}" Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Paste}" ></shared:ButtonAdv>
                                    </ListViewItem>
                                    <ListViewItem Width="250" BorderThickness="0"  Margin="3">
                                        <shared:ButtonAdv x:Name="PasteFormulaButton" Background="Transparent"  IconHeight="24" IconWidth="24" Width="250" HorizontalContentAlignment="Left"    BorderThickness="0"  IconStretch="UniformToFill"  IconTemplate="{StaticResource PasteFormula}"  Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_PasteFormulas}" ></shared:ButtonAdv>
                                    </ListViewItem>
                                    <ListViewItem Width="250" BorderThickness="0"   Margin="3">
                                        <shared:ButtonAdv x:Name="PasteFormulaFormatButton" Background="Transparent"  IconHeight="24" IconWidth="24" HorizontalContentAlignment="Left" Width="250" BorderThickness="0"  IconStretch="UniformToFill"  IconTemplate="{StaticResource PasteFormulaAndNumberFormat}"  Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_FormulaFormatting}" ></shared:ButtonAdv>
                                    </ListViewItem>
                                    <ListViewItem Width="250" BorderThickness="0"  Margin="3">
                                        <shared:ButtonAdv x:Name="PasteValueButton" Background="Transparent"  IconHeight="24" IconWidth="24" HorizontalContentAlignment="Left" Width="250" BorderThickness="0" IconStretch="UniformToFill"  IconTemplate="{StaticResource PasteValue}"  Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_PasteValues}" ></shared:ButtonAdv>
                                    </ListViewItem>
                                    <ListViewItem Width="250" BorderThickness="0"  Margin="3">
                                        <shared:ButtonAdv x:Name="PasteValueFormatButton" Background="Transparent" IconHeight="24"  IconWidth="24" HorizontalContentAlignment="Left" Width="250" BorderThickness="0"  IconStretch="UniformToFill"  IconTemplate="{StaticResource PasteValueAndSourceFormatting}"  Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_ValueFormatting}" ></shared:ButtonAdv>
                                    </ListViewItem>
                                    <ListViewItem Width="250" BorderThickness="0"  Margin="3">
                                        <shared:ButtonAdv x:Name="PasteFormatButton" Background="Transparent"  IconHeight="24" IconWidth="24" HorizontalContentAlignment="Left" Width="250" BorderThickness="0"  IconStretch="UniformToFill"  IconTemplate="{StaticResource PasteFormat}" Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_PasteFormat}"  ></shared:ButtonAdv>
                                    </ListViewItem>
                                </ListView>
                            </Popup>
                        </shared:ButtonAdv>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionPasteDropDownItemStyle}" TargetType="spreadsheet:PasteDropDownItem"></Style>

    <Style x:Key="SyncfusionFillDropDownItemStyle" TargetType="spreadsheet:FillDropDownItem">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:FillDropDownItem">
                    <Border BorderThickness="1" Background="{StaticResource PopupBackground}" BorderBrush="{StaticResource BorderAlt}">
                        <shared:ButtonAdv  Name="dropdownSplitter" IconHeight="24" IconWidth="24" IconTemplate="{StaticResource FillIcon}"  SizeMode="Small"  BorderThickness="0"  IconStretch="UniformToFill">
                            <Popup Name="fillDropdownPopup" >
                                <ListBox Name="FillList" MinWidth="50" MinHeight="50" 
                                         Background="{StaticResource PopupBackground}"
                                          ScrollViewer.HorizontalScrollBarVisibility="Disabled" 
                                          ScrollViewer.VerticalScrollBarVisibility="Disabled" BorderThickness="1">
                                    <!--WPF-45471 Fillseries popup misaligned-->
                                    <ListBoxItem>
                                        <RadioButton  Width="160" Height="17" Name="CopyCells" Content="Copy Cells" GroupName="Fill" IsChecked="True" />
                                    </ListBoxItem>
                                    <ListBoxItem>
                                        <RadioButton  Width="160" Height="17" Name="FillSeries" Content="Fill Series" GroupName="Fill" />
                                    </ListBoxItem>
                                    <ListBoxItem>
                                        <RadioButton  Width="160" Height="17" Name="FillFormattingOnly" Content="Fill Formatting Only" GroupName="Fill" />
                                    </ListBoxItem>
                                    <ListBoxItem>
                                        <RadioButton  Width="160" Height="17" Name="FillWithoutFormatting" Content="Fill Without Formatting" GroupName="Fill" />
                                    </ListBoxItem>
                                    <ListBoxItem>
                                        <RadioButton Width="160" Height="17" Name="FillDays" Content="Fill Days" GroupName="Fill" ></RadioButton>
                                    </ListBoxItem>
                                    <ListBoxItem>
                                        <RadioButton  Width="160" Height="17" Name="FillWeekDays" Content="Fill Weekdays" GroupName="Fill"></RadioButton>
                                    </ListBoxItem>
                                    <ListBoxItem>
                                        <RadioButton  Width="160" Height="17" Name="FillMonths" Content="Fill Months" GroupName="Fill"></RadioButton>
                                    </ListBoxItem>
                                    <ListBoxItem>
                                        <RadioButton  Width="160" Height="17" Name="FillYears" Content="Fill Years" GroupName="Fill"></RadioButton>
                                    </ListBoxItem>
                                </ListBox>
                            </Popup>
                        </shared:ButtonAdv>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionFillDropDownItemStyle}" TargetType="spreadsheet:FillDropDownItem"></Style>

    <Style x:Key="SpreadsheetComboBoxStyle" BasedOn="{StaticResource WPFComboBoxStyle}"  TargetType="ComboBox" >
        <Setter Property="SelectedIndex" Value="0" />
    </Style>
    
    <Style x:Key="FindAndReplaceDialog" TargetType="{x:Type spreadsheet:FindAndReplaceDialog}">
        <Setter Property="Template" >
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type spreadsheet:FindAndReplaceDialog}">
                    <Grid Background="{StaticResource PopupBackground}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <tools:TabControlExt  x:Name="tabcontrol" SelectedItemFontWeight="Medium" ShowTabItemContextMenu="False" EnableLabelEdit="False" AllowDragDrop="False" TabPanelBackground="{StaticResource ContentBackground}"
                                              Background="{StaticResource ContentBackground}"
                                              BorderBrush="{StaticResource BorderAlt}" TabListContextMenuOptions="None" CloseButtonType="Hide">
                            <tools:TabControlExt.BorderThickness>
                                <Thickness>0,0,0,1</Thickness>
                            </tools:TabControlExt.BorderThickness>
                            <tools:TabItemExt  Height="24" Width="54" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Find}">
                                <tools:TabItemExt.Margin>
                                    <Thickness>8,0,0,0</Thickness>
                                </tools:TabItemExt.Margin>
                                <StackPanel Margin="13,0,5,0">
                                    <Grid Margin="0,11,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_FindWhat}"  Padding="0,3,0,0" Foreground="{StaticResource ContentForegroundAlt1}">
                                            <TextBlock.Margin>
                                                <Thickness>0,5,0,0</Thickness>
                                            </TextBlock.Margin>
                                        </TextBlock>
                                        <ComboBox x:Name="FindSearchComboBox" Grid.Column="1" IsTextSearchEnabled="False" HorizontalAlignment="Stretch" IsEditable="True" Margin="15,4,8,0" Height="24" MinWidth="344" ></ComboBox>
                                    </Grid>
                                    <Grid x:Name="FindOuterGrid"  Margin="0,41,0,11">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Grid x:Name="FindInnerGrid" Visibility="Collapsed" HorizontalAlignment="Left">
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="30"/>
                                                <RowDefinition Height="34"/>
                                                <RowDefinition Height="30"/>
                                            </Grid.RowDefinitions>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="70"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Within}"   Foreground="{StaticResource ContentForegroundAlt1}" >
                                                <TextBlock.Padding>
                                                    <Thickness>0,7,0,0</Thickness>
                                                </TextBlock.Padding>
                                            </TextBlock>
                                            <ComboBox Grid.Column="1" Grid.Row="0" x:Name="FindSearchWithinComboBox" Style="{StaticResource SpreadsheetComboBoxStyle}" Width="133" Height="24" Margin="5,0,0,0">
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Sheet}"></ComboBoxItem>
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Workbook}"></ComboBoxItem>
                                            </ComboBox>
                                            <TextBlock Grid.Row="1" Grid.Column="0"  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Search}"  Foreground="{StaticResource ContentForegroundAlt1}" >
                                                <TextBlock.Margin>
                                                    <Thickness>0,9,0,0</Thickness>
                                                </TextBlock.Margin>
                                            </TextBlock>
                                            <ComboBox Grid.Row="1" Grid.Column="1" x:Name="FindSearchByComboBox" Style="{StaticResource SpreadsheetComboBoxStyle}" Width="133" Height="24" Margin="5,0,0,0">
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_ByRows}"></ComboBoxItem>
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_ByColumns}"></ComboBoxItem>
                                            </ComboBox>
                                            <TextBlock Grid.Row="2" Grid.Column="0"  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Lookin}"  Foreground="{StaticResource ContentForegroundAlt1}" >
                                                <TextBlock.Padding>
                                                    <Thickness>0,7,0,0</Thickness>
                                                </TextBlock.Padding>
                                            </TextBlock>
                                            <ComboBox Grid.Row="2" Grid.Column="1" x:Name="FindLookinComboBox" Width="133" Style="{StaticResource SpreadsheetComboBoxStyle}" Height="24" Margin="5,0,0,0">
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_RibbonFormulas}"></ComboBoxItem>
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Context_PasteValues}"></ComboBoxItem>
                                            </ComboBox>
                                        </Grid>
                                        <StackPanel  HorizontalAlignment="Left" x:Name="FindCheckBoxStackPanel" Grid.Column="1" Grid.Row="0" Visibility="Collapsed">
                                            <CheckBox x:Name="FindMatchCase" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_MatchCase}" IsChecked="False" Margin="15,3,0,0"></CheckBox>
                                            <CheckBox x:Name="FindMatchEntireCellContents" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_MatchEntireCellContents}" IsChecked="False" Margin="15,14,0,0"></CheckBox>
                                        </StackPanel>
                                        <Button Grid.Column="1" x:Name="FindOptionButton" VerticalAlignment="Bottom" Foreground="{StaticResource SecondaryForeground}" Margin="0,3,7,3" BorderThickness="1" HorizontalAlignment="Right" Width="82" Height="24" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_OptionsExpand}"></Button>
                                    </Grid>
                                </StackPanel>
                            </tools:TabItemExt>
                            <tools:TabItemExt Height="24" Width="73" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Replace}">
                                <TabItem.Margin>
                                    <Thickness>0</Thickness>
                                </TabItem.Margin>
                                <StackPanel Margin="13,0,5,0">
                                    <Grid Margin="0,11,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_FindWhat}"  Padding="0,3,0,0" Foreground="{StaticResource ContentForegroundAlt1}">
                                            <TextBlock.Margin>
                                                <Thickness>0,5,0,0</Thickness>
                                            </TextBlock.Margin>
                                        </TextBlock>
                                        <ComboBox Grid.Column="1" x:Name="ReplaceSearchComboBox" IsTextSearchEnabled="False" HorizontalAlignment="Stretch" MinWidth="344" Height="24" Margin="15,4,8,0" IsEditable="True"></ComboBox>
                                    </Grid>
                                    <Grid  Margin="0,4,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="75"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock HorizontalAlignment="Left" Width="70" Padding="0,3,0,0" Foreground="{StaticResource ContentForegroundAlt1}"
                                                    Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_ReplaceWith}">
                                            <TextBlock.Margin>
                                                <Thickness>0,5,0,0</Thickness>
                                            </TextBlock.Margin>
                                        </TextBlock>
                                        <ComboBox x:Name="ReplaceWithComboBox" Grid.Column="1" IsTextSearchEnabled="False" MinWidth="344" Height="24" HorizontalAlignment="Stretch" Margin="0,4,8,0" IsEditable="True" ></ComboBox>
                                    </Grid>
                                    <Grid x:Name="ReplaceOuterGrid" Margin="0,9,0,11">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Grid x:Name="ReplaceInnerGrid" Grid.RowSpan="2" Visibility="Collapsed" HorizontalAlignment="Left">
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="30"/>
                                                <RowDefinition Height="34"/>
                                                <RowDefinition Height="30"/>
                                            </Grid.RowDefinitions>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="70"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Within}"   Foreground="{StaticResource ContentForegroundAlt1}">
                                                <TextBlock.Padding>
                                                    <Thickness>0,7,0,0</Thickness>  
                                                </TextBlock.Padding>
                                            </TextBlock>
                                            <ComboBox Grid.Column="1" Grid.Row="0" x:Name="ReplaceWithinComboBox" Width="133" Style="{StaticResource SpreadsheetComboBoxStyle}" Height="24" Margin="5,0,0,0">
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Sheet}"></ComboBoxItem>
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Workbook}"></ComboBoxItem>
                                            </ComboBox>
                                            <TextBlock Grid.Column="0" Grid.Row="1" Foreground="{StaticResource ContentForegroundAlt1}" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Search}">
                                                <TextBlock.Padding>
                                                    <Thickness>0,9,0,0</Thickness>
                                                </TextBlock.Padding>
                                            </TextBlock>
                                            <ComboBox Grid.Column="1" Grid.Row="1" x:Name="ReplaceSearchByComboBox" Width="133" Style="{StaticResource SpreadsheetComboBoxStyle}" Height="24" Margin="5,0,0,0" >
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_ByRows}"></ComboBoxItem>
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_ByColumns}"></ComboBoxItem>
                                            </ComboBox>
                                            <TextBlock Grid.Column="0" Grid.Row="2"  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Lookin}"  Foreground="{StaticResource ContentForegroundAlt1}" >
                                                <TextBlock.Padding>
                                                    <Thickness>0,7,0,0</Thickness>
                                                </TextBlock.Padding>
                                            </TextBlock>
                                            <ComboBox Grid.Column="1" Grid.Row="2" x:Name="ReplaceLookinComboBox" Style="{StaticResource SpreadsheetComboBoxStyle}" Width="133" Height="24" Margin="5,0,0,0">
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_RibbonFormulas}"></ComboBoxItem>
                                            </ComboBox>
                                        </Grid>
                                        <StackPanel  HorizontalAlignment="Left" x:Name="ReplaceCheckBoxStackPanel" Grid.Column="1" Grid.Row="0" Visibility="Collapsed">
                                            <CheckBox x:Name="ReplaceMatchCase" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_MatchCase}" IsChecked="False" Margin="15,3,0,0"></CheckBox>
                                            <CheckBox x:Name="ReplaceMatchEntireCellContents" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_MatchEntireCellContents}" IsChecked="False" Margin="15,14,0,0"></CheckBox>
                                        </StackPanel>
                                        <Button Grid.Column="1" x:Name="ReplaceOptionButton" Foreground="{StaticResource SecondaryForeground}" HorizontalAlignment="Right" BorderThickness="1" VerticalAlignment="Bottom" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_OptionsExpand}" Margin="0,3,7,3" Width="82" Height="24"/>
                                    </Grid>
                                </StackPanel>
                            </tools:TabItemExt>
                        </tools:TabControlExt>
                        <Border Grid.Row="1" BorderBrush="{StaticResource BorderAlt}" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}">
                            <StackPanel  Orientation="Horizontal" HorizontalAlignment="Right"  Grid.Row="1"  Margin="10,4,15,3">
                                <Button x:Name="ReplaceAllButton" Foreground="{StaticResource SecondaryForeground}" Margin="0,1,0,4" Width="90" Height="24" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_ReplaceAll}"></Button>
                                <Button x:Name="ReplaceButton" Foreground="{StaticResource SecondaryForeground}" Margin="8,1,0,4" Width="72" Height="24" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Replace}"></Button>
                                <Button x:Name="FindAllButton" Foreground="{StaticResource SecondaryForeground}" Margin="18,1,0,4" Width="72" Height="24" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_FindAll}"></Button>
                                <Button x:Name="FindNextButton" Foreground="{StaticResource SecondaryForeground}" Margin="8,1,5,4" Width="83" Height="24" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_FindNext}"></Button>
                                <Button x:Name="CloseButton" Foreground="{StaticResource SecondaryForeground}" 
                                       Margin="3,1,0,4" Width="61" Height="24" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Close}"></Button>
                            </StackPanel>
                        </Border>                        
                        <DataGrid x:Name="FindAllDetailGrid" Grid.Row="2" MinHeight="0" Height="0"
                      Visibility="Visible"
                      Margin="9,0,9,0"
                      HeadersVisibility="None"
                      AutoGenerateColumns="False"
                      Background="{StaticResource ContentBackground}"
                      GridLinesVisibility="None"
                      IsReadOnly="True"
                      ScrollViewer.CanContentScroll="True"
                      ScrollViewer.VerticalScrollBarVisibility="Auto"
                      SelectionMode="Single"
                      KeyboardNavigation.DirectionalNavigation="Cycle"
                      SelectionUnit="FullRow" ColumnHeaderHeight="24" >
                            <DataGrid.Columns>
                                <DataGridTextColumn x:Name="Sheet" Width="54" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Sheet}" Binding="{Binding Path=Worksheet.Name}"></DataGridTextColumn>
                                <DataGridTextColumn x:Name="Cell" Width="44" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Cell}" Binding="{Binding Path=AddressGlobal , Converter={StaticResource CellAddressConverter}}"></DataGridTextColumn>
                                <DataGridTextColumn x:Name="Value" Width="58" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Value}" Binding="{Binding Path=DisplayText}"></DataGridTextColumn>
                                <DataGridTextColumn x:Name="Formula" Width="*"  Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Formula}" Binding="{Binding Path=Formula}"></DataGridTextColumn>
                            </DataGrid.Columns>
                            <DataGrid.CellStyle>
                                <Style TargetType="DataGridCell">
                                    <Style.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="BorderThickness" Value="0" />
                                            <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.CellStyle>
                            <DataGrid.ColumnHeaderStyle>
                                <Style TargetType="{x:Type DataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center" />
                                    <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="{StaticResource PrimaryBackgroundOpacity3}" />
                                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                                        </Trigger>
                                        <MultiTrigger>
                                            <MultiTrigger.Conditions>
                                                <Condition Property="IsMouseOver" Value="True" />
                                                <Condition Property="IsPressed" Value="True" />
                                            </MultiTrigger.Conditions>
                                            <Setter Property="Background" Value="{StaticResource PrimaryBackgroundOpacity2}" />
                                            <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                                        </MultiTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.ColumnHeaderStyle>
                            <DataGrid.Resources>
                                <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" 
                   Color="{StaticResource PrimaryBackground.Color}"/>
                            </DataGrid.Resources>
                        </DataGrid>
                        <TextBlock x:Name="CellsFoundDetail" FontSize="12" Grid.Row="3" Margin="9,6,0,3" Visibility="Collapsed"></TextBlock>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource FindAndReplaceDialog}" TargetType="spreadsheet:FindAndReplaceDialog"></Style>

    <ItemsPanelTemplate x:Key="ListViewItemsPanelTemplate">
        <WrapPanel Height="165" Width="176" ItemWidth="83" ItemHeight="26" HorizontalAlignment="Left" VerticalAlignment="Center"></WrapPanel>
    </ItemsPanelTemplate>

    <Style x:Key="BorderTabToggleButtonStyle" BasedOn="{StaticResource WPFToggleButtonStyle}"  TargetType="ToggleButton" >
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border x:Name="border" 
                            SnapsToDevicePixels="true" 
                            Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            >
                        <ContentPresenter x:Name="contentPresenter" Margin="{TemplateBinding Padding}" 
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          RecognizesAccessKey="True" 
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinmanager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="Button.IsDefaulted" Value="true">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt3}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="Default"/>
                                <Condition Property="IsFocused" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt3}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource ButtonBorderBrushHovered }"/>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="border" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="Transparent"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

    </Style>

    <Style x:Key="FormatCellsDialog" TargetType="spreadsheet:FormatCellsDialog" >
        <Setter Property="Template" >
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:FormatCellsDialog" >
                    <Border Background="{StaticResource PopupBackground}">
                        <Border.CornerRadius>
                            0,0,8,8
                        </Border.CornerRadius>

                        <Grid Background="Transparent">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="56"/>
                            </Grid.RowDefinitions>

                            <tools:TabControlExt x:Name="tabcontrol" HorizontalContentAlignment="Center" SelectedItemFontWeight="Medium" ShowTabItemContextMenu="False" EnableLabelEdit="False" AllowDragDrop="False" FocusVisualStyle="{x:Null}" Width="566" Height="398" SelectedIndex="0" TabPanelBackground="{StaticResource ContentBackground}" 
                                                 Background="{StaticResource ContentBackground}"
                                                 BorderBrush="{StaticResource BorderAlt}" TabListContextMenuOptions="None" CloseButtonType="Hide">
                                <tools:TabControlExt.BorderThickness>
                                    <Thickness>0,0,0,1</Thickness>
                                </tools:TabControlExt.BorderThickness>

                                <!--NumberTab-->

                                <tools:TabItemExt HorizontalContentAlignment="Center" FontSize="12" Height="24"  Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Number}">
                                    <tools:TabItemExt.Margin>
                                        <Thickness>8,0,0,0</Thickness>
                                    </tools:TabItemExt.Margin>
                                    <Grid Margin="-4,8,0,0" >
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="20"/>
                                            <RowDefinition Height="*"/>
                                            <RowDefinition Height="65"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Width="50"
                               Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Category}"
                               Height="20" 
                               
                               FontSize="12"
                               Foreground="{StaticResource ContentForegroundAlt1}"
                               Margin="5,4,0,0"
                               HorizontalAlignment="Left"/>

                                        <StackPanel Orientation="Horizontal" Grid.Row="1" Margin="0,6,0,0">

                                            <ListView Width="202" 
                                  Margin="5,0,0,0" 
                                  Height="262"
                                  BorderThickness="1"
                                  FontSize="12"
                                  x:Name="Category_List" 
                                  ScrollViewer.VerticalScrollBarVisibility="Visible" >
                                            </ListView>

                                            <StackPanel Orientation="Vertical" Margin="13,4,0,0">

                                                <GroupBox x:Name="Format_GroupBox" 
                                      Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Sample}" Width="316"
                                      HorizontalAlignment="Left"  
                                      Height="46"
                                      FontSize="12" 
                                      Foreground="{StaticResource ContentForeground}"
                                      VerticalAlignment="Top"
                                      >
                                                    <GroupBox.Margin>
                                                        <Thickness>0,0,0,0</Thickness>
                                                    </GroupBox.Margin>
                                                    <TextBlock x:Name="Sample_TextBlock"  
                                            Foreground="{StaticResource ContentForeground}"
                                            FontSize="12"
                                           Width="300">
                                                        <TextBlock.Margin>
                                                            <Thickness>10,0,0,0</Thickness>
                                                        </TextBlock.Margin>
                                                    </TextBlock>
                                                </GroupBox>

                                                <TextBlock x:Name="General_TextBlock"
                                   Foreground="{StaticResource ContentForeground}"
                                   FontSize="12"
                                   
                                   TextWrapping="Wrap"
                                   Visibility="Collapsed"                                  
                                   Width="316">
                                                    <TextBlock.Margin>
                                                        <Thickness>0,8,0,0</Thickness>
                                                    </TextBlock.Margin>
                                                </TextBlock>

                                                <TextBlock x:Name="Text_TextBlock" Margin="0,12,0,0"
                                   Foreground="{StaticResource ContentForeground}"
                                   FontSize="12"
                                   
                                   TextWrapping="Wrap"
                                   Visibility="Collapsed"
                                   Width="316"/>

                                                <StackPanel x:Name="Decimal_Stackpanel" Orientation="Horizontal" Visibility="Collapsed">
                                                    <StackPanel.Margin>
                                                        <Thickness>0,9,5,6</Thickness>
                                                    </StackPanel.Margin>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_DecimalPlaces}"   
                                            Width="80" Margin="0,4,0,0" Foreground="{StaticResource ContentForegroundAlt1}" FontSize="12"/>
                                                    <converter:UpDown HorizontalContentAlignment="Left" 
                                                   x:Name="DecimalPlaces_UpDown"
                                                   Width="49" 
                                                   Height="24"
                                                   Margin="7,0,0,0" 
                                                   MaxValue="30"
                                                   MinValue="0" 
                                                   NumberDecimalDigits="0" />
                                                </StackPanel>

                                                <StackPanel x:Name="Symbol_Stackpanel" Orientation="Horizontal" Visibility="Collapsed">
                                                    <StackPanel.Margin>
                                                        <Thickness>0,7,0,2</Thickness>
                                                    </StackPanel.Margin>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Symbol}"   Foreground="{StaticResource ContentForegroundAlt1}" FontSize="12" Width="50"/>
                                                    <TextBlock x:Name="Symbol_TextBlock"   
                                                                Width="266" />
                                                </StackPanel>

                                                <CheckBox x:Name="Separator_Checkbox" 
                                      Visibility="Collapsed"
                                      HorizontalAlignment="Left"
                                      Width="200"
                                      Height="20" 
                                      Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_SeparatorText}">
                                                    <CheckBox.Margin>
                                                        <Thickness>0,5,0,0</Thickness>
                                                    </CheckBox.Margin>
                                                </CheckBox>

                                                <StackPanel x:Name="NegativeNumber_StackPanel" Margin="0,12,0,0" Visibility="Collapsed" >
                                                    <TextBlock HorizontalAlignment="Left" Width="200"   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_NegativeNumber}" Foreground="{StaticResource ContentForegroundAlt1}" FontSize="12"/>
                                                    <ListView x:Name="NegativeNumberList"
                                          HorizontalAlignment="Left"
                                          ScrollViewer.VerticalScrollBarVisibility="Auto"
                                          Width="316"
                                          BorderThickness="1"
                                          SelectedIndex="0"
                                          Height="116" Margin="0,8,0,0" Padding="0" >
                                                    </ListView>
                                                </StackPanel>

                                                <StackPanel x:Name="Type_StackPanel" Visibility="Collapsed">
                                                    <StackPanel.Margin>
                                                        <Thickness >0,12,0,0</Thickness>
                                                    </StackPanel.Margin>
                                                    <TextBlock HorizontalAlignment="Left" Width="200"   Margin="0,0,0,8" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Type}" Foreground="{StaticResource ContentForegroundAlt1}" FontSize="12"/>
                                                    <ListView x:Name="TypeList"
                                          SelectedIndex="0"
                                          ScrollViewer.VerticalScrollBarVisibility="Auto"
                                          Width="316"
                                          BorderThickness="1"
                                          Height="180" >
                                                    </ListView>
                                                </StackPanel>

                                                <StackPanel x:Name="CustomType_StackPanel" Margin="0,11,0,0" Visibility="Collapsed">
                                                    <TextBlock HorizontalAlignment="Left" Margin="0,0,0,8" Width="200"   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Format_Type}" Foreground="{StaticResource ContentForegroundAlt1}" FontSize="12"/>
                                                    <TextBox x:Name="CustomTypeTextBox" Width="316" Style="{StaticResource WPFTextBoxStyle}" Height="24" Text="{Binding ElementName=CustomTypeList, Path=SelectedItem}" HorizontalAlignment="Left"/>
                                                    <ListView x:Name="CustomTypeList"                                          
                                          ScrollViewer.VerticalScrollBarVisibility="Auto"
                                          Width="316"
                                          BorderThickness="1"
                                          SelectedIndex="0"
                                          Height="130" >
                                                    </ListView>
                                                    <Button x:Name="DeleteBtn"
                                        Style="{StaticResource WPFPrimaryButtonStyle}"
                                        Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Delete}"
                                        Width="83"
                                        Height="24"
                                        FontSize="11"
                                        HorizontalAlignment="Right"
                                        Visibility="Collapsed"
                                        IsEnabled="False"
                                        Margin="220,5,0,10"/>
                                                </StackPanel>

                                            </StackPanel>
                                        </StackPanel>

                                        <TextBlock x:Name="Footer_Txtblock" TextWrapping="Wrap" Grid.Row="2" Width="534" HorizontalAlignment="Left"   Foreground="{StaticResource ContentForeground}" FontSize="12" Margin="5" >
                                        </TextBlock>

                                    </Grid>
                                </tools:TabItemExt>

                                <!--Alignment Tab-->

                                <tools:TabItemExt FontSize="12" Height="24" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Alignment}">

                                    <StackPanel Margin="12,0,0,0">
                                        <StackPanel Orientation="Horizontal" Margin="0,13,0,0">
                                            <TextBlock  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_TextAlignment}" 
                                                        Foreground="{StaticResource ContentForeground}"/>
                                            <Separator Width="450" Height="1" />
                                        </StackPanel>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="auto"/>
                                                <ColumnDefinition Width="auto"/>
                                            </Grid.ColumnDefinitions>
                                            <StackPanel Orientation="Vertical" Margin="0,12,8,0">
                                                <TextBlock Width="150" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Horizontal}" FontSize="12"  Foreground="{StaticResource ContentForegroundAlt1}"/>
                                                <ComboBox x:Name="Horizontal_Combo" Width="150" Height="24" Margin="0,8,0,0"/>
                                                <TextBlock x:Name="Indent_TxtBlock" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Vertical}" Margin="0,12,0,0" FontSize="12"  Foreground="{StaticResource ContentForegroundAlt1}"/>
                                                <ComboBox x:Name="Vertical_Combo" Width="150" Height="24" HorizontalAlignment="Left" Margin="0,8,0,0" />
                                            </StackPanel>
                                            <StackPanel Orientation="Vertical" Grid.Column="1" Margin="5,12,0,0">
                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Indent}" FontSize="12"  Foreground="{StaticResource ContentForegroundAlt1}"/>
                                                <converter:UpDown x:Name="Indent_UpDown" Width="70" Height="22" MaxValue="250" MinValue="0" NumberDecimalDigits="0" Margin="0,8,0,12"/>
                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Degrees}" FontSize="12"  Foreground="{StaticResource ContentForegroundAlt1}"/>
                                                <converter:UpDown x:Name="Degrees_UpDown" Width="70" Height="22" MaxValue="90" MinValue="-90" NumberDecimalDigits="0" NegativeForeground="Black" Margin="0,8,0,0"/>
                                            </StackPanel>
                                        </Grid>

                                        <StackPanel Orientation="Horizontal" Margin="0,16,0,0">
                                            <TextBlock  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_TextControl}" 
                                                        Foreground="{StaticResource ContentForeground}"/>
                                            <Separator Width="466" Height="1" />
                                        </StackPanel>

                                        <CheckBox x:Name="Wrap_CheckBox" Margin="0,12,0,0" Width="100" HorizontalAlignment="Left" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=WrapText}"/>
                                        <CheckBox x:Name="Merge_CheckBox" Margin="0,12,0,0" Width="100" HorizontalAlignment="Left" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=MergeCells}"/>

                                    </StackPanel>
                                </tools:TabItemExt>

                                <!--Font Tab-->

                                <tools:TabItemExt FontSize="12" Height="24"  Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_HeaderFont}">

                                    <StackPanel Margin="6,16,0,0">
                                        <StackPanel Orientation="Horizontal"  Margin="0,0,0,8">
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Font}" Margin="5,0,0,0" Width="244" FontSize="12" 
                                                       Foreground="{StaticResource ContentForeground}"/>
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_FontStyle}" Margin="13,0,0,0" Width="148" FontSize="12" 
                                                       Foreground="{StaticResource ContentForeground}"/>
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_FontSize}" Margin="13,0,0,0" Width="110" FontSize="12" 
                                                       Foreground="{StaticResource ContentForeground}"/>
                                        </StackPanel>

                                        <StackPanel Orientation="Horizontal" >
                                            <TextBox x:Name="FontType_TxtBox" Text="{Binding ElementName=FontTypeList, Path=SelectedValue}" Style="{StaticResource WPFTextBoxStyle}"  Height="24" Width="244" Margin="5,0,0,0"/>
                                            <TextBox x:Name="FontStyle_TxtBox" Text="{Binding ElementName=FontStyleList, Path=SelectedValue}" Style="{StaticResource WPFTextBoxStyle}"  Height="24" Width="148" Margin="13,0,0,0"/>
                                            <TextBox x:Name="FontSize_TxtBox" Text="{Binding ElementName=FontSizeList , Path=SelectedValue}" Style="{StaticResource WPFTextBoxStyle}"  Height="24" Width="110" Margin="13,0,0,0"/>
                                        </StackPanel>

                                        <StackPanel Orientation="Horizontal">
                                            <ListView x:Name="FontTypeList"
                                          ScrollViewer.VerticalScrollBarVisibility="Visible"
                                          Width="244"
                                          BorderThickness="1"
                                          Height="112"
                                          Margin="5,0,0,0"/>
                                            <ListView x:Name="FontStyleList"
                                          ScrollViewer.VerticalScrollBarVisibility="Auto"
                                          Width="148"
                                          BorderThickness="1"
                                          Height="112" 
                                          Margin="13,0,0,0"/>
                                            <ListView x:Name="FontSizeList"
                                          ScrollViewer.VerticalScrollBarVisibility="Visible"
                                          Width="110"
                                          BorderThickness="1"
                                          Height="112" 
                                          Margin="13,0,0,0"/>
                                        </StackPanel>

                                        <StackPanel Orientation="Horizontal" Margin="0,12,0,8">
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Underline}" FontSize="12"  Foreground="{StaticResource ContentForegroundAlt1}"
                                                       Margin="5,0,0,0" Width="253"/>
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Color}" FontSize="12" Foreground="{StaticResource ContentForegroundAlt1}" Margin="5,0,0,0" Width="120" />
                                        </StackPanel>

                                        <StackPanel Orientation="Horizontal">
                                            <ComboBox x:Name="Underline_Combo" Margin="5,0,0,0" Width="244"/>

                                            <shared:ColorPickerPalette x:Name="FontColorPicker"
                                                                       BlackWhiteVisibility="Both"
                                                                       BorderWidth="17"
                                                                       BorderHeight="17"
                                                                       Mode="Split"
                                                                       MoreColorOptionVisibility="Collapsed" 
                                                                       Height="24"
                                                                       Width="47"
                                                                       Margin="13,0,8,0"/>

                                            <CheckBox x:Name="NormalFont_Check" Margin="5,0,0,0" Width="100" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_NormalFont}"/>
                                        </StackPanel>

                                        <StackPanel Orientation="Horizontal" Margin="0,8,0,0">

                                            <GroupBox Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Effects}" FontSize="12" Foreground="{StaticResource ContentForeground}" Margin="5,0,0,0" Width="244" Height="85">
                                                <CheckBox x:Name="StrikeThrough_Check" FocusVisualStyle="{x:Null}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="5,5,0,0" Width="100" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Strikethrough}" Foreground="{StaticResource ContentForeground}"/>
                                            </GroupBox>

                                            <GroupBox Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Preview}" FontSize="12" BorderBrush="{StaticResource BorderAlt}"
                                                      Foreground="{StaticResource ContentForeground}" Margin="13,0,0,0" Width="274" Height="85">
                                                <Border BorderThickness="1" BorderBrush="Black" 
                                                        Height="50" Margin="0,-2,0,0" 
                                                        Background="WhiteSmoke">
                                                    <StackPanel Orientation="Horizontal">
                                                        <Separator Margin="-1,0,0,0" Width="81" HorizontalAlignment="Left" Height="1" BorderThickness="0" Background="Black"/>
                                                        <TextBlock x:Name="FontPreview_TxtBlock"
                                           Width="60"                                               
                                           Foreground="{Binding ElementName=FontColorPicker, Path=Color,Converter={StaticResource colorConverter}}"
                                           FontSize="{Binding ElementName=FontSize_TxtBox,Path=Text, Converter={StaticResource stringToFontSizeConverter}, ConverterParameter=24}"
                                           FontStyle="{Binding ElementName=FontStyle_TxtBox,Path=Text,Converter={StaticResource stringToFontStyleConverter}}"
                                           FontWeight="{Binding ElementName=FontStyle_TxtBox,Path=Text,Converter={StaticResource stringToFontWeightConverter}}"
                                           FontFamily="{Binding ElementName=FontType_TxtBox,Path=Text, Converter={StaticResource stringToFontFamilyConverter}}"                                                                                      
                                           HorizontalAlignment="Center"
                                           Margin="13,0,7,0"  
                                           VerticalAlignment="Center" 
                                           Text="AaBbCcYyZz"/>
                                                        <Separator Width="88" HorizontalAlignment="Right" Height="1" BorderThickness="0" Background="Black">
                                                            <Separator.Margin>
                                                                <Thickness>5,0,0,0</Thickness>
                                                            </Separator.Margin>
                                                        </Separator>
                                                        <Line Width="50"></Line>
                                                    </StackPanel>
                                                </Border>
                                            </GroupBox>
                                        </StackPanel>

                                        <TextBlock Margin="5,12,0,0" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Font_FooterText}" FontSize="12"  Foreground="{StaticResource ContentForeground}"/>
                                    </StackPanel>

                                </tools:TabItemExt>

                                <!--Border Tab-->

                                <tools:TabItemExt FontSize="12" Height="24"  Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Border}">

                                    <StackPanel Margin="10,0,0,0">
                                        <StackPanel Orientation="Horizontal" Margin="0,13,0,0">
                                            <GroupBox VerticalAlignment="Top" Width="201" Height="289" FontSize="12" BorderBrush="{StaticResource BorderAlt}"
                                                      Foreground="{StaticResource ContentForeground}" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Line}">
                                                <StackPanel Margin="0,3,0,0">
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Style}" HorizontalAlignment="Left" FontSize="12" Foreground="{StaticResource ContentForegroundAlt1}"/>

                                                    <ListView x:Name="BorderStyleList"                                               
                                              ItemsPanel="{Binding Source={StaticResource ListViewItemsPanelTemplate}}"
                                              ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                              ScrollViewer.VerticalScrollBarVisibility="Disabled"                                              
                                              Width="176"
                                              Margin="0,4,0,10"
                                              BorderThickness="1"
                                              BorderBrush="{StaticResource BorderAlt}"
                                              Height="170">
                                                        <ListView.Padding>
                                                            <Thickness>-1.5,0,0,0</Thickness>
                                                        </ListView.Padding>
                                                        <ListViewItem  Height="10" Padding="16,0,0,0" >
                                                            <ListViewItem.Margin>
                                                                <Thickness>3,0,5,0</Thickness>
                                                            </ListViewItem.Margin>
                                                            <Path Fill="{StaticResource ContentForeground}" Data="M5.44141 6H4.42578L1.75 1.88281C1.68229 1.77865 1.6263 1.67057 1.58203 1.55859H1.55859C1.57943 1.67839 1.58984 1.9349 1.58984 2.32812V6H0.691406V0.398438H1.77344L4.35938 4.41797C4.46875 4.58464 4.53906 4.69922 4.57031 4.76172H4.58594C4.5599 4.61328 4.54688 4.36198 4.54688 4.00781V0.398438H5.44141V6ZM8.50391 6.09375C7.88672 6.09375 7.39323 5.90755 7.02344 5.53516C6.65625 5.16016 6.47266 4.66406 6.47266 4.04688C6.47266 3.375 6.66406 2.85026 7.04688 2.47266C7.43229 2.09505 7.95052 1.90625 8.60156 1.90625C9.22656 1.90625 9.71354 2.08984 10.0625 2.45703C10.4115 2.82422 10.5859 3.33333 10.5859 3.98438C10.5859 4.6224 10.3971 5.13411 10.0195 5.51953C9.64453 5.90234 9.13932 6.09375 8.50391 6.09375ZM8.54688 2.62891C8.19271 2.62891 7.91276 2.7526 7.70703 3C7.5013 3.2474 7.39844 3.58854 7.39844 4.02344C7.39844 4.44271 7.5026 4.77344 7.71094 5.01562C7.91927 5.25521 8.19792 5.375 8.54688 5.375C8.90365 5.375 9.17708 5.25651 9.36719 5.01953C9.5599 4.78255 9.65625 4.44531 9.65625 4.00781C9.65625 3.56771 9.5599 3.22786 9.36719 2.98828C9.17708 2.7487 8.90365 2.62891 8.54688 2.62891ZM15.0742 6H14.168V3.74609C14.168 2.9987 13.9036 2.625 13.375 2.625C13.099 2.625 12.8711 2.72917 12.6914 2.9375C12.5117 3.14323 12.4219 3.40365 12.4219 3.71875V6H11.5117V2H12.4219V2.66406H12.4375C12.737 2.15885 13.1693 1.90625 13.7344 1.90625C14.1693 1.90625 14.5013 2.04818 14.7305 2.33203C14.9596 2.61328 15.0742 3.02083 15.0742 3.55469V6ZM19.5703 4.24609H16.8438C16.8542 4.61589 16.9674 4.90104 17.1836 5.10156C17.4023 5.30208 17.7018 5.40234 18.082 5.40234C18.5091 5.40234 18.901 5.27474 19.2578 5.01953V5.75C18.8932 5.97917 18.4115 6.09375 17.8125 6.09375C17.224 6.09375 16.7617 5.91276 16.4258 5.55078C16.0924 5.1862 15.9258 4.67448 15.9258 4.01562C15.9258 3.39323 16.1094 2.88672 16.4766 2.49609C16.8464 2.10286 17.3047 1.90625 17.8516 1.90625C18.3984 1.90625 18.8216 2.08203 19.1211 2.43359C19.4206 2.78516 19.5703 3.27344 19.5703 3.89844V4.24609ZM18.6953 3.60547C18.6927 3.27995 18.6159 3.02734 18.4648 2.84766C18.3138 2.66536 18.1055 2.57422 17.8398 2.57422C17.5794 2.57422 17.3581 2.66927 17.1758 2.85938C16.9961 3.04948 16.8854 3.29818 16.8438 3.60547H18.6953Z" ></Path>
                                                        </ListViewItem>
                                                        <ListViewItem Height="10"  Padding="16,0,0,0"  >
                                                            <ListViewItem.Margin>
                                                                <Thickness>7,0,1,0</Thickness>
                                                            </ListViewItem.Margin>
                                                            <Path Fill="{StaticResource ContentForeground}" Data="M3 0H0V1V2H3V1V0ZM6 2V1V0H13V1V2H6ZM30 2V1V0H37V1V2H30ZM23 2V1V0H26V1V2H23ZM19 1V2H16V1V0H19V1ZM44 2V1V0H41V1V2H44Z" ></Path>
                                                        </ListViewItem>
                                                        <ListViewItem Height="10" Padding="16,0,0,0" >
                                                            <ListViewItem.Margin>
                                                                <Thickness>3,0,5,0</Thickness>
                                                            </ListViewItem.Margin>
                                                            <Path Fill="{StaticResource ContentForeground}" Data="M1 0H0V1H1V0ZM3 0H2V1H3V0ZM4 0H5V1H4V0ZM7 0H6V1H7V0ZM8 0H9V1H8V0ZM11 0H10V1H11V0ZM12 0H13V1H12V0ZM15 0H14V1H15V0ZM16 0H17V1H16V0ZM19 0H18V1H19V0ZM20 0H21V1H20V0ZM23 0H22V1H23V0ZM24 0H25V1H24V0ZM27 0H26V1H27V0ZM28 0H29V1H28V0ZM31 0H30V1H31V0ZM32 0H33V1H32V0ZM35 0H34V1H35V0ZM36 0H37V1H36V0ZM39 0H38V1H39V0ZM40 0H41V1H40V0ZM43 0H42V1H43V0Z" ></Path>
                                                        </ListViewItem>
                                                        <ListViewItem Height="10"  Padding="16,0,0,0" >
                                                            <ListViewItem.Margin>
                                                                <Thickness>7,0,1,0</Thickness>
                                                            </ListViewItem.Margin>
                                                            <Path Fill="{StaticResource ContentForeground}" Data="M3 0H0V1V2H3V1V0ZM6 2V1V0H16V1V2H6ZM28 2V1V0H38V1V2H28ZM41 2V1V0H44V1V2H41ZM20 2V1V0H23V1V2H20Z" ></Path>
                                                        </ListViewItem>
                                                        <ListViewItem Height="10" Padding="16,0,0,0" >
                                                            <ListViewItem.Margin>
                                                                <Thickness>3,0,5,0</Thickness>
                                                            </ListViewItem.Margin>
                                                            <Path Fill="{StaticResource ContentForeground}" Data="M1 0H0V1H1V0ZM5 0H3V1H5V0ZM7 0H9V1H7V0ZM13 0H11V1H13V0ZM15 0H17V1H15V0ZM21 0H19V1H21V0ZM23 0H25V1H23V0ZM29 0H27V1H29V0ZM31 0H33V1H31V0ZM37 0H35V1H37V0ZM39 0H41V1H39V0ZM44 0H43V1H44V0Z" ></Path>
                                                        </ListViewItem>
                                                        <ListViewItem Height="10"  Padding="16,0,0,0" >
                                                            <ListViewItem.Margin>
                                                                <Thickness>7,0,1,0</Thickness>
                                                            </ListViewItem.Margin>
                                                            <Path Fill="{StaticResource ContentForeground}" Data="M5 0H0V1V2H5V1V0ZM9 2V1V0H14V1V2H9ZM19 2V1V0H24V1V2H19ZM29 2V1V0H34V1V2H29ZM39 2V1V0H44V1V2H39Z" ></Path>
                                                        </ListViewItem>
                                                        <ListViewItem Height="10" Padding="16,0,0,0" >
                                                            <ListViewItem.Margin>
                                                                <Thickness>3,0,5,0</Thickness>
                                                            </ListViewItem.Margin>
                                                            <Path Fill="{StaticResource ContentForeground}" Data="M0 0H5V1H0V0ZM8 0H11V1H8V0ZM31 0H28V1H31V0ZM14 0H17V1H14V0ZM36 0H33V1H36V0ZM20 0H25V1H20V0ZM44 0H39V1H44V0Z" ></Path>
                                                        </ListViewItem>
                                                        <ListViewItem Height="10"  Padding="16,0,0,0" >
                                                            <ListViewItem.Margin>
                                                                <Thickness>7,0,1,0</Thickness>
                                                            </ListViewItem.Margin>
                                                            <Path Fill="{StaticResource ContentForeground}" Data="M42 0H0V1V2H42V1V0Z" ></Path>
                                                        </ListViewItem>
                                                        <ListViewItem Height="10" Padding="16,0,0,0" >
                                                            <ListViewItem.Margin>
                                                                <Thickness>3,0,5,0</Thickness>
                                                            </ListViewItem.Margin>
                                                            <Path Fill="{StaticResource ContentForeground}" Data="M0 0H3V1H0V0ZM6 0H16V1H6V0ZM38 0H28V1H38V0ZM41 0H44V1H41V0ZM23 0H20V1H23V0Z" ></Path>
                                                        </ListViewItem>
                                                        <ListViewItem Height="10"  Padding="16,0,0,0" >
                                                            <ListViewItem.Margin>
                                                                <Thickness>7,0,1,0</Thickness>
                                                            </ListViewItem.Margin>
                                                            <Path Fill="{StaticResource ContentForeground}" Data="M0 0H42V1V2V3H0V2V1V0Z" ></Path>
                                                        </ListViewItem>
                                                        <ListViewItem Height="10" Padding="16,0,0,0" >
                                                            <ListViewItem.Margin>
                                                                <Thickness>3,0,5,0</Thickness>
                                                            </ListViewItem.Margin>
                                                            <Path Fill="{StaticResource ContentForeground}" Data="M0 0H2V1H0V0ZM4 0H6V1H4V0ZM10 0H8V1H10V0ZM12 0H14V1H12V0ZM18 0H16V1H18V0ZM20 0H22V1H20V0ZM26 0H24V1H26V0ZM28 0H30V1H28V0ZM34 0H32V1H34V0ZM36 0H38V1H36V0ZM42 0H40V1H42V0Z" ></Path>
                                                        </ListViewItem>
                                                        <ListViewItem Height="10"  Padding="16,0,0,0" >
                                                            <ListViewItem.Margin>
                                                                <Thickness>7,0,1,0</Thickness>
                                                            </ListViewItem.Margin>
                                                            <Path Fill="{StaticResource ContentForeground}" Data="M1 0H0V1H1H2H42V0H2H1Z" ></Path>
                                                        </ListViewItem>
                                                    </ListView>

                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Color}" Width="177" FontSize="12" Foreground="{StaticResource ContentForegroundAlt1}"/>
                                                    <shared:ColorPickerPalette x:Name="BorderColorPicker"
                                                                       BlackWhiteVisibility="Both"
                                                                       BorderWidth="17"
                                                                       BorderHeight="17"
                                                                       Mode="Split"
                                                                       Width="177"
                                                                       Margin="0,8,0,0"
                                                                       MoreColorOptionVisibility="Collapsed" 
                                                                       Height="24"/>
                                                </StackPanel>
                                            </GroupBox>

                                            <StackPanel Margin="8,11,0,0" >
                                                <!--Presets stackpanel-->

                                                <StackPanel  Orientation="Horizontal" Margin="5,0,0,10" >
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Presets}" Width="50" 
                                                               Foreground="{StaticResource ContentForeground}"/>
                                                    <Separator Width="277" Height="1"  Foreground="{StaticResource BorderAlt}"/>
                                                </StackPanel>

                                                <StackPanel Orientation="Horizontal" >
                                                    <Button Margin="75,0,0,0" x:Name="Border_NoneBtn"  
                                                            Background="Transparent" BorderThickness="1" Width="45" Height="44">
                                                        <StackPanel>
                                                            <Path Fill="{StaticResource BorderAlt3}" Data="M0 0V1H1V0H0ZM1 11H0V13H1V11ZM0 3H1V5H0V3ZM1 7H0V9H1V7ZM30 11H31V13H30V11ZM16 11H15V13H16V11ZM30 7H31V9H30V7ZM16 7H15V9H16V7ZM30 3H31V5H30V3ZM16 3H15V5H16V3ZM11 31V30H13V31H11ZM11 0V1H13V0H11ZM7 31V30H9V31H7ZM7 0V1H9V0H7ZM3 31V30H5V31H3ZM3 0V1H5V0H3ZM18 15H20V16H18V15ZM24 15H22V16H24V15ZM26 15H28V16H26V15ZM13 15H11V16H13V15ZM7 15H9V16H7V15ZM5 15H3V16H5V15ZM30 15H31V16H30V15ZM15 30V31H16V30H15ZM15 1V0H16V1H15ZM1 18H0V20H1V18ZM0 22H1V24H0V22ZM1 26H0V28H1V26ZM30 18H31V20H30V18ZM16 18H15V20H16V18ZM30 22H31V24H30V22ZM16 22H15V24H16V22ZM30 26H31V28H30V26ZM16 26H15V28H16V26ZM18 31V30H20V31H18ZM18 0V1H20V0H18ZM22 31V30H24V31H22ZM22 0V1H24V0H22ZM26 31V30H28V31H26ZM26 0V1H28V0H26ZM1 30H0V31H1V30ZM30 30H31V31H30V30ZM31 0H30V1H31V0ZM0 15H1V16H0V15ZM16 15H15V16H16V15Z" ></Path>
                                                        </StackPanel>
                                                    </Button>
                                                    <Button x:Name="Border_OutsideBtn" Margin="10,0,0,0"  
                                                            Background="Transparent" BorderThickness="1" Width="45" Height="44">
                                                        <StackPanel>
                                                            <Path Fill="{StaticResource BorderAlt3}" Data="M0 1.43051e-06V1V7V9V11V13V15V16V18V20V22V24V26V28V30V31H1H3H5H7H9H11H13H15H16H18H20H22H24H26H28H30H31V30V28V26V24V22V20V18V16V15V13V11V9V7V5V3V0.999999V0H30H28H26H24H22H20H18H16H15H13H11H9H7H5H3H1L0 1.43051e-06ZM1 16V15V13V11V9V7V1H3H5H7H9H11H13H15H16H18H20H22H24H26H28H30V3V5V7V9V11V13V15V16V18V20V22V24V26V28V30H28H26H24H22H20H18H16H15H13H11H9H7H5H3H1V28V26V24V22V20V18V16ZM16 11H15V13H16V11ZM16 7H15V9H16V7ZM16 3H15V5H16V3ZM18 15H20V16H18V15ZM24 15H22V16H24V15ZM26 15H28V16H26V15ZM13 15H11V16H13V15ZM7 15H9V16H7V15ZM5 15H3V16H5V15ZM16 18H15V20H16V18ZM16 22H15V24H16V22ZM16 26H15V28H16V26ZM16 15H15V16H16V15Z" ></Path>
                                                        </StackPanel>
                                                    </Button>
                                                    <Button x:Name="Border_InsideBtn" Margin="10,0,0,0"  
                                                            Background="Transparent" BorderThickness="1" Width="45" Height="44">
                                                        <StackPanel>
                                                            <Path Fill="{StaticResource BorderAlt3}" Data="M0 0V1H1V0H0ZM1 11H0V13H1V11ZM0 3H1V5H0V3ZM1 7H0V9H1V7ZM30 11H31V13H30V11ZM16 13V15H18H20H22H24H26H28V16H26H24H22H20H18H16V18V20V22V24V26V28H15V26V24V22V20V18V16H13H11H9H7H3V15H7H9H11H13H15V13V11V9V7V5V3H16V5V7V9V11V13ZM30 7H31V9H30V7ZM30 3H31V5H30V3ZM11 31V30H13V31H11ZM11 0V1H13V0H11ZM7 31V30H9V31H7ZM7 0V1H9V0H7ZM3 31V30H5V31H3ZM3 0V1H5V0H3ZM30 15H31V16H30V15ZM15 30V31H16V30H15ZM15 1V0H16V1H15ZM1 18H0V20H1V18ZM0 22H1V24H0V22ZM1 26H0V28H1V26ZM30 18H31V20H30V18ZM30 22H31V24H30V22ZM30 26H31V28H30V26ZM18 31V30H20V31H18ZM18 0V1H20V0H18ZM22 31V30H24V31H22ZM22 0V1H24V0H22ZM26 31V30H28V31H26ZM26 0V1H28V0H26ZM1 30H0V31H1V30ZM30 30H31V31H30V30ZM31 0H30V1H31V0ZM0 15H1V16H0V15Z" ></Path>
                                                        </StackPanel>
                                                    </Button>
                                                </StackPanel>

                                                <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_None}" Foreground="{StaticResource ContentForeground}" FontSize="12" Width="45" Margin="84,0,0,0"/>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Outline}" Foreground="{StaticResource ContentForeground}" FontSize="12" Width="45" Margin="4,0,0,0"/>
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Inside}" Foreground="{StaticResource ContentForeground}" FontSize="12" Width="45" Margin="14,0,0,0"/>
                                                </StackPanel>

                                                <StackPanel Orientation="Horizontal" Margin="5,8,0,10" >
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Border}" FontSize="12" Width="50" 
                                                               Foreground="{StaticResource ContentForeground}"/>
                                                    <Separator Width="277" Height="1"  Foreground="{StaticResource BorderAlt}"/>
                                                </StackPanel>

                                                <StackPanel Orientation="Horizontal" Margin="24,5,0,0">

                                                    <StackPanel HorizontalAlignment="Left" Margin="10,0,4,0">
                                                        <ToggleButton Style="{StaticResource BorderTabToggleButtonStyle}" Background="Transparent" 
                                                                      Margin="10,-5,0,0" x:Name="Border_TopBtn" Width="24" Height="24" BorderBrush="{StaticResource BorderAlt}">
                                                            <Path Fill="{StaticResource BorderAlt3}" Data="M0 0H1H3H5H7H8H10H12H14H15V1L14 1H12H10H8H7H5H3H1H0V0ZM0 3H1V5H0V3ZM15 3H14V5H15V3ZM3 15V14H5V15H3ZM1 7H0V8H1V7ZM6 7H7V6H8V7H9V8H8V9H7V8H6V7ZM15 7H14V8H15V7ZM7 15V14H8V15H7ZM7 11V12H8V11H7ZM7 4V3H8V4H7ZM0 10H1V12H0V10ZM15 10H14V12H15V10ZM10 15V14H12V15H10ZM1 14H0V15H1V14ZM14 14H15V15H14V14Z" ></Path>
                                                        </ToggleButton>
                                                        <ToggleButton Style="{StaticResource BorderTabToggleButtonStyle}" Background="Transparent" 
                                                                       Margin="10,12,0,0" x:Name="Border_HCenterBtn" Width="24" Height="24" BorderBrush="{StaticResource BorderAlt}">
                                                            <Path Fill="{StaticResource BorderAlt3}" Data="M0 1V0H1V1H0ZM0 3H1V5H0V3ZM15 3H14V5H15V3ZM3 15V14H5V15H3ZM1 7H0V8H1H6H7V9H8V8H9H14H15V7H14H9H8V6H7V7H6H1ZM7 15V14H8V15H7ZM7 0V1H8V0H7ZM7 11V12H8V11H7ZM7 4V3H8V4H7ZM0 10H1V12H0V10ZM15 10H14V12H15V10ZM10 15V14H12V15H10ZM10 0V1H12V0H10ZM1 14H0V15H1V14ZM14 14H15V15H14V14ZM15 0H14V1H15V0ZM3 1V0H5V1H3Z" ></Path>
                                                        </ToggleButton>
                                                        <ToggleButton Style="{StaticResource BorderTabToggleButtonStyle}" Background="Transparent" 
                                                                       Margin="10,12,0,0" x:Name="Border_BottomBtn" Width="24" Height="24" BorderBrush="{StaticResource BorderAlt}">
                                                            <Path Fill="{StaticResource BorderAlt3}" Data="M0 1V0H1V1H0ZM0 3H1V5H0V3ZM15 3H14V5H15V3ZM5 15H3H1H0V14H1H3H5H7H8H10H12H14H15V15H14H12H10H8H7H5ZM1 7H0V8H1V7ZM6 7H7V6H8V7H9V8H8V9H7V8H6V7ZM15 7H14V8H15V7ZM7 0V1H8V0H7ZM7 11V12H8V11H7ZM7 4V3H8V4H7ZM0 10H1V12H0V10ZM15 10H14V12H15V10ZM10 0V1H12V0H10ZM15 0H14V1H15V0ZM3 1V0H5V1H3Z" ></Path>
                                                        </ToggleButton>
                                                    </StackPanel>

                                                    <StackPanel Margin="5,-4,0,0">
                                                        <Border Width="154" Height="98" BorderThickness="1" BorderBrush="{StaticResource BorderAlt3}">

                                                            <Grid >

                                                                <Line x:Name="Top_Line" X1="0" Y1="10" X2="130" Y2="10" Width="130" Stroke="Black" StrokeThickness="1"/>
                                                                <Line x:Name="Bottom_Line" X1="0" Y1="86" Y2="86" X2="130" Width="130" Stroke="Black" StrokeThickness="1"/>
                                                                <Line x:Name="HCenter_Line" X1="0" Y1="0" X2="130" Width="130" VerticalAlignment="Center" Stroke="Black" StrokeThickness="1"/>
                                                                <Line x:Name="Left_Line" X1="11" Y1="0" X2="11" Y2="77" Height="77" Stroke="Black" StrokeThickness="1"/>
                                                                <Line x:Name="Right_Line" X1="141" X2="141" Y1="0" Y2="77" Height="77" Stroke="Black" StrokeThickness="1"/>
                                                                <Line x:Name="VCenter_Line" X1="75" X2="75" Y1="0" Y2="77" Height="77" Stroke="Black" StrokeThickness="1"/>

                                                            </Grid>

                                                        </Border>

                                                        <StackPanel Orientation="Horizontal" Width="154">
                                                            <ToggleButton Style="{StaticResource BorderTabToggleButtonStyle}" Background="Transparent"
                                                                           x:Name="Border_LeftBtn" Width="24" Height="24" Margin="0,5,0,0" BorderBrush="{StaticResource BorderAlt}">
                                                                <Path Fill="{StaticResource BorderAlt3}" Data="M0 1V0H1V1V3V5V7V8V10V12V14V15H0V14V12V10V8V7V5V3V1ZM15 3H14V5H15V3ZM3 15V14H5V15H3ZM3 0V1H5V0H3ZM6 7H7V6H8V7H9V8H8V9H7V8H6V7ZM15 7H14V8H15V7ZM7 15V14H8V15H7ZM7 0V1H8V0H7ZM7 11V12H8V11H7ZM7 4V3H8V4H7ZM15 10H14V12H15V10ZM10 15V14H12V15H10ZM10 0V1H12V0H10ZM14 14H15V15H14V14ZM15 0H14V1H15V0Z" ></Path>
                                                            </ToggleButton>
                                                            <ToggleButton Style="{StaticResource BorderTabToggleButtonStyle}" Background="Transparent" 
                                                                           x:Name="Border_VCenterBtn" Width="24" Height="24" Margin="40,5,0,0" BorderBrush="{StaticResource BorderAlt}">
                                                                <Path Fill="{StaticResource BorderAlt3}" Data="M0 0V1H1V0H0ZM1 3H0V5H1V3ZM14 3H15V5H14V3ZM3 14V15H5V14H3ZM3 1V0H5V1H3ZM5 7H3V8H5V7ZM10 7H12V8H10V7ZM15 7H14V8H15V7ZM8 14V15H7V14V12V11V9V6V4V3V1V0H8V1V3V4V6V9V11V12V14ZM0 10H1V12H0V10ZM15 10H14V12H15V10ZM10 15V14H12V15H10ZM10 0V1H12V0H10ZM1 14H0V15H1V14ZM14 14H15V15H14V14ZM15 0H14V1H15V0ZM0 7H1V8H0V7Z" ></Path>
                                                            </ToggleButton>
                                                            <ToggleButton Style="{StaticResource BorderTabToggleButtonStyle}" Background="Transparent" 
                                                                           x:Name="Border_RightBtn" Width="24" Height="24" Margin="40,5,0,0" BorderBrush="{StaticResource BorderAlt}">
                                                                <Path Fill="{StaticResource BorderAlt3}" Data="M0 1V0H1V1H0ZM0 3H1V5H0V3ZM15 5V7V8V10V12V14V15H14V14V12V10V8V7V5V3V1V0H15V1V3V5ZM3 15V14H5V15H3ZM3 0V1H5V0H3ZM6 7H7V6H8V7H9V8H8V9H7V8H6V7ZM7 15V14H8V15H7ZM7 0V1H8V0H7ZM7 11V12H8V11H7ZM7 4V3H8V4H7ZM0 10H1V12H0V10ZM10 15V14H12V15H10ZM10 0V1H12V0H10ZM1 14H0V15H1V14ZM0 7H1V8H0V7Z" ></Path>
                                                            </ToggleButton>
                                                        </StackPanel>
                                                    </StackPanel>
                                                </StackPanel>
                                            </StackPanel>
                                        </StackPanel>
                                        <TextBlock Width="540" Margin="0,10,0,0" HorizontalAlignment="Left" TextWrapping="Wrap"  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Border_FooterText}" Foreground="{StaticResource ContentForeground}" FontSize="12"/>
                                    </StackPanel>

                                </tools:TabItemExt>

                                <!--Fill Tab-->

                                <tools:TabItemExt FontSize="12" Height="24"  Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Fill}">

                                    <StackPanel Margin="12,0,0,0">

                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_BackgroundColor}" Foreground="{StaticResource ContentForeground}"
                                                    Margin="0,12,0,0"/>
                                        <ToggleButton x:Name="NoColor_Btn" FocusVisualStyle="{x:Null}" Width="186" HorizontalAlignment="Left" Height="22" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_NoColor}" Margin="0,16,0,2"/>
                                        <shared:ColorPickerPalette x:Name="FillColor_Palette"
                                                                       AutomaticColorVisibility="Collapsed"
                                                                       StandardPanelVisibility="Collapsed"
                                                                       RecentlyUsedPanelVisibility="Collapsed"
                                                                       BlackWhiteVisibility="Both"
                                                                       BorderHeight="15"
                                                                       BorderWidth="15"
                                                                       IsExpanded="True"                                                                          
                                                                       Width="199" 
                                                                       HorizontalAlignment="Left" 
                                                                       Height="223" Margin="-6.5,0,0,8"/>
                                        <GroupBox Width="536" Height="63" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Sample}" HorizontalAlignment="Left" VerticalAlignment="Bottom" FontSize="12" Foreground="{StaticResource ContentForeground}">
                                            <TextBlock x:Name="FillPreview_TxtBlock" Background="{Binding ElementName=FillColor_Palette, Path=Color,Converter={StaticResource colorConverter}}" HorizontalAlignment="Center" VerticalAlignment="Center" Height="45" Width="516"/>
                                        </GroupBox>

                                    </StackPanel>

                                </tools:TabItemExt>

                                <!--Protection Tab-->

                                <tools:TabItemExt FontSize="12" Height="24" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Protection}">

                                    <StackPanel Margin="11,16,0,0">
                                        <CheckBox x:Name="Locked_CheckBox" Width="100" HorizontalAlignment="Left" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Locked}"/>
                                        <CheckBox x:Name="Hidden_CheckBox" Width="100" HorizontalAlignment="Left" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Hidden}" Margin="0,12,0,12">
                                        </CheckBox>
                                        <TextBlock HorizontalAlignment="Left" Width="460" TextWrapping="Wrap" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=FormatCells_Protection_FooterText}" FontSize="12"  Foreground="{StaticResource ContentForeground}"/>
                                    </StackPanel>

                                </tools:TabItemExt>

                            </tools:TabControlExt>
                            <Border Grid.Row="2" BorderBrush="{StaticResource BorderAlt}" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="ApplyButton"
                                        Grid.Column="0" 
                                        Width="64" Height="24" Style="{StaticResource WPFPrimaryButtonStyle}" 
                                        Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}"  
                                        Margin="379 2 5 10 "/>
                                    <Button x:Name="CancelButton"
                                        Grid.Column="1"
                                         Width="84" Height="24" 
                                        Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}" 
                                        Margin="5 2 5 10 "/>
                                </Grid>
                            </Border>
                        </Grid>
                    
                    </Border>
                    
                </ControlTemplate>
            </Setter.Value>
        </Setter>

    </Style>

    <Style BasedOn="{StaticResource FormatCellsDialog}" TargetType="spreadsheet:FormatCellsDialog"></Style>

    <Style x:Key="ProtectWorkbookDialog" TargetType="spreadsheet:ProtectWorkbookDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:ProtectWorkbookDialog">
                    <Border Background="{StaticResource PopupBackground}">
                        <Border.CornerRadius>
                            <CornerRadius>0,0,8,8</CornerRadius>
                        </Border.CornerRadius>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition></RowDefinition>
                                <RowDefinition Height="49"></RowDefinition>
                            </Grid.RowDefinitions>
                            <StackPanel Background="{StaticResource ContentBackground}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="131"/>
                                        <ColumnDefinition/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock x:Name="Description" Margin="16,10,1,6" Height="16"  Style="{StaticResource DescriptionTextBlockStyle}" />
                                    <Separator Background="#DDDDDD" Grid.Column="1"  
                                               Margin="1 8 16 0" Width="250" Height="1" />
                                </Grid>
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Left">
                                    <CheckBox Margin="26,6,8,7" x:Name="Structure_Check" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Structure}" IsChecked="False" />
                                    <CheckBox Margin="26,7,8,6" IsEnabled="False" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Windows}" IsChecked="{Binding IsWindows, ElementName=protectWindow, Mode=TwoWay}"/>
                                </StackPanel>
                                <TextBlock x:Name="DescriptionText" Margin="16,6,0,4"  Style="{StaticResource DescriptionTextBlockStyle}"/>
                                <PasswordBox x:Name="txtValue" Margin="16 4 16 16" Height="24"/>                                
                            </StackPanel>
                            <Border Grid.Row="1" BorderBrush="{StaticResource BorderAlt}">
                                <Border.BorderThickness>
                                    <Thickness>0,1,0,0</Thickness>
                                </Border.BorderThickness>
                                <Grid  HorizontalAlignment="Right">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="Apply" Grid.Column="0 "
                                                Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}" Width="64" Height="24" 
                                                Margin="266 12 5 12 "/>
                                    <Button x:Name="Cancel" Grid.Column="1 "
                                                 Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}" Width="84" Height="24" 
                                                Margin="4 12 16 12 " />
                                </Grid>

                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource ProtectWorkbookDialog}" TargetType="spreadsheet:ProtectWorkbookDialog"></Style>

    <Style x:Key="ProtectSheetDialog" TargetType="spreadsheet:ProtectSheetDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:ProtectSheetDialog">
                    <Grid Background="{StaticResource ContentBackground}">
                        <Grid.RowDefinitions>
                            <RowDefinition  Height="28" />
                            <RowDefinition  Height="24" />
                            <RowDefinition  Height="32"/>
                            <RowDefinition  Height="26"/>
                            <RowDefinition  Height="221"/>
                            <RowDefinition   />
                        </Grid.RowDefinitions>
                        <TextBlock x:Name="Description" Margin="16,8,16,4"  Style="{StaticResource DescriptionTextBlockStyle}"/>
                        <TextBlock x:Name="DescriptionText" Grid.Row="1"  Margin="16,4,16,4" Style="{StaticResource DescriptionTextBlockStyle}"/>
                        <PasswordBox  x:Name="txtValue" Margin="16,4,16,4" Grid.Row="2"/>
                        <Label x:Name="DescriptionLbl" Grid.Row="3" Margin="12,1,16,0" Foreground="{StaticResource ContentForeground}" Style="{StaticResource DescriptionLabelStyle}"/>
                        <Border Grid.Row="4" Margin="16,4,16,16" BorderThickness="1" BorderBrush="{StaticResource BorderAlt}" CornerRadius="{StaticResource Windows11Light.CornerRadius4}" 
                                Width="364" >
                            <ScrollViewer>
                                <StackPanel >
                                    <CheckBox x:Name="LockedChkBox" IsChecked="True" Height="20" Margin="10,5,3,2" Padding ="8,0,0,0" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectOptions_Locked}"/>
                                    <CheckBox x:Name="UnlockedChkBox" IsChecked="True" Height="20" Margin="10,2,3,2"  Padding ="8,0,0,0" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectOptions_Unlocked}" />
                                    <CheckBox x:Name="FormatcellChkBox" Height="20" Margin="10,2,3,2" Padding ="8,0,0,0" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectOptions_Formatcells}" />
                                    <CheckBox x:Name="FormatcolChkBox" Height="20" Margin="10,2,3,2" Padding ="8,0,0,0" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectOptions_Formatcolumns}" />
                                    <CheckBox x:Name="FormatrowChkBox" Height="20" Margin="10,2,3,2" Padding ="8,0,0,0" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectOptions_Formatrows}" />
                                    <CheckBox x:Name="InsertcolChkBox" Height="20" Margin="10,2,3,2" Padding ="8,0,0,0" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectOptions_Insertcolumns}" />
                                    <CheckBox x:Name="InsertrowChkBox" Height="20" Margin="10,2,3,2" Padding ="8,0,0,0" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectOptions_Insertrows}" />
                                    <CheckBox x:Name="InserthyperlinkChkBox" Height="20" Margin="10,2,3,2" Padding ="8,0,0,0" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectOptions_InsertHyperlink}" />
                                    <CheckBox x:Name="DeleterowChkBox" Height="20" Margin="10,2,3,2" Padding ="8,0,0,0" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectOptions_Deleterows}" />
                                    <CheckBox x:Name="DeletecolChkBox" Height="20" Margin="10,2,3,2" Padding ="8,0,0,0" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectOptions_Deletecolumns}" />
                                    <CheckBox x:Name="EditobjectChkBox" Height="20" Margin="10,2,3,5" Padding ="8,0,0,0" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ProtectOptions_EditObjects}" />
                                </StackPanel>
                            </ScrollViewer>
                        </Border>
                        <Border Grid.Row="5" BorderBrush="{StaticResource BorderAlt}">
                            <Border.BorderThickness>
                                <Thickness>0,1,0,0</Thickness>
                            </Border.BorderThickness>
                            <Grid Height="49"
                                        Background="{StaticResource PopupBackground}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Button x:Name="Apply" Grid.Column="0"
                                            Margin="224 12 4 12 " Width="64" Height="24" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}" 
                                            Style="{StaticResource WPFPrimaryButtonStyle}"/>
                                <Button x:Name="Cancel" Grid.Column="1"
                                            Margin="4 12 16 12 " Width="84" Height="24" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}" 
                                            Style="{StaticResource WPFButtonStyle}" HorizontalAlignment="Left"/>
                            </Grid>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource ProtectSheetDialog}" TargetType="spreadsheet:ProtectSheetDialog"></Style>

    <Style x:Key="InsertDeleteCellsDialog" TargetType="spreadsheet:InsertDeleteCellsDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:InsertDeleteCellsDialog">
                    <Border Background="{StaticResource PopupBackground}">
                        <Border.CornerRadius>
                            <CornerRadius>0,0,8,8</CornerRadius>
                        </Border.CornerRadius>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="26"/>
                                <RowDefinition Height="auto"/>
                                <RowDefinition Height="49"/>
                            </Grid.RowDefinitions>
                            <StackPanel Orientation="Horizontal"  Background="{StaticResource ContentBackground}" >
                                <TextBlock x:Name="InsertDelete" FontSize="12" Margin="16,10,2,0"  Style="{StaticResource DescriptionTextBlockStyle}" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Insert}"></TextBlock>
                                <Separator  Background="#DDDDDD" Height="1"  Margin="2,10,16,0" Width="141"/>
                            </StackPanel>
                            <StackPanel Orientation="Vertical" Grid.Row="1"  Background="{StaticResource ContentBackground}" >
                                <RadioButton x:Name="ShiftCellsRight" IsChecked="True" Margin="26,13,8,7" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ShiftCellsRight}"/>
                                <RadioButton x:Name="ShiftCellsDown" Margin="26,7,8,7" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ShiftCellsDown}"/>
                                <RadioButton x:Name="EntireRow" Margin="26,7,8,7" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=EntireRow}"/>
                                <RadioButton x:Name="EntireColumn" Margin="26,7,8,17" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=EntireColumn}"/>
                            </StackPanel>
                            <Border Grid.Row="2" BorderBrush="{StaticResource BorderAlt}">
                                <Border.BorderThickness>
                                    <Thickness>0,1,0,0</Thickness>
                                </Border.BorderThickness>
                                <Grid  HorizontalAlignment="Right">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="Apply" Grid.Column="0 "
                                                Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}" 
                                                Height="24" Width="84 " 
                                                Margin="16 12 4 12 "/>
                                    <Button x:Name="Cancel"  Grid.Column="1 "
                                                 Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}" 
                                                Height="24" Width="84 " 
                                                Margin="4 12 16 12 "/>
                                </Grid>

                            </Border>
                        </Grid> 
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource InsertDeleteCellsDialog}" TargetType="spreadsheet:InsertDeleteCellsDialog"></Style>

    <Style x:Key="UnprotectWorkbookDialog" TargetType="spreadsheet:UnprotectWorkbookDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:UnprotectWorkbookDialog">
                    <Border Background="{StaticResource PopupBackground}">
                        <Border.CornerRadius>
                            <CornerRadius>0,0,8,8</CornerRadius>
                        </Border.CornerRadius>
                        <Grid>
                            <StackPanel>
                                <Grid Background="{StaticResource ContentBackground}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="72"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock x:Name="Password"  Margin="15,16,2,18" Width="Auto" FontSize="12"/>
                                    <PasswordBox x:Name="txtValue" Margin="2 13 15 11" Grid.Column="1" Height="24" Width="193" HorizontalAlignment="Stretch"/>
                                </Grid>
                                <Border BorderBrush="{StaticResource BorderAlt}">
                                    <Border.BorderThickness>
                                        <Thickness>0,1,0,0</Thickness>
                                    </Border.BorderThickness>
                                    <Grid HorizontalAlignment="Right">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <Button x:Name="Apply" Grid.Column="0 "
                                                    Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}" Width="64" Height="24" 
                                                    Margin="115 12 4 12 " />
                                        <Button x:Name="Cancel" Grid.Column="1 "
                                                     Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}" Width="84" Height="24" 
                                                     Margin="4 12 16 12 " />
                                    </Grid>
                                </Border>
                            </StackPanel>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
	
    <Style BasedOn="{StaticResource UnprotectWorkbookDialog}" TargetType="spreadsheet:UnprotectWorkbookDialog"></Style>
	
	<Style x:Key="UnprotectSheetDialog" TargetType="spreadsheet:UnprotectSheetDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:UnprotectSheetDialog">
                    <StackPanel>
                        <Grid Background="{StaticResource ContentBackground}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="76"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock x:Name="Password" Height="16" Margin="16 13 2 13" 
                                       Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=PasswordtoUnProtect}"/>
                            <PasswordBox x:Name="txtValue" Margin="2 13 16 13" Height="24" Grid.Column="1" HorizontalAlignment="Stretch"/>
                        </Grid>
                        <Border BorderBrush="{StaticResource BorderAlt}">
                            <Border.BorderThickness>
                                <Thickness>0,1,0,0</Thickness>
                            </Border.BorderThickness>
                            <Grid  Height="48" Background="{StaticResource PopupBackground}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Button x:Name="Apply"  Grid.Column="0"
                                            Margin="115 12 4 12 "  Width="64" Height="24" 
                                            Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}"/>
                                <Button x:Name="Cancel"  Grid.Column="1"
                                            Margin="4 12 16 12 "  Width="84" Height="24" 
                                             Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}"/>
                            </Grid>

                        </Border>
                    </StackPanel> 
				</ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource UnprotectSheetDialog}" TargetType="spreadsheet:UnprotectSheetDialog"></Style>

    <Style x:Key="GoToDialog" TargetType="spreadsheet:GoToDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:GoToDialog">
                    <Grid Background="{StaticResource ContentBackground}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="30"/>
                            <RowDefinition Height="132"/>
                            <RowDefinition Height="26"/>
                            <RowDefinition Height="44"/>
                            <RowDefinition Height="49"/>
                        </Grid.RowDefinitions>
                        <TextBlock Width="115" Margin="16,10,219,4"   Text="{spreadsheet:SpreadsheetLocalizationResourceExtension  ResourceName=Find_GoTo }"></TextBlock>
                        <ListView x:Name="GoToReferenceListView" BorderThickness="1" Margin="15 4 15 6" Width="318" Grid.Row="1" ScrollViewer.VerticalScrollBarVisibility="Auto"></ListView>
                        <TextBlock  Width="75" Margin="16,6,259,4" Grid.Row="2"  Style="{StaticResource DescriptionTextBlockStyle}" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Reference }"></TextBlock>
                        <TextBox x:Name="ReferenceTextBox" Margin="15,4,15,16" Width="318" Grid.Row="3" Style="{StaticResource WPFTextBoxStyle}"></TextBox>
                        <Border Grid.Row="4" BorderBrush="{StaticResource BorderAlt}">
                            <Border.BorderThickness>
                                <Thickness>0,1,0,0</Thickness>
                            </Border.BorderThickness>

                            <Grid HorizontalAlignment="Right"  Background="{StaticResource PopupBackground}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Button x:Name="OkButton" Grid.Column="0 "
                                            Margin="178 12 4 12 " Width="64" Height="24" 
                                            Style="{StaticResource WPFPrimaryButtonStyle}" BorderBrush="#ACACAC" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok }"></Button>
                                <Button x:Name="CancelButton"  Grid.Column="1 "
                                            Margin="4 12 16 12 "  Width="84" Height="24" 
                                             Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel }"></Button>
                            </Grid>

                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource GoToDialog}" TargetType="spreadsheet:GoToDialog"></Style>

    <Style x:Key="DataValidationDialog" TargetType="{x:Type spreadsheet:DataValidationDialog}">
        <Setter Property="Template" >
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type spreadsheet:DataValidationDialog}">
                    <Border Background="{StaticResource PopupBackground}" >
                        <Border.CornerRadius>
                            0,0,8,8
                        </Border.CornerRadius>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="56"/>
                            </Grid.RowDefinitions>
                            <tools:TabControlExt x:Name="tabcontrol" SelectedItemFontWeight="Medium" ShowTabItemContextMenu="False" BorderBrush="{StaticResource BorderAlt}" EnableLabelEdit="False" AllowDragDrop="False" 
                                                 TabPanelBackground="{StaticResource ContentBackground}"
                                                 Background="{StaticResource ContentBackground}" TabListContextMenuOptions="None" CloseButtonType="Hide" Width="452" Height="306" >
                                <tools:TabControlExt.BorderThickness>
                                    <Thickness>0,0,0,1</Thickness>
                                </tools:TabControlExt.BorderThickness>
                                <tools:TabItemExt Height="24" Width="75" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Settings}">
                                    <tools:TabItemExt.Margin>
                                        <Thickness>8,0,0,0</Thickness>
                                    </tools:TabItemExt.Margin>
                                    <Grid Height="284" Margin="9,6,0,0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="20"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <TextBlock  Grid.Row="0" Height="20" Foreground="{StaticResource ContentForeground}"
                                                      Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ValidationCriteria}" Margin="3,3,3,0"/>
                                        <Separator Grid.Row="0" Height="1" >
                                            <Separator.Margin>
                                                <Thickness>100,2,16,0</Thickness>
                                            </Separator.Margin>
                                        </Separator>
                                        <StackPanel Margin="10,0,5,5" Grid.Row="1">
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Allow}"  Foreground="{StaticResource ContentForegroundAlt1}" Margin="3,10,3,0"/>
                                            <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                                <ComboBox x:Name="ExcelDataTypeCombo" Style="{StaticResource SpreadsheetComboBoxStyle}"
                                      DisplayMemberPath="DV_ExcelDataTypeName" SelectedValuePath="DV_ExcelDataType"
                                      TabIndex="1" Width="200" Margin="3" Height="24"
                                      ItemsSource="{Binding Path=AllowTypeList,ElementName=validationWindow,Mode=TwoWay}">
                                                </ComboBox>
                                                <CheckBox x:Name="IgnoreBlank" Foreground="{StaticResource ContentForeground}" TabIndex="5" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=IgnoreBlank}" Height="20" IsChecked="True" >
                                                    <CheckBox.Margin>
                                                        <Thickness>11,0,0,10</Thickness>
                                                    </CheckBox.Margin>
                                                </CheckBox>
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal" Height="30">
                                                <TextBlock x:Name="data" IsEnabled="False" Foreground="{StaticResource ContentForegroundAlt1}" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Datatitle}" Margin="3,8,3,0"/>
                                                <CheckBox x:Name="InCellDropDown"  TabIndex="6" Visibility="Collapsed" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=InCellDropDown}" Height="20" IsChecked="True" Width="112">
                                                    <CheckBox.Margin>
                                                        <Thickness>180.5,0,0,10</Thickness>
                                                    </CheckBox.Margin>
                                                </CheckBox>
                                            </StackPanel>
                                            <ComboBox x:Name="ComparisonOperatorCombo" Margin="3,5,3,3" Height="24" Width="200"
                                  TabIndex="2" DisplayMemberPath="DV_ComparisionOperatorName" HorizontalAlignment="left"
                                          SelectedValuePath="DV_ComparisionOperator" Style="{StaticResource SpreadsheetComboBoxStyle}"
                                  ItemsSource="{Binding Path=DataList, ElementName=validationWindow,Mode=TwoWay}">
                                            </ComboBox>
                                            <TextBlock x:Name="firstValueTxtBlock" Margin="3,8,3,0"  Foreground="{StaticResource ContentForegroundAlt1}" />
                                            <TextBox x:Name="firstValueTxtBox" TabIndex="3" Width="266" Height="24" Style="{StaticResource WPFTextBoxStyle}" HorizontalAlignment="Left" Margin="3,8,3,3" Visibility="{Binding ElementName=allowCombo, Path=SelectedValue,Converter={StaticResource MinValueConverter}}"/>
                                            <TextBlock x:Name="secondValueTxtBlock" Margin="3,8,3,0"   Foreground="{StaticResource ContentForegroundAlt1}" />
                                            <TextBox x:Name="secondValueTxtBox" TabIndex="4" Width="266" Height="24" Style="{StaticResource WPFTextBoxStyle}" HorizontalAlignment="Left" Margin="3,8,3,3" Visibility="{Binding ElementName=dataCombo,Path=SelectedValue,Converter={StaticResource MaxValueConverter}}" IsEnabled="{Binding ElementName=allowCombo, Path=SelectedValue,Converter={StaticResource StingToBoolConverter}}"/>
                                        </StackPanel>
                                    </Grid>
                                </tools:TabItemExt>
                                <tools:TabItemExt Height="24" Width="109" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=InputMessage}">
                                    <Grid Height="284"  Margin="12,6,0,0" >
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="85*"/>
                                            <ColumnDefinition Width="109*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="22"/>
                                            <RowDefinition Height="27"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <CheckBox x:Name="ShowInputMessage" Foreground="{StaticResource ContentForeground}" IsChecked="True" FocusVisualStyle="{x:Null}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ShowInputMessage}" Grid.ColumnSpan="2" Margin="0,5,127,0"/>
                                        <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=InputmessageDescription}" Foreground="{StaticResource ContentForeground}" FontSize="12" Margin="0,10,3,0" Grid.Row="1" Grid.ColumnSpan="2"
                               Grid.Column="0" />
                                        <Separator Grid.Row="1"  Width="175" Height="1" Grid.Column="1" >
                                            <Separator.Margin>
                                                <Thickness>65,8,16,0</Thickness>
                                            </Separator.Margin>
                                        </Separator>
                                        <StackPanel Grid.Row="2" Margin="10,3,0,5" Grid.ColumnSpan="2" Grid.Column="0">
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Title}"  Foreground="{StaticResource ContentForegroundAlt1}" >
                                                <TextBlock.Margin>
                                                    <Thickness>1,10,3,0</Thickness>
                                                </TextBlock.Margin>
                                            </TextBlock>
                                            <TextBox x:Name="promptBoxTitleTxtBox" Style="{StaticResource WPFTextBoxStyle}" Height="24" Width="400" HorizontalAlignment="Left" >
                                                <TextBox.Margin>
                                                    <Thickness>1,9,0,3</Thickness>
                                                </TextBox.Margin>
                                            </TextBox>
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Inputmessagedesc}"  Foreground="{StaticResource ContentForegroundAlt1}" >
                                                <TextBlock.Margin>
                                                    <Thickness>1,6,3,0</Thickness>
                                                </TextBlock.Margin>
                                            </TextBlock>
                                            <TextBox x:Name="promptBoxTextTxtBox" Style="{StaticResource WPFTextBoxStyle}" TextWrapping="Wrap" VerticalScrollBarVisibility="Auto" VerticalAlignment="Top"
                                 AcceptsReturn="True" Width="400" Height="83" HorizontalAlignment="Left" >
                                                <TextBox.Margin>
                                                    <Thickness>1,10,3,3</Thickness>
                                                </TextBox.Margin>
                                            </TextBox>
                                        </StackPanel>
                                    </Grid>
                                </tools:TabItemExt>
                                <tools:TabItemExt Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ErrorAlert}" Height="24" Width="81" VerticalAlignment="Top" Margin="0,0">
                                    <Grid Height="284" Margin="12,6,0,0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="22"/>
                                            <RowDefinition Height="27"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="29*"/>
                                            <ColumnDefinition Width="156*"/>
                                            <ColumnDefinition Width="185*"/>
                                        </Grid.ColumnDefinitions>
                                        <CheckBox x:Name="ShowErrorMsg" IsChecked="True" Foreground="{StaticResource ContentForeground}" FocusVisualStyle="{x:Null}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ShowErrorMessage}" Grid.ColumnSpan="3" Margin="0,5,108,0"/>
                                        <TextBlock Grid.Row="1" FontSize="12" Foreground="{StaticResource ContentForeground}" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ErrorAlertDescription}" Margin="0,10,93,0" Grid.ColumnSpan="3"
                               Grid.Column="0" />
                                        <Separator Grid.Row="1" Grid.Column="2" Height="1" >
                                            <Separator.Margin>
                                                <Thickness>58,12,16,4</Thickness>
                                            </Separator.Margin>
                                        </Separator>
                                        <StackPanel Orientation="Vertical" Grid.Row="2" Grid.ColumnSpan="2" Grid.Column="0">
                                            <StackPanel.Margin>
                                                <Thickness>7,10,10,0</Thickness>
                                            </StackPanel.Margin>
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Style}"  Foreground="{StaticResource ContentForegroundAlt1}" Margin="5,3,3,0"/>
                                            <ComboBox x:Name="ErroralertStyle" Style="{StaticResource SpreadsheetComboBoxStyle}"  Margin="0,9,0,3" Height="24" Width="192">
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Stop}"/>
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Warning}"/>
                                                <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Information}"/>
                                            </ComboBox>
                                            <Image x:Name="ErrorAlertImage" HorizontalAlignment="Left" Source="/Syncfusion.SfSpreadsheet.WPF;component/Ribbon/Icons/Themes/Stop.png" Height="40" Width="40">
                                                <Image.Margin>
                                                    <Thickness>63,45,0,0</Thickness>
                                                </Image.Margin>
                                            </Image>
                                        </StackPanel>
                                        <StackPanel Grid.Row="2" Grid.ColumnSpan="2"  Grid.Column="1">
                                            <StackPanel.Margin>
                                                <Thickness>185,10,-9,0</Thickness>
                                            </StackPanel.Margin>
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Title}"  Foreground="{StaticResource ContentForegroundAlt1}" Margin="0,3,3,0"/>
                                            <TextBox x:Name="errorBoxTitleTxtBox" Style="{StaticResource WPFTextBoxStyle}" Width="192" Margin="0,9,3,3" HorizontalAlignment="Left" Height="24"/>
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ErrorMessage}"  Foreground="{StaticResource ContentForegroundAlt1}" Margin="-1,5,4,0"/>
                                            <TextBox x:Name="errorBoxTextTxtBox" Style="{StaticResource WPFTextBoxStyle}" Width="192" VerticalScrollBarVisibility="Auto" TextWrapping="Wrap"  VerticalAlignment="Top"
                                         AcceptsReturn="True" HorizontalAlignment="Left" Margin="0,9,3,3" Height="99"/>
                                        </StackPanel>
                                    </Grid>
                                </tools:TabItemExt>
                            </tools:TabControlExt>
                            <Border BorderBrush="{StaticResource BorderAlt}" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" Grid.Row="2">
                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="ClearAll" Grid.Column="0" BorderThickness="1" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ClearAll}" 
                                         TabIndex="7" Width="76" Height="24" 
                                        Margin="8,5,125,10"/>
                                    <Button x:Name="Apply" Grid.Column="1 " Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}" TabIndex="8" 
                                        Style="{StaticResource WPFPrimaryButtonStyle}" Width="64" Height="24" 
                                        Margin="61 5 5 10 "/>
                                    <Button x:Name="Cancel" Grid.Column="2 " 
                                        BorderThickness="1" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}" 
                                         TabIndex="9"  Width="84" Height="24" 
                                        Margin="5 5 5 10 "/>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource DataValidationDialog}" TargetType="spreadsheet:DataValidationDialog"></Style>

    <Style x:Key="SyncfusionUnHideSheetDialogStyle"  TargetType="spreadsheet:UnHideSheetDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:UnHideSheetDialog">
                    <Border Background="{StaticResource PopupBackground}" BorderBrush="{StaticResource BorderAlt}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="30"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="auto"/>
                            </Grid.RowDefinitions>
                            <StackPanel Background="{StaticResource ContentBackground}">
                                <TextBlock x:Name="PART_UnhideSheetDescriptionTextBlock"  Padding="16,6,16,4" HorizontalAlignment="Left" />
                            </StackPanel>
                            <Grid Grid.Row="1"  VerticalAlignment="Stretch" HorizontalAlignment="Stretch" Background="{StaticResource ContentBackground}">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="1*" />
                                </Grid.RowDefinitions>
                                <ListBox x:Name="PART_UnhideSheetListBox" Grid.Row="0" SelectedIndex="0" BorderThickness="{StaticResource Windows11Light.BorderThickness1}" Margin="16,4,16,16" ScrollViewer.VerticalScrollBarVisibility="Auto"/>
                            </Grid>
                            <Border Grid.Row="2" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" BorderBrush="{StaticResource BorderAlt}">
                                <Grid VerticalAlignment="Center"  HorizontalAlignment="Right">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=" Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyButton" Grid.Column="0"
                                                Margin="173 12 4 12 " Width="64" Height="24" 
                                                Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResource ResourceName=Ok}" />
                                    <Button x:Name="PART_CancelButton" Grid.Column="1"
                                                Margin="4 12 16 12 " Width="84" Height="24"
                                                 Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}"/>
                                </Grid>

                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionUnHideSheetDialogStyle}" TargetType="spreadsheet:UnHideSheetDialog"></Style>

    <Style x:Key="SyncfusionBetweenNotBetweenDialogStyle" TargetType="spreadsheet:BetweenNotBetweenDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:BetweenNotBetweenDialog">
                    <Border Background="{StaticResource PopupBackground}" BorderBrush="{StaticResource BorderAlt}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="30" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="53" />
                            </Grid.RowDefinitions>
                            <StackPanel Background="{StaticResource ContentBackground}">
                                <TextBlock Text="{Binding Path=Description,Mode=TwoWay,ElementName=FormatWindow}" 
                                           FontSize="14"
                                           FontWeight="SemiBold" Margin="16,6,16,6" VerticalAlignment="Center" HorizontalAlignment="Left" />
                            </StackPanel>
                            <Grid Grid.Row="1" Background="{StaticResource ContentBackground}">
                                <Grid HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="10,0,10,0" >
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="auto" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="33" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="34" />
                                        <ColumnDefinition Width="234" />
                                    </Grid.ColumnDefinitions>
                                    <TextBox x:Name="PART_StartNumberTextBox" Margin="6,6,6,16" MinWidth="150" Height="24" Grid.Column="0" HorizontalAlignment="Stretch" Style="{StaticResource WPFTextBoxStyle}" />
                                    <TextBlock x:Name="txtblock" Grid.Column="1" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=And}"  FontSize="12" Height="16" Margin="6,10,6,20" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                                    <TextBox x:Name="PART_EndNumberTextBox" Margin="6,6,6,16" MinWidth="150" Height="24" Grid.Column="2" HorizontalAlignment="Stretch" Style="{StaticResource WPFTextBoxStyle}" />
                                    <TextBlock Grid.Column="3" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=With}"  FontSize="12" Height="16" Margin="6,10,6,20" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                                    <ComboBox  x:Name="PART_BetweenComboBox" Grid.Column="4" Margin="6,6,6,16" SelectedIndex="0" Height="24"  >
                                        <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LightRedFillWithDarkRedText}"/>
                                        <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=YellowFillWithDarkYellowText}"/>
                                        <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=GreenFillWithDarkGreenText}"/>
                                        <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LightRedFill}"/>
                                        <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RedText}"/>
                                        <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RedBorder}"/>
                                    </ComboBox>
                                </Grid>
                            </Grid>
                            <Border BorderBrush="{StaticResource BorderAlt}" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" Grid.Row="2">
                                <Grid HorizontalAlignment="Right">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyButton" Width="64" Height="24" Grid.Column="0 "
                                                Margin="475 12 4 12 " 
                                                Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}"/>
                                    <Button x:Name="PART_CancelButton" Width="64" Height="24" Grid.Column="1"
                                                Margin="4 12 16 12 " 
                                                 Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}" />
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionBetweenNotBetweenDialogStyle}" TargetType="spreadsheet:BetweenNotBetweenDialog"></Style>

    <Style x:Key="SyncfusionPasswordDialogStyle" TargetType="spreadsheet:PasswordDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:PasswordDialog">
                    <Border Background="{StaticResource ContentBackground}" BorderBrush="{StaticResource BorderAlt}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="50"/>
                                <RowDefinition Height="auto"/>
                            </Grid.RowDefinitions>
                            <Grid Grid.Row="0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="78"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock FontSize="12" Margin="16,14,4,20"  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=PasswordtoUnProtect}"/>
                                <PasswordBox x:Name="PART_PasswordBox" Width="Auto" Height="24" Margin="0,10,16,14" Grid.Column="1" HorizontalAlignment="Stretch"/>
                            </Grid>
                            <Border Grid.Row="1" BorderBrush="{StaticResource BorderAlt}" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" Background="{StaticResource PopupBackground}">
                                <Grid HorizontalAlignment="Right" >
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyButton" Grid.Column="0"
                                            Margin="115 11 4 12 " 
                                            Width="64" Height="24" 
                                            Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}"/>
                                    <Button x:Name="PART_CancelButton" Grid.Column="1"
                                            Margin="4 11 16 12 "
                                            Width="84" Height="24" 
                                             Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}"/>
                                </Grid>

                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionPasswordDialogStyle}" TargetType="spreadsheet:PasswordDialog"></Style>

    <Style x:Key="SynfusionOutlineSettingsDialogStyle" TargetType="spreadsheet:OutlineSettingsDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:OutlineSettingsDialog">
                    <Border Background="{StaticResource PopupBackground}" BorderBrush="{StaticResource BorderAlt}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="auto" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="auto" />
                            </Grid.RowDefinitions>
                            <Grid Grid.Row="0" Background="{StaticResource ContentBackground}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition/>
                                </Grid.ColumnDefinitions>
                                <TextBlock x:Name="PART_OutlineSettingsTextBlock" Height="16"  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Direction}" Margin="16,8,8,6"/>
                                <Separator Grid.Column="1" Height="1" Margin="4,2,16,0"  />
                            </Grid>
                            <Grid Grid.Row="1" Background="{StaticResource ContentBackground}">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <StackPanel Orientation="Vertical" VerticalAlignment="Center">
                                    <CheckBox Name="PART_SummaryRowCheckBox" Height="16" IsChecked="{Binding Path=IsSummaryRowBelow,Mode=TwoWay,ElementName=OutlineSettings}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=IsSummaryRowBelow}" Margin="26,6,0,6"/>
                                    <CheckBox Name="PART_SummaryColumnCheckBox" Height="16" IsChecked="{Binding Path=IsSummaryColumnAtRight,Mode=TwoWay,ElementName=OutlineSettings}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=IsSummaryColumnAtRight}" Margin="26,6,0,14"/>
                                </StackPanel>
                            </Grid>
                            <Border BorderBrush="{StaticResource BorderAlt}" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" Grid.Row="2">
                                <Grid  HorizontalAlignment="Right" Height="48">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyButton" Grid.Column="0"
                                                Margin="164 12 4 12 " Width="64" Height="24" 
                                                Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok }"></Button>
                                    <Button x:Name="PART_CancelButton" Grid.Column="1"
                                               Margin="4 12 16 12 " Width="84" Height="24" 
                                                 Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel }"></Button>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SynfusionOutlineSettingsDialogStyle}" TargetType="spreadsheet:OutlineSettingsDialog"></Style>

    <Style x:Key="SyncfusionNewNameRangeDialogStyle" TargetType="spreadsheet:NewNameRangeDialog">
        <Setter Property="Template" >
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:NewNameRangeDialog">
                    <Border Background="{StaticResource PopupBackground}" BorderBrush="{StaticResource BorderAlt}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="auto"/>
                            </Grid.RowDefinitions>
                            <Grid Grid.Row="0" Background="{StaticResource ContentBackground}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="78" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0" Margin="0" HorizontalAlignment="Left" VerticalAlignment="Stretch" Orientation="Vertical">
                                    <TextBlock Height="16" Width="58" Margin="16,14,4,8" FontSize="12" HorizontalAlignment="Left"
                                               Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Name}" />
                                    <TextBlock Height="16" Width="58" Margin="16,8,4,8" FontSize="12" HorizontalAlignment="Left"
                                               Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Scope}"  />
                                    <TextBlock Height="16" Width="64"  Margin="16,8,4,20" FontSize="12" HorizontalAlignment="Left" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Refers_To}"  />
                                </StackPanel>
                                <StackPanel Grid.Column="1" HorizontalAlignment="Stretch" Orientation="Vertical">
                                    <TextBox x:Name="PART_DisplayNameTextBox"
                                             Height="24" Width="auto" Style="{StaticResource WPFTextBoxStyle}" Margin="0,10,16,8" HorizontalAlignment="Stretch" />
                                    <ComboBox x:Name="PART_ScopeComboBox" Height="24" Width="122" Style="{StaticResource WPFComboBoxStyle}" HorizontalAlignment="Left" Margin="0,0,0,8"
                                              SelectedIndex="0" SelectedItem="{Binding ComboBox_SelectedItem, ElementName=NewNameRange, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                    <TextBox x:Name="PART_AddressTextBox" Height="24" Width="auto" HorizontalAlignment="Stretch" Style="{StaticResource WPFTextBoxStyle}" Margin="0,0,16,16"
                                             VerticalContentAlignment="Center" Text="{Binding RefersTO, ElementName=NewNameRange, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                </StackPanel>
                            </Grid>
                            <Border Grid.Row="1" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" BorderBrush="{StaticResource BorderAlt}">
                                <Grid HorizontalAlignment="Right">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyButton" Grid.Column="0"
                                                Margin="115 16 8 16 " Width="64" Height="24" 
                                                Style="{StaticResource WPFPrimaryButtonStyle}"
                                                Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}"/>
                                    <Button x:Name="PART_CancelButton" Grid.Column="1"
                                                Margin="0 16 16 16 " Width="84" Height="24" 
                                                
                                                Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}"/>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <Style BasedOn="{StaticResource SyncfusionNewNameRangeDialogStyle}" TargetType="spreadsheet:NewNameRangeDialog"></Style>

    <Style x:Key="SyncfusionConditionalFormatDialogStyle" TargetType="spreadsheet:ConditionalFormatDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:ConditionalFormatDialog">
                    <Border Background="{StaticResource PopupBackground}" BorderBrush="{StaticResource BorderAlt}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="30"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="auto"/>
                            </Grid.RowDefinitions>
                            <Grid Background="{StaticResource ContentBackground}">
                                <TextBlock Text="{Binding Path=Description,Mode=TwoWay,ElementName=FormatWindow}" FontSize="14"
                                           FontWeight="SemiBold"
                                            Margin="16,6,16,6" VerticalAlignment="Center" HorizontalAlignment="Left" />
                            </Grid>
                            <Grid Grid.Row="1" Background="{StaticResource ContentBackground}">
                                <Grid HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="10,0,10,0" >
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="auto" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="auto"/>
                                        <ColumnDefinition Width="auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox x:Name="PART_ConditionTextBox" MinWidth="205" Height="24" HorizontalAlignment="Stretch" Style="{StaticResource WPFTextBoxStyle}" Margin="6,6,4,16"/>
                                    <TextBlock Margin="3,10,3,20" Height="16" FontSize="12"  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=With}" VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Column="1"/>
                                    <ComboBox  x:Name="PART_ConditionComboBox" SelectedIndex="0"  Margin="4,6,6,16" Grid.Column="2" Height="24" FontSize="12" Width="205">
                                        <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LightRedFillWithDarkRedText}"/>
                                        <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=YellowFillWithDarkYellowText}"/>
                                        <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=GreenFillWithDarkGreenText}"/>
                                        <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LightRedFill}"/>
                                        <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RedText}"/>
                                        <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RedBorder}"/>
                                    </ComboBox>
                                </Grid>
                            </Grid>
                            <Border BorderBrush="{StaticResource BorderAlt}" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" Grid.Row="2" >
                                <Grid HorizontalAlignment="Right">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyButton" Width="64" Height="24" Grid.Column="0"
                                                Margin="309 12 4 12 " 
                                                Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}"/>
                                    <Button x:Name="PART_CancelButton" Width="84" Height="24"  Grid.Column="1"
                                                Margin="4 12 16 12 "
                                                 Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}"/>
                                </Grid>

                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionConditionalFormatDialogStyle}" TargetType="spreadsheet:ConditionalFormatDialog"></Style>

    <Style x:Key="SyncfusionFileEncryptDialogStyle" TargetType="spreadsheet:FileEncryptDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:FileEncryptDialog">
                    <Grid Background="{StaticResource ContentBackground}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="auto"/>
                        </Grid.RowDefinitions>
                        <Grid Grid.Row="0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="186"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <GroupBox x:Name="Description" Width="auto" FontSize="10" HorizontalAlignment="Stretch" Height="156" Style="{StaticResource WPFGroupBoxStyle}"
                                       Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=EncryptDescription}" Margin="16,14,16,16">
                                <StackPanel>
                                    <TextBlock x:Name="PasswordText" FontSize="12" Width="57" Height="16" Style="{StaticResource WPFTextBlockStyle}" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=PasswordtoUnProtect}" 
                                               Margin="12 12 0 8" VerticalAlignment="Center" HorizontalAlignment="Left" />
                                    <PasswordBox x:Name="PART_PasswordTextValue" Width="auto" Height="24" Style="{StaticResource WPFPasswordBoxStyle}" Margin="12 0 12 8" Grid.Row="1"/>
                                    <TextBlock x:Name="PART_CautionText" Width="auto" Height="64" Style="{StaticResource WPFTextBlockStyle}" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Caution}" Grid.Row="2" FontSize="12" Margin="12 0 12 -5" TextWrapping="Wrap" VerticalAlignment="Stretch" HorizontalAlignment="Stretch" />
                                </StackPanel>
                            </GroupBox>
                        </Grid>
                        <Border Grid.Row="1" Background="{StaticResource PopupBackground}" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" BorderBrush="{StaticResource BorderAlt}">
                            <Grid HorizontalAlignment="Right">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Button x:Name="PART_ApplyButton" Grid.Column="0"
                                            Margin="204 12 8 12 " Width="64" Height="24" 
                                            Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}" />
                                <Button x:Name="PART_CancelButton" Grid.Column="1" Width="84" Height="24"
                                            Margin="0 12 16 12 "
                                             Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}" />
                            </Grid>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionFileEncryptDialogStyle}" TargetType="spreadsheet:FileEncryptDialog"></Style>

    <Style x:Key="SyncfusionDateOccurringConditionDialogStyle" TargetType="{x:Type spreadsheet:DateOccurringConditionDialog}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type spreadsheet:DateOccurringConditionDialog}">
                    <Border Background="{StaticResource PopupBackground}" HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="30"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="auto"/>
                            </Grid.RowDefinitions>
                            <StackPanel Grid.Row="0" Background="{StaticResource ContentBackground}">
                                <TextBlock x:Name="Part_DateOccurringTextBlock" Text="{Binding Path=Description,Mode=TwoWay,ElementName=FormatWindow}" 
                                           FontSize="14" 
                                           FontWeight="SemiBold"  Margin="16,10,0,6" Height="22"  Width="272" VerticalAlignment="Center" HorizontalAlignment="Left" />
                            </StackPanel>
                            <Grid  HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="1" Background="{StaticResource ContentBackground}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <ComboBox x:Name="Part_DateOptionComboBox" HorizontalAlignment="Stretch" Width="Auto" Grid.Column="0" SelectedIndex="0" MinWidth="150" Margin="15,6,0,16"  Height="24"/>
                                <TextBlock FontSize="12" Grid.Column="1"  Margin="12,10,12,20" Width="23" Height="16" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=With}"   />
                                <ComboBox  x:Name="Part_FormatComboBox" SelectedIndex="0" Grid.Column="2"  Margin="0,6,15,16" HorizontalAlignment="Stretch" Height="24" MinWidth="224" >
                                    <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LightRedFillWithDarkRedText}"/>
                                    <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=YellowFillWithDarkYellowText}"/>
                                    <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=GreenFillWithDarkGreenText}"/>
                                    <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=LightRedFill}"/>
                                    <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RedText}"/>
                                    <ComboBoxItem Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=RedBorder}"/>
                                </ComboBox>
                            </Grid>
                            <Border Grid.Row="2"  BorderBrush="{StaticResource BorderAlt}" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}">
                                <Grid  HorizontalAlignment="Right" VerticalAlignment="Stretch">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="Part_ApplyButton" Grid.Column="0"
                                                Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}" Height="24" Width="64" 
                                                Margin="281 12 4 12 "/>
                                    <Button x:Name="Part_CancelButton" Grid.Column="1"
                                                 Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}" Height="24" Width="84" 
                                                Margin="4 12 16 12 "/>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionDateOccurringConditionDialogStyle}" TargetType="{x:Type spreadsheet:DateOccurringConditionDialog}"/>
	
    <Style x:Key="SyncfusionDefaultColumnWidthDialogStyle" TargetType="spreadsheet:DefaultColumnWidthDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:DefaultColumnWidthDialog">
                    <Border Background="{StaticResource ContentBackground}" BorderBrush="{StaticResource BorderAlt}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="50"/>
                                <RowDefinition Height="auto"/>
                            </Grid.RowDefinitions>
                            <Grid Grid.Row="0" Background="{StaticResource ContentBackground}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="159"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Height="16" FontSize="12" Margin="16,14,4,20"  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=StandardWidthText}"/>
                                <TextBox x:Name="PART_DefaultWidthTextBox" Grid.Column="1" Height="24" Margin="0,10,16,16" Style="{StaticResource WPFTextBoxStyle}" Width="Auto" HorizontalAlignment="Stretch"/>
                            </Grid>
                            <Border Grid.Row="1" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" Background="{StaticResource PopupBackground}" BorderBrush="{StaticResource BorderAlt}">
                                <Grid  HorizontalAlignment="Right">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyButton" Grid.Column="0"
                                                Margin="115 11 4 12 " Width="64" Height="24" 
                                                Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}"/>
                                    <Button x:Name="PART_CancelButton" Grid.Column="1"
                                                Margin="4 11 16 12 " Width="84" Height="24" 
                                                 Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}"/>
                                </Grid>
                            </Border>
                        </Grid>
                </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionDefaultColumnWidthDialogStyle}" TargetType="spreadsheet:DefaultColumnWidthDialog"></Style>

    <Style x:Key="SyncfusionGroupUngroupDialogStyle" TargetType="spreadsheet:GroupUngroupDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:GroupUngroupDialog">
                    <Border Background="{StaticResource PopupBackground}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="26"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="49"/>
                            </Grid.RowDefinitions>
                            <Grid  Grid.Row="0" Background="{StaticResource ContentBackground}">
                                <TextBlock x:Name="PART_GroupUngroupDescriptionTextBlock" FontSize="12" Margin="16,10,2,0" Width="50" Height="16"  Style="{StaticResource DescriptionTextBlockStyle}"  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Group}"></TextBlock>
                                <Separator x:Name="PART_Separator" Width="Auto"  HorizontalAlignment="Stretch" Height="1"/>
                            </Grid>
                            <Grid HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Row="1"  Background="{StaticResource ContentBackground}">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <RadioButton Grid.Row="0"  x:Name="PART_RowsRadioButton" IsChecked="{Binding Path=Rows,Mode=TwoWay,ElementName=GroupUngroupWindow}"  Margin="27,13,8,7" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Rows}" GroupName="GroupBy"/>
                                <RadioButton Grid.Row="1" x:Name="PART_ColumnsRadioButton" IsChecked="{Binding Path=Columns,Mode=TwoWay,ElementName=GroupUngroupWindow}" Margin="27 7 8 17" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Columns}" GroupName="GroupBy"/>
                            </Grid>
                            <Border Grid.Row="2"  BorderBrush="{StaticResource BorderAlt}" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}">
                                <Grid HorizontalAlignment="Right" VerticalAlignment="Center">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyButton" Grid.Column="0"
                                                Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}" Height="24" 
                                                Width="83 " 
                                                Margin="15 12 4 12 "/>
                                    <Button x:Name="PART_CancelButton" Grid.Column="1"
                                                 Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}" Height="24" 
                                                Width="83 " 
                                                Margin="4 12 15 12 "/>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionGroupUngroupDialogStyle}" TargetType="spreadsheet:GroupUngroupDialog"/>
	
    <Style x:Key="SyncfusionFormatAsTableDialogStyle" TargetType="spreadsheet:FormatAsTableDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:FormatAsTableDialog">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="auto"/>
                        </Grid.RowDefinitions>
                        <StackPanel Grid.Row="0" Orientation="Vertical"  Background="{StaticResource ContentBackground}">
                            <TextBlock x:Name="FormatAsTableTextBlock" Height="18" Margin="16,14,0,4"  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormatTextBlockText}" FontSize="12" Width="Auto" VerticalAlignment="Center" HorizontalAlignment="Left" />
                            <TextBox Name="PART_FormatAsTableTextBox" Width="auto" HorizontalAlignment="Stretch" Margin="16,4,16,6" Height="24" Style="{StaticResource WPFTextBoxStyle}" Text="{Binding Path=Text,Mode=TwoWay,ElementName=TableFormatWindow}" />
                            <CheckBox Name="PART_MyTableHasHeaderCheckBox" Margin="16,6,0,13" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableFormat_CheckBoxContent}" FontSize="12"></CheckBox>
                        </StackPanel>
                        <Border Grid.Row="1" BorderBrush="{StaticResource BorderAlt}" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}">
                            <Grid HorizontalAlignment="Right"  Background="{StaticResource PopupBackground}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Button x:Name="PART_ApplyButton" Grid.Column="0"
                                            Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}"  
                                            Margin="115 12 4 12 " Width="64" Height="24"/>
                                <Button x:Name="PART_CancelButton" Grid.Column="1"
                                             Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}"  
                                            Margin="4 12 16 12 " Width="84" Height="24"/>
                            </Grid>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionFormatAsTableDialogStyle}" TargetType="spreadsheet:FormatAsTableDialog"></Style>
	
    <Style TargetType="RadioButton" x:Key="InsertHyperlinkDialogRadioButtonStyle" BasedOn="{StaticResource {x:Type ToggleButton}}">
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Width" Value="79"/>
        <Setter Property="Height" Value="68"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border x:Name="border" 
                        SnapsToDevicePixels="true" 
                        Background="{TemplateBinding Background}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                        >
                        <ContentPresenter x:Name="contentPresenter" Margin="{TemplateBinding Padding}" 
                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                      RecognizesAccessKey="True" 
                                      SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinmanager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="Button.IsDefaulted" Value="true">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt3}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="Default"/>
                                <Condition Property="IsFocused" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt3}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource ButtonBorderBrushHovered }"/>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource ButtonBorderBrushToggled}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="border" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="Transparent"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    
    <Style x:Key="SyncfusionInsertHyperlinkDialogStyle" TargetType="spreadsheet:InsertHyperlinkDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:InsertHyperlinkDialog">
                    <Border Background="{StaticResource ContentBackground}" BorderBrush="{StaticResource BorderAlt}"> 
                        <StackPanel>
                            <Grid Height="325">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="96"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid >
                                    <Grid.RowDefinitions >
                                        <RowDefinition Height="75"></RowDefinition>
                                        <RowDefinition Height="*"></RowDefinition>
                                    </Grid.RowDefinitions>
                                </Grid>
                                <Grid Grid.Column="1">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="277"></RowDefinition>
                                
                                    </Grid.RowDefinitions>

                                    <StackPanel Orientation="Vertical">

                                        <StackPanel Orientation="Horizontal" x:Name="displaytext" Margin="8,14,16,12" Grid.Column="1" HorizontalAlignment="Left" VerticalAlignment="Top">
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_TexttoDisplay}" Height="16" FontSize="12"
                                                        Width="79"></TextBlock>
                                            <TextBox x:Name="PART_DisplayTextBox" Margin="8,0,0,0" Width="351" Height="24"></TextBox>
                                        </StackPanel>

                                        <StackPanel Orientation="Horizontal" x:Name="screentip" Margin="8,0,16,12" 
                                                    Grid.Column="1" 
                                                    HorizontalAlignment="Left" 
                                                    VerticalAlignment="Top">
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_ScreenTip}"  Height="16" FontSize="12" Width="79"></TextBlock>
                                            <TextBox x:Name="PART_ScreenTipTextBox" Margin="8,0,0,0" Width="351" Height="24"></TextBox>
                                        </StackPanel>

                                        <Separator Width="438" HorizontalAlignment="Left" Margin="8,0,16,12"></Separator>

                                        <!--AddressBar-->

                                        <StackPanel Orientation="Vertical" x:Name="AddressPanel" Margin="8,0,16,181" Grid.Column="1" 
                                                    HorizontalAlignment="Left" VerticalAlignment="Top"
                                                    Visibility="{Binding ElementName=PART_WebRadioButton,Path=IsChecked,Mode=TwoWay, Converter={StaticResource booleanconverter}}">
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_HyperlinkAddress}" 
                                                     Height="16" FontSize="12" Width="79" HorizontalAlignment="Left"></TextBlock>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                                                <Grid>
                                                    <TextBox x:Name="PART_AddressTextBox" Style="{StaticResource WPFTextBoxStyle}" Height="24" Width="406" Margin="0,8,8,0" Padding="0,0,25,0"
                                                        HorizontalAlignment="Stretch" VerticalAlignment="Center" VerticalContentAlignment="Center"/>
                                                    <Button x:Name="PART_RemoveButton" Width="20" Height="20" Margin="0,8,11,0" Style="{StaticResource WPFGlyphButtonStyle}" HorizontalAlignment="Right" VerticalAlignment="Center" BorderBrush="Transparent" IsEnabled="True" Visibility="Collapsed">
                                                        <StackPanel Width="12" Height="12" Margin="3" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                            <ContentControl x:Name="Remove" ContentTemplate="{StaticResource CancelEditIcon}" Content="{Binding}" Margin="0,2,0,0"/>
                                                        </StackPanel>
                                                    </Button>
                                                </Grid>
                                                <Button x:Name="PART_HyperBrowseButton" Width="24" Height="24" Margin="0,8,0,0">
                                                    <StackPanel Width="12" Height="12" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                        <ContentControl x:Name="Browse" ContentTemplate="{StaticResource BrowseIcon}" Content="{Binding}" />
                                                    </StackPanel>
                                                </Button>
                                            </StackPanel>
                                        </StackPanel>

                                        <!--CellReferenceBar-->

                                        <StackPanel Orientation="Vertical" x:Name="CellReferencePanel" Margin="8,0,16,181" Grid.Column="1" 
                                                    HorizontalAlignment="Left" VerticalAlignment="Top"
                                                    Visibility="{Binding ElementName=PART_ReferenceRadioButton,Path=IsChecked,Mode=TwoWay, Converter={StaticResource booleanconverter}}">
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_Typethecellreference}" 
                                                    Height="16" FontSize="12" Width="122" HorizontalAlignment="Left"></TextBlock>
                                            <TextBox x:Name="PART_ReferenceTextBox" Margin="0,8,0,0" Width="438" Height="24"></TextBox>
                                        </StackPanel>

                                        <!--Document-->

                                        <StackPanel Orientation="Vertical" x:Name="DocumentPanel" Margin="8,0,16,-16" Grid.Column="1" 
                                                    HorizontalAlignment="Left" VerticalAlignment="Top"
                                                    Visibility="{Binding ElementName=PART_DocumentRadioButton,Path=IsChecked,Mode=TwoWay, Converter={StaticResource booleanconverter}}">
                                            <StackPanel Orientation="Vertical">
                                                <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_Nameofnewdocument}" 
                                                        Height="16" FontSize="12" Width="153" HorizontalAlignment="Left"></TextBlock>
                                                <TextBox x:Name="PART_NameOfNewDocumentTextBox" Margin="0,8,0,0" Width="438" Height="24" HorizontalAlignment="Left"></TextBox>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_FullPath}"                                 
                                                           Height="16" Margin="0,12,0,0" FontSize="12" Width="49"                            
                                                           x:Name="PART_FullPathText" HorizontalAlignment="Left"></TextBlock>
                                                    <Separator Width="385" Margin="0,20,0,8" HorizontalAlignment="Left"></Separator>
                                                </StackPanel>
                                            </StackPanel>

                                            <StackPanel Orientation="Horizontal" >
                                                <TextBlock x:Name="PART_DocumentPathText" Width="266" Margin="10,-8,38,-10" FontSize="12" Height="16" TextWrapping="Wrap"></TextBlock>
                                                <Button x:Name="PART_ChangeButton" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_Change}" 
                                                        Width="84" Margin="0,8,0,0" Height="24" ></Button>
                                            </StackPanel>

                                            <StackPanel Orientation="Vertical" HorizontalAlignment="Left">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_Whentoedit}" 
                                                            Height="16" Margin="0,12,0,0" FontSize="12" Width="70" HorizontalAlignment="Left"></TextBlock>
                                                    <Separator Width="364" Margin="0,20,0,8" HorizontalAlignment="Left"  ></Separator>
                                                </StackPanel>
                                                <RadioButton IsChecked="False" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_EditLater}"
                                                        Width="200" Height="16" Margin="11,8,0,0" Name="PART_EditLaterRadioButton" HorizontalAlignment="Left"></RadioButton>

                                                <RadioButton IsChecked="True" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_EditNow}"
                                                        Width="200" Height="16" Margin="11,12,0,0" Name="PART_EditNowRadioButton" HorizontalAlignment="Left"></RadioButton>
                                            </StackPanel>

                                        </StackPanel>

                                        <StackPanel Orientation="Vertical" x:Name="MailPanel" Margin="8,0,16,121" Grid.Column="1" HorizontalAlignment="Left" VerticalAlignment="Top"
                                                    Visibility="{Binding ElementName=PART_MailRadioButton,Path=IsChecked,Mode=TwoWay, Converter={StaticResource booleanconverter}}">
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_EmailAddress}" 
                                                   Height="16" FontSize="12" Width="122" HorizontalAlignment="Left"></TextBlock>
                                            <TextBox x:Name="PART_EmailTextBox" Width="438" Margin="0,8,0,0" Height="24"></TextBox>
                                            <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_Subject}" 
                                                    Width="122" Height="16" FontSize="12" Margin="0,12,0,0" HorizontalAlignment="Left"></TextBlock>
                                            <TextBox x:Name="PART_SubjectTextBox" Width="438" Margin="0,8,0,0" Height="24"></TextBox>
                                        </StackPanel>

                                    </StackPanel>
                                </Grid>
                                <StackPanel Orientation="Vertical" Margin="15,0,0,0">
                                    <TextBlock Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_LinkTo}" FontSize="12" 
                                                HorizontalAlignment="Center" Margin="20,14,20,8" />
                                    <Border BorderThickness="1" Width="81" Height="274" CornerRadius="{StaticResource Windows11Light.CornerRadius4}" BorderBrush="{StaticResource BorderAlt}">
                                        <StackPanel HorizontalAlignment="Center" Orientation="Vertical" Grid.Row="1">
                                            <RadioButton Background="Transparent" x:Name="PART_WebRadioButton" FocusVisualStyle="{x:Null}"
                                                        Style="{StaticResource InsertHyperlinkDialogRadioButtonStyle}">
                                                <StackPanel HorizontalAlignment="Center" Orientation="Vertical">
                                                    <ContentControl x:Name="Webicon" 
                                                    ContentTemplate="{StaticResource WebIcon}" Content="{Binding}" />
                                                    <TextBlock Margin="10,2,10,12" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_Exist}" Style="{StaticResource WPFTextBlockStyle}" FontSize="10" TextWrapping="Wrap">
                                                    </TextBlock>
                                                </StackPanel>
                                            </RadioButton>
                                            <RadioButton  Background="Transparent" x:Name="PART_ReferenceRadioButton" FocusVisualStyle="{x:Null}" 
                                                        Style="{StaticResource InsertHyperlinkDialogRadioButtonStyle}">
                                                <StackPanel Orientation="Vertical">
                                                    <ContentControl x:Name="ReferenceIcon" 
                                                    ContentTemplate="{StaticResource WorkBookIcon}" Content="{Binding}" />
                                                    <TextBlock Margin="10,2,10,12" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_Reference}" Style="{StaticResource WPFTextBlockStyle}" FontSize="10" TextWrapping="Wrap">
                                                    </TextBlock>
                                                </StackPanel>
                                            </RadioButton>
                                            <RadioButton Background="Transparent" x:Name="PART_DocumentRadioButton" FocusVisualStyle="{x:Null}" 
                                                        Style="{StaticResource InsertHyperlinkDialogRadioButtonStyle}">
                                                <StackPanel Orientation="Vertical">
                                                    <ContentControl x:Name="DocumentIcon" 
                                                    ContentTemplate="{StaticResource DocumentIcon}" Content="{Binding}" />
                                                    <TextBlock Margin="11,2,11,12" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=HLink_Document}" Style="{StaticResource WPFTextBlockStyle}" FontSize="10" TextWrapping="Wrap">
                                                    </TextBlock>
                                                </StackPanel>
                                            </RadioButton>
                                            <RadioButton Background="Transparent" x:Name="PART_MailRadioButton" FocusVisualStyle="{x:Null}" 
                                                        Style="{StaticResource InsertHyperlinkDialogRadioButtonStyle}">
                                                <StackPanel Orientation="Vertical">
                                                    <ContentControl x:Name="mail" 
                                                    ContentTemplate="{StaticResource MailIcon}" Content="{Binding}" />
                                                    <TextBlock Margin="11,2,11,12" Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName= HLink_Mail}" Style="{StaticResource WPFTextBlockStyle}" FontSize="10" HorizontalAlignment="Center" TextWrapping="Wrap" >
                                                    </TextBlock>
                                                </StackPanel>
                                            </RadioButton>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Grid>
                            <Border BorderBrush="{StaticResource BorderAlt}" Background="{StaticResource PopupBackground}" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" >
                                <Grid  HorizontalAlignment="Right">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_HyperlinkOkButton" Grid.Column="0"
                                                Style="{StaticResource WPFPrimaryButtonStyle}"
                                                Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}" Height="24" Width="64" 
                                                Margin="394 12 0 12 " IsEnabled="False"></Button>
                                    <Button x:Name="PART_HyperlinkCancelButton" Grid.Column="1"
                                                
                                                Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}" Height="24" Width="84" 
                                                 Margin="8 12 16 12 "></Button>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionInsertHyperlinkDialogStyle}" TargetType="spreadsheet:InsertHyperlinkDialog"></Style>

    <Style x:Key="SyncfusionNameManagerDialogStyle" TargetType="spreadsheet:NameManagerDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:NameManagerDialog">
                    <Border Background="{StaticResource PopupBackground}" BorderBrush="{StaticResource BorderAlt}">
                        <Grid HorizontalAlignment="Stretch">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <StackPanel Grid.Row="0" Orientation="Horizontal"  Background="{StaticResource ContentBackground}">
                                <Button x:Name="PART_NewButton"  Margin="16,6,7,4"
                                        Height="24" Width="72" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NewName}" />
                                <Button x:Name="PART_EditButton"  
                                        IsEnabled="{Binding SelectedIndex,ElementName=PART_NameRangesDataGrid,Mode=TwoWay,Converter={StaticResource IndexToBoolConverter}}" Margin="7,6,8,4"
                                         Height="24" Width="69" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Edit}"/>
                                <Button x:Name="PART_DeleteButton"  Margin="8,6,0,4" 
                                        Height="24" Width="83" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Delete}"
                                        IsEnabled="{Binding SelectedIndex,ElementName=PART_NameRangesDataGrid,Mode=TwoWay,Converter={StaticResource IndexToBoolConverter}}"/>
                            </StackPanel>
                            <Grid HorizontalAlignment="Right" Background="{StaticResource ContentBackground}">
                                <shared:DropDownButtonAdv x:Name="PART_FilterDropDownButton" Margin="0,6,16,4" Height="24" Width="87" IconHeight="0" IconWidth="0"
                                                      Label="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Filter}" >
                                    <shared:DropDownMenuGroup>
                                        <shared:DropDownMenuItem x:Name="PART_ClearFilterDropDownMenuItem" FontSize="12" 
                                                  Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=ClearFilter}"/>
                                        <Separator Width="224"/>
                                        <shared:DropDownMenuItem x:Name="PART_ScopedtoWorksheetDropDownMenuItem" IsCheckable="True" FontSize="12"  
                                                  Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NamesScopedtoWorksheet}"/>
                                        <shared:DropDownMenuItem x:Name="PART_ScopedtoWorkbookDropDownMenuItem" IsCheckable="True" FontSize="12"  
                                                  Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NamesScopedtoWorkbook}"/>
                                        <Separator Width="224"/>
                                        <shared:DropDownMenuItem x:Name="PART_NameswithErrorsDropDownMenuItem" IsCheckable="True" FontSize="12"  
                                                  Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NameswithErrors}"/>
                                        <shared:DropDownMenuItem x:Name="PART_NameswithoutErrorsDropDownMenuItem" IsCheckable="True" FontSize="12"  
                                                  Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NameswithoutErrors}"/>
                                        <Separator Visibility="Collapsed"  Width="224"/>
                                        <shared:DropDownMenuItem x:Name="PART_DefinedNamesDropDownMenuItem" Visibility="Collapsed" FontSize="12"  
                                                  Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=DefinedNames}"/>
                                        <shared:DropDownMenuItem x:Name="PART_TableNamesDropDownMenuItem" Visibility="Collapsed" FontSize="12"  
                                                  Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=TableNames}"/>
                                    </shared:DropDownMenuGroup>
                                </shared:DropDownButtonAdv>
                            </Grid>
                            <Grid Grid.Row="1" Background="{StaticResource BorderAlt}">
                                <DataGrid x:Name="PART_NameRangesDataGrid" Height="Auto" Margin="16,4,16,8" AutoGenerateColumns="False" CanUserAddRows="False" CanUserDeleteRows="False" 
                                          CanUserReorderColumns="False" CanUserResizeRows="True" ColumnWidth="*" GridLinesVisibility="None" IsReadOnly="True" 
                                          SelectionUnit="FullRow" SelectionMode="Single" RowHeaderWidth="0" ColumnHeaderHeight="24" RowHeight="20"
                                          
                                          SelectedIndex="{Binding ElementName=NamedRangeWindow,Path=DataGrid_SelectedIndex,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" >
                                    <DataGrid.Columns>
                                        <DataGridTextColumn x:Name="PART_NameDataGridColumn" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Find_Name}" Binding="{Binding Name}"></DataGridTextColumn>
                                        <DataGridTextColumn x:Name="PART_RefersToDataGridColumn" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NameRange_RefersTo}" Binding="{Binding RefersTo}"></DataGridTextColumn>
                                        <DataGridTextColumn x:Name="PART_ScopeDataGridColumn" Header="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=NameRange_Scope}" Binding="{Binding Scope}"></DataGridTextColumn>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                            <Grid Grid.Row="2" HorizontalAlignment="Stretch" VerticalAlignment="Center" Background="{StaticResource ContentBackground}">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <TextBlock x:Name="PART_ReferNameTextBlock" Grid.Row="0" Margin="16,8,0,4"  Text="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Refers_To}"/>
                                <StackPanel Grid.Row="1" Orientation="Horizontal">
                                    <shared:ButtonAdv x:Name="PART_CancelEditButtonAdv" Width="24" Height="24"  VerticalAlignment="Center" IconHeight="12" IconWidth="12" IsEnabled="False" 
                                                     Margin="16,4,2,16" SizeMode="Small" IconTemplate="{StaticResource CancelEditIcon}" Visibility="Visible" />
                                    <shared:ButtonAdv x:Name="PART_EndEditButtonAdv" Width="24" Height="24"  VerticalAlignment="Center" IconHeight="12" IconWidth="12" IsEnabled="False" 
                                                      Margin="2,4,4,16" SizeMode="Small" IconTemplate="{StaticResource EndEditIcon}" Visibility="Visible" />
                                </StackPanel>
                                <TextBox x:Name="PART_EditorTextBox" Grid.Row="1" Width="Auto" Height="24" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="76,4,16,16"
                                         IsEnabled="{Binding Path=SelectedItem,ElementName=PART_NameRangesDataGrid,Converter={StaticResource NullToBoolConverter}}"
                                         Text="{Binding ElementName=NamedRangeWindow,Path=SelectedAddress,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            </Grid>
                            <Border BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" BorderBrush="{StaticResource BorderAlt}" Grid.Row="3">
                                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Right" >
                                    <Button x:Name="PART_CloseButton"  Height="24" Width="77" 
                                            Margin="0,12,16,12" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Close}" />
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionNameManagerDialogStyle}" TargetType="spreadsheet:NameManagerDialog"></Style>

    <Style x:Key="SyncfusionFormatHeightAndWidthDialogStyle" TargetType="spreadsheet:FormatHeightAndWidthDialog">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="spreadsheet:FormatHeightAndWidthDialog">
                    <Border Background="{StaticResource ContentBackground}" BorderBrush="{StaticResource BorderAlt}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="50"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="96"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock x:Name="TextBlock" Height="16"  Style="{StaticResource DescriptionTextBlockStyle}" Text="{Binding Path=Description,Mode=TwoWay,ElementName=formatWindow}" Margin="16,14,4,20" />
                                <TextBox Name="PART_TextBoxValue" Grid.Column="1" Height="24" Margin="0,10,16,16" HorizontalAlignment="Stretch" Style="{StaticResource WPFTextBoxStyle}" Text="{Binding Path=Value,Mode=TwoWay,ElementName=formatWindow}"/>
                            </Grid>
                            <Border Grid.Row="1" BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" Background="{StaticResource PopupBackground}" BorderBrush="{StaticResource BorderAlt}">
                                <Grid HorizontalAlignment="Right">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyButton" Grid.Column="0"
                                                Margin="16 10 4 11 " 
                                                Width="84" Height="24" 
                                                Style="{StaticResource WPFPrimaryButtonStyle}" Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Ok}"/>
                                    <Button x:Name="PART_CancelButton" Grid.Column="1"
                                               Margin="4 10 16 11 " 
                                               Width="84" Height="24" 
                                                Content="{spreadsheet:SpreadsheetLocalizationResourceExtension ResourceName=Cancel}"/>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionFormatHeightAndWidthDialogStyle}" TargetType="spreadsheet:FormatHeightAndWidthDialog"/>

</ResourceDictionary>
