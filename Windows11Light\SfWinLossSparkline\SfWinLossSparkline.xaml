<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:winLossSparkline="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="SyncfusionSfWinLossSparklineStyle"
           TargetType="winLossSparkline:SfWinLossSparkline">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="Background" Value="{StaticResource ContentBackground}"></Setter>
        <Setter Property="BorderThickness" Value="0"></Setter>
        <Setter Property="Interior" Value="{StaticResource PrimaryBackground}"></Setter>
        <Setter Property="RangeBandBrush" Value="{StaticResource Series10}"></Setter>
        <Setter Property="NegativePointBrush" Value="{StaticResource ErrorBackground}"></Setter>
    </Style>

    <Style TargetType="winLossSparkline:SfWinLossSparkline"
           BasedOn="{StaticResource SyncfusionSfWinLossSparklineStyle}" />

</ResourceDictionary>
