<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:tools_controls="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
    xmlns:tools_controls_shared="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF">
    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphPrimaryToggleButton.xaml" /> 
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Expander.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/CheckListBox/CheckListBox.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <BooleanToVisibilityConverter x:Key="Converter" />
    <tools_controls:SortDirectionToVisibilityConverter x:Key="SortDirectionToVisibilityConverter" />
    <tools_controls:SortDirectionToAngleConverter x:Key="AngleConverter" />

    <CornerRadius x:Key="CardView.FiltererToggleButton.CornerRadius">1,1,1,1</CornerRadius>

    <!--  Expander Style  -->

    <Style
        x:Key="ExpanderStyle1"
        BasedOn="{StaticResource WPFExpanderStyle}"
        TargetType="{x:Type Expander}">
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}" />
    </Style>

    <Style x:Key="FileterToggleButtonStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Padding" Value="8 2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border x:Name="border" CornerRadius="{StaticResource CardView.FiltererToggleButton.CornerRadius}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" SnapsToDevicePixels="true">
                        <ContentPresenter x:Name="contentPresenter" Focusable="False" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="{TemplateBinding Padding}" RecognizesAccessKey="True" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundAlt5}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource ContentBackgroundAlt5}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource ContentBackground}"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="true">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource ContentBackground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundAlt1}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource ContentBackgroundAlt1}"/>
                        </Trigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  GroupItem Style  -->

    <ControlTemplate x:Key="GroupStyle1" TargetType="{x:Type GroupItem}">
        <Expander
            x:Name="PART_Expander"
            HorizontalAlignment="Stretch"
            IsExpanded="True"
            Style="{StaticResource ExpanderStyle1}">
            <Expander.Header>
                <StackPanel>
                    <TextBlock Text="{Binding Name}" />
                </StackPanel>
            </Expander.Header>
            <Expander.Content>
                <tools_controls:CardGroupControl
                    ItemContainerStyle="{Binding ItemContainerStyle, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:CardView}}}"
                    ItemsPanel="{Binding ItemsPanel, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:CardView}}}"
                    ItemsSource="{Binding Items}" />
            </Expander.Content>
        </Expander>
    </ControlTemplate>

    <GroupStyle x:Key="GroupStyle">
        <GroupStyle.ContainerStyle>
            <Style TargetType="{x:Type GroupItem}">
                <!--<Setter Property="Margin" Value="3"/>-->
                <Setter Property="Template" Value="{StaticResource GroupStyle1}" />
            </Style>
        </GroupStyle.ContainerStyle>
        <GroupStyle.Panel>
            <ItemsPanelTemplate>
                <tools_controls:CardViewPanel Orientation="{Binding Path=Orientation, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:CardView}}}" />
            </ItemsPanelTemplate>
        </GroupStyle.Panel>
    </GroupStyle>

    <Style x:Key="SyncfusionCardViewSortButtonStyle" TargetType="{x:Type tools_controls:SortButton}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:SortButton}">
                    <Border
                        x:Name="bg"
                        Background="Transparent"
                        BorderBrush="Transparent"
                        CornerRadius="4">
                        <Grid x:Name="grid" Background="Transparent">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <ContentControl
                                x:Name="content" Margin="4 2 2 2"
                                Content="{TemplateBinding Content}"
                                HorizontalContentAlignment="{TemplateBinding HorizontalAlignment}" HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                TextBlock.Foreground="{StaticResource SecondaryForeground}"/>
                            <Border
                                x:Name="Arrow"
                                Grid.Column="1"
                                Visibility="{Binding Path=SortButtonState, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:SortButton}}, Converter={StaticResource SortDirectionToVisibilityConverter}}">
                                <Path
                                    x:Name="path"
                                    Margin="2,2,2,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="M0,0 L7.0000002,0 3.5,3.9999999 z"
                                    Fill="Gray"
                                    RenderTransformOrigin="0.5,0.5">
                                    <Path.RenderTransform>
                                        <TransformGroup>
                                            <RotateTransform Angle="{Binding Path=SortButtonState, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:SortButton}}, Converter={StaticResource AngleConverter}}" />
                                        </TransformGroup>
                                    </Path.RenderTransform>
                                </Path>
                            </Border>

                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                        </Trigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="bg" Property="Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="bg" Property="BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="bg" Property="Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="bg" Property="BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="content" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter TargetName="path" Property="Fill" Value="{StaticResource SecondaryForegroundHovered}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--<Style
        x:Key="ToggleButtonStyle1"
        BasedOn="{StaticResource WPFToggleButtonStyle}"
        TargetType="{x:Type ToggleButton}">
        <Setter Property="Content">
            <Setter.Value>
                <Path
                    x:Name="small_down_arrow"
                    Width="7"
                    Height="4"
                    Margin="0,2,2,0"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Data="M0,0 L7.0000002,0 3.5,3.9999999 z"
                    Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                    Stretch="Fill" />
            </Setter.Value>
        </Setter>
    </Style>-->

    <Style
        x:Key="ToggleButtonStyle1"
        BasedOn="{StaticResource WPFToggleButtonStyle}"
        TargetType="{x:Type ToggleButton}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border
                        x:Name="ToggleButtonBorder"
                        CornerRadius="8">
                        <Grid x:Name="grid" Background="Transparent" HorizontalAlignment="Center">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <ContentControl x:Name="content"
                                            VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                            VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                            HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                            Content="{TemplateBinding Content}" />
                            <Border x:Name="Arrow" Grid.Column="1">
                                <Path
                                    x:Name="small_down_arrow"
                                    Width="7"
                                    Height="4"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Fill="{TemplateBinding Foreground}"
                                    Stretch="Fill" >
                                    <Path.Data>
                                        <PathGeometry>M2.75671 4.17412L0.502069 1.66896C-0.0771018 1.02544 0.379591 0 1.24536 0H5.75464C6.62041 0 7.0771 1.02544 6.49793 1.66897L4.24329 4.17412C3.84605 4.6155 3.15395 4.6155 2.75671 4.17412Z</PathGeometry>
                                    </Path.Data>
                                </Path>
                            </Border>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ToggleButtonBorder" Property="Background" Value="{StaticResource PrimaryBackground}" />
                            <Setter TargetName="ToggleButtonBorder" Property="BorderBrush" Value="{StaticResource ToggleButton.Checked.Border}" />
                            <Setter TargetName="content" Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <!--<Setter Property="Visibility" Value="Visible" TargetName="Arrow"></Setter>-->
                            <Setter TargetName="ToggleButtonBorder" Property="Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="ToggleButtonBorder" Property="BorderBrush" Value="{StaticResource ToggleButton.MouseOver.Border}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="ToggleButtonBorder" Property="Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="ToggleButtonBorder" Property="BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="content" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />

                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  CardViewItem Template  -->

    <Style x:Key="SyncfusionCardViewItemStyle" TargetType="{x:Type tools_controls:CardViewItem}">
        <Setter Property="Margin" Value="5" />
        <Setter Property="Width" Value="200" />
        <Setter Property="Background" Value="{StaticResource PopupBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.TitleTextStyle}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:CardViewItem}">
                    <Grid SnapsToDevicePixels="True">
                        <Border
                            x:Name="CardViewItemBorder" CornerRadius="4"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            />
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Border
                                    x:Name="headerBackground" BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                    Grid.Row="0" CornerRadius="4"
                                    Background="Transparent"
                                    BorderBrush="{StaticResource BorderAlt}">
                                    <ContentPresenter
                                        x:Name="PART_HeaderPresenter"
                                        Margin="8"
                                        Content="{TemplateBinding Header}"
                                        ContentTemplate="{Binding Path=HeaderTemplate, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:CardView}}}"
                                        TextBlock.Foreground="{TemplateBinding Foreground}"
                                        TextElement.FontWeight="{StaticResource Windows11Light.FontWeightMedium}"
                                        TextElement.FontSize="{StaticResource Windows11Light.TitleTextStyle}">
                                        <ContentPresenter.Resources>
                                            <Style BasedOn="{x:Null}" TargetType="TextBlock" />
                                        </ContentPresenter.Resources>
                                    </ContentPresenter>
                                </Border>
                                <ContentPresenter
                                    x:Name="PART_ContentPresenter"
                                    Grid.Row="1"
                                    Margin="2 0 2 2"
                                    ContentSource="Content"
                                    ContentTemplate="{Binding Path=ItemTemplate, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:CardView}}}"
                                    TextElement.FontSize="{Binding Path=FontSize, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:CardView}}}" />
                            </Grid>

                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" TargetName="headerBackground" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="headerBackground" Property="Background" Value="{StaticResource PopupHoveredBackground}" />
                            <Setter TargetName="headerBackground" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource HoveredForeground}" />
                            <Setter TargetName="headerBackground" Property="Background" Value="Transparent"/>
                            <Setter Property="Background" Value="{StaticResource PopupHoveredBackground}"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="headerBackground" Property="Background" Value="{StaticResource PopupSelectedBackground}" />
                            <Setter TargetName="headerBackground" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource SelectedForeground}" />
                            <Setter TargetName="headerBackground" Property="Background" Value="Transparent"/>
                            <Setter Property="Background" Value="{StaticResource PopupSelectedBackground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="headerBackground" Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
                            <Setter TargetName="headerBackground" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="CardViewItemBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="CardViewItemBorder" Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionCardViewItemStyle}" TargetType="{x:Type tools_controls:CardViewItem}" />

    <!--  CardView Template  -->

    <Style x:Key="SyncfusionCardViewStyle" TargetType="{x:Type tools_controls:CardView}">
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <tools_controls:CardViewPanel Orientation="{Binding Path=Orientation, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:CardView}}}" />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:CardView}">
                    <Border
                        x:Name="CardViewBorder"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        SnapsToDevicePixels="True"
                        CornerRadius="4">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Border
                                x:Name="GroupPanelBorder"
                                Height="42" BorderThickness="{StaticResource Windows11Light.BorderThickness0001}"
                                Background="{StaticResource ContentBackground}"
                                BorderBrush="{StaticResource BorderAlt}"
                                Visibility="{Binding ShowHeader, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource Converter}}"
                                CornerRadius="4">
                                <Grid
                                    x:Name="GroupPanel"
                                    AllowDrop="True"
                                    Background="Transparent">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ListBox
                                        x:Name="GroupBox"
                                        Grid.ColumnSpan="2"
                                        Background="Transparent"
                                        BorderBrush="Transparent"
                                        ItemsSource="{TemplateBinding GroupboxCollection}">
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Border
                                                        Width="4"
                                                        Height="10"
                                                        VerticalAlignment="Top"
                                                        Background="Black"
                                                        Visibility="{Binding CanInsert, Converter={StaticResource Converter}}" />
                                                        <ToggleButton x:Name="DragToggle"
                                                        Margin="4 0"
                                                        HorizontalAlignment="Center" VerticalContentAlignment="Center" BorderThickness="0"
                                                        VerticalAlignment="Center" Style="{StaticResource FileterToggleButtonStyle}" 
                                                        Command="{Binding Sort, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:CardView}}}"
                                                        CommandParameter="{Binding IsChecked, RelativeSource={RelativeSource Self}}"
                                                        Content="{Binding Name}" />
                                                    <Border
                                                        Width="4"
                                                        Height="10"
                                                        VerticalAlignment="Top"
                                                        Background="Black"
                                                        Visibility="{Binding CanInsertAfterThis, Converter={StaticResource Converter}}" />
                                                </StackPanel>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>
                                        <ListBox.ItemContainerStyle>
                                            <Style TargetType="ListBoxItem">
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="ListBoxItem">
                                                            <ContentPresenter />
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </ListBox.ItemContainerStyle>
                                        <ListBox.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <StackPanel Orientation="Horizontal" />
                                            </ItemsPanelTemplate>
                                        </ListBox.ItemsPanel>
                                    </ListBox>
                                    <ListBox
                                        x:Name="Group"
                                        Grid.Column="2"
                                        Background="{StaticResource ContentBackground}"
                                        BorderBrush="Transparent"
                                        ItemsSource="{TemplateBinding GroupNames}">
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto" />
                                                        <ColumnDefinition Width="Auto" />
                                                        <ColumnDefinition Width="Auto" />
                                                        <ColumnDefinition Width="*" />
                                                    </Grid.ColumnDefinitions>
                                                    <Border Width="1" Background="Transparent" />
                                                    <ToggleButton
                                                        x:Name="PART_Toggle"
                                                        Grid.Column="1"
                                                        Width="16"
                                                        IsTabStop="True"
                                                        Style="{StaticResource ToggleButtonStyle1}">
                                                        <Popup
                                                            x:Name="PART_FilterPopup"
                                                            AllowsTransparency="True"
                                                            IsOpen="{Binding ElementName=PART_Toggle, Path=IsChecked}"
                                                            Placement="MousePoint" SnapsToDevicePixels="True"
                                                            StaysOpen="False">
                                                            <Border 
                                                                Background="{StaticResource PopupBackground}" 
                                                                BorderThickness="1" 
                                                                Effect="{StaticResource Default.ShadowDepth4}" 
                                                                Margin="16 1 16 16"
                                                                CornerRadius="4">
                                                                <Grid Background="Transparent">
                                                                <Grid.RowDefinitions>
                                                                    <RowDefinition Height="Auto" />
                                                                    <RowDefinition Height="Auto" />
                                                                </Grid.RowDefinitions>
                                                                <tools_controls:CheckListBox
                                                                    x:Name="PART_FilterBox" 
                                                                    Padding="5" BorderThickness="0 0 0 1"
                                                                    Background="Transparent"
                                                                    ItemContainerStyle="{StaticResource SyncfusionCheckListBoxItemStyle}"
                                                                    ItemsSource="{Binding FilterValues}"
                                                                    Tag="{Binding Name}" />
                                                                <Button
                                                                    Grid.Row="1" Margin="2"
                                                                    HorizontalContentAlignment="Center" BorderThickness="0"
                                                                    Background="Transparent"
                                                                    Command="{Binding ClearFilter, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:CardView}}}"
                                                                    CommandParameter="{Binding Path=DataContext, ElementName=PART_FilterBox}"
                                                                    Content="Clear Filter" Height="24"
                                                                    Style="{StaticResource WPFButtonStyle}" />
                                                            </Grid>
                                                            </Border>
                                                        </Popup>
                                                    </ToggleButton>
                                                    <Border
                                                        Grid.Column="2"
                                                        Width="1"
                                                        Background="Transparent" />
                                                    <tools_controls:SortButton
                                                        x:Name="normalSort"
                                                        Grid.Column="3"
                                                        Margin="2 0 2 0"
                                                        VerticalAlignment="Center"
                                                        Command="{Binding NormalSort, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:CardView}}}"
                                                        Content="{Binding Name}"
                                                        IsTabStop="True"
                                                        Style="{StaticResource SyncfusionCardViewSortButtonStyle}" />
                                                </Grid>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>
                                        <ListBox.ItemContainerStyle>
                                            <Style TargetType="ListBoxItem">
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="ListBoxItem">
                                                            <ContentPresenter  />
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                                <Setter Property="IsTabStop" Value="False" />
                                            </Style>
                                        </ListBox.ItemContainerStyle>
                                    </ListBox>
                                    <TextBlock
                                        x:Name="grouptext"
                                        Grid.Column="0" Padding="8"
                                        Grid.ColumnSpan="2"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Foreground="{StaticResource PlaceholderForeground}"
                                        Text="Drag a column header here to group by that column"
                                        TextWrapping="WrapWithOverflow" />
                                </Grid>
                            </Border>
                            <ScrollViewer
                                x:Name="scroll"
                                Grid.Row="1"
                                HorizontalScrollBarVisibility="Auto"
                                VerticalScrollBarVisibility="Disabled"
                                Focusable="False">
                                <Grid Margin="5">
                                    <ItemsPresenter x:Name="PART_Host" />
                                </Grid>
                            </ScrollViewer>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="PART_Host" Property="IsEnabled" Value="false" />
                            <Setter TargetName="PART_Host" Property="Opacity" Value="0.5" />
                            <Setter TargetName="CardViewBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="CardViewBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="grouptext" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="GroupPanelBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="GroupPanelBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                        <Trigger Property="IsGrouping" Value="True">
                            <Setter TargetName="grouptext" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="tools_controls:CardView.Orientation" Value="Horizontal">
                            <Setter TargetName="scroll" Property="HorizontalScrollBarVisibility" Value="Disabled" />
                            <Setter TargetName="scroll" Property="VerticalScrollBarVisibility" Value="Auto" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionCardViewStyle}" TargetType="{x:Type tools_controls:CardView}" />

</ResourceDictionary>
