<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:syncfusion="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ToggleButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphPrimaryToggleButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Button.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ComboBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/DropDownButtonAdv/DropDownButtonAdv.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/ButtonAdv/ButtonAdv.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SplitButtonAdv/SplitButtonAdv.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/ComboBoxAdv/ComboBoxAdv.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Menu.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Label.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBlock.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/CheckBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Separator.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/RadioButton.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="ToolBarButtonStyle" BasedOn="{StaticResource WPFGlyphButtonStyle}" TargetType="{x:Type Button}">
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinWidth}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ToolBarToggleButtonStyle" BasedOn="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" TargetType="{x:Type ToggleButton}">
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinWidth}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionToolBarButtonAdvStyle" BasedOn="{StaticResource SyncfusionButtonAdvStyle}" TargetType="{x:Type syncfusion:ButtonAdv}" />

    <Style x:Key="SyncfusionToolBarDropDownButtonAdvStyle" BasedOn="{StaticResource SyncfusionDropDownButtonAdvStyle}" TargetType="{x:Type syncfusion:DropDownButtonAdv}" />

    <Style x:Key="SyncfusionToolBarSplitButtonAdvStyle" BasedOn="{StaticResource SyncfusionSplitButtonAdvStyle}" TargetType="{x:Type syncfusion:SplitButtonAdv}" />

    <Style x:Key="SyncfusionToolBarComboBoxAdvStyle" BasedOn="{StaticResource SyncfusionComboBoxAdvStyle}" TargetType="{x:Type syncfusion:ComboBoxAdv}">
        <Style.Resources>
            <Style x:Key="SyncfusionToolBarComboBoxItemAdvStyle" BasedOn="{StaticResource SyncfusionComboBoxItemAdvStyle}" TargetType="{x:Type syncfusion:ComboBoxItemAdv}" />
        </Style.Resources>
    </Style>    
    
    <Style x:Key="ToolBarCheckBoxStyle" BasedOn="{StaticResource WPFCheckBoxStyle}"
           TargetType="{x:Type CheckBox}"/>
    <Style x:Key="ToolBarRadioButtonStyle" BasedOn="{StaticResource WPFRadioButtonStyle}"
           TargetType="{x:Type RadioButton}"/>

    <Style x:Key="ToolBarComboBoxStyle" BasedOn="{StaticResource WPFComboBoxStyle}" TargetType="{x:Type ComboBox}">
        <Style.Resources>
            <Style x:Key="ToolBarComboBoxItemStyle" BasedOn="{StaticResource WPFComboBoxItemStyle}" TargetType="{x:Type ComboBoxItem}">
            </Style>
        </Style.Resources>
    </Style>
	
	<Style x:Key="ToolBarLabelStyle" BasedOn="{StaticResource WPFLabelStyle}"
           TargetType="{x:Type Label}"/>

    <Style x:Key="ToolBarTextBlockStyle" BasedOn="{StaticResource WPFTextBlockStyle}"
           TargetType="{x:Type TextBlock}"/>

    <Style x:Key="ToolBarTextBoxStyle" BasedOn="{StaticResource WPFTextBoxStyle}" TargetType="{x:Type TextBox}"/>

    <Style  x:Key="ToolBarMenuStyle" BasedOn="{StaticResource WPFMenuStyle}" TargetType="{x:Type Menu}">
        <Style.Resources>
            <Style x:Key="ToolBarRepeatButtonStyle" BasedOn="{StaticResource WPFMenuScrollButton}" TargetType="{x:Type RepeatButton}"/>
            <Style x:Key="ToolBarScrollViewerStyle" TargetType="{x:Type ScrollViewer}" BasedOn="{StaticResource WPFMenuScrollViewer}"/>
            <Style x:Key="ToolBarMenuItemStyle" BasedOn="{StaticResource WPFMenuItemStyle}" TargetType="{x:Type MenuItem}">
                <Setter Property="BorderBrush" Value="Transparent"/>
            </Style>
        </Style.Resources>
    </Style>

    <Style x:Key="ToolBarSeparatorStyle" BasedOn="{StaticResource WPFSeparatorStyle}" TargetType="{x:Type Separator}" />

</ResourceDictionary>
