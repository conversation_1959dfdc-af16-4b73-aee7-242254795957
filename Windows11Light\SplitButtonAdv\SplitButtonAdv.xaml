<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:local="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:skin="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
   >

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/DropDownButtonAdv/DropDownButtonAdv.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <LinearGradientBrush x:Key="SplitButtonAdvBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource SecondaryBorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource SecondaryBorder.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="SplitButtonAdvBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource SecondaryBorderHoveredGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource SecondaryBorderHovered.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <DataTemplate x:Key="LargeIconTemplate">
        <Image x:Name="LargeImage"
            Width="{Binding Width, RelativeSource={RelativeSource TemplatedParent}}"
            Height="{Binding Height, RelativeSource={RelativeSource TemplatedParent}}"
            Source="{Binding Tag, RelativeSource={RelativeSource TemplatedParent}}" />
    </DataTemplate>

    <DataTemplate x:Key="SmallIconTemplate">
        <Image x:Name="smallimage"
            Width="{Binding Width, RelativeSource={RelativeSource TemplatedParent}}"
            Height="{Binding Height, RelativeSource={RelativeSource TemplatedParent}}"
            Source="{Binding Tag, RelativeSource={RelativeSource TemplatedParent}}" />
    </DataTemplate>

    <ControlTemplate x:Key="SyncfusionSplitButtonAdvControlTemplate" TargetType="{x:Type local:SplitButtonAdv}">
        <Grid>
            <Border
                Name="ItemBorder1"
                MinHeight="{StaticResource Windows11Light.MinHeight}"
                Background="{TemplateBinding Background}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}"
                CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                
                SnapsToDevicePixels="True">
                <Grid Margin="{TemplateBinding Padding}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <Border x:Name="PART_Button" 
                        Focusable="True" 
                        FocusVisualStyle="{x:Null}" 
                        Background="Transparent"
                        CornerRadius="4 4 0 0"
                        Margin="0 0 0.5 0"
                        >
                    <ContentPresenter x:Name="LargeIconContent" ContentTemplate="{TemplateBinding IconTemplate}" Content="{Binding }" Margin="2"
                                                              MinWidth="{TemplateBinding MinWidth}"
                                                              MinHeight="{TemplateBinding MinHeight}"
                                                              ContentTemplateSelector="{TemplateBinding IconTemplateSelector}"/>
                </Border>
                <Border
                        x:Name="PART_DropDownButton"
                        Grid.Row="1" 
                        Focusable="True"
                        FocusVisualStyle="{x:Null}"
                        Background="Transparent"
                        CornerRadius="0 0 4 4"
                        BorderThickness="0,1,0,0" >
                    <StackPanel>
                        <TextBlock
                            x:Name="PART_TextAreaLarge" 
                            Margin="2,0,4,0"
							Text="{TemplateBinding Label}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}" >
                        </TextBlock>
                        <Path
                                        Name="Arrow"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"   
                                        Width="10"
                                        Height="10"
                                        Fill="{StaticResource IconColor}">
                            <Path.Data>
                                    <PathGeometry>M5.24264 7.77817L9.13173 3.88908L9.83884 4.59619L5.24264 9.19239L0.646448 4.59619L1.35356 3.88908L5.24264 7.77817Z</PathGeometry>
                            </Path.Data>
                            <Path.Margin>
                                    <Thickness>0</Thickness>
                            </Path.Margin>
                            <Path.LayoutTransform>
                                <RotateTransform Angle="0"/>
                            </Path.LayoutTransform>
                        </Path>
                    </StackPanel>
                </Border>
        </Grid>
            </Border>
        <Popup
                x:Name="PART_DropDown"
                AllowsTransparency="True"
                Placement="Bottom"
                StaysOpen="False">
                <Border
                    MinWidth="{TemplateBinding MinWidth}"
                    MinHeight="{TemplateBinding MinHeight}"
                    Background="{StaticResource PopupBackground}"
                    BorderBrush="{StaticResource BorderAlt}"
                    CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                    BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                    Effect="{StaticResource Default.ShadowDepth4}"
                    SnapsToDevicePixels="True">
                    <Border.Margin>
                        <Thickness>16,1,16,16</Thickness>
                    </Border.Margin>
                    <ContentPresenter/>
                    <Border.RenderTransform>
                        <TranslateTransform/>
                    </Border.RenderTransform>
                    <Border.Triggers>
                        <EventTrigger RoutedEvent="Border.Loaded">
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimationUsingKeyFrames
                                                    BeginTime="00:00:00"
                                                    Storyboard.TargetProperty="(RenderTransform).(TranslateTransform.Y)">
                                        <SplineDoubleKeyFrame
                                                       KeyTime="00:00:00.01"
                                                       Value="-800" />
                                        <SplineDoubleKeyFrame
                                                       KeyTime="00:00:0.2"
                                                       Value="0"
                                                       KeySpline="0.0, 0.1, 0.0, 1.0" />
                                    </DoubleAnimationUsingKeyFrames>
                                    <DoubleAnimationUsingKeyFrames
                                                        Storyboard.TargetProperty="(Effect).Opacity">
                                        <SplineDoubleKeyFrame
                                                        KeyTime="00:00:00.01"
                                                        Value="0" />
                                        <SplineDoubleKeyFrame
                                                        KeyTime="00:00:0.3"
                                                        Value="0" />
                                        <SplineDoubleKeyFrame
                                                        KeyTime="00:00:0.4"
                                                        Value="0.17" />
                                    </DoubleAnimationUsingKeyFrames>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Border.Triggers>
                </Border>
            </Popup>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                <Setter TargetName="PART_DropDownButton" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                <Setter TargetName="PART_TextAreaLarge" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
            </Trigger>
            <Trigger SourceName="PART_Button" Property="IsFocused" Value="True">
                <Setter TargetName="PART_Button" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                <Setter TargetName="PART_Button" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
				 <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                <Setter TargetName="LargeIconContent" Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
            </Trigger>
            <Trigger SourceName="PART_DropDownButton" Property="IsFocused" Value="True">
                <Setter TargetName="PART_DropDownButton" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                <Setter TargetName="PART_DropDownButton" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                <Setter TargetName="PART_TextAreaLarge" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
				 <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                <Setter TargetName="PART_TextAreaLarge" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}" />
            </Trigger>
            <Trigger SourceName="Arrow" Property="IsFocused" Value="True">
                <Setter TargetName="PART_DropDownButton" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                <Setter TargetName="PART_DropDownButton" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                <Setter TargetName="PART_TextAreaLarge" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
            </Trigger>
            <Trigger SourceName="PART_DropDownButton" Property="IsMouseOver" Value="true">
                <Setter TargetName="PART_DropDownButton" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                <Setter TargetName="PART_DropDownButton" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                <Setter TargetName="PART_TextAreaLarge" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                <Setter TargetName="PART_TextAreaLarge" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorHovered}" />
            </Trigger>
            <Trigger SourceName="Arrow" Property="IsMouseOver" Value="true">
                <Setter TargetName="PART_DropDownButton" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                <Setter TargetName="PART_DropDownButton" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                <Setter TargetName="PART_TextAreaLarge" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                <Setter TargetName="PART_TextAreaLarge" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
            </Trigger>
            <Trigger SourceName="PART_Button" Property="IsMouseOver" Value="true">
                <Setter TargetName="PART_Button" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                <Setter TargetName="PART_Button" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                <Setter TargetName="PART_DropDownButton" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                <Setter TargetName="LargeIconContent" Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                <Setter TargetName="PART_DropDownButton" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                <Setter TargetName="PART_DropDownButton" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                <Setter TargetName="PART_TextAreaLarge" Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}"/>
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}" />
            </Trigger>
            <Trigger Property="IsMultiLine" Value="True">
                <Setter Property="TextBlock.TextWrapping" Value="Wrap" TargetName="PART_TextAreaLarge"/>
            </Trigger>
            <Trigger Property="IsMultiLine" Value="False">
                <Setter Property="TextBlock.TextWrapping" Value="NoWrap" TargetName="PART_TextAreaLarge"/>
            </Trigger>
            <Trigger Property="IsDropDownPressed" Value="True">
                <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                <Setter TargetName="PART_DropDownButton" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                <Setter TargetName="PART_DropDownButton" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                <Setter TargetName="PART_TextAreaLarge" Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="LargeIconContent" Property="Opacity" Value="0.38" />
                <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundDisabled}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderDisabled}" />
                <Setter TargetName="PART_DropDownButton" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderDisabled}" />
                <Setter TargetName="PART_DropDownButton" Property="Border.Background" Value="{StaticResource SecondaryBackgroundDisabled}" />
                <Setter TargetName="PART_TextAreaLarge" Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IconTemplate" Value="{x:Null}"/>
                    <Condition Property="IconTemplateSelector" Value="{x:Null}"/>
                </MultiTrigger.Conditions>
                <Setter Property="ContentTemplate" TargetName="LargeIconContent" Value="{StaticResource LargeIconTemplate}"/>
                <Setter Property="Tag" TargetName="LargeIconContent" Value="{Binding LargeIcon, RelativeSource={RelativeSource TemplatedParent}}"/>
            </MultiTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SyncfusionSplitButtonAdvStyle" TargetType="{x:Type local:SplitButtonAdv}">
        <Setter Property="Background" Value="{StaticResource SecondaryBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource SplitButtonAdvBorderBrush}" />
        <Setter Property="Foreground" Value="{StaticResource SecondaryForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="SmallIcon" Value="/Syncfusion.Shared.WPF;component//Controls/ButtonControls/Images/WordArt16.png" />
        <Setter Property="LargeIcon" Value="/Syncfusion.Shared.WPF;component//Controls/ButtonControls/Images/WordArt32.png" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Label" Value="Split Button" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:SplitButtonAdv}">
                    <Grid>
                        <Border
                            Name="ItemBorder"
                            MinHeight="{StaticResource Windows11Light.MinHeight}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            
                            SnapsToDevicePixels="True">

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition x:Name="dropDownColumn" Width="24"  />
                                </Grid.ColumnDefinitions>
                                <Border
                                    x:Name="PART_ButtonNormal"
                                    Grid.Column="0" 
                                    Focusable="True" 
                                    FocusVisualStyle="{x:Null}"
                                    Background="Transparent"
                                    CornerRadius="4 0 0 4"
                                    Margin="0 0.5 0 0.5"
                                    >
                                    <Grid
                                        Margin="{TemplateBinding Padding}"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition />
                                        </Grid.ColumnDefinitions>
                                        <ContentPresenter x:Name="SmallIconContent" ContentTemplate="{TemplateBinding IconTemplate}" Content="{Binding }" Margin="2"
                                                              ContentTemplateSelector="{TemplateBinding IconTemplateSelector}"/>
                                        <TextBlock
                                            x:Name="PART_Text"
                                            Grid.Column="1"
                                            Margin="2,0,4,0"
                                            HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                            FontFamily="{TemplateBinding FontFamily}"
                                            FontSize="{TemplateBinding FontSize}"
                                            Foreground="{TemplateBinding Foreground}"
                                            Text="{TemplateBinding Label}" >
                                            <TextBlock.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="TextBlock"/>
                                            </TextBlock.Resources>
                                        </TextBlock>
                                    </Grid>
                                </Border>
                                <Border
                                    x:Name="PART_DropDownButtonNormal"
                                    Grid.Column="1" 
                                    Focusable="True" 
                                    FocusVisualStyle="{x:Null}"
                                    Background="Transparent"
                                    CornerRadius="0 4 4 0"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness1000}"
                                    >
                                    <Path
                                        Name="Arrow"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"   
                                        Width="10"
                                        Height="10"
                                        Fill="{StaticResource IconColor}">
                                        <Path.Data>
                                            <PathGeometry>M5.24264 7.77817L9.13173 3.88908L9.83884 4.59619L5.24264 9.19239L0.646448 4.59619L1.35356 3.88908L5.24264 7.77817Z</PathGeometry>
                                        </Path.Data>
                                        <Path.Margin>
                                            <Thickness>0</Thickness>
                                        </Path.Margin>
                                        <Path.LayoutTransform>
                                            <RotateTransform Angle="0"/>
                                        </Path.LayoutTransform>
                                    </Path>
                                </Border>
                            </Grid>
                        </Border>
                        <Popup
                            x:Name="PART_DropDown"
                            AllowsTransparency="True"
                            Placement="Bottom"
                            StaysOpen="False">
                            <Border
                                MinWidth="{TemplateBinding MinWidth}"
                                MinHeight="{TemplateBinding MinHeight}"
                                Background="{StaticResource PopupBackground}"
                                BorderBrush="{StaticResource BorderAlt}"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                Effect="{StaticResource Default.ShadowDepth4}"
                                SnapsToDevicePixels="True">
                                <Border.Margin>
                                    <Thickness>16,1,16,16</Thickness>
                                </Border.Margin>
                                <ContentPresenter/>
                                <Border.RenderTransform>
                                    <TranslateTransform/>
                                </Border.RenderTransform>
                                <Border.Triggers>
                                    <EventTrigger RoutedEvent="Border.Loaded">
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimationUsingKeyFrames
                                                    BeginTime="00:00:00"
                                                    Storyboard.TargetProperty="(RenderTransform).(TranslateTransform.Y)">
                                                    <SplineDoubleKeyFrame
                                                       KeyTime="00:00:00.01"
                                                       Value="-800" />
                                                    <SplineDoubleKeyFrame
                                                       KeyTime="00:00:0.2"
                                                       Value="0"
                                                       KeySpline="0.0, 0.1, 0.0, 1.0" />
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames
                                                        Storyboard.TargetProperty="(Effect).Opacity">
                                                    <SplineDoubleKeyFrame
                                                        KeyTime="00:00:00.01"
                                                        Value="0" />
                                                    <SplineDoubleKeyFrame
                                                        KeyTime="00:00:0.3"
                                                        Value="0" />
                                                    <SplineDoubleKeyFrame
                                                        KeyTime="00:00:0.4"
                                                        Value="0.17" />
                                                </DoubleAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger>
                                </Border.Triggers>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="SizeMode" Value="Small">
                            <Setter TargetName="dropDownColumn" Property="Width" Value="Auto"/>
                            <Setter TargetName="Arrow" Property="Margin" Value="2,1,2,1"/>
                            <Setter TargetName="PART_Text" Property="Visibility" Value="Collapsed"/>
                        </Trigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="dropDownColumn" Property="Width" Value="32"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_Text" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger SourceName="PART_ButtonNormal" Property="IsFocused" Value="True">
                            <Setter TargetName="PART_ButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_ButtonNormal" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" /> 
						   <Setter TargetName="PART_Text" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                        </Trigger>
                        <Trigger SourceName="PART_DropDownButtonNormal" Property="IsFocused" Value="True">
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                             <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
							<Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger SourceName="PART_DropDownButtonNormal" Property="IsMouseOver" Value="true">
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SplitButtonAdvBorderBrushHovered}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger SourceName="Arrow" Property="IsFocused" Value="True">
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger SourceName="Arrow" Property="IsMouseOver" Value="true">
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SplitButtonAdvBorderBrushHovered}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger SourceName="PART_ButtonNormal" Property="IsMouseOver" Value="true">
                            <Setter TargetName="PART_ButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_ButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SplitButtonAdvBorderBrushHovered}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                            <Setter TargetName="PART_Text" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="PART_ButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                            <Setter TargetName="PART_ButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                            <Setter TargetName="PART_Text" Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}"/>
                        </Trigger>
                        <Trigger Property="IsDropDownPressed" Value="True">
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                            <Setter TargetName="PART_Text" Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="SmallIconContent" Property="Border.Opacity" Value="0.38" />
                            <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundDisabled}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderDisabled}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundDisabled}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderDisabled}" />
                            <Setter TargetName="PART_Text" Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IconTemplate" Value="{x:Null}"/>
                                <Condition Property="IconTemplateSelector" Value="{x:Null}"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="ContentTemplate" TargetName="SmallIconContent" Value="{StaticResource SmallIconTemplate}"/>
                            <Setter Property="Tag" TargetName="SmallIconContent" Value="{Binding SmallIcon, RelativeSource={RelativeSource TemplatedParent}}"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="SizeMode" Value="Large">
                <Setter Property="Template" Value="{StaticResource SyncfusionSplitButtonAdvControlTemplate}" />
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSplitButtonAdvStyle}" TargetType="{x:Type local:SplitButtonAdv}" />

    <Style x:Key="SyncfusionFlatSplitButtonAdvStyle" TargetType="{x:Type local:SplitButtonAdv}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{StaticResource SplitButtonAdvBorderBrush}" />
        <Setter Property="Foreground" Value="{StaticResource SecondaryForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="SmallIcon" Value="/Syncfusion.Shared.WPF;component//Controls/ButtonControls/Images/WordArt16.png" />
        <Setter Property="LargeIcon" Value="/Syncfusion.Shared.WPF;component//Controls/ButtonControls/Images/WordArt32.png" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Label" Value="Split Button" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:SplitButtonAdv}">
                    <Grid>
                        <Border
                            Name="ItemBorder"
                            MinHeight="{StaticResource Windows11Light.MinHeight}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            SnapsToDevicePixels="True">

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition x:Name="dropDownColumn" Width="24" />
                            </Grid.ColumnDefinitions>
                            <Border
                                    x:Name="PART_ButtonNormal"
                                    Grid.Column="0" 
                                    Focusable="True" 
                                    FocusVisualStyle="{x:Null}"
                                    CornerRadius="4 0 0 4"
                                    Background="Transparent">
                                <Grid
                                        Margin="{TemplateBinding Padding}"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <ContentPresenter x:Name="SmallIconContent" ContentTemplate="{TemplateBinding IconTemplate}" Content="{Binding }"
                                                              ContentTemplateSelector="{TemplateBinding IconTemplateSelector}"/>
                                    <TextBlock
                                            x:Name="PART_Text"
                                            Grid.Column="1"
                                            Margin="2,0,4,0"
                                            HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                            FontFamily="{TemplateBinding FontFamily}"
                                            FontSize="{TemplateBinding FontSize}"
                                            Foreground="{TemplateBinding Foreground}"
                                            Text="{TemplateBinding Label}" >
                                        <TextBlock.Resources>
                                            <Style BasedOn="{x:Null}" TargetType="TextBlock"/>
                                        </TextBlock.Resources>
                                    </TextBlock>
                                </Grid>
                            </Border>
                            <Border
                                    x:Name="PART_DropDownButtonNormal"
                                    Grid.Column="1" 
                                    Focusable="True" 
                                    FocusVisualStyle="{x:Null}"
                                    Background="Transparent"
                                    CornerRadius="0 4 4 0"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness1000}">
                                <Path
                                        Name="Arrow"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Height="6"
                                        Width="12"
                                        Fill="{StaticResource IconColor}">
                                    <Path.Data>
                                        <PathGeometry>M6.0010305,0 L12.001999,5.9840137 10.590014,7.3999998 6.0010305,2.8240075 1.4119857,7.3999998 0,5.9840137 z</PathGeometry>
                                    </Path.Data>
                                    <Path.Margin>
                                            <Thickness>0</Thickness>
                                    </Path.Margin>
                                    <Path.LayoutTransform>
                                        <RotateTransform Angle="180"/>
                                    </Path.LayoutTransform>
                                </Path>
                            </Border>
                        </Grid>
                        </Border>
                        <Popup
                            x:Name="PART_DropDown"
                            AllowsTransparency="True"
                            Placement="Bottom"
                            StaysOpen="False">
                            <Border
                                MinWidth="{TemplateBinding MinWidth}"
                                MinHeight="{TemplateBinding MinHeight}"
                                Background="{StaticResource PopupBackground}"
                                BorderBrush="{StaticResource BorderAlt}"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                SnapsToDevicePixels="True">
                                <ContentPresenter/>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="SizeMode" Value="Small">
                            <Setter TargetName="dropDownColumn" Property="Width" Value="Auto"/>
                            <Setter TargetName="Arrow" Property="Margin" Value="2,1,2,1"/>
                            <Setter TargetName="PART_Text" Property="Visibility" Value="Collapsed"/>
                        </Trigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="dropDownColumn" Property="Width" Value="32"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="PART_Text" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger SourceName="PART_ButtonNormal" Property="IsFocused" Value="True">
                            <Setter TargetName="PART_ButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_ButtonNormal" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="PART_Text" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                        </Trigger>
                        <Trigger SourceName="PART_DropDownButtonNormal" Property="IsFocused" Value="True">
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger SourceName="PART_DropDownButtonNormal" Property="IsMouseOver" Value="true">
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SplitButtonAdvBorderBrushHovered}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger SourceName="Arrow" Property="IsFocused" Value="True">
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger SourceName="Arrow" Property="IsMouseOver" Value="true">
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SplitButtonAdvBorderBrushHovered}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger SourceName="PART_ButtonNormal" Property="IsMouseOver" Value="true">
                            <Setter TargetName="PART_ButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="PART_ButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SplitButtonAdvBorderBrushHovered}" />
                            <Setter TargetName="PART_Text" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="PART_ButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackground}" />
                            <Setter TargetName="PART_ButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorder}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackground}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorder}" />
                            <Setter TargetName="PART_Text" Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger Property="IsDropDownPressed" Value="True">
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                            <Setter TargetName="PART_Text" Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="SmallIconContent" Property="Border.Opacity" Value="0.38" />
                            <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundDisabled}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderDisabled}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.Background" Value="{StaticResource SecondaryBackgroundDisabled}" />
                            <Setter TargetName="PART_DropDownButtonNormal" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderDisabled}" />
                            <Setter TargetName="PART_Text" Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IconTemplate" Value="{x:Null}"/>
                                <Condition Property="IconTemplateSelector" Value="{x:Null}"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="ContentTemplate" TargetName="SmallIconContent" Value="{StaticResource SmallIconTemplate}"/>
                            <Setter Property="Tag" TargetName="SmallIconContent" Value="{Binding SmallIcon, RelativeSource={RelativeSource TemplatedParent}}"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="SizeMode" Value="Large">
                <Setter Property="Template" Value="{StaticResource SyncfusionSplitButtonAdvControlTemplate}" />
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CurveKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--This style is used in image editor control header toolbar crop button-->
    <Style x:Key="SyncfusionToolbarSplitButtonAdvStyle" 
           TargetType="{x:Type local:SplitButtonAdv}">
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Label" Value="Split Button"/>
        <Setter Property="BorderBrush" Value="{StaticResource SplitButtonAdvBorderBrush}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:SplitButtonAdv}">
                    <Grid Focusable="False">
                        <Popup x:Name="PART_DropDown" 
                               Placement="Bottom" 
                               AllowsTransparency="True" 
                               Focusable="False" 
                               IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource FindAncestor, AncestorLevel=1, AncestorType={x:Type local:SplitButtonAdv}}}"
                               PopupAnimation="Slide"
                               StaysOpen="False">
                            <Grid x:Name="Part_GridPopup" 
                                MinWidth="{TemplateBinding MinWidth}" 
                                MinHeight="{TemplateBinding MinHeight}">
                                <Border  BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                         CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                         Background="{StaticResource PopupBackground}"
                                         BorderBrush="{StaticResource BorderAlt}"
                                         MinWidth="28" 
                                         MinHeight="14"
                                         Effect="{StaticResource Default.ShadowDepth4}">
                                    <Border.Margin>
                                        <Thickness>16,1,16,16</Thickness>
                                    </Border.Margin>
                                    <ContentPresenter/>
                                </Border>
                            </Grid>
                        </Popup>
                        <Border Name="SplitButtonBorder" 
                                Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                MinHeight="22"
                                SnapsToDevicePixels="True"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}">

                            <Border Name="InnerBorder"
                                    Background="Transparent"
                                    MinHeight="22"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                    CornerRadius="{StaticResource Windows11Light.CornerRadius4}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition  />
                                        <ColumnDefinition x:Name="dropDownColumn" Width="22"/>
                                    </Grid.ColumnDefinitions>
                                    <Border x:Name="PART_ButtonNormal" 
                                            Focusable="True"
                                            Background="Transparent" 
                                            Grid.Column="0">
                                        <Border.CornerRadius>
                                            <CornerRadius>1,1,1,1</CornerRadius>
                                        </Border.CornerRadius>
                                        <Grid Margin="{TemplateBinding Padding}" 
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <ContentPresenter x:Name="SmallIconContent"
                                                              Margin="2"
                                                              ContentTemplate="{TemplateBinding IconTemplate}"
                                                              Content="{Binding}"
                                                              ContentTemplateSelector="{TemplateBinding IconTemplateSelector}" />
                                            <TextBlock x:Name="PART_NormalText"
                                                       Grid.Column="1"
                                                       Text="{TemplateBinding Label}"
                                                       FontFamily="{TemplateBinding FontFamily}"
                                                       FontSize="{TemplateBinding FontSize}"
                                                       VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                                       HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                                       Margin="2,0,2,0" />
                                        </Grid>
                                    </Border>
                                    <Border x:Name="PART_DropDownButtonNormal"
                                            Focusable="True"
                                            Grid.Column="1"
                                            BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                            Background="Transparent">
                                        <Border.CornerRadius>
                                            <CornerRadius>1,1,1,1</CornerRadius>
                                        </Border.CornerRadius>
                                        <Path Name="Arrow"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Fill="{StaticResource IconColor}"
                                              Data="M 0 0 L 4 4 L 8 0 Z"
                                              Margin="2,0,2,0" />
                                    </Border>
                                </Grid>
                            </Border>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" TargetName="SplitButtonBorder" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter TargetName="dropDownColumn" Property="Width" Value="32"/>
                            <Setter Property="Padding" Value="5"/>
                            <Setter Property="Height" TargetName="SmallIconContent" Value="20"/>
                            <Setter Property="Width" TargetName="SmallIconContent" Value="20"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Border.Background" TargetName="SplitButtonBorder" Value="{StaticResource SplitButtonAdvBorderBrushHovered}" />
                            <Setter Property="Border.BorderBrush" TargetName="PART_DropDownButtonNormal" Value="#DEDEDE"/>
                            <Setter Property="Path.Fill" TargetName="Arrow" Value="{StaticResource IconColor}"/>
                            <Setter Property="TextBlock.Foreground" TargetName="PART_NormalText" Value="Black"/>
                        </Trigger>
                       <Trigger Property="IsDropDownOpen" Value="True">
                            <Setter Property="Border.Background" TargetName="SplitButtonBorder" Value="{StaticResource SecondaryBackgroundSelected}"/>
                            <Setter Property="Border.BorderBrush" TargetName="PART_DropDownButtonNormal" Value="#DEDEDE"/>
                            <Setter Property="Path.Fill" TargetName="Arrow" Value="{StaticResource IconColor}"/>
                            <Setter Property="TextBlock.Foreground" TargetName="PART_NormalText" Value="Black"/>
                            <Setter Property="Border.Background"
                                    TargetName="PART_DropDownButtonNormal"
                                    Value="{StaticResource SplitButtonAdvBorderBrush}" />
                            <Setter Property="Border.Background"
                                    TargetName="PART_ButtonNormal"
                                    Value="{StaticResource SplitButtonAdvBorderBrush}" />
                            <Setter Property="Border.CornerRadius"
                                    TargetName="PART_ButtonNormal"
                                    Value="4,0,0,4" />
                            <Setter Property="Border.CornerRadius"
                                    TargetName="PART_DropDownButtonNormal"
                                    Value="0,4,4,0" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Border.Opacity" TargetName="SplitButtonBorder" Value="0.5"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" SourceName="PART_ButtonNormal" Value="true">
                            <Setter Property="Border.Background" TargetName="SplitButtonBorder" Value="Transparent"/>
                            <Setter Property="Border.BorderBrush" TargetName="SplitButtonBorder" Value="{StaticResource SecondaryBorderHovered}"/>
                            <Setter Property="Border.Background" TargetName="PART_ButtonNormal" Value="{StaticResource SplitButtonAdvBorderBrushHovered}" />
                            <Setter Property="Border.Background" TargetName="PART_DropDownButtonNormal" Value="Transparent" />
                            <Setter Property="Border.BorderBrush" TargetName="InnerBorder" Value="{StaticResource SecondaryBorderHovered}"/>
                            <Setter Property="Path.Fill" TargetName="Arrow" Value="{StaticResource IconColor}"/>
                            <Setter Property="Border.CornerRadius"
                                    TargetName="PART_ButtonNormal"
                                    Value="4,0,0,4" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" SourceName="PART_DropDownButtonNormal" Value="true">
                            <Setter Property="Border.Background" TargetName="SplitButtonBorder" Value="Transparent"/>
                            <Setter Property="Border.BorderBrush" TargetName="SplitButtonBorder" Value="{StaticResource SecondaryBorderHovered}"/>
                            <Setter Property="Border.BorderBrush" TargetName="PART_ButtonNormal" Value="Transparent"/>
                            <Setter Property="Border.Background" TargetName="PART_DropDownButtonNormal" Value="{StaticResource SplitButtonAdvBorderBrushHovered}"/>
                            <Setter Property="Border.BorderBrush" TargetName="InnerBorder" Value="{StaticResource SecondaryBorderHovered}"/>
                            <Setter Property="Path.Fill" TargetName="Arrow" Value="{StaticResource IconColor}"/>
                            <Setter Property="Border.CornerRadius"
                                    TargetName="PART_DropDownButtonNormal"
                                    Value="0,4,4,0" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
