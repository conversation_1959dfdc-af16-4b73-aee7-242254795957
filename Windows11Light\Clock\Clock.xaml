<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
     
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:utilsOuter="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphRepeatButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!--  converter  -->

    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

    <!--  brushes  -->

    <SolidColorBrush x:Key="Clock.DigitalClock.Static.Border" Color="White" />

    <!--  InnerClockGeneralStyle  -->
    <Style x:Key="InnerClockGeneralStyle" TargetType="{x:Type FrameworkElement}">
        <Setter Property="Width" Value="{Binding Path=InnerClockGeneralWidth, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="Height" Value="{Binding Path=InnerClockGeneralHeight, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
    </Style>

    <!--  ThirdBorderFrameStyle  -->
    <Style x:Key="ThirdBorderFrameStyle" TargetType="{x:Type Border}">
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource PopupBorder}" />
        <Setter Property="Width" Value="{Binding Path=ThirdBorderFrameRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="Height" Value="{Binding Path=ThirdBorderFrameRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="BorderThickness" Value="{Binding Path=InnerBorderThickness, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="CornerRadius" Value="{Binding Path=ClockCornerRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
    </Style>

    <!--  CenteredEllipseStyle  -->
    <Style x:Key="CenteredEllipseStyle" TargetType="{x:Type Ellipse}">
        <Setter Property="Stroke" Value="{StaticResource ContentForeground}" />
        <Setter Property="Fill" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="Width" Value="{Binding Path=CenteredEllipseRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="Height" Value="{Binding Path=CenteredEllipseRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="Panel.ZIndex" Value="4" />
        <Setter Property="Canvas.Top" Value="60" />
        <Setter Property="Canvas.Left" Value="57" />
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" Value="{StaticResource BorderAlt}" />
                <Setter Property="Stroke" Value="{StaticResource BorderAlt}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  ClockPointStyle  -->
    <Style x:Key="ClockPointStyle" TargetType="{x:Type Rectangle}">
        <Setter Property="Fill"  Value="{Binding Path=ClockPointBrush, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="Width" Value="1" />
        <Setter Property="Height" Value="8" />
        <Setter Property="Canvas.Top" Value="5" />
        <Setter Property="Canvas.Left" Value="60" />
        <Style.Triggers>
            <!--<Trigger Property="utilsOuter:SkinStorage.VisualStyle" Value="Default">
                <Setter Property="Fill" Value="{Binding Path=ClockPointBrush, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
            </Trigger>-->
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" Value="{StaticResource BorderAlt}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  HourHandStyle  -->
    <Style x:Key="HourHandStyle" TargetType="{x:Type Path}">
        <Setter Property="Stroke" Value="{StaticResource ContentForeground}" />
        <Setter Property="Fill" Value="{StaticResource ContentForeground}" />
        <Setter Property="Data" Value="M 6,6 L 6,38 L 3,38 L 3,6 Z" />
        <Setter Property="Panel.ZIndex" Value="2" />
        <Setter Property="Canvas.Top" Value="27" />
        <Setter Property="Canvas.Left" Value="58" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Fill" Value="{StaticResource ContentForegroundAlt1}" />
                <Setter Property="Stroke" Value="{StaticResource ContentForegroundAlt1}" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=IsPressedHourHand, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" Value="True">
                <Setter Property="Fill" Value="{StaticResource ContentForeground}" />
                <Setter Property="Stroke" Value="{StaticResource ContentForeground}" />
            </DataTrigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" Value="{StaticResource BorderAlt}" />
                <Setter Property="Stroke" Value="{StaticResource BorderAlt}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  HourHandDefaultStyle  -->
    <Style
        x:Key="HourHandDefaultStyle"
        BasedOn="{StaticResource HourHandStyle}"
        TargetType="{x:Type Path}" />

    <!--  MinuteHandStyle  -->
    <Style x:Key="MinuteHandStyle" TargetType="{x:Type Path}">
        <Setter Property="Stroke" Value="{StaticResource ContentForeground}" />
        <Setter Property="Fill" Value="{StaticResource ContentForeground}" />
        <Setter Property="Data" Value="M 6,6 L 6,48 L 3,48 L 3,6 Z" />
        <Setter Property="Panel.ZIndex" Value="1" />
        <Setter Property="Canvas.Top" Value="16" />
        <Setter Property="Canvas.Left" Value="58" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Fill" Value="{StaticResource ContentForegroundAlt1}" />
                <Setter Property="Stroke" Value="{StaticResource ContentForegroundAlt1}" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=IsPressedMinuteHand, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" Value="True">
                <Setter Property="Fill" Value="{StaticResource ContentForeground}" />
                <Setter Property="Stroke" Value="{StaticResource ContentForeground}" />
            </DataTrigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" Value="{StaticResource BorderAlt}" />
                <Setter Property="Stroke" Value="{StaticResource BorderAlt}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  MinuteHandDefaultStyle  -->
    <Style
        x:Key="MinuteHandDefaultStyle"
        BasedOn="{StaticResource MinuteHandStyle}"
        TargetType="{x:Type Path}" />

    <!--  SecondHandStyle  -->
    <Style x:Key="SecondHandStyle" TargetType="{x:Type Rectangle}">
        <Setter Property="Fill" Value="{StaticResource ContentForeground}" />
        <Setter Property="Width" Value="{Binding Path=SecondHandThickness, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="Height" Value="{Binding Path=SecondHandHeight, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="Panel.ZIndex" Value="3" />
        <Setter Property="Canvas.Top" Value="12" />
        <Setter Property="Canvas.Left" Value="61" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Fill" Value="{StaticResource ContentForegroundAlt1}" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=IsPressedSecondHand, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" Value="True">
                <Setter Property="Fill" Value="{StaticResource ContentForeground}" />
            </DataTrigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" Value="{StaticResource BorderAlt}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  SecondHandDefaultStyle  -->
    <Style
        x:Key="SecondHandDefaultStyle"
        BasedOn="{StaticResource SecondHandStyle}"
        TargetType="{x:Type Rectangle}" />

    <!--  TimeTextBlockStyle  -->
    <Style x:Key="TimeTextBlockStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="Width" Value="67" />
        <Setter Property="Margin" Value="2,0,5,0" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="FontWeight" Value="Normal" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="TextBlock.TextAlignment" Value="Center" />
    </Style>

    <!--  TimeTextBlockSmallStyle  -->
    <Style x:Key="TimeTextBlockSmallStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="FontSize" Value="10" />
        <Setter Property="FontWeight" Value="Normal" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Margin" Value="1,0,1,0" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalAlignment" Value="Right" />
    </Style>

    <!--  FrameBorderStyle  -->
    <Style x:Key="FrameBorderStyle" TargetType="{x:Type Border}">
        <Setter Property="Background"  Value="{Binding Path=FrameBackground, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="BorderBrush" Value="{Binding Path=FrameBorderBrush,RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="CornerRadius" Value="{Binding Path=FrameCornerRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="BorderThickness" Value="{Binding Path=FrameBorderThickness, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="Width" Value="{Binding Path=FrameWidth, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
    </Style>

    <!--  FrameInnerBorderStyle  -->
    <Style x:Key="FrameInnerBorderStyle" TargetType="{x:Type Border}">
        <Setter Property="CornerRadius" Value="{Binding Path=FrameCornerRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="BorderThickness" Value="{Binding Path=FrameInnerBorderThickness, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}"/>
        <Setter Property="Padding" Value="10,5,10,0" />
        <Setter Property="Width" Value="Auto" />
        <Setter Property="Background" Value="{Binding Path=FrameBackground, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
        <Setter Property="BorderBrush" Value="{Binding Path=FrameInnerBorderBrush,RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
    </Style>

    <!--  TwelfthPartTemplate  -->
    <DataTemplate x:Key="TwelfthPartTemplate">
        <Canvas>
            <TextBlock
                Canvas.Left="35"
                Canvas.Top="17"
                FontSize="10"
                Foreground="{StaticResource ContentForeground}"
                Text="11" />
            <Rectangle Style="{StaticResource ClockPointStyle}">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="0" CenterX="2" CenterY="60" />
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
    </DataTemplate>

    <!--  EleventhPartTemplate  -->
    <DataTemplate x:Key="EleventhPartTemplate">
        <Canvas>
            <TextBlock
                Canvas.Left="18"
                Canvas.Top="37"
                FontSize="10"
                Foreground="{StaticResource ContentForeground}"
                Text="10" />
            <Rectangle Canvas.Left="61" Style="{StaticResource ClockPointStyle}">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="330" CenterX="2" CenterY="60" />
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
    </DataTemplate>

    <!--  TenthPartTemplate  -->
    <DataTemplate x:Key="TenthPartTemplate">
        <Canvas>
            <TextBlock
                Canvas.Left="12"
                Canvas.Top="58"
                FontSize="10"
                Foreground="{StaticResource ContentForeground}"
                Text="9" />
            <Rectangle Canvas.Left="61" Style="{StaticResource ClockPointStyle}">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="300" CenterX="2" CenterY="60" />
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
    </DataTemplate>

    <!--  NinethPartTemplate  -->
    <DataTemplate x:Key="NinethPartTemplate">
        <Canvas>
            <TextBlock
                Canvas.Left="21"
                Canvas.Top="82"
                FontSize="10"
                Foreground="{StaticResource ContentForeground}"
                Text="8" />
            <Rectangle Style="{StaticResource ClockPointStyle}">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="270" CenterX="2" CenterY="60" />
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
    </DataTemplate>

    <!--  EighthPartTemplate  -->
    <DataTemplate x:Key="EighthPartTemplate">
        <Canvas>
            <TextBlock
                Canvas.Left="38"
                Canvas.Top="98"
                FontSize="10"
                Foreground="{StaticResource ContentForeground}"
                Text="7" />
            <Rectangle Canvas.Left="61" Style="{StaticResource ClockPointStyle}">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="240" CenterX="2" CenterY="60" />
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
    </DataTemplate>

    <!--  SeventhPartTemplate  -->
    <DataTemplate x:Key="SeventhPartTemplate">
        <Canvas>
            <TextBlock
                Canvas.Left="60"
                Canvas.Top="103"
                FontSize="10"
                Foreground="{StaticResource ContentForeground}"
                Text="6" />
            <Rectangle Canvas.Left="61" Style="{StaticResource ClockPointStyle}">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="210" CenterX="2" CenterY="60" />
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
    </DataTemplate>

    <!--  SixthPartTemplate  -->
    <DataTemplate x:Key="SixthPartTemplate">
        <Canvas>
            <TextBlock
                Canvas.Left="83"
                Canvas.Top="98"
                FontSize="10"
                Foreground="{StaticResource ContentForeground}"
                Text="5" />
            <Rectangle Style="{StaticResource ClockPointStyle}">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="180" CenterX="2" CenterY="60" />
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
    </DataTemplate>

    <!--  FifthPartTemplate  -->
    <DataTemplate x:Key="FifthPartTemplate">
        <Canvas>
            <TextBlock
                Canvas.Left="101"
                Canvas.Top="82"
                FontSize="10"
                Foreground="{StaticResource ContentForeground}"
                Text="4" />
            <Rectangle Canvas.Left="61" Style="{StaticResource ClockPointStyle}">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="150" CenterX="2" CenterY="60" />
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
    </DataTemplate>

    <!--  FourthPartTemplate  -->
    <DataTemplate x:Key="FourthPartTemplate">
        <Canvas>
            <TextBlock
                Canvas.Left="107"
                Canvas.Top="58"
                FontSize="10"
                Foreground="{StaticResource ContentForeground}"
                Text="3" />
            <Rectangle Canvas.Left="61" Style="{StaticResource ClockPointStyle}">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="120" CenterX="2" CenterY="60" />
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
    </DataTemplate>

    <!--  ThirdPartTemplate  -->
    <DataTemplate x:Key="ThirdPartTemplate">
        <Canvas>
            <TextBlock
                Canvas.Left="100"
                Canvas.Top="32"
                FontSize="10"
                Foreground="{StaticResource ContentForeground}"
                Text="2" />
            <Rectangle Style="{StaticResource ClockPointStyle}">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="90" CenterX="2" CenterY="60" />
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
    </DataTemplate>

    <!--  SecondPartTemplate  -->
    <DataTemplate x:Key="SecondPartTemplate">
        <Canvas>
            <TextBlock
                Canvas.Left="83"
                Canvas.Top="15"
                FontSize="10"
                Foreground="{StaticResource ContentForeground}"
                Text="1" />
            <Rectangle Canvas.Left="61" Style="{StaticResource ClockPointStyle}">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="60" CenterX="2" CenterY="60" />
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
    </DataTemplate>

    <!--  FirstPartTemplate  -->
    <DataTemplate x:Key="FirstPartTemplate">
        <Canvas>
            <TextBlock
                Canvas.Left="55"
                Canvas.Top="12"
                FontSize="10"
                Foreground="{StaticResource ContentForeground}"
                Text="12" />
            <Rectangle Canvas.Left="61" Style="{StaticResource ClockPointStyle}">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="30" CenterX="2" CenterY="60" />
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
    </DataTemplate>

    <Style
        x:Key="UpRepeatButtonStyle"
        BasedOn="{StaticResource WPFGlyphRepeatButtonStyle}"
        TargetType="{x:Type RepeatButton}">
        <Setter Property="ContentTemplate">
            <Setter.Value>
                <DataTemplate>
                    <Path
                        x:Name="downbuttonpath"
                        Width="8"
                        Height="4"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Stroke="{Binding Path=AMPMSelectorButtonsArrowBrush, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}"
                        StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}"
                        SnapsToDevicePixels="True"
                        Stretch="Uniform" >
                        <Path.Data>
                            <PathGeometry>M4.9960045,0 L10.008001,5.6040001 9.2630047,6.2709999 4.9960045,1.5 0.74499548,6.2530002 0,5.5860004 z</PathGeometry>
                        </Path.Data>
                    </Path>
                </DataTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="DownRepeatButtonStyle"
        BasedOn="{StaticResource WPFGlyphRepeatButtonStyle}"
        TargetType="{x:Type RepeatButton}">
        <Setter Property="Command" Value="local:Clock.m_AMPMSelect"/>
        <Setter Property="ContentTemplate">
            <Setter.Value>
                <DataTemplate>
                    <Path
                        x:Name="downbuttonpath"
                        Width="8"
                        Height="4"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Stroke="{Binding Path=AMPMSelectorButtonsArrowBrush, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}"
                        StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}"
                        SnapsToDevicePixels="True"
                        Stretch="Uniform" >
                        <Path.Data>
                            <PathGeometry>M0.74499548,0 L5.0119957,4.7700001 9.2630047,0.017000169 10.008001,0.68400005 5.0119957,6.2700001 0,0.66699985 z</PathGeometry>
                        </Path.Data>
                    </Path>
                </DataTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <LinearGradientBrush x:Key="Clock.Static.BorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="SyncfusionClockStyle" TargetType="{x:Type local:Clock}">
        <Setter Property="local:Clock.ClockPointBrush" Value="{StaticResource BorderAlt1}" />
        <Setter Property="local:Clock.CenterCircleBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="local:Clock.BorderBrush" Value="{StaticResource PopupBorder}" />

        <Setter Property="local:Clock.HourHandBrush" Value="{StaticResource ContentForeground}" />
        <Setter Property="local:Clock.MinuteHandBrush" Value="{StaticResource ContentForeground}" />
        <Setter Property="local:Clock.SecondHandBrush" Value="{StaticResource ContentForeground}" />
        <Setter Property="local:Clock.HourHandMouseOverBrush" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="local:Clock.MinuteHandMouseOverBrush" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="local:Clock.SecondHandMouseOverBrush" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="local:Clock.HourHandPressedBrush" Value="{StaticResource ContentForeground}" />
        <Setter Property="local:Clock.MinuteHandPressedBrush" Value="{StaticResource ContentForeground}" />
        <Setter Property="local:Clock.SecondHandPressedBrush" Value="{StaticResource ContentForeground}" />

        <Setter Property="local:Clock.AMPMSelectorButtonsBackground" Value="Transparent" />
        <Setter Property="local:Clock.AMPMSelectorButtonsBorderBrush" Value="Transparent" />
        <Setter Property="local:Clock.AMPMSelectorBorderBrush" Value="{StaticResource Clock.Static.BorderBrush}" />
        <Setter Property="local:Clock.AMPMSelectorButtonsArrowBrush" Value="{StaticResource IconColor}" />
        <Setter Property="local:Clock.AMPMMouseOverButtonsArrowBrush" Value="{StaticResource IconColorHovered}" />
        <Setter Property="local:Clock.AMPMMouseOverButtonsBorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
        <Setter Property="local:Clock.AMPMMouseOverButtonsBackground" Value="{StaticResource ContentBackgroundHovered}" />
        <Setter Property="local:Clock.AMPMSelectorBackground" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="local:Clock.AMPMSelectorForeground" Value="{StaticResource ContentForeground}" />

        <Setter Property="local:Clock.FrameBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="local:Clock.FrameInnerBorderBrush" Value="{StaticResource PopupBorder}" />
        <Setter Property="local:Clock.FrameBackground" Value="{StaticResource PopupBackground}" />
        <Setter Property="FrameBorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}"/>
        <!--<Setter Property="FirstBorderFrameRadius" Value="151"/>-->
        <Setter Property="BorderThickness" Value="2" />
        <Setter Property="ClockCornerRadius" Value="90" />
        <!--<Setter Property="SecondInnerBorderFrameRadius" Value="135"/>
        <Setter Property="ThirdBorderFrameRadius" Value="122"/>-->
        <Setter Property="InnerBorderThickness" Value="4" />
        <Setter Property="DialBorderThickness" Value="25" />
        <Setter Property="FrameCornerRadius" Value="4"/>
        <Setter Property="FrameInnerBorderThickness" Value="{StaticResource Windows11Light.BorderThickness}" />
        <Setter Property="local:Clock.AMPMSelectorCornerRadius" Value="4"/>
        <Setter Property="AMPMSelectorBorderThickness" Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}" />
        <!--<Setter Property="CenteredEllipseRadius" Value="10"/>-->
        <Style.Triggers>
            <Trigger Property="CanResize" Value="True">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type local:Clock}">
                            <Viewbox Height="{TemplateBinding ActualHeight}">
                                <Border Name="FrameBorder" Style="{StaticResource FrameBorderStyle}">
                                    <Border Name="FrameInnerBorder" Style="{StaticResource FrameInnerBorderStyle}">
                                        <Grid HorizontalAlignment="Center">
                                            <Grid.Resources>
                                                <local:SecondsConverter x:Key="secondsConverter" />
                                                <local:MinutesConverter x:Key="minutesConverter" />
                                                <local:HoursConverter x:Key="hoursConverter" />
                                            </Grid.Resources>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="{Binding Path=FirstBorderFrameRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="{Binding Path=FirstBorderFrameRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
                                                <RowDefinition Height="30" />
                                            </Grid.RowDefinitions>
                                            <Border Name="ThirdBorder" Style="{StaticResource ThirdBorderFrameStyle}" />
                                            <Canvas FlowDirection="LeftToRight" Style="{StaticResource InnerClockGeneralStyle}">
                                                <Ellipse Name="CenteredEllipse" Style="{StaticResource CenteredEllipseStyle}" />
                                                <ContentPresenter ContentTemplate="{StaticResource FirstPartTemplate}" />
                                                <ContentPresenter ContentTemplate="{StaticResource SecondPartTemplate}" />
                                                <ContentPresenter ContentTemplate="{StaticResource ThirdPartTemplate}" />
                                                <ContentPresenter ContentTemplate="{StaticResource FourthPartTemplate}" />
                                                <ContentPresenter ContentTemplate="{StaticResource FifthPartTemplate}" />
                                                <ContentPresenter ContentTemplate="{StaticResource SixthPartTemplate}" />
                                                <ContentPresenter ContentTemplate="{StaticResource SeventhPartTemplate}" />
                                                <ContentPresenter ContentTemplate="{StaticResource EighthPartTemplate}" />
                                                <ContentPresenter ContentTemplate="{StaticResource NinethPartTemplate}" />
                                                <ContentPresenter ContentTemplate="{StaticResource TenthPartTemplate}" />
                                                <ContentPresenter ContentTemplate="{StaticResource EleventhPartTemplate}" />
                                                <ContentPresenter ContentTemplate="{StaticResource TwelfthPartTemplate}" />
                                                <Path
                                                    x:Name="HourHand"
                                                    Width="5"
                                                    Style="{StaticResource HourHandDefaultStyle}">
                                                    <Path.RenderTransform>
                                                        <RotateTransform x:Name="HourHandRotateTransform" Angle="{Binding Path=DateTime, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource hoursConverter}}" CenterX="4" CenterY="39" />
                                                    </Path.RenderTransform>
                                                </Path>
                                                <Path
                                                    x:Name="MinuteHand"
                                                    Width="5"
                                                    Style="{StaticResource MinuteHandDefaultStyle}">
                                                    <Path.RenderTransform>
                                                        <RotateTransform x:Name="MinuteHandRotateTransform" Angle="{Binding Path=DateTime, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource minutesConverter}}" CenterX="0" CenterY="0" />
                                                    </Path.RenderTransform>
                                                </Path>
                                                <Rectangle
                                                    x:Name="SecondHand"
                                                    Width="1"
                                                    Style="{StaticResource SecondHandDefaultStyle}">
                                                    <Rectangle.RenderTransform>
                                                        <RotateTransform x:Name="SecondHandRotateTransform" Angle="{Binding Path=DateTime, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource secondsConverter}}" CenterX="0.5" CenterY="55" />
                                                    </Rectangle.RenderTransform>
                                                </Rectangle>

                                                <StackPanel
                                                    Name="SmallAmPmPanel"
                                                    Canvas.Left="45"
                                                    Canvas.Top="76"
                                                    Width="26"
                                                    Panel.ZIndex="0"
                                                    Visibility="{TemplateBinding IsInsideAmPmVisible,
                                                                                 Converter={StaticResource BooleanToVisibilityConverter}}">
                                                    <Border
                                                        Name="SmallAmPmPanelBorder"
                                                        BorderBrush="{Binding Path=AMPMSelectorBorderBrush, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}"
                                                        BorderThickness="{Binding Path=AMPMSelectorBorderThickness, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}"
                                                        CornerRadius="{Binding Path=AMPMSelectorCornerRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}">
                                                        <Grid Name="SmallAmPmPanelGrid" Background="{Binding Path=AMPMSelectorBackground, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="16" />
                                                                <ColumnDefinition Width="12" />
                                                            </Grid.ColumnDefinitions>
                                                            <TextBlock
                                                                Name="SmallTimeTextBlock"
                                                                Grid.Column="0"
                                                                Style="{StaticResource TimeTextBlockSmallStyle}"
                                                                Text="{Binding Path=LongTime, RelativeSource={RelativeSource TemplatedParent}}" />
                                                            <Grid
                                                                Grid.Row="0"
                                                                Grid.RowSpan="3"
                                                                Grid.Column="1">
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition />
                                                                </Grid.ColumnDefinitions>
                                                                <Grid.RowDefinitions>
                                                                    <RowDefinition />
                                                                    <RowDefinition />
                                                                </Grid.RowDefinitions>
                                                                <RepeatButton
                                                                    Name="UpInnerRepeatButton"
                                                                    Grid.Row="0"
                                                                    MinWidth="10"
                                                                    MinHeight="8"
                                                                    Margin="-1,0,0,0"
                                                                    Command="local:Clock.m_AMPMSelect"
                                                                    IsTabStop="False"
                                                                    Style="{StaticResource UpRepeatButtonStyle}" />
                                                                <RepeatButton
                                                                    Name="DownInnerRepeatButton"
                                                                    Grid.Row="1"
                                                                    MinWidth="10"
                                                                    MinHeight="8"
                                                                    Margin="-1,0,0,0"
                                                                    IsTabStop="False"
                                                                    Command="local:Clock.m_AMPMSelect"
                                                                    Style="{StaticResource DownRepeatButtonStyle}" />
                                                            </Grid>
                                                        </Grid>
                                                    </Border>
                                                </StackPanel>
                                            </Canvas>

                                            <StackPanel
                                                Name="BigAmPmPanel"
                                                Grid.Row="1"
                                                Grid.Column="0"
                                                Width="90"
                                                VerticalAlignment="Center"
                                                Visibility="{TemplateBinding IsDigitalAmPmVisible,
                                                                             Converter={StaticResource BooleanToVisibilityConverter}}">
                                                <Border
                                                    Name="BigAmPmPanelBorder"
                                                    BorderBrush="{Binding Path=AMPMSelectorBorderBrush, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}"
                                                    BorderThickness="{Binding Path=AMPMSelectorBorderThickness, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}"
                                                    CornerRadius="{Binding Path=AMPMSelectorCornerRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}">
                                                    <StackPanel
                                                        Name="BigAmPmContainer"
                                                        Background="Transparent"
                                                        Orientation="Horizontal">
                                                        <TextBlock
                                                            Name="TimeTextBlock"
                                                            Grid.Column="0"
                                                            Style="{StaticResource TimeTextBlockStyle}"
                                                            Text="{Binding Path=LongTime, RelativeSource={RelativeSource TemplatedParent}}" />
                                                        <Grid
                                                            Name="BigAmPmSelectorGrid"
                                                            Grid.Row="0"
                                                            Grid.RowSpan="3"
                                                            Grid.Column="1"
                                                            Width="16">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition />
                                                            </Grid.ColumnDefinitions>
                                                            <Grid.RowDefinitions>
                                                                <RowDefinition />
                                                                <RowDefinition />
                                                            </Grid.RowDefinitions>
                                                            <RepeatButton
                                                                Name="UpRepeatButton"
                                                                Grid.Row="0"
                                                                MinWidth="15"
                                                                MinHeight="10"
                                                                Command="local:Clock.m_AMPMSelect"
                                                                IsTabStop="False"
                                                                Style="{StaticResource UpRepeatButtonStyle}" />
                                                            <RepeatButton
                                                                Name="DownRepeatButton"
                                                                Grid.Row="1"
                                                                MinWidth="15"
                                                                MinHeight="10"
                                                                Command="local:Clock.m_AMPMSelect"
                                                                IsTabStop="False"
                                                                Style="{StaticResource DownRepeatButtonStyle}" />
                                                        </Grid>
                                                    </StackPanel>
                                                </Border>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </Border>
                            </Viewbox>
                            <ControlTemplate.Triggers>
                                <Trigger Property="local:Clock.IsDigitalAmPmVisible" Value="False">
                                    <Setter TargetName="BigAmPmPanel" Property="Width" Value="75" />
                                </Trigger>
                                <Trigger Property="local:Clock.IsDigitalAmPmVisible" Value="True">
                                    <Setter TargetName="BigAmPmPanel" Property="Width" Value="90" />
                                </Trigger>
                                <Trigger Property="local:Clock.AMPMSelectorPosition" Value="Bottom">
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Top" Value="96" />
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Left" Value="49" />
                                </Trigger>
                                <Trigger Property="local:Clock.AMPMSelectorPosition" Value="Top">
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Top" Value="16" />
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Left" Value="48" />
                                </Trigger>
                                <Trigger Property="local:Clock.AMPMSelectorPosition" Value="Left">
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Top" Value="56" />
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Left" Value="10" />
                                </Trigger>
                                <Trigger Property="local:Clock.AMPMSelectorPosition" Value="Right">
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Top" Value="56" />
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Left" Value="87" />
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="false">
                                    <Setter TargetName="FrameInnerBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                                    <Setter TargetName="FrameInnerBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                                    <Setter TargetName="FrameBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                                    <Setter TargetName="FrameBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                                    <Setter TargetName="ThirdBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                                    <Setter TargetName="ThirdBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                                    <Setter TargetName="SmallAmPmPanelBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                                    <Setter TargetName="SmallAmPmPanelGrid" Property="Background" Value="{StaticResource BorderAlt}" />
                                    <Setter TargetName="SmallTimeTextBlock" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="CanResize" Value="False">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type local:Clock}">
                            <Border Name="FrameBorder" Style="{StaticResource FrameBorderStyle}">
                                <Border Name="FrameInnerBorder" Style="{StaticResource FrameInnerBorderStyle}">
                                    <Grid HorizontalAlignment="Center">
                                        <Grid.Resources>
                                            <local:SecondsConverter x:Key="secondsConverter" />
                                            <local:MinutesConverter x:Key="minutesConverter" />
                                            <local:HoursConverter x:Key="hoursConverter" />
                                        </Grid.Resources>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="{Binding Path=FirstBorderFrameRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="{Binding Path=FirstBorderFrameRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}" />
                                            <RowDefinition Height="30" />
                                        </Grid.RowDefinitions>
                                        <Border Name="ThirdBorder" Style="{StaticResource ThirdBorderFrameStyle}" />
                                        <Canvas FlowDirection="LeftToRight" Style="{StaticResource InnerClockGeneralStyle}">
                                            <Ellipse Name="CenteredEllipse" Style="{StaticResource CenteredEllipseStyle}" />
                                            <ContentPresenter ContentTemplate="{StaticResource FirstPartTemplate}" />
                                            <ContentPresenter ContentTemplate="{StaticResource SecondPartTemplate}" />
                                            <ContentPresenter ContentTemplate="{StaticResource ThirdPartTemplate}" />
                                            <ContentPresenter ContentTemplate="{StaticResource FourthPartTemplate}" />
                                            <ContentPresenter ContentTemplate="{StaticResource FifthPartTemplate}" />
                                            <ContentPresenter ContentTemplate="{StaticResource SixthPartTemplate}" />
                                            <ContentPresenter ContentTemplate="{StaticResource SeventhPartTemplate}" />
                                            <ContentPresenter ContentTemplate="{StaticResource EighthPartTemplate}" />
                                            <ContentPresenter ContentTemplate="{StaticResource NinethPartTemplate}" />
                                            <ContentPresenter ContentTemplate="{StaticResource TenthPartTemplate}" />
                                            <ContentPresenter ContentTemplate="{StaticResource EleventhPartTemplate}" />
                                            <ContentPresenter ContentTemplate="{StaticResource TwelfthPartTemplate}" />
                                            <Path
                                                x:Name="HourHand"
                                                Width="5"
                                                Style="{StaticResource HourHandDefaultStyle}">
                                                <Path.RenderTransform>
                                                    <RotateTransform x:Name="HourHandRotateTransform" Angle="{Binding Path=DateTime, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource hoursConverter}}" CenterX="4" CenterY="39" />
                                                </Path.RenderTransform>
                                            </Path>
                                            <Path
                                                x:Name="MinuteHand"
                                                Width="5"
                                                Style="{StaticResource MinuteHandDefaultStyle}">
                                                <Path.RenderTransform>
                                                    <RotateTransform x:Name="MinuteHandRotateTransform" Angle="{Binding Path=DateTime, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource minutesConverter}}" CenterX="4.7" CenterY="49" />
                                                </Path.RenderTransform>
                                            </Path>
                                            <Rectangle
                                                x:Name="SecondHand"
                                                Width="1"
                                                Style="{StaticResource SecondHandDefaultStyle}">
                                                <Rectangle.RenderTransform>
                                                    <RotateTransform x:Name="SecondHandRotateTransform" Angle="{Binding Path=DateTime, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource secondsConverter}}" CenterX="0.5" CenterY="55" />
                                                </Rectangle.RenderTransform>
                                            </Rectangle>
                                            <StackPanel
                                                Name="SmallAmPmPanel"
                                                Canvas.Left="45"
                                                Canvas.Top="76"
                                                Width="26"
                                                Panel.ZIndex="0"
                                                Visibility="{TemplateBinding IsInsideAmPmVisible,
                                                                             Converter={StaticResource BooleanToVisibilityConverter}}">
                                                <Border
                                                    Name="SmallAmPmPanelBorder"
                                                    BorderBrush="{Binding Path=AMPMSelectorBorderBrush, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}"
                                                    BorderThickness="{Binding Path=AMPMSelectorBorderThickness, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}"
                                                    CornerRadius="{Binding Path=AMPMSelectorCornerRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}">
                                                    <Grid Name="SmallAmPmPanelGrid" Background="{Binding Path=AMPMSelectorBackground, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="16" />
                                                            <ColumnDefinition Width="12" />
                                                        </Grid.ColumnDefinitions>
                                                        <TextBlock
                                                            Name="SmallTimeTextBlock"
                                                            Grid.Column="0"
                                                            Style="{StaticResource TimeTextBlockSmallStyle}"
                                                            Text="{Binding Path=LongTime, RelativeSource={RelativeSource TemplatedParent}}" />
                                                        <Grid
                                                            Grid.Row="0"
                                                            Grid.RowSpan="3"
                                                            Grid.Column="1">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition />
                                                            </Grid.ColumnDefinitions>
                                                            <Grid.RowDefinitions>
                                                                <RowDefinition />
                                                                <RowDefinition />
                                                            </Grid.RowDefinitions>
                                                            <RepeatButton
                                                                Name="UpInnerRepeatButton"
                                                                Grid.Row="0"
                                                                MinWidth="10"
                                                                MinHeight="8"
                                                                Margin="-1,0,0,0"
                                                                Command="local:Clock.m_AMPMSelect"
                                                                IsTabStop="False"
                                                                Style="{StaticResource UpRepeatButtonStyle}" />
                                                            <RepeatButton
                                                                Name="DownInnerRepeatButton"
                                                                Grid.Row="1"
                                                                MinWidth="10"
                                                                MinHeight="8"
                                                                IsTabStop="False"
                                                                Margin="-1,0,0,0"
                                                                Command="local:Clock.m_AMPMSelect"
                                                                Style="{StaticResource DownRepeatButtonStyle}" />
                                                        </Grid>
                                                    </Grid>
                                                </Border>
                                            </StackPanel>
                                        </Canvas>
                                        <StackPanel
                                            Name="BigAmPmPanel"
                                            Grid.Row="1"
                                            Grid.Column="0"
                                            Width="90"
                                            VerticalAlignment="Center"
                                            Visibility="{TemplateBinding IsDigitalAmPmVisible,
                                                                         Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <Border
                                                Name="BigAmPmPanelBorder"
                                                BorderBrush="{Binding Path=AMPMSelectorBorderBrush, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}"
                                                BorderThickness="{Binding Path=AMPMSelectorBorderThickness, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}"
                                                CornerRadius="{Binding Path=AMPMSelectorCornerRadius, RelativeSource={RelativeSource AncestorType={x:Type local:Clock}}}">
                                                <StackPanel
                                                    Name="BigAmPmContainer"
                                                    Background="Transparent"
                                                    Orientation="Horizontal">
                                                    <TextBlock
                                                        Name="TimeTextBlock"
                                                        Grid.Column="0"
                                                        Style="{StaticResource TimeTextBlockStyle}"
                                                        Text="{Binding Path=LongTime, RelativeSource={RelativeSource TemplatedParent}}" />
                                                    <Grid
                                                        Name="BigAmPmSelectorGrid"
                                                        Grid.Row="0"
                                                        Grid.RowSpan="3"
                                                        Grid.Column="1"
                                                        Width="16">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition />
                                                        </Grid.ColumnDefinitions>
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition />
                                                            <RowDefinition />
                                                        </Grid.RowDefinitions>
                                                        <RepeatButton
                                                            Name="UpRepeatButton"
                                                            Grid.Row="0"
                                                            MinWidth="15"
                                                            MinHeight="10"
                                                            Command="local:Clock.m_AMPMSelect"
                                                            IsTabStop="False"
                                                            Style="{StaticResource UpRepeatButtonStyle}" />
                                                        <RepeatButton
                                                            Name="DownRepeatButton"
                                                            Grid.Row="1"
                                                            MinWidth="15"
                                                            MinHeight="10"
                                                            IsTabStop="False"
                                                            Command="local:Clock.m_AMPMSelect"
                                                            Style="{StaticResource DownRepeatButtonStyle}" />
                                                    </Grid>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="local:Clock.IsDigitalAmPmVisible" Value="False">
                                    <Setter TargetName="BigAmPmPanel" Property="Width" Value="75" />
                                </Trigger>
                                <Trigger Property="local:Clock.IsDigitalAmPmVisible" Value="True">
                                    <Setter TargetName="BigAmPmPanel" Property="Width" Value="90" />
                                </Trigger>
                                <Trigger Property="local:Clock.AMPMSelectorPosition" Value="Bottom">
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Top" Value="96" />
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Left" Value="49" />
                                </Trigger>
                                <Trigger Property="local:Clock.AMPMSelectorPosition" Value="Top">
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Top" Value="16" />
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Left" Value="48" />
                                </Trigger>
                                <Trigger Property="local:Clock.AMPMSelectorPosition" Value="Left">
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Top" Value="56" />
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Left" Value="10" />
                                </Trigger>
                                <Trigger Property="local:Clock.AMPMSelectorPosition" Value="Right">
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Top" Value="56" />
                                    <Setter TargetName="SmallAmPmPanel" Property="Canvas.Left" Value="87" />
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="false">
                                    <Setter TargetName="FrameInnerBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                                    <Setter TargetName="FrameInnerBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                                    <Setter TargetName="FrameBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                                    <Setter TargetName="FrameBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                                    <Setter TargetName="ThirdBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                                    <Setter TargetName="ThirdBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                                    <Setter TargetName="SmallAmPmPanelBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                                    <Setter TargetName="SmallAmPmPanelGrid" Property="Background" Value="{StaticResource BorderAlt}" />
                                    <Setter TargetName="SmallTimeTextBlock" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionClockStyle}" TargetType="{x:Type local:Clock}" />
</ResourceDictionary>

