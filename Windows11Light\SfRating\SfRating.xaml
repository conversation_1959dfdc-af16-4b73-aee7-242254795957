<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Input="clr-namespace:Syncfusion.Windows.Controls.Input;assembly=Syncfusion.SfInput.WPF"
    xmlns:Microsoft_Windows_Themes="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:converter="clr-namespace:Syncfusion.Windows.Converters;assembly=Syncfusion.SfInput.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:Syncfusion.Windows.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Primitives;assembly=Syncfusion.SfInput.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ToolTip.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!--  Theme Keys for SfRating  -->

    <converter:PrecisionToVisibilityConverter x:Key="VisibilityConverter" />

    <Style x:Key="ToolTipStyle" TargetType="ToolTip">
        <Setter Property="Background" Value="{StaticResource TooltipBackground}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.SubTitleTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="BorderBrush" Value="{StaticResource TooltipBorder}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ContentControl">
                    <Grid 
                        Effect="{StaticResource Default.ShadowDepth3}"
                        RenderTransformOrigin="0.5,0.5"
                        Margin="14,0,14,4">
                        <Border
                         Background="{TemplateBinding Background}"
                         Height="28"
                         BorderThickness="{TemplateBinding BorderThickness}"
                         BorderBrush="{TemplateBinding BorderBrush}"
                         VerticalAlignment="Center"
                         HorizontalAlignment="Center"
                         Padding="8,5,8,7"
                         SnapsToDevicePixels="True"
                         CornerRadius="{StaticResource Windows11Light.CornerRadius4}" >
                            <TextBlock
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Center"
                                FontFamily="{TemplateBinding FontFamily}"
                                FontSize="{TemplateBinding FontSize}"
                                FontWeight="{TemplateBinding FontWeight}"
                                Foreground="{TemplateBinding Foreground}"
                                Text="{Binding Path=PreviewValue, RelativeSource={RelativeSource AncestorType={x:Type Input:SfRating}}}" />     
                        </Border>
                        <TextBlock
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Center"
                            Margin="10,0,10,5"
                            FontFamily="{TemplateBinding FontFamily}"
                            FontSize="{TemplateBinding FontSize}"
                            FontWeight="{TemplateBinding FontWeight}"
                            Foreground="{TemplateBinding Foreground}"
                            Visibility="Hidden"
                            Text="{Binding Path=PreviewValue, RelativeSource={RelativeSource AncestorType={x:Type Input:SfRating}}}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Style for SfRating  -->
    <Style x:Key="SyncfusionSfRatingStyle" TargetType="Input:SfRating">
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="ItemsSpacing" Value="8" />
        <Setter Property="ItemSize" Value="16" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Input:SfRating">
                    <Grid x:Name="PART_ToolTip">
                        <Grid.ToolTip>
                            <ToolTip
                                x:Name="PART_Popup"
                                BorderThickness="2"
                                Style="{StaticResource ToolTipStyle}" />
                        </Grid.ToolTip>
                        <Border
                            x:Name="PART_Host"
                            Grid.Row="1"
                            Padding="0"
                            Margin="0,0,0,0"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                            <ItemsPresenter
                                Margin="{TemplateBinding Padding}"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <Input:RatingItemPanel Orientation="Horizontal" />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="ItemSize" Value="24" />
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinWidth}" />
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfRatingStyle}" TargetType="Input:SfRating" />

    <!--  Style for SfRatingItem  -->
    <Style x:Key="SyncfusionSfRatingItemStyle" TargetType="Input:SfRatingItem">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="UnratedFill" Value="Transparent" />
        <Setter Property="UnratedStroke" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="PointerOverFill" Value="{StaticResource ContentForeground}" />
        <Setter Property="PointerOverStroke" Value="{StaticResource ContentForeground}" />
        <Setter Property="RatedFill" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="RatedStroke" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Input:SfRatingItem">
                    <Grid
                        x:Name="PART_RatingGrid"
                        Margin="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}">
                        <Path
                            x:Name="Unrated"
                            VerticalAlignment="Stretch"
                            Fill="{TemplateBinding UnratedFill}"
                            Opacity="1"
                            Stretch="Uniform"
                            Stroke="{TemplateBinding UnratedStroke}"
                            StrokeLineJoin="Miter"
                            StrokeThickness="{TemplateBinding UnratedStrokeThickness}" >
                            <Path.Data>
                                <PathGeometry>M3.03906 12.2031L7 10.1172L10.9609 12.2031C10.8359 11.4635 10.7109 10.7292 10.5859 10C10.4661 9.26562 10.3385 8.52865 10.2031 7.78906L13.4141 4.66406L8.98438 4.02344C8.64583 3.35156 8.3125 2.68229 7.98438 2.01562C7.66146 1.34896 7.33333 0.679688 7 0.0078125C6.66667 0.679688 6.33594 1.34896 6.00781 2.01562C5.6849 2.68229 5.35417 3.35156 5.01562 4.02344L0.585938 4.66406L3.79688 7.78906C3.66146 8.52865 3.53125 9.26562 3.40625 10C3.28646 10.7292 3.16406 11.4635 3.03906 12.2031Z</PathGeometry>
                            </Path.Data>
                        </Path>
                        <Path
                            x:Name="MouseOverPath"
                            VerticalAlignment="Stretch"
                            Fill="{TemplateBinding PointerOverFill}"
                            Opacity="0"
                            Stretch="Uniform"
                            Stroke="{TemplateBinding PointerOverStroke}"
                            StrokeLineJoin="Miter"
                            StrokeThickness="{TemplateBinding PointerOverStrokeThickness}"
                            Visibility="{Binding Path=Precision, Converter={StaticResource VisibilityConverter}, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type Input:SfRating}}}" >
                            <Path.Data>
                                <PathGeometry>M3.03906 12.2031L7 10.1172L10.9609 12.2031C10.8359 11.4635 10.7109 10.7292 10.5859 10C10.4661 9.26562 10.3385 8.52865 10.2031 7.78906L13.4141 4.66406L8.98438 4.02344C8.64583 3.35156 8.3125 2.68229 7.98438 2.01562C7.66146 1.34896 7.33333 0.679688 7 0.0078125C6.66667 0.679688 6.33594 1.34896 6.00781 2.01562C5.6849 2.68229 5.35417 3.35156 5.01562 4.02344L0.585938 4.66406L3.79688 7.78906C3.66146 8.52865 3.53125 9.26562 3.40625 10C3.28646 10.7292 3.16406 11.4635 3.03906 12.2031Z</PathGeometry>
                            </Path.Data>
                        </Path>
                        <Path
                            x:Name="RatedPath"
                            VerticalAlignment="Stretch"
                            Fill="{TemplateBinding RatedFill}"
			    Opacity="1"
                            Stretch="Uniform"
                            Stroke="{TemplateBinding RatedStroke}"
                            StrokeLineJoin="Miter"
                            StrokeThickness="{TemplateBinding RatedStrokeThickness}" >
                            <Path.Data>
                                <PathGeometry>M3.03906 12.2031L7 10.1172L10.9609 12.2031C10.8359 11.4635 10.7109 10.7292 10.5859 10C10.4661 9.26562 10.3385 8.52865 10.2031 7.78906L13.4141 4.66406L8.98438 4.02344C8.64583 3.35156 8.3125 2.68229 7.98438 2.01562C7.66146 1.34896 7.33333 0.679688 7 0.0078125C6.66667 0.679688 6.33594 1.34896 6.00781 2.01562C5.6849 2.68229 5.35417 3.35156 5.01562 4.02344L0.585938 4.66406L3.79688 7.78906C3.66146 8.52865 3.53125 9.26562 3.40625 10C3.28646 10.7292 3.16406 11.4635 3.03906 12.2031Z</PathGeometry>
                            </Path.Data>
                        </Path>
                        <shared:LinearClipper x:Name="LinearClipper" IsTabStop="False" ExpandDirection="Right">
                            <Path
                                x:Name="MouseOverRatedPath"
                                VerticalAlignment="Stretch"
                                Fill="{TemplateBinding RatedFill}"
                                Stretch="Uniform"
                                Stroke="{TemplateBinding RatedStroke}"
                                StrokeLineJoin="Miter"
                                StrokeThickness="{TemplateBinding RatedStrokeThickness}" >
                                <Path.Data>
                                    <PathGeometry>M3.03906 12.2031L7 10.1172L10.9609 12.2031C10.8359 11.4635 10.7109 10.7292 10.5859 10C10.4661 9.26562 10.3385 8.52865 10.2031 7.78906L13.4141 4.66406L8.98438 4.02344C8.64583 3.35156 8.3125 2.68229 7.98438 2.01562C7.66146 1.34896 7.33333 0.679688 7 0.0078125C6.66667 0.679688 6.33594 1.34896 6.00781 2.01562C5.6849 2.68229 5.35417 3.35156 5.01562 4.02344L0.585938 4.66406L3.79688 7.78906C3.66146 8.52865 3.53125 9.26562 3.40625 10C3.28646 10.7292 3.16406 11.4635 3.03906 12.2031Z</PathGeometry>
                                </Path.Data>
                            </Path>
                        </shared:LinearClipper>
                        <shared:LinearClipper ExpandDirection="Right" IsTabStop="False" RatioVisible="{TemplateBinding InternalValue}">
                            <Path
                                x:Name="LinearCliperPath"
                                VerticalAlignment="Stretch"
                                Fill="{TemplateBinding RatedFill}"
                                Stretch="Uniform"
                                Stroke="{TemplateBinding RatedStroke}"
                                StrokeLineJoin="Miter"
                                StrokeThickness="{TemplateBinding RatedStrokeThickness}" >
                                <Path.Data>
                                    <PathGeometry>M3.03906 12.2031L7 10.1172L10.9609 12.2031C10.8359 11.4635 10.7109 10.7292 10.5859 10C10.4661 9.26562 10.3385 8.52865 10.2031 7.78906L13.4141 4.66406L8.98438 4.02344C8.64583 3.35156 8.3125 2.68229 7.98438 2.01562C7.66146 1.34896 7.33333 0.679688 7 0.0078125C6.66667 0.679688 6.33594 1.34896 6.00781 2.01562C5.6849 2.68229 5.35417 3.35156 5.01562 4.02344L0.585938 4.66406L3.79688 7.78906C3.66146 8.52865 3.53125 9.26562 3.40625 10C3.28646 10.7292 3.16406 11.4635 3.03906 12.2031Z</PathGeometry>
                                </Path.Data>
                            </Path>
                        </shared:LinearClipper>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <DoubleAnimation
                                            Storyboard.TargetName="Unrated"
                                            Storyboard.TargetProperty="(UIElement.Opacity)"
                                            To="0"
                                            Duration="0:0:0" />
                                        <DoubleAnimation
                                            Storyboard.TargetName="RatedPath"
                                            Storyboard.TargetProperty="(UIElement.Opacity)"
                                            To="0"
                                            Duration="0:0:0" />
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="Rated">
                                    <Storyboard>
                                        <DoubleAnimation
                                            Storyboard.TargetName="RatedPath"
                                            Storyboard.TargetProperty="(UIElement.Opacity)"
                                            To="1"
                                            Duration="0:0:0" />
                                        <DoubleAnimation
                                            Storyboard.TargetName="Unrated"
                                            Storyboard.TargetProperty="(UIElement.Opacity)"
                                            To="1"
                                            Duration="0:0:0" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="LinearCliperPath" Property="Fill" Value="{StaticResource BorderAlt4}" />
                            <Setter TargetName="LinearCliperPath" Property="Stroke" Value="{StaticResource BorderAlt4}" />
                            <Setter TargetName="MouseOverRatedPath" Property="Fill" Value="{StaticResource BorderAlt4}" />
                            <Setter TargetName="MouseOverRatedPath" Property="Stroke" Value="{StaticResource BorderAlt4}" />
                            <Setter TargetName="RatedPath" Property="Fill" Value="{StaticResource BorderAlt4}" />
                            <Setter TargetName="RatedPath" Property="Stroke" Value="{StaticResource BorderAlt4}" />
                            <Setter TargetName="Unrated" Property="Fill" Value="Transparent" />
                            <Setter TargetName="Unrated" Property="Stroke" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinSize}" />
                <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfRatingItemStyle}" TargetType="Input:SfRatingItem" />

</ResourceDictionary>
