<UserControl x:Class="AirMonitor.LicenseGenerator.Views.TemplateManagerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AirMonitor.LicenseGenerator.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <Grid Margin="{StaticResource LargePadding}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="20"/>
            <ColumnDefinition Width="2*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧：模板列表 -->
        <GroupBox Grid.Column="0" Header="模板列表" Style="{StaticResource ModernGroupBoxStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 工具栏 -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,8">
                    <Button Content="新建" Command="{Binding NewTemplateCommand}" 
                            Style="{StaticResource PrimaryButtonStyle}"/>
                    <Button Content="导入" Command="{Binding ImportTemplateCommand}" 
                            Style="{StaticResource SecondaryButtonStyle}"/>
                </StackPanel>

                <!-- 模板列表 -->
                <ListBox Grid.Row="1" 
                         ItemsSource="{Binding Templates}" 
                         SelectedItem="{Binding SelectedTemplate}"
                         Background="White"
                         BorderBrush="{StaticResource BorderBrush}"
                         BorderThickness="1">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Margin="8">
                                <TextBlock Text="{Binding TemplateName}" FontWeight="Medium" FontSize="14"/>
                                <TextBlock Text="{Binding Description}" 
                                           FontSize="10" 
                                           Foreground="{StaticResource TextSecondaryBrush}"
                                           TextWrapping="Wrap"
                                           Margin="0,2,0,0"/>
                                <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                    <TextBlock Text="{Binding LicenseType}" 
                                               FontSize="10" 
                                               Background="{StaticResource PrimaryBrush}"
                                               Foreground="White"
                                               Padding="4,2"
                                               Margin="0,0,4,0"/>
                                    <TextBlock Text="{Binding ValidityDays, Converter={x:Static local:ValidityDaysToTextConverter.Instance}}" 
                                               FontSize="10" 
                                               Background="{StaticResource SecondaryBrush}"
                                               Foreground="White"
                                               Padding="4,2"/>
                                </StackPanel>
                            </StackPanel>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>

                <!-- 操作按钮 -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,8,0,0">
                    <Button Content="编辑" Command="{Binding EditTemplateCommand}" 
                            Style="{StaticResource SecondaryButtonStyle}"/>
                    <Button Content="删除" Command="{Binding DeleteTemplateCommand}" 
                            Style="{StaticResource SecondaryButtonStyle}"/>
                    <Button Content="导出" Command="{Binding ExportTemplateCommand}" 
                            Style="{StaticResource SecondaryButtonStyle}"/>
                </StackPanel>
            </Grid>
        </GroupBox>

        <!-- 右侧：模板编辑 -->
        <GroupBox Grid.Column="2" Header="模板编辑" Style="{StaticResource ModernGroupBoxStyle}">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- 基本信息 -->
                    <GroupBox Header="基本信息" Style="{StaticResource ModernGroupBoxStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Label Grid.Row="0" Grid.Column="0" Content="模板名称:" Style="{StaticResource ModernLabelStyle}"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding TemplateName}" Style="{StaticResource ModernTextBoxStyle}"/>

                            <Label Grid.Row="1" Grid.Column="0" Content="模板描述:" Style="{StaticResource ModernLabelStyle}" VerticalAlignment="Top"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding TemplateDescription}" 
                                     Style="{StaticResource ModernTextBoxStyle}"
                                     Height="80" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                        </Grid>
                    </GroupBox>

                    <!-- 许可证配置 -->
                    <GroupBox Header="许可证配置" Style="{StaticResource ModernGroupBoxStyle}">
                        <StackPanel>
                            <TextBlock Text="许可证类型:" FontWeight="Medium" Margin="0,0,0,4"/>
                            <ComboBox SelectedValue="{Binding SelectedTemplate.LicenseType}" 
                                      SelectedValuePath="Type"
                                      DisplayMemberPath="Name"
                                      Style="{StaticResource {x:Type ComboBox}}"
                                      Margin="0,0,0,16">
                                <ComboBox.Items>
                                    <ComboBoxItem Content="普通版" Tag="Standard"/>
                                    <ComboBoxItem Content="售后版" Tag="AfterSales"/>
                                    <ComboBoxItem Content="研发版" Tag="Development"/>
                                    <ComboBoxItem Content="管理版" Tag="Management"/>
                                </ComboBox.Items>
                            </ComboBox>

                            <TextBlock Text="有效期设置:" FontWeight="Medium" Margin="0,0,0,4"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <CheckBox Grid.Column="0" Content="永久许可证" 
                                          IsChecked="{Binding SelectedTemplate.ValidityDays, Converter={x:Static local:ValidityDaysToBooleanConverter.Instance}}"
                                          Margin="0,0,16,0"/>
                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <TextBlock Text="有效期天数:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBox Text="{Binding SelectedTemplate.ValidityDays}" 
                                             Style="{StaticResource ModernTextBoxStyle}"
                                             Width="100"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </GroupBox>

                    <!-- 默认客户信息 -->
                    <GroupBox Header="默认客户信息" Style="{StaticResource ModernGroupBoxStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Label Grid.Row="0" Grid.Column="0" Content="默认客户名称:" Style="{StaticResource ModernLabelStyle}"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding SelectedTemplate.DefaultCustomerName}" Style="{StaticResource ModernTextBoxStyle}"/>

                            <Label Grid.Row="1" Grid.Column="0" Content="默认客户邮箱:" Style="{StaticResource ModernLabelStyle}"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding SelectedTemplate.DefaultCustomerEmail}" Style="{StaticResource ModernTextBoxStyle}"/>
                        </Grid>
                    </GroupBox>

                    <!-- 功能配置 -->
                    <GroupBox Header="功能配置" Style="{StaticResource ModernGroupBoxStyle}">
                        <ScrollViewer MaxHeight="200" VerticalScrollBarVisibility="Auto">
                            <StackPanel>
                                <TextBlock Text="授权功能列表:" FontWeight="Medium" Margin="0,0,0,8"/>
                                <!-- TODO: 这里需要绑定到模板的授权功能列表 -->
                                <TextBlock Text="功能配置界面待完善" 
                                           Foreground="{StaticResource TextSecondaryBrush}"
                                           HorizontalAlignment="Center"
                                           Margin="0,20"/>
                            </StackPanel>
                        </ScrollViewer>
                    </GroupBox>

                    <!-- 保存按钮 -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,16,0,0">
                        <Button Content="保存模板" Command="{Binding SaveTemplateCommand}" 
                                Style="{StaticResource PrimaryButtonStyle}"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </GroupBox>
    </Grid>
</UserControl>
