<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Sync_Resources="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:conv="clr-namespace:Syncfusion.Windows.Tools;assembly=Syncfusion.Shared.WPF"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Shared.WPF;component/SkinManager/MetroThemeBrushes.xaml" />
        <ResourceDictionary Source="/Syncfusion.Shared.WPF;component/Controls/ColorPicker/Themes/NullBrush.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/ColorEdit/ColorEdit.xaml" />
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <conv:ValueToStringConverter x:Key="FloatToString" />
    <conv:ColorToStringConverter x:Key="ColorToString" />
    <conv:ColorToValueConverter x:Key="ColorToValue" />
    <conv:ColorToBrushConverter x:Key="ColorToBrush" />
    <conv:GradientBrushToVisibilityConverter x:Key="BrushToVisibility" />
    <conv:ColorToWordKnownColorsConverter x:Key="ColorToWordKnownColors" />
    <conv:RGBToStringConverter x:Key="RGBToString" />
    <conv:RangedFloatToStringConverter x:Key="RangedFloatToString" />
    <conv:BrushToTextConverter x:Key="brushToTextConverter" />
    <shared:ColorItemList x:Key="ColorList" />
    <conv:DoubleToPointConverter x:Key="DoubleToPointConverter" />

    <SolidColorBrush x:Key="ColorPicker.Focused.Border" Color="#0279ff" />
    <SolidColorBrush x:Key="ColorPicker.MouseOver.Border" Color="#b1b1b1" />
    <SolidColorBrush x:Key="ColorPicker.Pressed.Border" Color="#3d98ff" />

    <LinearGradientBrush x:Key="ColorPickerBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ColorPickerBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ColorPickerBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="ToggleDropDownButtonStyle" TargetType="ToggleButton">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Border
                        Background="Transparent"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0"
                        SnapsToDevicePixels="True">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <ContentPresenter
                                Grid.Column="0"
                                HorizontalAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding Control.VerticalContentAlignment}"
                                Content="{TemplateBinding ContentControl.Content}"
                                ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}"
                                RecognizesAccessKey="True"
                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}" />

                            <Border
                                x:Name="MainBorder"
                                Grid.Column="1"
                                Margin="0,0,5,0"
                                SnapsToDevicePixels="True">
                                <Viewbox Width="9" Height="7">
                                    <Path
                                        x:Name="Arrow"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Fill="Transparent"
                                        Stroke="{StaticResource IconColor}"
                                        StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}">
                                        <Path.LayoutTransform>
                                            <RotateTransform Angle="0" />
                                        </Path.LayoutTransform>
                                        <Path.Data>
                                            <PathGeometry>M1 0.5L5 4.5L9 0.5</PathGeometry>
                                        </Path.Data>
                                    </Path>
                                </Viewbox>
                            </Border>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Arrow" Property="Stroke" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="Arrow" Property="Stroke" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="Arrow" Property="Stroke" Value="{StaticResource IconColorSelected}" />
                            <Setter TargetName="Arrow" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="180" />
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Arrow" Property="Stroke" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <DataTemplate x:Key="ColorItemTemplate">
        <StackPanel
            Name="colorName"
            Orientation="Horizontal"
            Style="{StaticResource colorNameStyle}">
            <Border
                Width="20"
                Height="12"
                Margin="1,1,3,1"
                Background="{Binding Path=Brush}"
                BorderBrush="Black"
                BorderThickness="1" />
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Text="{Binding Path=Name}" />
        </StackPanel>
    </DataTemplate>

    <DataTemplate x:Key="defaultHeaderTemplate" DataType="shared:ColorPicker">
        <StackPanel Orientation="Horizontal">
            <Border
                Name="selectedColorRect"
                Width="20"
                Height="18"
                Margin="2"
                HorizontalAlignment="Left"
                Background="{Binding Brush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:ColorPicker}}}"
                CornerRadius="{StaticResource Windows11Light.CornerRadius4}"/>
            <TextBlock
                Name="SelectedColorText"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Foreground="{StaticResource ContentForeground}"
                Margin="4,0,0,0">
                <TextBlock.Text>
                    <MultiBinding Converter="{StaticResource brushToTextConverter}">
                        <Binding Path="Brush" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type shared:ColorPicker}}" />
                        <Binding Path="Color" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type shared:ColorPicker}}" />
                        <Binding Path="GradientBrushDisplayMode" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type shared:ColorPicker}}" />
                    </MultiBinding>
                </TextBlock.Text>
            </TextBlock>
        </StackPanel>
        <DataTemplate.Triggers>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding BrushMode, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:ColorPicker}}}" Value="Solid" />
                    <Condition Binding="{Binding Color.A, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:ColorPicker}}}" Value="0" />
                </MultiDataTrigger.Conditions>
                <MultiDataTrigger.Setters>
                    <Setter TargetName="selectedColorRect" Property="Background" Value="{StaticResource TransparentBrush}" />
                </MultiDataTrigger.Setters>
            </MultiDataTrigger>
            <DataTrigger Binding="{Binding Brush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:ColorPicker}}}" Value="{x:Null}">
                <Setter TargetName="selectedColorRect" Property="Background" Value="{StaticResource NullBrush}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding IsEnabled, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:ColorPicker}}}" Value="False">
                <Setter TargetName="SelectedColorText" Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <!--  SyncfusionColorPickerStyle  -->
    <Style x:Key="SyncfusionColorPickerStyle" TargetType="{x:Type shared:ColorPicker}">
        <Setter Property="Padding" Value="2" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="BorderBrush" Value="{StaticResource ColorPickerBorderBrush}" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="ColorEditBackground" Value="{StaticResource PopupBackground}" />
        <Setter Property="HeaderTemplate" Value="{StaticResource defaultHeaderTemplate}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="MinHeight" Value="24" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:ColorPicker}">
                    <Border
                        Name="mainBorder"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        FocusVisualStyle="{TemplateBinding FocusVisualStyle}"
                        SnapsToDevicePixels="True"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius4}">
                        <Grid Margin="{TemplateBinding Padding}">
                            <ToggleButton
                                Name="colorToggleButton"
                                Grid.Column="2"
                                ClickMode="Press"
                                Command="shared:ColorPicker.M_displayPopup"
                                ContentTemplate="{TemplateBinding HeaderTemplate}"
                                FocusVisualStyle="{TemplateBinding FocusVisualStyle}"
                                Style="{StaticResource ToggleDropDownButtonStyle}" />
                            <Popup
                                Name="colorEditPopup"
                                MinWidth="300"
                                AllowsTransparency="True"
                                IsOpen="{Binding ElementName=colorToggleButton, Path=IsChecked}"
                                PlacementTarget="{Binding ElementName=colorToggleButton}"
                                PopupAnimation="Slide"
                                StaysOpen="False">
                                <Border Padding="7">
                                    <Border.Effect>
                                        <DropShadowEffect BlurRadius="8" Direction="270" Opacity=".42" ShadowDepth="1.5" />
                                    </Border.Effect>
                                    <StackPanel Background="Transparent">
                                        <shared:ColorEdit Name="ColorEdit" VisualizationStyle="{TemplateBinding VisualizationStyle}" />
                                        <Border
                                            Name="systemColorsPanel"
                                            DataContext="{DynamicResource ColorList}"
                                            Visibility="Collapsed">
                                            <ComboBox
                                                Name="systemColors"
                                                Margin="4,2,4,4"
                                                DockPanel.Dock="Left"
                                                IsSynchronizedWithCurrentItem="False"
                                                ItemTemplate="{StaticResource ColorItemTemplate}"
                                                ItemsSource="{Binding}" />
                                        </Border>
                                    </StackPanel>
                                </Border>
                            </Popup>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="mainBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                            <Setter TargetName="mainBorder" Property="BorderBrush" Value="{StaticResource ColorPickerBorderBrushHovered}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocusWithin" Value="True">
                            <Setter TargetName="mainBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="mainBorder" Property="BorderBrush" Value="{StaticResource ColorPickerBorderBrushFocused}" />
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}" />
                            <Setter Property="Padding" Value="2,2,2,1"/>
                        </Trigger>
                        <Trigger SourceName="colorToggleButton" Property="IsChecked" Value="True">
                            <Setter TargetName="mainBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                            <Setter TargetName="mainBorder" Property="BorderBrush" Value="{StaticResource ColorPickerBorderBrushFocused}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}" />
                        </Trigger>
                        <DataTrigger Binding="{Binding Path=IsColorPaletteVisible, RelativeSource={RelativeSource Self}}" Value="True">
                            <Setter TargetName="ColorEdit" Property="BorderThickness" Value="1,1,1,0" />
                            <Setter TargetName="systemColorsPanel" Property="BorderBrush" Value="{Binding ElementName=ColorEdit, Path=BorderBrush}" />
                            <Setter TargetName="systemColorsPanel" Property="Visibility" Value="Visible" />
                            <Setter TargetName="systemColorsPanel" Property="BorderThickness" Value="1,0,1,1" />
                        </DataTrigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="colorEditPopup" Property="MinWidth" Value="370" />
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="mainBorder" Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="mainBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                        <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                            <Setter Property="FocusVisualStyle" Value="{StaticResource CurveKeyboardFocusVisualStyle}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionColorPickerStyle}" TargetType="{x:Type shared:ColorPicker}" />
</ResourceDictionary>
