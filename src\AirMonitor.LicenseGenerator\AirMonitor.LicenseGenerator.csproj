<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
    <PackageReference Include="Syncfusion.Shared.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.Themes.Windows11Dark.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.Themes.Windows11Light.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.Tools.WPF" Version="29.2.9" />
      <PackageReference Include="Syncfusion.SfGrid.WPF" Version="29.2.9"/>
    <PackageReference Include="Syncfusion.SfSkinManager.WPF" Version="29.2.9"/>
    <PackageReference Include="Syncfusion.SfTextInputLayout.WPF" Version="29.2.9"/>
</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AirMonitor.Core\AirMonitor.Core.csproj" />
  </ItemGroup>

</Project>
