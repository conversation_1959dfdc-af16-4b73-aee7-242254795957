<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>Resources\Images\license-generator.ico</ApplicationIcon>
    <AssemblyTitle>AirMonitor License Generator</AssemblyTitle>
    <AssemblyDescription>AirMonitor许可证生成器</AssemblyDescription>
    <AssemblyCompany>AirMonitor Technologies</AssemblyCompany>
    <AssemblyProduct>AirMonitor License Generator</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024 AirMonitor Technologies. All rights reserved.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <!-- 项目引用 -->
    <ProjectReference Include="..\AirMonitor.Core\AirMonitor.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Syncfusion WPF Controls -->
    <PackageReference Include="Syncfusion.SfInput.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.SfGrid.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.SfChart.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.Themes.Windows11Dark.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.Themes.Windows11Light.WPF" Version="29.2.9" />

    <!-- MVVM框架 -->
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />

    <!-- 依赖注入 -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />

    <!-- 日志 -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.5" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />

    <!-- Excel操作 -->
    <PackageReference Include="EPPlus" Version="7.5.2" />

    <!-- 加密 -->
    <PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />

    <!-- 系统管理 -->
    <PackageReference Include="System.Management" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <!-- 资源文件 -->
    <Resource Include="Resources\**\*" />
  </ItemGroup>

  <ItemGroup>
    <!-- 配置文件 -->
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
