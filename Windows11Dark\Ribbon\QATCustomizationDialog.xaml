<ResourceDictionary 
       xmlns:system="clr-namespace:System;assembly=mscorlib"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:Microsoft_Windows_Themes="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
        xmlns:Sync_Resources="clr-namespace:Syncfusion.Windows.Tools.Controls.Resources;assembly=Syncfusion.Tools.WPF"
        xmlns:local="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
        xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
        xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
        x:Name="QATwindow" 
       >
    <ResourceDictionary.MergedDictionaries>

        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/TreeViewAdv/TreeViewAdv.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/ChromelessWindow/ChromelessWindow.xaml" /> 
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/DropDownButtonAdv/DropDownButtonAdv.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Button.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TreeView.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/PrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBlock.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ListBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Label.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ComboBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Menu.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/RepeatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/CheckBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ScrollViewer.xaml" />

    </ResourceDictionary.MergedDictionaries>
    <Thickness x:Key="QATBorderThickness">0</Thickness>
    <Thickness x:Key="QATOuterBorderThickness">1</Thickness>
    <SolidColorBrush x:Key="RibbonQATBorderBrush" Color="{StaticResource BorderAlt.Color}"/>
    <SolidColorBrush x:Key="RibbonWindowBackground" Color="{StaticResource ContentBackground.Color}" />
    <SolidColorBrush x:Key="QATWindowBackground" Color="{StaticResource ContentBackground.Color}" />
    <SolidColorBrush x:Key="QATOuterPathFill" Color="{StaticResource IconColor.Color}"/>
    <SolidColorBrush x:Key="QATInnerPathFill" Color="Transparent"/>
    <system:String x:Key="id">Id</system:String>
    <system:String x:Key="header">Header</system:String>
    <system:String x:Key="source">Source</system:String>
    <system:String x:Key="destination">Destination</system:String>
    
    <Thickness x:Key="Ribbon.QATDropDownButton.Popup.Border.Static.Margin">1</Thickness>
    <Thickness x:Key="Ribbon.QATDropDownButton.Popup.Border.Static.Padding">1</Thickness>

    <DataTemplate x:Key="ListBoxButtonItemTemplate">
            <StackPanel Margin="2" Orientation="Horizontal">
                <Grid>
                    <ContentPresenter Name="IconContent" ContentTemplateSelector="{Binding IconTemplateSelector}" ContentTemplate="{Binding IconTemplate}" Content="{Binding }" Height="16" Width="16" Margin="0,0,10,0"/>
                </Grid>
                <TextBlock Text="{Binding Path=Label}" />
            </StackPanel>
    </DataTemplate>

    <DataTemplate x:Key="ListBoxItemDataTemplate">
        <StackPanel Margin="2" Orientation="Horizontal">
            <Grid>
                <Viewbox
                        x:Name="VectorImage"
                        Width="16"
                        Height="16"
                        Margin="0,0,10,0">
                    <Grid x:Name="Path_Grid" />
                </Viewbox>
                <Image
                        x:Name="PART_Image"
                        Width="16"
                        Height="16"
                        Margin="0,0,10,0"
                        Source="{Binding Path=SmallIcon}"
                        Stretch="Fill" />
            </Grid>
            <TextBlock Text="{Binding Path=Label}" />
        </StackPanel>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=IconType}" Value="VectorImage">
                <DataTrigger.Setters>
                    <Setter TargetName="PART_Image" Property="Visibility" Value="Collapsed" />
                    <Setter TargetName="VectorImage" Property="Visibility" Value="Visible" />
                </DataTrigger.Setters>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=IconType}" Value="Icon">
                <DataTrigger.Setters>
                    <Setter TargetName="PART_Image" Property="Visibility" Value="Visible" />
                    <Setter TargetName="VectorImage" Property="Visibility" Value="Collapsed" />
                </DataTrigger.Setters>
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <DataTemplate x:Key="SourceListBoxItemDataTemplate">
        <StackPanel Margin="2" Orientation="Horizontal">
            <Grid>
                <Viewbox
                        x:Name="VectorImage"
                        Width="16"
                        Height="16"
                        Margin="0,0,10,0">
                    <Grid x:Name="Path_Grid" />
                </Viewbox>
                <Image
                        x:Name="PART_Image"
                        Width="16"
                        Height="16"
                        Margin="0,0,10,0"
                        Source="{Binding Path=SmallIcon}"
                        Stretch="Fill" />
            </Grid>
            <TextBlock Text="{Binding Path=Label}" />
        </StackPanel>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=IconType}" Value="VectorImage">
                <DataTrigger.Setters>
                    <Setter TargetName="PART_Image" Property="Visibility" Value="Collapsed" />
                    <Setter TargetName="VectorImage" Property="Visibility" Value="Visible" />
                </DataTrigger.Setters>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=IconType}" Value="Icon">
                <DataTrigger.Setters>
                    <Setter TargetName="PART_Image" Property="Visibility" Value="Visible" />
                    <Setter TargetName="VectorImage" Property="Visibility" Value="Collapsed" />
                </DataTrigger.Setters>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=CanVisibleImage}" Value="true">
                <DataTrigger.Setters>
                    <Setter Property="Visibility" Value="Hidden" TargetName="PART_Image" />
                </DataTrigger.Setters>
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <Style x:Key="SyncfusionQATRibbonMenuItemStyle"
            BasedOn="{StaticResource WPFMenuItemStyle}"
            TargetType="{x:Type local:RibbonMenuItem}" >
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource PopupHoveredBackground}"/>
                <Setter Property="BorderBrush" Value="{StaticResource PopupHoveredBackground}"/>
                <Setter Property="Foreground" Value="{StaticResource PopupHoveredForeground}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionQATListBoxStyle" BasedOn="{StaticResource WPFListBoxStyle}" TargetType="{x:Type local:QATListBox}">
        <Setter Property="BorderThickness" Value="1" />
    </Style>

    <Style x:Key="SyncfusionQATListBoxItemStyle" BasedOn="{StaticResource WPFListBoxItemStyle}" TargetType="local:QATListBoxItem" >
        <Setter Property="IconTemplate" Value="{Binding IconTemplate}" />
		<Setter Property="IconTemplateSelector" Value="{Binding IconTemplateSelector}" />		
		<Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Padding" Value="2,0,0,0" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="ContentTemplate" Value="{StaticResource ListBoxButtonItemTemplate}" />
        <Setter Property="VectorImage" Value="{Binding VectorImage}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:QATListBoxItem">
                    <Border
                            x:Name="Bd"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="0"
                            SnapsToDevicePixels="true">
                        <ContentPresenter
                                x:Name="contentPresenter"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                ContentTemplate="{TemplateBinding ContentTemplate}"
								ContentTemplateSelector="{TemplateBinding IconTemplateSelector}"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                    </Border>
                    <ControlTemplate.Triggers>
						<MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IconTemplate" Value="{x:Null}" />
                                <Condition Property="IconTemplateSelector" Value="{x:Null}" />
                            </MultiTrigger.Conditions>
                            <Setter Property="ContentTemplate" TargetName="contentPresenter" Value="{DynamicResource ListBoxItemDataTemplate}" />
                        </MultiTrigger>
						<Trigger Property="IsSelected" Value="true">
                            <Setter TargetName="Bd" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="Bd" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="TextElement.Foreground" Value="{StaticResource HoveredForeground}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="true" />
                                <Condition Property="Selector.IsSelectionActive" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="Bd" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="Bd" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Opacity" Value="0.5" />
                        </Trigger>

                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionQATSourceListBoxItemStyle" BasedOn="{StaticResource SyncfusionQATListBoxItemStyle}" TargetType="local:QATListBoxItem">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:QATListBoxItem">
                    <Border
                            x:Name="Bd"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="0"
                            SnapsToDevicePixels="true">
                        <ContentPresenter
                                x:Name="contentPresenter"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                ContentTemplate="{TemplateBinding ContentTemplate}"
								ContentTemplateSelector="{TemplateBinding IconTemplateSelector}"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IconTemplate" Value="{x:Null}" />
                                <Condition Property="IconTemplateSelector" Value="{x:Null}" />
                            </MultiTrigger.Conditions>
                            <Setter Property="ContentTemplate" TargetName="contentPresenter" Value="{DynamicResource SourceListBoxItemDataTemplate}" />
                        </MultiTrigger>
                        <Trigger Property="IsSelected" Value="true">
                            <Setter TargetName="Bd" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="Bd" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="TextElement.Foreground" Value="{StaticResource HoveredForeground}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="true" />
                                <Condition Property="Selector.IsSelectionActive" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="Bd" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="Bd" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Opacity" Value="0.5" />
                        </Trigger>

                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionQATDropDownButtonStyle" TargetType="{x:Type local:DropDownButton}">
        <Setter Property="IconTemplate" Value="{Binding IconTemplate}" />
        <Setter Property="IconTemplateSelector" Value="{Binding IconTemplateSelector}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1}" />
        <Setter Property="Background" Value="{StaticResource SecondaryBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource DropDownButtonAdv.Static.Border}" />
        <Setter Property="Foreground" Value="{StaticResource SecondaryForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="MinHeight" Value="{StaticResource Windows11Dark.MinHeight}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:DropDownButton}">
                    <Border
                            Name="PART_ToggleButton"
                            Height="{TemplateBinding Height}"
                            MinHeight="{TemplateBinding MinHeight}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            
                            CornerRadius="{StaticResource Windows11Dark.ThemeCornerRadiusVariant1}"
                            Opacity="{TemplateBinding Opacity}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Rectangle
                                    Name="InnerPath"
                                    Grid.ColumnSpan="3"
                                    RadiusX="2"
                                    RadiusY="2" />
                            <Border
                                    Name="PART_ImageBorder"
                                    Grid.Column="0"
                                    Margin="2">
                                <ContentPresenter Name="IconContent" ContentTemplateSelector="{TemplateBinding IconTemplateSelector}" ContentTemplate="{TemplateBinding IconTemplate}" Content="{Binding }" Height="16" Width="16"/>
                            </Border>
                            <TextBlock
                                    x:Name="label"
                                    Grid.Column="1"
                                    Margin="0" 
                                    FontFamily="{TemplateBinding FontFamily}"
                                    HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                    VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                    FontSize="{TemplateBinding FontSize}"
                                    Foreground="{TemplateBinding Foreground}"
                                    Text="{TemplateBinding Label}" />
                            <Path
                                    x:Name="Arrow"
                                    Grid.Column="2"
                                    Width="8"
                                    Height="8"
                                    Margin="3"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="M454.165,177.507L422.165,212.46 390.165,177.507 454.165,177.507z"
                                    Fill="{StaticResource SecondaryForeground}"
                                    Stretch="Uniform" />
                            <Popup
                                    Name="PART_Popup"
                                    Margin="0,8,0,0"
                                    AllowsTransparency="True"
                                    Focusable="False"
                                    IsOpen="{Binding Path=IsDropDownOpen, RelativeSource={RelativeSource AncestorType={x:Type local:DropDownButton}}}"
                                    Placement="Bottom"
                                    PopupAnimation="Fade"
                                    StaysOpen="False">
                                <Border
                                            MinWidth="28"
                                            MinHeight="14"
                                            Margin="{StaticResource Ribbon.QATDropDownButton.Popup.Border.Static.Margin}"
                                            Background="Transparent"
                                            
                                            
                                            CornerRadius="{StaticResource Windows11Dark.ThemeCornerRadiusVariant1}"
                                            BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" 
                                            Padding="{StaticResource Ribbon.QATDropDownButton.Popup.Border.Static.Padding}">
                                    <ScrollViewer
                                                Margin="1,0,1,0"
                                                CanContentScroll="True"
                                                Style="{StaticResource WPFMenuScrollViewer}">
                                        <ItemsPresenter
                                                    Name="Presenter"
                                                    Margin="0,0,0,0"
                                                    Grid.IsSharedSizeScope="true"
                                                    KeyboardNavigation.DirectionalNavigation="Cycle"
                                                    KeyboardNavigation.TabNavigation="Cycle"
                                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IconTemplate" Value="{x:Null}"/>
                                <Condition Property="IconTemplateSelector" Value="{x:Null}"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="ContentTemplate" TargetName="IconContent" Value="{DynamicResource ListBoxItemDataTemplate}"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="PART_ToggleButton" Property="Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="Arrow" Property="Fill" Value="{StaticResource IconColorHovered}" />
                            <Setter TargetName="label" Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter TargetName="PART_ToggleButton" Property="BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="PART_ToggleButton" Property="Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                            <Setter TargetName="Arrow" Property="Fill" Value="{StaticResource IconColorSelected}" />
                            <Setter TargetName="label" Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter TargetName="PART_ToggleButton" Property="BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                        </Trigger>
                        <Trigger Property="IsDropDownOpen" Value="True">
                            <Setter TargetName="PART_ToggleButton" Property="Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                            <Setter TargetName="Arrow" Property="Fill" Value="{StaticResource IconColorSelected}" />
                            <Setter TargetName="label" Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter TargetName="PART_ToggleButton" Property="BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ExpandCollapseToggleStyle"
            BasedOn="{StaticResource WPFToggleButtonStyle}"
            TargetType="ToggleButton">
        <Setter Property="Focusable" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Grid
                            Width="15"
                            Height="13"
                            Background="Transparent">
                        <Border BorderBrush="Gray" BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" CornerRadius="{StaticResource Windows11Dark.CornerRadius2}">
                            <Viewbox Width="6" Height="6">
                                <Path
                                        x:Name="ExpandPath"
                                        Width="4"
                                        Height="4"
                                        Margin="0,0,0,0"
                                        Data="M19.833,0L32.501,0 32.501,19.833999 52.334,19.833999 52.334,32.500999 32.501,32.500999 32.501,52.333 19.833,52.333 19.833,32.500999 0,32.500999 0,19.833999 19.833,19.833999z"
                                        RenderTransformOrigin="0.5,0.5"
                                        Fill="{StaticResource ContentForeground}"
                                        Stretch="Uniform">
                                    <Path.RenderTransform>
                                        <TransformGroup>
                                            <TransformGroup.Children>
                                                <RotateTransform Angle="0" />
                                                <ScaleTransform ScaleX="1" ScaleY="1" />
                                            </TransformGroup.Children>
                                        </TransformGroup>
                                    </Path.RenderTransform>
                                </Path>
                            </Viewbox>

                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ExpandPath" Property="Data" Value="M0,0L53.333,0 53.333,8.888 0,8.888z" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ExpandPath" Property="Fill" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <Style x:Key="TreeViewItemFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border>
                        <Rectangle
                                Margin="0,0,0,0"
                                Opacity="0"
                                Stroke="Black"
                                StrokeDashArray="1 2"
                                StrokeThickness="5" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionQATTreeViewItemStyle" TargetType="local:QATTreeViewItem">
       <Setter Property="Background"      Value="Transparent"/>
        <Setter Property="HorizontalContentAlignment" Value="{Binding Path=HorizontalContentAlignment,RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"/>
        <Setter Property="VerticalContentAlignment" Value="{Binding Path=VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"/>
        <Setter Property="Padding"     Value="1,0,0,0"/>
        <Setter Property="Foreground"   Value="{StaticResource ContentForeground}"/>
        <Setter Property="FocusVisualStyle" Value="{StaticResource TreeViewItemFocusVisual}"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:QATTreeViewItem">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition MinWidth="19" Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition/>
                        </Grid.RowDefinitions>
                        <ToggleButton x:Name="Expander"
                  Style="{StaticResource ExpandCollapseToggleStyle}"
                  IsChecked="{Binding Path=IsExpanded,
                              RelativeSource={RelativeSource TemplatedParent}}"
                  ClickMode="Press"/>
                        <Border Name="Bd"
              Grid.Column="1" 
              Background="{TemplateBinding Background}"
              BorderBrush="{TemplateBinding BorderBrush}"
              BorderThickness="{TemplateBinding BorderThickness}"
              Padding="{TemplateBinding Padding}">
                            <ContentPresenter x:Name="PART_Header"
                      ContentSource="Header"
                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"/>
                        </Border>
                        <ItemsPresenter x:Name="ItemsHost"
                  Grid.Row="1"
                  Grid.Column="1"
                  Grid.ColumnSpan="2"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsExpanded" Value="false">
                            <Setter TargetName="ItemsHost" Property="Visibility" Value="Collapsed"/>
                        </Trigger>
                        <Trigger Property="HasItems" Value="false">
                            <Setter TargetName="Expander" Property="Visibility" Value="Hidden"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="HasHeader" Value="false"/>
                                <Condition Property="Width" Value="Auto"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Header" Property="MinWidth" Value="75"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="HasHeader" Value="false"/>
                                <Condition Property="Height" Value="Auto"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Header" Property="MinHeight" Value="19"/>
                        </MultiTrigger>
                        <Trigger Property="IsSelected" Value="true">
                            <Setter TargetName="Bd" Property="Background" Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="Foreground" Value="{StaticResource SelectedForeground}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="true"/>
                                <Condition Property="IsSelectionActive" Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="Bd" Property="Background" Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="Foreground" Value="{StaticResource SelectedForeground}"/>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                            <Setter Property="Background" Value="Transparent"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    
    <Style x:Key="PathStyle" TargetType="Path">
        <Style.Triggers>
            <DataTrigger Binding="{Binding Path=IsEnabled, Mode=OneWay, RelativeSource={RelativeSource AncestorType={x:Type Button}}}" Value="False">
                <Setter Property="Shape.Fill" Value="{StaticResource SecondaryForegroundDisabled}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=IsEnabled, Mode=OneWay, RelativeSource={RelativeSource AncestorType={x:Type Button}}}" Value="True">
                <Setter Property="Shape.Fill" Value="{StaticResource SecondaryForeground}" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="QATButtonStyle"
            BasedOn="{StaticResource WPFButtonStyle}"
            TargetType="{x:Type Button}" >
    </Style>

    <Style x:Key="QATPrimaryButtonStyle"
            BasedOn="{StaticResource WPFPrimaryButtonStyle}"
            TargetType="{x:Type Button}" >
    </Style>

    <Style x:Key="SyncfusionQATTreeViewStyle"
            BasedOn="{StaticResource WPFTreeViewStyle}"
            TargetType="{x:Type local:QATTreeView}" >
        <Setter Property="BorderThickness" Value="1"/>
    </Style>
</ResourceDictionary>
