 <ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:vsm="clr-namespace:Syncfusion.Windows;assembly=Syncfusion.Shared.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatButton.xaml" />
    </ResourceDictionary.MergedDictionaries>
    
    <!--  Top Scroll Button Style  -->
    <Style x:Key="SyncfusionTopScrollButtonStyle" BasedOn="{StaticResource WPFFlatButtonStyle}" TargetType="{x:Type Button}">
        <Setter Property="BorderThickness" Value="0.5"/>
        <Setter Property="Content">
            <Setter.Value>
                <Path HorizontalAlignment="Center"
                      VerticalAlignment="Center"                     
                      Fill="{StaticResource PopupForeground}"
                      RenderTransformOrigin="0.5,0.5">
                    <Path.Data>
                        <PathGeometry>M0 1C0 0.864583 0.0260417 0.735677 0.078125 0.613281C0.130208 0.490885 0.200521 0.385417 0.289062 0.296875C0.380208 0.205729 0.485677 0.134115 0.605469 0.0820312C0.72526 0.0273438 0.854167 0 0.992188 0H7.01172C7.14714 0 7.27474 0.0260417 7.39453 0.078125C7.51693 0.130208 7.6224 0.201823 7.71094 0.292969C7.79948 0.384115 7.86979 0.490885 7.92188 0.613281C7.97396 0.733073 8 0.860677 8 0.996094C8 1.10547 7.98438 1.20573 7.95312 1.29688C7.92448 1.38802 7.88021 1.47917 7.82031 1.57031L5.21875 5.35547C5.08073 5.55599 4.90365 5.71354 4.6875 5.82812C4.47396 5.94271 4.24479 6 4 6C3.75521 6 3.52474 5.94271 3.30859 5.82812C3.09505 5.71354 2.91927 5.55599 2.78125 5.35547L0.179688 1.57031C0.119792 1.48177 0.0742188 1.39193 0.0429688 1.30078C0.0143229 1.20964 0 1.10938 0 1Z</PathGeometry>
                    </Path.Data>
                    <Path.RenderTransform>
                        <TransformGroup>
                            <ScaleTransform />
                            <SkewTransform />
                            <RotateTransform Angle="180" />
                            <TranslateTransform />
                        </TransformGroup>
                    </Path.RenderTransform>
                </Path>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Bottom Scroll Button Style  -->
    <Style x:Key="SyncfusionBottomScrollButtonStyle" BasedOn="{StaticResource WPFFlatButtonStyle}" TargetType="{x:Type Button}">
        <Setter Property="BorderThickness" Value="0.5"/>
        <Setter Property="Content">
            <Setter.Value>
                <Path HorizontalAlignment="Center"
                      VerticalAlignment="Center"               
                      Fill="{StaticResource PopupForeground}">
                    <Path.Data>
                        <PathGeometry>M0 1C0 0.864583 0.0260417 0.735677 0.078125 0.613281C0.130208 0.490885 0.200521 0.385417 0.289062 0.296875C0.380208 0.205729 0.485677 0.134115 0.605469 0.0820312C0.72526 0.0273438 0.854167 0 0.992188 0H7.01172C7.14714 0 7.27474 0.0260417 7.39453 0.078125C7.51693 0.130208 7.6224 0.201823 7.71094 0.292969C7.79948 0.384115 7.86979 0.490885 7.92188 0.613281C7.97396 0.733073 8 0.860677 8 0.996094C8 1.10547 7.98438 1.20573 7.95312 1.29688C7.92448 1.38802 7.88021 1.47917 7.82031 1.57031L5.21875 5.35547C5.08073 5.55599 4.90365 5.71354 4.6875 5.82812C4.47396 5.94271 4.24479 6 4 6C3.75521 6 3.52474 5.94271 3.30859 5.82812C3.09505 5.71354 2.91927 5.55599 2.78125 5.35547L0.179688 1.57031C0.119792 1.48177 0.0742188 1.39193 0.0429688 1.30078C0.0143229 1.20964 0 1.10938 0 1Z</PathGeometry>
                    </Path.Data>
                </Path>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Check Box Style  -->
    <Style x:Key="SyncfusionMenuCheckBoxStyle" TargetType="CheckBox">
        <Setter Property="Margin" Value="3" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <Grid>
                        <Border
                            x:Name="checkBoxBorder"
                            Width="12"
                            Height="12"
                            Background="Transparent"
                            BorderBrush="Transparent"
                            BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                            CornerRadius="2">
                            <Path
                                x:Name="glyph"                                                            
                                Width="10"
                                Height="7"
                                Fill="{StaticResource IconColor}"
                                FlowDirection="LeftToRight" >
                                <Path.Data>
                                    <PathGeometry>M0.834961 3.41504C0.834961 3.30111 0.875651 3.20345 0.957031 3.12207C1.03841 3.04069 1.13607 3 1.25 3C1.36393 3 1.46159 3.04069 1.54297 3.12207L3.75 5.3291L8.45703 0.62207C8.53841 0.54069 8.63607 0.5 8.75 0.5C8.80859 0.5 8.8623 0.511393 8.91113 0.53418C8.96322 0.553711 9.00716 0.583008 9.04297 0.62207C9.08203 0.657878 9.11296 0.701823 9.13574 0.753906C9.15853 0.802734 9.16992 0.856445 9.16992 0.915039C9.16992 1.02897 9.1276 1.12826 9.04297 1.21289L4.04297 6.21289C3.96159 6.29427 3.86393 6.33496 3.75 6.33496C3.63607 6.33496 3.53841 6.29427 3.45703 6.21289L0.957031 3.71289C0.875651 3.63151 0.834961 3.53223 0.834961 3.41504Z</PathGeometry>
                                </Path.Data>
                            </Path>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="true">
                            <Setter TargetName="glyph" Property="Fill" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger Property="IsChecked" Value="true">
                            <Setter TargetName="glyph" Property="Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="glyph" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Radion BUtton Style  -->
    <Style x:Key="SyncfusionMenuRadioButtonStyle" TargetType="RadioButton">
        <Setter Property="Margin" Value="3" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="RadioButton">
                    <Grid>
                        <Border
                            x:Name="radioButtonBorder"
                            Width="20"
                            Height="20"
                            Background="{StaticResource PrimaryBackground}"
                            BorderBrush="{StaticResource BorderAlt1}"
                            BorderThickness="1"
                            CornerRadius="0">
                            <Ellipse
                                x:Name="ellipsePath"
                                Width="8"
                                Height="8"
                                Margin="1"
                                Fill="{StaticResource IconColor}"
                                StrokeThickness="1" />
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="radioButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="radioButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="ellipsePath" Property="Fill" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger Property="IsChecked" Value="true">
                            <Setter TargetName="radioButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="radioButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="ellipsePath" Property="Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="radioButtonBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="radioButtonBorder" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="ellipsePath" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  MenuItemSeparator Style  -->
    <Style x:Key="SyncfusionMenuItemSeparatorStyle" TargetType="{x:Type shared:MenuItemSeparator}">
        <Setter Property="Background" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="BorderThickness" Value="0,1,0,0"/>
        <Setter Property="Margin">
            <Setter.Value>
                <Thickness>26,4,0,4</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="shared:MenuItemSeparator">
                    <Border
                            Height="1"
                            Background="{TemplateBinding BorderBrush}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionMenuItemSeparatorStyle}" TargetType="{x:Type shared:MenuItemSeparator}" />

    <!--  MenuAdv Style  -->
    <Style x:Key="SyncfusionMenuAdvStyle" TargetType="{x:Type shared:MenuAdv}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <StackPanel Orientation="Horizontal" />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="shared:MenuAdv">
                    <Border
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <ItemsPresenter />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionMenuAdvStyle}" TargetType="{x:Type shared:MenuAdv}" />

    <!--  MenuItemAdv Style  -->
    <Style x:Key="SyncfusionMenuItemAdvStyle" TargetType="{x:Type shared:MenuItemAdv}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="Background" Value="{StaticResource PopupBackground}" />
        <Setter Property="Foreground" Value="{StaticResource PopupForeground}" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BottomScrollButtonStyle" Value="{StaticResource SyncfusionBottomScrollButtonStyle}" />
        <Setter Property="TopScrollButtonStyle" Value="{StaticResource SyncfusionTopScrollButtonStyle}" />
        <Setter Property="CheckBoxStyle" Value="{StaticResource SyncfusionMenuCheckBoxStyle}" />
        <Setter Property="RadioButtonStyle" Value="{StaticResource SyncfusionMenuRadioButtonStyle}" />
        <Setter Property="Padding" Value="4,4,8,4" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />

        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate x:Name="MenuItemAdvDefault" TargetType="shared:MenuItemAdv">
                    <Grid x:Name="menuItemAdvGrid">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="5" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <Border
                            x:Name="MenuItemBorder"
                            Grid.ColumnSpan="5"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                            Opacity="0" 
                            CornerRadius="4"/>
                        <Grid
                            x:Name="IconGrid"
                            Grid.Column="0"
                            MinWidth="22">
                            <ContentControl
                                x:Name="IconContent"
                                Margin="3"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Content="{TemplateBinding Icon}"
                                IsTabStop="False" />
                            <CheckBox
                                x:Name="CheckBoxPanel"
                                IsTabStop="False"
                                Style="{TemplateBinding CheckBoxStyle}"
                                Visibility="{TemplateBinding CheckBoxVisibility}" />
                            <RadioButton
                                x:Name="RadioButtonPanel"
                                IsTabStop="False"
                                Style="{TemplateBinding RadioButtonStyle}"
                                Visibility="{TemplateBinding RadioButtonVisibility}" />
                        </Grid>

                        <ContentPresenter
                            x:Name="MenuItemContent"
                            Grid.Column="2"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            ContentSource="Header"
                            RecognizesAccessKey="True">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                            </ContentPresenter.Resources>
                        </ContentPresenter>

                        <TextBlock
                            x:Name="GestureTextBlock"
                            Grid.Column="3"
                            Margin="14,3,8,3"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            FontSize="{StaticResource Windows11Light.CaptionText}"
                            Foreground="{StaticResource ContentForegroundAlt1}"
                            Text="{TemplateBinding InputGestureText}"
                            Visibility="Collapsed" />

                        <Border
                            Grid.Column="4"
                            Width="20"
                            Visibility="{TemplateBinding ExtendButtonVisibility}">
                            <Path
                                Name="ExtendButton"
                                Grid.Column="5"
                                Width="3"
                                Height="5"                               
                                Margin="0,0,7,0"
                                HorizontalAlignment="right"                               
                                Fill="{StaticResource IconColor}"
                                Stretch="Fill"
                                Stroke="{StaticResource IconColor}" >
                                <Path.Data>
                                    <PathGeometry>M0.75 9.125C0.75 9.02344 0.787109 8.93555 0.861328 8.86133L4.7168 5L0.861328 1.13867C0.787109 1.06445 0.75 0.976562 0.75 0.875C0.75 0.773438 0.787109 0.685547 0.861328 0.611328C0.935547 0.537109 1.02344 0.5 1.125 0.5C1.22656 0.5 1.31445 0.537109 1.38867 0.611328L5.51367 4.73633C5.58789 4.81055 5.625 4.89844 5.625 5C5.625 5.10156 5.58789 5.18945 5.51367 5.26367L1.38867 9.38867C1.31445 9.46289 1.22656 9.5 1.125 9.5C1.02344 9.5 0.935547 9.46289 0.861328 9.38867C0.787109 9.31445 0.75 9.22656 0.75 9.125Z</PathGeometry>
                                </Path.Data>
                            </Path>
                        </Border>

                        <Popup x:Name="SubMenuPopup" AllowsTransparency="True">
                            <Border
                                x:Name="PopUpBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{StaticResource BorderAlt}"
                                CornerRadius="7"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                Effect="{StaticResource Default.ShadowDepth4}">
                                <Border.Margin>
                                    <Thickness>14,0,14,14</Thickness>
                                </Border.Margin>
                                <Border.Padding>
                                    <Thickness>5,5,5,5</Thickness>
                                </Border.Padding>
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="*" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Button
                                        x:Name="PART_TopScroll"
                                        Grid.Row="0"
                                        Height="{TemplateBinding ScrollerHeight}"
                                        Style="{TemplateBinding TopScrollButtonStyle}"
                                        Visibility="Collapsed" />
                                    <Grid x:Name="PopUpGrid" Grid.Row="1">
                                        <ScrollViewer
                                            x:Name="PART_ScrollViewer"
                                            Padding="1"
                                            Background="{Binding Path=Background, RelativeSource={RelativeSource TemplatedParent}}"
                                            BorderBrush="Transparent"
                                            HorizontalScrollBarVisibility="Disabled"
                                            VerticalScrollBarVisibility="Hidden">
                                            <ItemsPresenter Margin="0" />
                                        </ScrollViewer>
                                    </Grid>
                                    <Button
                                        x:Name="PART_BottomScroll"
                                        Grid.Row="2"
                                        Height="{TemplateBinding ScrollerHeight}"
                                        Style="{TemplateBinding BottomScrollButtonStyle}"
                                        Visibility="Collapsed" />
                                </Grid>
                                <Border.RenderTransform>
                                    <TransformGroup>
                                        <ScaleTransform />
                                        <TranslateTransform />
                                    </TransformGroup>
                                </Border.RenderTransform>
                            </Border>
                        </Popup>
                        <vsm:VisualStateManager.VisualStateGroups>
                            <vsm:VisualStateGroup x:Name="CommonStates">
                                <vsm:VisualState x:Name="Normal">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="MenuItemBorder" Storyboard.TargetProperty="Opacity">
                                            <SplineDoubleKeyFrame KeyTime="0" Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </vsm:VisualState>
                                <vsm:VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="GestureTextBlock" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentForegroundAlt1}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="ExtendButton" Storyboard.TargetProperty="Opacity">
                                            <SplineDoubleKeyFrame KeyTime="0" Value="0.5" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </vsm:VisualState>
                                <vsm:VisualState x:Name="MenuItemSelected">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="MenuItemBorder" Storyboard.TargetProperty="Opacity">
                                            <SplineDoubleKeyFrame KeyTime="0" Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="GestureTextBlock" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PopupSelectedForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ExtendButton" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ExtendButton" Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </vsm:VisualState>
                                <vsm:VisualState x:Name="MenuItemFocused">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="MenuItemBorder" Storyboard.TargetProperty="Opacity">
                                            <SplineDoubleKeyFrame KeyTime="0" Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </vsm:VisualState>
                                <vsm:VisualState x:Name="SubMenuItemFocused">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="MenuItemBorder" Storyboard.TargetProperty="Opacity">
                                            <SplineDoubleKeyFrame KeyTime="0" Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="GestureTextBlock" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PopupHoveredForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ExtendButton" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ExtendButton" Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </vsm:VisualState>
                            </vsm:VisualStateGroup>
                        </vsm:VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="SubMenuHeader" />
                                <Condition Property="IsFocused" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="GestureTextBlock" Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
                            <Setter TargetName="ExtendButton" Property="Fill" Value="{StaticResource IconColorHovered}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="SubMenuHeader" />
                                <Condition Property="IsPressed" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="GestureTextBlock" Property="Foreground" Value="{StaticResource PopupSelectedForeground}" />
                            <Setter TargetName="ExtendButton" Property="Fill" Value="{StaticResource IconColorSelected}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="SubMenuHeader" />
                                <Condition Property="IsEnabled" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="GestureTextBlock" Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
                            <Setter TargetName="ExtendButton" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="SubMenuHeader" />
                                <Condition Property="IsSubMenuOpen" Value="true" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="Transparent" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="TopLevelHeader" />
                                <Condition Property="IsFocused" Value="false" />
                                <Condition Property="IsMouseOver" Value="false" />
                                <Condition Property="IsPressed" Value="false" />
                                <Condition Property="IsEnabled" Value="true" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="Transparent" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="TopLevelHeader" />
                                <Condition Property="IsMouseOver" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="TopLevelHeader" />
                                <Condition Property="IsPressed" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="TopLevelHeader" />
                                <Condition Property="IsEnabled" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="Transparent" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="TopLevelHeader" />
                                <Condition Property="IsSubMenuOpen" Value="true" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="SubMenuItem" />
                                <Condition Property="IsFocused" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="GestureTextBlock" Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
                            <Setter TargetName="ExtendButton" Property="Fill" Value="{StaticResource IconColorHovered}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="SubMenuItem" />
                                <Condition Property="IsPressed" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="GestureTextBlock" Property="Foreground" Value="{StaticResource PopupSelectedForeground}" />
                            <Setter TargetName="ExtendButton" Property="Fill" Value="{StaticResource IconColorSelected}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="SubMenuItem" />
                                <Condition Property="IsEnabled" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="GestureTextBlock" Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
                            <Setter TargetName="ExtendButton" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="TopLevelItem" />
                                <Condition Property="IsFocused" Value="false" />
                                <Condition Property="IsMouseOver" Value="false" />
                                <Condition Property="IsPressed" Value="false" />
                                <Condition Property="IsEnabled" Value="true" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="Transparent" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="TopLevelItem" />
                                <Condition Property="IsMouseOver" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="TopLevelItem" />
                                <Condition Property="IsPressed" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="TopLevelItem" />
                                <Condition Property="IsFocused" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="TopLevelHeader" />
                                <Condition Property="IsFocused" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Role" Value="TopLevelItem" />
                                <Condition Property="IsEnabled" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="MenuItemBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="MenuItemBorder" Property="BorderBrush" Value="Transparent" />
                        </MultiTrigger>
                       
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5" />
            </Trigger>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
            </Trigger>
            <Trigger Property="IsFocused" Value="true">
                <Setter Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="true">
                <Setter Property="Foreground" Value="{StaticResource PopupSelectedForeground}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="SubMenuHeader" />
                    <Condition Property="IsFocused" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="SubMenuHeader" />
                    <Condition Property="IsPressed" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PopupSelectedForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="SubMenuHeader" />
                    <Condition Property="IsEnabled" Value="false" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="SubMenuHeader" />
                    <Condition Property="IsSubMenuOpen" Value="true" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PopupSelectedForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelHeader" />
                    <Condition Property="IsFocused" Value="false" />
                    <Condition Property="IsMouseOver" Value="false" />
                    <Condition Property="IsPressed" Value="false" />
                    <Condition Property="IsEnabled" Value="true" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelHeader" />
                    <Condition Property="IsMouseOver" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelHeader" />
                    <Condition Property="IsPressed" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelHeader" />
                    <Condition Property="IsEnabled" Value="false" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelHeader" />
                    <Condition Property="IsSubMenuOpen" Value="true" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="SubMenuItem" />
                    <Condition Property="IsFocused" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="SubMenuItem" />
                    <Condition Property="IsPressed" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PopupSelectedForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="SubMenuItem" />
                    <Condition Property="IsEnabled" Value="false" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelItem" />
                    <Condition Property="IsFocused" Value="false" />
                    <Condition Property="IsMouseOver" Value="false" />
                    <Condition Property="IsPressed" Value="false" />
                    <Condition Property="IsEnabled" Value="true" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelItem" />
                    <Condition Property="IsMouseOver" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelItem" />
                    <Condition Property="IsPressed" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelItem" />
                    <Condition Property="IsFocused" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelHeader" />
                    <Condition Property="IsFocused" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelItem" />
                    <Condition Property="IsEnabled" Value="false" />
                </MultiTrigger.Conditions>
                <Setter  Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </MultiTrigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CurveKeyboardFocusVisualStyle}"/>
            </Trigger>

        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionMenuItemAdvStyle}" TargetType="{x:Type shared:MenuItemAdv}" />

</ResourceDictionary>
