<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:smithChart="clr-namespace:Syncfusion.UI.Xaml.SmithChart;assembly=Syncfusion.SfSmithChart.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="SyncfusionSfSmithChartStyle"
           TargetType="smithChart:SfSmithChart">
        <Setter Property="SmithChartResourceDictionary">
            <Setter.Value>
                <ResourceDictionary Source="SfSmithChart.xaml" />
            </Setter.Value>
        </Setter>
        <Setter Property="ColorModel">
            <Setter.Value>
                <smithChart:SmithChartColorModel Palette="Custom">
                    <smithChart:SmithChartColorModel.CustomBrushes>
                        <SolidColorBrush Color="{StaticResource Series1.Color}" />
                        <SolidColorBrush Color="{StaticResource Series1.Color}" />
                    </smithChart:SmithChartColorModel.CustomBrushes>
                </smithChart:SmithChartColorModel>
            </Setter.Value>
        </Setter>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}" />
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="BorderThickness"
                Value="0" />
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}" />
        <Setter Property="ChartAreaBorderBrush"
                Value="{StaticResource BorderAlt}" />
        <Setter Property="ChartAreaBorderThickness"
                Value="0" />
    </Style>

    <Style x:Key="RadialAxisMajorGridlineStyle"
           TargetType="Path">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt4}" />
        <Setter Property="StrokeThickness"
                Value="1.0" />
    </Style>

    <Style x:Key="HorizontalAxisMajorGridlineStyle"
           TargetType="Path">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt4}" />
        <Setter Property="StrokeThickness"
                Value="1.0" />
    </Style>

    <Style x:Key="RadialAxisMinorGridlineStyle"
           TargetType="Path">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt4}" />
        <Setter Property="StrokeThickness"
                Value="0.5" />
    </Style>

    <Style x:Key="HorizontalAxisMinorGridlineStyle"
           TargetType="Path">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt4}" />
        <Setter Property="StrokeThickness"
                Value="0.5" />
    </Style>

    <Style x:Key="RadialAxisLineStyle"
           TargetType="Path">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt}" />
        <Setter Property="StrokeThickness"
                Value="1" />
    </Style>

    <Style x:Key="HorizontalAxisLineStyle"
           TargetType="Path">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt}" />
        <Setter Property="StrokeThickness"
                Value="1" />
    </Style>

    <Style x:Key="SyncfusionSfSmithChartHorizontalAxisStyle"
           TargetType="smithChart:HorizontalAxis">
        <Setter Property="MajorGridlineStyle"
                Value="{StaticResource HorizontalAxisMajorGridlineStyle}" />
        <Setter Property="MinorGridlineStyle"
                Value="{StaticResource HorizontalAxisMajorGridlineStyle}" />
        <Setter Property="AxisLineStyle"
                Value="{StaticResource HorizontalAxisLineStyle}" />
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}" />
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="IsTabStop" Value="False"/>
    </Style>

    <Style x:Key="SyncfusionSfSmithChartRadialAxisStyle"
           TargetType="smithChart:RadialAxis">
        <Setter Property="MajorGridlineStyle"
                Value="{StaticResource RadialAxisMajorGridlineStyle}" />
        <Setter Property="MinorGridlineStyle"
                Value="{StaticResource RadialAxisMinorGridlineStyle}" />
        <Setter Property="AxisLineStyle"
                Value="{StaticResource RadialAxisLineStyle}" />
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}" />
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="IsTabStop" Value="False"/>
    </Style>

    <Style x:Key="SyncfusionSfSmithChartLegendStyle"
           TargetType="smithChart:SmithChartLegend">
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.CaptionText}" />
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}" />
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}" />
        <Setter Property="ItemTemplate">
            <Setter.Value>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal"
                                Margin="{Binding ItemMargin}"
                                Opacity="{Binding Opacity}">
                        <ContentPresenter Height="{Binding IconHeight}"
                                          Width="{Binding IconWidth}"
                                          Content="{Binding}"
                                          ContentTemplate="{Binding LegendIconTemplate}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}"
                                       TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                        <ContentPresenter>
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}"
                                       TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                            <ContentPresenter.Content>
                                <TextBlock Text="{Binding Label}"
                                           VerticalAlignment="Center"
                                           Margin="3,0,0,0" />
                            </ContentPresenter.Content>
                        </ContentPresenter>
                    </StackPanel>
                </DataTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="IsTabStop" Value="False"/>
    </Style>

    <DataTemplate x:Key="ToolTipTemplate">
        <Border BorderBrush="{StaticResource TooltipBorder}"
                BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                Background="{StaticResource TooltipBackground}"
                Effect="{StaticResource Default.ShadowDepth4}">
            <Border.Margin>
                <Thickness>14,0,14,14</Thickness>
            </Border.Margin>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <!--The reason for handling collapse in tooltips is to restrict the appearance of the nose part between the mouse pointer and the tooltip popup-->
                <Polygon Grid.Column="0"
                         Visibility="Collapsed"
                         StrokeThickness="0"
                         Fill="{StaticResource TooltipBackground}"
                         Stroke="{StaticResource TooltipBorder}"
                         Points="{Binding PolygonPoints}" />
                <Grid Grid.Column="0"
                      Margin="7"
                      HorizontalAlignment="Center">
                    <Grid.RowDefinitions>
                        <RowDefinition />
                        <RowDefinition />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0"
                               Grid.Column="0"
                               Text="{smithChart:SmithChartLocalizationResourceExtension ResourceName=TooltipResistance}"
                               Foreground="{StaticResource TooltipForeground}"
                               FontSize="{StaticResource Windows11Dark.CaptionText}" />
                    <TextBlock Grid.Row="0"
                               Grid.Column="1"
                               Text=" : "
                               Foreground="{StaticResource TooltipForeground}"
                               FontSize="{StaticResource Windows11Dark.CaptionText}"
                               Margin="0,-1,0,0" />
                    <TextBlock Grid.Row="0"
                               Grid.Column="2"
                               Text="{Binding Resistance}"
                               Foreground="{StaticResource TooltipForeground}"
                               FontSize="{StaticResource Windows11Dark.CaptionText}"
                               Margin="3,0,0,0" />
                    <TextBlock Grid.Row="1"
                               Grid.Column="0"
                               Text="{smithChart:SmithChartLocalizationResourceExtension ResourceName=TooltipReactance}"
                               Foreground="{StaticResource TooltipForeground}"
                               FontSize="{StaticResource Windows11Dark.CaptionText}" />
                    <TextBlock Grid.Row="1"
                               Grid.Column="1"
                               Text=" : "
                               Foreground="{StaticResource TooltipForeground}"
                               FontSize="{StaticResource Windows11Dark.CaptionText}"
                               Margin="0,-1,0,0" />
                    <TextBlock Grid.Row="1"
                               Grid.Column="2"
                               Text="{Binding Reactance}"
                               Margin="3,0,0,0"
                               Foreground="{StaticResource TooltipForeground}"
                               FontSize="{StaticResource Windows11Dark.CaptionText}" />
                </Grid>
            </Grid>
        </Border>
    </DataTemplate>

    <Style x:Key="DefaultDataLabelStyle"
           TargetType="TextBlock">
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.CaptionText}"></Setter>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"></Setter>
    </Style>

    <DataTemplate x:Key="DataLabelTemplate">
        <Border CornerRadius="4"
                Background="{Binding Background}"
                BorderThickness="1"
                Padding="8,4,8,4"
                BorderBrush="{StaticResource SmithChart.LineSeries.DataLabel.Static.Border}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="{Binding Resistance}"
                           Style="{Binding LabelStyle}" />
                <TextBlock Text=" : "
                           Style="{Binding LabelStyle}" />
                <TextBlock Text="{Binding Reactance}"
                           Style="{Binding LabelStyle}" />
            </StackPanel>
        </Border>
    </DataTemplate>
   
    <!--The reason for handing interor, In marker fill and stroke color should be same as per UX figma design.-->
    <DataTemplate x:Key="Cross">
        <Polyline Stretch="Fill"
                  Fill="{Binding Interior}"
                  Stroke="{StaticResource ContentBackground}"
                  StrokeThickness="2"
                  Width="{Binding Width}"
                  Height="{Binding Height}"
                  Points="0,4, 4,0, 7.5,4, 11,0, 15,4, 11,7.5, 15,11, 11,15, 7.5,11, 4,15, 0,11, 4,7.5,0,4" />
    </DataTemplate>

    <!--The reason for handing interor, In marker fill and stroke color should be same as per UX figma design.-->
    <DataTemplate x:Key="Circle">
        <Ellipse Stretch="Fill"
                 Fill="{Binding Interior}"
                 Stroke="{StaticResource ContentBackground}"
                 StrokeThickness="2"
                 Width="{Binding Width}"
                 Height="{Binding Height}" />
    </DataTemplate>

    <!--The reason for handing interor, In marker fill and stroke color should be same as per UX figma design.-->
    <DataTemplate x:Key="Triangle">
        <Path Stretch="Fill"
              Fill="{Binding Interior}"
              Stroke="{StaticResource ContentBackground}"
              StrokeThickness="2"
              Width="{Binding Width}"
              Height="{Binding Height}">
            <Path.Data>
                <PathGeometry>
                    <PathFigure StartPoint="7.5,0"
                                IsClosed="True">
                        <LineSegment Point="15,15" />
                        <LineSegment Point="0,15" />
                        <LineSegment Point="7.5,0" />
                    </PathFigure>
                </PathGeometry>
            </Path.Data>
        </Path>
    </DataTemplate>

    <!--The reason for handing interor, In marker fill and stroke color should be same as per UX figma design.-->
    <DataTemplate x:Key="Rectangle">
        <Rectangle Stretch="Fill"
                   Fill="{Binding Interior}"
                   Stroke="{StaticResource ContentBackground}"
                   StrokeThickness="2"
                   Width="{Binding Width}"
                   Height="{Binding Height}" />
    </DataTemplate>

    <!--The reason for handing interor, In marker fill and stroke color should be same as per UX figma design.-->
    <DataTemplate x:Key="Diamond">
        <Polyline Stretch="Fill"
                  Fill="{Binding Interior}"
                  Stroke="{StaticResource ContentBackground}"
                  StrokeThickness="2"
                  Width="{Binding Width}"
                  Height="{Binding Height}"
                  Points="7.5,0,15,7.5,7.5,15,0,7.5,7.5,0" />
    </DataTemplate>

    <!--The reason for handing interor, In marker fill and stroke color should be same as per UX figma design.-->
    <DataTemplate x:Key="Pentagon">
        <Polyline Stretch="Fill"
                  Fill="{Binding Interior}"
                  Stroke="{StaticResource ContentBackground}"
                  StrokeThickness="2"
                  Width="{Binding Width}"
                  Height="{Binding Height}"
                  Points="7.5,0,15,7,12,15,3,15,0,7,7.5,0" />
    </DataTemplate>

    <!--The reason for handing interor, In marker fill and stroke color should be same as per UX figma design.-->
    <DataTemplate x:Key="Plus">
        <Path Stretch="Fill"
              Fill="{Binding Interior}"
              Stroke="{StaticResource ContentBackground}"
              StrokeThickness="2"
              Width="{Binding Width}"
              Height="{Binding Height}"
              Data="F1 M 145.193,54.8249L 169.315,54.8249L 169.315,78.9463L 145.193,78.9463L 145.193,103.074L 121.071,103.074L 121.071,78.9463L 96.946,78.9463L 96.946,54.8249L 121.071,54.8249L 121.071,30.6983L 145.193,30.6983L 145.193,54.8249 Z " />
    </DataTemplate>

    <Style BasedOn="{StaticResource SyncfusionSfSmithChartLegendStyle}"
           TargetType="smithChart:SmithChartLegend" />

    <Style BasedOn="{StaticResource SyncfusionSfSmithChartRadialAxisStyle}"
           TargetType="smithChart:RadialAxis" />

    <Style BasedOn="{StaticResource SyncfusionSfSmithChartHorizontalAxisStyle}"
           TargetType="smithChart:HorizontalAxis" />

    <Style BasedOn="{StaticResource SyncfusionSfSmithChartStyle}"
           TargetType="smithChart:SfSmithChart" />

</ResourceDictionary>
