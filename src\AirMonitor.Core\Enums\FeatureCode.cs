namespace AirMonitor.Core.Enums;

/// <summary>
/// 功能代码枚举
/// 定义可授权的功能模块
/// </summary>
public enum FeatureCode
{
    /// <summary>
    /// 串口通信功能
    /// </summary>
    SerialCommunication,

    /// <summary>
    /// 数据采集功能
    /// </summary>
    DataCollection,

    /// <summary>
    /// 实时监控功能
    /// </summary>
    RealTimeMonitoring,

    /// <summary>
    /// 历史数据查询功能
    /// </summary>
    HistoryData,

    /// <summary>
    /// 数据回放功能
    /// </summary>
    DataPlayback,

    /// <summary>
    /// 报警管理功能
    /// </summary>
    AlarmManagement,

    /// <summary>
    /// 数据导出功能
    /// </summary>
    DataExport,

    /// <summary>
    /// 高级分析功能
    /// </summary>
    AdvancedAnalysis,

    /// <summary>
    /// 多设备支持功能
    /// </summary>
    MultiDevice,

    /// <summary>
    /// 自定义协议功能
    /// </summary>
    CustomProtocol,

    /// <summary>
    /// 系统配置功能
    /// </summary>
    SystemConfiguration,

    /// <summary>
    /// 用户管理功能
    /// </summary>
    UserManagement,

    /// <summary>
    /// 许可证管理功能
    /// </summary>
    LicenseManagement
}
