namespace AirMonitor.Core.Constants;

/// <summary>
/// 应用程序常量定义
/// </summary>
public static class ApplicationConstants
{
    /// <summary>
    /// 应用程序名称
    /// </summary>
    public const string ApplicationName = "AirMonitor";

    /// <summary>
    /// License注册机名称
    /// </summary>
    public const string LicenseGeneratorName = "AirMonitor License Generator";

    /// <summary>
    /// 公司名称
    /// </summary>
    public const string CompanyName = "AirMonitor Technologies";

    /// <summary>
    /// 版权信息
    /// </summary>
    public const string Copyright = "Copyright © 2024 AirMonitor Technologies. All rights reserved.";

    /// <summary>
    /// 配置文件名
    /// </summary>
    public const string ConfigFileName = "appsettings.json";

    /// <summary>
    /// 日志文件夹
    /// </summary>
    public const string LogsFolder = "Logs";

    /// <summary>
    /// 数据文件夹
    /// </summary>
    public const string DataFolder = "Data";

    /// <summary>
    /// 备份文件夹
    /// </summary>
    public const string BackupFolder = "Backup";

    /// <summary>
    /// 主题文件夹
    /// </summary>
    public const string ThemesFolder = "Themes";

    /// <summary>
    /// 默认字体名称
    /// </summary>
    public const string DefaultFontFamily = "Microsoft YaHei";

    /// <summary>
    /// 默认字体大小
    /// </summary>
    public const double DefaultFontSize = 12.0;

    /// <summary>
    /// 窗口最小宽度
    /// </summary>
    public const double MinWindowWidth = 800;

    /// <summary>
    /// 窗口最小高度
    /// </summary>
    public const double MinWindowHeight = 600;

    /// <summary>
    /// 默认窗口宽度
    /// </summary>
    public const double DefaultWindowWidth = 1200;

    /// <summary>
    /// 默认窗口高度
    /// </summary>
    public const double DefaultWindowHeight = 800;
}
