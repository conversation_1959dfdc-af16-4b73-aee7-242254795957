<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:Syncfusion.Windows.Controls.Navigation;assembly=Syncfusion.SfTreeNavigator.WPF"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <BooleanToVisibilityConverter x:Key="VisibilityConverter" />

    <Style
        x:Key="SyncfusionBackButtonStyle"
        BasedOn="{StaticResource WPFGlyphButtonStyle}"
        TargetType="{x:Type Button}">
        <Setter Property="Width" Value="24" />
        <Setter Property="Height" Value="24" />
        <Setter Property="Margin">
            <Setter.Value>
                <Thickness>8,0,0,0</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="AutomationProperties.AutomationId" Value="BackButton" />
        <Setter Property="AutomationProperties.Name" Value="Back" />
        <Setter Property="AutomationProperties.ItemType" Value="Navigation Button" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        x:Name="PART_Border"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Background="{TemplateBinding Background}"
                        Effect="{TemplateBinding Effect}"
                        CornerRadius="1">
                        <Path x:Name="pressed"
                      Width="14"
                      Height="12"
                      Fill="{StaticResource ContentForeground}"
                      Stretch="Fill"
                      StrokeThickness="0">
                            <Path.Data>
                                <PathGeometry>M13.170958,0L14.585027,1.4149784 3.8476841,12.144024 32.000095,12.144024 32.000095,14.145017 3.8188674,14.145017 14.607,24.922982 13.191954,26.338998 0,13.159001z</PathGeometry>
                            </Path.Data>
                        </Path>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver"  Value="True">
                            <Setter TargetName="pressed" Property="Fill" Value="{StaticResource IconColorHovered}"/>
                            <Setter TargetName="PART_Border" Property="Background"  Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter TargetName="PART_Border" Property="BorderBrush"  Value="{StaticResource ContentBackgroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="pressed" Property="Fill" Value="{StaticResource IconColorSelected}"/>
                            <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="pressed" Property="Fill" Value="{StaticResource IconColorDisabled}"/>
                            <Setter TargetName="PART_Border" Property="Background" Value="Transparent"/>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="Transparent"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="Width" Value="{StaticResource TouchMode.MinWidth}"/>
                <Setter Property="Height" Value="{StaticResource TouchMode.MinHeight}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="TreeHeaderStyle" TargetType="ContentControl">
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightMedium}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="HorizontalAlignment" Value="Left" />
    </Style>

    <Style x:Key="SyncfusionSfTreeNavigatorItemStyle" TargetType="local:SfTreeNavigatorItem">
        <Setter Property="BorderThickness" Value="0,0,0,0" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Height" Value="32" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Padding">
            <Setter.Value>
                <Thickness>9,5,5,5</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:SfTreeNavigatorItem">
                    <Border x:Name="MainBorder"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        >
                            <Grid>
                                <Border
                                    x:Name="PART_Selection"
                                    Grid.ColumnSpan="2"
                                    Background="{TemplateBinding Background}" />
                                <Grid RenderTransformOrigin="0.5,0.5"
                                    Margin="{TemplateBinding Padding}"
                                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Background="Transparent">
                                        <Grid.RenderTransform>
                                            <TranslateTransform />
                                        </Grid.RenderTransform>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <ContentPresenter
                                            x:Name="contentControl"
                                            VerticalAlignment="Center"
                                            Content="{TemplateBinding Header}"
                                            ContentTemplate="{TemplateBinding HeaderTemplate}"
                                            ContentTemplateSelector="{TemplateBinding HeaderTemplateSelector}">
                                            <ContentPresenter.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="TextBlock" />
                                            </ContentPresenter.Resources>
                                            <ContentPresenter.Margin>
                                                <Thickness>0,3,0,3</Thickness>
                                            </ContentPresenter.Margin>
                                        </ContentPresenter>
                                <Path x:Name="PART_Arrow"
                                      Grid.Column="1"
                                      Width="6"
                                      Height="10"
                                      HorizontalAlignment="Right"
                                      Stretch="Fill"
                                      Stroke="Transparent"
                                      Fill="{StaticResource IconColor}"
                                      StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}"
                                      Visibility="{Binding HasItems, Converter={StaticResource VisibilityConverter}, RelativeSource={RelativeSource TemplatedParent}}">
                                    <Path.Margin>
                                        <Thickness>0,3,3,3</Thickness>
                                    </Path.Margin>
                                    <Path.Data>
                                        <PathGeometry>M1.5 9.00195C1.42969 9.00195 1.36523 8.98828 1.30664 8.96094C1.24414 8.93359 1.19141 8.89844 1.14844 8.85547C1.10156 8.80859 1.06445 8.75586 1.03711 8.69727C1.00977 8.63477 0.996094 8.56836 0.996094 8.49805C0.996094 8.42383 1.00977 8.35938 1.03711 8.30469C1.06055 8.25 1.09766 8.19727 1.14844 8.14648L4.29492 5L1.14844 1.85352C1.04688 1.75195 0.996094 1.63477 0.996094 1.50195C0.996094 1.43164 1.00977 1.36719 1.03711 1.30859C1.06445 1.24609 1.10156 1.19336 1.14844 1.15039C1.19141 1.10352 1.24414 1.06641 1.30664 1.03906C1.36523 1.01172 1.42969 0.998047 1.5 0.998047C1.63672 0.998047 1.75391 1.04688 1.85156 1.14453L5.35547 4.64844C5.45313 4.74609 5.50195 4.86328 5.50195 5C5.50195 5.13672 5.45313 5.25391 5.35547 5.35156L1.85156 8.85547C1.75391 8.95312 1.63672 9.00195 1.5 9.00195Z</PathGeometry>
                                    </Path.Data>
                                </Path>
                                </Grid>
                            </Grid>

                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="SelectionStates">
                                <VisualState x:Name="Selected" />
                                <VisualState x:Name="UnSelected" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="PointerOver" />
                                <VisualState x:Name="Pressed" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="true">
                            <Setter Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="PART_Selection" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="MainBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="contentControl" Property="TextElement.Foreground" Value="{StaticResource HoveredForeground}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="PART_Selection" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="PART_Arrow"
                                    Property="Stroke"
                                    Value="Transparent" />
                            <Setter TargetName="PART_Arrow"
                                    Property="Fill"
                                    Value="{StaticResource IconColor}" />
                            <Setter Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="contentControl" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="true">
                            <Setter Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="PART_Selection" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="MainBorder" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="contentControl" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="PART_Selection" Property="Background" Value="Transparent" />
                            <Setter TargetName="contentControl" Property="Control.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfTreeNavigatorItemStyle}" TargetType="local:SfTreeNavigatorItem" />
    <Style x:Key="SyncfusionTreeNavigatorHeaderItemStyle" TargetType="local:TreeNavigatorHeaderItem">
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightMedium}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="Height" Value="40" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:TreeNavigatorHeaderItem">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">

                        <Grid 
                            Margin="10,5,5,5"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            Background="Transparent">
                                <Grid.RenderTransform>
                                    <TranslateTransform />
                                </Grid.RenderTransform>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>

                                <ContentPresenter
                                    VerticalAlignment="Center"
                                    Content="{TemplateBinding Header}"
                                    ContentTemplate="{TemplateBinding HeaderTemplate}"
                                    ContentTemplateSelector="{TemplateBinding HeaderTemplateSelector}" />
                            <Path x:Name="PART_Arrow"
                                  Grid.Column="1"
                                  Width="6"
                                  Height="10"
                                  Margin="0,5,5,5"
                                  HorizontalAlignment="Right"                                  
                                  Stretch="Uniform"
                                  Stroke="Transparent"
                                  Fill="{StaticResource IconColor}"
                                  StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}">
                                <Path.Data>
                                    <PathGeometry>M4.5 0.998047C4.57031 0.998047 4.63477 1.01172 4.69336 1.03906C4.75586 1.06641 4.80859 1.10156 4.85156 1.14453C4.89844 1.19141 4.93555 1.24414 4.96289 1.30273C4.99023 1.36523 5.00391 1.43164 5.00391 1.50195C5.00391 1.57617 4.99023 1.64062 4.96289 1.69531C4.93945 1.75 4.90234 1.80273 4.85156 1.85352L1.70508 5L4.85156 8.14648C4.95312 8.24805 5.00391 8.36523 5.00391 8.49805C5.00391 8.56836 4.99023 8.63281 4.96289 8.69141C4.93555 8.75391 4.89844 8.80664 4.85156 8.84961C4.80859 8.89648 4.75586 8.93359 4.69336 8.96094C4.63477 8.98828 4.57031 9.00195 4.5 9.00195C4.36328 9.00195 4.24609 8.95312 4.14844 8.85547L0.644531 5.35156C0.546875 5.25391 0.498047 5.13672 0.498047 5C0.498047 4.86328 0.546875 4.74609 0.644531 4.64844L4.14844 1.14453C4.24609 1.04687 4.36328 0.998047 4.5 0.998047Z</PathGeometry>
                                </Path.Data>
                            </Path>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTreeNavigatorHeaderItemStyle}" TargetType="local:TreeNavigatorHeaderItem" />

    <Style x:Key="SyncfusionTreeNavigatorItemsHostStyle" TargetType="local:TreeNavigatorItemsHost">
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:TreeNavigatorItemsHost">
                    <Grid>
                        <Border
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <Grid>
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <ItemsPresenter Grid.Row="1" />
                                </ScrollViewer>
                            </Grid>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTreeNavigatorItemsHostStyle}" TargetType="local:TreeNavigatorItemsHost" />

    <Style x:Key="SyncfusionSfTreeNavigatorStyle" TargetType="local:SfTreeNavigator">
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Background" Value="Transparent" /> 
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:SfTreeNavigator">
                    <Border
                        x:Name="NavigationBorder"
                        CornerRadius="8"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
						Margin="0,0,-2,0"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid Margin="{TemplateBinding Padding}">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition />
                            </Grid.RowDefinitions>

                            <Border
                                x:Name="PART_DefaultModeHeader"
                                MinHeight="32"                                
                                BorderBrush="{StaticResource BorderAlt}"
                                Background="Transparent">
                                <Border.Padding>
                                    <Thickness>0,0,0,0</Thickness>
                                </Border.Padding>
                                <Border.BorderThickness>
                                    <Thickness>0,0,0,1</Thickness>
                                </Border.BorderThickness>
                                <Grid VerticalAlignment="Center">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Button
                                        x:Name="PART_BackButton"
                                        Grid.Column="0"
                                        IsTabStop="{TemplateBinding IsTabStop}"
                                        Style="{StaticResource SyncfusionBackButtonStyle}" />

                                    <ContentPresenter
                                        Grid.Column="1"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Content="{Binding DrillDownItem.Header,RelativeSource={RelativeSource TemplatedParent}}"                                
                                        ContentTemplate="{Binding DrillDownItem.HeaderTemplate,RelativeSource={RelativeSource TemplatedParent}}"
                                        ContentTemplateSelector="{Binding DrillDownItem.HeaderTemplateSelector,RelativeSource={RelativeSource TemplatedParent}}"
                                        TextElement.FontWeight="{StaticResource Windows11Dark.FontWeightMedium}"
                                        TextElement.FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        TextElement.Foreground="{StaticResource ContentForeground}">
                                        <ContentPresenter.Resources>
                                            <Style BasedOn="{x:Null}" TargetType="TextBlock" />
                                        </ContentPresenter.Resources>
                                        <ContentPresenter.Margin>
                                            <Thickness>8,8,0,8</Thickness>
                                        </ContentPresenter.Margin>
                                    </ContentPresenter>
                                </Grid>
                            </Border>
                            <Grid x:Name="PART_ExtendedModeHeader" Visibility="Collapsed">
                                <Grid.RowDefinitions>
                                    <RowDefinition x:Name="extendedHeaderRow"
                                                   MinHeight="32" />
                                    <RowDefinition />
                                </Grid.RowDefinitions>
                                <Border Background="Transparent"
                                        BorderBrush="{StaticResource BorderAlt}">
                                    <Border.BorderThickness>
                                        <Thickness>0,0,0,1</Thickness>
                                    </Border.BorderThickness>
                                    <Border.Margin>
                                        <Thickness>8,0,0,0</Thickness>
                                    </Border.Margin>
                                    <ContentPresenter
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Content="{TemplateBinding Header}"
                                        ContentTemplate="{TemplateBinding HeaderTemplate}"
                                        ContentTemplateSelector="{TemplateBinding HeaderTemplateSelector}"
                                        TextElement.FontWeight="{StaticResource Windows11Dark.FontWeightMedium}"
                                        TextElement.FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        TextElement.Foreground="{StaticResource ContentForeground}">
                                        <ContentPresenter.Resources>
                                            <Style BasedOn="{x:Null}" TargetType="TextBlock" />
                                        </ContentPresenter.Resources>
                                        <ContentPresenter.Margin>
                                            <Thickness>0,0,0,0</Thickness>
                                        </ContentPresenter.Margin>
                                    </ContentPresenter>
                                </Border>
                                <local:TreeNavigatorItemsHost
                                    x:Name="PART_DrillDownItemsHost"
                                    Grid.Row="1"
                                    HorizontalAlignment="Stretch"
                                    IsHeaderHost="True"
                                    ItemContainerStyle="{TemplateBinding ItemContainerStyle}"
                                    ItemContainerStyleSelector="{TemplateBinding ItemContainerStyleSelector}"
                                    ItemTemplate="{TemplateBinding ItemTemplate}"
                                    ItemTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                    ItemsSource="{TemplateBinding DrillDownItems}" />
                            </Grid>

                            <shared:SfNavigator
                                x:Name="PART_Navigator"
                                Grid.Row="2"

                                ActiveIndex="0"
                                Background="{TemplateBinding Background}"
                                IsTabStop="False">
                                <local:TreeNavigatorItemsHost
                                    x:Name="PART_Host"
                                    Header="{TemplateBinding Header}"
                                    Focusable="False"
                                    IsTabStop="False"
                                    ItemContainerStyle="{TemplateBinding ItemContainerStyle}"
                                    ItemContainerStyleSelector="{TemplateBinding ItemContainerStyleSelector}"
                                    ItemTemplate="{TemplateBinding ItemTemplate}"
                                    ItemTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                    ItemsSource="{Binding Items, RelativeSource={RelativeSource Mode=TemplatedParent}}" />
                            </shared:SfNavigator>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="NavigationBorder" Property="Opacity" Value="0.5" />
                        </Trigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" TargetName="PART_DefaultModeHeader" Value="40"/>
                            <Setter Property="MinHeight" TargetName="extendedHeaderRow" Value="40"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfTreeNavigatorStyle}" TargetType="{x:Type local:SfTreeNavigator}" />

</ResourceDictionary>
