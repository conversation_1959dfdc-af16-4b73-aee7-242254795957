<ResourceDictionary 
                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib"
                    xmlns:datapager="clr-namespace:Syncfusion.UI.Xaml.Controls.DataPager;assembly=Syncfusion.SfGrid.WPF"
                    xmlns:skinManager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:grid="clr-namespace:Syncfusion.UI.Xaml.Grid;assembly=Syncfusion.SfGrid.WPF">

    <ResourceDictionary.MergedDictionaries>
        <skinManager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <skinManager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="HorizontalScrollBarStyle" TargetType="ScrollBar">
        <Setter Property="MinWidth" Value="17" />
        <Setter Property="MinHeight" Value="17" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollBar">
                    <Grid x:Name="Root">
                        <Grid.Resources>
                            <ControlTemplate x:Key="RepeatButtonTemplate" TargetType="RepeatButton">
                                <Grid x:Name="Root"
                                      Height="4"
                                      Background="LightGray">
                                    <VisualStateManager.VisualStateGroups>
                                        <VisualStateGroup x:Name="CommonStates">
                                            <VisualState x:Name="Normal" />
                                        </VisualStateGroup>
                                    </VisualStateManager.VisualStateGroups>
                                </Grid>
                            </ControlTemplate>
                            <ControlTemplate x:Key="HorizontalThumbTemplate" TargetType="Thumb">
                                <Grid x:Name="HorizontalThumbRoot" Height="6">
                                    <VisualStateManager.VisualStateGroups>
                                        <VisualStateGroup x:Name="CommonStates">
                                            <VisualState x:Name="Normal" />
                                            <VisualState x:Name="PointerOver">
                                                <Storyboard>
                                                    <DoubleAnimation Duration="0:0:0"
                                                                     Storyboard.TargetName="BackgroundPointerOver"
                                                                     Storyboard.TargetProperty="Opacity"
                                                                     To="1" />
                                                    <DoubleAnimation Duration="0:0:0"
                                                                     Storyboard.TargetName="Background"
                                                                     Storyboard.TargetProperty="Opacity"
                                                                     To="0" />
                                                </Storyboard>
                                            </VisualState>
                                            <VisualState x:Name="Pressed">
                                                <Storyboard>
                                                    <DoubleAnimation Duration="0:0:0"
                                                                     Storyboard.TargetName="BackgroundPressed"
                                                                     Storyboard.TargetProperty="Opacity"
                                                                     To="1" />
                                                    <DoubleAnimation Duration="0:0:0"
                                                                     Storyboard.TargetName="Background"
                                                                     Storyboard.TargetProperty="Opacity"
                                                                     To="0" />
                                                </Storyboard>
                                            </VisualState>
                                            <VisualState x:Name="Disabled">
                                                <Storyboard>
                                                    <DoubleAnimation Duration="0:0:0"
                                                                     Storyboard.TargetName="ThumbVisual"
                                                                     Storyboard.TargetProperty="Opacity"
                                                                     To="0" />
                                                </Storyboard>
                                            </VisualState>
                                        </VisualStateGroup>
                                    </VisualStateManager.VisualStateGroups>
                                    <Grid x:Name="ThumbVisual"
                                          Margin="0"
                                          Background="LightSlateGray">
                                        <Border x:Name="Background" BorderThickness="1" />
                                        <Border x:Name="BackgroundPointerOver"
                                                BorderThickness="1"
                                                Opacity="0" />
                                        <Border x:Name="BackgroundPressed"
                                                BorderThickness="1"
                                                Opacity="0" />
                                    </Grid>
                                </Grid>
                            </ControlTemplate>
                        </Grid.Resources>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="PointerOver" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimation Duration="0"
                                                         Storyboard.TargetName="Root"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.5" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="ScrollingIndicatorStates">
                                <VisualState x:Name="TouchIndicator">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Duration="0"
                                                                       Storyboard.TargetName="HorizontalRoot"
                                                                       Storyboard.TargetProperty="Visibility">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="MouseIndicator">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Duration="0"
                                                                       Storyboard.TargetName="HorizontalPanningRoot"
                                                                       Storyboard.TargetProperty="Visibility">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalRoot" Storyboard.TargetProperty="IsHitTestVisible">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="True" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid x:Name="HorizontalRoot" IsHitTestVisible="False">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Rectangle Grid.ColumnSpan="5"
                                       Height="2"
                                       Margin="0" />
                            <RepeatButton x:Name="HorizontalLargeDecrease"
                                          Grid.Column="1"
                                          Width="0"
                                          Height="2"
                                          HorizontalAlignment="Stretch"
                                          VerticalAlignment="Stretch"
                                          Interval="50"
                                          IsTabStop="False"
                                          Template="{StaticResource RepeatButtonTemplate}" />
                            <Thumb x:Name="HorizontalThumb"
                                   Grid.Column="2"
                                   Height="10"
                                   MinWidth="48"
                                   Background="{TemplateBinding Background}"
                                   Template="{StaticResource HorizontalThumbTemplate}" />
                            <RepeatButton x:Name="HorizontalLargeIncrease"
                                          Grid.Column="3"
                                          Height="2"
                                          HorizontalAlignment="Stretch"
                                          VerticalAlignment="Stretch"
                                          Interval="50"
                                          IsTabStop="False"
                                          Template="{StaticResource RepeatButtonTemplate}" />
                        </Grid>
                        <Grid x:Name="HorizontalPanningRoot"
                              MinWidth="66"
                              HorizontalAlignment="Left">
                            <Border x:Name="HorizontalPanningThumb"
                                    Height="4"
                                    MinWidth="17" />
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionScrollableContentViewerStyle" TargetType="grid:ScrollableContentViewer">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:ScrollableContentViewer">
                    <Border x:Name="ScrollViewerRoot" BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.ThemeCornerRadiusVariant1}">
                        <Grid Background="{TemplateBinding Background}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <grid:ScrollableContentPresenter x:Name="PART_ScrollContentPresenter"
                                                             Grid.RowSpan="2"
                                                             Grid.ColumnSpan="2"
                                                             Margin="{TemplateBinding Margin}"
                                                             VerticalAlignment="Center"
                                                             ContentTemplate="{TemplateBinding ContentTemplate}"
                                                             TemplatedParent="{TemplateBinding Owner}" />
                            <ScrollBar x:Name="PART_VerticalScrollBar"
                                       Grid.Column="1"
                                       HorizontalAlignment="Right"
                                       IsTabStop="False"
                                       Maximum="{TemplateBinding ScrollableHeight}"
                                       Minimum="0"
                                       Orientation="Vertical"
                                       ViewportSize="{TemplateBinding ViewportHeight}"
                                       Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                       Value="{TemplateBinding VerticalOffset}" />
                            <ScrollBar x:Name="PART_HorizontalScrollBar"
                                       Grid.Row="1"
                                       VerticalAlignment="Bottom"
                                       IsTabStop="False"
                                       Maximum="{TemplateBinding ScrollableWidth}"
                                       Minimum="0"
                                       Orientation="Horizontal"
                                       Style="{StaticResource HorizontalScrollBarStyle}"
                                       ViewportSize="{TemplateBinding ViewportWidth}"
                                       Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                       Value="{TemplateBinding HorizontalOffset}" />
                            <Border x:Name="PART_ScrollBarSeparator"
                                    Grid.Row="1"
                                    Grid.Column="1"
                                    Background="Gray"
                                    BorderBrush="Gray"/>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionDataPagerNavigationButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal">
                                    <Storyboard>
                                        <DoubleAnimation Duration="0"
                                                         Storyboard.TargetName="PART_PageIndicator"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.8" />
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_PageIndicator" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(Button.Content).(Path.Fill)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_PageIndicator" Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_PageIndicator" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(Button.Content).(Path.Fill)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorSelected}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_PageIndicator" Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(Button.Content).(Path.Fill)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="FocusStates">
                                <VisualState x:Name="Focused">
                                    <Storyboard>
                                        <DoubleAnimation Duration="0"
                                                         Storyboard.TargetName="PART_FocusVisualWhite"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="1" />
                                        <DoubleAnimation Duration="0"
                                                         Storyboard.TargetName="PART_FocusVisualBlack"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="1" />
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Unfocused" />
                                <VisualState x:Name="PointerFocused" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Rectangle x:Name="PART_PageIndicator"
                                 Fill="{TemplateBinding Background}"
                                 Stroke="Transparent"
                                 StrokeThickness="0 "
                                 RadiusX="4 " 
                                 RadiusY="4">
                        </Rectangle>

                        <ContentPresenter x:Name="PART_ContentPresenter"
                                          Margin="{TemplateBinding Padding}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Content="{TemplateBinding Content}" 
                                          ContentTemplate="{TemplateBinding ContentTemplate}" />
                        <Rectangle x:Name="PART_FocusVisualWhite"
                                   IsHitTestVisible="False"
                                   Opacity="0"
                                   StrokeDashArray="1,1"
                                   StrokeDashOffset="1.5"
                                   StrokeEndLineCap="Square" />
                        <Rectangle x:Name="PART_FocusVisualBlack"
                                   IsHitTestVisible="False"
                                   Opacity="0"
                                   StrokeDashArray="1,1"
                                   StrokeDashOffset="0.5"
                                   StrokeEndLineCap="Square" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
   
    <Style TargetType="datapager:NumericButton" x:Key="SyncfusionNumericButtonStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="Height" Value="40" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="datapager:NumericButton">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates" >
                                <VisualState x:Name="Normal">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(TextElement.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentForegroundAlt1}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard BeginTime="0">
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CurrentPageIndicator" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(TextElement.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource HoveredForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CurrentPageIndicator" Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard BeginTime="0">
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CurrentPageIndicator" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(TextElement.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource SelectedForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CurrentPageIndicator" Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="CurrentPage">
                                    <Storyboard BeginTime="0">
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CurrentPageIndicator" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Visible</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CurrentPageLineIndicator" Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryBackground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Rectangle x:Name="PART_CurrentPageIndicator"
                                   Width="25"
                                   Height="25"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center" 
                                   StrokeThickness="0"
                                   RadiusX="4" 
                                   RadiusY="4">
                        </Rectangle>
                        <Rectangle x:Name="PART_CurrentPageLineIndicator"
                                   Width="25"
                                   Height="2"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Visibility="Visible"
                                   Margin="0,21,0,0"
                                   StrokeThickness="2"
                                   RadiusX="2" 
                                   RadiusY="2">
                        </Rectangle>
                        <Border x:Name="PART_RootBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"    
                                Height="25"
                                Width="25"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter x:Name="PART_ContentPresenter"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center" >
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Visibility" SourceName="PART_CurrentPageIndicator" Value="Visible">
                            <Setter Property="Fill" TargetName="PART_CurrentPageIndicator" Value="Transparent"/>
                            <Setter Property="Stroke" TargetName="PART_CurrentPageIndicator" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="PART_ContentPresenter" Value="{StaticResource ContentForegroundAlt1}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True" />
                                <Condition Property="Visibility" Value="Visible" SourceName="PART_CurrentPageLineIndicator"/>
                            </MultiTrigger.Conditions>
                            <MultiTrigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard Storyboard.TargetName="PART_CurrentPageLineIndicator" Storyboard.TargetProperty="Width" Duration="0:0:8">
                                        <DoubleAnimationUsingKeyFrames>
                                            <LinearDoubleKeyFrame Value="25" KeyTime="0:0:0"/>
                                            <LinearDoubleKeyFrame Value="30"  KeyTime="0:0:0.1"/>
                                            <LinearDoubleKeyFrame Value="35" KeyTime="0:0:0.2"/>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </BeginStoryboard>
                            </MultiTrigger.EnterActions>
                            <MultiTrigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard Storyboard.TargetName="PART_CurrentPageLineIndicator" Storyboard.TargetProperty="Width" Duration="0:0:8">
                                        <DoubleAnimationUsingKeyFrames>
                                            <LinearDoubleKeyFrame Value="35" KeyTime="0:0:0"/>
                                            <LinearDoubleKeyFrame Value="30" KeyTime="0:0:0.1"/>
                                            <LinearDoubleKeyFrame Value="25" KeyTime="0:0:0.2"/>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </BeginStoryboard>
                            </MultiTrigger.ExitActions>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="datapager:NumericButton" BasedOn="{StaticResource SyncfusionNumericButtonStyle}"/>

    <Style TargetType="datapager:SfDataPager" x:Key="SyncfusionSfDataPagerStyle">
        <Setter Property="AccentBackground" Value="Transparent"/>
        <Setter Property="AccentForeground" Value="{StaticResource IconColor}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="datapager:SfDataPager">
                    <Border x:Name="RootBorder">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="OrientationStates">
                                <VisualState x:Name="Vertical">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_ScrollViewer" Storyboard.TargetProperty="(FrameworkElement.Margin)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_FirstPageButton" Storyboard.TargetProperty="(Grid.Column)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="2" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_FirstPageButton" Storyboard.TargetProperty="(Grid.Row)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="0" />
                                        </Int32AnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_FirstPageButton" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(RotateTransform.Angle)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="90" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_PreviousPageButton" Storyboard.TargetProperty="(Grid.Column)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="2" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_PreviousPageButton" Storyboard.TargetProperty="(Grid.Row)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="1" />
                                        </Int32AnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_PreviousPageButton" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(RotateTransform.Angle)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="90" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_NumericPanelBorder" Storyboard.TargetProperty="(Grid.Column)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="2" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_NumericPanelBorder" Storyboard.TargetProperty="(Grid.Row)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="2" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_NextPageButton" Storyboard.TargetProperty="(Grid.Column)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="2" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_NextPageButton" Storyboard.TargetProperty="(Grid.Row)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="3" />
                                        </Int32AnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_NextPageButton" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(RotateTransform.Angle)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="90" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_LastPageButton" Storyboard.TargetProperty="(Grid.Column)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="2" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_LastPageButton" Storyboard.TargetProperty="(Grid.Row)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="4" />
                                        </Int32AnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_LastPageButton" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(RotateTransform.Angle)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="90" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Horizontal">
                                    <Storyboard>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_FirstPageButton" Storyboard.TargetProperty="(Grid.Row)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="2" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_FirstPageButton" Storyboard.TargetProperty="(Grid.Column)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="0" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_PreviousPageButton" Storyboard.TargetProperty="(Grid.Row)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="2" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_PreviousPageButton" Storyboard.TargetProperty="(Grid.Column)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="1" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_NumericPanelBorder" Storyboard.TargetProperty="(Grid.Row)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="2" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_NumericPanelBorder" Storyboard.TargetProperty="(Grid.Column)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="2" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_NextPageButton" Storyboard.TargetProperty="(Grid.Row)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="2" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_NextPageButton" Storyboard.TargetProperty="(Grid.Column)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="3" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_LastPageButton" Storyboard.TargetProperty="(Grid.Row)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="2" />
                                        </Int32AnimationUsingKeyFrames>
                                        <Int32AnimationUsingKeyFrames Storyboard.TargetName="PART_LastPageButton" Storyboard.TargetProperty="(Grid.Column)">
                                            <EasingInt32KeyFrame KeyTime="0" Value="4" />
                                        </Int32AnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="ButtonStates">
                                <VisualState x:Name="Normal">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_FirstPageButton" Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_PreviousPageButton" Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_NextPageButton" Storyboard.TargetProperty="(UIElement.Opacity)">

                                            <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_LastPageButton" Storyboard.TargetProperty="(UIElement.Opacity)">

                                            <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="LeftButtonsDisabled">
                                    <Storyboard>
                                        <BooleanAnimationUsingKeyFrames Storyboard.TargetName="PART_FirstPageButton" Storyboard.TargetProperty="(UIElement.IsEnabled)">
                                            <DiscreteBooleanKeyFrame KeyTime="0" Value="True" />
                                            <DiscreteBooleanKeyFrame KeyTime="0:0:0.1" Value="False" />
                                        </BooleanAnimationUsingKeyFrames>
                                        <BooleanAnimationUsingKeyFrames Storyboard.TargetName="PART_PreviousPageButton" Storyboard.TargetProperty="(UIElement.IsEnabled)">
                                            <DiscreteBooleanKeyFrame KeyTime="0" Value="True" />
                                            <DiscreteBooleanKeyFrame KeyTime="0:0:0.1" Value="False" />
                                        </BooleanAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>

                                <VisualState x:Name="RightButtonsDisabled">
                                    <Storyboard>
                                        <BooleanAnimationUsingKeyFrames Storyboard.TargetName="PART_NextPageButton" Storyboard.TargetProperty="(Control.IsEnabled)">
                                            <DiscreteBooleanKeyFrame KeyTime="0" Value="False" />
                                        </BooleanAnimationUsingKeyFrames>
                                        <BooleanAnimationUsingKeyFrames Storyboard.TargetName="PART_LastPageButton" Storyboard.TargetProperty="(Control.IsEnabled)">
                                            <DiscreteBooleanKeyFrame KeyTime="0" Value="False" />
                                        </BooleanAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>

                                <VisualState x:Name="LeftRightButtonsDisabled">
                                    <Storyboard>
                                        <BooleanAnimationUsingKeyFrames Storyboard.TargetName="PART_NextPageButton" Storyboard.TargetProperty="(Control.IsEnabled)">
                                            <DiscreteBooleanKeyFrame KeyTime="0" Value="False" />
                                        </BooleanAnimationUsingKeyFrames>
                                        <BooleanAnimationUsingKeyFrames Storyboard.TargetName="PART_LastPageButton" Storyboard.TargetProperty="(Control.IsEnabled)">
                                            <DiscreteBooleanKeyFrame KeyTime="0" Value="False" />
                                        </BooleanAnimationUsingKeyFrames>
                                        <BooleanAnimationUsingKeyFrames Storyboard.TargetName="PART_FirstPageButton" Storyboard.TargetProperty="(UIElement.IsEnabled)">
                                            <DiscreteBooleanKeyFrame KeyTime="0:0:0.1" Value="False" />
                                        </BooleanAnimationUsingKeyFrames>
                                        <BooleanAnimationUsingKeyFrames Storyboard.TargetName="PART_PreviousPageButton" Storyboard.TargetProperty="(UIElement.IsEnabled)">
                                            <DiscreteBooleanKeyFrame KeyTime="0:0:0.1" Value="False" />
                                        </BooleanAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="Center">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Border x:Name="PART_NumericPanelBorder"
                                    Grid.Row="2"
                                    Grid.Column="2"
                                    Margin="-2,0,0,0"
                                    Background="Transparent">
                                <grid:ScrollableContentViewer x:Name="PART_ScrollViewer"
                                                              VerticalAlignment="Center"
                                                              HorizontalScrollBarVisibility="Hidden"
                                                              Style="{StaticResource SyncfusionScrollableContentViewerStyle}"
                                                              VerticalScrollBarVisibility="Hidden"
                                                              Visibility="Collapsed">
                                    <datapager:NumericButtonPanel x:Name="PART_NumericButtonPanel" />
                                </grid:ScrollableContentViewer>
                            </Border>
                            <Button x:Name="PART_FirstPageButton"
                                    Grid.Row="2"
                                    Grid.Column="0"
                                    Width="25"
                                    Height="25"                                    
                                    Margin="5"
                                    Background="{TemplateBinding AccentBackground}"
                                    Style="{StaticResource SyncfusionDataPagerNavigationButtonStyle}"
                                    Visibility="Collapsed">
                                <Button.Content>
                                    <Path 
                                          Width="8"
                                          Height="9"
                                          Margin="0,0,0,0"
                                          Fill="{TemplateBinding AccentForeground}"
                                          Stretch="Fill">
                                        <Path.Data>
                                            <PathGeometry>M0 8.625V0.375C0 0.273438 0.0371094 0.185547 0.111328 0.111328C0.185547 0.0371094 0.273438 0 0.375 0C0.476562 0 0.564453 0.0371094 0.638672 0.111328C0.712891 0.185547 0.75 0.273438 0.75 0.375V8.625C0.75 8.72656 0.712891 8.81445 0.638672 8.88867C0.564453 8.96289 0.476562 9 0.375 9C0.273438 9 0.185547 8.96289 0.111328 8.88867C0.0371094 8.81445 0 8.72656 0 8.625ZM2.625 4.5C2.625 4.39844 2.66211 4.31055 2.73633 4.23633L6.86133 0.111328C6.93555 0.0371094 7.02344 0 7.125 0C7.22656 0 7.31445 0.0371094 7.38867 0.111328C7.46289 0.185547 7.5 0.273438 7.5 0.375C7.5 0.476562 7.46289 0.564453 7.38867 0.638672L3.5332 4.5L7.38867 8.36133C7.46289 8.43555 7.5 8.52344 7.5 8.625C7.5 8.72656 7.46289 8.81445 7.38867 8.88867C7.31445 8.96289 7.22656 9 7.125 9C7.02344 9 6.93555 8.96289 6.86133 8.88867L2.73633 4.76367C2.66211 4.68945 2.625 4.60156 2.625 4.5Z</PathGeometry>
                                        </Path.Data>
                                    </Path>
                                </Button.Content>
                                <Button.RenderTransform>
                                    <TransformGroup>
                                        <RotateTransform CenterX="12.5" CenterY="12.5" />
                                    </TransformGroup>
                                </Button.RenderTransform>
                            </Button>
                            <Button x:Name="PART_PreviousPageButton"
                                    Grid.Row="2"
                                    Grid.Column="1"
                                    Width="25"
                                    Height="25"
                                    Margin="5"
                                    Background="{TemplateBinding AccentBackground}"
                                    Style="{StaticResource SyncfusionDataPagerNavigationButtonStyle}"
                                    BorderThickness="1"
                                    Visibility="Collapsed">
                                <Button.Content>
                                    <Path 
                                          Width="5"
                                          Height="9"
                                          Margin="0,0,0,0"
                                          Fill="{TemplateBinding AccentForeground}"
                                          Stretch="Fill">
                                        <Path.Data>
                                            <PathGeometry>M4.875 8.625C4.875 8.52344 4.83789 8.43555 4.76367 8.36133L0.908203 4.5L4.76367 0.638672C4.83789 0.564453 4.875 0.476562 4.875 0.375C4.875 0.273438 4.83789 0.185547 4.76367 0.111328C4.68945 0.0371094 4.60156 0 4.5 0C4.39844 0 4.31055 0.0371094 4.23633 0.111328L0.111328 4.23633C0.0371094 4.31055 0 4.39844 0 4.5C0 4.60156 0.0371094 4.68945 0.111328 4.76367L4.23633 8.88867C4.31055 8.96289 4.39844 9 4.5 9C4.60156 9 4.68945 8.96289 4.76367 8.88867C4.83789 8.81445 4.875 8.72656 4.875 8.625Z</PathGeometry>
                                        </Path.Data>
                                    </Path>
                                </Button.Content>
                                <Button.RenderTransform>
                                    <TransformGroup>
                                        <RotateTransform CenterX="12.5" CenterY="12.5" />
                                    </TransformGroup>
                                </Button.RenderTransform>
                            </Button>

                            <Button x:Name="PART_NextPageButton"
                                    Grid.Row="2"
                                    Grid.Column="3"
                                    Width="25"
                                    Height="25"
                                    Margin="5"
                                    Background="{TemplateBinding AccentBackground}"
                                    Style="{StaticResource SyncfusionDataPagerNavigationButtonStyle}"
                                    Visibility="Collapsed">
                                <Button.Content>
                                    <Path 
                                          Width="5"
                                          Height="9"
                                          Fill="{TemplateBinding AccentForeground}"
                                          Stretch="Fill">
                                        <Path.Data>
                                            <PathGeometry>M0 8.625C0 8.52344 0.0371094 8.43555 0.111328 8.36133L3.9668 4.5L0.111328 0.638672C0.0371094 0.564453 0 0.476562 0 0.375C0 0.273438 0.0371094 0.185547 0.111328 0.111328C0.185547 0.0371094 0.273438 0 0.375 0C0.476562 0 0.564453 0.0371094 0.638672 0.111328L4.76367 4.23633C4.83789 4.31055 4.875 4.39844 4.875 4.5C4.875 4.60156 4.83789 4.68945 4.76367 4.76367L0.638672 8.88867C0.564453 8.96289 0.476562 9 0.375 9C0.273438 9 0.185547 8.96289 0.111328 8.88867C0.0371094 8.81445 0 8.72656 0 8.625Z</PathGeometry>
                                        </Path.Data>
                                    </Path>
                                </Button.Content>
                                <Button.RenderTransform>
                                    <TransformGroup>
                                        <RotateTransform CenterX="12.5" CenterY="12.5" />
                                    </TransformGroup>
                                </Button.RenderTransform>
                            </Button>

                            <Button x:Name="PART_LastPageButton"
                                    Grid.Row="2"
                                    Grid.Column="4"
                                    Width="25"
                                    Height="25"
                                    Margin="5"
                                    Background="{TemplateBinding AccentBackground}"
                                    Style="{StaticResource SyncfusionDataPagerNavigationButtonStyle}"
                                    Visibility="Collapsed">
                                <Button.Content>
                                    <Path 
                                          Width="8"
                                          Height="9"
                                          Margin="0,0,0,0"
                                          Fill="{TemplateBinding AccentForeground}"
                                          Stretch="Fill">
                                        <Path.Data>
                                            <PathGeometry>M7 9.125V0.875C7 0.773438 7.03711 0.685547 7.11133 0.611328C7.18555 0.537109 7.27344 0.5 7.375 0.5C7.47656 0.5 7.56445 0.537109 7.63867 0.611328C7.71289 0.685547 7.75 0.773438 7.75 0.875V9.125C7.75 9.22656 7.71289 9.31445 7.63867 9.38867C7.56445 9.46289 7.47656 9.5 7.375 9.5C7.27344 9.5 7.18555 9.46289 7.11133 9.38867C7.03711 9.31445 7 9.22656 7 9.125ZM0.25 9.125C0.25 9.02344 0.287109 8.93555 0.361328 8.86133L4.22266 5L0.361328 1.13867C0.287109 1.06445 0.25 0.976562 0.25 0.875C0.25 0.773438 0.287109 0.685547 0.361328 0.611328C0.435547 0.537109 0.523438 0.5 0.625 0.5C0.726562 0.5 0.814453 0.537109 0.888672 0.611328L5.01367 4.73633C5.08789 4.81055 5.125 4.89844 5.125 5C5.125 5.10156 5.08789 5.18945 5.01367 5.26367L0.888672 9.38867C0.814453 9.46289 0.726562 9.5 0.625 9.5C0.523438 9.5 0.435547 9.46289 0.361328 9.38867C0.287109 9.31445 0.25 9.22656 0.25 9.125Z</PathGeometry>
                                        </Path.Data>
                                    </Path>
                                </Button.Content>
                                <Button.RenderTransform>
                                    <TransformGroup>
                                        <RotateTransform CenterX="12.5" CenterY="12.5" />
                                    </TransformGroup>
                                </Button.RenderTransform>
                            </Button>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type datapager:SfDataPager}" BasedOn="{StaticResource SyncfusionSfDataPagerStyle}"/>
</ResourceDictionary>
