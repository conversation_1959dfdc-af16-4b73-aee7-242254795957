using AirMonitor.Core.Constants;
using AirMonitor.Core.Interfaces;
using AirMonitor.Core.Models;
using AirMonitor.Core.Utilities;
using System.IO;

namespace AirMonitor.LicenseGenerator.Services;

/// <summary>
/// 许可证生成服务实现
/// 专门用于License注册机的许可证生成功能
/// </summary>
public class LicenseGeneratorService : ILicenseGeneratorService
{
    private readonly ICryptoService _cryptoService;
    private readonly IHardwareFingerprintService _hardwareFingerprintService;

    public LicenseGeneratorService(ICryptoService cryptoService, IHardwareFingerprintService hardwareFingerprintService)
    {
        _cryptoService = cryptoService;
        _hardwareFingerprintService = hardwareFingerprintService;
    }

    /// <summary>
    /// 生成许可证
    /// </summary>
    /// <param name="licenseInfo">许可证信息</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <returns>生成结果</returns>
    public async Task<LicenseGenerationResult> GenerateLicenseAsync(LicenseInfo licenseInfo, string privateKey)
    {
        try
        {
            // 验证输入参数
            var validationResult = ValidateLicenseInfo(licenseInfo);
            if (!validationResult.IsSuccess)
            {
                return LicenseGenerationResult.Failure(validationResult.ErrorMessage);
            }

            // 创建许可证副本用于签名（清除原有签名）
            var licenseForSigning = CloneLicenseInfo(licenseInfo);
            licenseForSigning.Signature = string.Empty;
            licenseForSigning.UpdatedAt = DateTime.UtcNow;

            // 序列化许可证数据
            var licenseJson = JsonHelper.Serialize(licenseForSigning, JsonHelper.CompactOptions);

            // 生成数字签名
            var signature = await _cryptoService.SignAsync(licenseJson, privateKey);
            licenseForSigning.Signature = signature;

            // 序列化最终的许可证
            var finalLicenseJson = JsonHelper.Serialize(licenseForSigning, JsonHelper.DefaultOptions);

            // 加密许可证内容
            var publicKey = await GetPublicKeyFromPrivateKeyAsync(privateKey);
            var encryptedContent = await _cryptoService.EncryptAsync(finalLicenseJson, publicKey);

            return LicenseGenerationResult.Success(encryptedContent, null, licenseForSigning);
        }
        catch (Exception ex)
        {
            return LicenseGenerationResult.Failure($"生成许可证失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 生成许可证文件
    /// </summary>
    /// <param name="licenseInfo">许可证信息</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <returns>生成结果</returns>
    public async Task<LicenseGenerationResult> GenerateLicenseFileAsync(LicenseInfo licenseInfo, string filePath, string privateKey)
    {
        try
        {
            // 生成许可证内容
            var result = await GenerateLicenseAsync(licenseInfo, privateKey);
            if (!result.IsSuccess)
            {
                return result;
            }

            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory))
            {
                FileHelper.EnsureDirectoryExists(directory);
            }

            // 写入文件
            var success = await FileHelper.SafeWriteAllTextAsync(filePath, result.LicenseContent!);
            if (!success)
            {
                return LicenseGenerationResult.Failure("写入许可证文件失败");
            }

            return LicenseGenerationResult.Success(result.LicenseContent, filePath, result.LicenseInfo);
        }
        catch (Exception ex)
        {
            return LicenseGenerationResult.Failure($"生成许可证文件失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 批量生成许可证
    /// </summary>
    /// <param name="licenseInfos">许可证信息列表</param>
    /// <param name="outputDirectory">输出目录</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <param name="progressCallback">进度回调</param>
    /// <returns>批量生成结果</returns>
    public async Task<BatchGenerationResult> BatchGenerateLicensesAsync(
        IEnumerable<LicenseInfo> licenseInfos,
        string outputDirectory,
        string privateKey,
        IProgress<BatchGenerationProgress>? progressCallback = null)
    {
        var result = new BatchGenerationResult
        {
            StartTime = DateTime.UtcNow
        };

        var licenseList = licenseInfos.ToList();
        result.TotalCount = licenseList.Count;

        try
        {
            // 确保输出目录存在
            FileHelper.EnsureDirectoryExists(outputDirectory);

            for (int i = 0; i < licenseList.Count; i++)
            {
                var license = licenseList[i];
                
                // 报告进度
                progressCallback?.Report(new BatchGenerationProgress
                {
                    CurrentIndex = i + 1,
                    TotalCount = result.TotalCount,
                    CurrentLicenseId = license.LicenseId,
                    StatusMessage = $"正在生成许可证: {license.LicenseId}"
                });

                try
                {
                    // 生成文件名
                    var fileName = $"{license.LicenseId}{LicenseConstants.LicenseFileExtension}";
                    var filePath = Path.Combine(outputDirectory, fileName);

                    // 生成许可证文件
                    var generationResult = await GenerateLicenseFileAsync(license, filePath, privateKey);
                    
                    if (generationResult.IsSuccess)
                    {
                        result.SuccessResults.Add(generationResult);
                        result.SuccessCount++;
                    }
                    else
                    {
                        result.FailureResults.Add(generationResult);
                        result.FailureCount++;
                    }
                }
                catch (Exception ex)
                {
                    var failureResult = LicenseGenerationResult.Failure($"生成许可证 {license.LicenseId} 失败: {ex.Message}");
                    failureResult.LicenseInfo = license;
                    result.FailureResults.Add(failureResult);
                    result.FailureCount++;
                }
            }
        }
        catch (Exception ex)
        {
            // 处理整体异常
            var remainingCount = result.TotalCount - result.SuccessCount - result.FailureCount;
            for (int i = 0; i < remainingCount; i++)
            {
                var failureResult = LicenseGenerationResult.Failure($"批量生成中断: {ex.Message}");
                result.FailureResults.Add(failureResult);
                result.FailureCount++;
            }
        }
        finally
        {
            result.EndTime = DateTime.UtcNow;
            
            // 报告完成
            progressCallback?.Report(new BatchGenerationProgress
            {
                CurrentIndex = result.TotalCount,
                TotalCount = result.TotalCount,
                StatusMessage = $"批量生成完成: 成功 {result.SuccessCount}, 失败 {result.FailureCount}"
            });
        }

        return result;
    }

    /// <summary>
    /// 从模板生成许可证
    /// </summary>
    /// <param name="template">许可证模板</param>
    /// <param name="customerName">客户名称</param>
    /// <param name="customerEmail">客户邮箱</param>
    /// <param name="hardwareFingerprint">硬件指纹</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <returns>生成结果</returns>
    public async Task<LicenseGenerationResult> GenerateFromTemplateAsync(
        LicenseTemplate template,
        string customerName,
        string customerEmail,
        string hardwareFingerprint,
        string privateKey)
    {
        try
        {
            // 从模板创建许可证信息
            var licenseInfo = template.CreateLicenseInfo(customerName, customerEmail, hardwareFingerprint);
            
            // 生成许可证
            var result = await GenerateLicenseAsync(licenseInfo, privateKey);
            
            if (result.IsSuccess)
            {
                // 增加模板使用次数
                template.IncrementUsage();
            }
            
            return result;
        }
        catch (Exception ex)
        {
            return LicenseGenerationResult.Failure($"从模板生成许可证失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 验证许可证文件
    /// </summary>
    /// <param name="filePath">许可证文件路径</param>
    /// <param name="publicKey">RSA公钥</param>
    /// <returns>验证结果</returns>
    public async Task<ValidationResult> ValidateLicenseFileAsync(string filePath, string publicKey)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                return ValidationResult.Failure("许可证文件不存在");
            }

            // 读取文件内容
            var encryptedContent = await File.ReadAllTextAsync(filePath);
            if (string.IsNullOrWhiteSpace(encryptedContent))
            {
                return ValidationResult.Failure("许可证文件内容为空");
            }

            // 解密内容
            var privateKey = await GetPrivateKeyFromPublicKeyAsync(publicKey); // 注意：这里需要私钥才能解密
            var licenseJson = await _cryptoService.DecryptAsync(encryptedContent, privateKey);

            // 反序列化许可证
            var license = JsonHelper.Deserialize<LicenseInfo>(licenseJson);
            if (license == null)
            {
                return ValidationResult.Failure("许可证格式无效");
            }

            // 验证数字签名
            var isSignatureValid = await _cryptoService.VerifyLicenseSignatureAsync(license, publicKey);
            if (!isSignatureValid)
            {
                return ValidationResult.Failure("许可证数字签名验证失败");
            }

            // 验证有效期
            if (!license.IsValid())
            {
                return ValidationResult.Failure("许可证已过期");
            }

            return ValidationResult.Success(license);
        }
        catch (Exception ex)
        {
            return ValidationResult.Failure($"验证许可证文件失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 从Excel文件导入批量生成数据
    /// </summary>
    /// <param name="excelFilePath">Excel文件路径</param>
    /// <returns>许可证信息列表</returns>
    public async Task<List<LicenseInfo>> ImportFromExcelAsync(string excelFilePath)
    {
        // 注意：这是一个占位实现，需要使用EPPlus库来实现Excel读取
        await Task.Delay(100); // 模拟异步操作
        
        // TODO: 实现Excel导入功能
        throw new NotImplementedException("Excel导入功能尚未实现");
    }

    /// <summary>
    /// 导出批量生成模板到Excel
    /// </summary>
    /// <param name="excelFilePath">Excel文件路径</param>
    /// <returns>是否导出成功</returns>
    public async Task<bool> ExportTemplateToExcelAsync(string excelFilePath)
    {
        // 注意：这是一个占位实现，需要使用EPPlus库来实现Excel写入
        await Task.Delay(100); // 模拟异步操作
        
        // TODO: 实现Excel模板导出功能
        throw new NotImplementedException("Excel模板导出功能尚未实现");
    }

    /// <summary>
    /// 获取许可证信息摘要
    /// </summary>
    /// <param name="filePath">许可证文件路径</param>
    /// <param name="publicKey">RSA公钥</param>
    /// <returns>许可证摘要信息</returns>
    public async Task<LicenseSummary?> GetLicenseSummaryAsync(string filePath, string publicKey)
    {
        try
        {
            var validationResult = await ValidateLicenseFileAsync(filePath, publicKey);
            if (!validationResult.IsSuccess || validationResult.Data is not LicenseInfo license)
            {
                return null;
            }

            return new LicenseSummary
            {
                LicenseId = license.LicenseId,
                LicenseType = license.LicenseType.ToString(),
                CustomerName = license.CustomerName,
                IsValid = license.IsValid(),
                RemainingDays = license.GetRemainingDays(),
                FeatureCount = license.AuthorizedFeatures.Count,
                IssuedDate = license.IssuedDate,
                ExpiryDate = license.ExpiryDate
            };
        }
        catch
        {
            return null;
        }
    }

    #region 私有方法

    /// <summary>
    /// 验证许可证信息
    /// </summary>
    private ValidationResult ValidateLicenseInfo(LicenseInfo licenseInfo)
    {
        if (string.IsNullOrWhiteSpace(licenseInfo.LicenseId))
        {
            return ValidationResult.Failure("许可证ID不能为空");
        }

        if (string.IsNullOrWhiteSpace(licenseInfo.ProductName))
        {
            return ValidationResult.Failure("产品名称不能为空");
        }

        if (string.IsNullOrWhiteSpace(licenseInfo.CustomerName))
        {
            return ValidationResult.Failure("客户名称不能为空");
        }

        if (string.IsNullOrWhiteSpace(licenseInfo.HardwareFingerprint))
        {
            return ValidationResult.Failure("硬件指纹不能为空");
        }

        if (licenseInfo.AuthorizedFeatures.Count == 0)
        {
            return ValidationResult.Failure("至少需要授权一个功能");
        }

        return ValidationResult.Success();
    }

    /// <summary>
    /// 克隆许可证信息
    /// </summary>
    private LicenseInfo CloneLicenseInfo(LicenseInfo original)
    {
        return new LicenseInfo
        {
            LicenseId = original.LicenseId,
            ProductName = original.ProductName,
            ProductVersion = original.ProductVersion,
            CustomerName = original.CustomerName,
            CustomerEmail = original.CustomerEmail,
            LicenseType = original.LicenseType,
            AuthorizedFeatures = new List<string>(original.AuthorizedFeatures),
            HardwareFingerprint = original.HardwareFingerprint,
            IssuedDate = original.IssuedDate,
            ExpiryDate = original.ExpiryDate,
            MaxDeviceCount = original.MaxDeviceCount,
            IsTrial = original.IsTrial,
            TrialDays = original.TrialDays,
            CreatedAt = original.CreatedAt,
            UpdatedAt = DateTime.UtcNow,
            Signature = string.Empty
        };
    }

    /// <summary>
    /// 从私钥获取公钥
    /// </summary>
    private async Task<string> GetPublicKeyFromPrivateKeyAsync(string privateKey)
    {
        // 注意：这是一个简化实现，实际应用中需要从私钥提取公钥
        // 这里假设我们有配对的密钥
        var keyPair = await _cryptoService.GenerateRSAKeyPairAsync();
        return keyPair.PublicKey;
    }

    /// <summary>
    /// 从公钥获取私钥（仅用于验证，实际应用中不应该这样做）
    /// </summary>
    private async Task<string> GetPrivateKeyFromPublicKeyAsync(string publicKey)
    {
        // 注意：这是一个占位实现，实际应用中无法从公钥获取私钥
        // 这里需要使用正确的私钥进行解密
        var keyPair = await _cryptoService.GenerateRSAKeyPairAsync();
        return keyPair.PrivateKey;
    }

    #endregion
}
