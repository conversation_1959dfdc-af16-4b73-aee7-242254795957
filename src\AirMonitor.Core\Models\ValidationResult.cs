using AirMonitor.Core.Enums;

namespace AirMonitor.Core.Models;

/// <summary>
/// 验证结果模型
/// 用于封装验证操作的结果信息
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否验证成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public LicenseErrorCode ErrorCode { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 详细错误信息
    /// </summary>
    public string DetailedMessage { get; set; } = string.Empty;

    /// <summary>
    /// 验证时间
    /// </summary>
    public DateTime ValidationTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 相关数据（如许可证信息）
    /// </summary>
    public object? Data { get; set; }

    /// <summary>
    /// 创建成功的验证结果
    /// </summary>
    /// <param name="data">相关数据</param>
    /// <returns>成功的验证结果</returns>
    public static ValidationResult Success(object? data = null)
    {
        return new ValidationResult
        {
            IsSuccess = true,
            ErrorCode = LicenseErrorCode.Success,
            ErrorMessage = "验证成功",
            Data = data
        };
    }

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    /// <param name="errorCode">错误代码</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="detailedMessage">详细错误信息</param>
    /// <returns>失败的验证结果</returns>
    public static ValidationResult Failure(LicenseErrorCode errorCode, string errorMessage, string? detailedMessage = null)
    {
        return new ValidationResult
        {
            IsSuccess = false,
            ErrorCode = errorCode,
            ErrorMessage = errorMessage,
            DetailedMessage = detailedMessage ?? errorMessage
        };
    }

    /// <summary>
    /// 创建失败的验证结果（使用默认错误代码）
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>失败的验证结果</returns>
    public static ValidationResult Failure(string errorMessage)
    {
        return Failure(LicenseErrorCode.Unknown, errorMessage);
    }

    /// <summary>
    /// 获取格式化的错误信息
    /// </summary>
    /// <returns>格式化的错误信息</returns>
    public string GetFormattedError()
    {
        if (IsSuccess)
        {
            return "验证成功";
        }

        return $"[{ErrorCode}] {ErrorMessage}" +
               (string.IsNullOrWhiteSpace(DetailedMessage) || DetailedMessage == ErrorMessage
                   ? ""
                   : $"\n详细信息: {DetailedMessage}");
    }

    /// <summary>
    /// 转换为字符串表示
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return GetFormattedError();
    }
}
