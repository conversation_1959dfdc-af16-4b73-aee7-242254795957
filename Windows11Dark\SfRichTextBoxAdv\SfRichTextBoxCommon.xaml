<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"   
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:richtextboxadv="clr-namespace:Syncfusion.Windows.Controls.RichTextBoxAdv;assembly=Syncfusion.SfRichTextBoxAdv.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:tools_controls_shared="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.shared.WPF"
					xmlns:Syncfusion="http://schemas.syncfusion.com/wpf">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Button.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ComboBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/CheckBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Label.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TabControl.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ToggleButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatToggleButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/RepeatButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ListBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/RadioButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/UpDown/UpDown.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphPrimaryToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBlock.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Menu.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/ColorPickerPalette/ColorPickerPalette.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SplitButtonAdv/SplitButtonAdv.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="SfRichTextBoxAdv.Pane.Static.Foreground" Color="#FFB1B6BC" />

    <FontWeight x:Key="SfRichTextBoxAdv.Pane.Heading1.Static.FontWeight">Semibold</FontWeight>

    <FontWeight x:Key="SfRichTextBoxAdv.Pane.Heading2.Static.FontWeight">Semibold</FontWeight>
    <SolidColorBrush x:Key="SfRichTextBoxAdv.Pane.Heading2.Static.Glyph" Color="#FF3B3C3B" />

    <richtextboxadv:FontFamilyStringConverter x:Key="FontFamilyStringConverter"/>
    <richtextboxadv:DoubleStringConverter x:Key="DoubleStringConverter"/>
    <richtextboxadv:HighlightColorToggleConverter x:Key="HighlightColorConverter"/>
    <richtextboxadv:UnderlineToggleConverter x:Key="UnderlineToggleconverter"/>
    <richtextboxadv:TextAlignmentStringConverter x:Key="TextAlignmentStringConverter"/>
    <richtextboxadv:LineSpacingTypeStringConverter x:Key="LineSpacingTypeStringConverter"/>
    <richtextboxadv:SingleStrikeThroughToggleConverter x:Key="SingleStrikeThroughToggleConverter"/>
    <richtextboxadv:DoubleStrikeThroughToggleConverter x:Key="DoubleStrikeThroughToggleConverter" />
    <richtextboxadv:SuperscriptToggleConverter x:Key="SuperscriptToggleConverter"/>
    <richtextboxadv:SubscriptToggleConverter x:Key="SubscriptToggleConverter"/>
    <richtextboxadv:TableAlignmentToggleConverter x:Key="TableAlignmentToggleConverter"/>
    <richtextboxadv:VerticalAlignmentToggleConverter x:Key="VerticalAlignmentToggleConverter"/>
    <richtextboxadv:WidthTypeStringConverter x:Key="WidthTypeStringConverter" />
    <richtextboxadv:HeightTypeStringConverter x:Key="HeightTypeStringConverter" />
    <richtextboxadv:ToggleVisibilityConverter x:Key="ToggleVisibilityConverter"/>
    <richtextboxadv:ColorBrushConverter x:Key="ColorBrushConverter"/>
    <richtextboxadv:ToggleBooleanConverter x:Key="ToggleBooleanConverter"/>
    
    <sys:Boolean x:Key="True">True</sys:Boolean>
    <sys:Boolean x:Key="False">False</sys:Boolean>
    
    <shared:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
    <shared:ClipConverter x:Key="ClipConverter" />
    <shared:RadiusConverter x:Key="RadiusConverter" />

</ResourceDictionary>
