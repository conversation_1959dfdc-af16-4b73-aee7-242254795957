<UserControl x:Class="AirMonitor.LicenseGenerator.Views.LicenseGeneratorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AirMonitor.LicenseGenerator.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="{StaticResource LargePadding}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：许可证配置 -->
            <StackPanel Grid.Column="0">
                
                <!-- 基本信息 -->
                <GroupBox Header="基本信息" Style="{StaticResource ModernGroupBoxStyle}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 许可证ID -->
                        <Label Grid.Row="0" Grid.Column="0" Content="许可证ID:" Style="{StaticResource ModernLabelStyle}"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding LicenseId}" Style="{StaticResource ModernTextBoxStyle}"/>
                        <Button Grid.Row="0" Grid.Column="2" Content="生成" Command="{Binding GenerateNewIdCommand}" 
                                Style="{StaticResource SecondaryButtonStyle}" Margin="8,0,0,0"/>

                        <!-- 产品信息 -->
                        <Label Grid.Row="1" Grid.Column="0" Content="产品名称:" Style="{StaticResource ModernLabelStyle}"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2" Text="{Binding ProductName}" Style="{StaticResource ModernTextBoxStyle}"/>

                        <Label Grid.Row="2" Grid.Column="0" Content="产品版本:" Style="{StaticResource ModernLabelStyle}"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2" Text="{Binding ProductVersion}" Style="{StaticResource ModernTextBoxStyle}"/>

                        <!-- 客户信息 -->
                        <Label Grid.Row="3" Grid.Column="0" Content="客户名称:" Style="{StaticResource ModernLabelStyle}"/>
                        <TextBox Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="2" Text="{Binding CustomerName}" Style="{StaticResource ModernTextBoxStyle}"/>

                        <Label Grid.Row="4" Grid.Column="0" Content="客户邮箱:" Style="{StaticResource ModernLabelStyle}"/>
                        <TextBox Grid.Row="4" Grid.Column="1" Grid.ColumnSpan="2" Text="{Binding CustomerEmail}" Style="{StaticResource ModernTextBoxStyle}"/>
                    </Grid>
                </GroupBox>

                <!-- 许可证类型 -->
                <GroupBox Header="许可证类型" Style="{StaticResource ModernGroupBoxStyle}">
                    <ComboBox ItemsSource="{Binding LicenseTypes}" 
                              SelectedValue="{Binding SelectedLicenseType}" 
                              SelectedValuePath="Type"
                              DisplayMemberPath="Name"
                              Style="{StaticResource {x:Type ComboBox}}">
                        <ComboBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel>
                                    <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                    <TextBlock Text="{Binding Description}" FontSize="10" 
                                               Foreground="{StaticResource TextSecondaryBrush}"/>
                                </StackPanel>
                            </DataTemplate>
                        </ComboBox.ItemTemplate>
                    </ComboBox>
                </GroupBox>

                <!-- 有效期设置 -->
                <GroupBox Header="有效期设置" Style="{StaticResource ModernGroupBoxStyle}">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Label Grid.Row="0" Grid.Column="0" Content="签发日期:" Style="{StaticResource ModernLabelStyle}"/>
                            <DatePicker Grid.Row="0" Grid.Column="1" SelectedDate="{Binding IssuedDate}"/>

                            <CheckBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" 
                                      Content="永久许可证" IsChecked="{Binding IsPermanent}" Margin="0,8"/>

                            <Label Grid.Row="2" Grid.Column="0" Content="有效期天数:" Style="{StaticResource ModernLabelStyle}"
                                   IsEnabled="{Binding IsPermanent, Converter={x:Static local:BooleanToInverseBooleanConverter.Instance}}"/>
                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding ValidityDays}" Style="{StaticResource ModernTextBoxStyle}"
                                     IsEnabled="{Binding IsPermanent, Converter={x:Static local:BooleanToInverseBooleanConverter.Instance}}"/>

                            <Label Grid.Row="3" Grid.Column="0" Content="过期日期:" Style="{StaticResource ModernLabelStyle}"/>
                            <DatePicker Grid.Row="3" Grid.Column="1" SelectedDate="{Binding ExpiryDate}" IsEnabled="False"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- 硬件绑定 -->
                <GroupBox Header="硬件绑定" Style="{StaticResource ModernGroupBoxStyle}">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox Grid.Column="0" Text="{Binding HardwareFingerprint}" 
                                     Style="{StaticResource ModernTextBoxStyle}" 
                                     ToolTip="硬件指纹"/>
                            <Button Grid.Column="1" Content="获取本地" Command="{Binding GetLocalFingerprintCommand}"
                                    Style="{StaticResource SecondaryButtonStyle}" Margin="8,0,0,0"/>
                            <Button Grid.Column="2" Content="导入" Command="{Binding ImportFingerprintCommand}"
                                    Style="{StaticResource SecondaryButtonStyle}" Margin="8,0,0,0"/>
                        </Grid>
                        
                        <!-- 硬件信息显示 -->
                        <Border Style="{StaticResource CardStyle}" Margin="0,8,0,0"
                                Visibility="{Binding HardwareFingerprintInfo, Converter={x:Static local:NullToVisibilityConverter.Instance}}">
                            <StackPanel>
                                <TextBlock Text="硬件信息:" FontWeight="Medium" Margin="0,0,0,8"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="计算机名:" Margin="0,0,8,4"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding HardwareFingerprintInfo.ComputerName}" Margin="0,0,0,4"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="CPU ID:" Margin="0,0,8,4"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding HardwareFingerprintInfo.CpuId}" Margin="0,0,0,4"/>
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="主板ID:" Margin="0,0,8,4"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding HardwareFingerprintInfo.MotherboardId}" Margin="0,0,0,4"/>
                                    
                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="指纹:" Margin="0,0,8,4"/>
                                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding HardwareFingerprintInfo.Fingerprint}" 
                                               FontWeight="Medium" Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,4"/>
                                </Grid>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </GroupBox>

                <!-- 功能授权 -->
                <GroupBox Header="功能授权" Style="{StaticResource ModernGroupBoxStyle}">
                    <ScrollViewer MaxHeight="200" VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding Features}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <CheckBox IsChecked="{Binding IsSelected}" Margin="0,4">
                                        <StackPanel>
                                            <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                            <TextBlock Text="{Binding Description}" FontSize="10" 
                                                       Foreground="{StaticResource TextSecondaryBrush}"/>
                                        </StackPanel>
                                    </CheckBox>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </GroupBox>

                <!-- 操作按钮 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,16,0,0">
                    <Button Content="生成许可证" Command="{Binding GenerateLicenseCommand}" 
                            Style="{StaticResource PrimaryButtonStyle}"/>
                    <Button Content="预览" Command="{Binding PreviewLicenseCommand}" 
                            Style="{StaticResource SecondaryButtonStyle}"/>
                    <Button Content="清除" Command="{Binding ClearFormCommand}" 
                            Style="{StaticResource SecondaryButtonStyle}"/>
                </StackPanel>
            </StackPanel>

            <!-- 右侧：生成结果 -->
            <StackPanel Grid.Column="2">
                <GroupBox Header="生成结果" Style="{StaticResource ModernGroupBoxStyle}">
                    <StackPanel>
                        <!-- 许可证内容 -->
                        <Label Content="许可证内容:" Style="{StaticResource ModernLabelStyle}"/>
                        <TextBox Text="{Binding GeneratedLicenseContent}" 
                                 Style="{StaticResource ModernTextBoxStyle}"
                                 Height="300" 
                                 AcceptsReturn="True" 
                                 VerticalScrollBarVisibility="Auto"
                                 IsReadOnly="True"
                                 FontFamily="Consolas"/>

                        <!-- 保存按钮 -->
                        <Button Content="保存许可证文件" Command="{Binding SaveLicenseCommand}" 
                                Style="{StaticResource PrimaryButtonStyle}" 
                                Margin="0,16,0,0"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </Grid>
    </ScrollViewer>
</UserControl>
