<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:treeView="clr-namespace:Syncfusion.UI.Xaml.TreeView;assembly=Syncfusion.SfTreeView.WPF"
                    xmlns:notification="clr-namespace:Syncfusion.Windows.Controls.Notification;assembly=Syncfusion.SfBusyIndicator.WPF"  
                    xmlns:skinManager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <skinManager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <skinManager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>
    
    <SolidColorBrush x:Key="SfTreeView.CurrentItem.InnerBorder.Static.Border" Color="#FAFAFA" />

    <BooleanToVisibilityConverter x:Key="boolToVisibilityConverter"/>

    <DataTemplate x:Key="busyIndicatorTemplate">
        <notification:SfBusyIndicator x:Name="PART_BusyIndicator"
                                    IsBusy="True"
                                    AnimationType="DotCircle"
                                    ViewboxWidth="{TemplateBinding Width}"
                                    VerticalContentAlignment="Center"
                                    VerticalAlignment="Center">
        </notification:SfBusyIndicator>
    </DataTemplate> 

    <DataTemplate x:Key="SfTreeViewStartExpanderTemplate" >
        <Border x:Name="border"  
              Width="{TemplateBinding Width}"
              Height="{TemplateBinding Height}"
              Background="Transparent"
              BorderBrush="Transparent"
              CornerRadius="{StaticResource Windows11Light.ThemeCornerRadiusVariant1}">
            <StackPanel x:Name="panel" VerticalAlignment="Center">
                <Path x:Name="PART_CollapseCellPath"
                  Width="6.27"
                  Height="10.008" 
                  VerticalAlignment="Center"
                  Stretch="Uniform"
                  Fill="{StaticResource IconColor}" >
                    <Path.Data>
                        M0.335938 10.6641C0.335938 10.4818 0.401042 10.3255 0.53125 10.1953L4.72656 6L0.53125 1.80469C0.401042 1.67448 0.335938 1.51823 0.335938 1.33594C0.335938 1.15365 0.398438 0.997396 0.523438 0.867188C0.653646 0.731771 0.8125 0.664062 1 0.664062C1.18229 0.664062 1.33854 0.729167 1.46875 0.859375L6.14062 5.53125C6.27083 5.66146 6.33594 5.81771 6.33594 6C6.33594 6.18229 6.27083 6.33854 6.14062 6.46875L1.46875 11.1406C1.33854 11.2708 1.18229 11.3359 1 11.3359C0.8125 11.3359 0.653646 11.2708 0.523438 11.1406C0.398438 11.0052 0.335938 10.8464 0.335938 10.6641Z
                    </Path.Data>
                </Path>
                <Path x:Name="PART_ExpanderCellPath"
                  Width="10.008"
                  Height="6.27"
                  VerticalAlignment="Center"
                  Stretch="Uniform"
                  Fill="{StaticResource IconColor}">
                    <Path.Data>
                        M1.33594 0.335937C1.51823 0.335937 1.67448 0.401041 1.80469 0.53125L6 4.72656L10.1953 0.53125C10.3255 0.401042 10.4818 0.335937 10.6641 0.335937C10.8464 0.335937 11.0026 0.398437 11.1328 0.523437C11.2682 0.653646 11.3359 0.8125 11.3359 1C11.3359 1.18229 11.2708 1.33854 11.1406 1.46875L6.46875 6.14062C6.33854 6.27083 6.18229 6.33594 6 6.33594C5.81771 6.33594 5.66146 6.27083 5.53125 6.14062L0.859375 1.46875C0.729167 1.33854 0.664062 1.18229 0.664062 1C0.664062 0.8125 0.729167 0.653645 0.859375 0.523437C0.994792 0.398437 1.15365 0.335937 1.33594 0.335937Z
                    </Path.Data>
                </Path>
            </StackPanel>
        </Border>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding IsExpanded}" Value="True">
                <Setter Property="Visibility" TargetName="PART_ExpanderCellPath" Value="Visible"/>
                <Setter Property="Visibility" TargetName="PART_CollapseCellPath" Value="Collapsed"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding IsExpanded}" Value="False">
                <Setter Property="Visibility" TargetName="PART_ExpanderCellPath" Value="Collapsed"/>
                <Setter Property="Visibility" TargetName="PART_CollapseCellPath" Value="Visible"/>
            </DataTrigger> 
            <Trigger Property="IsEnabled" Value="false">
                <Setter TargetName="border" Property="Background" Value="Transparent"/>
                <Setter TargetName="border" Property="BorderBrush" Value="Transparent"/>
                <Setter TargetName="PART_CollapseCellPath" Property="Fill" Value="{StaticResource IconColorDisabled}"/>
                <Setter TargetName="PART_ExpanderCellPath" Property="Fill" Value="{StaticResource IconColorDisabled}"/>
            </Trigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <DataTemplate x:Key="SfTreeViewEndExpanderTemplate">
        <Border x:Name="border"
              Width="{TemplateBinding Width}"
              Height="{TemplateBinding Height}"              
              Background="Transparent"
              BorderBrush="Transparent"
              CornerRadius="{StaticResource Windows11Light.ThemeCornerRadiusVariant1}">
            <StackPanel x:Name="panel" VerticalAlignment="Center">
                <Path x:Name="PART_CollapseCellPath"
                  Width="6.27"
                  Height="10.08"    
                  VerticalAlignment="Center"
                  Stretch="Uniform"
                  Fill="{StaticResource IconColor}" >
                    <Path.Data>
                        M6.33594 10C6.33594 9.81771 6.27083 9.66146 6.14062 9.53125L1.94531 5.33594L6.14062 1.14062C6.27083 1.01042 6.33594 0.854167 6.33594 0.671875C6.33594 0.489583 6.27344 0.333333 6.14844 0.203125C6.01823 0.0677083 5.85938 0 5.67188 0C5.48958 0 5.33333 0.0651042 5.20312 0.195312L0.53125 4.86719C0.401042 4.9974 0.335938 5.15365 0.335938 5.33594C0.335938 5.51823 0.401042 5.67448 0.53125 5.80469L5.20312 10.4766C5.33333 10.6068 5.48958 10.6719 5.67188 10.6719C5.85938 10.6719 6.01823 10.6068 6.14844 10.4766C6.27344 10.3411 6.33594 10.1823 6.33594 10Z
                    </Path.Data>
                </Path>
                <Path x:Name="PART_ExpanderCellPath"
                  Width="10.008"
                  Height="6.27"
                  VerticalAlignment="Center"
                  Stretch="Uniform"
                  Fill="{StaticResource IconColor}">
                    <Path.Data>
                        M1.33594 0.335937C1.51823 0.335937 1.67448 0.401041 1.80469 0.53125L6 4.72656L10.1953 0.53125C10.3255 0.401042 10.4818 0.335937 10.6641 0.335937C10.8464 0.335937 11.0026 0.398437 11.1328 0.523437C11.2682 0.653646 11.3359 0.8125 11.3359 1C11.3359 1.18229 11.2708 1.33854 11.1406 1.46875L6.46875 6.14062C6.33854 6.27083 6.18229 6.33594 6 6.33594C5.81771 6.33594 5.66146 6.27083 5.53125 6.14062L0.859375 1.46875C0.729167 1.33854 0.664062 1.18229 0.664062 1C0.664062 0.8125 0.729167 0.653645 0.859375 0.523437C0.994792 0.398437 1.15365 0.335937 1.33594 0.335937Z
                    </Path.Data>
                </Path>
            </StackPanel>
        </Border>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding IsExpanded}" Value="True">
                <Setter Property="Visibility" TargetName="PART_ExpanderCellPath" Value="Visible"/>
                <Setter Property="Visibility" TargetName="PART_CollapseCellPath" Value="Collapsed"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding IsExpanded}" Value="False">
                <Setter Property="Visibility" TargetName="PART_ExpanderCellPath" Value="Collapsed"/>
                <Setter Property="Visibility" TargetName="PART_CollapseCellPath" Value="Visible"/>
            </DataTrigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter TargetName="border" Property="Background" Value="Transparent"/>
                <Setter TargetName="border" Property="BorderBrush" Value="Transparent"/>
                <Setter TargetName="PART_CollapseCellPath" Property="Fill" Value="{StaticResource IconColorDisabled}"/>
                <Setter TargetName="PART_ExpanderCellPath" Property="Fill" Value="{StaticResource IconColorDisabled}"/>
            </Trigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <Style x:Key="SyncfusionTreeViewItemStyle" TargetType="treeView:TreeViewItem">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type treeView:TreeViewItem}">
                    <Grid x:Name="ROOT" Background="{TemplateBinding Background}">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="DropStates">
                                <VisualState x:Name="Normal">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames
                                            BeginTime="00:00:00"
                                            Storyboard.TargetName="PART_ContentView"
                                            Storyboard.TargetProperty="Foreground"
                                            Duration="00:00:00">
                                            <DiscreteObjectKeyFrame KeyTime="00:00:00" 
                                                                    Value="{StaticResource ContentForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="DropAsChild">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames BeginTime="00:00:00"
                                                                       Duration="00:00:00"
                                                                       Storyboard.TargetName="BorderContent"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" 
                                                                    Value="{StaticResource ContentBackgroundSelected}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames BeginTime="00:00:00"
                                                                       Duration="00:00:00"
                                                                       Storyboard.TargetName="BorderContent"
                                                                       Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" 
                                                                    Value="{StaticResource PrimaryBackground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames BeginTime="00:00:00"
                                                                       Duration="00:00:00"
                                                                       Storyboard.TargetName="BorderContent"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                            <DiscreteObjectKeyFrame KeyTime="00:00:00">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Thickness>2</Thickness>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames
                                            BeginTime="00:00:00"
                                            Storyboard.TargetName="PART_ContentView"
                                            Storyboard.TargetProperty="Foreground"
                                            Duration="00:00:00">
                                            <DiscreteObjectKeyFrame KeyTime="00:00:00" 
                                                                    Value="{StaticResource ContentForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames BeginTime="00:00:00"
                                                                       Duration="00:00:00"
                                                                       Storyboard.TargetName="PART_ExpanderView"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="00:00:00" 
                                                                    Value="{StaticResource PrimaryBackground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition MaxWidth="{TemplateBinding IndentationWidth}"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Border x:Name="BorderContent"  
                                Background="Transparent"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                BorderBrush="Transparent">
                            <Rectangle x:Name="PART_SelectionIndicator" Visibility="Collapsed"
                                    Margin="1,0,0,0"
                                    RadiusX="2" RadiusY="2"
                                    Fill="{StaticResource PrimaryBackground}"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"/>
                                </Border>
                        <Border x:Name="PART_CurrentItemBorder" 
                                CornerRadius="{StaticResource Windows11Light.ThemeCornerRadiusVariant1}"
                                Visibility="Collapsed">
                            <Border x:Name="PART_CurrentItemInnerBorder" 
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness1}" 
                                    CornerRadius="{StaticResource Windows11Light.ThemeCornerRadiusVariant1}"
                                    BorderBrush="{StaticResource SfTreeView.CurrentItem.InnerBorder.Static.Border}" Visibility="Collapsed"/>
                        </Border>
                        <Border x:Name="PART_HoverBorder" 
                                Background="{StaticResource ContentBackgroundHovered}"
                                BorderBrush="{StaticResource ContentBackgroundHovered}"
                                Margin="0 2 0 2"
                                Visibility="Collapsed"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                />
                        <StackPanel x:Name="PART_IndentContainer" 
                                    Panel.ZIndex="0"
                                    Orientation="Horizontal"
                                    Grid.Column="0">
                            <Rectangle x:Name="PART_IndentLine" 
                                    StrokeDashArray="2,2"
                                    HorizontalAlignment="Stretch"
                                    Visibility="Hidden">
                            </Rectangle>
                        </StackPanel>

                        <Grid x:Name="PART_LineGrid"
                              Grid.Column="1"
                              Panel.ZIndex="0"
                              Width="{TemplateBinding ExpanderWidth}"
                              Visibility="Hidden">
                            <Rectangle x:Name="PART_HorizontalLine" 
                                    StrokeDashArray="2,2"
                                    Margin="10,0,0,0"
                                    Width="10"
                                    VerticalAlignment="Center" />
                            <Rectangle x:Name="PART_VerticalLine" 
                                    StrokeDashArray="2,2"
                                    HorizontalAlignment="Stretch"/>
                        </Grid>

                        <ContentControl x:Name="PART_ExpanderView"
                                        Focusable="False"
                                        Width="{TemplateBinding ExpanderWidth}"
                                        Visibility="{Binding HasChildNodes, Converter={StaticResource boolToVisibilityConverter}}"
                                        ContentTemplate="{TemplateBinding ExpanderTemplate}">
                        </ContentControl>

                        <treeView:TreeNodeView x:Name="PART_ContentView" Grid.Column="2"
                                            VerticalAlignment="Center"
                                            Focusable="False"
                                            ContentTemplate="{TemplateBinding ItemTemplate}">
                            <treeView:TreeNodeView.Margin>
                                <Thickness>9,0,4,0</Thickness>
                            </treeView:TreeNodeView.Margin>
                        </treeView:TreeNodeView>

                        <Border x:Name="PART_DragLine" Grid.ColumnSpan="3" Visibility="Collapsed" BorderBrush="{StaticResource BorderAlt3}" />

                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="skinManager:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                            <Setter Property="Visibility" TargetName="PART_CurrentItemInnerBorder" Value="Visible"/>
                        </Trigger>
                        <Trigger Property="FullRowSelect" Value="True">
                            <Setter Property="Grid.Column" TargetName="BorderContent" Value="0"/>
                            <Setter Property="Grid.ColumnSpan" TargetName="BorderContent" Value="4"/>
                            <Setter Property="Grid.Column" TargetName="PART_CurrentItemBorder" Value="0"/>
                            <Setter Property="Grid.ColumnSpan" TargetName="PART_CurrentItemBorder" Value="4"/>
                            <Setter Property="Grid.Column" TargetName="PART_HoverBorder" Value="0"/>
                            <Setter Property="Grid.ColumnSpan" TargetName="PART_HoverBorder" Value="4"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="FullRowSelect" Value="False"/>
                                <Condition Property="ExpanderPosition" Value="Start"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Grid.Column" TargetName="BorderContent" Value="2"/>
                            <Setter Property="Grid.ColumnSpan" TargetName="BorderContent" Value="1"/>
                            <Setter Property="Grid.Column" TargetName="PART_CurrentItemBorder" Value="2"/>
                            <Setter Property="Grid.ColumnSpan" TargetName="PART_CurrentItemBorder" Value="1"/>
                            <Setter Property="Grid.Column" TargetName="PART_HoverBorder" Value="2"/>
                            <Setter Property="Grid.ColumnSpan" TargetName="PART_HoverBorder" Value="1"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="FullRowSelect" Value="False"/>
                                <Condition Property="ExpanderPosition" Value="End"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Grid.Column" TargetName="BorderContent" Value="0"/>
                            <Setter Property="Grid.ColumnSpan" TargetName="BorderContent" Value="3"/>
                            <Setter Property="Grid.Column" TargetName="PART_CurrentItemBorder" Value="0"/>
                            <Setter Property="Grid.ColumnSpan" TargetName="PART_CurrentItemBorder" Value="3"/>
                            <Setter Property="Grid.Column" TargetName="PART_HoverBorder" Value="0"/>
                            <Setter Property="Grid.ColumnSpan" TargetName="PART_HoverBorder" Value="3"/>
                        </MultiTrigger>
                        <Trigger Property="ExpanderPosition" Value="Start">
                            <Setter Property="Grid.Column" TargetName="PART_ExpanderView" Value="1"/>
                        </Trigger>
                        <Trigger Property="ExpanderPosition" Value="End">
                            <Setter Property="Grid.Column" TargetName="PART_ExpanderView" Value="3"/>
                        </Trigger>
                        <Trigger Property="ShowLines" Value="True">
                            <Setter Property="Visibility" TargetName="PART_LineGrid" Value="Visible"/>
                        </Trigger>
                        <DataTrigger Binding="{Binding ShowExpanderAnimation}"  Value="True">
                            <Setter Property="ContentTemplate" TargetName="PART_ExpanderView" Value="{StaticResource busyIndicatorTemplate}"/>
                        </DataTrigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="PART_ContentView" Property="Foreground" Value="{StaticResource HoveredForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTreeViewItemStyle}" TargetType="treeView:TreeViewItem"/>

    <Style x:Key="SyncfusionSfTreeViewStyle" TargetType="treeView:SfTreeView">
        <Setter Property="SelectionBackgroundColor" Value="{StaticResource ContentBackgroundSelected}" />
        <Setter Property="SelectionForegroundColor" Value="{StaticResource SelectedForeground}" />
        <Setter Property="FocusBorderColor" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="LineStroke" Value="{StaticResource BorderAlt1}" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="ExpanderTemplate" Value="{StaticResource SfTreeViewStartExpanderTemplate}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="treeView:SfTreeView">
                    <Grid x:Name="treeViewGrid" Background="Transparent">
                        <Border x:Name="PART_TreeViewBorder" Background="Transparent"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                BorderThickness="1"
                                SnapsToDevicePixels="True">
                            <ScrollViewer x:Name="PART_ScrollViewer" 
                                      Background="Transparent"
                                      CanContentScroll="True"
                                      IsTabStop="False" 
                                          FlowDirection="{TemplateBinding FlowDirection}"
                                          HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                          IsDeferredScrollingEnabled="{TemplateBinding ScrollViewer.IsDeferredScrollingEnabled}"
                                          PanningMode="{TemplateBinding ScrollViewer.PanningMode}"
                                          PanningRatio="{TemplateBinding ScrollViewer.PanningRatio}"
                                      VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}">
                                    <treeView:TreeNodeContainer x:Name="PART_TreeNodeContainer" 
                                                                Background="Transparent"/>
                            </ScrollViewer>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="ExpanderPosition" Value="End">
                            <Setter Property="ExpanderTemplate" Value="{StaticResource SfTreeViewEndExpanderTemplate}"/>
                        </Trigger> 
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="PART_TreeNodeContainer" Property="Background" Value="Transparent" />
                            <Setter TargetName="PART_TreeViewBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                        <Trigger Property="skinManager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="ItemHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfTreeViewStyle}" TargetType="treeView:SfTreeView" />

    <Style x:Key="SyncfusionTreeViewDragPreviewControlStyle" TargetType="treeView:TreeViewDragPreviewControl">
        <Setter Property="Background" Value="{StaticResource TooltipBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource TooltipBorder}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="treeView:TreeViewDragPreviewControl">
                    <Border
                        x:Name="PART_Border"
                        Padding="5"
                        Margin=" 14 0 14 14"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="1"
                        Effect="{StaticResource Default.ShadowDepth3}"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius4}">
                        <ItemsControl x:Name="PART_ItemsControl" Margin="2" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTreeViewDragPreviewControlStyle}" TargetType="treeView:TreeViewDragPreviewControl" />
    
</ResourceDictionary>
