using AirMonitor.Core.Models;

namespace AirMonitor.Core.Interfaces;

/// <summary>
/// 硬件指纹服务接口
/// 主程序和注册机共享的硬件指纹服务定义
/// </summary>
public interface IHardwareFingerprintService
{
    /// <summary>
    /// 获取当前机器的硬件指纹
    /// </summary>
    /// <returns>硬件指纹字符串</returns>
    Task<string> GetFingerprintAsync();

    /// <summary>
    /// 获取当前机器的硬件指纹（同步版本）
    /// </summary>
    /// <returns>硬件指纹字符串</returns>
    string GetFingerprint();

    /// <summary>
    /// 获取详细的硬件指纹信息
    /// </summary>
    /// <returns>硬件指纹信息对象</returns>
    Task<HardwareFingerprintInfo> GetDetailedFingerprintAsync();

    /// <summary>
    /// 获取CPU序列号
    /// </summary>
    /// <returns>CPU序列号</returns>
    Task<string> GetCpuIdAsync();

    /// <summary>
    /// 获取主板序列号
    /// </summary>
    /// <returns>主板序列号</returns>
    Task<string> GetMotherboardIdAsync();

    /// <summary>
    /// 获取MAC地址
    /// </summary>
    /// <returns>MAC地址</returns>
    Task<string> GetMacAddressAsync();

    /// <summary>
    /// 获取硬盘序列号
    /// </summary>
    /// <returns>硬盘序列号</returns>
    Task<string> GetDiskIdAsync();

    /// <summary>
    /// 获取计算机名称
    /// </summary>
    /// <returns>计算机名称</returns>
    string GetComputerName();

    /// <summary>
    /// 获取用户名
    /// </summary>
    /// <returns>用户名</returns>
    string GetUserName();

    /// <summary>
    /// 获取操作系统信息
    /// </summary>
    /// <returns>操作系统信息</returns>
    Task<string> GetOSInfoAsync();

    /// <summary>
    /// 验证硬件指纹是否匹配
    /// </summary>
    /// <param name="targetFingerprint">目标指纹</param>
    /// <returns>true表示匹配，false表示不匹配</returns>
    Task<bool> ValidateFingerprintAsync(string targetFingerprint);

    /// <summary>
    /// 生成硬件指纹
    /// </summary>
    /// <param name="cpuId">CPU序列号</param>
    /// <param name="motherboardId">主板序列号</param>
    /// <param name="macAddress">MAC地址（可选）</param>
    /// <returns>生成的硬件指纹</returns>
    Task<string> GenerateFingerprintAsync(string cpuId, string motherboardId, string? macAddress = null);

    /// <summary>
    /// 从远程机器获取硬件指纹（通过网络或其他方式）
    /// </summary>
    /// <param name="targetMachine">目标机器标识</param>
    /// <returns>远程机器的硬件指纹信息</returns>
    Task<HardwareFingerprintInfo?> GetRemoteFingerprintAsync(string targetMachine);

    /// <summary>
    /// 导出硬件指纹信息到文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="fingerprintInfo">硬件指纹信息</param>
    /// <returns>是否导出成功</returns>
    Task<bool> ExportFingerprintAsync(string filePath, HardwareFingerprintInfo fingerprintInfo);

    /// <summary>
    /// 从文件导入硬件指纹信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>硬件指纹信息</returns>
    Task<HardwareFingerprintInfo?> ImportFingerprintAsync(string filePath);

    /// <summary>
    /// 检查硬件环境是否发生变化
    /// </summary>
    /// <param name="originalFingerprint">原始指纹</param>
    /// <returns>变化检测结果</returns>
    Task<HardwareChangeResult> DetectHardwareChangesAsync(string originalFingerprint);
}

/// <summary>
/// 硬件变化检测结果
/// </summary>
public class HardwareChangeResult
{
    /// <summary>
    /// 是否发生变化
    /// </summary>
    public bool HasChanged { get; set; }

    /// <summary>
    /// 变化的组件列表
    /// </summary>
    public List<string> ChangedComponents { get; set; } = new();

    /// <summary>
    /// 变化详情
    /// </summary>
    public string Details { get; set; } = string.Empty;

    /// <summary>
    /// 检测时间
    /// </summary>
    public DateTime DetectedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 当前硬件指纹
    /// </summary>
    public string CurrentFingerprint { get; set; } = string.Empty;

    /// <summary>
    /// 原始硬件指纹
    /// </summary>
    public string OriginalFingerprint { get; set; } = string.Empty;
}
