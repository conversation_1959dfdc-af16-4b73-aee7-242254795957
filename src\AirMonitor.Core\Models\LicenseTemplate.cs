using System.Text.Json.Serialization;
using AirMonitor.Core.Enums;

namespace AirMonitor.Core.Models;

/// <summary>
/// 许可证模板模型
/// 用于保存和复用许可证生成配置
/// </summary>
public class LicenseTemplate
{
    /// <summary>
    /// 模板唯一标识
    /// </summary>
    [JsonPropertyName("templateId")]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// 模板名称
    /// </summary>
    [JsonPropertyName("templateName")]
    public string TemplateName { get; set; } = string.Empty;

    /// <summary>
    /// 模板描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 许可证类型
    /// </summary>
    [JsonPropertyName("licenseType")]
    public LicenseType LicenseType { get; set; }

    /// <summary>
    /// 授权功能列表
    /// </summary>
    [JsonPropertyName("authorizedFeatures")]
    public List<string> AuthorizedFeatures { get; set; } = new();

    /// <summary>
    /// 有效期天数（-1表示永久）
    /// </summary>
    [JsonPropertyName("validityDays")]
    public int ValidityDays { get; set; }

    /// <summary>
    /// 最大设备数量（-1表示无限制）
    /// </summary>
    [JsonPropertyName("maxDeviceCount")]
    public int MaxDeviceCount { get; set; } = -1;

    /// <summary>
    /// 是否为试用版模板
    /// </summary>
    [JsonPropertyName("isTrial")]
    public bool IsTrial { get; set; } = false;

    /// <summary>
    /// 试用天数
    /// </summary>
    [JsonPropertyName("trialDays")]
    public int TrialDays { get; set; } = 0;

    /// <summary>
    /// 默认部门名称
    /// </summary>
    [JsonPropertyName("defaultCustomerName")]
    public string DefaultCustomerName { get; set; } = string.Empty;

    /// <summary>
    /// 默认部门邮箱
    /// </summary>
    [JsonPropertyName("defaultCustomerEmail")]
    public string DefaultCustomerEmail { get; set; } = string.Empty;

    /// <summary>
    /// 是否为系统预设模板
    /// </summary>
    [JsonPropertyName("isSystemTemplate")]
    public bool IsSystemTemplate { get; set; } = false;

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 更新时间
    /// </summary>
    [JsonPropertyName("updatedAt")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建者
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 使用次数
    /// </summary>
    [JsonPropertyName("usageCount")]
    public int UsageCount { get; set; } = 0;

    /// <summary>
    /// 最后使用时间
    /// </summary>
    [JsonPropertyName("lastUsedAt")]
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// 根据模板创建许可证信息
    /// </summary>
    /// <param name="customerName">客户名称</param>
    /// <param name="customerEmail">客户邮箱</param>
    /// <param name="hardwareFingerprint">硬件指纹</param>
    /// <returns>许可证信息</returns>
    public LicenseInfo CreateLicenseInfo(string? customerName = null, string? customerEmail = null, string? hardwareFingerprint = null)
    {
        var now = DateTime.UtcNow;
        var licenseId = GenerateLicenseId();

        var license = new LicenseInfo
        {
            LicenseId = licenseId,
            ProductName = Constants.LicenseConstants.ProductName,
            ProductVersion = Constants.LicenseConstants.ProductVersion,
            CustomerName = customerName ?? DefaultCustomerName,
            CustomerEmail = customerEmail ?? DefaultCustomerEmail,
            LicenseType = LicenseType,
            AuthorizedFeatures = new List<string>(AuthorizedFeatures),
            HardwareFingerprint = hardwareFingerprint ?? string.Empty,
            IssuedDate = now,
            MaxDeviceCount = MaxDeviceCount,
            IsTrial = IsTrial,
            TrialDays = TrialDays,
            CreatedAt = now,
            UpdatedAt = now
        };

        // 设置过期日期
        if (ValidityDays == -1)
        {
            license.ExpiryDate = DateTime.MaxValue; // 永久许可证
        }
        else
        {
            license.ExpiryDate = now.AddDays(ValidityDays);
        }

        return license;
    }

    /// <summary>
    /// 生成许可证ID
    /// </summary>
    /// <returns>许可证ID</returns>
    private string GenerateLicenseId()
    {
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd");
        var random = new Random().Next(100000, 999999);
        return $"{Constants.LicenseConstants.LicenseIdPrefix}-{timestamp}-{random}";
    }

    /// <summary>
    /// 增加使用次数
    /// </summary>
    public void IncrementUsage()
    {
        UsageCount++;
        LastUsedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 获取模板摘要信息
    /// </summary>
    /// <returns>模板摘要</returns>
    public string GetSummary()
    {
        var validityText = ValidityDays == -1 ? "永久" : $"{ValidityDays}天";
        return $"{TemplateName} ({LicenseType}, {validityText}, {AuthorizedFeatures.Count}个功能)";
    }
}
