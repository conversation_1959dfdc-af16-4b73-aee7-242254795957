using AirMonitor.Core.Models;

namespace AirMonitor.Core.Interfaces;

/// <summary>
/// 加密服务接口
/// 主程序和注册机共享的加密服务定义
/// </summary>
public interface ICryptoService
{
    /// <summary>
    /// 生成RSA密钥对
    /// </summary>
    /// <param name="keySize">密钥大小（位）</param>
    /// <returns>密钥对（公钥，私钥）</returns>
    Task<(string PublicKey, string PrivateKey)> GenerateRSAKeyPairAsync(int keySize = 2048);

    /// <summary>
    /// 加密数据
    /// </summary>
    /// <param name="data">要加密的数据</param>
    /// <param name="publicKey">RSA公钥</param>
    /// <returns>加密后的数据</returns>
    Task<string> EncryptAsync(string data, string publicKey);

    /// <summary>
    /// 解密数据
    /// </summary>
    /// <param name="encryptedData">加密的数据</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <returns>解密后的数据</returns>
    Task<string> DecryptAsync(string encryptedData, string privateKey);

    /// <summary>
    /// 生成数字签名
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <returns>数字签名</returns>
    Task<string> SignAsync(string data, string privateKey);

    /// <summary>
    /// 验证数字签名
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">数字签名</param>
    /// <param name="publicKey">RSA公钥</param>
    /// <returns>true表示验证成功，false表示验证失败</returns>
    Task<bool> VerifySignatureAsync(string data, string signature, string publicKey);

    /// <summary>
    /// 验证许可证签名
    /// </summary>
    /// <param name="license">许可证信息</param>
    /// <param name="publicKey">RSA公钥</param>
    /// <returns>true表示验证成功，false表示验证失败</returns>
    Task<bool> VerifyLicenseSignatureAsync(LicenseInfo license, string publicKey);

    /// <summary>
    /// 计算哈希值
    /// </summary>
    /// <param name="data">要计算哈希的数据</param>
    /// <param name="algorithm">哈希算法（默认SHA256）</param>
    /// <returns>哈希值</returns>
    Task<string> ComputeHashAsync(string data, string algorithm = "SHA256");

    /// <summary>
    /// 生成随机盐值
    /// </summary>
    /// <param name="length">盐值长度</param>
    /// <returns>随机盐值</returns>
    string GenerateSalt(int length = 16);

    /// <summary>
    /// 使用密码派生密钥
    /// </summary>
    /// <param name="password">密码</param>
    /// <param name="salt">盐值</param>
    /// <param name="iterations">迭代次数</param>
    /// <param name="keyLength">密钥长度</param>
    /// <returns>派生的密钥</returns>
    Task<byte[]> DeriveKeyAsync(string password, byte[] salt, int iterations = 10000, int keyLength = 32);

    /// <summary>
    /// AES加密
    /// </summary>
    /// <param name="data">要加密的数据</param>
    /// <param name="key">AES密钥</param>
    /// <param name="iv">初始化向量</param>
    /// <returns>加密后的数据</returns>
    Task<string> AESEncryptAsync(string data, byte[] key, byte[] iv);

    /// <summary>
    /// AES解密
    /// </summary>
    /// <param name="encryptedData">加密的数据</param>
    /// <param name="key">AES密钥</param>
    /// <param name="iv">初始化向量</param>
    /// <returns>解密后的数据</returns>
    Task<string> AESDecryptAsync(string encryptedData, byte[] key, byte[] iv);

    /// <summary>
    /// 生成随机IV
    /// </summary>
    /// <param name="length">IV长度</param>
    /// <returns>随机IV</returns>
    byte[] GenerateIV(int length = 16);

    /// <summary>
    /// 安全比较两个字节数组
    /// </summary>
    /// <param name="a">字节数组A</param>
    /// <param name="b">字节数组B</param>
    /// <returns>true表示相等，false表示不相等</returns>
    bool SecureCompare(byte[] a, byte[] b);

    /// <summary>
    /// 清除敏感数据
    /// </summary>
    /// <param name="data">要清除的字节数组</param>
    void ClearSensitiveData(byte[] data);
}
