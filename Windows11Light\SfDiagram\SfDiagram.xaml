<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:diagram="clr-namespace:Syncfusion.UI.Xaml.Diagram;assembly=Syncfusion.SfDiagram.WPF"
                    xmlns:diagram_localControls="clr-namespace:Syncfusion.UI.Xaml.Diagram.Controls;assembly=Syncfusion.SfDiagram.WPF"
                    xmlns:diagram_panels="clr-namespace:Syncfusion.UI.Xaml.Diagram.Panels;assembly=Syncfusion.SfDiagram.WPF"
                    xmlns:diagram_util="clr-namespace:Syncfusion.UI.Xaml.Diagram.Utility;assembly=Syncfusion.SfDiagram.WPF"
                    xmlns:diagram_printing="clr-namespace:Syncfusion.UI.Xaml.Diagram.Controls;assembly=Syncfusion.SfDiagram.WPF"
                    xmlns:diagram_stencil="clr-namespace:Syncfusion.UI.Xaml.Diagram.Stencil;assembly=Syncfusion.SfDiagram.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ComboBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBlock.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ScrollViewer.xaml"/>
        <ResourceDictionary Source="/Syncfusion.SfDiagram.Wpf;component/Resources/BasicShapes.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Window.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Slider.xaml"/>
        <ResourceDictionary Source="/Syncfusion.SfDiagram.Wpf;Component/Themes/DefaultStyles.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!--Utilities-->
    <diagram_util:PathConverter x:Key="PathConverter"/>
    <diagram_util:NulltoVisibilityConverter x:Key="NulltoVisibilityConverter"/>
    <diagram_util:NullToVisibityConverter x:Key="NullToVisibityConverter"/>
    <diagram_util:NullToBooleanConverter x:Key="NullToBooleanConverter"/>
    <diagram_util:VisibilityToBoolConverter x:Key="VisibilityToBoolConverter"/>
    <diagram_util:OrthogonalControlPointToBooleanConverter x:Key="OrthogonalControlPointToBooleanConverter"/>
    <diagram_util:BezierControlPointToBooleanConverter x:Key="BezierControlPointToBooleanConverter"/>
    <diagram_util:EndControlPointToBooleanConverter x:Key="EndControlPointToBooleanConverter"/>
    <diagram_util:OverviewResizerControlPointToBooleanConverter x:Key="OverviewResizerControlPointToBooleanConverter"/>
    <diagram_util:QuickCommandControlPointToBooleanConverter x:Key="QuickCommandControlPointToBooleanConverter"/>
    <diagram_util:GridLengthToDoubleConverter x:Key="GridLengthToDoubleConverter"/>
    <diagram_util:OutlineVisibilityConverter x:Key="OutlineVisibilityConverter"/>

    <!--<Rotator>-->

    <!--<Pivot Thumb >-->

    <!--<BezierThumb>-->

    <!--Resizer Thumb-->

    <!--EndThumb-->

    <!--OrthogonalThumb-->

    <!--OverView Resizer-->

    <!--<Selector>-->

    <!--Ruler-->

    <!--<RuntimeConnectionIndicator>-->

    <!--<OverView>-->

    <Style TargetType="diagram:Container" BasedOn="{StaticResource SyncfusionContainerStyle}"/>

    <Style TargetType="diagram:BpmnGroup" BasedOn="{StaticResource SyncfusionBpmnGroupStyle}"/>

    <Style TargetType="diagram:BpmnNode" BasedOn="{StaticResource SyncfusionBpmnNodeStyle}"/>

    <Style TargetType="diagram:Node" BasedOn="{StaticResource SyncfusionNodeStyle}"/>

    <Style TargetType="diagram:PortBase" BasedOn="{StaticResource SyncfusionPortBaseStyle}"/>

    <Style TargetType="diagram:NodePort" BasedOn="{StaticResource SyncfusionNodePortStyle}"/>

    <Style TargetType="diagram:ConnectorPort" BasedOn="{StaticResource SyncfusionConnectorPortStyle}"/>

    <Style TargetType="diagram:BpmnFlow" BasedOn="{StaticResource SyncfusionBpmnFlowStyle}"/>

    <Style TargetType="diagram:Connector" BasedOn="{StaticResource SyncfusionConnectorStyle}"/>

    <Style TargetType="diagram:Group" BasedOn="{StaticResource SyncfusionGroupStyle}"/>

    <Style TargetType="diagram:Swimlane" BasedOn="{StaticResource SyncfusionSwimlaneStyle}"/>

    <Style TargetType="diagram:ContainerHeader" BasedOn="{StaticResource SyncfusionContainerHeaderStyle}"/>

    <Style TargetType="diagram:SwimlaneHeader" BasedOn="{StaticResource SyncfusionSwimlaneHeaderStyle}"/>

    <Style TargetType="diagram:Lane" BasedOn="{StaticResource SyncfusionLaneStyle}"/>

    <Style TargetType="diagram:Phase" BasedOn="{StaticResource SyncfusionPhaseStyle}"/>

    <Style TargetType="diagram:DockPort" BasedOn="{StaticResource SyncfusionDockPortStyle}"/>

    <Style TargetType="diagram:QuickCommand" BasedOn="{StaticResource SyncfusionQuickCommandStyle}"/>

    <Style x:Key="SyncfusionRotatorDiagramThumbStyle" TargetType="diagram_localControls:DiagramThumb">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_localControls:DiagramThumb">
                    <Border x:Name="thumb"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Viewbox Height="20" Width="20">
                            <Grid>
                                <Path x:Name="ellipse"
                                      Data="F1M23.467,11.733C23.467,18.213 18.214,23.466 11.734,23.466 5.253,23.466 0,18.213 0,11.733 0,5.253 5.253,0 11.734,0 18.214,0 23.467,5.253 23.467,11.733" 
                                      StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}">
                                    <Path.Fill>
                                        <SolidColorBrush x:Name="ellipseFill" Color="{StaticResource PrimaryBackground.Color}"/>
                                    </Path.Fill>
                                    <Path.Stroke>
                                        <SolidColorBrush x:Name="ellipseStroke" Color="{StaticResource ContentBackground.Color}"/>
                                    </Path.Stroke>
                                </Path>
                                <Path x:Name="icon"
                                      Width="14"                                      
                                      Height="12"
                                      Margin="2.632,3.515,4.552,4.485"
                                      Data="M6.8570004,0 C8.3210011,0.035999242 9.5359993,0.5359993 10.5,1.5000002 11.464001,2.4639999 11.964001,3.6790003 12,5.1430003 11.964001,6.607001 11.464001,7.8220012 10.5,8.786 9.5359993,9.7500007 8.3210011,10.250002 6.8570004,10.286001 6.2679996,10.286001 5.7049999,10.192001 5.1700001,10.005001 4.6340008,9.8170001 4.151001,9.5539996 3.7240009,9.2140014 L4.526001,8.411 C5.2049999,8.8750007 5.9820004,9.1159999 6.8570004,9.1339995 8,9.1159999 8.9460011,8.732001 9.6960011,7.982001 10.445999,7.232001 10.830002,6.286 10.848999,5.1430003 10.830002,4.0000007 10.445999,3.0540003 9.6960011,2.3040001 8.9460011,1.5540001 8,1.1700001 6.8570004,1.1519995 5.7140007,1.1700001 4.7679996,1.5540001 4.0179996,2.3040001 3.2679996,3.0540003 2.8840008,4.0000007 2.8659992,5.1430003 L4.5799999,5.1430003 2.276001,7.4200007 0,5.1430003 1.7140007,5.1430003 C1.75,3.6790003 2.25,2.4639999 3.2140007,1.5000002 4.1790009,0.5359993 5.3929996,0.035999242 6.8570004,0 z"                                      
                                      Stretch="Fill"
                                      UseLayoutRounding="False">
                                    <Path.Fill>
                                        <SolidColorBrush x:Name="IconFill" Color="{StaticResource PrimaryForeground.Color}"/>
                                    </Path.Fill>
                                </Path>
                            </Grid>
                        </Viewbox>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="ThumbStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorLight1.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="IconFill"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource PrimaryForeground.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorDark1.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="IconFill"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource PrimaryForeground.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundAlt2.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="IconFill"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource IconColorDisabled.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionPivotDiagramThumbStyle" TargetType="diagram_localControls:DiagramThumb">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_localControls:DiagramThumb">
                    <Border x:Name="thumb"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Width="25"
                            Height="25"
                            ToolTipService.ToolTip="{Binding ToolTip,RelativeSource={RelativeSource Mode=TemplatedParent}}"
                            RenderTransformOrigin="0.5,0.5"
                            Opacity="0.6">
                        <Border.RenderTransform>
                            <ScaleTransform/>
                        </Border.RenderTransform>
                        <Grid>
                            <Ellipse Name="OuterEllipse"
                                     Stroke="Transparent"
                                     StrokeThickness="8">
                                <Ellipse.Fill>
                                    <SolidColorBrush x:Name="OuterEllipseFill" Color="{StaticResource PrimaryBackground.Color}"/>
                                </Ellipse.Fill>
                            </Ellipse>
                            <Ellipse Name="ellipse"
                                     Margin="5" >
                                <Ellipse.Fill>
                                    <SolidColorBrush x:Name="ellipseFill" Color="{StaticResource PrimaryBackground.Color}"/>
                                </Ellipse.Fill>
                                <Ellipse.Stroke>
                                    <SolidColorBrush x:Name="ellipseStroke" Color="{StaticResource ContentBackground.Color}"/>
                                </Ellipse.Stroke>
                            </Ellipse>
                            <Ellipse Margin="13"
                                     Fill="White"
                                     Stroke="#FF777F85"
                                     StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}" />
                        </Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="ThumbStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill" 
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorLight1.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke" 
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipseFill"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorLight1.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="thumb"
                                                         Storyboard.TargetProperty="Opacity" 
                                                         To="0.6"
                                                         Duration="0"/>
                                        <DoubleAnimation Storyboard.TargetName="thumb"
                                                         Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                         To="1"
                                                         Duration="0"/>
                                        <DoubleAnimation Storyboard.TargetName="thumb"
                                                         Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                         To="1" 
                                                         Duration="0"/>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill" 
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorDark1.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke" 
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipseFill"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorDark1.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill" 
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundAlt2.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke" 
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipseFill"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundAlt2.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <DoubleAnimation Storyboard.TargetName="thumb"
                                                         Storyboard.TargetProperty="Opacity" 
                                                         To="1"
                                                         Duration="0"/>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="diagram:Selector" x:Key="SyncfusionSelectorStyle">
        <Setter Property="OffsetX"
                Value="{Binding OffsetX , Mode=TwoWay}"/>
        <Setter Property="OffsetY" 
                Value="{Binding OffsetY , Mode=TwoWay}"/>
        <Setter Property="RotateAngle" 
                Value="{Binding RotateAngle , Mode=TwoWay}"/>
        <Setter Property="UnitWidth" 
                Value="{Binding UnitWidth , Mode=TwoWay}"/>
        <Setter Property="UnitHeight" 
                Value="{Binding UnitHeight , Mode=TwoWay}"/>
        <Setter Property="Pivot" 
                Value="{Binding Pivot , Mode=TwoWay}"/>
        <Setter Property="SelectorConstraints" 
                Value="{Binding SelectorConstraints , Mode=TwoWay}"/>
        <Setter Property="TooltipTemplate">
            <Setter.Value>
                <DataTemplate>
                    <Grid Background="{StaticResource TooltipBackground}">
                        <Border x:Name="border"
                                CornerRadius="{StaticResource Windows11Light.ThemeCornerRadiusVariant1}"
                                Background="{StaticResource TooltipBackground}"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                BorderBrush="{StaticResource TooltipBorder}"
                                Padding="5,3,5,3">
                            <TextBlock x:Name="textBlock"
                                       Text="{Binding TooltipContent}"
                                       Foreground="{StaticResource ContentForeground}"
                                       FontSize="{StaticResource Windows11Light.CaptionText}"
                                       FontWeight="{StaticResource Windows11Light.FontWeightNormal}"
                                       FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"/>
                        </Border>
                    </Grid>
                    <DataTemplate.Triggers>
                        <DataTrigger Binding="{Binding Path=SelectorHandleDisplayMode, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram:SfDiagram}}" Value="Compact">
                            <Setter TargetName="border" Property="CornerRadius" Value="5"/>
                            <Setter TargetName="textBlock" Property="Margin" Value="5"/>
                        </DataTrigger>
                    </DataTemplate.Triggers>
                </DataTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram:Selector">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <diagram_panels:SelectorPanel x:Name="PART_SelectorPanel">
                            <diagram_panels:SelectorPanel.Resources>
                                <Style x:Key="pathStyle" TargetType="Shape">
                                    <Setter Property="StrokeThickness" Value="1.5"/>
                                    <Setter Property="Stroke" Value="{StaticResource Black}"/>
                                </Style>
                            </diagram_panels:SelectorPanel.Resources>
                            <Grid x:Name="PART_ToolTipGrid"
                                  Background="Transparent"
                                  Visibility="{TemplateBinding TooltipVisibility}"
                                  VerticalAlignment="Center" 
                                  HorizontalAlignment="Center">
                                <ContentPresenter Content="{Binding}"
                                                  ContentTemplate="{TemplateBinding TooltipTemplate}"/>
                            </Grid>
                            <ContentPresenter Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"/>
                            <Rectangle x:Name="Part_Rectangle"
                                       Style="{StaticResource pathStyle}"
                                       Stretch="Fill"
                                       StrokeDashArray="2, 2" />
                            <Line x:Name="PART_Line"
                                  X1="0"
                                  X2="0" 
                                  Y1="0"
                                  Y2="0" 
                                  Visibility="{Binding SelectorConstraints, Converter={StaticResource NulltoVisibilityConverter},ConverterParameter=Rotator}"
                                  StrokeDashArray="2, 2"
                                  Style="{StaticResource pathStyle}"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_TopLeft" 
                                                                ControlPointType="TopLeftResizer"
                                                                HorizontalAlignment="Left"
                                                                VerticalAlignment="Top"
                                                                Visibility="{Binding SelectorConstraints, Converter={StaticResource NulltoVisibilityConverter},ConverterParameter=Resize}"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_Top"
                                                                ControlPointType="TopResizer"
                                                                HorizontalAlignment="Center"
                                                                VerticalAlignment="Top" 
                                                                Visibility="{Binding SelectorConstraints, Converter={StaticResource NulltoVisibilityConverter},ConverterParameter=Resize}"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_TopRight"
                                                                ControlPointType="TopRightResizer"
                                                                HorizontalAlignment="Right" 
                                                                VerticalAlignment="Top"
                                                                Visibility="{Binding SelectorConstraints, Converter={StaticResource NulltoVisibilityConverter},ConverterParameter=Resize}"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_Left"
                                                                ControlPointType="LeftResizer"
                                                                HorizontalAlignment="Left"
                                                                VerticalAlignment="Center"
                                                                Visibility="{Binding SelectorConstraints, Converter={StaticResource NulltoVisibilityConverter},ConverterParameter=Resize}" />
                            <diagram_localControls:DiagramThumb x:Name="PART_Right"
                                                                ControlPointType="RightResizer" 
                                                                HorizontalAlignment="Right"
                                                                VerticalAlignment="Center" 
                                                                Visibility="{Binding SelectorConstraints, Converter={StaticResource NulltoVisibilityConverter},ConverterParameter=Resize}"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_BottomLeft"
                                                                ControlPointType="BottomLeftResizer"
                                                                HorizontalAlignment="Left"
                                                                VerticalAlignment="Bottom"
                                                                Visibility="{Binding SelectorConstraints, Converter={StaticResource NulltoVisibilityConverter},ConverterParameter=Resize}"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_Bottom" 
                                                                ControlPointType="BottomResizer"
                                                                HorizontalAlignment="Center"
                                                                VerticalAlignment="Bottom"
                                                                Visibility="{Binding SelectorConstraints, Converter={StaticResource NulltoVisibilityConverter},ConverterParameter=Resize}"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_BottomRight"
                                                                ControlPointType="BottomRightResizer"
                                                                HorizontalAlignment="Right"
                                                                VerticalAlignment="Bottom" 
                                                                Visibility="{Binding SelectorConstraints, Converter={StaticResource NulltoVisibilityConverter},ConverterParameter=Resize}"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_Pivot"
                                                                Style="{StaticResource SyncfusionPivotDiagramThumbStyle}" 
                                                                ControlPointType="Pivot"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_Rotator"
                                                                Style="{StaticResource SyncfusionRotatorDiagramThumbStyle}"
                                                                Canvas.Top="-50"
                                                                ControlPointType="Rotator"
                                                                Visibility="{Binding SelectorConstraints, Converter={StaticResource NulltoVisibilityConverter},ConverterParameter=Rotator}" />
                        </diagram_panels:SelectorPanel>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="diagram:Selector" BasedOn="{StaticResource SyncfusionSelectorStyle}"/>

    <Style TargetType="diagram_localControls:DiagramThumb" x:Key="SyncfusionDiagramThumbStyle">
        <Setter Property="IsTabStop" 
                Value="False"/>
        <Setter Property="Focusable" 
                Value="False"/>
        <Setter Property="Shape"
                Value="{StaticResource Rectangle}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_localControls:DiagramThumb">
                    <Border x:Name="thumb"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            Opacity="0.6"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            RenderTransformOrigin="0.5,0.5">
                        <Border.RenderTransform>
                            <ScaleTransform></ScaleTransform>
                        </Border.RenderTransform>
                        <Grid>
                            <Rectangle Fill="Transparent" 
                                       Width="25"
                                       Height="25" />
                            <Path x:Name="ellipse" 
                                  Width="8"
                                  Height="8"
                                  StrokeThickness="1.1"
                                  Stretch="Fill">
                                <Path.Data>
                                    <PathGeometry>M358.5,440.5C358.5,443.813,361.187,446.5,364.5,446.5C367.813,446.5,370.5,443.813,370.5,440.5C370.5,437.187,367.813,434.5,364.5,434.5C361.187,434.5,358.5,437.187,358.5,440.5z</PathGeometry>
                                </Path.Data>
                                <Path.Fill>
                                    <SolidColorBrush x:Name="ellipseFill" Color="{StaticResource PrimaryBackground.Color}"/>
                                </Path.Fill>
                                <Path.Stroke>
                                    <SolidColorBrush x:Name="ellipseStroke" Color="{StaticResource ContentBackground.Color}"/>
                                </Path.Stroke>
                            </Path>
                        </Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="ThumbStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorLight1.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorLight2.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundAlt2.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke"
                                                                      Storyboard.TargetProperty="Color">
                                            <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding RelativeSource={RelativeSource Self}, Path=ControlPointType, Converter={StaticResource OrthogonalControlPointToBooleanConverter}}" Value="True">
                <Setter Property="Shape" 
                        Value="{StaticResource Rectangle}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="diagram_localControls:DiagramThumb">
                            <Border x:Name="thumb"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Width="8"
                                    Height="8"
                                    RenderTransformOrigin="0.5,0.5">
                                <Border.RenderTransform>
                                    <ScaleTransform />
                                </Border.RenderTransform>
                                <Grid>
                                    <Border Background="Transparent">
                                        <Path x:Name="ellipse"
                                              Data="{TemplateBinding Shape, Converter={StaticResource PathConverter}}" 
                                              Stretch="Fill" >
                                            <Path.Fill>
                                                <SolidColorBrush x:Name="ellipseFill" Color="{StaticResource ContentBackground.Color}"/>
                                            </Path.Fill>
                                            <Path.Stroke>
                                                <SolidColorBrush x:Name="ellipseStroke" Color="{StaticResource BorderAlt4.Color}"/>
                                            </Path.Stroke>
                                        </Path>
                                    </Border>
                                </Grid>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="ThumbStates">
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="MouseOver">
                                            <Storyboard>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill" 
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke"
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource BorderAlt1.Color}"/>
                                                </ColorAnimationUsingKeyFrames>                                                
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <Storyboard>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill" 
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke"
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource BorderAlt1.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Disabled">
                                            <Storyboard>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill" 
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke"
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource White.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </DataTrigger>

            <DataTrigger Binding="{Binding RelativeSource={RelativeSource Self}, Path=ControlPointType, Converter={StaticResource BezierControlPointToBooleanConverter}}" Value="True">
                <Setter Property="Shape" 
                        Value="{StaticResource Rectangle}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="diagram_localControls:DiagramThumb">
                            <Border x:Name="thumb"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}" 
                                    RenderTransformOrigin="0.5,0.5">
                                <Border.RenderTransform>
                                    <ScaleTransform />
                                </Border.RenderTransform>
                                <Grid>
                                    <Rectangle Fill="Transparent"
                                               Width="23" 
                                               Height="23"/>
                                    <Path x:Name="ellipse"
                                          Data="{TemplateBinding Shape, Converter={StaticResource PathConverter}}" 
                                          Height="8"
                                          Width="8"
                                          Stretch="Fill"
                                          StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}">
                                        <Path.Fill>
                                            <SolidColorBrush x:Name="ellipseFill" Color="{StaticResource ContentBackground.Color}"/>
                                        </Path.Fill>
                                        <Path.Stroke>
                                            <SolidColorBrush x:Name="ellipseStroke" Color="{StaticResource BorderAlt1.Color}"/>

                                        </Path.Stroke>
                                    </Path>
                                </Grid>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="ThumbStates">
                                        <VisualState x:Name="Pressed">
                                            <Storyboard>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill" 
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke"
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource BorderAlt1.Color}"/>
                                                </ColorAnimationUsingKeyFrames>                                                
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="MouseOver">
                                            <Storyboard>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill" 
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke"
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource BorderAlt1.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Disabled">
                                            <Storyboard>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill" 
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke"
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource White.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </DataTrigger>

            <DataTrigger Binding="{Binding RelativeSource={RelativeSource Self}, Path=ControlPointType, Converter={StaticResource EndControlPointToBooleanConverter}}" Value="True">
                <Setter Property="Shape" 
                        Value="{StaticResource Ellipse}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="diagram_localControls:DiagramThumb">
                            <Border x:Name="thumb"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}" 
                                    RenderTransformOrigin="0.5,0.5">
                                <Border.RenderTransform>
                                    <ScaleTransform />
                                </Border.RenderTransform>
                                <Grid>
                                    <Rectangle Fill="Transparent" 
                                               Width="25" 
                                               Height="25"/>
                                    <Path x:Name="ellipse" 
                                          Data="{TemplateBinding Shape, Converter={StaticResource PathConverter}}" 
                                          Height="8"
                                          Width="8"
                                          Stretch="Fill"
                                          StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}">
                                        <Path.Fill>
                                            <SolidColorBrush x:Name="ellipseFill" Color="{StaticResource PrimaryBackground.Color}"/>
                                        </Path.Fill>
                                        <Path.Stroke>
                                            <SolidColorBrush x:Name="ellipseStroke" Color="{StaticResource ContentBackground.Color}"/>
                                        </Path.Stroke>
                                    </Path>
                                </Grid>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="ThumbStates">
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="MouseOver">
                                            <Storyboard>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill"
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorLight1.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke" 
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <Storyboard>
                                                <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                                                                 Storyboard.TargetName="ellipse"
                                                                 To="0.9"
                                                                 Duration="0" />
                                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"                                                                 
                                                                 Storyboard.TargetName="ellipse"
                                                                 To="1.5" 
                                                                 Duration="0" />
                                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                                 Storyboard.TargetName="ellipse"
                                                                 To="1.5"
                                                                 Duration="0" />
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill"
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorLight2.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke" 
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Disabled">
                                            <Storyboard>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseFill"
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundAlt2.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                                <ColorAnimationUsingKeyFrames Storyboard.TargetName="ellipseStroke" 
                                                                              Storyboard.TargetProperty="Color">
                                                    <DiscreteColorKeyFrame KeyTime="0" Value="{StaticResource ContentBackground.Color}"/>
                                                </ColorAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </DataTrigger>

            <DataTrigger Binding="{Binding RelativeSource={RelativeSource Self}, Path=Name, Converter={StaticResource OverviewResizerControlPointToBooleanConverter}}" Value="True">
                <Setter Property="IsTabStop" 
                        Value="false"/>
                <Setter Property="Shape"
                        Value="{StaticResource Ellipse}"/>
                <Setter Property="ShapeStyle">
                    <Setter.Value>
                        <Style TargetType="Path">
                            <Setter Property="Stroke" 
                                    Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="StrokeThickness" 
                                    Value="{StaticResource Windows11Light.StrokeThickness2}"/>
                        </Style>
                    </Setter.Value>
                </Setter>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="diagram_localControls:DiagramThumb">
                            <Path x:Name="PART_ResizerBorder1"
                                  Data="{TemplateBinding Shape, Converter={StaticResource PathConverter}}"
                                  Style="{TemplateBinding ShapeStyle}"
                                  Width="{TemplateBinding Width}"
                                  Height="{TemplateBinding Height}"
                                  HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                  VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                  Margin="{TemplateBinding Margin}"
                                  Stretch="Fill"/>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="diagram_localControls:DiagramThumb" BasedOn="{StaticResource SyncfusionDiagramThumbStyle}"/>

    <Style TargetType="diagram_localControls:Ruler" x:Key="SyncfusionRulerStyle">
        <Setter Property="Background"
                Value="{StaticResource ContentBackgroundAlt2}"/>
        <Setter Property="BorderThickness"
                Value="0,0,1,1"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForegroundAlt1}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.CaptionText}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_localControls:Ruler">
                    <Border BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Background="{TemplateBinding Background}">
                            <Grid>
                                <Canvas x:Name="Part_RulerPanel" />
                                <Canvas x:Name="Part_RulerPanelOverlay">
                                    <Line x:Name="Part_PositionIndicatorLine"
                                            Stroke="{StaticResource PrimaryBackground}"
                                            StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}"/>
                                </Canvas>
                            </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="diagram_localControls:Ruler" BasedOn="{StaticResource SyncfusionRulerStyle}"/>

    <Style TargetType="diagram_localControls:RulerSegment" x:Key="SyncfusionRulerSegmentStyle">
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_localControls:RulerSegment">
                    <Canvas x:Name="Part_RulerSegmentPanel">
                        <Canvas.Resources>
                            <diagram_localControls:LabelConverter x:Key="LabelConverter"/>
                            <Style TargetType="Line">
                                <Setter Property="Stroke" Value="{StaticResource BorderAlt1}"/>
                            </Style>
                        </Canvas.Resources>
                        <TextBlock x:Name="PART_Label" 
                                   Foreground="{StaticResource ContentForegroundAlt1}"
                                   FontSize="{StaticResource Windows11Light.CaptionText}"
                                   FontWeight="{StaticResource Windows11Light.FontWeightNormal}"
                                   FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                   Text="{Binding Path=StartValue, RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource LabelConverter}}"/>
                    </Canvas>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="diagram_localControls:RulerSegment" BasedOn="{StaticResource SyncfusionRulerSegmentStyle}"/>

    <Style TargetType="diagram:SfDiagram" x:Key="SyncfusionSfDiagramStyle">
        <Setter Property="FontFamily" 
                Value="{StaticResource SfDiagram.Static.FontFamily}"/>
        <Setter Property="FontWeight" 
                Value="{StaticResource SfDiagram.Static.FontWeight}"/>
        <Setter Property="BorderThickness"
                Value="{StaticResource Windows11Light.BorderThickness1}"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource TooltipBorder}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram:SfDiagram">
                    <Border Background="Transparent"
                            BorderBrush="{TemplateBinding BorderBrush}" 
                            BorderThickness="{TemplateBinding BorderThickness}"
                            diagram_util:FocusUtility.FocusOnLoad="False">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition/>
                            </Grid.ColumnDefinitions>
                            <!--<Ruler/>-->
                            <Canvas x:Name="InVisibleCanvas"
                                    Background="Transparent"
                                    Width="0"
                                    Height="0"/>
                            <ContentPresenter x:Name="PART_Preview"
                                              Grid.RowSpan="2"
                                              Grid.ColumnSpan="2"                                              
                                              HorizontalAlignment="Left"
                                              VerticalAlignment="Top"/>
                            <Rectangle Fill="{StaticResource ContentBackgroundAlt2}"/>
                            <ContentPresenter Grid.Row="0" 
                                              Grid.Column="1" 
                                              Content="{TemplateBinding HorizontalRuler}"/>
                            <ContentPresenter Grid.Row="1"
                                              Grid.Column="0"
                                              Content="{TemplateBinding VerticalRuler}"/>
                            <Rectangle x:Name="PART_Background"
                                       Grid.Row="1"
                                       Grid.Column="1"
                                       Fill="{TemplateBinding Background}"/>
                            <diagram_localControls:ScrollViewer x:Name="Part_Scrollviewer"
                                                                Grid.Row="1" 
                                                                Grid.Column="1"
                                                                Page="{TemplateBinding Page}">
                            </diagram_localControls:ScrollViewer>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Background"
                Value="{StaticResource White}"/>
        <Setter Property="FirstSelectionIndicatorStyle">
            <Setter.Value>
                <Style TargetType="Shape">
                    <Setter Property="Stroke" 
                            Value="#FF8CC63F" />
                    <Setter Property="StrokeThickness" 
                            Value="3"/>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="NodeSelectionIndicatorStyle">
            <Setter.Value>
                <Style TargetType="Rectangle">
                    <Setter Property="Stroke" 
                            Value="#FF8CC63F" />
                    <Setter Property="StrokeThickness" 
                            Value="1"/>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="ConnectorSelectionIndicatorStyle">
            <Setter.Value>
                <Style TargetType="Path">
                    <Setter Property="Stroke" 
                            Value="#FF8CC63F" />
                    <Setter Property="StrokeThickness" 
                            Value="1"/>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="NodeDropIndicatorStyle">
            <Setter.Value>
                <Style TargetType="Rectangle">
                    <Setter Property="Stroke" 
                            Value="Green"/>
                    <Setter Property="StrokeThickness" 
                            Value="1"/>
                    <Setter Property="StrokeDashArray" 
                            Value="2,2"/>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="BpmnGroupDropIndicatorStyle">
            <Setter.Value>
                <Style TargetType="Rectangle">
                    <Setter Property="Stroke" 
                            Value="Green"/>
                    <Setter Property="StrokeThickness" 
                            Value="2"/>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="ConnectorDropIndicatorStyle">
            <Setter.Value>
                <Style TargetType="Path">
                    <Setter Property="Stroke" 
                            Value="Green"/>
                    <Setter Property="StrokeThickness" 
                            Value="2"/>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="LaneDropIndicatorStyle">
            <Setter.Value>
                <Style TargetType="Rectangle">
                    <Setter Property="Stroke" 
                            Value="#FF8CC63F"/>
                    <Setter Property="StrokeThickness" 
                            Value="2"/>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="ConnectionIndicatorStyle">
            <Setter.Value>
                <Style TargetType="Path">
                    <Setter Property="Stroke" 
                            Value="#FF0dc923"/>
                    <Setter Property="StrokeThickness"
                            Value="1.5"/>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="diagram:SfDiagram" BasedOn="{StaticResource SyncfusionSfDiagramStyle}"/>

    <Style TargetType="diagram_localControls:Overview" x:Key="SyncfusionOverviewStyle">
        <Setter Property="HorizontalContentAlignment"
                Value="Center"/>
        <Setter Property="VerticalContentAlignment"
                Value="Center"/>
        <Setter Property="Background"
                Value="Transparent"/>
        <Setter Property="FocusBrush"
                Value="{StaticResource ContentBackgroundHovered}"/>
        <Setter Property="UnFocusBrush" 
                Value="{StaticResource Overlay}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_localControls:Overview">
                    <Grid Margin="5"
                            Effect="{StaticResource Default.ShadowDepth2}">
                        <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalAlignment}"
                            Padding="{TemplateBinding Padding}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Grid HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                    <diagram_panels:OverviewCustomPanel x:Name="PART_CustomPanel"
                                                                        Grid.Column="0"
                                                                        HorizontalAlignment="Center"
                                                                        VerticalAlignment="Center">
                                        <Grid HorizontalAlignment="Stretch" 
                                              VerticalAlignment="Stretch">
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=VpOffsetY, Converter={StaticResource GridLengthToDoubleConverter}}"/>
                                                <RowDefinition Height="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=WindowHeight, Converter={StaticResource GridLengthToDoubleConverter}}"/>
                                                <RowDefinition Height="*"/>
                                            </Grid.RowDefinitions>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=VpOffsetX, Converter={StaticResource GridLengthToDoubleConverter}}"/>
                                                <ColumnDefinition Width="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=WindowWidth, Converter={StaticResource GridLengthToDoubleConverter}}"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Rectangle Grid.Row="0"
                                               Grid.ColumnSpan="3"
                                               Fill="{TemplateBinding UnFocusBrush}" 
                                               SnapsToDevicePixels="True"/>
                                            <Rectangle Grid.Row="1"
                                               Fill="{TemplateBinding UnFocusBrush}"
                                               SnapsToDevicePixels="True"/>
                                            <Rectangle Grid.Row="1" 
                                               Grid.Column="2"
                                               Fill="{TemplateBinding UnFocusBrush}"
                                               SnapsToDevicePixels="True"/>
                                            <Rectangle Grid.Row="2"
                                               Grid.ColumnSpan="3"
                                               Fill="{TemplateBinding UnFocusBrush}" 
                                               SnapsToDevicePixels="True"/>
                                            <Rectangle Grid.Row="1"
                                               Grid.Column="1"
                                               Fill="{TemplateBinding FocusBrush}"
                                               SnapsToDevicePixels="True"/>
                                        </Grid>
                                        <Grid x:Name="PART_Grid" 
                                      HorizontalAlignment="Left" 
                                      VerticalAlignment="Top">
                                            <Path x:Name="PART_PathPreview"
                                          Style="{Binding RelativeSource={RelativeSource TemplatedParent},Path=OutlineSettings.OutlineStyle}" 
                                          Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Source.Constraints, Converter={StaticResource OutlineVisibilityConverter}, ConverterParameter=PART_PathPreview}" />
                                            <Grid.Background>
                                                <VisualBrush x:Name="PART_Preview"
                                                     Stretch="Uniform"
                                                     Visual="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=ScrollContentTarget}"/>
                                            </Grid.Background>
                                        </Grid>
                                        <Rectangle x:Name="PART_RubberBandRectangle" 
                                           HorizontalAlignment="Left" 
                                           VerticalAlignment="Top" 
                                           Fill="Transparent" 
                                           Stroke="{StaticResource PrimaryBackground}" 
                                           StrokeDashArray="2,2" 
                                           StrokeThickness="1"/>
                                        <Rectangle x:Name="PART_ResizerPreview"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Top"
                                           RenderTransform="{TemplateBinding ViewportPosition}" 
                                           Fill="Transparent" 
                                           Stroke="{StaticResource PrimaryBackground}" 
                                           StrokeDashArray="2,2" 
                                           Width="{TemplateBinding VpWidth}" 
                                           Height="{TemplateBinding VpHeight}"/>
                                        <diagram_localControls:OverviewResizer x:Name="Part_OverviewResizer"
                                                                           HorizontalAlignment="Left"
                                                                           VerticalAlignment="Top">
                                            <diagram_localControls:OverviewResizer.Content>
                                                <Grid>
                                                    <Thumb x:Name="PART_DragResizer">
                                                        <Thumb.Style>
                                                            <Style TargetType="Thumb">
                                                                <Setter Property="Template">
                                                                    <Setter.Value>
                                                                        <ControlTemplate>
                                                                            <Border Background="Transparent"/>
                                                                        </ControlTemplate>
                                                                    </Setter.Value>
                                                                </Setter>
                                                            </Style>
                                                        </Thumb.Style>
                                                    </Thumb>
                                                </Grid>
                                            </diagram_localControls:OverviewResizer.Content>
                                        </diagram_localControls:OverviewResizer>
                                    </diagram_panels:OverviewCustomPanel>
                                </Grid>
                                <Grid x:Name ="PART_ZoomPanel"  
                                      Grid.Column="1"
                                      Focusable="False"
                                      Width="40"
                                      Margin="0,8,0,8"
                                      Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=ShowZoomSlider, Converter={StaticResource VisibilityToBoolConverter}}">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Button x:Name="PART_PlusZoomButton"
                                            Grid.Row="0" 
                                            Width="24"
                                            Height="24"    
                                            Focusable="False"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Style="{StaticResource WPFGlyphButtonStyle}">
                                        <Button.Margin>
                                            <Thickness>0,0,0,0</Thickness>
                                        </Button.Margin>
                                        <Path Width="15" 
                                              Height="15"
                                              Stretch="Fill"
                                              IsHitTestVisible="false"
                                              HorizontalAlignment="Center" 
                                              VerticalAlignment="Center"
                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}">
                                            <Path.Data>
                                                <PathGeometry>M7.5 1C3.91015 1 1 3.91015 1 7.5C1 11.0899 3.91015 14 7.5 14C11.0899 14 14 11.0899 14 7.5C14 3.91015 11.0899 1 7.5 1ZM0 7.5C0 3.35786 3.35786 0 7.5 0C11.6421 0 15 3.35786 15 7.5C15 11.6421 11.6421 15 7.5 15C3.35786 15 0 11.6421 0 7.5ZM7.5 4C7.77614 4 8 4.22386 8 4.5V7H10.5C10.7761 7 11 7.22386 11 7.5C11 7.77614 10.7761 8 10.5 8H8V10.5C8 10.7761 7.77614 11 7.5 11C7.22386 11 7 10.7761 7 10.5V8H4.5C4.22386 8 4 7.77614 4 7.5C4 7.22386 4.22386 7 4.5 7H7V4.5C7 4.22386 7.22386 4 7.5 4Z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </Button>
                                    <Slider x:Name="PART_ZoomSlider"
                                            Margin="0,2"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Style="{StaticResource WPFSliderStyle}"
                                            Grid.Row="1"
                                            Focusable="False"
                                            Orientation="Vertical"
                                            IsMoveToPointEnabled="True">
                                    </Slider>
                                    <Button x:Name="PART_MinusZoomButton" 
                                            Grid.Row="2" 
                                            Width="24"
                                            Height="24"
                                            Focusable="False"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Style="{StaticResource WPFGlyphButtonStyle}">
                                        <Button.Margin>
                                            <Thickness>0,0,0,0</Thickness>
                                        </Button.Margin>
                                        <Path Width="14" 
                                              Height="14" 
                                              Stretch="Fill" 
                                              IsHitTestVisible="false"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}">
                                            <Path.Data>
                                                <PathGeometry>M10.5 7C10.5 7.27614 10.2761 7.5 10 7.5L4 7.5C3.72386 7.5 3.5 7.27614 3.5 7C3.5 6.72386 3.72386 6.5 4 6.5L10 6.5C10.2761 6.5 10.5 6.72386 10.5 7ZM7 13C10.3137 13 13 10.3137 13 7C13 3.68629 10.3137 1 7 1C3.68629 1 1 3.68629 1 7C1 10.3137 3.68629 13 7 13ZM7 14C10.866 14 14 10.866 14 7C14 3.13401 10.866 0 7 0C3.13401 0 0 3.13401 0 7C0 10.866 3.13401 14 7 14Z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </Button>
                                </Grid>
                            </Grid>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="diagram_localControls:Overview" BasedOn="{StaticResource SyncfusionOverviewStyle}"/>

    <Style TargetType="diagram_localControls:OverviewResizer" x:Key="SyncfusionOverviewResizerStyle">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_localControls:OverviewResizer">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid>
                            <ContentPresenter Content="{TemplateBinding Content}"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_OverViewLeft"
                                                                ControlPointType="LeftResizer"
                                                                Width="4"  
                                                                Margin="-2 0 0 0"
                                                                VerticalAlignment="Stretch"
                                                                HorizontalAlignment="Left"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_OverViewTop"
                                                                ControlPointType="TopResizer"
                                                                Height="4"
                                                                Margin="0 -2 0 0"
                                                                VerticalAlignment="Top"
                                                                HorizontalAlignment="Stretch"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_OverViewRight"
                                                                ControlPointType="RightResizer"
                                                                Width="4"  
                                                                Margin="0 0 -2 0"
                                                                VerticalAlignment="Stretch"
                                                                HorizontalAlignment="Right"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_OverViewBottom"
                                                                ControlPointType="BottomResizer"
                                                                Height="4"
                                                                Margin="0 0 0 -2"
                                                                VerticalAlignment="Bottom" 
                                                                HorizontalAlignment="Stretch"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_OverViewTopLeftCorner"
                                                                ControlPointType="TopLeftResizer"
                                                                Width="5"
                                                                Height="5" 
                                                                Margin="-3 -3 0 0"
                                                                VerticalAlignment="Top" 
                                                                HorizontalAlignment="Left"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_OverViewTopRightCorner"
                                                                ControlPointType="TopRightResizer"
                                                                Width="5"
                                                                Height="5" 
                                                                Margin="0 -3 -3 0"
                                                                VerticalAlignment="Top"
                                                                HorizontalAlignment="Right"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_OverViewBottomLeftCorner" 
                                                                ControlPointType="BottomLeftResizer" 
                                                                Width="5"
                                                                Height="5" 
                                                                Margin="-3 0 0 -3"
                                                                VerticalAlignment="Bottom"
                                                                HorizontalAlignment="Left"/>
                            <diagram_localControls:DiagramThumb x:Name="PART_OverViewBottomRightCorner" 
                                                                ControlPointType="BottomRightResizer"
                                                                Width="5"
                                                                Height="5"
                                                                Margin="0 0 -3 -3"
                                                                VerticalAlignment="Bottom"
                                                                HorizontalAlignment="Right"/>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="diagram_localControls:OverviewResizer" BasedOn="{StaticResource SyncfusionOverviewResizerStyle}"/>

    <Style TargetType="diagram_localControls:AnnotationEditor" x:Key="SyncfusionAnnotationEditorStyle">
        <Setter Property="ViewTemplate" 
                Value="{Binding ViewTemplate}" />
        <Setter Property="EditTemplate" 
                Value="{Binding EditTemplate}" />
        <Setter Property="Mode" 
                Value="{Binding Mode}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_localControls:AnnotationEditor">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid Width="{TemplateBinding Width}"
                              Height="{TemplateBinding Height}"
                              HorizontalAlignment="{TemplateBinding HorizontalAlignment}" 
                              VerticalAlignment="{TemplateBinding VerticalAlignment}">
                            <ContentPresenter x:Name="PART_ContentPresenter" 
                                              Content="{Binding}" 
                                              RenderTransformOrigin="{TemplateBinding RenderTransformOrigin}">
                                <ContentPresenter.Resources>
                                    <Style TargetType="TextBlock" BasedOn="{x:Null}"/>
                                    <Style TargetType="TextBox" BasedOn="{x:Null}"/>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="diagram_localControls:AnnotationEditor" BasedOn="{StaticResource SyncfusionAnnotationEditorStyle}"/>

    <Style TargetType="diagram_localControls:RunTimeConnectionIndicator" x:Key="SyncfusionRunTimeConnectionIndicatorStyle">
        <Setter Property="Width" Value="100"/>
        <Setter Property="Height" Value="100"/>
        <Style.Triggers>
            <Trigger Property="PortHoverEffect" Value="Shrink">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="diagram_localControls:RunTimeConnectionIndicator">
                            <Grid>
                                <Path x:Name="path0" Data="F1M311.832,302.7778L278.832,302.7778L278.832,316.4448L311.832,316.4448L311.832,349.4448L325.498,349.4448L325.498,316.4448L325.498,302.7778z"
                  Fill="{StaticResource SuccessForeground}" Height="30" Stretch="Fill" VerticalAlignment="Bottom" HorizontalAlignment="Left" Width="30" RenderTransformOrigin="0.5,0.5">
                                    <Path.RenderTransform>
                                        <TranslateTransform/>
                                    </Path.RenderTransform>
                                </Path>
                                <Path x:Name="path1" Data="F1M386.502,302.7778L353.502,302.7778L339.836,302.7778L339.836,316.4448L339.836,349.4448L353.502,349.4448L353.502,316.4448L386.502,316.4448z"
                  Fill="{StaticResource SuccessForeground}" Height="30" Stretch="Fill" Width="30" HorizontalAlignment="Right" VerticalAlignment="Bottom" RenderTransformOrigin="0.5,0.5">
                                    <Path.RenderTransform>
                                        <TranslateTransform/>
                                    </Path.RenderTransform>
                                </Path>
                                <Path x:Name="path2" Data="F1M311.832,242.105L311.832,275.105L278.832,275.105L278.832,288.772L311.832,288.772L325.498,288.772L325.498,275.105L325.498,242.105z"
                  Fill="{StaticResource SuccessForeground}" Height="30" Stretch="Fill" VerticalAlignment="Top" HorizontalAlignment="Left" Width="30" RenderTransformOrigin="0.5,0.5">
                                    <Path.RenderTransform>
                                        <TranslateTransform/>
                                    </Path.RenderTransform>
                                </Path>
                                <Path x:Name="path3" Data="F1M353.502,275.105L353.502,242.105L339.836,242.105L339.836,275.105L339.836,288.772L353.502,288.772L386.502,288.772L386.502,275.105z"
                  Fill="{StaticResource SuccessForeground}" Height="30" Stretch="Fill" Width="30" HorizontalAlignment="Right" VerticalAlignment="Top" RenderTransformOrigin="0.5,0.5">
                                    <Path.RenderTransform>
                                        <TranslateTransform/>
                                    </Path.RenderTransform>
                                </Path>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="connectionIndication">
                                        <VisualState x:Name="Connecting">
                                            <Storyboard RepeatBehavior="Forever">
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)" Storyboard.TargetName="path0">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="-30"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="18"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="20"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" Storyboard.TargetName="path0">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="30"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="-19"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="-21"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)" Storyboard.TargetName="path1">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="30"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="-19"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="-21"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" Storyboard.TargetName="path1">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="30"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="-19"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="-21"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)" Storyboard.TargetName="path2">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="-30"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="18"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="20"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" Storyboard.TargetName="path2">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="-30"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="18"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="20"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)" Storyboard.TargetName="path3">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="30"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="-19"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="-21"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" Storyboard.TargetName="path3">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="-30"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="18"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="20"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="path0">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="1"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="path1">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="1"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="path2">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="1"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="path3">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="1"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                                                </DoubleAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Normal">
                                            <Storyboard />
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="PortHoverEffect" Value="Ripple">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="diagram_localControls:RunTimeConnectionIndicator">
                            <Grid>
                                <Viewbox x:Name="PART_Ripple"
                                         Width="80" 
                                         Height="80"
                                         Visibility="Collapsed">
                                    <Grid>
                                        <Path x:Name="ripple"
                                              Data="M75,8C37.997,8,8,37.997,8,75C8,112.003,37.997,142,75,142C112.003,142,142,112.003,142,75C142,37.997,112.003,8,75,8z"
                                              Height="140" 
                                              Width="140" 
                                              Stretch="Fill"
                                              Stroke="{StaticResource SuccessForeground}"
                                              StrokeThickness="6" 
                                              StrokeStartLineCap="Round"
                                              StrokeEndLineCap="Round" 
                                              StrokeMiterLimit="10"
                                              StrokeLineJoin="Miter"
                                              Margin="23.5,23,23.5,20"
                                              RenderTransformOrigin="0.5,0.5">
                                            <Path.RenderTransform>
                                                <TransformGroup>
                                                    <ScaleTransform/>
                                                    <SkewTransform/>
                                                    <RotateTransform/>
                                                    <TranslateTransform/>
                                                </TransformGroup>
                                            </Path.RenderTransform>
                                        </Path>
                                        <Path x:Name="ripple1" 
                                              Data="M75,42.877C57.259,42.877,42.877,57.259,42.877,75C42.877,92.741,57.259,107.123,75,107.123C92.741,107.123,107.123,92.741,107.123,75C107.123,57.259,92.741,42.877,75,42.877z" 
                                              Width="71"
                                              Height="71"
                                              Stretch="Fill"
                                              Stroke="{StaticResource SuccessForeground}"
                                              StrokeThickness="6"
                                              StrokeStartLineCap="Round"
                                              StrokeEndLineCap="Round"
                                              StrokeMiterLimit="10"
                                              StrokeLineJoin="Miter"
                                              Margin="58,57.25,58,54.75" 
                                              RenderTransformOrigin="0.5,0.5">
                                            <Path.RenderTransform>
                                                <TransformGroup>
                                                    <ScaleTransform/>
                                                    <SkewTransform/>
                                                    <RotateTransform/>
                                                    <TranslateTransform/>
                                                </TransformGroup>
                                            </Path.RenderTransform>
                                        </Path>
                                        <Ellipse x:Name="ripple_ellipse" 
                                                 Height="23"
                                                 Width="23"
                                                 Stroke="{StaticResource SuccessForeground}"
                                                 StrokeThickness="6" 
                                                 StrokeStartLineCap="Round"
                                                 StrokeEndLineCap="Round" 
                                                 StrokeMiterLimit="10"
                                                 StrokeLineJoin="Miter" 
                                                 Margin="82.291,81.166,81.708,78.833"
                                                 RenderTransformOrigin="0.5,0.5">
                                            <Ellipse.RenderTransform>
                                                <TransformGroup>
                                                    <ScaleTransform/>
                                                    <SkewTransform/>
                                                    <RotateTransform/>
                                                    <TranslateTransform/>
                                                </TransformGroup>
                                            </Ellipse.RenderTransform>
                                        </Ellipse>
                                    </Grid>
                                </Viewbox>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="connectionIndication">
                                        <VisualState x:Name="Connecting">
                                            <Storyboard RepeatBehavior="Forever">
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Visibility)" Storyboard.TargetName="PART_Ripple">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.X)"
                                                                       Storyboard.TargetName="ripple">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="0"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.X)" Storyboard.TargetName="ripple1">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="0"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" Storyboard.TargetName="ripple1">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0.16"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="0.301"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0.453"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="0.593"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0.718"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="0.86"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="1.008"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="1.156"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="1.31"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="1.436"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)" Storyboard.TargetName="ripple1">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="-0.16"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="-0.301"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="-0.453"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="-0.593"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="-0.718"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="-0.86"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="-1.008"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="-1.156"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="-1.31"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="-1.436"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.Y)" Storyboard.TargetName="ripple1">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="0"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" Storyboard.TargetName="ripple">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0.719"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="0.786"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0.862"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="0.929"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="1"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="1.076"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="1.143"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="1.219"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="1.286"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="1.359"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)" Storyboard.TargetName="ripple">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0.719"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="0.786"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0.862"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="0.929"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="1"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="1.076"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="1.143"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="1.219"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="1.286"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="1.359"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.Y)" Storyboard.TargetName="ripple">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="0"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="ripple">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0.6"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="0.6"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0.6"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="0.6"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0.6"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="0.6"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0.4"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0.2"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="0.1"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="0"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.Y)" Storyboard.TargetName="ripple_ellipse">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="0"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.X)" Storyboard.TargetName="ripple_ellipse">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="-0.375"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="ripple_ellipse">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="0"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="ripple1">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="1"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="1"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0.68888888888888888"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0.6"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="0.4"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="0.2"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)" Storyboard.TargetName="ripple_ellipse">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="1"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="0.478"/>
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" Storyboard.TargetName="ripple_ellipse">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="1"/>
                                                    <EasingDoubleKeyFrame KeyTime="0:0:0.9" Value="0.478"/>
                                                </DoubleAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Normal">
                                            <Storyboard />
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="PortHoverEffect" Value="FilledRipple">
            <Setter Property="Template">
              <Setter.Value>
                <ControlTemplate TargetType="diagram_localControls:RunTimeConnectionIndicator">
                  <Grid>
                    <Viewbox x:Name="PART_Ripple" Width="55" Height="55" Visibility="Visible">
                      <Grid x:Name="LayoutRoot">
                        <Path x:Name="_4" Data="F1M30,15C30,23.284,23.284,30,15,30C6.716,30,0,23.284,0,15C0,6.716,6.716,0,15,0C23.284,0,30,6.716,30,15"
                              Fill="{StaticResource SuccessForeground}" Height="30" Opacity="0.25" Stretch="Fill" Width="30"
                              HorizontalAlignment="Left" VerticalAlignment="Top" RenderTransformOrigin="0.5,0.5">
                          <Path.RenderTransform>
                            <TransformGroup>
                              <ScaleTransform/>
                              <SkewTransform/>
                              <RotateTransform/>
                              <TranslateTransform/>
                            </TransformGroup>
                          </Path.RenderTransform>
                        </Path>
                      </Grid>
                    </Viewbox>
                    <VisualStateManager.VisualStateGroups>
                      <VisualStateGroup x:Name="connectionIndication">
                        <VisualState x:Name="Connecting">
                          <Storyboard RepeatBehavior="Forever">
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.X)" Storyboard.TargetName="_4">
                              <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="0"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:1.0" Value="0"/>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" Storyboard.TargetName="_4">
                              <EasingDoubleKeyFrame KeyTime="0" Value="-0.333"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="-0.5"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="-0.667"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="-0.834"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="-1.001"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:1.0" Value="-1.201"/>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)" Storyboard.TargetName="_4">
                              <EasingDoubleKeyFrame KeyTime="0" Value="0.333"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0.5"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0.667"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0.834"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="1.01"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:1.0" Value="1.201"/>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.Y)" Storyboard.TargetName="_4">
                              <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="0"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:1.0" Value="0"/>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="_4">
                              <EasingDoubleKeyFrame KeyTime="0" Value="1"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0.80"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0.60"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0.40"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="0.20"/>
                              <EasingDoubleKeyFrame KeyTime="0:0:1.0" Value="0"/>
                            </DoubleAnimationUsingKeyFrames>
                          </Storyboard>
                        </VisualState>
                        <VisualState x:Name="Normal">
                          <Storyboard />
                        </VisualState>
                      </VisualStateGroup>
                    </VisualStateManager.VisualStateGroups>
                  </Grid>
                </ControlTemplate>
              </Setter.Value>
            </Setter>
          </Trigger>
            <Trigger Property="PortHoverEffect" Value="None">
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="diagram_localControls:RunTimeConnectionIndicator" BasedOn="{StaticResource SyncfusionRunTimeConnectionIndicatorStyle}"/>

    <Style TargetType="diagram_localControls:ScrollViewer" x:Key="SyncfusionDiagramScrollViewerStyle">
        <Setter Property="IsTabStop" 
                Value="False"/>
        <Setter Property="Focusable"
                Value="False"/>
        <Setter Property="HorizontalContentAlignment" 
                Value="Left"/>
        <Setter Property="VerticalContentAlignment"
                Value="Top"/>
        <Setter Property="Padding" 
                Value="0"/>
        <Setter Property="Background"
                Value="Transparent"/>
        <Setter Property="BorderBrush"
                Value="Transparent"/>
        <Setter Property="BorderThickness"
                Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_localControls:ScrollViewer">
                    <Border BorderBrush="{TemplateBinding BorderBrush}">
                        <Grid Background="{TemplateBinding Background}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <ContentPresenter x:Name="PART_ContentPresenter"
                                              HorizontalAlignment="Stretch" 
                                              VerticalAlignment="Stretch">
                                <ContentPresenter.Content>
                                    <Canvas x:Name="PART_DiagramCanvas"/>
                                </ContentPresenter.Content>
                            </ContentPresenter>
                            <ScrollBar x:Name="VerticalScrollBar"
                                       Grid.Row="0"
                                       Grid.Column="1"
                                       IsTabStop="False"
                                       Minimum="{TemplateBinding MinimumY}"
                                       Maximum="{TemplateBinding MaximumY}"
                                       SmallChange="3"
                                       LargeChange="{TemplateBinding ViewportHeight}"              
                                       Width="{StaticResource Windows11Light.HeaderTextStyle}"
                                       Orientation="Vertical"
                                       Style="{StaticResource WPFScrollBarStyle}"
                                       Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                       ViewportSize="{TemplateBinding ViewportHeight}"
                                       Value="{Binding Path=VerticalOffset, RelativeSource={RelativeSource TemplatedParent}, Mode=TwoWay}"/>
                            <ScrollBar x:Name="HorizontalScrollBar"
                                       Grid.Column="0"
                                       Grid.Row="1"
                                       IsTabStop="False"
                                       Minimum="{TemplateBinding MinimumX}"
                                       Maximum="{TemplateBinding MaximumX}"
                                       Style="{StaticResource WPFScrollBarStyle}"
                                       SmallChange="3"
                                       LargeChange="{TemplateBinding ViewportWidth}"
                                       Height="{StaticResource Windows11Light.HeaderTextStyle}"
                                       Orientation="Horizontal"                                       
                                       Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                       ViewportSize="{TemplateBinding ViewportWidth}"
                                       Value="{Binding Path=HorizontalOffset, RelativeSource={RelativeSource TemplatedParent}, Mode=TwoWay}"/>
                            <Border x:Name="ScrollBarSeparator"
                                    Grid.Row="1"
                                    Grid.Column="1"
                                    Background="{StaticResource White}" />
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="diagram_localControls:ScrollViewer" BasedOn="{StaticResource SyncfusionDiagramScrollViewerStyle}"/>

    <Style TargetType="diagram_printing:PrintPreviewWindow" BasedOn="{StaticResource WPFWindowStyle}" x:Key="SyncfusionPrintPreviewWindowStyle">
        <Setter Property="Title" 
                Value="{Binding Title}"/>
        <Setter Property="Icon" 
                Value="pack://application:,,,/Syncfusion.SfDiagram.Wpf;component/Resources/App.ico"/>
        <Setter Property="ResizeMode" 
                Value="NoResize"/>
        <Setter Property="Height"
                Value="650" />
        <Setter Property="Width"
                Value="840" />
        <Setter Property="MinHeight" 
                Value="400" />
        <Setter Property="MinWidth" 
                Value="500" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_printing:PrintPreviewWindow">
                    <ContentPresenter Content="{TemplateBinding Content}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="diagram_printing:PrintPreviewWindow" BasedOn="{StaticResource SyncfusionPrintPreviewWindowStyle}"/>

</ResourceDictionary>
