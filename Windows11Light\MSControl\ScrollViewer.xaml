<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="ScrollBarFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="{StaticResource Windows11Light.FocusMargin}" 
                               Stroke="{StaticResource BorderAlt3}" 
                               StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Light.StrokeDashArray}"
                               SnapsToDevicePixels="True"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <Style x:Key="ScrollBarButton" TargetType="{x:Type RepeatButton}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource ScrollBarFocusVisual}"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Padding" Value="1"/>
        <Setter Property="Focusable" Value="false"/>
        <Setter Property="IsTabStop" Value="false"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <Border x:Name="border" 
                            BorderBrush="Transparent" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            Background="Transparent" 
                            SnapsToDevicePixels="true">
                        <ContentPresenter x:Name="contentPresenter" 
                                          Focusable="False" 
                                          Margin="{TemplateBinding Padding}" 
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Background" TargetName="border" Value="Transparent"/>
                            <Setter Property="BorderBrush"
                                    TargetName="border"
                                    Value="{StaticResource IconColorHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter Property="Background"
                                    TargetName="border"
                                    Value="Transparent"/>
                            <Setter Property="BorderBrush"
                                    TargetName="border"
                                    Value="{StaticResource IconColorSelected}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Opacity" TargetName="contentPresenter" Value="0.56"/>
                            <Setter Property="Background"
                                    TargetName="border"
                                    Value="Transparent"/>
                            <Setter Property="BorderBrush"
                                    TargetName="border"
                                    Value="{StaticResource IconColorDisabled}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style x:Key="RepeatButtonTransparent" TargetType="{x:Type RepeatButton}">
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Focusable" Value="false"/>
        <Setter Property="IsTabStop" Value="false"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <Rectangle Fill="{TemplateBinding Background}" 
                               Height="{TemplateBinding Height}" 
                               Width="{TemplateBinding Width}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ScrollBarThumbVertical" TargetType="{x:Type Thumb}">
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="IsTabStop" Value="false"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border x:Name="thumbBorder" 
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}" 
                            BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                            Background="{StaticResource BorderAlt1}"
                            BorderBrush="{StaticResource BorderAlt1}"
                            Width="{TemplateBinding Width}"
                            Height="{TemplateBinding Height}">
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact">
                            <Setter Property="CornerRadius" TargetName="thumbBorder" Value="2"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="CornerRadius" TargetName="thumbBorder" Value="{StaticResource Windows11Light.CornerRadius4}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsDragging" Value="True"/>
                                <Condition Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="CornerRadius" TargetName="thumbBorder" Value="{StaticResource Windows11Light.CornerRadius4}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="CornerRadius" TargetName="thumbBorder" Value="{StaticResource Windows11Light.CornerRadius4}"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Background" TargetName="thumbBorder" Value="{StaticResource BorderAlt1}"/>
                            <Setter Property="BorderBrush" TargetName="thumbBorder" Value="{StaticResource BorderAlt1}" />
                        </Trigger>
                        <Trigger Property="IsDragging" Value="true">
                            <Setter Property="Background" TargetName="thumbBorder" Value="{StaticResource BorderAlt1}"/>
                            <Setter Property="BorderBrush" TargetName="thumbBorder" Value="{StaticResource BorderAlt1}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="thumbBorder" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="thumbBorder" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style x:Key="ScrollBarThumbHorizontal" TargetType="{x:Type Thumb}">
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="IsTabStop" Value="false"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border x:Name="thumbBorder"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}" 
                            BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                            Background="{StaticResource BorderAlt1}"
                            BorderBrush="{StaticResource BorderAlt1}"
                            Height="{TemplateBinding Height}"
                            Width="{TemplateBinding Width}">
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact">
                            <Setter Property="CornerRadius" TargetName="thumbBorder" Value="2"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="CornerRadius" TargetName="thumbBorder" Value="{StaticResource Windows11Light.CornerRadius4}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsDragging" Value="True"/>
                                <Condition Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="CornerRadius" TargetName="thumbBorder" Value="{StaticResource Windows11Light.CornerRadius4}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="CornerRadius" TargetName="thumbBorder" Value="{StaticResource Windows11Light.CornerRadius4}"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Background" TargetName="thumbBorder" Value="{StaticResource BorderAlt1}"/>
                            <Setter Property="BorderBrush" TargetName="thumbBorder" Value="{StaticResource BorderAlt1}" />
                        </Trigger>
                        <Trigger Property="IsDragging" Value="true">
                            <Setter Property="Background" TargetName="thumbBorder" Value="{StaticResource BorderAlt1}"/>
                            <Setter Property="BorderBrush" TargetName="thumbBorder" Value="{StaticResource BorderAlt1}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="thumbBorder" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="thumbBorder" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFScrollBarStyle" TargetType="{x:Type ScrollBar}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="MinWidth" Value="12"/>
        <Setter Property="Stylus.IsPressAndHoldEnabled" Value="false"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="false"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollBar}">
                    <Grid x:Name="Bg" SnapsToDevicePixels="true"
                          Width="{Binding Width, RelativeSource={RelativeSource Mode=TemplatedParent}}">
                        <Grid.RowDefinitions>
                            <RowDefinition MaxHeight="12"/>
                            <RowDefinition Height="0.00001*"/>
                            <RowDefinition MaxHeight="12"/>
                        </Grid.RowDefinitions>
                        <Border Grid.Row="1"
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                Background="{TemplateBinding Background}" />
                        <RepeatButton x:Name="PART_LineUpButton" 
                                      Command="{x:Static ScrollBar.LineUpCommand}"
                                      BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                      Style="{StaticResource ScrollBarButton}">
                            <TextBlock x:Name="ArrowTop"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="{StaticResource IconColor}"
                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                       
                                       Margin="1"
                                       Text="&#xe709;"/>
                        </RepeatButton>
                        <Track x:Name="PART_Track" 
                               Grid.Row="1"
                               IsDirectionReversed="true" >
                            <Track.DecreaseRepeatButton>
                                <RepeatButton Command="{x:Static ScrollBar.PageUpCommand}" 
                                              Style="{StaticResource RepeatButtonTransparent}"/>
                            </Track.DecreaseRepeatButton>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton Command="{x:Static ScrollBar.PageDownCommand}" 
                                              Style="{StaticResource RepeatButtonTransparent}"/>
                            </Track.IncreaseRepeatButton>
                            <Track.Thumb>
                                <Thumb x:Name="thumb" 
                                       Style="{StaticResource ScrollBarThumbVertical}" 
                                       Width="6"/>
                            </Track.Thumb>
                        </Track>
                        <RepeatButton x:Name="PART_LineDownButton" 
                                      Grid.Row="2" 
                                      BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                      Command="{x:Static ScrollBar.LineDownCommand}" 
                                      Style="{StaticResource ScrollBarButton}">
                            <TextBlock x:Name="ArrowBottom"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="{StaticResource IconColor}"
                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                       
                                       Margin="1"
                                       Text="&#xe708;"/>
                        </RepeatButton>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact">
                            <Setter Property="HorizontalAlignment" TargetName="thumb" Value="Right"/>
                            <Setter Property="Margin" TargetName="thumb" Value="0,0,3,0"/>
                            <Setter Property="Width" TargetName="thumb" Value="2"/>
                            <Setter Property="Visibility" TargetName="PART_LineDownButton" Value="Hidden"/>
                            <Setter Property="Visibility" TargetName="PART_LineUpButton" Value="Hidden"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="HorizontalAlignment" TargetName="thumb" Value="Center"/>
                            <Setter Property="Margin" TargetName="thumb" Value="0"/>
                            <Setter Property="Width" TargetName="thumb" Value="6"/>
                            <Setter Property="Visibility" TargetName="PART_LineDownButton" Value="Visible"/>
                            <Setter Property="Visibility" TargetName="PART_LineUpButton" Value="Visible"/>
                            <Setter Property="Visibility" TargetName="ArrowTop" Value="Visible"/>
                            <Setter Property="Visibility" TargetName="ArrowBottom" Value="Visible"/>
                        </MultiTrigger>
                       <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="HorizontalAlignment" TargetName="thumb" Value="Center"/>
                            <Setter Property="Margin" TargetName="thumb" Value="0"/>
                            <Setter Property="Width" TargetName="thumb" Value="6"/>
                            <Setter Property="Visibility" TargetName="PART_LineDownButton" Value="Visible"/>
                            <Setter Property="Visibility" TargetName="PART_LineUpButton" Value="Visible"/>
                        </MultiTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsMouseOver, ElementName=PART_LineDownButton}" Value="true"/>
                                <Condition Binding="{Binding IsPressed, ElementName=PART_LineDownButton}" Value="true"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Foreground" TargetName="ArrowBottom" Value="{StaticResource IconColorSelected}"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsMouseOver, ElementName=PART_LineUpButton}" Value="true"/>
                                <Condition Binding="{Binding IsPressed, ElementName=PART_LineUpButton}" Value="true"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Foreground" TargetName="ArrowTop" Value="{StaticResource IconColorSelected}"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsMouseOver, ElementName=PART_LineDownButton}" Value="true"/>
                                <Condition Binding="{Binding IsPressed, ElementName=PART_LineDownButton}" Value="false"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Foreground" TargetName="ArrowBottom" Value="{StaticResource IconColorHovered}"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsMouseOver, ElementName=PART_LineUpButton}" Value="true"/>
                                <Condition Binding="{Binding IsPressed, ElementName=PART_LineUpButton}" Value="false"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Foreground" TargetName="ArrowTop" Value="{StaticResource IconColorHovered}"/>
                        </MultiDataTrigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" TargetName="ArrowTop" Value="{StaticResource IconColorDisabled}"/>
                            <Setter Property="Foreground" TargetName="ArrowBottom" Value="{StaticResource IconColorDisabled}"/>
                        </Trigger> 
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Orientation" Value="Horizontal">
                <Setter Property="Width" Value="Auto"/>
                <Setter Property="MinWidth" Value="0"/>
                <Setter Property="MinHeight" Value="12"/>
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type ScrollBar}">
                            <Grid x:Name="Bg" SnapsToDevicePixels="true"
                                  Height="{Binding Height, RelativeSource={RelativeSource Mode=TemplatedParent}}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition MaxWidth="12"/>
                                    <ColumnDefinition Width="0.00001*"/>
                                    <ColumnDefinition MaxWidth="12"/>
                                </Grid.ColumnDefinitions>
                                <Border Grid.Column="1"
                                        BorderBrush="{TemplateBinding BorderBrush}" 
                                        BorderThickness="{TemplateBinding BorderThickness}" 
                                        Background="{TemplateBinding Background}" />
                                <RepeatButton x:Name="PART_LineLeftButton" 
                                              Command="{x:Static ScrollBar.LineLeftCommand}"
                                              BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                              Style="{StaticResource ScrollBarButton}">
                                    <TextBlock x:Name="ArrowLeft"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"
                                               Foreground="{StaticResource IconColor}"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               
                                               Margin="1"
                                               Text="&#xe70a;"/>
                                </RepeatButton>
                                <Track x:Name="PART_Track" 
                                       Grid.Column="1">
                                    <Track.DecreaseRepeatButton>
                                        <RepeatButton Command="{x:Static ScrollBar.PageLeftCommand}" 
                                                      Style="{StaticResource RepeatButtonTransparent}"/>
                                    </Track.DecreaseRepeatButton>
                                    <Track.IncreaseRepeatButton>
                                        <RepeatButton Command="{x:Static ScrollBar.PageRightCommand}" 
                                                      Style="{StaticResource RepeatButtonTransparent}"/>
                                    </Track.IncreaseRepeatButton>
                                    <Track.Thumb>
                                        <Thumb x:Name="thumb" 
                                               Style="{StaticResource ScrollBarThumbHorizontal}" 
                                               Height="6"/>
                                    </Track.Thumb>
                                </Track>
                                <RepeatButton x:Name="PART_LineRightButton" 
                                              Grid.Column="2" 
                                              Command="{x:Static ScrollBar.LineRightCommand}" 
                                              BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                              Style="{StaticResource ScrollBarButton}">
                                    <TextBlock x:Name="ArrowRight"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"
                                               Foreground="{StaticResource IconColor}"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               
                                               Margin="1"
                                               Text="&#xe70b;"/>
                                </RepeatButton>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                                    <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                                </Trigger>
                                <Trigger Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact">
                                    <Setter Property="VerticalAlignment" TargetName="thumb" Value="Bottom"/>
                                    <Setter Property="Margin" TargetName="thumb" Value="0,0,0,3"/>
                                    <Setter Property="Height" TargetName="thumb" Value="2"/>
                                    <Setter Property="Visibility" TargetName="PART_LineRightButton" Value="Hidden"/>
                                    <Setter Property="Visibility" TargetName="PART_LineLeftButton" Value="Hidden"/>
                                </Trigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsMouseOver" Value="True"/>
                                        <Condition Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact"/>
                                    </MultiTrigger.Conditions>
                                    <Setter Property="VerticalAlignment" TargetName="thumb" Value="Center"/>
                                    <Setter Property="Margin" TargetName="thumb" Value="0"/>
                                    <Setter Property="Height" TargetName="thumb" Value="6"/>
                                    <Setter Property="Visibility" TargetName="PART_LineRightButton" Value="Visible"/>
                                    <Setter Property="Visibility" TargetName="PART_LineLeftButton" Value="Visible"/>
                                    <Setter Property="Visibility" TargetName="ArrowLeft" Value="Visible"/>
                                    <Setter Property="Visibility" TargetName="ArrowRight" Value="Visible"/>
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsEnabled" Value="False"/>
                                        <Condition Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact"/>
                                    </MultiTrigger.Conditions>
                                    <Setter Property="VerticalAlignment" TargetName="thumb" Value="Center"/>
                                    <Setter Property="Margin" TargetName="thumb" Value="0"/>
                                    <Setter Property="Height" TargetName="thumb" Value="6"/>
                                    <Setter Property="Visibility" TargetName="PART_LineRightButton" Value="Visible"/>
                                    <Setter Property="Visibility" TargetName="PART_LineLeftButton" Value="Visible"/>
                                </MultiTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding IsMouseOver, ElementName=PART_LineRightButton}" Value="true"/>
                                        <Condition Binding="{Binding IsPressed, ElementName=PART_LineRightButton}" Value="true"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="Foreground" TargetName="ArrowRight" Value="{StaticResource IconColorSelected}"/>
                                </MultiDataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding IsMouseOver, ElementName=PART_LineLeftButton}" Value="true"/>
                                        <Condition Binding="{Binding IsPressed, ElementName=PART_LineLeftButton}" Value="true"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="Foreground" TargetName="ArrowLeft" Value="{StaticResource IconColorSelected}"/>
                                </MultiDataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding IsMouseOver, ElementName=PART_LineRightButton}" Value="true"/>
                                        <Condition Binding="{Binding IsPressed, ElementName=PART_LineRightButton}" Value="false"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="Foreground" TargetName="ArrowRight" Value="{StaticResource IconColorHovered}"/>
                                </MultiDataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding IsMouseOver, ElementName=PART_LineLeftButton}" Value="true"/>
                                        <Condition Binding="{Binding IsPressed, ElementName=PART_LineLeftButton}" Value="false"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="Foreground" TargetName="ArrowLeft" Value="{StaticResource IconColorHovered}"/>
                                </MultiDataTrigger>
                                <Trigger Property="IsEnabled" Value="false">
                                    <Setter Property="Foreground" TargetName="ArrowLeft" Value="{StaticResource IconColorDisabled}"/>
                                    <Setter Property="Foreground" TargetName="ArrowRight" Value="{StaticResource IconColorDisabled}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource ScrollBarBackground}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource WPFScrollBarStyle}" TargetType="{x:Type ScrollBar}"/>

    <Style x:Key="WPFScrollViewerStyle" TargetType="{x:Type ScrollViewer}">
        <Setter Property="OverridesDefaultStyle" Value="True"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt1}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollViewer}">
                    <Grid x:Name="Grid" Background="{TemplateBinding Background}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Rectangle x:Name="Corner" 
                                   Grid.Column="1" 
                                   Grid.Row="1"
                                   Fill="Transparent" />
                        <ScrollContentPresenter Margin="{TemplateBinding Padding}" 
                                                x:Name="PART_ScrollContentPresenter" 
                                                Grid.Column="0" 
                                                Grid.Row="0" 
                                                Content="{TemplateBinding Content}" 
                                                ContentTemplate="{TemplateBinding ContentTemplate}" 
                                                CanContentScroll="{TemplateBinding CanContentScroll}" 
                                                CanHorizontallyScroll="False" 
                                                CanVerticallyScroll="False"/>
                        <ScrollBar x:Name="PART_VerticalScrollBar"                                   
                                   Grid.Column="1" 
                                   Grid.Row="0" 
                                   Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}" 
                                   Cursor="Arrow"  
                                   ViewportSize="{TemplateBinding ViewportHeight}" 
                                   Maximum="{TemplateBinding ScrollableHeight}" 
                                   Minimum="0"
                                   Width="12"
                                   Value="{Binding Path=VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" 
                                   AutomationProperties.AutomationId="VerticalScrollBar"/>
                        <ScrollBar x:Name="PART_HorizontalScrollBar"                                    
                                   Grid.Column="0" 
                                   Grid.Row="1" 
                                   Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}" 
                                   Cursor="Arrow" 
                                   Orientation="Horizontal" 
                                   ViewportSize="{TemplateBinding ViewportWidth}" 
                                   Maximum="{TemplateBinding ScrollableWidth}" 
                                   Minimum="0"
                                   Height="12"
                                   Value="{Binding Path=HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"
                                   AutomationProperties.AutomationId="HorizontalScrollBar"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact">
                            <Setter Property="Grid.ColumnSpan" TargetName="PART_ScrollContentPresenter" Value="2"/>
                            <Setter Property="Grid.RowSpan" TargetName="PART_ScrollContentPresenter" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource WPFScrollViewerStyle}" TargetType="{x:Type ScrollViewer}"/>
</ResourceDictionary>
    
