<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    
    xmlns:Sync_Shared_Resources="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Button.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatPrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/PrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphRepeatButton.xaml" />
		<ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Calendar.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/RepeatButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="DateTimeEdit.MouseOver.Border" Color="#d1eaf5" />

    <LinearGradientBrush x:Key="DateTimeEditBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DateTimeEditBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DateTimeEditBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <BooleanToVisibilityConverter x:Key="DateTimeBooleanToVisibilityConverter" />
    <!--  GlyphToggleButton Style  -->
    <Style x:Key="SyncfusionDropDownGlyphToggleButtonStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1000}" />
        <Setter Property="Foreground" Value="{StaticResource IconColor}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border
                        x:Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="4"
                        SnapsToDevicePixels="true">
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            RecognizesAccessKey="True"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="TextElement.Foreground" Value="{StaticResource IconColor}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="TextElement.Foreground" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="TextElement.Foreground" Value="{StaticResource IconColor}" />
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter Property="TextElement.Foreground" Value="{StaticResource IconColor}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="TextElement.Foreground" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  GlyphToggleButton Style  -->

    <DataTemplate x:Key="WatermarkContentTemplate">
        <TextBlock Style="{x:Null}" Text="{Binding}" />
    </DataTemplate>

    <!--  DateTimeEdit Style  -->

    <Style x:Key="SyncfusionDateTimeEditStyle" TargetType="{x:Type shared:DateTimeEdit}">
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="BorderBrush" Value="{StaticResource DateTimeEditBorderBrush}" />
        <Setter Property="SelectionBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="FocusedBorderBrush" Value="{StaticResource DateTimeEditBorderBrushFocused}" />
        <Setter Property="Clock">
            <Setter.Value>
                <shared:Clock x:Name="Clock" />
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="RepeatButtonBackground" Value="Transparent" />
        <Setter Property="RepeatButtonBorderBrush" Value="{StaticResource Border}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant1}" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:DateTimeEdit}">
                    <Grid>
                        <Border
                            x:Name="Border"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Opacity="1"
                            SnapsToDevicePixels="True">
                            <Grid x:Name="ContentHost">
                                <Grid.RowDefinitions>
                                    <RowDefinition />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="*" />
                                        <RowDefinition Height="0" />
                                    </Grid.RowDefinitions>

                                    <ScrollViewer
                                        x:Name="PART_ContentHost"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Background="Transparent"
                                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                        Visibility="{TemplateBinding ContentElementVisibility}" >
                                        <ScrollViewer.Margin>
                                            <Thickness>4,0,0,0</Thickness>
                                        </ScrollViewer.Margin>
                                    </ScrollViewer>
                                    <ContentControl
                                        x:Name="PART_EmptyContent"
                                        Margin="6 0 0 0"
                                        Padding="{TemplateBinding Padding}"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Background="{StaticResource ContentBackgroundAlt4}"
                                        Content="{TemplateBinding NoneDateText}"
                                        ContentTemplate="{StaticResource WatermarkContentTemplate}"
                                        FontFamily="{TemplateBinding FontFamily}"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontStretch="{TemplateBinding FontStretch}"
                                        FontStyle="{TemplateBinding FontStyle}"
                                        FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                                        Foreground="{StaticResource PlaceholderForeground}"
                                        IsHitTestVisible="False"
                                        IsTabStop="False"
                                        Visibility="{TemplateBinding WatermarkVisibility}" />

                                    <ToggleButton
                                        x:Name="PART_DropDown"
                                        Grid.Column="2"
                                        Width="24"
                                        Height="18"
                                        Background="Transparent"
                                        HorizontalAlignment="Stretch"
                                        VerticalAlignment="Stretch"
                                        IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                        IsTabStop="False"
                                        Margin="0 1 4 1"
                                        Style="{StaticResource SyncfusionDropDownGlyphToggleButtonStyle}"
                                        Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IsButtonPopUpEnabled, Converter={StaticResource DateTimeBooleanToVisibilityConverter}}">
                                        <ToggleButton.Padding>
                                            <Thickness>5,3,5,3</Thickness>
                                        </ToggleButton.Padding>
                                        <ToggleButton.Content>
                                            <Path
                                                        x:Name="path"
                                                        Width="12"
                                                        Height="12"
                                                        Margin="1,0,0,0"
                                                        StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}"
                                                        RenderTransformOrigin="0.5,0.5"
                                                        Stretch="Fill" >
                                                <Path.Data>
                                                    <PathGeometry>M1.50781 13.8047C1.80469 13.9348 2.11719 14 2.44531 14H5.9375C5.87683 13.9209 5.81812 13.8403 5.76147 13.7585C5.7063 13.679 5.65295 13.5979 5.60156 13.5156C5.50256 13.3489 5.40881 13.177 5.32031 13H2.5C2.29688 13 2.10413 12.9609 1.92188 12.8828C1.73962 12.8047 1.57812 12.698 1.4375 12.5625C1.30212 12.4219 1.19531 12.2605 1.11719 12.0781C1.0636 11.9531 1.02844 11.8232 1.0116 11.6885C1.00391 11.6267 1 11.564 1 11.5V4H7H9H13V5.32031C13.1771 5.40894 13.349 5.50513 13.5156 5.60938C13.6823 5.70825 13.8438 5.81763 14 5.9375V2.44531C14 2.28735 13.9844 2.13306 13.953 1.98242C13.9191 1.82007 13.8671 1.66187 13.7969 1.50781C13.6666 1.21094 13.4896 0.953125 13.2656 0.734375C13.1288 0.594482 12.9767 0.472656 12.8093 0.369385C12.7091 0.307373 12.6034 0.251953 12.4922 0.203125C12.1953 0.067627 11.8828 0 11.5547 0H2.44531C2.11719 0 1.80469 0.067627 1.50781 0.203125C1.21094 0.333252 0.950562 0.510498 0.726562 0.734375C0.507812 0.953125 0.330688 1.21094 0.195312 1.50781C0.0650635 1.80469 0 2.11719 0 2.44531V11.5547C0 11.8828 0.0650635 12.1953 0.195312 12.4922C0.330688 12.7891 0.507812 13.0496 0.726562 13.2734C0.950562 13.4922 1.21094 13.6692 1.50781 13.8047ZM9 3H13V2.5C13 2.29688 12.9609 2.10425 12.8828 1.92188C12.8416 1.82568 12.7916 1.73608 12.733 1.65283C12.6805 1.57837 12.6211 1.50928 12.5547 1.44531C12.4193 1.30469 12.2604 1.19531 12.0781 1.11719C11.8959 1.03906 11.7031 1 11.5 1H2.5C2.29688 1 2.10413 1.03906 1.92188 1.11719C1.73962 1.19531 1.57812 1.30469 1.4375 1.44531C1.30212 1.58081 1.19531 1.7395 1.11719 1.92188C1.03906 2.10425 1 2.29688 1 2.5V3H7H9ZM6.35156 8.75C6.11719 9.29688 6 9.88013 6 10.5C6 11.125 6.11719 11.7109 6.35156 12.2578C6.58594 12.8047 6.90625 13.2812 7.3125 13.6875C7.71875 14.0938 8.19531 14.4141 8.74219 14.6484C9.28906 14.8828 9.875 15 10.5 15C10.7916 15 11.0752 14.9741 11.3507 14.9221C11.6608 14.8638 11.9604 14.7725 12.25 14.6484C12.7969 14.4089 13.2734 14.0859 13.6797 13.6797C14.0859 13.2734 14.4062 12.7969 14.6406 12.25C14.8802 11.7031 15 11.1199 15 10.5C15 10.0886 14.9453 9.69263 14.8359 9.3125C14.7318 8.927 14.5807 8.56763 14.3828 8.23438C14.1901 7.90112 13.9557 7.59644 13.6797 7.32031C13.4037 7.04419 13.099 6.80981 12.7656 6.61719C12.5742 6.50342 12.3743 6.40527 12.1656 6.32251C12.011 6.26123 11.8516 6.2085 11.6875 6.16406C11.3073 6.05469 10.9115 6 10.5 6C9.88025 6 9.29688 6.11987 8.75 6.35938C8.20312 6.59375 7.72656 6.91406 7.32031 7.32031C6.91406 7.72656 6.59119 8.20312 6.35156 8.75ZM12.3516 10.8516C12.2526 10.9504 12.1354 11 12 11H10.5C10.3646 11 10.2474 10.9504 10.1484 10.8516C10.1011 10.8042 10.0651 10.7527 10.0403 10.697C10.0134 10.6362 10 10.5706 10 10.5V8.5C10 8.3645 10.0494 8.24731 10.1484 8.14844C10.2135 8.0835 10.2863 8.03979 10.3669 8.01758C10.4092 8.00586 10.4535 8 10.5 8C10.6354 8 10.7526 8.04956 10.8516 8.14844C10.9506 8.24731 11 8.3645 11 8.5V10H12C12.1354 10 12.2526 10.0496 12.3516 10.1484C12.4506 10.2473 12.5 10.3645 12.5 10.5C12.5 10.6355 12.4506 10.7527 12.3516 10.8516Z</PathGeometry>
                                                </Path.Data>
                                                <Path.Style>
                                                    <Style TargetType="Path">
                                                        <Setter Property="Fill" Value="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"/>
                                                    </Style>
                                                </Path.Style>
                                            </Path>
                                        </ToggleButton.Content>
                                    </ToggleButton>

                                    <Grid Grid.Column="3">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="5*" />
                                            <RowDefinition Height="5*" />
                                        </Grid.RowDefinitions>

                                        <RepeatButton
                                            x:Name="PART_UpArrow"
                                            Grid.Row="0"
                                            Grid.Column="1"
                                            Width="16"
                                            Margin="0 2 4 1"
                                            Padding="{TemplateBinding Padding}"
                                            Background="{TemplateBinding RepeatButtonBackground}"
                                            BorderThickness="{StaticResource Windows11Dark.BorderThickness}"
                                            BorderBrush="{TemplateBinding RepeatButtonBorderBrush}"
                                            IsEnabled="{TemplateBinding IsEnabledRepeatButton}"
                                            IsTabStop="False"
                                            Style="{StaticResource WPFRepeatButtonStyle}"
                                            Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IsVisibleRepeatButton, Converter={StaticResource DateTimeBooleanToVisibilityConverter}}">
                                            <RepeatButton.Content>
                                                <Path
                                                    x:Name="upbuttonpath"
                                                    Width="8"
                                                    Height="4"
                                                    HorizontalAlignment="Stretch"
                                                    VerticalAlignment="Stretch"
                                                    Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                                    Stretch="Uniform" >
                                                    <Path.Data>
                                                        <PathGeometry>M0.5 4.875C0.5 4.77344 0.537109 4.68555 0.611328 4.61133L4.73633 0.486328C4.81055 0.412109 4.89844 0.375 5 0.375C5.10156 0.375 5.18945 0.412109 5.26367 0.486328L9.38867 4.61133C9.46289 4.68555 9.5 4.77344 9.5 4.875C9.5 4.97656 9.46289 5.06445 9.38867 5.13867C9.31445 5.21289 9.22656 5.25 9.125 5.25C9.02344 5.25 8.93555 5.21289 8.86133 5.13867L5 1.2832L1.13867 5.13867C1.06445 5.21289 0.976562 5.25 0.875 5.25C0.773438 5.25 0.685547 5.21289 0.611328 5.13867C0.537109 5.06445 0.5 4.97656 0.5 4.875Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </RepeatButton.Content>
                                        </RepeatButton>

                                        <RepeatButton
                                            x:Name="PART_DownArrow"
                                            Grid.Row="1"
                                            Grid.Column="1"
                                            Width="16"
                                            Margin="0 1 4 2"
                                            Padding="{TemplateBinding Padding}"
                                            Background="{TemplateBinding RepeatButtonBackground}"
                                            BorderThickness="{StaticResource Windows11Dark.BorderThickness}"
                                            BorderBrush="{TemplateBinding RepeatButtonBorderBrush}"
                                            IsEnabled="{TemplateBinding IsEnabledRepeatButton}"
                                            IsTabStop="False"
                                            Style="{StaticResource WPFRepeatButtonStyle}"
                                            Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IsVisibleRepeatButton, Converter={StaticResource DateTimeBooleanToVisibilityConverter}}">
                                            <RepeatButton.Content>
                                                <Path
                                                    x:Name="downbuttonpath"
                                                    Width="8"
                                                    Height="4"
                                                    HorizontalAlignment="Stretch"
                                                    VerticalAlignment="Stretch"
                                                    Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                                    Stretch="Uniform" >
                                                    <Path.Data>
                                                        <PathGeometry>M0.5 1.125C0.5 1.02344 0.537109 0.935547 0.611328 0.861328C0.685547 0.787109 0.773438 0.75 0.875 0.75C0.976562 0.75 1.06445 0.787109 1.13867 0.861328L5 4.7168L8.86133 0.861328C8.93555 0.787109 9.02344 0.75 9.125 0.75C9.22656 0.75 9.31445 0.787109 9.38867 0.861328C9.46289 0.935547 9.5 1.02344 9.5 1.125C9.5 1.22656 9.46289 1.31445 9.38867 1.38867L5.26367 5.51367C5.18945 5.58789 5.10156 5.625 5 5.625C4.89844 5.625 4.81055 5.58789 4.73633 5.51367L0.611328 1.38867C0.537109 1.31445 0.5 1.22656 0.5 1.125Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </RepeatButton.Content>
                                        </RepeatButton>
                                    </Grid>

                                    <Popup
                                        x:Name="PART_Popup"
                                        Grid.Row="1"
                                        AllowsTransparency="True"
                                        IsOpen="False"
                                        Placement="Bottom"
                                        PlacementTarget="{Binding ElementName=Border}"
                                        StaysOpen="False">
                                        <Border
                                            x:Name="CombinedBorder"
                                            Margin="16 1 16 16"
                                            Background="{StaticResource PopupBackground}"
                                            BorderBrush="{StaticResource Border}"
                                            
                                            CornerRadius="4"
                                            Effect="{StaticResource Default.ShadowDepth4}"
                                            SnapsToDevicePixels="True">
                                            <Grid x:Name="PART_PopupGrid_Classic" Background="Transparent">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="*" />
                                                    <RowDefinition Height="Auto" />
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="*" />
                                                </Grid.ColumnDefinitions>
                                                <ContentControl
                                                    x:Name="CombinedCalendar"
                                                    Grid.Column="0"
                                                    Margin="4"
                                                    VerticalAlignment="Center"
                                                    Content="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=DateTimeCalender, Mode=TwoWay}"
                                                    Visibility="Visible" />
                                                <Border
                                                    x:Name="Separator"
                                                    Grid.Column="1"
                                                    Width="1"
                                                    Margin="0,32,0,32"
                                                    Background="{StaticResource BorderAlt}"
                                                    SnapsToDevicePixels="True" />
                                                <ContentControl
                                                    x:Name="CombinedClock"
                                                    Grid.Column="2"
                                                    Content="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Clock, Mode=TwoWay}"
                                                    Visibility="Visible" />

                                                <Border
                                                    x:Name="Footer"
                                                    Grid.Row="1"
                                                    Grid.ColumnSpan="4"
                                                    Background="{StaticResource PopupBackground}"
                                                    BorderBrush="{StaticResource BorderAlt}"
                                                    BorderThickness="0,1,0,0"
                                                    SnapsToDevicePixels="True"
                                                    CornerRadius="4">
                                                    <Grid x:Name="FooterHost">
                                                        <Grid x:Name="CalendarFooter" HorizontalAlignment="Left">
                                                            <Button
                                                                x:Name="todayButton"
                                                                Margin="4"
                                                                Padding="8,4"
                                                                
                                                                BorderThickness="1"
                                                                Content="{Sync_Shared_Resources:SharedLocalizationResourceExtension ResourceName=TodayLabel}"
                                                                SnapsToDevicePixels="True"
                                                                Style="{StaticResource WPFButtonStyle}" />
                                                        </Grid>

                                                        <Grid x:Name="ClockFooter" HorizontalAlignment="Stretch">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*" />
                                                                <ColumnDefinition Width="*" />
                                                                <ColumnDefinition Width="*" />
                                                                <ColumnDefinition Width="*" />
                                                            </Grid.ColumnDefinitions>
                                                            <Button
                                                                x:Name="noneButton"
                                                                Margin="4"
                                                                Padding="8,4"
                                                                
                                                                BorderThickness="1"
                                                                Content="{Sync_Shared_Resources:SharedLocalizationResourceExtension ResourceName=AccessEmptyDateText}"
                                                                Style="{StaticResource WPFButtonStyle}"
                                                                Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IsEmptyDateEnabled, Converter={StaticResource DateTimeBooleanToVisibilityConverter}}" />
                                                            <Border
                                                                x:Name="ButtonSeparator"
                                                                Grid.Column="1"
                                                                Width="1"
                                                                Margin="4"
                                                                Background="{StaticResource BorderAlt}"
                                                                SnapsToDevicePixels="True" />
                                                            <Button
                                                                x:Name="okButton"
                                                                Grid.Column="2"
                                                                Margin="4"
                                                                Padding="8,4"
                                                                
                                                                BorderThickness="1"
                                                                Content="{Sync_Shared_Resources:SharedLocalizationResourceExtension ResourceName=OkLabel}"
                                                                Style="{StaticResource WPFPrimaryButtonStyle}" />
                                                            <Button
                                                                x:Name="cancelButton"
                                                                Grid.Column="3"
                                                                Margin="4"
                                                                Padding="8,4"
                                                                
                                                                BorderThickness="1"
                                                                Content="{Sync_Shared_Resources:SharedLocalizationResourceExtension ResourceName=CancelLabel}"
                                                                Style="{StaticResource WPFButtonStyle}" />
                                                        </Grid>
                                                    </Grid>
                                                </Border>
                                            </Grid>
                                        </Border>

                                    </Popup>
                                </Grid>
                            </Grid>
                        </Border>

                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="MinHeight" TargetName="PART_DropDown" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="MinWidth" TargetName="PART_DropDown" Value="{StaticResource TouchMode.MinSize}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource DateTimeEditBorderBrushHovered}" />
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt5}" />
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>

                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource DateTimeEditBorderBrushFocused}" />
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}" />
                        </Trigger>
                        <Trigger SourceName="PART_Popup" Property="IsOpen" Value="true">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt2}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant2}" />
                        </Trigger>
                        <Trigger SourceName="PART_UpArrow" Property="IsPressed" Value="true">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt2}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant2}" />
                        </Trigger>
                        <Trigger SourceName="PART_DownArrow" Property="IsPressed" Value="true">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt2}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant2}" />
                        </Trigger>
                        <Trigger SourceName="PART_UpArrow" Property="IsMouseOver" Value="true">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource DateTimeEditBorderBrushFocused}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant2}" />
                        </Trigger>
                        <Trigger SourceName="PART_DownArrow" Property="IsMouseOver" Value="true">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource DateTimeEditBorderBrushFocused}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant2}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocusWithin" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource DateTimeEditBorderBrushFocused}" />
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}" />
                            <Setter TargetName="PART_DropDown"  Property="Padding" Value="5,3,5,2" />
                        </Trigger>

                        <Trigger Property="DropDownView" Value="Calendar">
                            <Setter TargetName="CombinedCalendar" Property="Visibility" Value="Visible" />
                            <Setter TargetName="CombinedClock" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="Separator" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="cancelButton" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="okButton" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="todayButton" Property="Visibility" Value="Visible" />
                            <Setter TargetName="CalendarFooter" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ClockFooter" Property="HorizontalAlignment" Value="Right" />
                            <Setter TargetName="path" Property="Data" Value="M12.4031 0.0373535C12.2847 0.0124512 12.1633 0 12.0391 0H1.96094C1.70056 0 1.45056 0.0546875 1.21094 0.164062C0.976562 0.268311 0.768188 0.411377 0.585938 0.59375C0.408813 0.770752 0.265625 0.979248 0.15625 1.21875C0.052124 1.45312 0 1.70044 0 1.96094V12.0391C0 12.1633 0.0118408 12.2852 0.0355225 12.4045C0.0615234 12.5356 0.101807 12.6638 0.15625 12.7891C0.265625 13.0234 0.408813 13.2317 0.585938 13.4141C0.768188 13.5911 0.976562 13.7344 1.21094 13.8438C1.45056 13.948 1.70056 14 1.96094 14H12.0391C12.2994 14 12.5469 13.948 12.7812 13.8438C13.0209 13.7344 13.2291 13.5911 13.4062 13.4141C13.5885 13.2317 13.7318 13.0234 13.8359 12.7891C13.9453 12.5496 14 12.2996 14 12.0391V1.96094C14 1.70044 13.9453 1.45312 13.8359 1.21875C13.7318 0.979248 13.5885 0.770752 13.4062 0.59375C13.2291 0.411377 13.0209 0.268311 12.7812 0.164062C12.6588 0.106934 12.5327 0.0646973 12.4031 0.0373535ZM1.60938 1.07812C1.72913 1.02612 1.85938 1 2 1H12C12.1354 1 12.2631 1.02612 12.3828 1.07812C12.5078 1.13013 12.6146 1.20312 12.7031 1.29688C12.7969 1.3855 12.8698 1.49219 12.9219 1.61719C12.974 1.73706 13 1.8645 13 2V3H7H6H1V2C1 1.85938 1.026 1.72925 1.07812 1.60938C1.11084 1.53418 1.15063 1.46533 1.19763 1.40234C1.22559 1.36499 1.2561 1.32983 1.28906 1.29688C1.38281 1.20312 1.48962 1.13013 1.60938 1.07812ZM6 4H1V12C1 12.0476 1.00293 12.0942 1.00891 12.1399C1.02051 12.2295 1.04358 12.3157 1.07812 12.3984C1.13025 12.5183 1.20056 12.6223 1.28906 12.7109C1.37756 12.7996 1.48181 12.8699 1.60156 12.9219C1.72656 12.9739 1.85938 13 2 13H12C12.1406 13 12.2709 12.9739 12.3906 12.9219C12.4536 12.8945 12.5123 12.8623 12.5667 12.8247C12.6156 12.791 12.6611 12.7529 12.7031 12.7109C12.7474 12.6667 12.787 12.6196 12.822 12.5696C12.8611 12.5134 12.8944 12.4539 12.9219 12.3906C12.974 12.2708 13 12.1406 13 12V4H7H6ZM3.07812 6.60938C3.026 6.72925 3 6.85938 3 7C3 7.14062 3.026 7.27344 3.07812 7.39844C3.13025 7.51831 3.20056 7.62231 3.28906 7.71094C3.33252 7.75439 3.37976 7.79346 3.43079 7.82812C3.48364 7.86426 3.54065 7.89551 3.60156 7.92188C3.72656 7.97388 3.85938 8 4 8C4.08459 8 4.16528 7.99072 4.24231 7.97168C4.29346 7.95923 4.3429 7.94263 4.39062 7.92188C4.51038 7.86987 4.61462 7.79956 4.70312 7.71094C4.79688 7.61719 4.86975 7.5105 4.92188 7.39062C4.974 7.26562 5 7.13281 5 6.99219C5 6.85156 4.974 6.72144 4.92188 6.60156C4.86975 6.48169 4.79944 6.37769 4.71094 6.28906C4.62244 6.20044 4.51819 6.13013 4.39844 6.07812C4.27869 6.02612 4.14844 6 4.00781 6C3.86719 6 3.73438 6.02612 3.60938 6.07812C3.48962 6.13013 3.38281 6.20312 3.28906 6.29688C3.20056 6.3855 3.13025 6.4895 3.07812 6.60938ZM6.00879 6.8623C6.00293 6.90698 6 6.95288 6 7C6 7.14062 6.026 7.27344 6.07812 7.39844C6.13025 7.51831 6.20056 7.62231 6.28906 7.71094C6.33032 7.7522 6.375 7.78955 6.4231 7.823C6.47815 7.86108 6.53772 7.89404 6.60156 7.92188C6.72656 7.97388 6.85938 8 7 8C7.07935 8 7.1554 7.9917 7.22815 7.9751C7.2843 7.9624 7.3385 7.94458 7.39062 7.92188C7.51038 7.86987 7.61462 7.79956 7.70312 7.71094C7.79688 7.61719 7.86975 7.5105 7.92188 7.39062C7.974 7.26562 8 7.13281 8 6.99219C8 6.85156 7.974 6.72144 7.92188 6.60156C7.86975 6.48169 7.79944 6.37769 7.71094 6.28906C7.62244 6.20044 7.51819 6.13013 7.39844 6.07812C7.27869 6.02612 7.14844 6 7.00781 6C6.86719 6 6.73438 6.02612 6.60938 6.07812C6.48962 6.13013 6.38281 6.20312 6.28906 6.29688C6.20056 6.3855 6.13025 6.4895 6.07812 6.60938C6.04346 6.68921 6.02026 6.77344 6.00879 6.8623ZM10.9219 7.39062C10.974 7.26562 11 7.13281 11 6.99219C11 6.90967 10.9911 6.83057 10.9731 6.75513C10.9604 6.70215 10.9434 6.65088 10.9219 6.60156C10.8698 6.48169 10.7994 6.37769 10.7109 6.28906C10.6224 6.20044 10.5182 6.13013 10.3984 6.07812C10.2787 6.02612 10.1484 6 10.0078 6C9.86719 6 9.73438 6.02612 9.60938 6.07812C9.48962 6.13013 9.38281 6.20312 9.28906 6.29688C9.20056 6.3855 9.13025 6.4895 9.07812 6.60938C9.026 6.72925 9 6.85938 9 7C9 7.14062 9.026 7.27344 9.07812 7.39844C9.13025 7.51831 9.20056 7.62231 9.28906 7.71094C9.37756 7.79956 9.48181 7.86987 9.60156 7.92188C9.72656 7.97388 9.85938 8 10 8C10.1406 8 10.2709 7.97388 10.3906 7.92188C10.5104 7.86987 10.6146 7.79956 10.7031 7.71094C10.7969 7.61719 10.8698 7.5105 10.9219 7.39062ZM4.97803 10.2146C4.99268 10.146 5 10.0745 5 10C5 9.85938 4.974 9.72925 4.92188 9.60938C4.86975 9.48438 4.79944 9.37769 4.71094 9.28906C4.62244 9.20044 4.51562 9.13013 4.39062 9.07812C4.27087 9.02612 4.14062 9 4 9C3.85938 9 3.72913 9.02612 3.60938 9.07812C3.48962 9.13013 3.38281 9.20312 3.28906 9.29688C3.20056 9.3855 3.13025 9.49219 3.07812 9.61719C3.026 9.73706 3 9.86719 3 10.0078C3 10.1484 3.026 10.2786 3.07812 10.3984C3.11499 10.4832 3.16089 10.5601 3.21594 10.6292C3.23877 10.6577 3.26318 10.6851 3.28906 10.7109C3.37756 10.7996 3.48181 10.8699 3.60156 10.9219C3.72131 10.9739 3.85156 11 3.99219 11C4.13281 11 4.26306 10.9739 4.38281 10.9219C4.50781 10.8699 4.61462 10.7996 4.70312 10.7109C4.79688 10.6172 4.86975 10.5105 4.92188 10.3906C4.94641 10.3342 4.96509 10.2756 4.97803 10.2146ZM7.92188 10.3906C7.974 10.2708 8 10.1406 8 10C8 9.94702 7.99634 9.89551 7.98889 9.84546C7.97668 9.7627 7.95435 9.68408 7.92188 9.60938C7.88953 9.53174 7.8501 9.46094 7.80371 9.39746C7.77539 9.35864 7.74451 9.32251 7.71094 9.28906C7.62244 9.20044 7.51562 9.13013 7.39062 9.07812C7.27087 9.02612 7.14062 9 7 9C6.85938 9 6.72913 9.02612 6.60938 9.07812C6.53406 9.11084 6.46387 9.15186 6.3988 9.20093C6.36047 9.22998 6.32385 9.26196 6.28906 9.29688C6.20056 9.3855 6.13025 9.49219 6.07812 9.61719C6.026 9.73706 6 9.86719 6 10.0078C6 10.1484 6.026 10.2786 6.07812 10.3984C6.13025 10.5183 6.20056 10.6223 6.28906 10.7109C6.37756 10.7996 6.48181 10.8699 6.60156 10.9219C6.72131 10.9739 6.85156 11 6.99219 11C7.13281 11 7.26306 10.9739 7.38281 10.9219C7.50781 10.8699 7.61462 10.7996 7.70312 10.7109C7.73474 10.6794 7.76392 10.6465 7.79077 10.6121C7.84363 10.5439 7.88733 10.4702 7.92188 10.3906Z"/>
                        </Trigger>
                        <Trigger Property="DropDownView" Value="Clock">
                            <Setter TargetName="CombinedCalendar" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="CombinedClock" Property="Visibility" Value="Visible" />
                            <Setter TargetName="Separator" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="cancelButton" Property="Visibility" Value="Visible" />
                            <Setter TargetName="okButton" Property="Visibility" Value="Visible" />
                            <Setter TargetName="todayButton" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="okButton" Property="Grid.Column" Value="0" />
                            <Setter TargetName="CalendarFooter" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="ClockFooter" Property="HorizontalAlignment" Value="Stretch" />
                            <Setter TargetName="path" Property="Data" Value="M5.86719 15.7188C6.54944 15.9062 7.26038 16 8 16C8.73438 16 9.44275 15.9062 10.125 15.7188C10.8073 15.5261 11.4453 15.2578 12.0391 14.9141C12.4034 14.7 12.7472 14.4602 13.0704 14.1951C13.2739 14.0281 13.4691 13.8511 13.6562 13.6641C14.1459 13.1746 14.5625 12.6328 14.9062 12.0391C15.2552 11.4453 15.5234 10.8098 15.7109 10.1328C15.9037 9.45044 16 8.7395 16 8C16 7.26562 15.9037 6.55737 15.7109 5.875C15.62 5.54395 15.51 5.22339 15.381 4.91309C15.2441 4.58398 15.0859 4.2666 14.9062 3.96094C14.5625 3.36719 14.1459 2.82812 13.6562 2.34375C13.1719 1.85425 12.6328 1.4375 12.0391 1.09375C11.4453 0.744873 10.8073 0.476562 10.125 0.289062C9.44275 0.0964355 8.73438 0 8 0C7.26562 0 6.55725 0.0964355 5.875 0.289062C5.19275 0.476562 4.55469 0.744873 3.96094 1.09375C3.36719 1.4375 2.82556 1.85425 2.33594 2.34375C1.85156 2.82812 1.43494 3.36719 1.08594 3.96094C0.742188 4.55469 0.473999 5.19263 0.28125 5.875C0.09375 6.55737 0 7.26562 0 8C0 8.73438 0.09375 9.44263 0.28125 10.125C0.473999 10.8074 0.742188 11.4453 1.08594 12.0391C1.43494 12.6328 1.85156 13.1746 2.33594 13.6641C2.82556 14.1484 3.36719 14.5652 3.96094 14.9141C4.55469 15.2578 5.19006 15.5261 5.86719 15.7188ZM7.15991 1.04907C7.43579 1.01636 7.71582 1 8 1C8.64062 1 9.25781 1.08325 9.85156 1.25C10.4506 1.41675 11.0078 1.65356 11.5234 1.96094C12.0443 2.26294 12.5182 2.62769 12.9453 3.05469C13.2076 3.31689 13.4464 3.59692 13.6615 3.89478C13.7966 4.08179 13.9225 4.27563 14.0391 4.47656C14.3463 4.99219 14.5834 5.54956 14.75 6.14844C14.9166 6.74219 15 7.35938 15 8C15 8.64062 14.9166 9.2605 14.75 9.85938C14.5834 10.4531 14.3463 11.0105 14.0391 11.5312C13.7369 12.0469 13.3724 12.5183 12.9453 12.9453C12.5182 13.3723 12.0443 13.7395 11.5234 14.0469C11.0078 14.3489 10.4506 14.5833 9.85156 14.75C9.25781 14.9167 8.64062 15 8 15C7.35938 15 6.73962 14.9167 6.14062 14.75C5.85364 14.6694 5.5752 14.573 5.3053 14.4607C5.01672 14.3408 4.73792 14.2029 4.46875 14.0469C3.95312 13.7395 3.48181 13.3723 3.05469 12.9453C2.62756 12.5183 2.26038 12.0469 1.95312 11.5312C1.651 11.0105 1.41663 10.4531 1.25 9.85938C1.08337 9.2605 1 8.64062 1 8C1 7.35938 1.08337 6.74219 1.25 6.14844C1.33167 5.85498 1.42957 5.57153 1.5437 5.29785C1.6626 5.01318 1.79907 4.7395 1.95312 4.47656C2.26038 3.95581 2.62756 3.48169 3.05469 3.05469C3.36462 2.74487 3.69788 2.46777 4.05444 2.22363C4.18921 2.13135 4.32727 2.0437 4.46875 1.96094C4.98962 1.65356 5.54688 1.41675 6.14062 1.25C6.474 1.15723 6.81372 1.09033 7.15991 1.04907ZM7 8.33936C7 8.70435 7.29578 9 7.66052 9H9H10H10.5C10.7761 9 11 8.77612 11 8.5C11 8.22388 10.7761 8 10.5 8H10H9H8V3.5C8 3.22388 7.77612 3 7.5 3C7.22388 3 7 3.22388 7 3.5V8.33936Z"/>
                        </Trigger>
                        <Trigger Property="DropDownView" Value="Combined">
                            <Setter TargetName="CombinedCalendar" Property="Visibility" Value="Visible" />
                            <Setter TargetName="CombinedClock" Property="Visibility" Value="Visible" />
                            <Setter TargetName="Separator" Property="Visibility" Value="Visible" />
                            <Setter TargetName="todayButton" Property="Visibility" Value="Visible" />
                            <Setter TargetName="cancelButton" Property="Visibility" Value="Visible" />
                            <Setter TargetName="okButton" Property="Visibility" Value="Visible" />
                            <Setter TargetName="okButton" Property="Grid.Column" Value="2" />
                            <Setter TargetName="CalendarFooter" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ClockFooter" Property="HorizontalAlignment" Value="Right" />
                            <Setter TargetName="CalendarFooter" Property="HorizontalAlignment" Value="Left" />
                            <Setter TargetName="path" Property="Data" Value="M1.50781 13.8047C1.80469 13.9348 2.11719 14 2.44531 14H5.9375C5.87683 13.9209 5.81812 13.8403 5.76147 13.7585C5.7063 13.679 5.65295 13.5979 5.60156 13.5156C5.50256 13.3489 5.40881 13.177 5.32031 13H2.5C2.29688 13 2.10413 12.9609 1.92188 12.8828C1.73962 12.8047 1.57812 12.698 1.4375 12.5625C1.30212 12.4219 1.19531 12.2605 1.11719 12.0781C1.0636 11.9531 1.02844 11.8232 1.0116 11.6885C1.00391 11.6267 1 11.564 1 11.5V4H7H9H13V5.32031C13.1771 5.40894 13.349 5.50513 13.5156 5.60938C13.6823 5.70825 13.8438 5.81763 14 5.9375V2.44531C14 2.28735 13.9844 2.13306 13.953 1.98242C13.9191 1.82007 13.8671 1.66187 13.7969 1.50781C13.6666 1.21094 13.4896 0.953125 13.2656 0.734375C13.1288 0.594482 12.9767 0.472656 12.8093 0.369385C12.7091 0.307373 12.6034 0.251953 12.4922 0.203125C12.1953 0.067627 11.8828 0 11.5547 0H2.44531C2.11719 0 1.80469 0.067627 1.50781 0.203125C1.21094 0.333252 0.950562 0.510498 0.726562 0.734375C0.507812 0.953125 0.330688 1.21094 0.195312 1.50781C0.0650635 1.80469 0 2.11719 0 2.44531V11.5547C0 11.8828 0.0650635 12.1953 0.195312 12.4922C0.330688 12.7891 0.507812 13.0496 0.726562 13.2734C0.950562 13.4922 1.21094 13.6692 1.50781 13.8047ZM9 3H13V2.5C13 2.29688 12.9609 2.10425 12.8828 1.92188C12.8416 1.82568 12.7916 1.73608 12.733 1.65283C12.6805 1.57837 12.6211 1.50928 12.5547 1.44531C12.4193 1.30469 12.2604 1.19531 12.0781 1.11719C11.8959 1.03906 11.7031 1 11.5 1H2.5C2.29688 1 2.10413 1.03906 1.92188 1.11719C1.73962 1.19531 1.57812 1.30469 1.4375 1.44531C1.30212 1.58081 1.19531 1.7395 1.11719 1.92188C1.03906 2.10425 1 2.29688 1 2.5V3H7H9ZM6.35156 8.75C6.11719 9.29688 6 9.88013 6 10.5C6 11.125 6.11719 11.7109 6.35156 12.2578C6.58594 12.8047 6.90625 13.2812 7.3125 13.6875C7.71875 14.0938 8.19531 14.4141 8.74219 14.6484C9.28906 14.8828 9.875 15 10.5 15C10.7916 15 11.0752 14.9741 11.3507 14.9221C11.6608 14.8638 11.9604 14.7725 12.25 14.6484C12.7969 14.4089 13.2734 14.0859 13.6797 13.6797C14.0859 13.2734 14.4062 12.7969 14.6406 12.25C14.8802 11.7031 15 11.1199 15 10.5C15 10.0886 14.9453 9.69263 14.8359 9.3125C14.7318 8.927 14.5807 8.56763 14.3828 8.23438C14.1901 7.90112 13.9557 7.59644 13.6797 7.32031C13.4037 7.04419 13.099 6.80981 12.7656 6.61719C12.5742 6.50342 12.3743 6.40527 12.1656 6.32251C12.011 6.26123 11.8516 6.2085 11.6875 6.16406C11.3073 6.05469 10.9115 6 10.5 6C9.88025 6 9.29688 6.11987 8.75 6.35938C8.20312 6.59375 7.72656 6.91406 7.32031 7.32031C6.91406 7.72656 6.59119 8.20312 6.35156 8.75ZM12.3516 10.8516C12.2526 10.9504 12.1354 11 12 11H10.5C10.3646 11 10.2474 10.9504 10.1484 10.8516C10.1011 10.8042 10.0651 10.7527 10.0403 10.697C10.0134 10.6362 10 10.5706 10 10.5V8.5C10 8.3645 10.0494 8.24731 10.1484 8.14844C10.2135 8.0835 10.2863 8.03979 10.3669 8.01758C10.4092 8.00586 10.4535 8 10.5 8C10.6354 8 10.7526 8.04956 10.8516 8.14844C10.9506 8.24731 11 8.3645 11 8.5V10H12C12.1354 10 12.2526 10.0496 12.3516 10.1484C12.4506 10.2473 12.5 10.3645 12.5 10.5C12.5 10.6355 12.4506 10.7527 12.3516 10.8516Z"/>
                        </Trigger>

                        <Trigger Property="DropDownView" Value="Classic">
                            <Setter TargetName="CombinedCalendar" Property="Visibility" Value="Visible" />
                            <Setter TargetName="CombinedClock" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="Separator" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="cancelButton" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="okButton" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="todayButton" Property="Visibility" Value="Visible" />
                            <Setter TargetName="CalendarFooter" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ClockFooter" Property="HorizontalAlignment" Value="Right" />
                            <Setter TargetName="path" Property="Data" Value="M12.4031 0.0373535C12.2847 0.0124512 12.1633 0 12.0391 0H1.96094C1.70056 0 1.45056 0.0546875 1.21094 0.164062C0.976562 0.268311 0.768188 0.411377 0.585938 0.59375C0.408813 0.770752 0.265625 0.979248 0.15625 1.21875C0.052124 1.45312 0 1.70044 0 1.96094V12.0391C0 12.1633 0.0118408 12.2852 0.0355225 12.4045C0.0615234 12.5356 0.101807 12.6638 0.15625 12.7891C0.265625 13.0234 0.408813 13.2317 0.585938 13.4141C0.768188 13.5911 0.976562 13.7344 1.21094 13.8438C1.45056 13.948 1.70056 14 1.96094 14H12.0391C12.2994 14 12.5469 13.948 12.7812 13.8438C13.0209 13.7344 13.2291 13.5911 13.4062 13.4141C13.5885 13.2317 13.7318 13.0234 13.8359 12.7891C13.9453 12.5496 14 12.2996 14 12.0391V1.96094C14 1.70044 13.9453 1.45312 13.8359 1.21875C13.7318 0.979248 13.5885 0.770752 13.4062 0.59375C13.2291 0.411377 13.0209 0.268311 12.7812 0.164062C12.6588 0.106934 12.5327 0.0646973 12.4031 0.0373535ZM1.60938 1.07812C1.72913 1.02612 1.85938 1 2 1H12C12.1354 1 12.2631 1.02612 12.3828 1.07812C12.5078 1.13013 12.6146 1.20312 12.7031 1.29688C12.7969 1.3855 12.8698 1.49219 12.9219 1.61719C12.974 1.73706 13 1.8645 13 2V3H7H6H1V2C1 1.85938 1.026 1.72925 1.07812 1.60938C1.11084 1.53418 1.15063 1.46533 1.19763 1.40234C1.22559 1.36499 1.2561 1.32983 1.28906 1.29688C1.38281 1.20312 1.48962 1.13013 1.60938 1.07812ZM6 4H1V12C1 12.0476 1.00293 12.0942 1.00891 12.1399C1.02051 12.2295 1.04358 12.3157 1.07812 12.3984C1.13025 12.5183 1.20056 12.6223 1.28906 12.7109C1.37756 12.7996 1.48181 12.8699 1.60156 12.9219C1.72656 12.9739 1.85938 13 2 13H12C12.1406 13 12.2709 12.9739 12.3906 12.9219C12.4536 12.8945 12.5123 12.8623 12.5667 12.8247C12.6156 12.791 12.6611 12.7529 12.7031 12.7109C12.7474 12.6667 12.787 12.6196 12.822 12.5696C12.8611 12.5134 12.8944 12.4539 12.9219 12.3906C12.974 12.2708 13 12.1406 13 12V4H7H6ZM3.07812 6.60938C3.026 6.72925 3 6.85938 3 7C3 7.14062 3.026 7.27344 3.07812 7.39844C3.13025 7.51831 3.20056 7.62231 3.28906 7.71094C3.33252 7.75439 3.37976 7.79346 3.43079 7.82812C3.48364 7.86426 3.54065 7.89551 3.60156 7.92188C3.72656 7.97388 3.85938 8 4 8C4.08459 8 4.16528 7.99072 4.24231 7.97168C4.29346 7.95923 4.3429 7.94263 4.39062 7.92188C4.51038 7.86987 4.61462 7.79956 4.70312 7.71094C4.79688 7.61719 4.86975 7.5105 4.92188 7.39062C4.974 7.26562 5 7.13281 5 6.99219C5 6.85156 4.974 6.72144 4.92188 6.60156C4.86975 6.48169 4.79944 6.37769 4.71094 6.28906C4.62244 6.20044 4.51819 6.13013 4.39844 6.07812C4.27869 6.02612 4.14844 6 4.00781 6C3.86719 6 3.73438 6.02612 3.60938 6.07812C3.48962 6.13013 3.38281 6.20312 3.28906 6.29688C3.20056 6.3855 3.13025 6.4895 3.07812 6.60938ZM6.00879 6.8623C6.00293 6.90698 6 6.95288 6 7C6 7.14062 6.026 7.27344 6.07812 7.39844C6.13025 7.51831 6.20056 7.62231 6.28906 7.71094C6.33032 7.7522 6.375 7.78955 6.4231 7.823C6.47815 7.86108 6.53772 7.89404 6.60156 7.92188C6.72656 7.97388 6.85938 8 7 8C7.07935 8 7.1554 7.9917 7.22815 7.9751C7.2843 7.9624 7.3385 7.94458 7.39062 7.92188C7.51038 7.86987 7.61462 7.79956 7.70312 7.71094C7.79688 7.61719 7.86975 7.5105 7.92188 7.39062C7.974 7.26562 8 7.13281 8 6.99219C8 6.85156 7.974 6.72144 7.92188 6.60156C7.86975 6.48169 7.79944 6.37769 7.71094 6.28906C7.62244 6.20044 7.51819 6.13013 7.39844 6.07812C7.27869 6.02612 7.14844 6 7.00781 6C6.86719 6 6.73438 6.02612 6.60938 6.07812C6.48962 6.13013 6.38281 6.20312 6.28906 6.29688C6.20056 6.3855 6.13025 6.4895 6.07812 6.60938C6.04346 6.68921 6.02026 6.77344 6.00879 6.8623ZM10.9219 7.39062C10.974 7.26562 11 7.13281 11 6.99219C11 6.90967 10.9911 6.83057 10.9731 6.75513C10.9604 6.70215 10.9434 6.65088 10.9219 6.60156C10.8698 6.48169 10.7994 6.37769 10.7109 6.28906C10.6224 6.20044 10.5182 6.13013 10.3984 6.07812C10.2787 6.02612 10.1484 6 10.0078 6C9.86719 6 9.73438 6.02612 9.60938 6.07812C9.48962 6.13013 9.38281 6.20312 9.28906 6.29688C9.20056 6.3855 9.13025 6.4895 9.07812 6.60938C9.026 6.72925 9 6.85938 9 7C9 7.14062 9.026 7.27344 9.07812 7.39844C9.13025 7.51831 9.20056 7.62231 9.28906 7.71094C9.37756 7.79956 9.48181 7.86987 9.60156 7.92188C9.72656 7.97388 9.85938 8 10 8C10.1406 8 10.2709 7.97388 10.3906 7.92188C10.5104 7.86987 10.6146 7.79956 10.7031 7.71094C10.7969 7.61719 10.8698 7.5105 10.9219 7.39062ZM4.97803 10.2146C4.99268 10.146 5 10.0745 5 10C5 9.85938 4.974 9.72925 4.92188 9.60938C4.86975 9.48438 4.79944 9.37769 4.71094 9.28906C4.62244 9.20044 4.51562 9.13013 4.39062 9.07812C4.27087 9.02612 4.14062 9 4 9C3.85938 9 3.72913 9.02612 3.60938 9.07812C3.48962 9.13013 3.38281 9.20312 3.28906 9.29688C3.20056 9.3855 3.13025 9.49219 3.07812 9.61719C3.026 9.73706 3 9.86719 3 10.0078C3 10.1484 3.026 10.2786 3.07812 10.3984C3.11499 10.4832 3.16089 10.5601 3.21594 10.6292C3.23877 10.6577 3.26318 10.6851 3.28906 10.7109C3.37756 10.7996 3.48181 10.8699 3.60156 10.9219C3.72131 10.9739 3.85156 11 3.99219 11C4.13281 11 4.26306 10.9739 4.38281 10.9219C4.50781 10.8699 4.61462 10.7996 4.70312 10.7109C4.79688 10.6172 4.86975 10.5105 4.92188 10.3906C4.94641 10.3342 4.96509 10.2756 4.97803 10.2146ZM7.92188 10.3906C7.974 10.2708 8 10.1406 8 10C8 9.94702 7.99634 9.89551 7.98889 9.84546C7.97668 9.7627 7.95435 9.68408 7.92188 9.60938C7.88953 9.53174 7.8501 9.46094 7.80371 9.39746C7.77539 9.35864 7.74451 9.32251 7.71094 9.28906C7.62244 9.20044 7.51562 9.13013 7.39062 9.07812C7.27087 9.02612 7.14062 9 7 9C6.85938 9 6.72913 9.02612 6.60938 9.07812C6.53406 9.11084 6.46387 9.15186 6.3988 9.20093C6.36047 9.22998 6.32385 9.26196 6.28906 9.29688C6.20056 9.3855 6.13025 9.49219 6.07812 9.61719C6.026 9.73706 6 9.86719 6 10.0078C6 10.1484 6.026 10.2786 6.07812 10.3984C6.13025 10.5183 6.20056 10.6223 6.28906 10.7109C6.37756 10.7996 6.48181 10.8699 6.60156 10.9219C6.72131 10.9739 6.85156 11 6.99219 11C7.13281 11 7.26306 10.9739 7.38281 10.9219C7.50781 10.8699 7.61462 10.7996 7.70312 10.7109C7.73474 10.6794 7.76392 10.6465 7.79077 10.6121C7.84363 10.5439 7.88733 10.4702 7.92188 10.3906Z"/>
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="DropDownView" Value="Clock" />
                                <Condition Property="IsEmptyDateEnabled" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="noneButton" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="ButtonSeparator" Property="Visibility" Value="Collapsed" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="DropDownView" Value="Calendar" />
                                <Condition Property="IsEmptyDateEnabled" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="noneButton" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ButtonSeparator" Property="Visibility" Value="Collapsed" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="DropDownView" Value="Classic" />
                                <Condition Property="IsEmptyDateEnabled" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="noneButton" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ButtonSeparator" Property="Visibility" Value="Collapsed" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="DropDownView" Value="Calendar" />
                                <Condition Property="IsEmptyDateEnabled" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="ButtonSeparator" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="CalendarFooter" Property="HorizontalAlignment" Value="Stretch" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="DropDownView" Value="Classic" />
                                <Condition Property="IsEmptyDateEnabled" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="ButtonSeparator" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="CalendarFooter" Property="HorizontalAlignment" Value="Stretch" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="DropDownView" Value="Combined" />
                                <Condition Property="IsEmptyDateEnabled" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="noneButton" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ButtonSeparator" Property="Visibility" Value="Visible" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="DropDownView" Value="Combined" />
                                <Condition Property="IsEmptyDateEnabled" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="noneButton" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="ButtonSeparator" Property="Visibility" Value="Collapsed" />
                        </MultiTrigger>

                        <Trigger Property="IsReadOnly" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter TargetName="PART_DropDown" Property="Visibility" Value="Collapsed" /> 
                            <Setter TargetName="PART_ContentHost" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsKeyboardFocusWithin" Value="True"/>
                                <Condition Property="IsReadOnly" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt2}" />
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter TargetName="PART_DropDown" Property="Visibility" Value="Collapsed" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}" />
                            <Setter TargetName="PART_ContentHost" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>                       
                      
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionDateTimeEditStyle}" TargetType="{x:Type shared:DateTimeEdit}" />
</ResourceDictionary>
