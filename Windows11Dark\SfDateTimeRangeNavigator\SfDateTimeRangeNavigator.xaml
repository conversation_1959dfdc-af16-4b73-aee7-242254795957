<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:local="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF"
                    xmlns:System="clr-namespace:System;assembly=mscorlib"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <Thickness x:Key="SfDateTimeRangeNavigator.Tooltip.Static.Padding">0,0,0,0</Thickness>
    <Thickness x:Key="SfDateTimeRangeNavigator.Tooltip.Path.Static.Margin">0,0,0,0</Thickness>
    <Thickness x:Key="SfDateTimeRangeNavigator.Tooltip.TextBlock.Static.Padding">0,0,0,0</Thickness>

            <DataTemplate x:Key="leftTooltipTemplate">
        <Border Effect="{StaticResource Default.ShadowDepth3}"
             Background="{StaticResource TooltipBackground}"
             CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
            <Grid>
                <Path x:Name="light_left"
               Data="F1M398.0605,350.6934L377.7585,350.6934L372.3835,350.6934L372.3215,350.6934C370.2155,350.6934,368.5085,352.5044,368.5085,354.7344C368.5085,356.9664,370.2155,358.7774,372.3215,358.7774L372.3835,358.7774L377.7585,358.7774L398.0605,358.7774z"
               Fill="Transparent"
               Stretch="Fill"/>
                <TextBlock Text="{Binding}"
                           Margin="5,5,5,5"
                           Foreground="{StaticResource TooltipForeground}" 
                           Padding="{StaticResource SfDateTimeRangeNavigator.Tooltip.Static.Padding}">
                </TextBlock>
            </Grid>
        </Border>
    </DataTemplate>

    <DataTemplate x:Key="rightTooltipTemplate">
        <Border Effect="{StaticResource Default.ShadowDepth3}"
                Background="{StaticResource TooltipBackground}"
                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                Margin="2,0,0,0">
            <Grid>
                <Path x:Name="dark_right"
                      Data="F1M400.5342,362.2568L420.8372,362.2568L426.2122,362.2568L426.2742,362.2568C428.3802,362.2568,430.0872,364.0658,430.0872,366.2978C430.0872,368.5298,428.3802,370.3388,426.2742,370.3388L426.2122,370.3388L420.8372,370.3388L400.5342,370.3388z"
                      Fill="Transparent"
                      Stretch="Fill" 
                      Margin="{StaticResource SfDateTimeRangeNavigator.Tooltip.Path.Static.Margin}">
                </Path>
                <TextBlock Text="{Binding}"
                           Foreground="{StaticResource TooltipForeground}" 
                           Padding="{StaticResource SfDateTimeRangeNavigator.Tooltip.TextBlock.Static.Padding}"
                           Margin="5,5,5,5">
                </TextBlock>
            </Grid>
        </Border>
    </DataTemplate>

    <Style x:Key="DefaultThumbLineStyle"
           TargetType="Line">
        <Setter Property="Stroke"
                Value="{StaticResource Series10}"></Setter>
        <Setter Property="StrokeThickness"
                Value="{StaticResource Windows11Dark.StrokeThickness1}"></Setter>
    </Style>

    <DataTemplate x:Key="DefaultSymbolTemplate">
        <Ellipse  Height="12"
                  Width="12"
                  VerticalAlignment="Center"
                  Stroke="{StaticResource Series10}"
                  StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}"
                  Fill="{StaticResource ContentBackground}"></Ellipse>
    </DataTemplate>

    <ControlTemplate x:Key="SyncfusionRangeNavigatorSelectorControlTemplate"
                     TargetType="local:RangeNavigatorSelector">
        <Grid x:Name="Root">
            <Grid.Resources>
                <!-- RepeatButton Templates -->
                <ControlTemplate x:Key="RepeatButtonTemplate"
                                 TargetType="RepeatButton">
                    <Grid x:Name="Root"
                          Background="{Binding RelativeSource={RelativeSource TemplatedParent},Path=Background}">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                </ControlTemplate>
                <ControlTemplate x:Key="HorizontalIncrementTemplate"
                                 TargetType="RepeatButton">
                    <Grid x:Name="Root"
                          Margin="-1,0,0,0">
                        <Rectangle x:Name="Background"
                                   Opacity="0"
                                   Fill="{StaticResource ContentBackground}"
                                   StrokeThickness="1">
                        </Rectangle>
                        <Rectangle x:Name="Highlight"
                                   Opacity="0"
                                   IsHitTestVisible="false"
                                   Stroke="{StaticResource BorderAlt}"
                                   StrokeThickness="1"
                                   Margin="4,0,0,0" />
                        <TextBlock Text="&#xe70b;"
                                   FontSize="16"
                                   FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                   Foreground="{StaticResource ContentBackground}"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"/>
                        <Rectangle x:Name="DisabledElement"
                                   Opacity="0"
                                   Fill="#FFFFFFFF" />
                    </Grid>
                </ControlTemplate>
                <ControlTemplate x:Key="HorizontalDecrementTemplate"
                                 TargetType="RepeatButton">
                    <Grid x:Name="Root"
                          Margin="-1,0,0,0">
                        <Rectangle x:Name="Background"
                                   Opacity="0"
                                   Fill="{StaticResource ContentBackground}"
                                   StrokeThickness="1"
                                   Margin="4">
                        </Rectangle>
                        <TextBlock Text="&#xe70a;"
                                   FontSize="16"
                                   FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                   Foreground="{StaticResource ContentBackground}"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"/>
                        <Rectangle x:Name="DisabledElement"
                                   Opacity="0"
                                   Fill="{StaticResource ContentBackgroundAlt1}" />
                    </Grid>
                </ControlTemplate>

                <!-- Vertical Inc/Dec Templates -->
                <ControlTemplate x:Key="VerticalIncrementTemplate"
                                 TargetType="RepeatButton">
                    <Grid x:Name="Root"
                          Margin="1,0,0,0">
                        <Rectangle x:Name="Background"
                                   Opacity="0"
                                   RadiusX="8"
                                   RadiusY="8"
                                   Fill="{StaticResource ContentBackground}"
                                   StrokeThickness="1"></Rectangle>
                        <Rectangle x:Name="Highlight"
                                   Opacity="0"
                                   RadiusX="8"
                                   RadiusY="8"
                                   IsHitTestVisible="false"
                                   Stroke="{StaticResource BorderAlt}"
                                   StrokeThickness="1"
                                   Margin="4,0,0,0" />
                        <TextBlock Text="&#xe708;"
                                   FontSize="16"
                                   FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                   Foreground="{StaticResource ContentBackground}"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"/>
                        <Rectangle x:Name="DisabledElement"
                                   Opacity="0"
                                   RadiusX="8"
                                   RadiusY="8"
                                   Fill="{StaticResource ContentBackgroundAlt1}" />
                    </Grid>
                </ControlTemplate>
                <ControlTemplate x:Key="VerticalDecrementTemplate"
                                 TargetType="RepeatButton">
                    <Grid x:Name="Root"
                          Margin="1,0,0,0">
                        <Rectangle x:Name="Background"
                                   Opacity="0"
                                   RadiusX="8"
                                   RadiusY="8"
                                   Fill="{StaticResource ContentBackground}"
                                   StrokeThickness="1">
                        </Rectangle>
                        <Rectangle x:Name="Highlight"
                                   Opacity="0"
                                   RadiusX="1"
                                   RadiusY="1"
                                   IsHitTestVisible="false"
                                   Stroke="{StaticResource BorderAlt}"
                                   StrokeThickness="1"
                                   Margin="4,0,0,0" />
                        <TextBlock Text="&#xe709;"
                                   FontSize="16"
                                   FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                   Foreground="{StaticResource IconColor}"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"/>
                        <Rectangle x:Name="DisabledElement"
                                   Opacity="0"
                                   RadiusX="8"
                                   RadiusY="8"
                                   Fill="{StaticResource ContentBackgroundAlt1}" />
                    </Grid>
                </ControlTemplate>

                <!-- Thumb Templates -->
                <ControlTemplate x:Key="VerticalThumbTemplate"
                                 TargetType="Thumb">
                    <Grid x:Name="ThumbVisual">
                        <Rectangle x:Name="Background"
                                   Fill="{StaticResource ContentBackground}">
                        </Rectangle>
                    </Grid>
                </ControlTemplate>
                <ControlTemplate x:Key="HorizontalThumbTemplate"
                                 TargetType="Thumb">
                    <Grid x:Name="ThumbVisual">
                        <Rectangle x:Name="Background"
                                   Fill="Transparent">
                        </Rectangle>
                    </Grid>
                </ControlTemplate>
                <ControlTemplate x:Key="HorizontalPositionThumbTemplate"
                                 TargetType="Thumb">
                    <Grid x:Name="left">
                        <Line HorizontalAlignment="Right"
                              Style="{StaticResource DefaultThumbLineStyle}"
                              Stretch="Fill"
                              X1="0"
                              Y1="0"
                              X2="0"
                              Y2="1" />
                        <ContentPresenter Margin="-15,0,-15,0"
                                          ContentTemplate="{StaticResource DefaultSymbolTemplate}"></ContentPresenter>
                    </Grid>
                </ControlTemplate>

                <ControlTemplate x:Key="VerticalNearThumbTemplate"
                                 TargetType="Thumb">
                    <Border Margin="0,0,0,-1"
                            Height="18"
                            Background="{StaticResource ContentBackgroundAlt3}">
                        <Grid  Margin="4">
                            <ContentControl>
                                <Ellipse Width="9"
                                         Height="9"
                                         Fill="{StaticResource IconColor}">
                                </Ellipse>
                            </ContentControl>
                        </Grid>
                    </Border>
                </ControlTemplate>
                <ControlTemplate x:Key="VerticalFarThumbTemplate"
                                 TargetType="Thumb">
                    <Border Margin="0,-1,0,0"
                            Height="18"
                            Background="{StaticResource ContentBackgroundAlt3}">
                        <Grid Margin="4">
                            <ContentControl>
                                <Ellipse Width="9"
                                         Height="9"
                                         Fill="{StaticResource IconColor}">
                                </Ellipse>
                            </ContentControl>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Grid.Resources>

            <VisualStateManager.VisualStateGroups>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="MouseOver" />
                    <VisualState x:Name="Disabled">
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetName="Root"
                                             Storyboard.TargetProperty="Opacity"
                                             To="0.5"
                                             Duration="0" />
                        </Storyboard>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateManager.VisualStateGroups>

            <!-- Horizontal Template -->
            <Grid x:Name="HorizontalRoot"
                  Visibility="Collapsed">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <ContentPresenter Content="{TemplateBinding Content}"
                                  Grid.Column="1"
                                  Grid.ColumnSpan="5" />
                <!-- Repeat Buttons + Thumb -->
                <RepeatButton x:Name="HorizontalSmallDecrease"
                              Visibility="{TemplateBinding ScrollButtonVisibility}"
                              Grid.Column="0"
                              MinWidth="16"
                              IsTabStop="False"
                              Interval="50"
                              Template="{StaticResource HorizontalDecrementTemplate}"
                              Margin="1" />
                <RepeatButton x:Name="HorizontalLargeDecrease"
                              Grid.Column="1"
                              Width="0"
                              Template="{StaticResource RepeatButtonTemplate}"
                              Interval="50"
                              IsTabStop="False"
                              Background="{TemplateBinding OverlayBrush}" />
                <Thumb x:Name="HorizontalThumb"
                       MinWidth="0"
                       Width="20"
                       Grid.Column="3"
                       Template="{StaticResource HorizontalThumbTemplate}" />
                <RepeatButton x:Name="HorizontalLargeIncrease"
                              Grid.Column="5"
                              Width="0"
                              Interval="50"
                              Template="{StaticResource RepeatButtonTemplate}"
                              IsTabStop="False"
                              Background="{TemplateBinding OverlayBrush}" />
                <RepeatButton x:Name="HorizontalSmallIncrease"
                              Visibility="{TemplateBinding ScrollButtonVisibility}"
                              Grid.Column="6"
                              MinWidth="16"
                              IsTabStop="False"
                              Interval="50"
                              Template="{StaticResource HorizontalIncrementTemplate}"
                              Margin="1" />
                <Thumb x:Name="HorizontalThumbHand1"
                       Background="{TemplateBinding Background}"
                       Grid.Column="2"
                       Template="{StaticResource HorizontalPositionThumbTemplate}" />
                <Thumb x:Name="HorizontalThumbHand2"
                       Background="{TemplateBinding Background}"
                       Grid.Column="4"
                       Template="{StaticResource HorizontalPositionThumbTemplate}" />

            </Grid>

            <!-- Vertical Template -->
            <Grid x:Name="VerticalRoot">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!-- Track Layer -->
                <Rectangle Grid.RowSpan="7"
                           RadiusX="8"
                           RadiusY="8"
                           StrokeThickness="1"
                           Stroke="#00000000"
                           Fill="{StaticResource ContentBackground}"></Rectangle>
                <Rectangle Grid.RowSpan="7"
                           RadiusX="8"
                           RadiusY="8"
                           StrokeThickness="1"
                           Fill="{StaticResource ContentBackground}"></Rectangle>
                <Rectangle Grid.RowSpan="7"
                           RadiusX="8"
                           RadiusY="8"
                           Margin="1"
                           Stroke="{StaticResource ContentBackground}"></Rectangle>

                <!-- Repeat Buttons + Thumb -->
                <RepeatButton x:Name="VerticalSmallDecrease"
                              Visibility="{TemplateBinding ScrollButtonVisibility}"
                              Grid.Row="0"
                              MinHeight="16"
                              IsTabStop="False"
                              Interval="50"
                              Template="{StaticResource VerticalDecrementTemplate}"
                              Margin="1" />
                <RepeatButton x:Name="VerticalLargeDecrease"
                              Grid.Row="1"
                              Height="0"
                              Template="{StaticResource RepeatButtonTemplate}"
                              Interval="50"
                              IsTabStop="False"
                              Background="{TemplateBinding OverlayBrush}" />
                <Thumb x:Name="VerticalThumbHand1"
                       MinHeight="18"
                       Grid.Row="2"
                       Template="{StaticResource VerticalNearThumbTemplate}" />
                <Thumb x:Name="VerticalThumb"
                       MinHeight="0"
                       Height="18"
                       Grid.Row="3"
                       Template="{StaticResource VerticalThumbTemplate}" />
                <Thumb x:Name="VerticalThumbHand2"
                       MinHeight="18"
                       Grid.Row="4"
                       Template="{StaticResource VerticalFarThumbTemplate}" />
                <RepeatButton x:Name="VerticalLargeIncrease"
                              Grid.Row="5"
                              Template="{StaticResource RepeatButtonTemplate}"
                              Interval="50"
                              IsTabStop="False"
                              Background="{TemplateBinding OverlayBrush}" />
                <RepeatButton x:Name="VerticalSmallIncrease"
                              Grid.Row="6"
                              Visibility="{TemplateBinding ScrollButtonVisibility}"
                              MinHeight="16"
                              IsTabStop="False"
                              Interval="50"
                              Template="{StaticResource VerticalIncrementTemplate}"
                              Margin="1" />
            </Grid>
        </Grid>
    </ControlTemplate>

    <Style TargetType="TextBlock"
           x:Key="labelStyle">
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"></Setter>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.CaptionText}"></Setter>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}"></Setter>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForegroundAlt2}" />
    </Style>

    <Style TargetType="Line"
           x:Key="SyncfusionRangeNavigatorLineStyle">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="StrokeThickness"
                Value="{StaticResource Windows11Dark.StrokeThickness1}"></Setter>
    </Style>

    <Style TargetType="Line"
           x:Key="tickLineStyle">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="StrokeThickness"
                Value="{StaticResource Windows11Dark.StrokeThickness1}"></Setter>
    </Style>

    <Style x:Key="SyncfusionResizableScrollBarNavigatorStyle" TargetType="local:ResizableScrollBar">
        <Setter Property="Background"
                Value="{StaticResource ContentBackground}"></Setter>
        <Setter Property="MinWidth"
                Value="17" />
        <Setter Property="MinHeight"
                Value="17" />
        <Setter Property="IsTabStop"
                Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:ResizableScrollBar">
                    <Grid x:Name="Root">
                        <Grid.Resources>
                            <ControlTemplate x:Key="RepeatButtonTemplate"
                                             TargetType="RepeatButton">
                                <Grid x:Name="Root"
                                      Background="Transparent">
                                    <VisualStateManager.VisualStateGroups>
                                        <VisualStateGroup x:Name="CommonStates">
                                            <VisualState x:Name="Normal" />
                                        </VisualStateGroup>
                                    </VisualStateManager.VisualStateGroups>
                                </Grid>
                            </ControlTemplate>
                            <ControlTemplate x:Key="HorizontalIncrementTemplate"
                                             TargetType="RepeatButton">
                                <Grid x:Name="Root">
                                    <Rectangle x:Name="Background"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackground}"
                                               StrokeThickness="1">
                                    </Rectangle>
                                    <Rectangle x:Name="Highlight"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               IsHitTestVisible="false"
                                               Stroke="{StaticResource BorderAlt}"
                                               StrokeThickness="1"
                                               Margin="1" />
                                    <TextBlock Text="&#xe70b;"
                                               FontSize="16"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               Foreground="{StaticResource IconColor}"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                    <Rectangle x:Name="DisabledElement"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackgroundAlt1}" />
                                </Grid>
                            </ControlTemplate>
                            <ControlTemplate x:Key="HorizontalDecrementTemplate"
                                             TargetType="RepeatButton">
                                <Grid x:Name="Root">
                                    <Rectangle x:Name="Background"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackground}"
                                               StrokeThickness="1">
                                    </Rectangle>
                                    <TextBlock Text="&#xe70a;"
                                               FontSize="16"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               Foreground="{StaticResource IconColor}"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                    <Rectangle x:Name="DisabledElement"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="9"
                                               Fill="{StaticResource ContentBackgroundAlt1}" />
                                </Grid>
                            </ControlTemplate>
                            <ControlTemplate x:Key="VerticalIncrementTemplate"
                                             TargetType="RepeatButton">
                                <Grid x:Name="Root">
                                    <Rectangle x:Name="Background"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackground}"
                                               StrokeThickness="1"></Rectangle>
                                    <Rectangle x:Name="Highlight"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               IsHitTestVisible="false"
                                               Stroke="#FF6DBDD1"
                                               StrokeThickness="1"
                                               Margin="1" />
                                    <TextBlock Text="&#xe708;"
                                               FontSize="16"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               Foreground="{StaticResource IconColor}"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                    <Rectangle x:Name="DisabledElement"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackgroundAlt1}" />
                                </Grid>
                            </ControlTemplate>
                            <ControlTemplate x:Key="VerticalDecrementTemplate"
                                             TargetType="RepeatButton">
                                <Grid x:Name="Root">
                                    <Rectangle x:Name="Background"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackground}"
                                               StrokeThickness="1">
                                    </Rectangle>
                                    <Rectangle x:Name="Highlight"
                                               Opacity="0"
                                               RadiusX="1"
                                               RadiusY="1"
                                               IsHitTestVisible="false"
                                               Stroke="#FF6DBDD1"
                                               StrokeThickness="1"
                                               Margin="1" />
                                    <TextBlock Text="&#xe709;"
                                               FontSize="16"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               Foreground="{StaticResource IconColor}"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                    <Rectangle x:Name="DisabledElement"
                                               Opacity="0"
                                               RadiusX="8"
                                               RadiusY="8"
                                               Fill="{StaticResource ContentBackgroundAlt1}" />
                                </Grid>
                            </ControlTemplate>
                            <ControlTemplate x:Key="VerticalThumbTemplate"
                                             TargetType="Thumb">
                                <Grid x:Name="ThumbVisual">
                                    <Rectangle x:Name="Background"
                                               Fill="{StaticResource ContentBackground}">
                                    </Rectangle>
                                    <ContentControl HorizontalAlignment="Center"
                                                    VerticalAlignment="Center">
                                        <Grid Height="15"
                                              Width="12">
                                            <TextBlock Text="&#xe7ef;"
                                                       FontSize="22"
                                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                                       Foreground="{StaticResource IconColor}"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0.795,0,0"/>
                                            <TextBlock Text="&#xe7ef;"
                                                       FontSize="22"
                                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                                       Foreground="{StaticResource IconColor}" 
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,0,0.794"/>
                                        </Grid>
                                    </ContentControl>
                                </Grid>
                            </ControlTemplate>
                            <ControlTemplate x:Key="HorizontalThumbTemplate"
                                             TargetType="Thumb">
                                <Grid x:Name="ThumbVisual">
                                    <Rectangle x:Name="Background"
                                               Fill="{StaticResource ContentBackgroundAlt3}">
                                    </Rectangle>
                                    <ContentControl HorizontalAlignment="Center"
                                                    VerticalAlignment="Center">
                                        <Grid Height="12"
                                              Width="15">
                                            <TextBlock Text="&#xe7f0;"
                                                       FontSize="22"
                                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons" 
                                                       Foreground="{StaticResource IconColor}"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,0.795,0"
                                                       />
                                            <TextBlock Text="&#xe7f0;"
                                                       FontSize="22"
                                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons" 
                                                       Foreground="{StaticResource BorderAlt4}"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"
                                                       Margin="0.794,0,0,0"
                                                       />
                                        </Grid>
                                    </ContentControl>
                                </Grid>
                            </ControlTemplate>
                            <ControlTemplate x:Key="HorizontalNearThumbTemplate"
                                             TargetType="Thumb">
                                <Border CornerRadius="8,0,0,8"
                                        Width="18"
                                        Background="{StaticResource ContentBackgroundAlt3}"
										BorderBrush="{StaticResource BorderAlt4}"
                                        Margin="0,0,0,0"
                                        BorderThickness="0,0,1,0">
                                    <Grid VerticalAlignment="Center"
                                          HorizontalAlignment="Center"
                                          Margin="3,0,1,0">
                                        <ContentControl>
                                            <Ellipse Width="8"
                                                     Height="8"
                                                     Fill="{StaticResource IconColor}">
                                            </Ellipse>
                                        </ContentControl>
                                    </Grid>
                                </Border>
                            </ControlTemplate>
                            <ControlTemplate x:Key="HorizontalFarThumbTemplate"
                                             TargetType="Thumb">
                                <Border CornerRadius="0,8,8,0"
                                        Width="18"
                                        Background="{StaticResource ContentBackgroundAlt3}"
										BorderBrush="{StaticResource BorderAlt4}"
                                        Margin="0,0,0,0"
                                        BorderThickness="1,0,0,0">
                                    <Grid VerticalAlignment="Center"
                                          HorizontalAlignment="Center"
                                          Margin="1,0,3,0">
                                        <ContentControl>
                                            <Ellipse Width="8"
                                                     Height="8"
                                                     Fill="{StaticResource IconColor}">
                                            </Ellipse>
                                        </ContentControl>
                                    </Grid>
                                </Border>
                            </ControlTemplate>
                            <ControlTemplate x:Key="VerticalNearThumbTemplate"
                                             TargetType="Thumb">
                                <Border CornerRadius="0,0,8,8"
                                        Margin="0,-1,0,0"
                                        Height="18"
                                        Background="#FF686868">
                                    <Grid VerticalAlignment="Center"
                                          HorizontalAlignment="Center">
                                        <ContentControl>
                                            <Ellipse Width="9"
                                                     Height="9"
                                                     Fill="#FFB7B7B7">
                                            </Ellipse>
                                        </ContentControl>
                                    </Grid>
                                </Border>
                            </ControlTemplate>
                            <ControlTemplate x:Key="VerticalFarThumbTemplate"
                                             TargetType="Thumb">
                                <Border CornerRadius="8,8,0,0"
                                        Margin="0,0,0,-1"
                                        Height="18"
                                        Background="#FF686868">
                                    <Grid VerticalAlignment="Center"
                                          HorizontalAlignment="Center">
                                        <ContentControl>
                                            <Ellipse Width="9"
                                                     Height="9"
                                                     Fill="{StaticResource IconColor}">
                                            </Ellipse>
                                        </ContentControl>
                                    </Grid>
                                </Border>
                            </ControlTemplate>
                        </Grid.Resources>

                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="MouseOver" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="Root"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.5"
                                                         Duration="0" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid x:Name="HorizontalRoot"
                              Visibility="Collapsed">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Rectangle Grid.ColumnSpan="7"
                                       RadiusX="{StaticResource Windows11Dark.StrokeThickness}"
                                       RadiusY="{StaticResource Windows11Dark.StrokeThickness}"
                                       StrokeThickness="1"
                                       Stroke="Transparent"
                                       Fill="{StaticResource ContentBackground}"></Rectangle>
                            <Rectangle Grid.ColumnSpan="7"
                                       RadiusX="{StaticResource Windows11Dark.StrokeThickness}"
                                       RadiusY="{StaticResource Windows11Dark.StrokeThickness}"
                                       StrokeThickness="1"
                                       Stroke="Transparent"
                                       Fill="{TemplateBinding Background}" />
                            <Rectangle Grid.ColumnSpan="7"
                                       RadiusX="{StaticResource Windows11Dark.StrokeThickness}"
                                       RadiusY="{StaticResource Windows11Dark.StrokeThickness}"
                                       StrokeThickness="1"
                                       Stroke="Transparent"
                                       Fill="{StaticResource ContentBackground}"></Rectangle>
                            <Rectangle x:Name="ScrollRect"
                                       Grid.ColumnSpan="7"
                                       RadiusX="{StaticResource Windows11Dark.StrokeThickness}"
                                       RadiusY="{StaticResource Windows11Dark.StrokeThickness}"
                                       Margin="1"
                                       Stroke="{StaticResource BorderAlt}">
                            </Rectangle>
                            <RepeatButton x:Name="HorizontalSmallDecrease"
                                          Visibility="{TemplateBinding Property=ScrollButtonVisibility}"
                                          Grid.Column="0"
                                          MinWidth="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Template="{StaticResource HorizontalDecrementTemplate}"
                                          Margin="1" />
                            <RepeatButton x:Name="HorizontalLargeDecrease"
                                          Grid.Column="1"
                                          Width="0"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          Interval="50"
                                          IsTabStop="False" />
                            <Thumb x:Name="HorizontalThumbHand1"
                                   Cursor="ScrollWE"
                                   Background="{TemplateBinding Background}"
                                   MinWidth="10"
                                   Grid.Column="2"
                                   Template="{StaticResource HorizontalNearThumbTemplate}" />
                            <Thumb x:Name="HorizontalThumb"
                                   Cursor="Hand"
                                   Background="{TemplateBinding Background}"
                                   MinWidth="0"
                                   Width="18"
                                   Grid.Column="3"
                                   Template="{StaticResource HorizontalThumbTemplate}" />
                            <Thumb x:Name="HorizontalThumbHand2"
                                   Cursor="ScrollWE"
                                   Background="{TemplateBinding Background}"
                                   MinWidth="10"
                                   Grid.Column="4"
                                   Template="{StaticResource HorizontalFarThumbTemplate}" />
                            <RepeatButton x:Name="HorizontalLargeIncrease"
                                          Grid.Column="5"
                                          Interval="50"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          IsTabStop="False" />
                            <RepeatButton x:Name="HorizontalSmallIncrease"
                                          Visibility="{TemplateBinding Property=ScrollButtonVisibility}"
                                          Grid.Column="6"
                                          MinWidth="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Template="{StaticResource HorizontalIncrementTemplate}"
                                          Margin="1" />
                        </Grid>
                        
                        <Grid x:Name="VerticalRoot">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <Rectangle Grid.RowSpan="7"
                                       RadiusX="8"
                                       RadiusY="8"
                                       StrokeThickness="1"
                                       Stroke="#00000000"
                                       Fill="{StaticResource ContentBackground}"></Rectangle>
                            <Rectangle Grid.RowSpan="7"
                                       RadiusX="8"
                                       RadiusY="8"
                                       StrokeThickness="1"
                                       Fill="{StaticResource ContentBackground}"></Rectangle>
                            <Rectangle Grid.RowSpan="7"
                                       RadiusX="8"
                                       RadiusY="8"
                                       Margin="1"
                                       Stroke="{StaticResource ContentBackground}"></Rectangle>

                            <RepeatButton x:Name="VerticalSmallDecrease"
                                          Visibility="{TemplateBinding Property=ScrollButtonVisibility}"
                                          Grid.Row="6"
                                          MinHeight="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Template="{StaticResource VerticalDecrementTemplate}"
                                          Margin="1"
                                          RenderTransformOrigin="0.5,0.5">
                                <RepeatButton.RenderTransform>
                                    <RotateTransform Angle="180"></RotateTransform>
                                </RepeatButton.RenderTransform>
                            </RepeatButton>
                            <RepeatButton x:Name="VerticalLargeDecrease"
                                          Grid.Row="5"
                                          Height="0"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          Interval="50"
                                          IsTabStop="False" />
                            <Thumb x:Name="VerticalThumbHand1"
                                   MinHeight="18"
                                   Grid.Row="4"
                                   Cursor="ScrollNS"
                                   Template="{StaticResource VerticalNearThumbTemplate}" />
                            <Thumb x:Name="VerticalThumb"
                                   Cursor="Hand"
                                   MinHeight="0"
                                   Height="18"
                                   Grid.Row="3"
                                   Template="{StaticResource VerticalThumbTemplate}" />
                            <Thumb x:Name="VerticalThumbHand2"
                                   MinHeight="18"
                                   Grid.Row="2"
                                   Cursor="ScrollNS"
                                   Template="{StaticResource VerticalFarThumbTemplate}" />
                            <RepeatButton x:Name="VerticalLargeIncrease"
                                          Grid.Row="1"
                                          Template="{StaticResource RepeatButtonTemplate}"
                                          Interval="50"
                                          IsTabStop="False" />
                            <RepeatButton x:Name="VerticalSmallIncrease"
                                          Grid.Row="0"
                                          Visibility="{TemplateBinding Property=ScrollButtonVisibility}"
                                          MinHeight="16"
                                          IsTabStop="False"
                                          Interval="50"
                                          Template="{StaticResource VerticalIncrementTemplate}"
                                          Margin="1"
                                          RenderTransformOrigin="0.5,0.5">
                                <RepeatButton.RenderTransform>
                                    <RotateTransform Angle="180"></RotateTransform>
                                </RepeatButton.RenderTransform>
                            </RepeatButton>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="local:ResizableScrollBar" BasedOn="{StaticResource SyncfusionResizableScrollBarNavigatorStyle}"></Style>
    
    <Style x:Key="SyncfusionSfDateTimeRangeNavigatorStyle" TargetType="local:SfDateTimeRangeNavigator">
        <Setter Property="LeftToolTipTemplate"
                Value="{StaticResource leftTooltipTemplate}"></Setter>
        <Setter Property="RightToolTipTemplate"
                Value="{StaticResource rightTooltipTemplate}"></Setter>
        <Setter Property="OverlayBrush"
                Value="{StaticResource OverlayRangeNavigator}" />
        <Setter Property="FontSize"
                Value="12" />
        <Setter Property="LowerLabelStyle"
                Value="{StaticResource labelStyle}" />
        <Setter Property="HigherLabelStyle"
                Value="{StaticResource labelStyle}" />
        <Setter Property="LowerLevelBarStyle">
            <Setter.Value>
                <local:LabelBarStyle Background="{StaticResource ContentBackgroundAlt2}"
                                     LabelHorizontalAlignment="Center"
                                     SelectedLabelBrush="{StaticResource ContentForeground}" />
            </Setter.Value>
        </Setter>
        <Setter Property="HigherLevelBarStyle">
            <Setter.Value>
                <local:LabelBarStyle Background="{StaticResource ContentBackgroundAlt2}"
                                     LabelHorizontalAlignment="Center"
                                     SelectedLabelBrush="{StaticResource ContentForeground}" />
            </Setter.Value>
        </Setter>
        <Setter Property="LowerBarGridLineStyle"
                Value="{StaticResource SyncfusionRangeNavigatorLineStyle}"></Setter>
        <Setter Property="HigherBarTickLineStyle"
                Value="{StaticResource tickLineStyle}"></Setter>
        <Setter Property="LowerBarTickLineStyle"
                Value="{StaticResource tickLineStyle}"></Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:SfDateTimeRangeNavigator">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            KeyboardNavigation.TabNavigation="None">
                        <Grid x:Name="PART_RangeNavigatorPanel"
                              MinHeight="100">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Border  VerticalAlignment="Top"
                                     x:Name="Part_UpperBorder"
                                     Grid.Row="0"
                                     Background="#dddddd"
                                     Padding="0,1,0,3">
                                <Grid>
                                    <Canvas x:Name="PART_UPPERLINE" />
                                    <local:ResizeCanvas x:Name="PART_UPPERBAR"
                                                        Background="Transparent">
                                        <local:ResizeCanvas.Resources>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Foreground"
                                                        Value="#848484" />
                                            </Style>
                                        </local:ResizeCanvas.Resources>
                                    </local:ResizeCanvas>
                                </Grid>
                            </Border>

                            <local:RangeNavigatorSelector x:Name="Part_RangePicker"
                                                          Orientation="Horizontal"
                                                          Grid.Row="1"
                                                          Template="{StaticResource SyncfusionRangeNavigatorSelectorControlTemplate}"
                                                          OverlayBrush="{TemplateBinding OverlayBrush}"
                                                          Margin="0,0,0,0">
                                <Grid>
                                    <ContentControl Content="{TemplateBinding Content}"></ContentControl>
                                    <Canvas x:Name="Part_Content_line" />
                                    <Canvas x:Name="Part_Hover"
                                            IsHitTestVisible="False">
                                        <Rectangle></Rectangle>
                                    </Canvas>
                                </Grid>
                            </local:RangeNavigatorSelector>
                            <Canvas x:Name="Part_Tooltip"
                                    Grid.Row="1"
                                    IsHitTestVisible="False">
                                <ContentControl></ContentControl>
                                <ContentControl></ContentControl>
                            </Canvas>
                            <Border  x:Name="Part_Border"
                                     Grid.Row="2"
                                     Padding="0,1,0,3">
                                <Grid>
                                    <Canvas x:Name="PART_LOWERLINE" />
                                    <local:ResizeCanvas x:Name="PART_LOWERBAR"
                                                        Background="Transparent">
                                        <local:ResizeCanvas.Resources>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Foreground"
                                                        Value="#848484" />
                                            </Style>
                                        </local:ResizeCanvas.Resources>
                                    </local:ResizeCanvas>
                                </Grid>
                            </Border>
                            <local:ResizableScrollBar x:Name="Part_Scroll"
                                                      Grid.Row="3"                                                    
                                                      Visibility="{TemplateBinding ScrollbarVisibility}"
                                                      Orientation="Horizontal" />
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfDateTimeRangeNavigatorStyle}" TargetType="local:SfDateTimeRangeNavigator"></Style>

</ResourceDictionary>
