<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                      
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" 
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="TextBox.Static.Border" Color="#FF707070"/>

    <SolidColorBrush x:Key="TextBox.MouseOver.Border" Color="#179bd7"/>

    <SolidColorBrush x:Key="TextBox.Focused.Border" Color="#686969"/>

    <LinearGradientBrush x:Key="TextBoxBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="TextBoxBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="TextBoxBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="WPFTextBoxStyle"
           TargetType="{x:Type TextBox}">
        <Setter Property="FocusVisualStyle"
                Value="{x:Null}"/>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
        <Setter Property="Background"
                Value="{StaticResource ContentBackgroundAlt4}"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource TextBoxBorderBrush}"/>
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant1}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="SelectionBrush"
                Value="{StaticResource PrimaryBackground}"/>
        <Setter Property="AllowDrop"
                Value="true"/>
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="AllowDrop" Value="true"/>
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">
                    <Grid>
                        <Border x:Name="border"
                            Padding="10 0 0 0" 
                            BorderBrush="{TemplateBinding BorderBrush}" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            Background="{TemplateBinding Background}"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            SnapsToDevicePixels="true">
                        <ScrollViewer x:Name="PART_ContentHost" 
                                      Focusable="False" Background="Transparent"
                                      HorizontalScrollBarVisibility="Hidden"
                                      VerticalScrollBarVisibility="Hidden"/>
                    </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource TextBoxBorderBrushHovered}"/>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundAlt5}"/>
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource TextBoxBorderBrushFocused}"/>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}"/>
                            <Setter Property="Padding" Value="0,0,0,-1"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundAlt6}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource WPFTextBoxStyle}" TargetType="{x:Type TextBox}"/>

    <Style x:Key="WPFBorderlessTextBoxStyle" TargetType="{x:Type TextBox}">
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="AllowDrop" Value="true"/>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}"/>
        <Setter Property="SelectionBrush" Value="{StaticResource PrimaryBackground}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">
                    <ScrollViewer x:Name="PART_ContentHost" 
                                  Background="Transparent" 
					              BorderBrush="Transparent" 
                                  Focusable="false" 
                                  HorizontalScrollBarVisibility="Hidden" 
                                  VerticalScrollBarVisibility="Hidden"/>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsReadOnly" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFRichTextBoxStyle"
           TargetType="{x:Type TextBox}">
        <Setter Property="FocusVisualStyle"
                Value="{x:Null}"/>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
        <Setter Property="Background"
                Value="{StaticResource ContentBackgroundAlt4}"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource TextBoxBorderBrush}"/>
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant1}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="SelectionBrush"
                Value="{StaticResource PrimaryBackground}"/>
        <Setter Property="AllowDrop"
                Value="true"/>
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="AllowDrop" Value="true"/>
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">
                    <!--<Grid>
                        <Border x:Name="border"
                            Padding="10 0 0 0" 
                            BorderBrush="{TemplateBinding BorderBrush}" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            Background="{TemplateBinding Background}"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            SnapsToDevicePixels="true">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                      Focusable="False" Background="Transparent"
                                      HorizontalScrollBarVisibility="Hidden"
                                      VerticalScrollBarVisibility="Hidden"/>
                        </Border>
                    </Grid>-->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Border x:Name="border"
                                Grid.RowSpan="2"
                                Grid.ColumnSpan="2"
     Padding="10 0 0 0" 
     BorderBrush="{TemplateBinding BorderBrush}" 
     BorderThickness="{TemplateBinding BorderThickness}" 
     Background="{TemplateBinding Background}"
     CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
     SnapsToDevicePixels="true">
     <ScrollViewer x:Name="PART_ContentHost" 
               Focusable="False" Background="Transparent"
               HorizontalScrollBarVisibility="Hidden"
               VerticalScrollBarVisibility="Hidden"/>
 </Border>
                        <TextBlock
          x:Name="PlaceholderTextContentPresenter" 
          Foreground="Black" 
          Margin="{TemplateBinding BorderThickness}"
          Padding="{TemplateBinding Padding}"                           
          TextAlignment="{TemplateBinding TextAlignment}" 
          TextWrapping="{TemplateBinding TextWrapping}" 
          IsHitTestVisible="False" />
                        <Button 
           x:Name="send"
           Height="22"                             
           Width="30" Style="{StaticResource WPFFlatButtonStyle}"
           BorderThickness="1" BorderBrush="Transparent"
           HorizontalAlignment="Right" Padding="4" Margin="8,5,8,5"
           Grid.Column="1">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <Viewbox Stretch="Uniform">
                                        <TextBlock Text="&#xE724;" FontFamily="Segoe MDL2 Assets" FontSize="28" />

                                    </Viewbox>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource TextBoxBorderBrushHovered}"/>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundAlt5}"/>
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource TextBoxBorderBrushFocused}"/>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}"/>
                            <Setter Property="Padding" Value="0,0,0,-1"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundAlt6}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
   
</ResourceDictionary>
