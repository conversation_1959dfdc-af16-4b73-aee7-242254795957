# WPF功能文档 - License注册机

## 文档信息
- **模块名称**: License注册机
- **文档版本**: 1.0
- **创建日期**: 2024-12-09
- **最后更新**: 2024-12-09
- **负责人**: AI Assistant

## 1. 模块概述

### 1.1 功能描述
License注册机是商用空调监控调试软件的配套工具，专门用于生成、验证和管理4种类型的许可证文件。该工具为公司内部使用，确保主软件的授权管理和功能控制。注册机采用WPF技术开发，提供直观的图形界面，支持批量生成和管理许可证。

### 1.2 核心特性
- **4种许可证类型**: 支持普通版、售后版、研发版、管理版许可证生成
- **硬件绑定**: 基于CPU和主板序列号进行硬件绑定
- **安全加密**: 采用RSA+AES混合加密确保许可证安全性
- **批量生成**: 支持批量生成多个许可证文件
- **验证功能**: 提供许可证验证和信息查看功能
- **模板管理**: 支持许可证模板保存和复用
- **统一UI风格**: 与主程序保持完全一致的界面风格和用户体验
- **共享代码库**: 基于AirMonitor.Core公共类库，确保代码复用和一致性

### 1.3 技术架构
```
┌─────────────────────────────────────────────────────────────┐
│                  License注册机架构                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Generator   │  │ Validator   │  │ Template    │         │
│  │ View        │  │ View        │  │ Manager     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ License     │  │ Crypto      │  │ Hardware    │         │
│  │ Generator   │  │ Service     │  │ Service     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 2. 许可证类型定义

### 2.1 许可证类型详细说明

| 类型 | 代码 | 名称 | 功能权限 | 有效期 | 适用场景 |
|------|------|------|----------|--------|----------|
| Standard | STD | 普通版 | 基础监控功能 | 1年 | 一般用户使用 |
| AfterSales | AS | 售后版 | 售后服务专用功能 | 1年 | 售后服务部门 |
| Development | DEV | 研发版 | 所有功能 | 永久 | 研发部门专用 |
| Management | MGT | 管理版 | 所有功能+管理权限 | 永久 | 管理层专用 |

### 2.2 功能权限矩阵

| 功能模块 | 普通版 | 售后版 | 研发版 | 管理版 |
|---------|--------|--------|--------|--------|
| 串口通信 | ✓ | ✓ | ✓ | ✓ |
| 数据采集 | ✓ | ✓ | ✓ | ✓ |
| 实时监控 | ✓ | ✓ | ✓ | ✓ |
| 历史数据查询 | ✓ | ✓ | ✓ | ✓ |
| 数据导出 | ✗ | ✓ | ✓ | ✓ |
| 高级分析 | ✗ | ✓ | ✓ | ✓ |
| 数据回放 | ✗ | ✓ | ✓ | ✓ |
| 报警管理 | ✓ | ✓ | ✓ | ✓ |
| 自定义协议 | ✗ | ✗ | ✓ | ✓ |
| 系统配置 | ✗ | ✗ | ✓ | ✓ |
| 用户管理 | ✗ | ✗ | ✗ | ✓ |
| 许可证管理 | ✗ | ✗ | ✗ | ✓ |

## 3. 用户界面设计

### 3.1 UI风格统一要求
注册机项目必须与主程序保持完全一致的界面风格：

#### 3.1.1 技术规范
- **UI框架**: 使用相同的Syncfusion WPF Controls 29.2.9
- **主题系统**: 统一使用Windows11Dark/Light主题
- **字体规范**: 统一使用微软雅黑字体，12-16px字号
- **颜色方案**: 与主程序保持一致的颜色配置
- **控件样式**: 复用主程序的控件样式定义

#### 3.1.2 共享资源
- **样式文件**: 共享主程序的XAML样式资源
- **图标资源**: 使用统一的图标库和设计风格
- **布局规范**: 遵循相同的间距、对齐和布局原则
- **交互模式**: 保持一致的用户交互体验

#### 3.1.3 代码复用
- **Core类库**: 基于AirMonitor.Core公共类库
- **工具类**: 复用Core中的工具类和扩展方法
- **验证逻辑**: 共享数据验证和错误处理机制
- **配置管理**: 使用统一的配置管理方式

### 3.2 主界面布局
- **菜单栏**: 文件、工具、帮助菜单（使用Syncfusion菜单控件）
- **工具栏**: 常用功能快捷按钮（使用Syncfusion工具栏控件）
- **主工作区**: 选项卡式界面，包含生成器、验证器、模板管理（使用Syncfusion TabControl）
- **状态栏**: 显示当前操作状态和进度（使用Syncfusion状态栏控件）

### 3.2 许可证生成界面
#### 3.2.1 基本信息区域
- **许可证ID**: 自动生成或手动输入
- **产品名称**: 固定为"AirMonitor"
- **产品版本**: 下拉选择当前版本
- **许可证类型**: 单选按钮选择4种类型
- **部门名称**: 文本输入框
- **部门邮箱**: 文本输入框

#### 3.2.2 授权配置区域
- **功能权限**: 复选框列表，根据许可证类型自动勾选
- **有效期设置**: 日期选择器，支持永久和定期
- **硬件绑定**: 
  - 手动输入硬件指纹
  - 从目标机器获取硬件信息
  - 硬件指纹验证

#### 3.2.3 操作按钮区域
- **生成许可证**: 生成单个许可证文件
- **批量生成**: 从Excel模板批量生成
- **保存模板**: 保存当前配置为模板
- **加载模板**: 加载已保存的模板
- **预览**: 预览许可证内容

### 3.3 许可证验证界面
#### 3.3.1 文件选择区域
- **选择文件**: 浏览按钮选择许可证文件
- **拖拽支持**: 支持拖拽许可证文件到界面

#### 3.3.2 验证结果显示
- **验证状态**: 显示验证成功/失败状态
- **许可证信息**: 表格显示许可证详细信息
- **错误信息**: 显示验证失败的具体原因
- **功能权限**: 列表显示授权的功能模块

### 3.4 模板管理界面
#### 3.4.1 模板列表
- **模板名称**: 显示已保存的模板名称
- **创建时间**: 显示模板创建时间
- **许可证类型**: 显示模板对应的许可证类型
- **描述**: 显示模板描述信息

#### 3.4.2 模板操作
- **新建模板**: 创建新的许可证模板
- **编辑模板**: 编辑选中的模板
- **删除模板**: 删除选中的模板
- **导入模板**: 从文件导入模板
- **导出模板**: 导出模板到文件

## 4. 核心功能实现

### 4.1 许可证生成流程
1. **信息收集**: 收集部门信息、许可证类型、硬件指纹等
2. **权限配置**: 根据许可证类型自动配置功能权限
3. **数据验证**: 验证输入数据的完整性和有效性
4. **加密处理**: 使用RSA+AES混合加密生成许可证
5. **文件生成**: 生成加密的license文件
6. **数字签名**: 对许可证文件进行数字签名

### 4.2 硬件指纹获取
```csharp
public class HardwareFingerprintService
{
    public string GetLocalFingerprint()
    {
        var cpuId = GetCpuId();
        var motherboardId = GetMotherboardId();
        return GenerateFingerprint(cpuId, motherboardId);
    }

    public string GetRemoteFingerprint(string targetMachine)
    {
        // 通过网络获取远程机器硬件信息
        // 或提供工具让用户在目标机器上获取
    }
}
```

### 4.3 批量生成功能
- **Excel模板**: 提供标准的Excel模板文件
- **数据导入**: 从Excel文件读取批量生成数据
- **进度显示**: 显示批量生成进度和状态
- **错误处理**: 记录和显示生成过程中的错误
- **结果导出**: 生成批量操作结果报告

### 4.4 许可证验证功能
```csharp
public class LicenseValidator
{
    public ValidationResult ValidateLicense(string licenseFilePath)
    {
        try
        {
            // 1. 读取许可证文件
            var encryptedContent = File.ReadAllText(licenseFilePath);
            
            // 2. 解密许可证内容
            var licenseJson = _cryptoService.Decrypt(encryptedContent);
            
            // 3. 反序列化许可证对象
            var license = JsonSerializer.Deserialize<LicenseInfo>(licenseJson);
            
            // 4. 验证数字签名
            if (!_cryptoService.VerifySignature(license))
                return ValidationResult.Failure("数字签名验证失败");
            
            // 5. 验证有效期
            if (!license.IsValid())
                return ValidationResult.Failure("许可证已过期");
            
            return ValidationResult.Success(license);
        }
        catch (Exception ex)
        {
            return ValidationResult.Failure($"验证失败: {ex.Message}");
        }
    }
}
```

## 5. 安全机制

### 5.1 加密算法
- **RSA-2048**: 用于加密AES密钥和数字签名
- **AES-256**: 用于加密许可证内容
- **SHA-256**: 用于生成数字签名和硬件指纹

### 5.2 密钥管理
- **私钥保护**: RSA私钥存储在安全位置，使用密码保护
- **公钥分发**: RSA公钥内嵌在主软件中
- **密钥轮换**: 支持定期更换加密密钥

### 5.3 安全措施
- **访问控制**: 注册机需要管理员权限运行
- **操作日志**: 记录所有许可证生成和验证操作
- **文件保护**: 生成的许可证文件具有防篡改机制

## 6. 配置和部署

### 6.1 配置文件
```json
{
  "CryptoSettings": {
    "RSAKeySize": 2048,
    "AESKeySize": 256,
    "PrivateKeyPath": "keys/private.key",
    "PublicKeyPath": "keys/public.key"
  },
  "LicenseSettings": {
    "DefaultValidityDays": 365,
    "MaxDeviceCount": -1,
    "ProductName": "AirMonitor",
    "ProductVersion": "1.0.0"
  },
  "TemplateSettings": {
    "TemplatePath": "templates/",
    "ExcelTemplatePath": "templates/batch_template.xlsx"
  }
}
```

### 6.2 部署要求
- **操作系统**: Windows 10/11 (x64)
- **.NET运行时**: .NET 8.0 Desktop Runtime
- **权限要求**: 管理员权限
- **硬件要求**: 最小2GB内存，100MB磁盘空间

### 6.3 安装步骤
1. 解压注册机安装包到指定目录
2. 配置RSA密钥对（首次安装）
3. 设置配置文件参数
4. 创建桌面快捷方式
5. 验证安装是否成功

## 7. 使用指南

### 7.1 生成单个许可证
1. 启动License注册机
2. 切换到"许可证生成"选项卡
3. 填写部门信息和许可证类型
4. 获取或输入目标机器硬件指纹
5. 设置有效期和功能权限
6. 点击"生成许可证"按钮
7. 选择保存位置并保存文件

### 7.2 批量生成许可证
1. 下载Excel模板文件
2. 填写批量生成数据
3. 在注册机中选择"批量生成"
4. 导入填写好的Excel文件
5. 检查数据预览
6. 开始批量生成
7. 查看生成结果报告

### 7.3 验证许可证
1. 切换到"许可证验证"选项卡
2. 选择要验证的许可证文件
3. 点击"验证"按钮
4. 查看验证结果和许可证信息

## 8. 故障排除

### 8.1 常见问题
- **私钥文件缺失**: 检查密钥文件路径和权限
- **硬件指纹获取失败**: 确认WMI服务正常运行
- **许可证生成失败**: 检查输入数据格式和完整性
- **验证失败**: 确认许可证文件未被篡改

### 8.2 错误代码
- **E001**: 私钥文件不存在或无法读取
- **E002**: 硬件指纹获取失败
- **E003**: 许可证数据验证失败
- **E004**: 加密操作失败
- **E005**: 文件写入权限不足

## 9. 版本历史

| 版本 | 日期 | 变更内容 | 负责人 |
|------|------|----------|--------|
| 1.0 | 2024-12-09 | 初始版本，支持4种许可证类型生成和验证 | AI Assistant |
