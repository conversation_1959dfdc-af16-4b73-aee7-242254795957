﻿<Window x:Class="AirMonitor.LicenseGenerator.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AirMonitor.LicenseGenerator"
        xmlns:views="clr-namespace:AirMonitor.LicenseGenerator.Views"
        mc:Ignorable="d"
        Title="{Binding ApplicationTitle}"
        Height="800" Width="1200"
        MinHeight="600" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Icon="Resources/Images/license-generator.ico">

    <Window.Resources>
        <!-- 数据模板 -->
        <DataTemplate x:Key="LicenseGeneratorTemplate">
            <views:LicenseGeneratorView/>
        </DataTemplate>

        <DataTemplate x:Key="LicenseValidatorTemplate">
            <views:LicenseValidatorView/>
        </DataTemplate>

        <DataTemplate x:Key="TemplateManagerTemplate">
            <views:TemplateManagerView/>
        </DataTemplate>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="{StaticResource SurfaceBrush}">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="新建(_N)" Command="{Binding NewCommand}" InputGestureText="Ctrl+N">
                    <MenuItem.Icon>
                        <TextBlock Text="📄" FontSize="14"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="打开(_O)" Command="{Binding OpenCommand}" InputGestureText="Ctrl+O">
                    <MenuItem.Icon>
                        <TextBlock Text="📂" FontSize="14"/>
                    </MenuItem.Icon>
                </MenuItem>
                <Separator/>
                <MenuItem Header="保存(_S)" Command="{Binding SaveCommand}" InputGestureText="Ctrl+S">
                    <MenuItem.Icon>
                        <TextBlock Text="💾" FontSize="14"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="另存为(_A)" Command="{Binding SaveAsCommand}" InputGestureText="Ctrl+Shift+S">
                    <MenuItem.Icon>
                        <TextBlock Text="💾" FontSize="14"/>
                    </MenuItem.Icon>
                </MenuItem>
                <Separator/>
                <MenuItem Header="退出(_X)" Command="{Binding ExitCommand}" InputGestureText="Alt+F4">
                    <MenuItem.Icon>
                        <TextBlock Text="❌" FontSize="14"/>
                    </MenuItem.Icon>
                </MenuItem>
            </MenuItem>

            <MenuItem Header="工具(_T)">
                <MenuItem Header="设置(_S)" Command="{Binding SettingsCommand}">
                    <MenuItem.Icon>
                        <TextBlock Text="⚙️" FontSize="14"/>
                    </MenuItem.Icon>
                </MenuItem>
            </MenuItem>

            <MenuItem Header="帮助(_H)">
                <MenuItem Header="帮助文档(_H)" Command="{Binding HelpCommand}" InputGestureText="F1">
                    <MenuItem.Icon>
                        <TextBlock Text="❓" FontSize="14"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="关于(_A)" Command="{Binding AboutCommand}">
                    <MenuItem.Icon>
                        <TextBlock Text="ℹ️" FontSize="14"/>
                    </MenuItem.Icon>
                </MenuItem>
            </MenuItem>
        </Menu>

        <!-- 主内容区域 -->
        <TabControl Grid.Row="1"
                    SelectedIndex="{Binding SelectedTabIndex}"
                    Background="White"
                    BorderThickness="0">

            <!-- 许可证生成选项卡 -->
            <TabItem Header="🔑 许可证生成" ToolTip="生成新的许可证文件">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal" Margin="8,4">
                            <TextBlock Text="🔑" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="许可证生成" FontSize="12" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                <ContentPresenter ContentTemplate="{StaticResource LicenseGeneratorTemplate}"
                                  Content="{Binding LicenseGeneratorViewModel}"/>
            </TabItem>

            <!-- 许可证验证选项卡 -->
            <TabItem Header="✅ 许可证验证" ToolTip="验证现有的许可证文件">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal" Margin="8,4">
                            <TextBlock Text="✅" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="许可证验证" FontSize="12" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                <ContentPresenter ContentTemplate="{StaticResource LicenseValidatorTemplate}"
                                  Content="{Binding LicenseValidatorViewModel}"/>
            </TabItem>

            <!-- 模板管理选项卡 -->
            <TabItem Header="📋 模板管理" ToolTip="管理许可证模板">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal" Margin="8,4">
                            <TextBlock Text="📋" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="模板管理" FontSize="12" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                <ContentPresenter ContentTemplate="{StaticResource TemplateManagerTemplate}"
                                  Content="{Binding TemplateManagerViewModel}"/>
            </TabItem>
        </TabControl>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Style="{StaticResource ModernStatusBarStyle}">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="状态: " FontWeight="Medium"/>
                    <TextBlock Text="{Binding StatusMessage}" Margin="4,0,0,0"/>
                </StackPanel>
            </StatusBarItem>

            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="AirMonitor License Generator v1.0.0"
                               Foreground="{StaticResource TextSecondaryBrush}"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
