<ResourceDictionary  
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:System="clr-namespace:System;assembly=mscorlib">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/SfChart/ChartAxis.xaml" />
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>
    
    <Style x:Key="SyncfusionSfChartStyle" TargetType="local:SfChart" >
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Light.ThemeFontFamily}"></Setter>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"></Setter>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"></Setter>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Light.FontWeightNormal}"></Setter>
        <Setter Property="AreaBackground"
                Value="Transparent"></Setter>
        <Setter Property="AreaBorderBrush"
                Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="Background"
                Value="{StaticResource ContentBackground}"></Setter>
        <Setter Property="ChartResourceDictionary">
            <Setter.Value>
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/SfChart/SfChartCommon.xaml" />
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="local:SfChart" BasedOn="{StaticResource SyncfusionSfChartStyle}"></Style>
    
</ResourceDictionary>
