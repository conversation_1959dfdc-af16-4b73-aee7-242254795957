<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Microsoft_Windows_Luna="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Luna"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:tools_controls="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
    xmlns:tools_resources="clr-namespace:Syncfusion.Windows.Tools.Controls.Resources;assembly=Syncfusion.Tools.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ListBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Menu.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/TabControlExt/TabControlExt.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <tools_controls:BoolToScrollBarVisibilityConverter x:Key="BoolToScrollBarVisibility" />
    <tools_controls:DocumentHeaderTrimmingTemplate x:Key="DocumentHeaderTrimmingTemplate" />
    <tools_controls:SizeToDoubleConverter x:Key="SizeToDoubleConverter" />
    <tools_controls:ParentToBoolConverter x:Key="ParentToBool" />
    <tools_controls:TestConverter x:Key="TestConverter" />
    <BooleanToVisibilityConverter x:Key="BooleanToVisibility" />

    <!--  PreviewGenericVS2005  -->
    <Style x:Key="ListBoxItemStyle1" TargetType="{x:Type ListBoxItem}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="MinWidth" Value="30" />
        <Setter Property="MinHeight" Value="25" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListBoxItem}">
                    <Border
                        Name="WrappBorder"
                        Margin="2"
                        Padding="2"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{Binding Path=ItemBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}"
                        BorderThickness="{Binding Path=ItemBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}"
                        CornerRadius="{Binding Path=ItemCornerRadius, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}"
                        SnapsToDevicePixels="True">
                        <StackPanel Orientation="Horizontal">
                            <Border
                                Name="IconBorder"
                                Width="16"
                                Margin="1"
                                Background="{Binding Path=(tools_controls:DocumentContainer.Icon)}" />
                            <ContentPresenter
                                Name="content"
                                Content="{Binding Path=(tools_controls:DocumentContainer.Header)}"
                                ContentTemplate="{Binding Path=(FrameworkElement.DataContext).(tools_controls:DocumentContainer.HeaderTemplate), ElementName=WrappBorder}" />
                        </StackPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="WrappBorder" Property="BorderBrush" Value="{Binding Path=SelectedItemBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}" />
                            <Setter TargetName="WrappBorder" Property="BorderThickness" Value="{Binding Path=SelectedItemBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}" />
                            <Setter TargetName="WrappBorder" Property="Background" Value="{Binding Path=SelectedItemBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="WrappBorder" Property="BorderBrush" Value="{Binding Path=SelectedItemBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}" />
                            <Setter TargetName="WrappBorder" Property="BorderThickness" Value="{Binding Path=SelectedItemBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}" />
                            <Setter TargetName="WrappBorder" Property="Background" Value="{Binding Path=SelectedItemBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="ListBoxTemplate1" TargetType="{x:Type ListBox}">
        <ScrollViewer
            x:Name="PART_ScrollViewer"
            CanContentScroll="True"
            Focusable="False">
            <ItemsPresenter x:Name="PART_ItemsPresenter" />
        </ScrollViewer>
    </ControlTemplate>

    <Style x:Key="{x:Type tools_controls:VS2005SwitchPreviewControl}" TargetType="{x:Type tools_controls:VS2005SwitchPreviewControl}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="CornerRadius" Value="0" />
        <Setter Property="SelectedItemBorderThickness" Value="1" />
        <Setter Property="ItemBorderThickness" Value="1" />
        <Setter Property="SelectedItemBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="ItemBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="SelectedItemBackground" Value="{StaticResource ContentBackgroundSelected}" />
        <Setter Property="ItemCornerRadius" Value="0" />
        <Setter Property="ItemWidth" Value="0" />
        <Setter Property="ItemHeight" Value="0" />
        <Setter Property="MinWidth" Value="400" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:VS2005SwitchPreviewControl}">
                    <Border
                        x:Name="RootBorder"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="1">
                        <Grid x:Name="RootGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition />
                                <RowDefinition Height="115" />
                            </Grid.RowDefinitions>
                            <Border
                                x:Name="WrapBorder"
                                Grid.Row="1"
                                Grid.ColumnSpan="2"
                                Margin="2"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="1">
                                <DockPanel x:Name="PART_DescriptionPanel">
                                    <shared:PreviewBorder
                                        Name="PART_PreviewBorder"
                                        Width="115"
                                        Margin="2"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="1"
                                        DockPanel.Dock="Right" />
                                    <Border
                                        x:Name="PART_DescriptionBorder"
                                        Margin="2"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="1">
                                        <ContentControl
                                            x:Name="PART_DescriptionContent"
                                            Margin="12"
                                            Content="{Binding Path=(tools_controls:VS2005SwitchPreviewControl.ParentContainer).(tools_controls:DocumentContainer.DocumentDescription), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                            ContentTemplate="{Binding Path=(tools_controls:VS2005SwitchPreviewControl.ParentContainer).(tools_controls:DocumentContainer.DocumentDescriptionTemplate), RelativeSource={RelativeSource Mode=TemplatedParent}}" />
                                    </Border>
                                </DockPanel>
                            </Border>
                            <DockPanel
                                x:Name="PART_ToolsWindowsPanel"
                                Grid.Column="0"
                                Margin="10,5,5,5"
                                DockPanel.Dock="Left"
                                LastChildFill="True">
                                <ContentControl
                                    x:Name="PART_ToolsWindowsHeader"
                                    Content="{Binding Path=(tools_controls:VS2005SwitchPreviewControl.ParentContainer).(tools_controls:DocumentContainer.ToolWindowsListHeader), RelativeSource={RelativeSource TemplatedParent}}"
                                    ContentTemplate="{Binding Path=(tools_controls:VS2005SwitchPreviewControl.ParentContainer).(tools_controls:DocumentContainer.ToolWindowsListHeaderTemplate), RelativeSource={RelativeSource TemplatedParent}}"
                                    DockPanel.Dock="Top" />
                                <ListBox
                                    Name="PART_ToolWindows"
                                    Background="Transparent"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    ItemContainerStyle="{StaticResource ListBoxItemStyle1}"
                                    Template="{StaticResource ListBoxTemplate1}" />
                            </DockPanel>
                            <DockPanel
                                x:Name="PART_DocumentWindowsPanel"
                                Grid.Column="1"
                                Margin="5"
                                DockPanel.Dock="Right"
                                LastChildFill="True">
                                <ContentControl
                                    x:Name="PART_DocumentWindowsHeader"
                                    Content="{Binding Path=(tools_controls:VS2005SwitchPreviewControl.ParentContainer).(tools_controls:DocumentContainer.DocumentListHeader), RelativeSource={RelativeSource TemplatedParent}}"
                                    ContentTemplate="{Binding Path=(tools_controls:VS2005SwitchPreviewControl.ParentContainer).(tools_controls:DocumentContainer.DocumentListHeaderTemplate), RelativeSource={RelativeSource TemplatedParent}}"
                                    DockPanel.Dock="Top" />
                                <ListBox
                                    Name="PART_ListBox"
                                    Background="Transparent"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    ItemContainerStyle="{StaticResource ListBoxItemStyle1}"
                                    Template="{StaticResource ListBoxTemplate1}" />
                            </DockPanel>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Popup}}, Converter={StaticResource ParentToBool}}" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Background" Value="{StaticResource ContentBackground}" />
                <Setter Property="SelectedItemBorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="SelectedItemBackground" Value="{StaticResource ContentBackgroundSelected}" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <!--  VistaFlipSwitchPreviewControl  -->
    <Style x:Key="{x:Type tools_controls:VistaFlipSwitchPreviewControl}" TargetType="{x:Type tools_controls:VistaFlipSwitchPreviewControl}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="CornerRadius" Value="0" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="ItemBorderThickness" Value="1" />
        <Setter Property="SelectedItemBorderThickness" Value="1" />
        <Setter Property="SelectedItemBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="ItemBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="SelectedItemBackground" Value="{StaticResource ContentBackgroundSelected}" />
        <Setter Property="ItemCornerRadius" Value="0" />
        <Setter Property="ItemWidth" Value="0" />
        <Setter Property="ItemHeight" Value="0" />
        <Setter Property="MinWidth" Value="550" />
    </Style>

    <!--  PreviewGenericQuickTab  -->

    <Style x:Key="PoorHeaderButtonStyle" TargetType="{x:Type ContentControl}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Width" Value="16" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ContentControl}">
                    <Border
                        x:Name="Border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="1">
                        <ContentPresenter
                            Name="content"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Top"
                            RecognizesAccessKey="True"
                            TextElement.Foreground="{TemplateBinding Foreground}" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

    </Style>
    <Style x:Key="{x:Type tools_controls:ItemHeader}" TargetType="{x:Type tools_controls:ItemHeader}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:ItemHeader}">
                    <DockPanel
                        Width="{TemplateBinding ActualWidth}"
                        Height="24"
                        Background="{TemplateBinding Background}">
                        <ContentControl
                            Name="PART_Button"
                            Width="16"
                            Height="16"
                            Margin="0,2,2,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Top"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            DockPanel.Dock="Right"
                            Foreground="{TemplateBinding Foreground}"
                            Style="{StaticResource PoorHeaderButtonStyle}"
                            Visibility="{Binding Path=CloseButtonVisible, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:QuickTabSwicthPreviewControl}}, Converter={StaticResource BooleanToVisibility}}">
                            <Path
                                        x:Name="closeButtonPath"
                                        Width="10"
                                        Height="10"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        StrokeThickness="1"
                                        SnapsToDevicePixels="False"
                                        Stretch="Fill">
                                <Path.Data>
                                    <PathGeometry>M0.5 9.5L9.5 0.5M9.5 9.5L0.5 0.5</PathGeometry>
                                </Path.Data>
                                <Path.Style>
                                    <Style TargetType="Path">
                                        <Setter Property="Stroke" Value="{StaticResource IconColor}"/>
                                    </Style>
                                </Path.Style>
                            </Path>
                        </ContentControl>
                        <TextBlock
                            DockPanel.Dock="Left"
                            Foreground="{TemplateBinding Foreground}"
                            Text="{Binding Path=(tools_controls:DocumentContainer.Header)}"
                            TextAlignment="Center"
                            TextTrimming="CharacterEllipsis" />
                    </DockPanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="{x:Type tools_controls:ItemWindow}" TargetType="{x:Type tools_controls:ItemWindow}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:ItemWindow}">
                    <Border
                        Name="WrappBorder"
                        Margin="2"
                        Padding="2"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{Binding Path=ItemBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:QuickTabSwicthPreviewControl}}}"
                        BorderThickness="{Binding Path=ItemBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:QuickTabSwicthPreviewControl}}}"
                        CornerRadius="{Binding Path=ItemCornerRadius, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:QuickTabSwicthPreviewControl}}}"
                        SnapsToDevicePixels="True">
                        <DockPanel LastChildFill="True">
                            <tools_controls:ItemHeader Name="PART_Header" DockPanel.Dock="Top" />
                            <Border
                                Name="bd"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="1">
                                <shared:PreviewBorder
                                    Name="PART_PreviewBorder"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="1" />
                            </Border>

                        </DockPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background" Value="{Binding Path=SelectedItemBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:QuickTabSwicthPreviewControl}}}" />
                            <Setter Property="BorderBrush" Value="{Binding Path=SelectedItemBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:QuickTabSwicthPreviewControl}}}" />
                            <Setter Property="BorderThickness" Value="{Binding Path=SelectedItemBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:QuickTabSwicthPreviewControl}}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="{x:Type tools_controls:QuickTabSwicthPreviewControl}" TargetType="{x:Type tools_controls:QuickTabSwicthPreviewControl}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="1.5" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="CornerRadius" Value="0" />
        <Setter Property="ItemBorderThickness" Value="1" />
        <Setter Property="SelectedItemBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="ItemBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="SelectedItemBackground" Value="{StaticResource ContentBackgroundSelected}" />
        <Setter Property="SelectedItemBorderThickness" Value="1" />
        <Setter Property="ItemCornerRadius" Value="0" />
        <Setter Property="ItemWidth" Value="0" />
        <Setter Property="ItemHeight" Value="0" />
        <Setter Property="MaxWidth" Value="550" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:QuickTabSwicthPreviewControl}">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="1">
                        <tools_controls:Gallery
                            AllowDrop="False"
                            AllowMultiSelect="False"
                            Focusable="False"
                            IsHeaderVisible="False"
                            ItemHeight="85"
                            ItemMaxHeight="85"
                            ItemMaxWidth="75"
                            ItemMinHeight="85"
                            ItemMinWidth="75"
                            ItemWidth="75"
                            PanelMargin="0"
                            PanelPadding="0">
                            <tools_controls:GalleryGroup Name="PART_GalleryGroup" Focusable="False" />
                        </tools_controls:Gallery>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Popup}}, Converter={StaticResource ParentToBool}}" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Background" Value="{StaticResource ContentBackground}" />
                <Setter Property="SelectedItemBorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="SelectedItemBackground" Value="{StaticResource ContentBackgroundSelected}" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <DataTemplate x:Key="DocumentContainerHeaderDataTemplate" DataType="{x:Type tools_controls:TabItemExt}">
        <TextBlock
            x:Name="HeaderText"
            Padding="4,0,4,1"
            Focusable="False"
            IsHitTestVisible="False"
            Text="{Binding}"
            TextTrimming="CharacterEllipsis" />
    </DataTemplate>

    <DataTemplate x:Key="DocumentContainerEditableDataTemplate" DataType="{x:Type tools_controls:TabItemExt}">
        <TextBox
            x:Name="EditableHeader"
            Padding="4,1,4,1"
            Focusable="True"
            IsReadOnly="False"
            Style="{Binding Path=EditModeDocumentTabHeaderStyle, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockingManager}}}"
            Text="{Binding Path=Header, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, Mode=TwoWay}" />
    </DataTemplate>

    <Style x:Key="HeaderButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Width" Value="16" />
        <Setter Property="Height" Value="16" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="BorderThickness" Value="2" />
    </Style>

    <Style
        x:Key="MaximizeButtonStyle"
        BasedOn="{StaticResource HeaderButtonStyle}"
        TargetType="{x:Type Button}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        x:Name="MaximizeButtonBorder"
                        Width="{TemplateBinding ActualWidth}"
                        Height="{TemplateBinding ActualHeight}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0"
                        CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                        <Path
                                    x:Name="MaximizeButtonPath"
                                    Width="9"
                                    Height="9"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    StrokeThickness="1"
                                    SnapsToDevicePixels="False"
                                    Stretch="Fill" >
                            <Path.Data>
                                <PathGeometry>M0.5 1.25C0.5 0.835786 0.835786 0.5 1.25 0.5H7.75C8.16421 0.5 8.5 0.835786 8.5 1.25V7.75C8.5 8.16421 8.16421 8.5 7.75 8.5H1.25C0.835786 8.5 0.5 8.16421 0.5 7.75V1.25Z</PathGeometry>
                            </Path.Data>
                            <Path.Style>
                                <Style TargetType="Path">
                                    <Setter Property="Stroke" Value="{StaticResource IconColor}"/>
                                </Style>
                            </Path.Style>
                        </Path>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="MaximizeButtonPath" Property="Stroke" Value="{StaticResource IconColorHovered}" />
                            <Setter TargetName="MaximizeButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="MaximizeButtonBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="MaximizeButtonPath" Property="Stroke" Value="{StaticResource IconColorSelected}" />
                            <Setter TargetName="MaximizeButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="MaximizeButtonBorder" Property="BorderBrush" Value="{StaticResource SecondaryBackgroundSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="MaximizeButtonPath" Property="Stroke" Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="MaximizeButtonBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="MaximizeButtonBorder" Property="BorderBrush" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="MinimizeButtonStyle"
        BasedOn="{StaticResource HeaderButtonStyle}"
        TargetType="{x:Type Button}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        x:Name="MinimizeButtonBorder"
                        Width="{TemplateBinding ActualWidth}"
                        Height="{TemplateBinding ActualHeight}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0"
                        CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                        <Path
                                    x:Name="MinimizeButtonPath"
                                    Width="10"
                                    Height="1"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    StrokeThickness="1"
                                    SnapsToDevicePixels="False"
                                    Stretch="Fill" >
                            <Path.Data>
                                <PathGeometry>M0.5 0.5H10.5</PathGeometry>
                            </Path.Data>
                            <Path.Style>
                                <Style TargetType="Path">
                                    <Setter Property="Stroke" Value="{StaticResource IconColor}"/>
                                </Style>
                            </Path.Style>
                        </Path>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="MinimizeButtonPath" Property="Stroke" Value="{StaticResource IconColorHovered}" />
                            <Setter TargetName="MinimizeButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="MinimizeButtonBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="MinimizeButtonPath" Property="Stroke" Value="{StaticResource IconColorSelected}" />
                            <Setter TargetName="MinimizeButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="MinimizeButtonBorder" Property="BorderBrush" Value="{StaticResource SecondaryBackgroundSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="MinimizeButtonPath" Property="Stroke" Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="MinimizeButtonBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="MinimizeButtonBorder" Property="BorderBrush" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="RestoreButtonStyle"
        BasedOn="{StaticResource HeaderButtonStyle}"
        TargetType="{x:Type Button}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        x:Name="RestoreButtonBorder"
                        Width="{TemplateBinding ActualWidth}"
                        Height="{TemplateBinding ActualHeight}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0"
                        CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                        <Path
                                    x:Name="RestoreButtonPath"
                                    Width="10"
                                    Height="10"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    StrokeThickness="1"
                                    SnapsToDevicePixels="False"
                                    Stretch="Fill">
                            <Path.Data>
                                <PathGeometry>M2.5 0.5H8C8.82843 0.5 9.5 1.17157 9.5 2V7.5M1.5 9.5H6.5C7.05228 9.5 7.5 9.05228 7.5 8.5V3.5C7.5 2.94772 7.05228 2.5 6.5 2.5H1.5C0.947715 2.5 0.5 2.94772 0.5 3.5V8.5C0.5 9.05228 0.947715 9.5 1.5 9.5Z</PathGeometry>
                            </Path.Data>
                            <Path.Style>
                                <Style TargetType="Path">
                                    <Setter Property="Stroke" Value="{StaticResource IconColor}"/>
                                </Style>
                            </Path.Style>
                        </Path>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="RestoreButtonPath" Property="Stroke" Value="{StaticResource IconColorHovered}" />
                            <Setter TargetName="RestoreButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="RestoreButtonBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="RestoreButtonPath" Property="Stroke" Value="{StaticResource IconColorSelected}" />
                            <Setter TargetName="RestoreButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="RestoreButtonBorder" Property="BorderBrush" Value="{StaticResource SecondaryBackgroundSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="RestoreButtonPath" Property="Stroke" Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="RestoreButtonBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="RestoreButtonBorder" Property="BorderBrush" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="SyncfusionDocumentContainerCloseButtonStyle"
        BasedOn="{StaticResource HeaderButtonStyle}"
        TargetType="{x:Type Button}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        x:Name="CloseButtonBorder"
                        Width="{TemplateBinding ActualWidth}"
                        Height="{TemplateBinding ActualHeight}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0"
                        CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                        <Path
                                    x:Name="CloseButtonPath"
                                    Width="10"
                                    Height="10"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    StrokeThickness="1"
                                    SnapsToDevicePixels="False"
                                    Stretch="Fill" >
                            <Path.Data>
                                <PathGeometry>M0.5 9.5L9.5 0.5M9.5 9.5L0.5 0.5</PathGeometry>
                            </Path.Data>
                            <Path.Style>
                                <Style TargetType="Path">
                                    <Setter Property="Stroke" Value="{StaticResource IconColor}"/>
                                </Style>
                            </Path.Style>
                        </Path>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="CloseButtonPath" Property="Stroke" Value="{StaticResource IconColorHovered}" />
                            <Setter TargetName="CloseButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="CloseButtonBorder" Property="BorderBrush" Value="{StaticResource SecondaryBackgroundHovered}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="CloseButtonPath" Property="Stroke" Value="{StaticResource IconColorSelected}" />
                            <Setter TargetName="CloseButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="CloseButtonBorder" Property="BorderBrush" Value="{StaticResource SecondaryBackgroundSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="CloseButtonPath" Property="Stroke" Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="CloseButtonBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="CloseButtonBorder" Property="BorderBrush" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionDocumentHeaderStyle" TargetType="{x:Type tools_controls:DocumentHeader}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="MinHeight" Value="30" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="CloseButtonStyle" Value="{StaticResource SyncfusionDocumentContainerCloseButtonStyle}" />
        <Setter Property="MaximizeButtonStyle" Value="{StaticResource MaximizeButtonStyle}" />
        <Setter Property="MinimizeButtonStyle" Value="{StaticResource MinimizeButtonStyle}" />
        <Setter Property="RestoreButtonStyle" Value="{StaticResource RestoreButtonStyle}" />
        <Setter Property="Padding" Value="8,0,0,0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:DocumentHeader}">
                    <Border
                        Name="Border"
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        SnapsToDevicePixels="True"
                        CornerRadius="4, 4, 0, 0">
                        <DockPanel VerticalAlignment="{TemplateBinding VerticalAlignment}" LastChildFill="True">
                            <Button
                                Name="btnClose"
                                Width="18"
                                Height="18"
                                Margin="0,0,8,0"
                                Focusable="False"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                Command="{x:Static tools_controls:DocumentContainer.HideDocumentCommand}"
                                CommandTarget="{tools_controls:FindWindow}"
                                DockPanel.Dock="Right"
                                Foreground="{StaticResource IconColor}"
                                Style="{TemplateBinding CloseButtonStyle}"
                                ToolTip="{tools_controls:ToolsLocalizationResourceExtension ResourceName=CloseButtonTooltipText}"
                                Visibility="{tools_controls:ContainerButtonVisibility}" />
                            <Button
                                Name="btnMaximize"
                                Width="18"
                                Height="18"
                                Margin="0,0,8,0"
                                Focusable="False"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                Command="{x:Static tools_controls:DocumentContainer.MaximizeDocumentCommand}"
                                CommandTarget="{tools_controls:FindWindow}"
                                DockPanel.Dock="Right"
                                Foreground="{StaticResource IconColor}"
                                Style="{TemplateBinding MaximizeButtonStyle}"
                                ToolTip="{tools_controls:ToolsLocalizationResourceExtension ResourceName=Maximize}"
                                Visibility="{tools_controls:ContainerButtonVisibility}" />
                            <Button
                                Name="btnRestore"
                                Width="18"
                                Height="18"
                                Margin="0,0,8,0"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                Focusable="False"
                                Command="{x:Static tools_controls:DocumentContainer.RestoreDocumentCommand}"
                                CommandTarget="{tools_controls:FindWindow}"
                                DockPanel.Dock="Right"
                                Foreground="{StaticResource IconColor}"
                                Style="{TemplateBinding RestoreButtonStyle}"
                                ToolTip="{tools_controls:ToolsLocalizationResourceExtension ResourceName=Restore}"
                                Visibility="{tools_controls:ContainerButtonVisibility}" />
                            <Button
                                Name="btnMinimize"
                                Width="18"
                                Height="18"
                                Margin="0,0,8,0"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                Focusable="False"
                                Command="{x:Static tools_controls:DocumentContainer.MinimizeDocumentCommand}"
                                CommandTarget="{tools_controls:FindWindow}"
                                DockPanel.Dock="Right"
                                Foreground="{StaticResource IconColor}"
                                Style="{TemplateBinding MinimizeButtonStyle}"
                                ToolTip="{tools_controls:ToolsLocalizationResourceExtension ResourceName=Minimize}"
                                Visibility="{tools_controls:ContainerButtonVisibility}" />
                            <Border
                                Name="IconBorder"
                                Width="{Binding Path=IconSize, Converter={StaticResource SizeToDoubleConverter}, ConverterParameter=width, RelativeSource={RelativeSource TemplatedParent}}"
                                Height="{Binding Path=IconSize, Converter={StaticResource SizeToDoubleConverter}, ConverterParameter=height, RelativeSource={RelativeSource TemplatedParent}}"
                                Margin="1"
                                Background="{TemplateBinding Icon}"
                                DockPanel.Dock="Left" />
                            <ContentPresenter
                                Name="content"
                                Margin="{TemplateBinding Padding}"
                                VerticalAlignment="Center"
                                Content="{TemplateBinding Header}"
                                ContentTemplate="{TemplateBinding HeaderTemplate}"
                                ContentTemplateSelector="{StaticResource DocumentHeaderTrimmingTemplate}"
                                TextBlock.Foreground="{TemplateBinding Foreground}" >
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </DockPanel>

                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <DataTrigger Binding="{Binding ElementName=IconBorder, Path=(tools_controls:DocumentContainer.Icon)}" Value="{x:Null}">
                            <Setter TargetName="IconBorder" Property="Visibility" Value="Collapsed" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=Content.(tools_controls:DocumentContainer.MDIWindowState), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:MDIWindow}}}" Value="Minimized">
                            <Setter TargetName="btnMinimize" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="btnRestore" Property="Visibility" Value="{tools_controls:ContainerButtonVisibility}" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Icon" Value="{Binding Path=(tools_controls:MDIWindow.Content).(tools_controls:DocumentContainer.Icon), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:MDIWindow}}}" />
        <Setter Property="Header" Value="{Binding Path=(tools_controls:MDIWindow.Content).(tools_controls:DocumentContainer.Header), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:MDIWindow}}}" />
        <Setter Property="HeaderTemplate" Value="{Binding Path=(tools_controls:MDIWindow.Content).(tools_controls:DocumentContainer.HeaderTemplate), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:MDIWindow}}}" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionDocumentHeaderStyle}" TargetType="{x:Type tools_controls:DocumentHeader}" />

    <DataTemplate x:Key="DragDropTempl">
        <shared:PreviewBorder DataContext="{TemplateBinding ContentControl.Content}" />
    </DataTemplate>

    <Style x:Key="SyncfusionDocumentContainerStyle" TargetType="{x:Type tools_controls:DocumentContainer}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="MenuItemsPanelTemplate" Value="{Binding Path=DefaultMenuItemsPanelTemplate, RelativeSource={RelativeSource Self}}" />
        <Setter Property="DragDropTemplate" Value="{StaticResource DragDropTempl}" />
        <Setter Property="tools_controls:DocumentContainer.DocumentTabControlStyle" Value="{StaticResource SyncfusionTabControlExtStyle}" />
        <Setter Property="tools_controls:DocumentContainer.DocumentTabItemStyle" Value="{StaticResource SyncfusionTabItemExtStyle}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:DocumentContainer}">
                    <Border
                        Name="Border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        ClipToBounds="True"
                        Focusable="False"
                        CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                        <Grid>
                            <DockPanel Name="Panel" LastChildFill="True">
                                <ScrollViewer
                                    Name="Viewer"
                                    Focusable="False"
                                    HorizontalScrollBarVisibility="{TemplateBinding IsEnabledScroll,
                                                                                    Converter={StaticResource BoolToScrollBarVisibility}}"
                                    VerticalScrollBarVisibility="{TemplateBinding IsEnabledScroll,
                                                                                  Converter={StaticResource BoolToScrollBarVisibility}}">
                                    <tools_controls:MDILayoutPanel x:Name="PART_Layouter" ClipToBounds="True" />
                                </ScrollViewer>
                            </DockPanel>
                            <tools_controls:VistaFlipSwitchPreviewControl
                                Name="PART_VistaFlip"
                                Background="{TemplateBinding Background}"
                                Visibility="Collapsed" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger SourceName="PART_VistaFlip" Property="Visibility" Value="Visible">
                            <Setter TargetName="PART_Layouter" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Mode" Value="TDI">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:DocumentContainer}">
                            <Border
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                ClipToBounds="True">
                                <Grid>
                                    <tools_controls:TDILayoutPanel x:Name="PART_TabControl" />
                                    <tools_controls:VistaFlipSwitchPreviewControl
                                        Name="PART_VistaFlip"
                                        Background="{TemplateBinding Background}"
                                        Visibility="Collapsed" />
                                </Grid>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger SourceName="PART_VistaFlip" Property="Visibility" Value="Visible">
                                    <Setter TargetName="PART_TabControl" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Mode" Value="MDI" />
                    <Condition Property="IsInMDIMaximizedState" Value="true" />
                </MultiTrigger.Conditions>
                <Setter Property="MenuItemsPanelTemplate">
                    <Setter.Value>
                        <ItemsPanelTemplate>
                            <tools_controls:CustomWrapPanel IsItemsHost="True" />
                        </ItemsPanelTemplate>
                    </Setter.Value>
                </Setter>
            </MultiTrigger>
        </Style.Triggers>
        <Style.Resources>
            <Style TargetType="{x:Type tools_controls:Splitter}">
                <Setter Property="Background" Value="{StaticResource BorderAlt}" />
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{StaticResource Border}" />
                        <Setter Property="BorderBrush" Value="{StaticResource Border}" />
                    </Trigger>
                </Style.Triggers>
            </Style>
        </Style.Resources>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionDocumentContainerStyle}" TargetType="{x:Type tools_controls:DocumentContainer}" />

    <Style x:Key="SyncfusionMDIWindowStyle" TargetType="{x:Type tools_controls:MDIWindow}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Background" Value="{StaticResource PopupBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="MinHeight" Value="32" />
        <Setter Property="MinWidth" Value="62" />
        <Setter Property="HeaderBrush" Value="{StaticResource ContentBackground}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="Cycle" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:MDIWindow}">
                    <Grid>
                        <Border
                            x:Name="ShadowBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"/>
                        <Border
                            Name="Border3D"
                            tools_controls:DocumentContainerHelper.IsMDIBorder="True"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            DataContext="{Binding Path=DataContext, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:MDIWindow}}}"
                            SnapsToDevicePixels="True">
                            <DockPanel
                                Name="WrapDockPanel"
                                MinHeight="{TemplateBinding MinHeight}"
                                LastChildFill="True">
                                <tools_controls:DocumentHeader
                                    Name="PART_DocumentHeader"
                                    tools_controls:DocumentContainerHelper.IsMDIHeader="True"
                                    Background="{TemplateBinding HeaderBrush}"
                                    ContextMenuService.PlacementTarget="{tools_controls:FindWindow}"
                                    DockPanel.Dock="Top" BorderThickness="{StaticResource Windows11Dark.BorderThickness}"
                                    FocusManager.IsFocusScope="True"
                                    Foreground="{TemplateBinding Foreground}">
                                    <tools_controls:DocumentHeader.ContextMenu>
                                        <tools_controls:DocumentContextMenu
                                            Name="documentContextMenu"
                                            Padding="0,2,0,2"
                                            Background="{TemplateBinding Background}" />
                                    </tools_controls:DocumentHeader.ContextMenu>
                                </tools_controls:DocumentHeader>
                                <AdornerDecorator Name="WrapAdorner">
                                    <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" 
                                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" >
                                        <Grid Name="grid" Background="Transparent">
                                            <ContentPresenter
                                                Name="PART_ContentPresenter"
                                                Margin="1"
                                                Content="{Binding Path=Content, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:MDIWindow}}}"
                                                ContentSource="{Binding Path=Content, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:MDIWindow}}}"
                                                ContentTemplate="{Binding Path=ContentTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:MDIWindow}}}"
                                                ContentTemplateSelector="{Binding Path=ContentTemplateSelector, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:MDIWindow}}}"
                                                FocusVisualStyle="{x:Null}"
                                                KeyboardNavigation.TabNavigation="Cycle" />
                                            <Border Name="PART_CoverletControl" Visibility="Collapsed" />
                                        </Grid>
                                    </Border>
                                </AdornerDecorator>
                            </DockPanel>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Path=(tools_controls:MDIWindow.Content).(tools_controls:DocumentContainer.MDIWindowState), RelativeSource={RelativeSource Mode=Self}}" Value="Maximized">
                            <Setter Property="FontWeight" Value="SemiBold" />
                            <Setter Property="BorderThickness" Value="1" />
                            <Setter TargetName="PART_DocumentHeader" Property="Border.CornerRadius" Value="0" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=(tools_controls:MDIWindow.Content).(tools_controls:DocumentContainer.MDIWindowState), RelativeSource={RelativeSource Mode=Self}}" Value="Minimized">
                            <Setter Property="tools_controls:DocumentContainer.AllowMDIResize" Value="False" />
                            <Setter TargetName="PART_DocumentHeader" Property="ContextMenuService.Placement" Value="Top" />
                            <Setter TargetName="ShadowBorder" Property="Visibility" Value="Collapsed" />
                            <Setter Property="BorderThickness" Value="1" />
                        </DataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=UseInteropCompatibility, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DocumentContainer}}}" Value="True" />
                                <Condition Binding="{Binding Path=IsActive, RelativeSource={RelativeSource Self}}" Value="False" />
                                <Condition Binding="{Binding Path=HasHwndHost, RelativeSource={RelativeSource Self}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_ContentPresenter" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="PART_CoverletControl" Property="Visibility" Value="Visible" />
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <!--<Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBackground}" />
                <Setter Property="HeaderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter Property="Foreground" Value="{StaticResource HoveredForeground}" />
            </Trigger>-->
            <Trigger Property="IsActive" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="HeaderBrush" Value="{StaticResource ContentBackgroundAlt1}" />
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </Trigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:MDIWindow.Content).(tools_controls:DocumentContainer.MDIWindowState), RelativeSource={RelativeSource Mode=Self}}" Value="Maximized" />
                    <Condition Binding="{Binding Path=IsActive, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
            </MultiDataTrigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="HeaderBrush" Value="{StaticResource ContentBackground}" />
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionMDIWindowStyle}" TargetType="{x:Type tools_controls:MDIWindow}" />

    <Style BasedOn="{StaticResource WPFContextMenuStyle}" TargetType="{x:Type tools_controls:DocumentContextMenu}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="CustomItemStyle" Value="{StaticResource WPFMenuItemStyle}" />
        <Setter Property="CustomSeparatorStyle" Value="{StaticResource WPFSeparatorStyle}" />
    </Style>

    <ControlTemplate x:Key="ListBoxTemplate" TargetType="{x:Type ListBox}">
        <Border
            Name="bd"
            Padding="1,1,1,1"
            Background="{TemplateBinding Background}"
            BorderBrush="{TemplateBinding BorderBrush}"
            BorderThickness="{TemplateBinding BorderThickness}"
            SnapsToDevicePixels="True">
            <ScrollViewer Padding="{TemplateBinding Control.Padding}" Focusable="False">
                <DockPanel LastChildFill="True">
                    <Border
                        Name="border"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="1"
                        DockPanel.Dock="Right">
                        <tools_controls:TrackContainer VerticalAlignment="Center">
                            <shared:PreviewBorder
                                Name="PART_PreviewBorder"
                                Width="75"
                                Height="75"
                                Background="{StaticResource ContentBackground}"
                                BorderBrush="{StaticResource BorderAlt}" />
                        </tools_controls:TrackContainer>
                    </Border>
                    <ItemsPresenter SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}" />
                </DockPanel>
            </ScrollViewer>
        </Border>
    </ControlTemplate>

    <Style x:Key="ListBoxItemStyle" TargetType="{x:Type ListBoxItem}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="MinWidth" Value="30" />
        <Setter Property="MinHeight" Value="25" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListBoxItem}">
                    <Border
                        Name="WrappBorder"
                        Margin="2"
                        Padding="2"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{Binding Path=ItemBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:ListSwicthPreviewControl}}}"
                        BorderThickness="{Binding Path=ItemBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:ListSwicthPreviewControl}}}"
                        CornerRadius="{Binding Path=ItemCornerRadius, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:ListSwicthPreviewControl}}}"
                        SnapsToDevicePixels="True">
                        <StackPanel Orientation="Horizontal">
                            <Border
                                Name="IconBorder"
                                Width="16"
                                Margin="1"
                                Background="{Binding Path=(tools_controls:DocumentContainer.Icon), Converter={StaticResource TestConverter}}" />
                            <ContentPresenter
                                Name="content"
                                Content="{Binding Path=(tools_controls:DocumentContainer.Header)}"
                                ContentTemplate="{Binding Path=(FrameworkElement.DataContext).(tools_controls:DocumentContainer.HeaderTemplate), ElementName=WrappBorder}"
                                TextBlock.Foreground="{TemplateBinding Foreground}" />
                        </StackPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="WrappBorder" Property="BorderBrush" Value="{Binding Path=SelectedItemBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}" />
                            <Setter TargetName="WrappBorder" Property="BorderThickness" Value="{Binding Path=SelectedItemBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}" />
                            <Setter TargetName="WrappBorder" Property="Background" Value="{Binding Path=SelectedItemBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="WrappBorder" Property="BorderBrush" Value="{Binding Path=SelectedItemBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}" />
                            <Setter TargetName="WrappBorder" Property="BorderThickness" Value="{Binding Path=SelectedItemBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}" />
                            <Setter TargetName="WrappBorder" Property="Background" Value="{Binding Path=SelectedItemBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:VS2005SwitchPreviewControl}}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="{x:Type tools_controls:ListSwicthPreviewControl}" TargetType="{x:Type tools_controls:ListSwicthPreviewControl}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="1.5" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="CornerRadius" Value="0" />
        <Setter Property="ItemBorderThickness" Value="1" />
        <Setter Property="SelectedItemBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="ItemBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="SelectedItemBackground" Value="{StaticResource ContentBackgroundSelected}" />
        <Setter Property="SelectedItemBorderThickness" Value="1" />
        <Setter Property="ItemCornerRadius" Value="0" />
        <Setter Property="ItemWidth" Value="0" />
        <Setter Property="ItemHeight" Value="0" />
        <Setter Property="MinWidth" Value="550" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:ListSwicthPreviewControl}">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <ListBox
                            Name="PART_ListBox"
                            Background="Transparent"
                            BorderBrush="{StaticResource BorderAlt}"
                            ItemContainerStyle="{StaticResource ListBoxItemStyle}"
                            Template="{StaticResource ListBoxTemplate}" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Popup}}, Converter={StaticResource ParentToBool}}" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Background" Value="{StaticResource ContentBackground}" />
                <Setter Property="SelectedItemBorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="SelectedItemBackground" Value="{StaticResource ContentBackgroundSelected}" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="{x:Type tools_controls:DocMenuItem}"
        BasedOn="{StaticResource WPFMenuItemStyle}"
        TargetType="{x:Type tools_controls:DocMenuItem}" />

</ResourceDictionary>
