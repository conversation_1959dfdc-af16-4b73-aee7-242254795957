﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\AirMonitor.Infrastructure\AirMonitor.Infrastructure.csproj" />
    <ProjectReference Include="..\AirMonitor.Core\AirMonitor.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Validation -->
    <PackageReference Include="FluentValidation" Version="12.0.0" />

    <!-- Retry Policy -->
    <PackageReference Include="Polly" Version="8.5.2" />

    <!-- Object Mapping -->
    <PackageReference Include="AutoMapper" Version="14.0.0" />

    <!-- Logging -->
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />

    <!-- Dependency Injection -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
