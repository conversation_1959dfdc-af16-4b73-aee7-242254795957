<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Microsoft_Windows_Aero="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:input="clr-namespace:Syncfusion.Windows.Controls.Input;assembly=Syncfusion.SfInput.WPF"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shared_converters="clr-namespace:Syncfusion.Windows.Converters;assembly=Syncfusion.SfInput.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>
    
    <BooleanToVisibilityConverter x:Key="BooleanVisibilityConverter" />

    <Style x:Key="ArrowButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Grid Background="{TemplateBinding Background}" Focusable="False">
                        <ContentPresenter />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <DataTemplate x:Key="ExpandCollapseButtonTemplate">
        <Grid>
            <Border
                x:Name="RectangleButton11"
                Width="32"
                Height="16" CornerRadius="0,0,2,2"
                HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                VerticalAlignment="{TemplateBinding VerticalAlignment}"
                Background="{StaticResource ContentBackgroundAlt1}"
                SnapsToDevicePixels="True"
                BorderBrush="{StaticResource ContentBackgroundAlt1}" />
            <Path
                        x:Name="Arrow"
                        Width="12"
                        Height="6"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Fill="Transparent"
                        Stroke="{StaticResource IconColor}"
                        StrokeThickness="1"
                        Stretch="Fill" >
                <Path.Data>
                    <PathGeometry>M1 1L7 7L13 1</PathGeometry>
                </Path.Data>
            </Path>
        </Grid>
        <DataTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter TargetName="RectangleButton11" Property="Background" Value="{StaticResource ContentBackgroundAlt2}" />
                <Setter TargetName="RectangleButton11" Property="BorderBrush" Value="{StaticResource ContentBackgroundAlt2}" />
                <Setter TargetName="Arrow" Property="Stroke" Value="{StaticResource IconColorHovered}" />
            </Trigger>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" TargetName="RectangleButton11" Value="20" />
                <Setter Property="MinWidth" TargetName="RectangleButton11" Value="40" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=IsPressed, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="True">
                <Setter TargetName="RectangleButton11" Property="Background" Value="{StaticResource ContentBackgroundAlt3}" />
                <Setter TargetName="RectangleButton11" Property="BorderBrush" Value="{StaticResource ContentBackgroundAlt3}" />
                <Setter TargetName="Arrow" Property="Stroke" Value="{StaticResource IconColorSelected}" />
            </DataTrigger>
            
        </DataTemplate.Triggers>
    </DataTemplate>

    <DataTemplate x:Key="GripperTemplate">
        <StackPanel
                x:Name="HorizontalResizeGripperPanel"
                Width="13"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Background="Transparent"
                Orientation="Horizontal">
            <Path
                    x:Name="HorizontalGripper"
                    Width="3"
                    Height="13"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Top"
                    Fill="{StaticResource IconColor}"
                    Stroke="{StaticResource IconColor}"
                    StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}"
                    Stretch="Fill">
                <Path.Data>
                    <PathGeometry>M0.5 0V12M2.5 0V12</PathGeometry>
                </Path.Data>
                <Path.LayoutTransform>
                    <RotateTransform x:Name="Transform" Angle="90" />
                </Path.LayoutTransform>
            </Path>
        </StackPanel>
        <DataTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter TargetName="HorizontalGripper" Property="Fill" Value="{StaticResource IconColor}" />
                <Setter TargetName="HorizontalGripper" Property="Stroke" Value="{StaticResource IconColor}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="HorizontalGripper" Property="Fill" Value="{StaticResource IconColor}" />
                <Setter TargetName="HorizontalGripper" Property="Stroke" Value="{StaticResource IconColor}" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=IsPressed, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="True">
                <Setter TargetName="HorizontalGripper" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                <Setter TargetName="HorizontalGripper" Property="Stroke" Value="{StaticResource IconColorDisabled}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=Orientation, RelativeSource={RelativeSource AncestorType={x:Type input:SfGridSplitter}},UpdateSourceTrigger=PropertyChanged}" Value="Columns">
                <Setter Property="Orientation" TargetName="HorizontalResizeGripperPanel" Value="Vertical"></Setter>
                <Setter Property="Width" TargetName="HorizontalResizeGripperPanel" Value="3"></Setter>
                <Setter Property="Height" TargetName="HorizontalResizeGripperPanel" Value="13"></Setter>
                <Setter Property="LayoutTransform" TargetName="HorizontalGripper">
                    <Setter.Value>
                        <RotateTransform Angle="0"></RotateTransform>
                    </Setter.Value>
                </Setter>
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <ControlTemplate x:Key="SyncfusionSfGridSplitterHorizontalControlTemplate" TargetType="{x:Type input:SfGridSplitter}">
        <Grid
            x:Name="HorizontalGrid"
            Width="{TemplateBinding Width}"
            Height="{TemplateBinding Height}"
            Background="{TemplateBinding Background}">
            <ContentPresenter ContentTemplate="{Binding HorizontalGripperTemplate,RelativeSource={RelativeSource Mode=TemplatedParent}}"></ContentPresenter>
            <Popup
                x:Name="Popup_Up"
                AllowsTransparency="True"
                Placement="Center"
                PopupAnimation="Fade"
                VerticalOffset="-18">
                <Button
                    x:Name="PART_Up"
                    Style="{StaticResource ArrowButtonStyle}"
                    Foreground="{StaticResource ContentBackgroundAlt1}"
                    ContentTemplate="{Binding UpButtonTemplate, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                    Visibility="{Binding EnableCollapseButton, RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource BooleanVisibilityConverter}}">
                    <Button.LayoutTransform>
                        <RotateTransform Angle="180" />
                    </Button.LayoutTransform>
                </Button>
            </Popup>
            <Popup
                x:Name="Popup_Down"
                AllowsTransparency="True"
                Placement="Center"
                PopupAnimation="Fade"
                VerticalOffset="18">
                <Button
                    x:Name="PART_Down"
                    Style="{StaticResource ArrowButtonStyle}"
                    Grid.Column="1"
                    Foreground="{StaticResource ContentBackgroundAlt1}"
                    ContentTemplate="{Binding DownButtonTemplate, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                    Visibility="{Binding EnableCollapseButton, RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource BooleanVisibilityConverter}}" />
            </Popup>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource BorderAlt1}" />
            </Trigger>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter TargetName="Popup_Up" Property="VerticalOffset" Value="-26"/>
                <Setter TargetName="Popup_Down" Property="VerticalOffset" Value="26"/>
            </Trigger>
            <DataTrigger Binding="{Binding Path=IsPressed, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="True">
                <Setter Property="Background" Value="{StaticResource PrimaryBackground}" />
            </DataTrigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background" Value="{StaticResource BorderAlt}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionSfGridSplitterVerticalControlTemplate" TargetType="{x:Type input:SfGridSplitter}">
        <Grid
            x:Name="VerticalGrid"
            Width="{TemplateBinding Width}"
            Background="{TemplateBinding Background}">
            <ContentPresenter ContentTemplate="{Binding VerticalGripperTemplate,RelativeSource={RelativeSource Mode=TemplatedParent}}"></ContentPresenter>
            <Popup
                x:Name="Popup_Left"
                AllowsTransparency="True"
                HorizontalOffset="-18"
                Placement="Center"
                PopupAnimation="Fade">
                <Button
                    x:Name="PART_Left"
                    Style="{StaticResource ArrowButtonStyle}"
                    Foreground="{StaticResource ContentBackgroundAlt1}"
                    ContentTemplate="{Binding LeftButtonTemplate, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                    Visibility="{Binding EnableCollapseButton, RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource BooleanVisibilityConverter}}">
                    <Button.LayoutTransform>
                        <RotateTransform Angle="90" />
                    </Button.LayoutTransform>
                </Button>
            </Popup>
            <Popup
                x:Name="Popup_Right"
                AllowsTransparency="True"
                HorizontalOffset="18"
                Placement="Center"
                PopupAnimation="Fade">
                <Button
                    x:Name="PART_Right"
                    Style="{StaticResource ArrowButtonStyle}"
                    Grid.Row="1"
                    Foreground="{StaticResource ContentBackgroundAlt1}"
                    ContentTemplate="{Binding RightButtonTemplate, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                    Visibility="{Binding EnableCollapseButton, RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource BooleanVisibilityConverter}}">
                    <Button.LayoutTransform>
                        <RotateTransform Angle="270" />
                    </Button.LayoutTransform>
                </Button>
            </Popup>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource BorderAlt1}" />
            </Trigger>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter TargetName="Popup_Right" Property="HorizontalOffset" Value="26"/>
                <Setter TargetName="Popup_Left" Property="HorizontalOffset" Value="-26"/>
            </Trigger>
            <DataTrigger Binding="{Binding Path=IsPressed, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="True">
                <Setter Property="Background" Value="{StaticResource PrimaryBackground}" />
            </DataTrigger>

            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background" Value="{StaticResource BorderAlt}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SyncfusionSfGridSplitterStyle" TargetType="{x:Type input:SfGridSplitter}">   
        <Setter Property="Background" Value="{StaticResource BorderAlt}" />
        <Setter Property="IsTabStop" Value="true" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="UpButtonTemplate" Value="{StaticResource ExpandCollapseButtonTemplate}" />
        <Setter Property="DownButtonTemplate" Value="{StaticResource ExpandCollapseButtonTemplate}" />
        <Setter Property="LeftButtonTemplate" Value="{StaticResource ExpandCollapseButtonTemplate}"/>
        <Setter Property="RightButtonTemplate" Value="{StaticResource ExpandCollapseButtonTemplate}"/>
        <Setter Property="HorizontalGripperTemplate" Value="{StaticResource GripperTemplate}"/>
        <Setter Property="VerticalGripperTemplate" Value="{StaticResource GripperTemplate}"/>
        <Setter Property="PreviewStyle">
            <Setter.Value>
                <Style TargetType="Control">
                    <Setter Property="Width" Value="{Binding Width, RelativeSource={RelativeSource Self}}" />
                    <Setter Property="Height" Value="{Binding Height, RelativeSource={RelativeSource Self}}" />
                    <Setter Property="Background" Value="{StaticResource PrimaryBackground}" />
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="Control">
                                <Grid x:Name="Root" Opacity=".5">
                                    <Rectangle Fill="{TemplateBinding Background}" />
                                </Grid>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding RelativeSource={RelativeSource Mode=Self}, Path=Orientation, Mode=OneWay}" Value="Rows" />
                    <Condition Binding="{Binding RelativeSource={RelativeSource Mode=Self}, Path=HorizontalAlignment}" Value="Stretch" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Template" Value="{StaticResource SyncfusionSfGridSplitterHorizontalControlTemplate}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding RelativeSource={RelativeSource Mode=Self}, Path=Orientation, Mode=OneWay}" Value="Columns" />
                    <Condition Binding="{Binding RelativeSource={RelativeSource Mode=Self}, Path=VerticalAlignment}" Value="Stretch" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Template" Value="{StaticResource SyncfusionSfGridSplitterVerticalControlTemplate}" />
            </MultiDataTrigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfGridSplitterStyle}" TargetType="{x:Type input:SfGridSplitter}" />

</ResourceDictionary>
