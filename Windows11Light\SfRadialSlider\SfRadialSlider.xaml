<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:radialmenu="clr-namespace:Syncfusion.Windows.Controls.Navigation;assembly=Syncfusion.SfRadialMenu.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <!--  Radial Slider  -->

    <Style x:Key="SyncfusionTicksRadialListStyle" TargetType="radialmenu:RadialList">
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <radialmenu:RadialPanel />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionLabelRadialListStyle" TargetType="radialmenu:RadialList">
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Width" Value="NaN" />
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <radialmenu:RadialPanel RotateItems="False" />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionRadialLabelStyle" TargetType="radialmenu:RadialLabel">
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="radialmenu:RadialLabel">
                    <ContentPresenter />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionRadialLabelStyle}" TargetType="{x:Type radialmenu:RadialLabel}" />

    <Style x:Key="SyncfusionRadialPointerStyle" TargetType="radialmenu:RadialPointer">
        <Setter Property="Height" Value="2" />
        <Setter Property="Background" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="radialmenu:RadialPointer">
                    <Border x:Name="Needle" Background="{TemplateBinding Background}" />
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Needle" Property="Background" Value="{StaticResource BorderAlt1}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionRadialPreviewPointerStyle" TargetType="radialmenu:RadialPreviewPointer">
        <Setter Property="Height" Value="1" />
        <Setter Property="Background" Value="{StaticResource PrimaryColorLight3}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="radialmenu:RadialPreviewPointer">
                    <Border x:Name="PreviewNeedle" Background="{TemplateBinding Background}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <DataTemplate x:Key="TickTemplate">
        <Border x:Name="Tick" Background="{StaticResource BorderAlt1}" />
        <DataTemplate.Triggers>
            <Trigger SourceName="Tick" Property="IsEnabled" Value="False">
                <Setter TargetName="Tick" Property="Background" Value="{StaticResource BorderAlt}" />
            </Trigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <DataTemplate x:Key="LabelTemplate">
        <TextBlock
            x:Name="Label"
            Width="35"
            FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
            FontSize="{StaticResource Windows11Light.CaptionText}"
            FontWeight="{StaticResource Windows11Light.FontWeightNormal}"
            Foreground="{StaticResource ContentForeground}"
            Text="{Binding}"
            TextAlignment="Center"
            TextTrimming="WordEllipsis" />
        <DataTemplate.Triggers>
            <Trigger SourceName="Label" Property="IsEnabled" Value="False">
                <Setter TargetName="Label" Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <DataTemplate x:Key="RunTimeLabelTemplate">
        <TextBlock
        x:Name="RunTimeLabel"
        Width="35"
        FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
        FontSize="{StaticResource Windows11Light.CaptionText}"
        FontWeight="{StaticResource Windows11Light.FontWeightNormal}"
        Foreground="{StaticResource ContentForeground}"
        Text="{Binding}"
        TextAlignment="Center"
        TextTrimming="WordEllipsis" 
        Background="{StaticResource ContentBackground}" />
        <DataTemplate.Triggers>
            <Trigger SourceName="RunTimeLabel" Property="IsEnabled" Value="False">
                <Setter TargetName="RunTimeLabel" Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <Style x:Key="SyncfusionRadialTickStyle" TargetType="radialmenu:RadialTick">
        <Setter Property="Width" Value="6" />
        <Setter Property="Height" Value="1" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="radialmenu:RadialTick">
                    <ContentPresenter />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionRadialTickStyle}" TargetType="{x:Type radialmenu:RadialTick}" />

    <Style x:Key="SyncfusionSfRadialSliderStyle" TargetType="radialmenu:SfRadialSlider">
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="OuterRimStroke" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="InnerRimStroke" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="InnerRimFill" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="TickTemplate" Value="{StaticResource TickTemplate}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.CaptionText}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="LabelTemplate" Value="{StaticResource LabelTemplate}" />
        <Setter Property="Minimum" Value="0" />
        <Setter Property="Maximum" Value="100" />
        <Setter Property="TickFrequency" Value="10" />
        <Setter Property="PointerStyle" Value="{StaticResource SyncfusionRadialPointerStyle}" />
        <Setter Property="PreviewPointerStyle" Value="{StaticResource SyncfusionRadialPreviewPointerStyle}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="radialmenu:SfRadialSlider">
                    <Grid x:Name="PART_Root" Background="Transparent">
                        <Ellipse
                            x:Name="PART_OuterEllipse"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Fill="{TemplateBinding Background}"
                            Stroke="{TemplateBinding OuterRimStroke}"
                            StrokeThickness="{TemplateBinding OuterRimStrokeThickness}" />
                        <radialmenu:RadialList
                            x:Name="PART_TicksRunTime"
                            ListHost="{Binding RelativeSource={RelativeSource TemplatedParent}}"
                            Style="{StaticResource SyncfusionTicksRadialListStyle}"
                            Visibility="{TemplateBinding TickVisibility}" />
                        <radialmenu:RadialList
                            x:Name="PART_Ticks"
                            ListHost="{Binding RelativeSource={RelativeSource TemplatedParent}}"
                            Style="{StaticResource SyncfusionTicksRadialListStyle}"
                            Visibility="{TemplateBinding TickVisibility}" />
                        <radialmenu:RadialList
                            x:Name="PART_Labels"
                            ListHost="{Binding RelativeSource={RelativeSource TemplatedParent}}"
                            Style="{StaticResource SyncfusionLabelRadialListStyle}"
                            Visibility="{TemplateBinding LabelVisibility}" />
                        <radialmenu:RadialPointer x:Name="PART_Pointer" Style="{TemplateBinding PointerStyle}" />
                        <radialmenu:RadialPreviewPointer
                            x:Name="PART_PreviewPointer"
                            Style="{TemplateBinding PreviewPointerStyle}"
                            Visibility="Collapsed" />
						<radialmenu:RadialList
                            x:Name="PART_LabelsRunTime"
                            ListHost="{Binding RelativeSource={RelativeSource TemplatedParent}}"
                            Style="{StaticResource SyncfusionLabelRadialListStyle}"
                            Visibility="{TemplateBinding LabelVisibility}" />

                        <Ellipse
                            x:Name="PART_InnerEllipse"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Fill="{TemplateBinding InnerRimFill}"
                            Stroke="{TemplateBinding InnerRimStroke}"
                            StrokeThickness="{TemplateBinding InnerRimStrokeThickness}" />
                        <Border x:Name="PART_ContentBorder" Padding="5">
                            <ContentPresenter
                                x:Name="PART_Content"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Content="{TemplateBinding Content}"
                                ContentTemplate="{TemplateBinding ContentTemplate}"
                                TextElement.FontFamily="{TemplateBinding FontFamily}"
                                TextElement.FontSize="{TemplateBinding FontSize}"
                                TextElement.FontWeight="{TemplateBinding FontWeight}"
                                TextElement.Foreground="{TemplateBinding Foreground}" >
                                <ContentPresenter.Resources>
                                    <Style TargetType="{x:Type TextBlock}">
                                        <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}"/>
                                        <Setter Property="FontSize" Value="12"/>
                                    </Style>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="PART_OuterEllipse" Property="Stroke" Value="{StaticResource PrimaryColorLight3}" />
                            <Setter TargetName="PART_InnerEllipse" Property="Stroke" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
		    <DataTrigger Binding="{Binding RunTimeLabelVisible, RelativeSource={RelativeSource Self}}" Value="True">
                <Setter Property="LabelTemplate" Value="{StaticResource RunTimeLabelTemplate}"/>
            </DataTrigger>
        </Style.Triggers>

    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfRadialSliderStyle}" TargetType="{x:Type radialmenu:SfRadialSlider}" />

</ResourceDictionary>
