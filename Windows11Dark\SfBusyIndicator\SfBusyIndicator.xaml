<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:Syncfusion.Windows.Controls.Notification;assembly=Syncfusion.SfBusyIndicator.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <local:BusyIndicatorBrushToColorConverter x:Key="BrushToColorConverter" />
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

    <Style x:Key="SyncfusionSfBusyIndicatorStyle" TargetType="local:SfBusyIndicator">
        <Setter Property="Foreground" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.SubTitleTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSfBusyIndicatorStyle}" TargetType="local:SfBusyIndicator" />
</ResourceDictionary>
