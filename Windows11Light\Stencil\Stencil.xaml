<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:diagram_panels="clr-namespace:Syncfusion.UI.Xaml.Diagram.Panels;assembly=Syncfusion.SfDiagram.WPF"
                    xmlns:diagram_util="clr-namespace:Syncfusion.UI.Xaml.Diagram.Utility;assembly=Syncfusion.SfDiagram.WPF"
                    xmlns:diagram_stencil="clr-namespace:Syncfusion.UI.Xaml.Diagram.Stencil;assembly=Syncfusion.SfDiagram.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ComboBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ScrollViewer.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Expander.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBlock.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GridSplitter.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ToggleButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphToggleButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Menu.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ListView.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ListBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Label.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/RepeatButton.xaml"/>

    </ResourceDictionary.MergedDictionaries>

    <diagram_util:NullToBooleanConverter x:Key="NullToBooleanConverter"/>
    <diagram_util:NullToVisibityConverter x:Key="NullToVisibityConverter"/>
    <diagram_util:VisibilityToBoolConverter x:Key="VisibilityToBoolConverter"/>
    <diagram_util:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
    <diagram_util:EnumToBooleanConverter x:Key="EnumToBooleanConverter"/>

    <ControlTemplate x:Key="SymbolGroupListHeaderTemplate" TargetType="ListViewItem">
        <Grid>
            <Border x:Name="Border" Background="{TemplateBinding Background}">
                <ContentPresenter HorizontalAlignment="Left" 
                                  VerticalAlignment="Center"
                                  Margin="8,0,0,0" 
                                  RenderTransformOrigin="0.5,0.5"
                                  Content="{TemplateBinding Content}"
                                  ContentTemplate="{TemplateBinding  ContentTemplate}"/>
            </Border>
        </Grid>
    </ControlTemplate>

    <ControlTemplate TargetType="{x:Type ListViewItem}" x:Key="StencilGroupTabHeaderTemplate">
        <Grid>
            <Border x:Name="Bd"
                    CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                    BorderBrush="{StaticResource BorderAlt}" 
                    BorderThickness="{TemplateBinding BorderThickness}" 
                    Background="Transparent" 
                    Padding="{TemplateBinding Padding}" 
                    SnapsToDevicePixels="true">
                <ContentPresenter x:Name="content"
                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                  SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                    <ContentPresenter.Resources>
                        <Style TargetType="TextBlock" BasedOn="{x:Null}"/>
                    </ContentPresenter.Resources>
                </ContentPresenter>
            </Border>
            <Border x:Name="ListViewSelectionIndicator"
                    HorizontalAlignment="Right"             
                    CornerRadius="1.5"
                    Visibility="Collapsed"
                    Height="{TemplateBinding Height}"
                    Width="2"
                    Background="{StaticResource PrimaryBackground}">
                <Border.Margin>
                    <Thickness>0,0,1,0</Thickness>
                </Border.Margin>
            </Border>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" TargetName="Bd" Value="{StaticResource ContentBackgroundHovered}"/>
                <Setter Property="BorderBrush" TargetName="Bd" Value="{StaticResource BorderAlt}"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="true">
                <Setter Property="Background" TargetName="Bd" Value="Transparent"/>
                <Setter Property="BorderBrush" TargetName="Bd" Value="{StaticResource BorderAlt}"/>
                <Setter Property="Visibility" TargetName="ListViewSelectionIndicator" Value="Visible"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <DataTemplate x:Key="DefaultTabHeaderTemplate">
        <Grid RenderTransformOrigin="0.5,0.5">
            <TextBlock Text="{Binding}" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Top">
                <TextBlock.LayoutTransform>
                    <RotateTransform Angle="270"/>
                </TextBlock.LayoutTransform>
            </TextBlock>
        </Grid>
    </DataTemplate>
    
    <Style TargetType="{x:Type TextBox}" x:Key="SearchTextBoxStyle" BasedOn="{StaticResource WPFTextBoxStyle}">
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Height" Value="24"/>        
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">
                    <Border x:Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid x:Name="LayoutGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="24" />
                                <ColumnDefinition Width="24" />
                            </Grid.ColumnDefinitions>
                            <ScrollViewer  x:Name="PART_ContentHost" Grid.Column="0" VerticalAlignment="Center" Style="{StaticResource WPFScrollViewerStyle}"/>
                            <Label x:Name="LabelText" 
                                   Style="{StaticResource WPFLabelStyle}"
                                   Visibility="Hidden"
                                   Grid.Column="0"
                                   Padding="2,0,0,0"
                                   VerticalAlignment="Center" />
                            <Border x:Name="PART_SearchIconBorder"  
                                    Grid.Column="2" 
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="0,0,0,0"
                                    VerticalAlignment="Stretch"
                                    HorizontalAlignment="Stretch">
                                <Button Style="{StaticResource WPFGlyphButtonStyle}">
                                    <Button.Content>
                                        <Grid x:Name="SearchIcon" Height="15"  Width="16">
                                            <Path Data="M4 6.25C4 9.42564 6.57436 12 9.75 12V11C7.12665 11 5 8.87335 5 6.25H4ZM9.75 12C12.9256 12 15.5 9.42564 15.5 6.25H14.5C14.5 8.87335 12.3734 11 9.75 11V12ZM15.5 6.25C15.5 3.07436 12.9256 0.5 9.75 0.5V1.5C12.3734 1.5 14.5 3.62665 14.5 6.25H15.5ZM9.75 0.5C6.57436 0.5 4 3.07436 4 6.25H5C5 3.62665 7.12665 1.5 9.75 1.5V0.5ZM5.49588 9.36302L0.683381 13.3005L1.31662 14.0745L6.12912 10.137L5.49588 9.36302Z" 
                                                  Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}" />
                                        </Grid>
                                    </Button.Content>
                                </Button>
                            </Border>
                            <Border x:Name="PART_PreviousItem"
                                    Grid.Column="1"
                                    Background="{TemplateBinding Background}"
                                    VerticalAlignment="Stretch"
                                    HorizontalAlignment="Stretch">
                                <Button Style="{StaticResource WPFGlyphButtonStyle}">
                                    <Button.Content>
                                        <Path x:Name="PreviousIcon"
                                              HorizontalAlignment="Center" 
                                              VerticalAlignment="Center" 
                                              Stretch="None"
                                              Width="9" 
                                              Height="5" 
                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}">
                                            <Path.Data>
                                                <PathGeometry>M7.5 0C7.36328 0 7.24609 0.0488281 7.14844 0.146484L4.00195 3.29297L0.855469 0.146484C0.757812 0.0488281 0.640625 0 0.503906 0C0.367188 0 0.25 0.046875 0.152344 0.140625C0.0507812 0.238281 0 0.357422 0 0.498047C0 0.634766 0.0488281 0.751953 0.146484 0.849609L3.65039 4.35352C3.74805 4.45117 3.86523 4.5 4.00195 4.5C4.13867 4.5 4.25586 4.45117 4.35352 4.35352L7.85742 0.849609C7.95508 0.751953 8.00391 0.634766 8.00391 0.498047C8.00391 0.357422 7.95508 0.238281 7.85742 0.140625C7.75586 0.046875 7.63672 0 7.5 0Z</PathGeometry>
                                            </Path.Data>
                                        </Path>
                                    </Button.Content>
                                </Button>
                            </Border>
                            <Border x:Name="PART_ClearIconBorder"
                                    Grid.Column="2" 
                                    Visibility="Hidden"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="0,0,0,0"
                                    VerticalAlignment="Stretch"
                                    HorizontalAlignment="Stretch">
                                <Button Style="{StaticResource WPFGlyphButtonStyle}">
                                    <Button.Content>
                                        <Path x:Name="ClearType"
                                              Data="M0.70700001,0 L2.5025001,1.7929902 4.2979999,0 5.0050001,0.70800779 3.2104995,2.5 5.0050001,4.2919922 4.2979999,5 2.5025001,3.2070098 0.70700001,5 0,4.2919922 1.7945005,2.5 0,0.70800779 z" 
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Stretch="Fill" 
                                              Width="10"
                                              Height="10"
                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}" />
                                    </Button.Content>
                                </Button>
                            </Border>
                            <Popup x:Name="Part_Popup" >
                                <ScrollViewer  CanContentScroll="True" Style="{StaticResource WPFScrollViewerStyle}">
                                    <ListBox MaxHeight="100" x:Name="Part_ListBox" Style="{StaticResource WPFListBoxStyle}" Background="{StaticResource PopupBackground}"/>
                                </ScrollViewer>
                            </Popup>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True" SourceName="LabelText">
                            <Setter Property="Cursor" Value="IBeam" />
                        </Trigger>
                        <Trigger Property="Text" Value="">
                            <Setter Property="Visibility" TargetName="LabelText" Value="Visible" />
                        </Trigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="diagram_stencil:Stencil" x:Key="SyncfusionStencilStyle">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}"/>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
            <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_stencil:Stencil">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid Background="{TemplateBinding Background}">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Canvas x:Name="PART_InvisibleDiagramCanvas"
                                    Background="{TemplateBinding Background}"/>
                            <Grid x:Name="Part_ExpanderGrid"
                                  Grid.Row="1"
                                 Margin="10,8,0,0"
                                  Background="{TemplateBinding Background}">
                                <Expander x:Name="expander"          
                                          ExpandDirection="Right"
                                          HorizontalAlignment="Right"
                                          Style="{StaticResource WPFFlatExpanderStyle}"
                                          Visibility="{TemplateBinding ShowDisplayModeToggleButton,Converter={StaticResource VisibilityToBoolConverter}}"
                                          IsExpanded="{Binding Path=DisplayMode,RelativeSource={RelativeSource TemplatedParent},Mode=TwoWay,Converter={StaticResource EnumToBooleanConverter}}"/>
                                <ContentPresenter x:Name="ContentPresenter" 
                                                  HorizontalAlignment="Left"
                                                  VerticalAlignment="Center"
                                                  Content="{TemplateBinding Title}"
                                                  RenderTransformOrigin="0.5,0.5"                                                  
                                                  ContentTemplate="{TemplateBinding TitleTemplate}">
                                    <ContentPresenter.Resources>
                                        <Style TargetType="TextBlock" BasedOn="{StaticResource WPFTextBlockStyle}"/>
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                            </Grid>
                            <Grid Grid.Row="2"
                                      Background="{TemplateBinding Background}"
                                      Visibility="{Binding ElementName=expander,Path=IsExpanded,Converter={StaticResource VisibilityToBoolConverter}}">
                                <Grid.Margin>
                                    <Thickness>4,0,0,0</Thickness>
                                </Grid.Margin>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid x:Name="Part_SearchOption"
                                      Grid.Row="0" 
                                      Grid.Column="0" 
                                      Grid.ColumnSpan="2"
                                      Margin="0,5,4,5"
                                      Background="{TemplateBinding Background}"
                                      Visibility="{TemplateBinding ShowSearchTextBox,Converter={StaticResource VisibilityToBoolConverter}}" >
                                    <TextBox x:Name="Part_SearchTextBox"
                                             Margin="2,0,0,0"
                                             Style="{StaticResource SearchTextBoxStyle}"/>
                                </Grid>
                                <Border Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2"
                                            Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}">
                                    <ComboBox x:Name="Part_ComboBoxFilter"
                                                  HorizontalAlignment="Stretch" 
                                                  VerticalAlignment="Stretch" 
                                                  Padding="8"
                                                  Style="{StaticResource WPFComboBoxStyle}"
                                                  IsEnabled="{Binding Path=SymbolFilters, RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource NullToBooleanConverter}}"
                                                  Visibility="{Binding RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource NullToVisibityConverter}}"
                                                  ItemsSource="{TemplateBinding SymbolFilters}"
                                                  SelectedItem="{Binding Path=SelectedFilter, RelativeSource={RelativeSource TemplatedParent}, Mode=TwoWay}">
                                        <ComboBox.ItemTemplate>
                                            <DataTemplate>
                                                <ContentControl Content="{Binding Content}"  ContentTemplate="{Binding ContentTemplate}" />
                                            </DataTemplate>
                                        </ComboBox.ItemTemplate>
                                    </ComboBox>
                                    <Border.Style>
                                        <Style TargetType="{x:Type Border}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Path=SymbolGroupDisplayMode, RelativeSource={RelativeSource TemplatedParent}}" Value="Accordion">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                </Border>
                                <Grid x:Name="Part_AdvancedFilter" 
                                      Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2"
                                      Margin="0,0,4,0"
                                          Background="{TemplateBinding Background}">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <ToggleButton x:Name="Part_MoreShape"
                                                  Height="30">
                                        <ToggleButton.Content>
                                            <Grid Background="{TemplateBinding Background}" Width="{Binding ElementName=Part_MoreShape, Path=ActualWidth}">
                                                <TextBlock x:Name="MoreShape_Text" Text="More Shapes"
                                                                   HorizontalAlignment="Left"
                                                                   VerticalAlignment="Center"
                                                                   Style="{StaticResource WPFTextBlockStyle}">
                                                    <TextBlock.Margin>
                                                        <Thickness>4,0,0,0</Thickness>
                                                    </TextBlock.Margin>
                                                </TextBlock>
                                                <Path x:Name="ButtonPath" 
                                                              HorizontalAlignment="Right"
                                                              Width="5"
                                                              Height="9"
                                                              Margin="0,0,18,0"
                                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}" 
                                                              Stretch="None">
                                                    <Path.Data>
                                                        <PathGeometry>M0 7.5C0 7.36328 0.0488281 7.24609 0.146484 7.14844L3.29297 4.00195L0.146484 0.855469C0.0488281 0.757812 0 0.640625 0 0.503906C0 0.367188 0.046875 0.25 0.140625 0.152344C0.238281 0.0507812 0.357422 0 0.498047 0C0.634766 0 0.751953 0.0488281 0.849609 0.146484L4.35352 3.65039C4.45117 3.74805 4.5 3.86523 4.5 4.00195C4.5 4.13867 4.45117 4.25586 4.35352 4.35352L0.849609 7.85742C0.751953 7.95508 0.634766 8.00391 0.498047 8.00391C0.357422 8.00391 0.238281 7.95508 0.140625 7.85742C0.046875 7.75586 0 7.63672 0 7.5Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </ToggleButton.Content>
                                        <ToggleButton.ContextMenu>
                                            <ContextMenu x:Name="Part_ExpandedContextMenu" >
                                                <ContextMenu.Style>
                                                    <Style TargetType="{x:Type ContextMenu}" BasedOn="{StaticResource WPFContextMenuStyle}">
                                                        <Setter Property="ItemContainerStyle">
                                                            <Setter.Value>
                                                                <Style TargetType="MenuItem" BasedOn="{StaticResource WPFMenuItemStyle}">
                                                                    <Setter Property="Height" Value="32"></Setter>
                                                                    <Setter Property="StaysOpenOnClick" Value="True"></Setter>
                                                                    <Setter Property="Header" Value="{Binding Header}"/>
                                                                    <Setter Property="Foreground" Value="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(ContextMenu.Foreground)}"/>
                                                                    <Setter Property="IsChecked" Value="{Binding HeaderVisibility, Converter={StaticResource VisibilityToBoolConverter}, Mode=TwoWay}"/>
                                                                    <Setter Property="IsCheckable" Value="True"/>
                                                                </Style>
                                                            </Setter.Value>
                                                        </Setter>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Path=SymbolFilters, RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource NullToBooleanConverter}}" Value="True">
                                                                <Setter Property="ItemContainerStyle">
                                                                    <Setter.Value>
                                                                        <Style TargetType="MenuItem" BasedOn="{StaticResource WPFMenuItemStyle}">
                                                                            <Setter Property="Height" Value="32"></Setter>
                                                                            <Setter Property="StaysOpenOnClick" Value="True"></Setter>
                                                                            <Setter Property="Header" Value="{Binding Content}"/>
                                                                            <Setter Property="HeaderTemplate" Value="{Binding ContentTemplate}"/>
                                                                            <Setter Property="Foreground" Value="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(ContextMenu.Foreground)}"/>
                                                                            <Setter Property="IsChecked" Value="{Binding IsChecked,Mode=TwoWay}"/>
                                                                            <Setter Property="Command" Value="{Binding Command}"/>
                                                                            <Setter Property="CommandParameter" Value="{Binding RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}"/>
                                                                        </Style>
                                                                    </Setter.Value>
                                                                </Setter>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </ContextMenu.Style>
                                            </ContextMenu>
                                        </ToggleButton.ContextMenu>
                                    </ToggleButton>
                                    <Grid x:Name="Part_SymbolHeadersContainer"
                                              Background="{TemplateBinding Background}"
                                              Grid.Row="1">
                                        <ScrollViewer x:Name="Part_AccordionScrollViewer" 
                                                          VerticalScrollBarVisibility="Auto"
                                                          Style="{StaticResource WPFScrollViewerStyle}" 
                                                          CanContentScroll="True">
                                            <ListView x:Name="Part_SymbolHeaders"
                                                          HorizontalAlignment="Stretch"
                                                          VerticalAlignment="Stretch">
                                                <ListView.Style>
                                                    <Style TargetType="{x:Type ListView}" BasedOn="{StaticResource WPFListViewStyle}">
                                                        <Setter Property="ItemContainerStyle">
                                                            <Setter.Value>
                                                                <Style TargetType="{x:Type ListViewItem}" BasedOn="{StaticResource WPFListViewItemStyle}">
                                                                    <Setter Property="Height" Value="32"/>
                                                                    <Setter Property="Content" Value="{Binding Path=Header}"/>
                                                                    <Setter Property="ContentTemplate" Value="{Binding Path=HeaderTemplate}"/>
                                                                    <Setter Property="Template" Value="{StaticResource SymbolGroupListHeaderTemplate}"/>
                                                                </Style>
                                                            </Setter.Value>
                                                        </Setter>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Path=SymbolFilters, RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource NullToBooleanConverter}}" Value="True">
                                                                <Setter Property="SelectedItem" Value="{Binding Path=SelectedFilter, RelativeSource={RelativeSource TemplatedParent}, Mode=TwoWay}"/>
                                                                <Setter Property="ItemContainerStyle">
                                                                    <Setter.Value>
                                                                        <Style TargetType="{x:Type ListViewItem}" BasedOn="{StaticResource WPFListViewItemStyle}">
                                                                            <Setter Property="Height" Value="32"/>
                                                                            <Setter Property="Content" Value="{Binding Path=Content}"/>
                                                                            <Setter Property="ContentTemplate" Value="{Binding Path=ContentTemplate}"/>
                                                                            <Setter Property="Template" Value="{StaticResource SymbolGroupListHeaderTemplate}"/>
                                                                        </Style>
                                                                    </Setter.Value>
                                                                </Setter>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </ListView.Style>
                                            </ListView>
                                        </ScrollViewer>
                                    </Grid>
                                    <Grid.Style>
                                        <Style TargetType="{x:Type Grid}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Path=SymbolGroupDisplayMode, RelativeSource={RelativeSource TemplatedParent}}" Value="List">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Grid.Style>
                                </Grid>
                                <GridSplitter x:Name="Part_GridSplitter" 
                                              Grid.Row="2" 
                                              Grid.Column="0" 
                                              Grid.ColumnSpan="2"
                                              Height="1.5" 
                                              ResizeBehavior="PreviousAndNext"
                                              HorizontalAlignment="Stretch">
                                    <GridSplitter.Margin>
                                        <Thickness>0,2,4,2</Thickness>
                                    </GridSplitter.Margin>
                                    <GridSplitter.Style>
                                        <Style TargetType="{x:Type GridSplitter}" BasedOn="{StaticResource WPFGridSplitterStyle}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Path=SymbolGroupDisplayMode, RelativeSource={RelativeSource TemplatedParent}}" Value="List">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </GridSplitter.Style>
                                </GridSplitter>
                                <Grid Grid.Row="3" 
                                      Grid.Column="0"
                                      Width="32"
                                      x:Name="Part_TabHeaderGrid">
                                    <Border BorderThickness="{StaticResource Windows11Light.BorderThickness1101}"
                                            Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}">
                                        <Grid VerticalAlignment="Top">
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="auto"/>
                                                <RowDefinition Height="*"/>
                                                <RowDefinition Height="auto"/>
                                                <RowDefinition Height="auto"/>
                                            </Grid.RowDefinitions>
                                            <Button Grid.Row="0" 
                                                    Margin="2" 
                                                    x:Name="Part_TabHeaderNavUpButton" 
                                                    Width="24" 
                                                    Height="24"                                                     
                                                    HorizontalAlignment="Center" 
                                                    VerticalAlignment="Center" 
                                                    Visibility="Collapsed"
                                                    Style="{StaticResource WPFGlyphButtonStyle}">
                                                <Button.Content>
                                                    <Path Width="6"
                                                          Height="10"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Stretch="Uniform"
                                                          Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                          StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}">
                                                        <Path.Data>
                                                            <PathGeometry>M1.5 9.00195C1.42969 9.00195 1.36523 8.98828 1.30664 8.96094C1.24414 8.93359 1.19141 8.89844 1.14844 8.85547C1.10156 8.80859 1.06445 8.75586 1.03711 8.69727C1.00977 8.63477 0.996094 8.56836 0.996094 8.49805C0.996094 8.42383 1.00977 8.35938 1.03711 8.30469C1.06055 8.25 1.09766 8.19727 1.14844 8.14648L4.29492 5L1.14844 1.85352C1.04688 1.75195 0.996094 1.63477 0.996094 1.50195C0.996094 1.43164 1.00977 1.36719 1.03711 1.30859C1.06445 1.24609 1.10156 1.19336 1.14844 1.15039C1.19141 1.10352 1.24414 1.06641 1.30664 1.03906C1.36523 1.01172 1.42969 0.998047 1.5 0.998047C1.63672 0.998047 1.75391 1.04688 1.85156 1.14453L5.35547 4.64844C5.45313 4.74609 5.50195 4.86328 5.50195 5C5.50195 5.13672 5.45313 5.25391 5.35547 5.35156L1.85156 8.85547C1.75391 8.95312 1.63672 9.00195 1.5 9.00195Z</PathGeometry>
                                                        </Path.Data>
                                                        <Path.RenderTransform>
                                                            <RotateTransform Angle="270" />
                                                        </Path.RenderTransform>
                                                        <Path.Margin>
                                                            <Thickness Left="0" Top="0" Right="4" Bottom="-14"/>
                                                        </Path.Margin>
                                                    </Path>
                                                </Button.Content>
                                            </Button>
                                            <ScrollViewer x:Name="Part_TabHeaderScrollViewer"
                                                          Grid.Row="1" 
                                                          Style="{StaticResource WPFScrollViewerStyle}" 
                                                          VerticalScrollBarVisibility="Hidden"
                                                          CanContentScroll="True">
                                                <ListView x:Name="Part_TabHeaderListView"
                                                          HorizontalAlignment="Stretch" 
                                                          VerticalAlignment="Stretch">
                                                    <ListView.Style>
                                                        <Style TargetType="{x:Type ListView}" BasedOn="{StaticResource WPFListViewStyle}">
                                                            <Setter Property="ItemContainerStyle">
                                                                <Setter.Value>
                                                                    <Style TargetType="{x:Type ListViewItem}" BasedOn="{StaticResource WPFListViewItemStyle}">
                                                                        <Setter Property="Content" Value="{Binding Path=Header}" />
                                                                        <Setter Property="ContentTemplate" Value="{Binding Path=HeaderTemplate}" />
                                                                        <Setter Property="Width" Value="28"/>
                                                                        <Setter Property="Padding">
                                                                            <Setter.Value>
                                                                                <Thickness>4,4,0,4</Thickness>
                                                                            </Setter.Value>
                                                                        </Setter>
                                                                        <Style.Triggers>
                                                                            <DataTrigger Binding="{Binding Path=HeaderTemplate, Converter={StaticResource NullToBooleanConverter}}" Value="False">
                                                                                <Setter Property="ContentTemplate" Value="{StaticResource DefaultTabHeaderTemplate}"/>
                                                                                <Setter Property="Padding">
                                                                                    <Setter.Value>
                                                                                        <Thickness>4,4,0,4</Thickness>
                                                                                    </Setter.Value>
                                                                                </Setter>
                                                                            </DataTrigger>
                                                                            <Trigger Property="GridView.ColumnCollection" Value="{x:Null}">
                                                                                <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                                                                                <Setter Property="Template" Value="{StaticResource StencilGroupTabHeaderTemplate}"/>
                                                                                <Setter Property="Padding">
                                                                                    <Setter.Value>
                                                                                        <Thickness>4,4,0,4</Thickness>
                                                                                    </Setter.Value>
                                                                                </Setter>
                                                                            </Trigger>
                                                                            <Trigger Property="IsSelected" Value="True">
                                                                                <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                                                                            </Trigger>
                                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                                <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                                                                            </Trigger>
                                                                        </Style.Triggers>                                                                        
                                                                    </Style>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Style>
                                                    </ListView.Style>
                                                </ListView>
                                            </ScrollViewer>
                                            <Button Grid.Row="2" 
                                                    Margin="0,2" 
                                                    x:Name="Part_TabHeaderNavDownButton"
                                                    Width="24" 
                                                    Height="24" 
                                                    HorizontalAlignment="Center" 
                                                    VerticalAlignment="Center"
                                                    Visibility="Collapsed" 
                                                    Style="{StaticResource WPFGlyphButtonStyle}">
                                                <Button.Content>
                                                    <Path Width="6"
                                                          Height="10"
                                                          HorizontalAlignment="Right"
                                                          VerticalAlignment="Center"
                                                          Stretch="Uniform"
                                                          Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                          StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}">
                                                        <Path.Data>
                                                            <PathGeometry>M1.5 9.00195C1.42969 9.00195 1.36523 8.98828 1.30664 8.96094C1.24414 8.93359 1.19141 8.89844 1.14844 8.85547C1.10156 8.80859 1.06445 8.75586 1.03711 8.69727C1.00977 8.63477 0.996094 8.56836 0.996094 8.49805C0.996094 8.42383 1.00977 8.35938 1.03711 8.30469C1.06055 8.25 1.09766 8.19727 1.14844 8.14648L4.29492 5L1.14844 1.85352C1.04688 1.75195 0.996094 1.63477 0.996094 1.50195C0.996094 1.43164 1.00977 1.36719 1.03711 1.30859C1.06445 1.24609 1.10156 1.19336 1.14844 1.15039C1.19141 1.10352 1.24414 1.06641 1.30664 1.03906C1.36523 1.01172 1.42969 0.998047 1.5 0.998047C1.63672 0.998047 1.75391 1.04688 1.85156 1.14453L5.35547 4.64844C5.45313 4.74609 5.50195 4.86328 5.50195 5C5.50195 5.13672 5.45313 5.25391 5.35547 5.35156L1.85156 8.85547C1.75391 8.95312 1.63672 9.00195 1.5 9.00195Z</PathGeometry>
                                                        </Path.Data>
                                                        <Path.RenderTransform>
                                                            <RotateTransform Angle="90" />
                                                        </Path.RenderTransform>
                                                        <Path.Margin>
                                                            <Thickness Left="14" Top="8" Right="0" Bottom="0"/>
                                                        </Path.Margin>
                                                    </Path>
                                                </Button.Content>
                                            </Button>
                                            <ToggleButton x:Name="Part_TabHeaderAddSymbolGroup"
                                                          Grid.Row="3" 
                                                          Margin="0,2,0,100" 
                                                          Width="24" 
                                                          Height="24" 
                                                          HorizontalAlignment="Center" 
                                                          VerticalAlignment="Center">
                                                <ToggleButton.Content>
                                                    <Path Width="12" 
                                                          Height="12" 
                                                          Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                          Data="M10.5 5.625C10.5 5.72656 10.4629 5.81445 10.3887 5.88867C10.3145 5.96289 10.2266 6 10.125 6H6V10.125C6 10.2266 5.96289 10.3145 5.88867 10.3887C5.81445 10.4629 5.72656 10.5 5.625 10.5C5.52344 10.5 5.43555 10.4629 5.36133 10.3887C5.28711 10.3145 5.25 10.2266 5.25 10.125V6H1.125C1.02344 6 0.935547 5.96289 0.861328 5.88867C0.787109 5.81445 0.75 5.72656 0.75 5.625C0.75 5.52344 0.787109 5.43555 0.861328 5.36133C0.935547 5.28711 1.02344 5.25 1.125 5.25H5.25V1.125C5.25 1.02344 5.28711 0.935547 5.36133 0.861328C5.43555 0.787109 5.52344 0.75 5.625 0.75C5.72656 0.75 5.81445 0.787109 5.88867 0.861328C5.96289 0.935547 6 1.02344 6 1.125V5.25H10.125C10.2266 5.25 10.3145 5.28711 10.3887 5.36133C10.4629 5.43555 10.5 5.52344 10.5 5.625Z"/>
                                                </ToggleButton.Content>
                                                <ToggleButton.ContextMenu>
                                                    <ContextMenu x:Name="Part_TabHeaderAddSymbolGroupMenu" Style="{StaticResource WPFContextMenuStyle}">
                                                        <ContextMenu.ItemContainerStyle>
                                                            <Style TargetType="MenuItem" BasedOn="{StaticResource WPFMenuItemStyle}">
                                                                <Setter Property="Height" Value="32"></Setter>
                                                                <Setter Property="StaysOpenOnClick" Value="True"></Setter>
                                                                <Setter Property="Header" Value="{Binding Header}"/>
                                                                <!--<Setter Property="Foreground" Value="{StaticResource SymbolFilterTextblockForegroundBrush}"/>-->
                                                                <Setter Property="IsChecked" Value="{Binding HeaderVisibility, Converter={StaticResource VisibilityToBoolConverter}, Mode=TwoWay}"/>
                                                                <Setter Property="IsCheckable" Value="True"/>
                                                            </Style>
                                                        </ContextMenu.ItemContainerStyle>
                                                    </ContextMenu>
                                                </ToggleButton.ContextMenu>
                                            </ToggleButton>
                                        </Grid>
                                    </Border>
                                    <Grid.Style>
                                        <Style TargetType="{x:Type Grid}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Path=SymbolGroupDisplayMode, RelativeSource={RelativeSource TemplatedParent}}" Value="Tab">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Grid.Style>
                                </Grid>
                                <ScrollViewer Grid.Row="3"
                                                  Grid.Column="1"
                                                  VerticalScrollBarVisibility="Auto"
                                                  Style="{StaticResource WPFScrollViewerStyle}"
                                                  CanContentScroll="True">
                                    <diagram_stencil:SymbolGroup ItemsSource="{TemplateBinding SymbolGroup}">
                                        <diagram_stencil:SymbolGroup.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <StackPanel/>
                                            </ItemsPanelTemplate>
                                        </diagram_stencil:SymbolGroup.ItemsPanel>
                                        <diagram_stencil:SymbolGroup.HeaderTemplate>
                                            <DataTemplate/>
                                        </diagram_stencil:SymbolGroup.HeaderTemplate>
                                        <diagram_stencil:SymbolGroup.Template>
                                            <ControlTemplate TargetType="diagram_stencil:SymbolGroup">
                                                <Border Background="{TemplateBinding Background}"
                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                            BorderThickness="{TemplateBinding BorderThickness}">
                                                    <Grid>
                                                        <ItemsPresenter />
                                                        <Canvas>
                                                            <ContentPresenter x:Name="PART_DragPreview" 
                                                                              HorizontalAlignment="Left"
                                                                              VerticalAlignment="Top"  
                                                                              Opacity="0.5"
                                                                              Visibility="Collapsed"
                                                                              IsHitTestVisible="False"/>
                                                            <Rectangle x:Name="PART_ReorderIndicator"
                                                                       HorizontalAlignment="Left" 
                                                                       VerticalAlignment="Top" 
                                                                       Visibility="Collapsed" 
                                                                       IsHitTestVisible="False"
                                                                       Fill="{StaticResource BorderAlt3}" 
                                                                       Stroke="{StaticResource BorderAlt3}"/>
                                                            <Rectangle x:Name="PART_SelectionRectangle" 
                                                                       HorizontalAlignment="Left" 
                                                                       VerticalAlignment="Top"
                                                                       Visibility="Collapsed"
                                                                       Fill="Transparent"
                                                                       Stroke="{StaticResource BorderAlt3}"/>
                                                        </Canvas>
                                                    </Grid>
                                                </Border>
                                            </ControlTemplate>
                                        </diagram_stencil:SymbolGroup.Template>
                                    </diagram_stencil:SymbolGroup>
                                </ScrollViewer>
                            </Grid>
                            <Grid x:Name="Part_CollapsedGrid"
                                      Grid.Row="2"
                                      Background="{TemplateBinding Background}"
                                      Visibility="{Binding ElementName=expander,Path=IsExpanded,Converter={StaticResource InverseBoolToVisibilityConverter}}">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid Background="{TemplateBinding Background}" Visibility="{Binding ElementName=expander,Path=IsExpanded,Converter={StaticResource InverseBoolToVisibilityConverter}}">
                                    <ToggleButton x:Name="Part_CollapsedShape"
                                                  Height="30">
                                        <ToggleButton.Content>
                                            <Grid >
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="20"/>
                                                </Grid.ColumnDefinitions>
                                                <Grid         Width="16"
                                                              Height="16"
                                                              HorizontalAlignment="Right"
                                                              VerticalAlignment="Center">
                                                    <Path HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                                          Data="M1 1H4V4H1V1ZM0 0H1H4H5V1V4V5H4H1H0V4V1V0ZM1 7H4V10H1V7ZM0 6H1H4H5V7V10V11H4H1H0V10V7V6ZM10 1H7V4H10V1ZM7 0H6V1V4V5H7H10H11V4V1V0H10H7ZM7 7H10V10H7V7ZM6 6H7H10H11V7V10V11H10H7H6V10V7V6Z"/>
                                                </Grid>
                                                <Path         Grid.Column="1"
                                                              Width="5"
                                                              Height="7" 
                                                              Stretch="Fill"
                                                              Margin="0,0,5,0"
                                                              HorizontalAlignment="Right"
                                                              Data="M0,0 L4,3.500001 0,7.0000019 z" 
                                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"/>
                                            </Grid>
                                        </ToggleButton.Content>
                                        <ToggleButton.ContextMenu>
                                            <ContextMenu x:Name="Part_CollapsedContextMenu"
                                                             Style="{StaticResource WPFContextMenuStyle}">
                                                <MenuItem Header="More Shapes">
                                                    <MenuItem.ItemContainerStyle>
                                                        <Style TargetType="MenuItem" BasedOn="{StaticResource WPFMenuItemStyle}">
                                                            <Style.Setters>
                                                                <Setter Property="StaysOpenOnClick" Value="True"></Setter>
                                                                <Setter Property="Height" Value="32"/>
                                                                <Setter Property="Header" Value=""/>
                                                            </Style.Setters>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding Path=SymbolFilters, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=diagram_stencil:Stencil},Converter={StaticResource NullToBooleanConverter}}" Value="False">
                                                                    <DataTrigger.Setters>
                                                                        <Setter Property="Header" Value="{Binding Header}"/>
                                                                        <Setter Property="IsChecked" Value="{Binding HeaderVisibility, Converter={StaticResource VisibilityToBoolConverter}, Mode=TwoWay}"/>
                                                                        <Setter Property="IsCheckable" Value="True"/>
                                                                    </DataTrigger.Setters>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding Path=SymbolFilters, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=diagram_stencil:Stencil},Converter={StaticResource NullToBooleanConverter}}" Value="True">
                                                                    <Setter Property="Header" Value="{Binding Content}"/>
                                                                    <Setter Property="IsChecked" Value="{Binding IsChecked,Mode=TwoWay}"/>
                                                                    <Setter Property="Command" Value="{Binding Command}"/>
                                                                    <Setter Property="CommandParameter" Value="{Binding RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </MenuItem.ItemContainerStyle>
                                                </MenuItem>
                                            </ContextMenu>
                                        </ToggleButton.ContextMenu>
                                        <ToggleButton.Style>
                                            <Style TargetType="{x:Type ToggleButton}" BasedOn="{StaticResource WPFToggleButtonStyle}">
                                                <Setter Property="Visibility" Value="Hidden" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Path=SymbolGroupDisplayMode, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}" Value="List">
                                                        <Setter Property="Visibility" Value="Visible" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Path=SymbolGroupDisplayMode, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}" Value="Tab">
                                                        <Setter Property="Visibility" Value="Visible" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ToggleButton.Style>
                                    </ToggleButton>
                                    <ToggleButton x:Name="Part_AccordionShape"
                                                      Height="30">
                                        <ToggleButton.Content>
                                            <Grid  Background="{TemplateBinding Background}">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="20"/>
                                                </Grid.ColumnDefinitions>
                                                <Grid 
                                                              HorizontalAlignment="Right"
                                                              VerticalAlignment="Center"
                                                              Height="16"
                                                              Width="16"
                                                              Background="{TemplateBinding Background}">
                                                    <Path HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center"
                                                                  Data="M1 1H4V4H1V1ZM0 0H1H4H5V1V4V5H4H1H0V4V1V0ZM1 7H4V10H1V7ZM0 6H1H4H5V7V10V11H4H1H0V10V7V6ZM10 1H7V4H10V1ZM7 0H6V1V4V5H7H10H11V4V1V0H10H7ZM7 7H10V10H7V7ZM6 6H7H10H11V7V10V11H10H7H6V10V7V6Z" 
                                                                  Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"/>
                                                </Grid>
                                                <Path        Grid.Column="1"
                                                              Width="5" 
                                                              Height="7"
                                                              Stretch="Fill"
                                                              Margin="0,0,5,0"
                                                              HorizontalAlignment="Right"
                                                              Data="M0,0 L4,3.500001 0,7.0000019 z" 
                                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"/>
                                            </Grid>
                                        </ToggleButton.Content>
                                        <ToggleButton.Style>
                                            <Style TargetType="{x:Type ToggleButton}" BasedOn="{StaticResource WPFToggleButtonStyle}">
                                                <Setter Property="Visibility" Value="Hidden" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Path=SymbolGroupDisplayMode, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}" Value="Accordion">
                                                        <Setter Property="Visibility" Value="Visible" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ToggleButton.Style>
                                        <ToggleButton.ContextMenu>
                                            <ContextMenu x:Name="Part_AccordionContextMenu"
                                                             Style="{StaticResource WPFContextMenuStyle}">
                                                <ContextMenu.ItemContainerStyle>
                                                    <Style TargetType="MenuItem" BasedOn="{StaticResource WPFMenuItemStyle}">
                                                        <Setter Property="Height" Value="32"></Setter>
                                                        <Setter Property="StaysOpenOnClick" Value="True"></Setter>
                                                        <Setter Property="Header" Value="{Binding Header}"/>
                                                        <Setter Property="IsChecked" Value="{Binding IsExpanded, Mode=OneWay}"/>
                                                        <Setter Property="IsCheckable" Value="True"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Path=SymbolFilters, RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource NullToBooleanConverter}}" Value="True">
                                                                <Setter Property="ItemContainerStyle">
                                                                    <Setter.Value>
                                                                        <Style TargetType="MenuItem" BasedOn="{StaticResource WPFMenuItemStyle}">
                                                                            <Setter Property="Height" Value="32"/>
                                                                            <Setter Property="StaysOpenOnClick" Value="True"/>
                                                                            <Setter Property="Header" Value="{Binding Content}"/>
                                                                            <Setter Property="IsCheckable" Value="True"/>
                                                                        </Style>
                                                                    </Setter.Value>
                                                                </Setter>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </ContextMenu.ItemContainerStyle>
                                            </ContextMenu>
                                        </ToggleButton.ContextMenu>
                                    </ToggleButton>
                                </Grid>
                                <RepeatButton x:Name="UpButton"
                                              Grid.Row="1" 
                                              Height="15"
                                              Style="{StaticResource WPFRepeatButtonStyle}">
                                    <Path HorizontalAlignment="Center" 
                                          VerticalAlignment="Center" 
                                          Data="M 0 4 L 8 4 L 4 0 Z"
                                          Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(RepeatButton.Foreground)}" />
                                </RepeatButton>
                                <ScrollViewer x:Name="Part_CompactMode_Scrollbar" 
                                                  Grid.Row="2"
                                                  CanContentScroll="True"
                                                  VerticalScrollBarVisibility="Hidden"
                                                  HorizontalScrollBarVisibility="Disabled"
                                                  Style="{StaticResource WPFScrollViewerStyle}">
                                    <diagram_stencil:SymbolGroup ItemsSource="{TemplateBinding SymbolGroup}">
                                        <diagram_stencil:SymbolGroup.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <StackPanel x:Name="PartStackPanel"/>
                                            </ItemsPanelTemplate>
                                        </diagram_stencil:SymbolGroup.ItemsPanel>
                                        <diagram_stencil:SymbolGroup.Template>
                                            <ControlTemplate TargetType="diagram_stencil:SymbolGroup">
                                                <Border Background="{TemplateBinding Background}"
                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                            BorderThickness="{TemplateBinding BorderThickness}">
                                                    <Grid>
                                                        <ItemsPresenter />
                                                        <Canvas>
                                                            <ContentPresenter x:Name="PART_DragPreview"
                                                                              HorizontalAlignment="Left"
                                                                              VerticalAlignment="Top" 
                                                                              Opacity="0.5"
                                                                              Visibility="Collapsed" 
                                                                              IsHitTestVisible="False"/>
                                                            <Rectangle x:Name="PART_ReorderIndicator" 
                                                                       HorizontalAlignment="Left"
                                                                       VerticalAlignment="Top" 
                                                                       Visibility="Collapsed"
                                                                       IsHitTestVisible="False"
                                                                       Fill="{StaticResource BorderAlt3}" 
                                                                       Stroke="{StaticResource BorderAlt3}"/>
                                                            <Rectangle x:Name="PART_SelectionRectangle" 
                                                                       HorizontalAlignment="Left" 
                                                                       VerticalAlignment="Top"
                                                                       Visibility="Collapsed"
                                                                       Fill="Transparent"
                                                                       Stroke="{StaticResource BorderAlt3}" />
                                                        </Canvas>
                                                    </Grid>
                                                </Border>
                                            </ControlTemplate>
                                        </diagram_stencil:SymbolGroup.Template>
                                    </diagram_stencil:SymbolGroup>
                                </ScrollViewer>
                                <RepeatButton x:Name="DownButton"
                                              Grid.Row="3" 
                                              Height="15"
                                              Style="{StaticResource WPFRepeatButtonStyle}">
                                    <Path Data="M 0 0 L 4 4 L 8 0 Z"
                                          HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"
                                          Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(RepeatButton.Foreground)}"/>
                                </RepeatButton>
                            </Grid>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding IsExpanded, ElementName=expander}" Value="False">
                            <Setter Property="Visibility" Value="Visible" TargetName="Part_CollapsedGrid"/>
                            <Setter Property="Visibility" Value="Collapsed" TargetName="ContentPresenter"/>
                        </DataTrigger>
                        <Trigger Property="Content" Value="{x:Null}" SourceName="ContentPresenter">
                            <Setter Property="Visibility" Value="Collapsed" TargetName="ContentPresenter"/>
                        </Trigger>
                        <Trigger Property="Content" Value="" SourceName="ContentPresenter">
                            <Setter Property="Visibility" Value="Collapsed" TargetName="ContentPresenter"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition  Property="Visibility" Value="Collapsed" SourceName="ContentPresenter"/>
                                <Condition  Property="ShowDisplayModeToggleButton" Value="False"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Visibility" Value="Collapsed" TargetName="Part_ExpanderGrid"></Setter>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>

                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="diagram_stencil:Stencil" BasedOn="{StaticResource SyncfusionStencilStyle}"/>

    <Style TargetType="diagram_stencil:Symbol" x:Key="SyncfusionSymbolStyle">
        <Setter Property="BorderThickness"
                Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="Background"
                Value="{StaticResource ContentBackground}"/>
        <Setter Property="Height" 
                Value="48"/>
        <Setter Property="Width"
                Value="48"/>
        <Setter Property="Padding"
                Value="4"/>
        <Setter Property="Margin"
                Value="1.5"/>
        <Setter Property="ToolTip"
                Value="{Binding Name}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_stencil:Symbol">
                    <Grid Background="Transparent">
                        <Border x:Name="symbolborder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="{TemplateBinding Padding}"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                Margin="1">
                            <ContentPresenter Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"/>
                        </Border>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="PointerOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="symbolborder"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Selected">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="symbolborder"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" 
                                                                    Value="{StaticResource ContentBackgroundSelected}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="symbolborder"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" 
                                                                    Value="{StaticResource ContentBackgroundSelected}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Name}" Value="">
                <Setter Property="ToolTip" Value="{x:Null}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=SymbolsDisplayMode, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}" Value="NamesUnderIcons">
                <Setter Property="ToolTip" Value="{x:Null}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="diagram_stencil:Symbol">
                            <Grid Background="Transparent">
                                <Border x:Name="symbolborder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="{TemplateBinding Padding}"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                Margin="1">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition x:Name="Part_RowIcon" Height="4*"/>
                                            <RowDefinition x:Name="Part_RowName" Height="6*"/>
                                        </Grid.RowDefinitions>
                                        <ContentPresenter Content="{TemplateBinding Content}" ContentTemplate="{TemplateBinding ContentTemplate}" Grid.Row="0"/>
                                        <TextBox x:Name="Part_NameTextBox" 
                                                 Grid.Row="1" 
                                                 Margin="1" 
                                                 Text="{Binding Name}" 
                                                 Cursor="Arrow"
                                                 Focusable="False"
                                                 IsReadOnly="True"
                                                 HorizontalAlignment="Center" 
                                                 VerticalAlignment="Center" 
                                                 TextWrapping="WrapWithOverflow" 
                                                 VerticalScrollBarVisibility="Disabled">
                                            <TextBox.Style>
                                                <Style TargetType="TextBox" BasedOn="{StaticResource WPFTextBoxStyle}">
                                                    <Setter Property="BorderThickness" Value="0"/>
                                                    <Setter Property="Background" Value="Transparent"/>
                                                    <Style.Triggers>
                                                        <Trigger Property="Focusable" Value="False">
                                                            <Setter Property="Template">
                                                                <Setter.Value>
                                                                    <ControlTemplate TargetType="TextBox">
                                                                        <TextBlock Text="{Binding Name}" 
                                                                                   TextWrapping="WrapWithOverflow"
                                                                                   Cursor="Arrow"
                                                                                   Focusable="False"
                                                                                   Background="Transparent" 
                                                                                   TextTrimming="CharacterEllipsis"
                                                                                   VerticalAlignment="Center" 
                                                                                   HorizontalAlignment="Center"
                                                                                   Style="{StaticResource WPFTextBlockStyle}"/>
                                                                    </ControlTemplate>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Trigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBox.Style>
                                        </TextBox>
                                    </Grid>
                                </Border>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="PointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="symbolborder"
                                                                       Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Selected">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="symbolborder"
                                                                       Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" 
                                                                    Value="{StaticResource ContentBackgroundSelected}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="symbolborder"
                                                                       Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" 
                                                                    Value="{StaticResource ContentBackgroundSelected}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="diagram_stencil:Symbol" BasedOn="{StaticResource SyncfusionSymbolStyle}"/>

    <Style TargetType="diagram_stencil:SymbolGroup" x:Key="SyncfusionSymbolGroupStyle">
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize" 
                Value="{StaticResource Windows11Light.SubHeaderTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="Background"
                Value="{StaticResource ContentBackground}"/>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <diagram_panels:WrapPanel/>
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="HeaderTemplate">
            <Setter.Value>
                <DataTemplate>
                    <diagram_stencil:Header>
                        <diagram_stencil:Header.Template>
                            <ControlTemplate TargetType="diagram_stencil:Header">
                                <Grid>
                                    <Border x:Name="header"
                                            Background="{StaticResource ContentBackground}" 
                                            BorderBrush="{StaticResource BorderAlt}"
                                            BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                            Margin="0">
                                        <TextBlock Margin="10"
                                                   Text="{Binding}"
                                                   Style="{StaticResource WPFTextBlockStyle}"/>
                                    </Border>
                                    <VisualStateManager.VisualStateGroups>
                                        <VisualStateGroup x:Name="CommonStates">
                                            <VisualState x:Name="Normal" />
                                            <VisualState x:Name="PointerOver">
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="header" 
                                                                                   Storyboard.TargetProperty="Background">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </VisualState>
                                            <VisualState x:Name="Expanded">
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="header" 
                                                                                   Storyboard.TargetProperty="Background">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackground}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </VisualState>
                                            <VisualState x:Name="Collapsed">
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="header" 
                                                                                   Storyboard.TargetProperty="Background">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackground}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </VisualState>
                                        </VisualStateGroup>
                                    </VisualStateManager.VisualStateGroups>
                                </Grid>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                                        <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </diagram_stencil:Header.Template>
                    </diagram_stencil:Header>
                </DataTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_stencil:SymbolGroup">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{StaticResource BorderAlt}"
                            BorderThickness="{StaticResource Windows11Light.BorderThickness}">
                        <StackPanel>
                            <Grid x:Name="rootgrid"
                                  Visibility="{Binding RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource NullToVisibityConverter}}">
                                <ContentPresenter x:Name="PART_Header"
                                                  Content="{TemplateBinding Header}"
                                                  ContentTemplate="{TemplateBinding HeaderTemplate}" />
                                <Border x:Name="borderpath"
                                        BorderBrush="{StaticResource BorderAlt}"
                                        BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                        Margin="0,0,0,2"
                                        HorizontalAlignment="Right">
                                    <Path x:Name="path1"
                                          IsHitTestVisible="False"
                                          HorizontalAlignment="Right"
                                          VerticalAlignment="Center" 
                                          Width="8" 
                                          Height="5"
                                          Margin="10,0,10,0"
                                          Data="M6.0010305,0 L12.001999,5.9840137 10.590014,7.3999998 6.0010305,2.8240075 1.4119857,7.3999998 0,5.9840137 z" 
                                          Stretch="Uniform" 
                                          Fill="{StaticResource IconColor}" 
                                          RenderTransformOrigin="0.5,0.5">
                                        <Path.RenderTransform>
                                            <TransformGroup>
                                                <ScaleTransform/>
                                                <SkewTransform/>
                                                <RotateTransform/>
                                                <TranslateTransform/>
                                            </TransformGroup>
                                        </Path.RenderTransform>
                                    </Path>
                                </Border>
                            </Grid>
                            <ItemsPresenter x:Name="symbolgroup"
                                            RenderTransformOrigin="0.5,0.5" 
                                            Visibility="{Binding IsExpanded, RelativeSource={RelativeSource TemplatedParent}, Mode=TwoWay,Converter={StaticResource VisibilityToBoolConverter}}"
                                            Margin="0">
                                <ItemsPresenter.RenderTransform>
                                    <TransformGroup>
                                        <ScaleTransform/>
                                        <SkewTransform/>
                                        <RotateTransform/>
                                        <TranslateTransform/>
                                    </TransformGroup>
                                </ItemsPresenter.RenderTransform>
                            </ItemsPresenter>
                        </StackPanel>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup>
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="Expanded">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="path1" 
                                                                       Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[2].(RotateTransform.Angle)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="180"/>
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="0"/>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Collapsed">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="path1"
                                                                       Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[2].(RotateTransform.Angle)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="180"/>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Path=DisplayMode, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}" Value="Compact">
                            <Setter Property="Visibility" Value="Collapsed" TargetName="rootgrid"></Setter>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=DisplayMode, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}" Value="Expanded">
                            <Setter Property="Margin" Value="0,0,0,0" TargetName="symbolgroup"></Setter>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=ExpandMode, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}" Value="All">
                            <Setter Property="Visibility" Value="Collapsed" TargetName="borderpath"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=SymbolGroupDisplayMode, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}" Value="List">
                            <Setter Property="Visibility" Value="Collapsed" TargetName="borderpath"/>
                            <Setter TargetName="PART_Header" Property="ContentTemplate">
                                <Setter.Value>
                                    <DataTemplate>
                                        <diagram_stencil:Header>
                                            <diagram_stencil:Header.Template>
                                                <ControlTemplate TargetType="diagram_stencil:Header">
                                                    <Grid>
                                                        <Border>
                                                            <TextBlock Margin="10" Text="{Binding}" Style="{StaticResource WPFTextBlockStyle}"/>
                                                        </Border>
                                                    </Grid>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                                                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </diagram_stencil:Header.Template>
                                        </diagram_stencil:Header>
                                    </DataTemplate>
                                </Setter.Value>
                            </Setter>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=SymbolGroupDisplayMode, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}" Value="Tab">
                            <Setter TargetName="borderpath" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="PART_Header" Property="ContentTemplate">
                                <Setter.Value>
                                    <DataTemplate>
                                        <diagram_stencil:Header>
                                            <diagram_stencil:Header.Template>
                                                <ControlTemplate TargetType="diagram_stencil:Header">
                                                    <Grid>
                                                        <Border>
                                                            <TextBlock Margin="10" Text="{Binding}" Style="{StaticResource WPFTextBlockStyle}"/>
                                                        </Border>
                                                    </Grid>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                                                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </diagram_stencil:Header.Template>
                                        </diagram_stencil:Header>
                                    </DataTemplate>
                                </Setter.Value>
                            </Setter>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Path=SymbolGroupDisplayMode, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}" Value="List">
                <Setter Property="HeaderTemplate" Value="{x:Null}"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=SymbolGroupDisplayMode, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=diagram_stencil:Stencil}}" Value="Tab">
                <Setter Property="HeaderTemplate">
                    <Setter.Value>
                        <DataTemplate>
                            <Grid RenderTransformOrigin="0.5,0.5">
                                <TextBlock Text="{Binding}" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock.LayoutTransform>
                                        <RotateTransform Angle="270"/>
                                    </TextBlock.LayoutTransform>
                                </TextBlock>
                            </Grid>
                        </DataTemplate>
                    </Setter.Value>
                </Setter>
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="diagram_stencil:SymbolGroup" BasedOn="{StaticResource SyncfusionSymbolGroupStyle}"/>
</ResourceDictionary>
