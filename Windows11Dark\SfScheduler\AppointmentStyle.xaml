<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib"
                    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:scheduler="clr-namespace:Syncfusion.UI.Xaml.Scheduler;assembly=Syncfusion.SfScheduler.WPF">
    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ToolTip.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <DataTemplate x:Key="ToolTipTemplate">
        <Border x:Name="PART_ToolTipBorder"
                BorderBrush="{Binding BorderBrush}"
                Background="{Binding Background}"
                CornerRadius="{Binding CornerRadius}"
                VerticalAlignment="Stretch"
                HorizontalAlignment="Stretch"
                BorderThickness="{Binding BorderThickness}"
                Padding="5">
            <StackPanel>
                <TextBlock x:Name="PART_AppointmentTextBlock"
                           Text="{Binding Subject}"
                           FontSize="{Binding FontSize }"
                           FontWeight="{Binding FontWeight}"
                           FontFamily="{Binding FontFamily}"
                           TextWrapping="Wrap"
                           TextTrimming="CharacterEllipsis" />
                <TextBlock  FontSize="{Binding FontSize}"
                            FontWeight="{Binding FontWeight }"
                            FontFamily="{Binding FontFamily}"
                            TextWrapping="Wrap"
                            Margin="0,3,0,0"
                            TextTrimming="CharacterEllipsis">
                    <TextBlock.Text>
                        <MultiBinding StringFormat="{}{0:MMM dd} - {1:MMM dd yyyy}">
                            <Binding Path="StartTime" />
                            <Binding Path="EndTime" />
                        </MultiBinding>
                    </TextBlock.Text>
                </TextBlock>
            </StackPanel>
        </Border>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=Subject}" Value="{x:Static system:String.Empty}">
                <Setter TargetName="PART_AppointmentTextBlock" Property="TextBlock.Text" Value="{scheduler:SchedulerLocalizationResourceExtension ResourceName=EmptyAppointmentSubject}"/>
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>
    
    <Style x:Key="SyncfusionAppointmentControlStyle" TargetType="scheduler:AppointmentControl">
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="Background" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="BorderThickness" Value="1.5" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:AppointmentControl">
                    <Grid>
                        <Border
                            x:Name="PART_ResizeBorder">
                            <ContentPresenter
                                DataContext="{TemplateBinding DataContext}"
                                Content="{TemplateBinding DataContext}"
                                ContentTemplate="{TemplateBinding ContentTemplate}"
                                TextBlock.FontFamily="{TemplateBinding FontFamily}"
                                TextBlock.FontSize="{TemplateBinding FontSize}"
                                TextBlock.FontWeight="{TemplateBinding FontWeight}"
                                TextBlock.Foreground="{TemplateBinding Foreground}"
                                x:Name="PART_AppointmentContentPresenter"
                                Margin="{TemplateBinding Padding}" />
                        </Border>
                        <Border x:Name="PART_SelectionBorder" 
                                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                        </Border>
                        <Grid.ToolTip>
                            <ToolTip x:Name="PART_ToolTip" 
                                     ContentTemplate="{StaticResource ToolTipTemplate}"
                                     Style="{StaticResource WPFToolTipStyle}"/>
                        </Grid.ToolTip>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionAppointmentControlStyle}" TargetType="scheduler:AppointmentControl" />

    <DataTemplate x:Key="SyncfusionMoreAppointmentsIndicatorContentTemplate">
        <Border VerticalAlignment="Stretch" 
                HorizontalAlignment="Stretch" 
                Padding="2,0,0,0" 
                Background="Transparent">
            <TextBlock 
                Text="{Binding StringFormat=+{0}}"
                TextAlignment="Left"
                FontSize="{StaticResource Windows11Dark.CaptionText}" 
                FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                VerticalAlignment="Center"
                TextTrimming="CharacterEllipsis"/>
        </Border>
    </DataTemplate>

    <Style x:Key="SyncfusionAppointmentsCountControlStyle" TargetType="scheduler:AppointmentsCountControl">
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Cursor" Value="Hand"/>
            </Trigger>
        </Style.Triggers>
        <Setter Property="ContentTemplate" Value="{StaticResource SyncfusionMoreAppointmentsIndicatorContentTemplate}"/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.CaptionText}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:AppointmentsCountControl">
                    <Border 
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Background="{TemplateBinding Background}">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="SelectionStates">
                                <VisualState x:Name="Unselected" />
                                <VisualState x:Name="Selected">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource SelectedForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState Name="MouseOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ContentPresenter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource HoveredForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <ContentPresenter  x:Name="PART_ContentPresenter"
                                           Content="{TemplateBinding Content}"
                                           DataContext="{TemplateBinding DataContext}" 
                                           TextBlock.Foreground="{TemplateBinding Foreground}"
                                           ContentTemplate="{TemplateBinding ContentTemplate}" >
                        </ContentPresenter>
                    </Border>

                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionAppointmentsCountControlStyle}" TargetType="scheduler:AppointmentsCountControl" />

</ResourceDictionary>
