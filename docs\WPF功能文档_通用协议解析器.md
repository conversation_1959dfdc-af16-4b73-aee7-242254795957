# WPF功能文档：通用协议解析器

## 1. 功能概述
- **功能名称**：通用协议解析器
- **功能描述**：实现可配置的485协议数据解析，支持两种解析格式（固定字节解析、参数索引解析），通过配置文件定义解析规则
- **目标用户**：空调维护工程师和技术人员
- **优先级**：高
- **预估工作量**：7-10个工作日
- **依赖的WPF技术**：数据绑定、配置管理、JSON序列化、动态UI生成

## 2. 协议解析需求详解

### 2.1 支持的功能码
- **格式1（固定字节解析）**：A1、01
- **格式2（参数索引解析）**：A5、12、02、05

### 2.2 数据格式分析

#### 格式1示例（A1功能码）：
```
7E F1 00 A1 17 23 02 00 DC 00 E6 01 EA 01 03 00 01 17 15 01 00 01 76
```
- **固定结构**：头码+源地址+目标地址+功能码+数据长度+数据区+CRC16
- **解析方式**：每个字节位置有固定含义，需要按位解析或多字节组合

#### 格式2示例（12功能码）：
```
7E F1 F1 12 25 35 0E 03 B8 00 FD 00 F9 01 07 00 00 00 00 FF 9C FF 9C 00 04 00 04 00 00 00 00 00 00 00 00 4A D2
```
- **动态结构**：基于参数索引号(idx)和参数个数动态解析
- **解析方式**：查询参数表获取数据类型、单位、解析规则

### 2.3 配置化解析规则

#### 格式1配置示例：
```json
{
  "FunctionCode": "A1",
  "Format": "FixedByte",
  "Fields": [
    {
      "Name": "系统配置",
      "Position": 5,
      "Type": "BitField",
      "Bits": [
        {"Bit": 7, "Name": "内机过冷热度修正支持", "Values": {"0": "不支持", "1": "支持"}},
        {"Bit": 6, "Name": "自动地址设定", "Values": {"0": "不允许", "1": "允许"}},
        {"Bit": 5, "Name": "内机地址竞争方式", "Values": {"0": "手动", "1": "自动"}},
        {"Bit": 4, "Name": "ThermoOn转换禁止", "Values": {"0": "无", "1": "禁止"}},
        {"Bit": 3, "Name": "26度节能锁定", "Values": {"0": "无节能", "1": "节能锁定"}},
        {"Bits": "2-0", "Name": "系统模式优先级", "Values": {
          "0": "通常", "1": "单冷模式", "2": "单热模式", 
          "3": "冷优先", "4": "热优先", "5": "少数服从多数"
        }}
      ]
    },
    {
      "Name": "外机系统高压饱和温度",
      "Position": "8-9",
      "Type": "u16",
      "Scale": 0.1,
      "Unit": "°C"
    }
  ]
}
```

#### 格式2配置示例：
```json
{
  "FunctionCode": "12",
  "Format": "ParameterIndex",
  "Parameters": {
    "53": {
      "Name": "额定容量",
      "Type": "u16",
      "Unit": "百瓦"
    },
    "54": {
      "Name": "板换气进温度Tgi",
      "Type": "s16",
      "Scale": 0.1,
      "Unit": "°C"
    },
    "51": {
      "Name": "设备配置",
      "Type": "BitField",
      "Bits": [
        {"Bit": 0, "Name": "变频1", "Values": {"0": "无效", "1": "有效"}},
        {"Bit": 1, "Name": "定频1", "Values": {"0": "无效", "1": "有效"}},
        {"Bit": 2, "Name": "定频2", "Values": {"0": "无效", "1": "有效"}},
        {"Bit": 3, "Name": "电子膨胀阀1", "Values": {"0": "无效", "1": "有效"}},
        {"Bit": 4, "Name": "电子膨胀阀2", "Values": {"0": "无效", "1": "有效"}},
        {"Bit": 5, "Name": "电子膨胀阀3", "Values": {"0": "无效", "1": "有效"}},
        {"Bit": 6, "Name": "风机1", "Values": {"0": "无效", "1": "有效"}},
        {"Bit": 7, "Name": "风机2", "Values": {"0": "无效", "1": "有效"}},
        {"Bit": 8, "Name": "压缩机电加热1", "Values": {"0": "无效", "1": "有效"}},
        {"Bit": 9, "Name": "压缩机电加热2", "Values": {"0": "无效", "1": "有效"}},
        {"Bit": 10, "Name": "压缩机电加热3", "Values": {"0": "无效", "1": "有效"}},
        {"Bit": 11, "Name": "底盘电加热", "Values": {"0": "无效", "1": "有效"}},
        {"Bit": 12, "Name": "变频2", "Values": {"0": "无效", "1": "有效"}},
        {"Bits": "13-15", "Name": "预留", "Values": {}}
      ]
    }
  }
}
```

## 3. WPF架构设计

### 3.1 核心类设计
```csharp
// 协议解析器接口
public interface IProtocolParser
{
    ParsedData ParseFrame(byte[] frameData);
    bool LoadConfiguration(string configPath);
    List<FunctionCodeConfig> GetSupportedFunctionCodes();
}

// 解析结果数据模型
public class ParsedData
{
    public string FunctionCode { get; set; }
    public List<ParsedField> Fields { get; set; }
    public DateTime ParseTime { get; set; }
    public bool IsValid { get; set; }
    public string ErrorMessage { get; set; }
}

// 解析字段模型
public class ParsedField
{
    public string Name { get; set; }
    public object Value { get; set; }
    public string DisplayValue { get; set; }
    public string Unit { get; set; }
    public FieldType Type { get; set; }
    public List<ParsedBit> Bits { get; set; } // 位域解析结果
}

// 位域解析结果
public class ParsedBit
{
    public int BitPosition { get; set; }
    public string Name { get; set; }
    public bool Value { get; set; }
    public string DisplayValue { get; set; }
}
```

### 3.2 配置管理
```csharp
// 功能码配置
public class FunctionCodeConfig
{
    public string FunctionCode { get; set; }
    public ParseFormat Format { get; set; }
    public List<FieldConfig> Fields { get; set; } // 格式1使用
    public Dictionary<int, ParameterConfig> Parameters { get; set; } // 格式2使用
}

// 字段配置
public class FieldConfig
{
    public string Name { get; set; }
    public object Position { get; set; } // int或string（如"8-9"）
    public DataType Type { get; set; }
    public double Scale { get; set; } = 1.0;
    public string Unit { get; set; }
    public List<BitConfig> Bits { get; set; }
    public Dictionary<string, string> Values { get; set; }
}

// 参数配置（格式2）
public class ParameterConfig
{
    public string Name { get; set; }
    public DataType Type { get; set; }
    public double Scale { get; set; } = 1.0;
    public string Unit { get; set; }
    public List<BitConfig> Bits { get; set; }
}

// 位配置
public class BitConfig
{
    public object Bit { get; set; } // int或string（如"2-0"）
    public string Name { get; set; }
    public Dictionary<string, string> Values { get; set; }
}

// 枚举定义
public enum ParseFormat { FixedByte, ParameterIndex }
public enum DataType { u8, s8, u16, s16, u32, s32, BitField }
public enum FieldType { Numeric, BitField, Enum, String }
```

## 4. 解析器实现方案

### 4.1 格式1解析流程
```csharp
public class FixedByteParser
{
    public ParsedData Parse(byte[] data, FunctionCodeConfig config)
    {
        var result = new ParsedData
        {
            FunctionCode = config.FunctionCode,
            Fields = new List<ParsedField>()
        };

        foreach (var fieldConfig in config.Fields)
        {
            var field = ParseField(data, fieldConfig);
            result.Fields.Add(field);
        }

        return result;
    }

    private ParsedField ParseField(byte[] data, FieldConfig config)
    {
        var field = new ParsedField
        {
            Name = config.Name,
            Type = GetFieldType(config.Type),
            Unit = config.Unit
        };

        if (config.Type == DataType.BitField)
        {
            field.Bits = ParseBitField(data, config);
            field.DisplayValue = FormatBitField(field.Bits);
        }
        else
        {
            var rawValue = ExtractValue(data, config.Position, config.Type);
            field.Value = rawValue * config.Scale;
            field.DisplayValue = FormatValue(field.Value, config);
        }

        return field;
    }

    private List<ParsedBit> ParseBitField(byte[] data, FieldConfig config)
    {
        var bits = new List<ParsedBit>();
        var byteValue = data[(int)config.Position];

        foreach (var bitConfig in config.Bits)
        {
            if (bitConfig.Bit is int singleBit)
            {
                var bitValue = (byteValue & (1 << singleBit)) != 0;
                bits.Add(new ParsedBit
                {
                    BitPosition = singleBit,
                    Name = bitConfig.Name,
                    Value = bitValue,
                    DisplayValue = GetBitDisplayValue(bitValue, bitConfig.Values)
                });
            }
            else if (bitConfig.Bit is string bitRange)
            {
                // 处理位范围，如"2-0"
                var (startBit, endBit) = ParseBitRange(bitRange);
                var mask = CreateBitMask(startBit, endBit);
                var rangeValue = (byteValue & mask) >> endBit;

                bits.Add(new ParsedBit
                {
                    BitPosition = endBit,
                    Name = bitConfig.Name,
                    Value = rangeValue > 0,
                    DisplayValue = GetRangeDisplayValue(rangeValue, bitConfig.Values)
                });
            }
        }

        return bits;
    }
}
```

### 4.2 格式2解析流程
```csharp
public class ParameterIndexParser
{
    public ParsedData Parse(byte[] data, FunctionCodeConfig config)
    {
        var result = new ParsedData
        {
            FunctionCode = config.FunctionCode,
            Fields = new List<ParsedField>()
        };

        // 解析参数索引和个数
        var paramIndex = data[5]; // 参数索引号idx
        var paramCount = data[6]; // 参数个数N+1

        // 从第7个字节开始解析参数值
        var dataOffset = 7;

        for (int i = 0; i < paramCount; i++)
        {
            var currentIndex = paramIndex + i;

            if (config.Parameters.TryGetValue(currentIndex, out var paramConfig))
            {
                var paramValue = ExtractParameterValue(data, dataOffset, paramConfig.Type);
                var field = CreateParsedField(paramConfig, paramValue, currentIndex);
                result.Fields.Add(field);
            }

            dataOffset += GetDataTypeSize(paramConfig?.Type ?? DataType.u16);
        }

        return result;
    }

    private ParsedField CreateParsedField(ParameterConfig config, object rawValue, int index)
    {
        var field = new ParsedField
        {
            Name = $"{config.Name} (idx:{index})",
            Unit = config.Unit,
            Type = GetFieldType(config.Type)
        };

        if (config.Type == DataType.BitField && config.Bits != null)
        {
            field.Bits = ParseParameterBitField((ushort)rawValue, config.Bits);
            field.DisplayValue = FormatBitField(field.Bits);
        }
        else
        {
            field.Value = Convert.ToDouble(rawValue) * config.Scale;
            field.DisplayValue = FormatParameterValue(field.Value, config);
        }

        return field;
    }
}
```

## 5. 配置文件管理

### 5.1 配置文件结构
```json
{
  "ProtocolVersion": "1.0",
  "FunctionCodes": [
    {
      "FunctionCode": "A1",
      "Name": "外机状态查询",
      "Format": "FixedByte",
      "Fields": [...]
    },
    {
      "FunctionCode": "12",
      "Name": "参数读取",
      "Format": "ParameterIndex",
      "Parameters": {...}
    }
  ]
}
```

### 5.2 配置加载服务
```csharp
public class ProtocolConfigService
{
    private Dictionary<string, FunctionCodeConfig> _configs;

    public bool LoadConfiguration(string configPath)
    {
        try
        {
            var json = File.ReadAllText(configPath);
            var protocolConfig = JsonSerializer.Deserialize<ProtocolConfiguration>(json);

            _configs = protocolConfig.FunctionCodes
                .ToDictionary(c => c.FunctionCode, c => c);

            return true;
        }
        catch (Exception ex)
        {
            // 记录错误日志
            return false;
        }
    }

    public FunctionCodeConfig GetConfig(string functionCode)
    {
        return _configs.TryGetValue(functionCode, out var config) ? config : null;
    }
}
```

## 6. WPF界面设计

### 6.1 解析结果显示界面
- **TreeView控件**：分层显示解析结果
- **DataGrid控件**：表格形式显示参数列表
- **自定义UserControl**：位域可视化显示

### 6.2 配置管理界面
- **配置文件选择**：支持加载不同的协议配置文件
- **实时预览**：配置修改后实时预览解析效果
- **配置验证**：检查配置文件的正确性

## 7. 测试方案

### 7.1 单元测试
- **解析器测试**：使用已知数据验证解析结果
- **配置加载测试**：验证配置文件的正确加载
- **数据类型测试**：测试各种数据类型的解析

### 7.2 集成测试
- **端到端测试**：从原始数据到界面显示的完整流程
- **配置切换测试**：验证不同配置文件的切换功能
- **错误处理测试**：测试异常数据的处理能力
