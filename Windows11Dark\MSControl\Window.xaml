<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="WindowFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="{StaticResource Windows11Dark.FocusMargin}" 
                               SnapsToDevicePixels="true" 
                               Stroke="{StaticResource BorderAlt4}" 
                               StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Dark.StrokeDashArray}"/>
                    <ControlTemplate.Triggers>
                            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFWindowStyle"
       TargetType="{x:Type Window}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource WindowFocusVisual}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Background" Value="{StaticResource WindowBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt4}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="{StaticResource WindowBackgroundBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource WPFWindowStyle}" TargetType="{x:Type Window}"/>
</ResourceDictionary>
