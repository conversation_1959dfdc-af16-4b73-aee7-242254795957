<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:tools="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/CheckBox.xaml" />

    </ResourceDictionary.MergedDictionaries>

    <tools:ListItemIndentConverter x:Key="ListItemIndentConverter" />

    <!--  CheckListBoxStyle  -->
    <Style x:Key="SyncfusionCheckListBoxStyle" TargetType="{x:Type tools:CheckListBox}">
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="MouseOverBackground" Value="{StaticResource ContentBackgroundHovered}" />
        <Setter Property="SelectedItemBackground" Value="{StaticResource ContentBackgroundSelected}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="{x:Static ScrollBarVisibility.Auto}" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="{x:Static ScrollBarVisibility.Auto}" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="Once" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Padding" Value="0" />
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <VirtualizingStackPanel />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools:CheckListBox}">
                    <Border
                        x:Name="Border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        SnapsToDevicePixels="True"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius4}">
                        <Grid>
                            <ContentPresenter Name="PART_ContentPresenter" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="Border" Property="Background" Value="Transparent" />
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionCheckListBoxStyle}" TargetType="{x:Type tools:CheckListBox}" />

    <!--  CheckListBoxItemStyle  -->
    <Style x:Key="SyncfusionCheckListBoxItemStyle" TargetType="{x:Type tools:CheckListBoxItem}">
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="tools:CheckListBox.CheckBoxAlignment" Value="Left" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="IsSelected" Value="{Binding RelativeSource={RelativeSource Mode=Self}, Path=State.IsSelected, Mode=TwoWay}" />
        <Setter Property="Padding" Value="{Binding Path=State.Level, RelativeSource={RelativeSource Mode=Self}, Converter={StaticResource ListItemIndentConverter}, ConverterParameter='Item'}" />
        <Setter Property="Margin" Value="5, 2, 5, 2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools:CheckListBoxItem}">
                    <Grid>
                        <Border
                            Name="Border"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="true"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            >
                            <Grid KeyboardNavigation.DirectionalNavigation="None">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Name="firstColumn" Width="Auto" />
                                    <ColumnDefinition Name="secondColumn" Width="*" />
                                </Grid.ColumnDefinitions>
                                <CheckBox
                                    Name="PART_CheckBox"
                                    Margin="5, 1 ,5, 1"
                                    VerticalAlignment="Center"
                                    Focusable="False"
                                    IsChecked="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=State.IsChecked}" />
                                <Border Name="ContentBorder" Grid.Column="1">
                                    <ContentPresenter Name="PART_ContentPresenter" Margin="3" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" >
                                        <ContentPresenter.Resources>
                                            <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                        </ContentPresenter.Resources>
                                    </ContentPresenter>
                                </Border>
                            </Grid>
                        </Border>
                        <Rectangle
                            x:Name="BottomBorder"
                            Height="1"
                            VerticalAlignment="Bottom"
                            Fill="{TemplateBinding BorderBrush}"
                            Visibility="Collapsed" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Path=(tools:CheckListBox.CheckBoxAlignment), RelativeSource={RelativeSource AncestorType={x:Type tools:CheckListBox}}}" Value="Right">
                            <Setter TargetName="PART_CheckBox" Property="Grid.Column" Value="2" />
                            <Setter TargetName="ContentBorder" Property="Grid.Column" Value="1" />
                            <Setter TargetName="firstColumn" Property="Width" Value="*" />
                            <Setter TargetName="secondColumn" Property="Width" Value="Auto" />
                            <Setter TargetName="PART_CheckBox" Property="Margin" Value="5,1,1,1" />
                        </DataTrigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource HoveredForeground}" />
                            <Setter TargetName="Border" Property="Background" Value="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:CheckListBox}}}" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="{Binding Path=SelectedItemBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:CheckListBox}}}" />
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True" />
                                <Condition Property="Selector.IsSelectionActive" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundInactive}" />
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                        <Trigger Property="Content" Value="{x:Null}">
                            <Setter TargetName="PART_CheckBox" Property="IsChecked" Value="{Binding IsChecked}" />
                            <Setter TargetName="PART_ContentPresenter" Property="Content" Value="{Binding Source}" />
                            <Setter TargetName="PART_ContentPresenter" Property="ContentTemplate" Value="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=tools:CheckListBox}, Path=ItemTemplate}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionCheckListBoxItemStyle}" TargetType="{x:Type tools:CheckListBoxItem}" />

    <Style x:Key="SyncfusionCheckListSelectAllItemStyle" TargetType="{x:Type tools:CheckListSelectAllItem}">
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="tools:CheckListBox.CheckBoxAlignment" Value="Left" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="IsSelected" Value="{Binding RelativeSource={RelativeSource Mode=Self}, Path=State.IsSelected}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Margin" Value="5, 2, 5, 2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools:CheckListSelectAllItem}">
                    <Grid>
                        <Border
                            Name="Border"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="true"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            >
                            <Grid KeyboardNavigation.DirectionalNavigation="None">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Name="firstColumn" Width="Auto" />
                                    <ColumnDefinition Name="secondColumn" Width="*" />
                                </Grid.ColumnDefinitions>
                                <CheckBox
                                    Name="PART_CheckBox"
                                    Margin="5, 1 ,5, 1"
                                    VerticalAlignment="Center"
                                    Focusable="False"
                                    IsChecked="{Binding IsChecked}" />
                                <Border Name="ContentBorder" Grid.Column="1">
                                    <ContentPresenter
                                        x:Name="PART_ContentPresenter"
                                        Margin="3" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Content="{Binding Source}"
                                        ContentTemplate="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=tools:CheckListBox}, Path=SelectAllTemplate}" >
                                        <ContentPresenter.Resources>
                                            <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                        </ContentPresenter.Resources>
                                    </ContentPresenter>
                                </Border>
                            </Grid>
                        </Border>
                        <Rectangle
                            x:Name="BottomBorder"
                            Height="1"
                            VerticalAlignment="Bottom"
                            Fill="{TemplateBinding BorderBrush}"
                            Visibility="Collapsed" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Path=(tools:CheckListBox.CheckBoxAlignment), RelativeSource={RelativeSource AncestorType={x:Type tools:CheckListBox}}}" Value="Right">
                            <Setter TargetName="PART_CheckBox" Property="Grid.Column" Value="1" />
                            <Setter TargetName="ContentBorder" Property="Grid.Column" Value="0" />
                            <Setter TargetName="firstColumn" Property="Width" Value="*" />
                            <Setter TargetName="secondColumn" Property="Width" Value="Auto" />
                            <Setter TargetName="PART_CheckBox" Property="Margin" Value="5,1,1,1" />
                        </DataTrigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="Border" Property="Background" Value="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:CheckListBox}}}" />
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource HoveredForeground}" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="{Binding Path=SelectedItemBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:CheckListBox}}}" />
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True" />
                                <Condition Property="Selector.IsSelectionActive" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundInactive}" />
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionCheckListSelectAllItemStyle}" TargetType="{x:Type tools:CheckListSelectAllItem}" />

    <Style x:Key="SyncfusionExpanderToggleButtonStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="Focusable" Value="False" />
        <Setter Property="Width" Value="16" />
        <Setter Property="Height" Value="16" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border x:Name="border"
                        Width="16"
                        Height="16"
                        Padding="5"
                        Background="Transparent">
                        <Path
                            x:Name="ExpandPath"
                            Data="M0,0 L0,6 L6,0 z"
                            Fill="{StaticResource IconColor}"
                            Stroke="{StaticResource IconColor}">
                            <Path.RenderTransform>
                                <RotateTransform Angle="135" CenterX="3" CenterY="3" />
                            </Path.RenderTransform>
                        </Path>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinSize}"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ExpandPath" Property="RenderTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="180" CenterX="3" CenterY="3" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="ExpandPath" Property="Fill" Value="{StaticResource IconColor}" />
                            <Setter TargetName="ExpandPath" Property="Stroke" Value="{StaticResource IconColor}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ExpandPath" Property="Stroke" Value="{StaticResource IconColorHovered}" />
                            <Setter TargetName="ExpandPath" Property="Fill" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True" />
                                <Condition Property="IsChecked" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="ExpandPath" Property="Stroke" Value="{StaticResource IconColorHovered}" />
                            <Setter TargetName="ExpandPath" Property="Fill" Value="{StaticResource IconColorHovered}" />
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ExpandPath" Property="Stroke" Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="ExpandPath" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionCheckListGroupItemStyle" TargetType="{x:Type tools:CheckListGroupItem}">
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="tools:CheckListBox.CheckBoxAlignment" Value="Left" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="IsSelected" Value="{Binding RelativeSource={RelativeSource Mode=Self}, Path=State.IsSelected}" />
        <Setter Property="IsExpanded" Value="{Binding RelativeSource={RelativeSource Mode=Self}, Path=State.IsExpanded}" />
        <Setter Property="Padding" Value="{Binding Path=State.Level, RelativeSource={RelativeSource Mode=Self}, Converter={StaticResource ListItemIndentConverter}, ConverterParameter='Group'}" />
        <Setter Property="Margin" Value="5, 2, 5, 2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools:CheckListGroupItem}">
                    <Grid>
                        <Border
                            Name="Border"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="true"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            >
                            <Grid KeyboardNavigation.DirectionalNavigation="None">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Name="firstColumn" Width="16" />
                                    <ColumnDefinition Name="secondColumn" Width="Auto" />
                                    <ColumnDefinition Name="thirdColumn" Width="*" />
                                </Grid.ColumnDefinitions>
                                <ToggleButton
                                    x:Name="Expander"
                                    ClickMode="Press"
                                    IsChecked="{Binding State.IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                                    Style="{StaticResource SyncfusionExpanderToggleButtonStyle}" />
                                <CheckBox
                                    Name="PART_CheckBox"
                                    Grid.Column="1"
                                    Margin="5, 1 ,5, 1"
                                    VerticalAlignment="Center"
                                    Focusable="False"
                                    IsChecked="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=State.IsChecked}" />
                                <Border Name="ContentBorder" Grid.Column="2">
                                    <ContentPresenter
                                        x:Name="PART_ContentPresenter"
                                        Margin="3" 
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Content="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=tools:CheckListGroupItem}, Path=State.Source}"
                                        ContentTemplate="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=tools:CheckListBox}, Path=GroupTemplate}" />
                                </Border>
                            </Grid>
                        </Border>
                        <Rectangle
                            x:Name="BottomBorder"
                            Height="1"
                            VerticalAlignment="Bottom"
                            Fill="{TemplateBinding BorderBrush}"
                            Visibility="Collapsed" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Path=(tools:CheckListBox.CheckBoxAlignment), RelativeSource={RelativeSource AncestorType={x:Type tools:CheckListBox}}}" Value="Right">
                            <Setter TargetName="PART_CheckBox" Property="Grid.Column" Value="1" />
                            <Setter TargetName="ContentBorder" Property="Grid.Column" Value="0" />
                            <Setter TargetName="secondColumn" Property="Width" Value="*" />
                            <Setter TargetName="thirdColumn" Property="Width" Value="Auto" />
                            <Setter TargetName="PART_CheckBox" Property="Margin" Value="5,1,1,1" />
                        </DataTrigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="Border" Property="Background" Value="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:CheckListBox}}}" />
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource HoveredForeground}" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="{Binding Path=SelectedItemBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools:CheckListBox}}}" />
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True" />
                                <Condition Property="Selector.IsSelectionActive" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundInactive}" />
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                            <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionCheckListGroupItemStyle}" TargetType="{x:Type tools:CheckListGroupItem}" />
</ResourceDictionary>
