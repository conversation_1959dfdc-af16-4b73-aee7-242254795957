<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="ListBoxItemFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFListBoxItemStyle"
           TargetType="{x:Type ListBoxItem}">
        <Setter Property="FocusVisualStyle"
                Value="{StaticResource ListBoxItemFocusVisual}"/>
        <Setter Property="Background"
                Value="Transparent"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="Padding"
                Value="7,3"/>
        <Setter Property="Margin"
                Value="4,2"/>
        <Setter Property="MinHeight" Value="{StaticResource Windows11Light.MinHeight}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListBoxItem}">
                    <Grid>
                        <Border x:Name="SelectionIndicator"
                                HorizontalAlignment="Left"
                                CornerRadius="1.5"
                                Height="12"
                                Width="2"
                                Visibility="Collapsed"
                                Background="{StaticResource PrimaryBackground}" />

                        <Border x:Name="Bd" 
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                Background="{TemplateBinding Background}" 
                                Padding="{TemplateBinding Padding}" 
                                SnapsToDevicePixels="true"
                                >
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" >
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource HoveredForeground}"/>
                        </Trigger>
                        <Trigger Property="IsSelected"
                                 Value="true">
                            <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource SelectedForeground}"/>
                            <Setter Property="Visibility" TargetName="SelectionIndicator" Value="Visible"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected"
                                           Value="true"/>
                                <Condition Property="IsMouseOver"
                                           Value="true"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource SelectedForeground}"/>
                            <Setter Property="Visibility" TargetName="SelectionIndicator" Value="Visible"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected"
                                           Value="true"/>
                                <Condition Property="Selector.IsSelectionActive"
                                           Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundInactive}"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundInactive}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource ContentForeground}"/>
                            <Setter Property="Visibility" TargetName="SelectionIndicator" Value="Visible"/>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter Property="Background" TargetName="Bd"
                                    Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="Bd"
                                    Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource WPFListBoxItemStyle}" TargetType="{x:Type ListBoxItem}"/>

    <Style x:Key="WPFListBoxStyle"
           TargetType="{x:Type ListBox}">
        <Setter Property="Background"
                Value="Transparent"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" 
                Value="Auto"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" 
                Value="Auto"/>
        <Setter Property="ScrollViewer.CanContentScroll" 
                Value="true"/>
        <Setter Property="ScrollViewer.PanningMode"
                Value="Both"/>
        <Setter Property="Stylus.IsFlicksEnabled"
                Value="False"/>
        <Setter Property="VerticalContentAlignment"
                Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListBox}">
                    <Border Name="Bd"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            Background="{TemplateBinding Background}"
                            SnapsToDevicePixels="true">
                        <ScrollViewer x:Name="scrollviewer" 
                                      Focusable="false"
                                      Padding="{TemplateBinding Padding}">
                            <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                        </ScrollViewer>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsGrouping"
                                 Value="true">
                            <Setter Property="ScrollViewer.CanContentScroll"
                                    Value="false"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="Bd" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="Bd" Value="Transparent"/>
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource WPFListBoxStyle}" TargetType="{x:Type ListBox}"/>

</ResourceDictionary>
