<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:primitives="clr-namespace:Syncfusion.Windows.Controls.Primitives;assembly=Syncfusion.Shared.WPF"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:shared_controls="clr-namespace:Syncfusion.Windows.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="WPFCalendarButtonStyle" TargetType="{x:Type CalendarButton}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="MinWidth" Value="45"/>
        <Setter Property="MinHeight" Value="47"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type CalendarButton}">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0:0:0.1"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="MouseOver"/>
                                <VisualState x:Name="Pressed"/>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="SelectionStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Unselected"/>
                                <VisualState x:Name="Selected"/>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="ActiveStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Active"/>
                                <VisualState x:Name="Inactive">
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CalendarButtonFocusStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="CalendarButtonFocused"/>
                                <VisualState x:Name="CalendarButtonUnfocused"/>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Rectangle x:Name="SelectedBackground"
                                   Fill="Transparent"
                                   Opacity="0"
                                   RadiusY="100"
                                   RadiusX="100"/>
                        <Rectangle x:Name="Background"
                                   Fill="{TemplateBinding Background}"
                                   Stroke="{TemplateBinding BorderBrush}"
                                   Opacity="0"
                                   RadiusY="100"
                                   RadiusX="100"/>
                        <ContentPresenter x:Name="NormalText"
                                          TextElement.Foreground="{TemplateBinding Foreground}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          Margin="1,0,1,1"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                        <Rectangle x:Name="CalendarButtonFocusVisual"
                                   IsHitTestVisible="false"
                                   RadiusY="100" 
                                   RadiusX="100"
                                   Stroke="{StaticResource PrimaryBackground}"
                                   Visibility="Collapsed"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="60"/>
                            <Setter Property="MinWidth" Value="55"/>
                        </Trigger>                        
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Opacity" TargetName="Background" Value="1"/>
                            <Setter Property="Fill" TargetName="Background" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="Stroke" TargetName="Background" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="Visibility" TargetName="CalendarButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryBackground}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Opacity" TargetName="Background" Value="1"/>
                            <Setter Property="Fill" TargetName="Background" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="Stroke" TargetName="Background" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <Trigger Property="IsInactive" Value="True">
                            <Setter Property="Opacity" TargetName="Background" Value="1"/>
                            <Setter Property="Fill" TargetName="Background" Value="Transparent"/>
                            <Setter Property="Stroke" TargetName="Background" Value="Transparent"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CircleKeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default">
                <Setter Property="FocusVisualStyle" Value="{StaticResource DottedCircleKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource WPFCalendarButtonStyle}" TargetType="{x:Type CalendarButton}"/>

    <Style x:Key="WPFCalendarDayButtonStyle" TargetType="{x:Type CalendarDayButton}">
        <Setter Property="MinWidth" Value="5"/>
        <Setter Property="MinHeight" Value="5"/>
        <Setter Property="Height" Value="30"/>
        <Setter Property="Width" Value="30"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type CalendarDayButton}">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0:0:0.1"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="MouseOver"/>
                                <VisualState x:Name="Pressed"/>
                                <VisualState x:Name="Disabled"/>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="SelectionStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Unselected"/>
                                <VisualState x:Name="Selected"/>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CalendarButtonFocusStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="CalendarButtonFocused">
                                </VisualState>
                                <VisualState x:Name="CalendarButtonUnfocused">
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="ActiveStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Active"/>
                                <VisualState x:Name="Inactive">
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="DayStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="RegularDay"/>
                                <VisualState x:Name="Today">
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="BlackoutDayStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0"/>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="NormalDay"/>
                                <VisualState x:Name="BlackoutDay">
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="TodayBackground"
                                 Background="{StaticResource PrimaryBackground}"
                                 BorderBrush="{StaticResource PrimaryBackground}"
                                 BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                 Opacity="0"
                                 CornerRadius="14"
                                 Width="28"
                                 Height="28">
                        </Border>
                        <Border x:Name="SelectedBackground"
                                 Background="Transparent"
                                 Opacity="0"
                                 CornerRadius="14"
                                 Width="28"
                                 Height="28">
                        </Border>
                        <Border BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Background="{TemplateBinding Background}"/>
                        <Border x:Name="HighlightBackground"
                                 Background="{StaticResource ContentBackgroundHovered}"
                                 BorderBrush="{StaticResource ContentBackgroundHovered}"
                                 BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                 Opacity="0"
                                 CornerRadius="14"
                                 Width="28"
                                 Height="28">
                        </Border>
                        <ContentPresenter x:Name="NormalText"
                                          TextElement.Foreground="{StaticResource ContentForeground}"
                                          TextElement.FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          Margin="5,1,5,1"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                        <Path x:Name="Blackout"
                              Data="M0,0 L16,0 16,2 0,2 z"
                              Fill="Transparent"
                              HorizontalAlignment="Stretch"
                              Margin="4"
                              Height="1"
                              Opacity="0"
                              RenderTransformOrigin="0.5,0.5"
                              Stretch="Fill"
                              VerticalAlignment="Stretch">
                            <Path.RenderTransform>
                                <RotateTransform CenterX="0" CenterY="0" Angle="45" />
                            </Path.RenderTransform>
                        </Path>
                        <Border x:Name="DayButtonFocusVisual"
                                 IsHitTestVisible="false"
                                 Width="28"
                                 Height="28"
                                 BorderBrush="{StaticResource PrimaryBackground}"
                                 BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                 CornerRadius="14"
                                 Visibility="Collapsed">
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinWidth}"/>
                        </Trigger>                        
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryBackground}"/>
                        </Trigger>
                        <Trigger Property="IsToday" Value="True">
                            <Setter Property="Opacity" TargetName="TodayBackground" Value="1"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryForeground}"/>
                        </Trigger>
                        <Trigger Property="IsInactive" Value="True">
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                        </Trigger>
                        <Trigger Property="IsBlackedOut" Value="True">
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Stroke" TargetName="Blackout" Value="{StaticResource BorderAlt1}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryBackground}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True"/>
                                <Condition Property="IsToday" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Opacity" TargetName="TodayBackground" Value="0"/>
                            <Setter Property="Width" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Height" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" Value="True"/>
                                <Condition Property="IsToday" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Opacity" TargetName="TodayBackground" Value="0"/>
                            <Setter Property="Width" TargetName="SelectedBackground" Value="25"/>
                            <Setter Property="Height" TargetName="SelectedBackground" Value="25"/> 
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="IsToday" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="0"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="IsToday" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" Value="True"/>
                                <Condition Property="IsInactive" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="IsInactive" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="IsInactive" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource DisabledForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource HoveredForeground}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource DisabledForeground}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True"/>
                                <Condition Property="IsToday" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="TodayBackground" Value="0"/>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Width" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Height" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" Value="True"/>
                                <Condition Property="IsToday" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="TodayBackground" Value="0"/>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Width" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Height" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="IsToday" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="IsToday" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource DisabledForeground}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" Value="True"/>
                                <Condition Property="IsInactive" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="IsInactive" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="IsInactive" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource DisabledForeground}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CircleKeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default">
                <Setter Property="FocusVisualStyle" Value="{StaticResource DottedCircleKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource WPFCalendarDayButtonStyle}" TargetType="{x:Type CalendarDayButton}"/>

    <Style x:Key="WPFCalendarItemStyle" TargetType="{x:Type CalendarItem}">
        <Setter Property="Margin" Value="0,3,0,3"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type CalendarItem}">
                    <ControlTemplate.Resources>
                        <DataTemplate x:Key="{x:Static CalendarItem.DayTitleTemplateResourceKey}">
                            <TextBlock Foreground="{StaticResource ContentForeground}"
                                       FontWeight="{StaticResource Windows11Dark.FontWeightMedium}"
                                       FontSize="{StaticResource Windows11Dark.CaptionText}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                       HorizontalAlignment="Center"
                                       Margin="0,6,0,6"
                                       Text="{Binding}"
                                       VerticalAlignment="Center"/>
                        </DataTemplate>
                    </ControlTemplate.Resources>
                    <Grid x:Name="PART_Root">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimation Duration="0" To="1" Storyboard.TargetProperty="Opacity" Storyboard.TargetName="PART_DisabledVisual"/>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                Background="{TemplateBinding Background}" 
                                SnapsToDevicePixels="True"
                                CornerRadius="{StaticResource Windows11Dark.CornerRadius8}">
                            <Grid>
                                <Grid.Resources>
                                    <Style x:Key="MaterialPreviousArrowButtonStyle"
                                           BasedOn="{StaticResource WPFGlyphButtonStyle}"
                                           TargetType="{x:Type Button}">
                                        <Setter Property="Content">
                                            <Setter.Value>
                                                <TextBlock x:Name="path"
                                                           Margin="0"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"
                                                           FontSize="14"
                                                           FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                                           Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                                                           Opacity="0.67"
                                                           Text="&#xe709;"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                                                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                    <Style x:Key="MaterialNextArrowButtonStyle"
                                           BasedOn="{StaticResource WPFGlyphButtonStyle}"
                                           TargetType="{x:Type Button}">
                                        <Setter Property="Content">
                                            <Setter.Value>
                                                <TextBlock x:Name="path"
                                                           Margin="0"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"
                                                           FontSize="14"
                                                           FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                                           Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                                                           Opacity="0.67"
                                                           Text="&#xe708;"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                                                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                    <Style x:Key="MaterialHeaderButtonStyle" TargetType="{x:Type Button}">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="{x:Type Button}">
                                                    <Border x:Name="HeaderButtonGrid" 
                                                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" 
                                                            Cursor="Hand">
                                                        <VisualStateManager.VisualStateGroups>
                                                            <VisualStateGroup x:Name="CommonStates">
                                                                <VisualState x:Name="Normal"/>
                                                                <VisualState x:Name="MouseOver"/>
                                                                <VisualState x:Name="Pressed"/>
                                                                <VisualState x:Name="Disabled">
                                                                </VisualState>
                                                            </VisualStateGroup>
                                                        </VisualStateManager.VisualStateGroups>
                                                        <ContentPresenter x:Name="buttonContent"
                                                                          ContentTemplate="{TemplateBinding ContentTemplate}"
                                                                          Content="{TemplateBinding Content}"
                                                                          TextElement.Foreground="{StaticResource ContentForeground}"
                                                                          TextElement.FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                                                          TextElement.FontSize="{StaticResource Windows11Dark.TitleTextStyle}"
                                                                          TextElement.FontWeight="{StaticResource Windows11Dark.FontWeightMedium}"
                                                                          HorizontalAlignment="Left"
                                                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                                          Margin="4,0,4,0">
                                                            <ContentPresenter.Resources>
                                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                                            </ContentPresenter.Resources>
                                                        </ContentPresenter>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="true">
                                                            <Setter Property="TextElement.Foreground" TargetName="buttonContent" Value="{StaticResource ContentForeground}"/>
                                                            <Setter Property="Background" TargetName="HeaderButtonGrid" Value="{StaticResource ContentBackgroundHovered}"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed" Value="true">
                                                            <Setter Property="TextElement.Foreground" TargetName="buttonContent" Value="{StaticResource DisabledForeground}"/>
                                                            <Setter Property="Background" TargetName="HeaderButtonGrid" Value="{StaticResource ContentBackgroundPressed}"/>
                                                        </Trigger>
                                                        <Trigger Property="IsEnabled" Value="false">
                                                            <Setter Property="TextElement.Foreground" TargetName="buttonContent" Value="{StaticResource DisabledForeground}"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Grid.Resources>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="28"/>
                                    <ColumnDefinition Width="28"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" MinHeight="38"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <Border Grid.ColumnSpan="3" 
                                        SnapsToDevicePixels="True"
                                        Visibility="Visible"
                                        BorderThickness="0,0,0,1"
                                        BorderBrush="{TemplateBinding BorderBrush}"/>
                                <Button x:Name="PART_PreviousButton"
                                        Grid.Column="1"
                                        HorizontalAlignment="Right"
                                        Width="24"
                                        Height="24"
                                        Grid.Row="0"
                                        Margin="0,0,4,0"
                                        Style="{StaticResource MaterialPreviousArrowButtonStyle}"/>
                                <Button x:Name="PART_HeaderButton"
                                        Grid.Column="0"
                                        FontWeight="Bold"
                                        Focusable="False"
                                        FontSize="10.5"
                                        Height="24" 
                                        MinWidth="140"
                                        VerticalAlignment="Center"
                                        HorizontalAlignment="Left"
                                        Grid.Row="0"
                                        Margin="4,0,0,0"
                                        Style="{StaticResource MaterialHeaderButtonStyle}"/>
                                <Button x:Name="PART_NextButton"
                                        Grid.Column="2"
                                        HorizontalAlignment="Right"
                                        Width="24"
                                        Height="24"
                                        Grid.Row="0"
                                        Margin="0,0,4,0"
                                        Style="{StaticResource MaterialNextArrowButtonStyle}"/>
                                <Grid x:Name="PART_MonthView"
                                      Grid.ColumnSpan="3"
                                      HorizontalAlignment="Stretch"
                                      Margin="1"
                                      Grid.Row="1"
                                      Visibility="Visible">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                </Grid>
                                <Grid x:Name="PART_YearView"
                                      Grid.ColumnSpan="3"
                                      HorizontalAlignment="Center"
                                      Margin="6,16,6,16"
                                      Grid.Row="1"
                                      Visibility="Hidden">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                </Grid>
                            </Grid>
                        </Border>
                        <Rectangle x:Name="PART_DisabledVisual"
                                   Fill="Transparent"
                                   Opacity="0"
                                   RadiusY="2" RadiusX="2"
                                   Stretch="Fill"
                                   Stroke="Transparent"
                                   StrokeThickness="1"
                                   Visibility="Collapsed"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>                        
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Visibility" TargetName="PART_DisabledVisual" Value="Visible"/>
                        </Trigger>
                        <DataTrigger Binding="{Binding DisplayMode, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Calendar}}}" Value="Year">
                            <Setter Property="Visibility" TargetName="PART_MonthView" Value="Hidden"/>
                            <Setter Property="Visibility" TargetName="PART_YearView" Value="Visible"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding DisplayMode, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Calendar}}}" Value="Decade">
                            <Setter Property="Visibility" TargetName="PART_MonthView" Value="Hidden"/>
                            <Setter Property="Visibility" TargetName="PART_YearView" Value="Visible"/>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource WPFCalendarItemStyle}" TargetType="{x:Type CalendarItem}"/>

    <Style x:Key="WPFCalendarStyle" TargetType="{x:Type Calendar}">
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="CalendarItemStyle" Value="{StaticResource WPFCalendarItemStyle}"/>
        <Setter Property="CalendarDayButtonStyle" Value="{StaticResource WPFCalendarDayButtonStyle}"/>
        <Setter Property="CalendarButtonStyle" Value="{StaticResource WPFCalendarButtonStyle}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Calendar}">
                    <StackPanel x:Name="PART_Root" HorizontalAlignment="Center">
                        <CalendarItem x:Name="PART_CalendarItem"
                                      BorderBrush="{TemplateBinding BorderBrush}"
                                      BorderThickness="{TemplateBinding BorderThickness}"
                                      Background="{TemplateBinding Background}"
                                      Style="{TemplateBinding CalendarItemStyle}"/>
                    </StackPanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <Style BasedOn="{StaticResource WPFCalendarStyle}" TargetType="{x:Type Calendar}"/>

    <!--  DateTimEdit Calendar Style  -->

    <Style x:Key="SyncfusionCalendarButtonStyle" TargetType="{x:Type primitives:CalendarButton}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="MinWidth" Value="45" />
        <Setter Property="MinHeight" Value="47" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type primitives:CalendarButton}">
                    <Grid>
                        <Rectangle
                            x:Name="SelectedBackground"
                            Fill="Transparent"
                            Opacity="0"
                            RadiusX="100"
                            RadiusY="100" />
                        <Rectangle
                            x:Name="Background"
                            Fill="{TemplateBinding Background}"
                            Opacity="0"
                            RadiusX="100"
                            RadiusY="100"
                            Stroke="{TemplateBinding BorderBrush}" />
                        <ContentPresenter
                            x:Name="NormalText"
                            Margin="1,0,1,1"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            TextElement.Foreground="{TemplateBinding Foreground}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                        <Rectangle
                            x:Name="CalendarButtonFocusVisual"
                            IsHitTestVisible="false"
                            RadiusX="100"
                            RadiusY="100"
                            Stroke="{StaticResource PrimaryBackground}"
                            Visibility="Collapsed" />
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0:0:0.1" />
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="MouseOver" />
                                <VisualState x:Name="Pressed" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="SelectionStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0" />
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Unselected" />
                                <VisualState x:Name="Selected" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="ActiveStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0" />
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Active" />
                                <VisualState x:Name="Inactive" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CalendarButtonFocusStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0" />
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="CalendarButtonFocused" />
                                <VisualState x:Name="CalendarButtonUnfocused" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="60"/>
                            <Setter Property="MinWidth" Value="55"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Background" Property="Opacity" Value="1" />
                            <Setter TargetName="Background" Property="Fill" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="Background" Property="Stroke" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="NormalText" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="CalendarButtonFocusVisual" Property="Visibility" Value="Visible" />
                            <Setter TargetName="SelectedBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="NormalText" Property="TextElement.Foreground" Value="{StaticResource PrimaryBackground}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Background" Property="Opacity" Value="1" />
                            <Setter TargetName="Background" Property="Fill" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="Background" Property="Stroke" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="NormalText" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>
                        <Trigger Property="IsInactive" Value="True">
                            <Setter TargetName="Background" Property="Opacity" Value="1" />
                            <Setter TargetName="Background" Property="Fill" Value="Transparent" />
                            <Setter TargetName="Background" Property="Stroke" Value="Transparent" />
                            <Setter TargetName="NormalText" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CircleKeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default">
                <Setter Property="FocusVisualStyle" Value="{StaticResource DottedCircleKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionCalendarButtonStyle}" TargetType="{x:Type primitives:CalendarButton}" />

    <Style x:Key="SyncfusionCalendarDayButtonStyle" TargetType="{x:Type primitives:CalendarDayButton}">
        <Setter Property="MinWidth" Value="5" />
        <Setter Property="MinHeight" Value="5" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type primitives:CalendarDayButton}">
                    <Grid>
                        <Border x:Name="TodayBackground"
                                 Background="{StaticResource PrimaryBackground}"
                                 BorderBrush="{StaticResource PrimaryBackground}"
                                 BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                 Opacity="0"
                                 CornerRadius="14"
                                 Width="24" Height="24">
                        </Border>
                        <Border x:Name="SelectedBackground"
                                 Background="Transparent"
                                 Opacity="0"
                                 CornerRadius="14"
                                 Width="24" Height="24">
                        </Border>
                        <Border BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Background="{TemplateBinding Background}"/>
                        <Border x:Name="HighlightBackground"
                                 Background="{StaticResource ContentBackgroundHovered}"
                                 BorderBrush="{StaticResource ContentBackgroundHovered}"
                                 BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                 Opacity="0"
                                 CornerRadius="14"
                                 Width="24" Height="24">
                        </Border>
                        <ContentPresenter x:Name="NormalText"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          Margin="5,1,5,1"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                        <Path x:Name="Blackout"
                              Data="M0,0 L16,0 16,2 0,2 z"
                              Fill="Transparent"
                              HorizontalAlignment="Stretch"
                              Margin="4"
                              Height="1"
                              Opacity="0"
                              RenderTransformOrigin="0.5,0.5"
                              Stretch="Fill"
                              VerticalAlignment="Stretch">
                            <Path.RenderTransform>
                                <RotateTransform CenterX="0" CenterY="0" Angle="45" />
                            </Path.RenderTransform>
                        </Path>
                        <Border x:Name="DayButtonFocusVisual"
                                 IsHitTestVisible="false"
                                 Width="24" Height="24"
                                 BorderBrush="{StaticResource PrimaryBackground}"
                                 BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                 CornerRadius="14"
                                 Visibility="Collapsed">
                        </Border>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0:0:0.1" />
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="MouseOver" />
                                <VisualState x:Name="Pressed" />
                                <VisualState x:Name="Disabled" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="SelectionStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0" />
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Unselected" />
                                <VisualState x:Name="Selected" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CalendarButtonFocusStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0" />
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="CalendarButtonFocused" />
                                <VisualState x:Name="CalendarButtonUnfocused" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="ActiveStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0" />
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Active" />
                                <VisualState x:Name="Inactive" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="DayStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0" />
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="RegularDay" />
                                <VisualState x:Name="Today" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="BlackoutDayStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0" />
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="NormalDay" />
                                <VisualState x:Name="BlackoutDay" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinWidth}"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryBackground}"/>
                        </Trigger>
                        <Trigger Property="IsToday" Value="True">
                            <Setter Property="Opacity" TargetName="TodayBackground" Value="1"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryForeground}"/>
                        </Trigger>
                        <Trigger Property="IsInactive" Value="True">
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                        </Trigger>
                        <Trigger Property="IsBlackedOut" Value="True">
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Stroke" TargetName="Blackout" Value="{StaticResource BorderAlt1}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryBackground}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True"/>
                                <Condition Property="IsToday" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Opacity" TargetName="TodayBackground" Value="0"/>
                            <Setter Property="Width" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Height" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" Value="True"/>
                                <Condition Property="IsToday" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Opacity" TargetName="TodayBackground" Value="0"/>
                            <Setter Property="Width" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Height" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="IsToday" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="0"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="IsToday" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource PrimaryForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" Value="True"/>
                                <Condition Property="IsInactive" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="IsInactive" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="IsInactive" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource DisabledForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource HoveredForeground}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource DisabledForeground}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True"/>
                                <Condition Property="IsToday" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="TodayBackground" Value="0"/>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Width" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Height" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" Value="True"/>
                                <Condition Property="IsToday" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="TodayBackground" Value="0"/>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Width" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Height" TargetName="SelectedBackground" Value="18"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="IsToday" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="IsToday" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource DisabledForeground}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" Value="True"/>
                                <Condition Property="IsInactive" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="SelectedBackground" Value="1"/>
                            <Setter Property="Visibility" TargetName="DayButtonFocusVisual" Value="Visible"/>
                            <Setter Property="Background" TargetName="SelectedBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="DayButtonFocusVisual" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="IsInactive" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="IsInactive" Value="True"/>
                                <Condition Property="IsBlackedOut" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Opacity" TargetName="Blackout" Value="1"/>
                            <Setter Property="Opacity" TargetName="HighlightBackground" Value="1"/>
                            <Setter Property="Background" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="HighlightBackground" Value="Transparent"/>
                            <Setter Property="TextElement.Foreground" TargetName="NormalText" Value="{StaticResource DisabledForeground}"/>
                            <Setter Property="Fill" TargetName="Blackout" Value="{StaticResource IconColorDisabled}"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CircleKeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default">
                <Setter Property="FocusVisualStyle" Value="{StaticResource DottedCircleKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionCalendarDayButtonStyle}" TargetType="{x:Type primitives:CalendarDayButton}" />

    <Style x:Key="SyncfusionCalendarItemStyle" TargetType="{x:Type primitives:CalendarItem}">
        <Setter Property="Margin" Value="0,3,0,3" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.CaptionText}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightMedium}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type primitives:CalendarItem}">
                    <ControlTemplate.Resources>
                        <DataTemplate x:Key="DayTitleTemplate">
                            <TextBlock
                                Margin="0,6,0,6"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Text="{Binding}" />
                        </DataTemplate>
                    </ControlTemplate.Resources>
                    <Grid x:Name="PART_Root">
                        <Border
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius8}">
                            <Grid>
                                <Grid.Resources>
                                    <Style x:Key="MaterialPreviousArrowButtonStyle"
                                           BasedOn="{StaticResource WPFGlyphButtonStyle}"
                                           TargetType="{x:Type Button}">
                                        <Setter Property="Content">
                                            <Setter.Value>
                                                <TextBlock x:Name="path"
                                                           Margin="0"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"
                                                           FontSize="14"
                                                           FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                                           Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                                                           Text="&#xe70a;"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                                                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                    <Style x:Key="MaterialNextArrowButtonStyle"
                                           BasedOn="{StaticResource WPFGlyphButtonStyle}"
                                           TargetType="{x:Type Button}">
                                        <Setter Property="Content">
                                            <Setter.Value>
                                                <TextBlock x:Name="path"
                                                           Margin="0"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"
                                                           FontSize="14"
                                                           FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                                           Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                                                           Text="&#xe70b;"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                                                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                    <Style x:Key="MaterialHeaderButtonStyle" TargetType="{x:Type Button}">
                                        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                                        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightMedium}"/>
                                        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.TitleTextStyle}"/>
                                        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="{x:Type Button}">
                                                    <Border x:Name="HeaderButtonGrid" 
                                                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" 
                                                            Cursor="Hand">
                                                        <ContentPresenter x:Name="buttonContent"
                                                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                                          Content="{TemplateBinding Content}"
                                                                          ContentTemplate="{TemplateBinding ContentTemplate}"
                                                                          Margin="4,0,4,0">
                                                            <ContentPresenter.Resources>
                                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                                            </ContentPresenter.Resources>
                                                        </ContentPresenter>
                                                        <VisualStateManager.VisualStateGroups>
                                                            <VisualStateGroup x:Name="CommonStates">
                                                                <VisualState x:Name="Normal" />
                                                                <VisualState x:Name="MouseOver" />
                                                                <VisualState x:Name="Pressed"/>
                                                                <VisualState x:Name="Disabled" />
                                                            </VisualStateGroup>
                                                        </VisualStateManager.VisualStateGroups>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="true">
                                                            <Setter TargetName="buttonContent" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                                                            <Setter Property="Background" TargetName="HeaderButtonGrid" Value="{StaticResource ContentBackgroundHovered}"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed" Value="true">
                                                            <Setter Property="TextElement.Foreground" TargetName="buttonContent" Value="{StaticResource DisabledForeground}"/>
                                                            <Setter Property="Background" TargetName="HeaderButtonGrid" Value="{StaticResource ContentBackgroundPressed}"/>
                                                        </Trigger>
                                                        <Trigger Property="IsEnabled" Value="false">
                                                            <Setter TargetName="buttonContent" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Grid.Resources>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="28" />
                                    <ColumnDefinition Width="auto" />
                                    <ColumnDefinition Width="28" />
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Button x:Name="PART_PreviousButton"
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        Width="24"
                                        Height="24"
                                        Margin="8,0,8,0"
                                        HorizontalAlignment="Left"
                                        Style="{StaticResource MaterialPreviousArrowButtonStyle}" />
                                <Button x:Name="PART_HeaderButton"
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        Height="25"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Focusable="False"
                                        FontSize="10.5"
                                        FontWeight="Bold"
                                        Style="{StaticResource MaterialHeaderButtonStyle}" />
                                <Button x:Name="PART_NextButton"
                                        Grid.Row="0"
                                        Grid.Column="2"
                                        Width="24"
                                        Height="24"
                                        HorizontalAlignment="Right"
                                        Margin="0,8,8,8"
                                        Style="{StaticResource MaterialNextArrowButtonStyle}" />
                                <Grid x:Name="PART_MonthView"
                                      Grid.Row="1"
                                      Grid.ColumnSpan="3"
                                      Margin="6"
                                      HorizontalAlignment="Center"
                                      Visibility="Visible">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                </Grid>
                                <Grid x:Name="PART_YearView"
                                      Grid.Row="1"
                                      Grid.ColumnSpan="3"
                                      Margin="6,16,6,16"
                                      HorizontalAlignment="Center"
                                      Visibility="Hidden">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                </Grid>
                            </Grid>
                        </Border>
                        <Rectangle x:Name="PART_DisabledVisual"
                                   Fill="Transparent"
                                   Opacity="0"
                                   RadiusX="2"
                                   RadiusY="2"
                                   Stretch="Fill"
                                   Stroke="Transparent"
                                   StrokeThickness="1"
                                   Visibility="Collapsed" />
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="PART_DisabledVisual"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="1"
                                                         Duration="0" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>                        
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="PART_DisabledVisual" Property="Visibility" Value="Visible" />
                        </Trigger>
                        <DataTrigger Binding="{Binding DisplayMode, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared_controls:Calendar}}}" Value="Year">
                            <Setter TargetName="PART_MonthView" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="PART_YearView" Property="Visibility" Value="Visible" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding DisplayMode, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared_controls:Calendar}}}" Value="Decade">
                            <Setter TargetName="PART_MonthView" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="PART_YearView" Property="Visibility" Value="Visible" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionCalendarItemStyle}" TargetType="{x:Type primitives:CalendarItem}" />

    <Style x:Key="SyncfusionCalendarStyle" TargetType="{x:Type shared_controls:Calendar}">
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared_controls:Calendar}">
                    <StackPanel x:Name="PART_Root" HorizontalAlignment="Center">
                        <primitives:CalendarItem
                            x:Name="PART_CalendarItem"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"/>
                    </StackPanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionCalendarStyle}" TargetType="{x:Type shared_controls:Calendar}" />

    <!--  DateTimeEdit Calendar Style  -->

</ResourceDictionary>
