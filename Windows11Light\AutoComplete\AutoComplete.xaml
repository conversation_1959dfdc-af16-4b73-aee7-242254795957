<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Microsoft_Windows_Themes="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:converters="clr-namespace:Syncfusion.Windows.Tools;assembly=Syncfusion.Tools.WPF"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:local="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphEditableDropdownExpander.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <converters:PopupControlRelatedPlacementConverter x:Key="PopupPlacementConverter" />
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

    <SolidColorBrush x:Key="AutoComplete.Static.Border" Color="#9E9E9E" />

    <SolidColorBrush x:Key="AutoComplete.Focused.Border" Color="#0279FF" />

    <SolidColorBrush x:Key="AutoComplete.MouseOver.Border" Color="#757575" />

    <LinearGradientBrush x:Key="AutoComplete.Static.BorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="AutoComplete.Static.BorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="AutoComplete.Static.BorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <!--  Thumb Style  -->
    <Style x:Key="SyncfusionResizerThumbStyle" TargetType="{x:Type Thumb}">
        <Setter Property="Stylus.IsPressAndHoldEnabled" Value="false" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Grid Background="Transparent">
                        <Path
                            Width="8"
                            Height="8"
                            Data="M36.396,36.017 L47.901,36.017 47.901,47.521999 36.396,47.521999 z M18.198,36.017 L29.716,36.017 29.716,47.521999 18.198,47.521999 z M0,36.017 L11.511999,36.017 11.511999,47.521999 0,47.521999 z M36.396,18.191001 L47.901,18.191001 47.901,29.696 36.396,29.696 z M18.198,18.191 L29.716,18.191 29.716,29.696 18.198,29.696 z M36.396,0 L47.901,0 47.901,11.512 36.396,11.512 z"
                            Fill="{StaticResource IconColorDisabled}"
                            Stretch="Fill" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Auto Complete Style  -->
    <Style x:Key="SyncfusionAutoCompleteStyle" TargetType="{x:Type local:AutoComplete}">
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="ScrollViewer.CanContentScroll" Value="True" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}" />
        <Setter Property="BorderBrush" Value="{StaticResource AutoComplete.Static.BorderBrush}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="Local" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:AutoComplete}">
                    <Border
                        Name="MainBorder"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="4">
                        <Grid Name="ContentGrid">
                            <Grid.RowDefinitions>
                                <RowDefinition />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="30*" />
                                <ColumnDefinition Width="Auto" MinWidth="13" />
                            </Grid.ColumnDefinitions>
                            <local:VistaSpecificProgressBar x:Name="PART_VistaProgressBar" Grid.Column="0" />
                            <TextBox
                                Name="PART_EditableTextBox"
                                Grid.Column="0"
                                Margin="1,0,1,0"
                                Padding="{TemplateBinding Padding}"
                                VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                Background="Transparent"
                                BorderThickness="0"
                                FontFamily="{TemplateBinding FontFamily}"
                                FontSize="{TemplateBinding FontSize}"
                                FontStretch="{TemplateBinding FontStretch}"
                                FontStyle="{TemplateBinding FontStyle}"
                                FontWeight="{TemplateBinding FontWeight}"
                                Foreground="{TemplateBinding Foreground}"
                                Text="{Binding Path=Text, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type local:AutoComplete}}}" />
                            <ToggleButton
                                Name="PART_CheckButton"
                                Grid.Column="1"
                                Width="20"
                                IsChecked="False"
                                IsTabStop="False"
                                SnapsToDevicePixels="True"
                                Style="{StaticResource WPFGlyphEditableDropdownExpanderStyle}"
                                Visibility="{Binding Path=EnableDropDown, RelativeSource={RelativeSource AncestorType={x:Type local:AutoComplete}}, Converter={StaticResource BooleanToVisibilityConverter}}" >
                                <ToggleButton.Margin>
                                    <Thickness>2</Thickness>
                                </ToggleButton.Margin>
                            </ToggleButton>
                            <Popup
                                x:Name="PART_Popup"
                                AllowsTransparency="True"
                                Focusable="True"
                                IsOpen="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type local:AutoComplete}}}"
                                Placement="{Binding Path=PopupPlacement, Mode=OneWay, RelativeSource={RelativeSource AncestorType={x:Type local:AutoComplete}}, Converter={StaticResource PopupPlacementConverter}}"
                                PlacementTarget="{Binding ElementName=MainBorder}"
                                PopupAnimation="Slide"
                                SnapsToDevicePixels="True">
                                <Border
                                    x:Name="PopupBorder"
                                    Margin="16 1 16 16"
                                    Background="{StaticResource PopupBackground}"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    BorderThickness="1"
                                    Effect="{StaticResource Default.ShadowDepth4}"
                                    CornerRadius="4">
                                    <Grid HorizontalAlignment="Stretch" Background="{StaticResource PopupBackground}">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="13" />
                                        </Grid.RowDefinitions>
                                        <ListBox
                                            Name="PART_Container"
                                            Grid.Row="0"
                                            Width="{TemplateBinding ActualWidth}"
                                            Height="{TemplateBinding MaxDropHeight}"
                                            Margin="0"
                                            HorizontalAlignment="Stretch"
                                            BorderThickness="0"
                                            Focusable="True"
                                            IsSynchronizedWithCurrentItem="True"
                                            KeyboardNavigation.DirectionalNavigation="Cycle"
                                            SelectionMode="{TemplateBinding SelectionMode}"
                                            SnapsToDevicePixels="True" />
                                        <Thumb
                                            x:Name="ContainerThumb"
                                            Grid.Row="1"
                                            HorizontalAlignment="Right"
                                            VerticalAlignment="Bottom"
                                            Style="{StaticResource SyncfusionResizerThumbStyle}" />
                                    </Grid>
                                </Border>
                            </Popup>
                            <Popup
                                Name="HistoryPopup"
                                AllowsTransparency="True"
                                Focusable="True"
                                IsOpen="{Binding Path=IsHistoryDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type local:AutoComplete}}}"
                                Placement="{Binding Path=PopupPlacement, Mode=OneWay, RelativeSource={RelativeSource AncestorType={x:Type local:AutoComplete}}, Converter={StaticResource PopupPlacementConverter}}"
                                PlacementTarget="{Binding ElementName=MainBorder}"
                                PopupAnimation="Slide"
                                SnapsToDevicePixels="True">
                                <Border
                                    x:Name="HistoryPopupBorder"
                                    Margin="16 1 16 16"
                                    Background="{StaticResource PopupBackground}"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    BorderThickness="1"
                                    Effect="{StaticResource Default.ShadowDepth4}"
                                    CornerRadius="4">
                                    <Grid Background="{StaticResource PopupBackground}">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="13" />
                                        </Grid.RowDefinitions>
                                        <ListBox
                                            Name="PART_HistoryContainer"
                                            Grid.Row="0"
                                            Width="{TemplateBinding ActualWidth}"
                                            Height="{TemplateBinding HistoryListHeight}"
                                            Margin="0"
                                            BorderThickness="0"
                                            Focusable="True"
                                            IsSynchronizedWithCurrentItem="True"
                                            KeyboardNavigation.DirectionalNavigation="Cycle"
                                            SelectionMode="{TemplateBinding SelectionMode}"
                                            SnapsToDevicePixels="True" />
                                        <Thumb
                                            x:Name="HistoryThumb"
                                            Grid.Row="1"
                                            HorizontalAlignment="Right"
                                            VerticalAlignment="Bottom"
                                            Style="{StaticResource SyncfusionResizerThumbStyle}" />
                                    </Grid>
                                </Border>
                            </Popup>
                        </Grid>
                    </Border>

                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsHistory" Value="True">
                            <Setter TargetName="PART_CheckButton" Property="IsChecked" Value="{Binding Path=IsHistoryDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type local:AutoComplete}}}" />
                        </Trigger>
                        <Trigger Property="IsHistory" Value="False">
                            <Setter TargetName="PART_CheckButton" Property="IsChecked" Value="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type local:AutoComplete}}}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="MainBorder" Property="BorderBrush" Value="{StaticResource AutoComplete.Static.BorderBrushHovered}" />
                            <Setter TargetName="MainBorder" Property="Background" Value="{StaticResource ContentBackgroundAlt5}"/>
                        </Trigger>
                        <Trigger Property="SelectionMode" Value="Single">
                            <Setter TargetName="PART_Popup" Property="StaysOpen" Value="False" />
                        </Trigger>
                        <Trigger Property="SelectionMode" Value="Multiple">
                            <Setter TargetName="PART_Popup" Property="StaysOpen" Value="False" />
                        </Trigger>
                        <Trigger Property="SelectionMode" Value="Extended">
                            <Setter TargetName="PART_Popup" Property="StaysOpen" Value="True" />
                        </Trigger>
                        <Trigger Property="IsDropDownOpen" Value="True">
                            <Setter TargetName="MainBorder" Property="BorderBrush" Value="{StaticResource AutoComplete.Static.BorderBrushFocused}" />
                            <Setter TargetName="MainBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="MainBorder" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}" />
                        </Trigger>
                        <Trigger SourceName="PART_EditableTextBox" Property="IsKeyboardFocused" Value="True">
                            <Setter TargetName="MainBorder" Property="BorderBrush" Value="{StaticResource AutoComplete.Static.BorderBrushFocused}" />
                            <Setter TargetName="MainBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="MainBorder" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}" />
                            <Setter Property="Padding" Value="0,0,0,-1"/>
                            <Setter TargetName="PART_CheckButton" Property="Padding" Value="0,0,0,-1"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="MainBorder" Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter TargetName="PART_EditableTextBox" Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter TargetName="PART_EditableTextBox" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="MainBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionAutoCompleteStyle}" TargetType="{x:Type local:AutoComplete}" />
</ResourceDictionary>
