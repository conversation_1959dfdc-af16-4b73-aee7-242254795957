D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\AirMonitor.WPF.exe
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\AirMonitor.WPF.deps.json
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\AirMonitor.WPF.runtimeconfig.json
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\AirMonitor.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\AirMonitor.WPF.pdb
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\AutoMapper.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\CommunityToolkit.Mvvm.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\FluentValidation.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Data.Sqlite.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.EntityFrameworkCore.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.EntityFrameworkCore.Relational.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.EntityFrameworkCore.Sqlite.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Caching.Abstractions.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Caching.Memory.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Binder.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.CommandLine.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Json.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.UserSecrets.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyModel.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Diagnostics.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Diagnostics.Abstractions.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Hosting.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Hosting.Abstractions.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Configuration.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Console.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Debug.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.EventLog.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.EventSource.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Options.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Options.ConfigurationExtensions.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Microsoft.Extensions.Primitives.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Polly.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Polly.Core.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Serilog.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Serilog.Extensions.Hosting.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Serilog.Extensions.Logging.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Serilog.Sinks.File.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\SQLitePCLRaw.batteries_v2.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\SQLitePCLRaw.core.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\SQLitePCLRaw.provider.e_sqlite3.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Syncfusion.Data.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Syncfusion.Licensing.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Syncfusion.SfBusyIndicator.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Syncfusion.SfChart.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Syncfusion.SfGauge.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Syncfusion.SfGrid.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Syncfusion.SfInput.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Syncfusion.SfScheduler.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Syncfusion.SfSkinManager.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Syncfusion.Shared.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Syncfusion.Themes.Windows11Dark.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\Syncfusion.Themes.Windows11Light.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\System.Diagnostics.DiagnosticSource.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\System.Diagnostics.EventLog.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\System.IO.Pipelines.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\System.IO.Ports.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\System.Text.Encodings.Web.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\System.Text.Json.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\android-arm\native\libSystem.IO.Ports.Native.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libSystem.IO.Ports.Native.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\android-x64\native\libSystem.IO.Ports.Native.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\android-x86\native\libSystem.IO.Ports.Native.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libSystem.IO.Ports.Native.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libSystem.IO.Ports.Native.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-bionic-arm64\native\libSystem.IO.Ports.Native.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-bionic-x64\native\libSystem.IO.Ports.Native.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-musl-arm\native\libSystem.IO.Ports.Native.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-musl-arm64\native\libSystem.IO.Ports.Native.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-musl-x64\native\libSystem.IO.Ports.Native.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libSystem.IO.Ports.Native.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\maccatalyst-arm64\native\libSystem.IO.Ports.Native.dylib
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\maccatalyst-x64\native\libSystem.IO.Ports.Native.dylib
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\osx-arm64\native\libSystem.IO.Ports.Native.dylib
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\osx-x64\native\libSystem.IO.Ports.Native.dylib
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\browser-wasm\nativeassets\net8.0\e_sqlite3.a
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libe_sqlite3.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libe_sqlite3.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-armel\native\libe_sqlite3.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-mips64\native\libe_sqlite3.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-musl-arm\native\libe_sqlite3.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-musl-arm64\native\libe_sqlite3.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-musl-s390x\native\libe_sqlite3.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-musl-x64\native\libe_sqlite3.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-ppc64le\native\libe_sqlite3.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-s390x\native\libe_sqlite3.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libe_sqlite3.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\linux-x86\native\libe_sqlite3.so
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\osx-arm64\native\libe_sqlite3.dylib
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\osx-x64\native\libe_sqlite3.dylib
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\win-arm\native\e_sqlite3.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\win-arm64\native\e_sqlite3.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\win-x64\native\e_sqlite3.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\win-x86\native\e_sqlite3.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.Messages.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\unix\lib\net8.0\System.IO.Ports.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.IO.Ports.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\AirMonitor.Core.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\AirMonitor.Infrastructure.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\AirMonitor.Services.dll
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\AirMonitor.Services.pdb
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\AirMonitor.Core.pdb
D:\05 AirMonitor\src\AirMonitor.WPF\bin\Debug\net8.0-windows\AirMonitor.Infrastructure.pdb
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\AirMonitor.WPF.csproj.AssemblyReference.cache
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\MainWindow.g.cs
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\App.g.cs
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\AirMonitor.WPF_MarkupCompile.cache
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\AirMonitor.WPF_MarkupCompile.lref
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\MainWindow.baml
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\AirMonitor.WPF.g.resources
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\AirMonitor.WPF.GeneratedMSBuildEditorConfig.editorconfig
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\AirMonitor.WPF.AssemblyInfoInputs.cache
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\AirMonitor.WPF.AssemblyInfo.cs
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\AirMonitor.WPF.csproj.CoreCompileInputs.cache
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\AirMonit.9E0E503A.Up2Date
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\AirMonitor.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\refint\AirMonitor.WPF.dll
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\AirMonitor.WPF.pdb
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\AirMonitor.WPF.genruntimeconfig.cache
D:\05 AirMonitor\src\AirMonitor.WPF\obj\Debug\net8.0-windows\ref\AirMonitor.WPF.dll
