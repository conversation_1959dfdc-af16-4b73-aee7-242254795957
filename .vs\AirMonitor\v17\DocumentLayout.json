{"Version": 1, "WorkspaceRootPath": "D:\\05 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|d:\\05 airmonitor\\src\\airmonitor.licensegenerator\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|solutionrelative:src\\airmonitor.licensegenerator\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|d:\\05 airmonitor\\src\\airmonitor.licensegenerator\\converters\\booleantocolorconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|solutionrelative:src\\airmonitor.licensegenerator\\converters\\booleantocolorconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|d:\\05 airmonitor\\src\\airmonitor.licensegenerator\\views\\licensevalidatorview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|solutionrelative:src\\airmonitor.licensegenerator\\views\\licensevalidatorview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|d:\\05 airmonitor\\src\\airmonitor.licensegenerator\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|solutionrelative:src\\airmonitor.licensegenerator\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|d:\\05 airmonitor\\src\\airmonitor.licensegenerator\\views\\licensegeneratorview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|solutionrelative:src\\airmonitor.licensegenerator\\views\\licensegeneratorview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|d:\\05 airmonitor\\src\\airmonitor.licensegenerator\\services\\cryptoservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|solutionrelative:src\\airmonitor.licensegenerator\\services\\cryptoservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|d:\\05 airmonitor\\src\\airmonitor.licensegenerator\\viewmodels\\templatemanagerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|solutionrelative:src\\airmonitor.licensegenerator\\viewmodels\\templatemanagerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|d:\\05 airmonitor\\src\\airmonitor.licensegenerator\\views\\templatemanagerview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{903EC151-29D9-45F9-899F-28122B61D159}|src\\AirMonitor.LicenseGenerator\\AirMonitor.LicenseGenerator.csproj|solutionrelative:src\\airmonitor.licensegenerator\\views\\templatemanagerview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{DD02700B-5B33-4FD3-B411-C9C83B52FC26}|src\\AirMonitor.WPF\\AirMonitor.WPF.csproj|d:\\05 airmonitor\\src\\airmonitor.wpf\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{DD02700B-5B33-4FD3-B411-C9C83B52FC26}|src\\AirMonitor.WPF\\AirMonitor.WPF.csproj|solutionrelative:src\\airmonitor.wpf\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 10, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "BooleanToColorConverter.cs", "DocumentMoniker": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\Converters\\BooleanToColorConverter.cs", "RelativeDocumentMoniker": "src\\AirMonitor.LicenseGenerator\\Converters\\BooleanToColorConverter.cs", "ToolTip": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\Converters\\BooleanToColorConverter.cs", "RelativeToolTip": "src\\AirMonitor.LicenseGenerator\\Converters\\BooleanToColorConverter.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwQAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T06:29:07.527Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "App.xaml.cs", "DocumentMoniker": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\App.xaml.cs", "RelativeDocumentMoniker": "src\\AirMonitor.LicenseGenerator\\App.xaml.cs", "ToolTip": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\App.xaml.cs", "RelativeToolTip": "src\\AirMonitor.LicenseGenerator\\App.xaml.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAAAE4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T06:14:32.974Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "LicenseValidatorView.xaml", "DocumentMoniker": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\Views\\LicenseValidatorView.xaml", "RelativeDocumentMoniker": "src\\AirMonitor.LicenseGenerator\\Views\\LicenseValidatorView.xaml", "ToolTip": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\Views\\LicenseValidatorView.xaml", "RelativeToolTip": "src\\AirMonitor.LicenseGenerator\\Views\\LicenseValidatorView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-06T06:15:31.58Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "App.xaml", "DocumentMoniker": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\App.xaml", "RelativeDocumentMoniker": "src\\AirMonitor.LicenseGenerator\\App.xaml", "ToolTip": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\App.xaml", "RelativeToolTip": "src\\AirMonitor.LicenseGenerator\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-06T06:16:09.3Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "CryptoService.cs", "DocumentMoniker": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\Services\\CryptoService.cs", "RelativeDocumentMoniker": "src\\AirMonitor.LicenseGenerator\\Services\\CryptoService.cs", "ToolTip": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\Services\\CryptoService.cs", "RelativeToolTip": "src\\AirMonitor.LicenseGenerator\\Services\\CryptoService.cs", "ViewState": "AgIAAH4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T06:23:21.547Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "LicenseGeneratorView.xaml", "DocumentMoniker": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\Views\\LicenseGeneratorView.xaml", "RelativeDocumentMoniker": "src\\AirMonitor.LicenseGenerator\\Views\\LicenseGeneratorView.xaml", "ToolTip": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\Views\\LicenseGeneratorView.xaml", "RelativeToolTip": "src\\AirMonitor.LicenseGenerator\\Views\\LicenseGeneratorView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-06T06:15:13.391Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "TemplateManagerViewModel.cs", "DocumentMoniker": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\ViewModels\\TemplateManagerViewModel.cs", "RelativeDocumentMoniker": "src\\AirMonitor.LicenseGenerator\\ViewModels\\TemplateManagerViewModel.cs", "ToolTip": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\ViewModels\\TemplateManagerViewModel.cs", "RelativeToolTip": "src\\AirMonitor.LicenseGenerator\\ViewModels\\TemplateManagerViewModel.cs", "ViewState": "AgIAABQAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T06:16:51.845Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "TemplateManagerView.xaml", "DocumentMoniker": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\Views\\TemplateManagerView.xaml", "RelativeDocumentMoniker": "src\\AirMonitor.LicenseGenerator\\Views\\TemplateManagerView.xaml", "ToolTip": "D:\\05 AirMonitor\\src\\AirMonitor.LicenseGenerator\\Views\\TemplateManagerView.xaml", "RelativeToolTip": "src\\AirMonitor.LicenseGenerator\\Views\\TemplateManagerView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-06T06:15:40.188Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "App.xaml", "DocumentMoniker": "D:\\05 AirMonitor\\src\\AirMonitor.WPF\\App.xaml", "RelativeDocumentMoniker": "src\\AirMonitor.WPF\\App.xaml", "ToolTip": "D:\\05 AirMonitor\\src\\AirMonitor.WPF\\App.xaml", "RelativeToolTip": "src\\AirMonitor.WPF\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-06T06:12:00.068Z", "EditorCaption": ""}]}]}]}