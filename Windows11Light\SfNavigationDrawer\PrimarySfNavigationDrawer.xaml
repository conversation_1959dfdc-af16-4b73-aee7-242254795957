<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:Syncfusion.Windows.Converters;assembly=Syncfusion.Shared.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:Syncfusion.UI.Xaml.NavigationDrawer;assembly=Syncfusion.SfNavigationDrawer.WPF"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:provider="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <provider:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light" />
        <provider:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Separator.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Label.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <local:ObjectNullToVisibilityConverter x:Key="ObjectNullToVisibilityConverter" />
    <local:CollectionVisibilityConverter x:Key="CollectionVisibilityConverter" />
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

    <Geometry x:Key="HamburgerPath">M0.5,10 L15.5,10 C15.776,10 16,10.224 16,10.5 16,10.776 15.776,11 15.5,11 L0.5,11 C0.22400001,11 0,10.776 0,10.5 0,10.224 0.22400001,10 0.5,10 z M0.5,5 L15.5,5 C15.776001,5 16,5.223999 16,5.5 16,5.776001 15.776001,6 15.5,6 L0.5,6 C0.22399902,6 0,5.776001 0,5.5 0,5.223999 0.22399902,5 0.5,5 z M0.5,0 L15.5,0 C15.776,0 16,0.22399998 16,0.5 16,0.77600002 15.776,1 15.5,1 L0.5,1 C0.22400001,1 0,0.77600002 0,0.5 0,0.22399998 0.22400001,0 0.5,0 z</Geometry>

    <Style x:Key="SyncfusionPrimaryNavigationItemExpanderToggleButtonStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="Focusable" Value="False" />
        <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border x:Name="ExpanderBorder" 
                            Background="Transparent"
                            CornerRadius="0">
                        <Grid>
                            <Path
                                x:Name="ExpanderPath"
                                Width="10"
                                Height="6"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Data="M1 1L8.5 8.5L16 1"
                                Stretch="Fill"
                                Stroke="{TemplateBinding Foreground}"
                                StrokeThickness="1"
                                Visibility="{TemplateBinding ContentTemplate,
                                                             Converter={StaticResource ObjectNullToVisibilityConverter},
                                                             ConverterParameter=Path}" />
                            <ContentControl
                                x:Name="ExpanderTemplate"
                                ContentTemplate="{TemplateBinding ContentTemplate}"
                                Visibility="{TemplateBinding ContentTemplate,
                                                             Converter={StaticResource ObjectNullToVisibilityConverter},
                                                             ConverterParameter=Template}" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ExpanderPath" Property="RenderTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="180" CenterX="5" CenterY="2" />
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ExpanderBorder" Property="Background" Value="{StaticResource PrimaryColorDark1}" />
                            <Setter TargetName="ExpanderPath" Property="Stroke" Value="{StaticResource PrimaryForeground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ExpanderBorder" Property="Background" Value="{StaticResource PrimaryColorDark2}" />
                            <Setter TargetName="ExpanderPath" Property="Stroke" Value="{StaticResource PrimaryForegroundOpacity1}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ExpanderBorder" Property="Background" Value="{StaticResource PrimaryBackground}" />
                            <Setter TargetName="ExpanderPath" Property="Stroke" Value="{StaticResource PrimaryForegroundDisabled}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="SyncfusionPrimaryNavigationItemTabButtonControlTemplate" TargetType="local:NavigationItem">
        <Grid Focusable="False">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Border
                x:Name="NavigationItemHeaderBorder"
                HorizontalAlignment="Stretch"
                
                CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                Background="{TemplateBinding Background}"
                Focusable="True"
                Margin="4,3,4,2">
                <Grid x:Name="NavigationItemHeaderGrid">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" MinWidth="{Binding Path=CompactModeWidth, RelativeSource={RelativeSource AncestorType={x:Type local:SfNavigationDrawer}}}" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ToggleButton
                        x:Name="NavigationItemExpander"
                        Grid.Column="1"
                        Width="38"
                        Height="NaN"
                        Margin="0"
                        Foreground="{TemplateBinding Foreground}"
                        ClickMode="Press"
                        ContentTemplate="{Binding ExpanderTemplate, RelativeSource={RelativeSource TemplatedParent}}"
                        IsChecked="{Binding IsExpanded, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                        Style="{StaticResource SyncfusionPrimaryNavigationItemExpanderToggleButtonStyle}"
                        Visibility="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource CollectionVisibilityConverter}}" />
                    <StackPanel Orientation="Horizontal">
                        <ContentPresenter
                            Width="{Binding Path=CompactModeWidth, RelativeSource={RelativeSource AncestorType={x:Type local:SfNavigationDrawer}}}"
                            Height="25"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding Icon}"
                            ContentTemplate="{TemplateBinding IconTemplate}"
                            Margin="-4,2,4,2">
                        </ContentPresenter>
                        <ContentPresenter
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding Header}"
                            ContentTemplate="{TemplateBinding HeaderTemplate}"
                            Margin="-4,0,15,0 ">
                        </ContentPresenter>
                    </StackPanel>
                </Grid>
                <Border.InputBindings>
                    <MouseBinding
                        Command="{Binding Command, RelativeSource={RelativeSource TemplatedParent}}"
                        CommandParameter="{Binding CommandParameter, RelativeSource={RelativeSource TemplatedParent}}"
                        MouseAction="LeftClick" />
                </Border.InputBindings>
            </Border>
            <Popup
                x:Name="NavigationItemSubItemsPopup"
                AllowsTransparency="True"
                StaysOpen="False"
                UseLayoutRounding="True">
                <Grid x:Name="NavigationItemSubItemsGrid" Background="Transparent">
                    <Border
                        Background="{Binding Path=DrawerBackground, RelativeSource={RelativeSource AncestorType={x:Type local:SfNavigationDrawer}}}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        Effect="{StaticResource Default.ShadowDepth6}"
                        BorderThickness="{StaticResource Windows11Light.BorderThickness1}" 
                        CornerRadius="0"
                        Padding="0"
                        Margin="3,3,16,16">
                        <local:NavigationItemsView
                            x:Name="NavigationItemPopupItemsControl"
                            ItemTemplate="{TemplateBinding ItemTemplate}"
                            ItemTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                            ItemsSource="{TemplateBinding Items}" />
                    </Border>
                </Grid>
            </Popup>
            <Rectangle
                x:Name="NavigationItemSelectionRectangle"
                Width="3"
                Height="0"
                HorizontalAlignment="Left"
                Fill="{TemplateBinding SelectionBackground}"
                Focusable="False"
                Visibility="Collapsed">
            </Rectangle>
            <local:NavigationItemsView
                x:Name="NavigationItemSubItemsItemsControl"
                Grid.Row="1"
                HorizontalAlignment="Stretch"
                Focusable="False"
                IsTabStop="False"
                ItemContainerStyle="{TemplateBinding ItemContainerStyle}"
                ItemTemplate="{TemplateBinding ItemTemplate}"
                ItemTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                ItemsSource="{TemplateBinding Items}" />
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger SourceName="NavigationItemHeaderBorder" Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource PrimaryColorDark1}" />
                <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsSelected" Value="True" />
                    <Condition Property="ItemType" Value="Tab" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter TargetName="NavigationItemSelectionRectangle" Property="Visibility" Value="Visible" />
                    <Setter Property="Background" Value="{StaticResource PrimaryColorDark2}" />
                    <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                </MultiTrigger.Setters>
            </MultiTrigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="Transparent" />
            </Trigger>
            <Trigger Property="provider:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter TargetName="NavigationItemHeaderBorder" Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionPrimaryNavigationItemHeaderControlTemplate" TargetType="local:NavigationItem">
        <Grid
            HorizontalAlignment="Stretch"
            Focusable="False"
            Visibility="{Binding Path=IsOpen, Converter={StaticResource BooleanToVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type local:SfNavigationDrawer}}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <ContentPresenter
                Width="{Binding Path=CompactModeWidth, RelativeSource={RelativeSource AncestorType={x:Type local:SfNavigationDrawer}}}"
                Height="25"
                Margin="0,5,5,5"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Center"
                Content="{TemplateBinding Icon}"
                ContentTemplate="{TemplateBinding IconTemplate}"
                Visibility="{TemplateBinding Icon,
                                             Converter={StaticResource ObjectNullToVisibilityConverter},
                                             ConverterParameter=Template}" />
            <Label
                Grid.Column="1"
                Margin="5"
                HorizontalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                Content="{TemplateBinding Header}"
                ContentTemplate="{TemplateBinding HeaderTemplate}"
                FontFamily="{TemplateBinding FontFamily}"
                FontSize="{TemplateBinding FontSize}"
                FontWeight="{TemplateBinding FontWeight}"
                Foreground="{StaticResource PrimaryForeground}" />
        </Grid>
    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionPrimaryNavigationItemSeparatorControlTemplate" TargetType="local:NavigationItem">
        <Grid Height="25">
            <Separator
                Height="1"
                Margin="2,2,4,2"
                HorizontalAlignment="Stretch"
                Background="{StaticResource PrimaryColorLight2}"
                Focusable="False" />
        </Grid>
    </ControlTemplate>

    <Style x:Key="SyncfusionPrimaryDefaultNavigationItemStyle" TargetType="local:NavigationItem">
        <Setter Property="Background" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="SelectionBackground" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
        <Style.Triggers>
            <Trigger Property="ItemType" Value="Tab">
                <Setter Property="Focusable" Value="False" />
                <Setter Property="Template" Value="{StaticResource SyncfusionPrimaryNavigationItemTabButtonControlTemplate}" />
            </Trigger>
            <Trigger Property="ItemType" Value="Button">
                <Setter Property="Focusable" Value="False" />
                <Setter Property="Template" Value="{StaticResource SyncfusionPrimaryNavigationItemTabButtonControlTemplate}" />
            </Trigger>
            <Trigger Property="ItemType" Value="Header">
                <Setter Property="IsTabStop" Value="False" />
                <Setter Property="Focusable" Value="False" />
                <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightMedium}" />
                <Setter Property="Template" Value="{StaticResource SyncfusionPrimaryNavigationItemHeaderControlTemplate}" />
            </Trigger>
            <Trigger Property="ItemType" Value="Separator">
                <Setter Property="IsTabStop" Value="False" />
                <Setter Property="Focusable" Value="False" />
                <Setter Property="Template" Value="{StaticResource SyncfusionPrimaryNavigationItemSeparatorControlTemplate}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="{StaticResource PrimaryForegroundDisabled}" />
                <Setter Property="Background" Value="Transparent" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionPrimaryNavigationDrawerToggleButtonStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border
                        x:Name="HamburgurButtonBorder"
                        Background="Transparent"
                        BorderBrush="Transparent"
                        BorderThickness="0"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                        Margin="1,3,8,3">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="HamburgurButtonBorder" Property="Background" Value="{StaticResource PrimaryColorDark1}" />
                            <Setter TargetName="HamburgurButtonBorder" Property="BorderBrush" Value="{StaticResource PrimaryColorDark1}" />
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="HamburgurButtonBorder" Property="Background" Value="{StaticResource PrimaryBackground}" />
                            <Setter TargetName="HamburgurButtonBorder" Property="BorderBrush" Value="{StaticResource PrimaryBackground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="HamburgurButtonBorder" Property="Background" Value="{StaticResource PrimaryColorDark2}" />
                            <Setter TargetName="HamburgurButtonBorder" Property="BorderBrush" Value="{StaticResource PrimaryColorDark2}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="HamburgurButtonBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="HamburgurButtonBorder" Property="BorderBrush" Value="Transparent" />
                        </Trigger>
                        <Trigger Property="provider:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                            <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="SyncfusionPrimarySfNavigationDrawerExpandedCompactModeControlTemplate" TargetType="local:SfNavigationDrawer">
        <Grid
            Name="NavigationDrawerMainGrid"
            Background="Transparent"
            ClipToBounds="True">
            <ContentPresenter
                Name="ContentViewContentPresenter"
                Content="{Binding ContentView, RelativeSource={RelativeSource TemplatedParent}}"
                Focusable="False" />
            <Grid
                Name="NavigationDrawerTransparentViewGrid"
                Background="Transparent"
                Focusable="False"
                Visibility="Collapsed" />
            <Border
                HorizontalAlignment="Stretch"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="1">
                <Grid
                    Name="DrawerContentGrid"
                    Width="{Binding CompactModeWidth, RelativeSource={RelativeSource TemplatedParent}}"
                    HorizontalAlignment="Left"
                    Background="{TemplateBinding DrawerBackground}">
                    <Border
                        Margin="0,0,-1,0"
                        HorizontalAlignment="Stretch"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0,0,1,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid Name="NavigationDrawerHeaderView" Background="{TemplateBinding DrawerBackground}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <ToggleButton
                                    x:Name="HamburgurButton"
                                    Width="{Binding CompactModeWidth, RelativeSource={RelativeSource TemplatedParent}}"
                                    Height="35"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Stretch"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    Margin="3,2,1,0"
                                    ContentTemplate="{Binding ToggleButtonIconTemplate, RelativeSource={RelativeSource TemplatedParent}}"
                                    IsChecked="{Binding IsOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                    Style="{StaticResource SyncfusionPrimaryNavigationDrawerToggleButtonStyle}"
                                    Visibility="{Binding IsToggleButtonVisible, Converter={StaticResource BooleanToVisibilityConverter}, RelativeSource={RelativeSource TemplatedParent}}">
                                    <Grid>
                                        <Path
                                            x:Name="HamburgerPath"
                                            Width="16"
                                            Height="11"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Data="{StaticResource HamburgerPath}"
                                            Fill="{StaticResource PrimaryForeground}"
                                            Stretch="Fill"
                                            Visibility="{Binding ToggleButtonIconTemplate, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ObjectNullToVisibilityConverter}, ConverterParameter=Path}" >
                                            <Path.RenderTransformOrigin>
                                                <Point>0.5,0.5</Point>
                                            </Path.RenderTransformOrigin>
                                            <Path.RenderTransform>
                                                <ScaleTransform ScaleX="1" x:Name="HamburgerAction"/>
                                            </Path.RenderTransform>
                                        </Path>
                                    </Grid>
                                </ToggleButton>
                                <ContentControl 
                                    IsTabStop="False"
                                    Grid.Column="1"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    FontSize="{TemplateBinding FontSize}"
                                    ContentTemplate="{Binding ToggleButtonContentTemplate, RelativeSource={RelativeSource TemplatedParent}}"
                                    Foreground="{StaticResource PrimaryForeground}" />
                            </Grid>
                            <Grid Name="NavigationDrawerContentView" Grid.Row="1">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <local:NavigationView
                                        x:Name="DrawerContent"
                                        HorizontalAlignment="Stretch"
                                        Background="{TemplateBinding DrawerBackground}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="0"
                                        Focusable="False"
                                        IsTabStop="False"
                                        ItemContainerStyle="{Binding ItemContainerStyle, RelativeSource={RelativeSource TemplatedParent}}"
                                        ItemTemplate="{Binding ItemTemplate, RelativeSource={RelativeSource TemplatedParent}}"
                                        ItemTemplateSelector="{Binding ItemTemplateSelector, RelativeSource={RelativeSource TemplatedParent}}"
                                        ItemsSource="{Binding Items, RelativeSource={RelativeSource TemplatedParent}}"
                                        ScrollViewer.HorizontalScrollBarVisibility="Disabled" />
                                </ScrollViewer>
                            </Grid>
                            <Grid
                                Name="NavigationDrawerFooterView"
                                Grid.Row="2"
                                Background="{TemplateBinding DrawerBackground}">
                                <local:NavigationView Focusable="False" ItemsSource="{Binding FooterItems, RelativeSource={RelativeSource TemplatedParent}}" />
                            </Grid>
                        </Grid>
                    </Border>
                </Grid>
            </Border>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger SourceName="HamburgurButton" Property="IsChecked" Value="True">
                <Setter TargetName="HamburgerPath" Property="Fill" Value="{StaticResource PrimaryForeground}" />
            </Trigger>
            <Trigger SourceName="HamburgurButton" Property="IsMouseOver" Value="True">
                <Setter TargetName="HamburgerPath" Property="Fill" Value="{StaticResource PrimaryForeground}" />
            </Trigger>
            <Trigger SourceName="HamburgurButton" Property="IsPressed" Value="True">
                <Setter TargetName="HamburgerPath" Property="Fill" Value="{StaticResource PrimaryForegroundOpacity1}" />
            </Trigger>
            <Trigger SourceName="HamburgurButton" Property="IsEnabled" Value="False">
                <Setter TargetName="HamburgerPath" Property="Fill" Value="{StaticResource PrimaryForegroundDisabled}" />
            </Trigger>
            <EventTrigger RoutedEvent="PreviewMouseLeftButtonDown" SourceName="HamburgurButton">
                <BeginStoryboard x:Name="HamburgerAnimation">
                    <Storyboard >
                        <DoubleAnimation Storyboard.TargetName="HamburgerAction" Storyboard.TargetProperty="ScaleX" From="1" To="0.5" Duration="0:0:0:.167"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
            <EventTrigger RoutedEvent="PreviewMouseLeftButtonUp" SourceName="HamburgurButton">
                <BeginStoryboard>
                    <Storyboard >
                        <DoubleAnimation Storyboard.TargetName="HamburgerAction" Storyboard.TargetProperty="ScaleX" From="0.5" To="1" Duration="0:0:0:.167"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionPrimarySfNavigationDrawerControlTemplate" TargetType="local:SfNavigationDrawer">
        <Grid
            Name="NavigationDrawerMainGrid"
            Background="Transparent"
            ClipToBounds="True">
            <Grid
                Name="NavigationDrawerContentViewGrid"
                Background="Transparent"
                Visibility="Visible">
                <ContentPresenter Name="ContentViewContentPresenter" Content="{Binding ContentView, RelativeSource={RelativeSource TemplatedParent}}" />
            </Grid>
            <Grid
                Name="NavigationDrawerTransparentViewGrid"
                Background="Transparent"
                Visibility="Collapsed" />
            <Grid
                Name="DrawerView"
                Width="{Binding DrawerWidth, RelativeSource={RelativeSource TemplatedParent}}"
                Height="{Binding DrawerHeight, RelativeSource={RelativeSource TemplatedParent}}"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Background="{TemplateBinding Background}"
                Visibility="Collapsed">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Grid Name="NavigationDrawerHeaderView">
                    <ContentPresenter Name="HeaderContentPresenter" Content="{Binding DrawerHeaderView, RelativeSource={RelativeSource TemplatedParent}}" />
                </Grid>
                <Grid Name="NavigationDrawerContentView" Grid.Row="1">
                    <ContentPresenter Name="DrawerContentPresenter" Content="{Binding DrawerContentView, RelativeSource={RelativeSource TemplatedParent}}" />
                </Grid>
                <Grid Name="NavigationDrawerFooterView" Grid.Row="2">
                    <ContentPresenter Name="FooterContentPresenter" Content="{Binding DrawerFooterView, RelativeSource={RelativeSource TemplatedParent}}" />
                </Grid>
            </Grid>
        </Grid>
    </ControlTemplate>

    <Style x:Key="SyncfusionPrimarySfNavigationDrawerStyle" TargetType="local:SfNavigationDrawer">
        <Setter Property="Background" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="DrawerBackground" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="IsTabStop" Value="False" />
        <Style.Triggers>
            <Trigger Property="DisplayMode" Value="Default">
                <Setter Property="Template" Value="{StaticResource SyncfusionPrimarySfNavigationDrawerControlTemplate}" />
            </Trigger>
            <Trigger Property="DisplayMode" Value="Compact">
                <Setter Property="Template" Value="{StaticResource SyncfusionPrimarySfNavigationDrawerExpandedCompactModeControlTemplate}" />
            </Trigger>
            <Trigger Property="DisplayMode" Value="Expanded">
                <Setter Property="Template" Value="{StaticResource SyncfusionPrimarySfNavigationDrawerExpandedCompactModeControlTemplate}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="Transparent" />
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
