<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Microsoft_Windows_Aero="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:Microsoft_Windows_Luna="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Luna"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:po="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:tools_controls="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
    xmlns:tools_resources="clr-namespace:Syncfusion.Windows.Tools.Controls.Resources;assembly=Syncfusion.Tools.WPF"
    xmlns:windows_shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF">

    <!--  Brushes  -->

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Menu.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphPrimaryToggleButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="TabItemExt.FilledHeader.Active.CaptionButton.Static.Border" Color="#ffffff" />
    <SolidColorBrush x:Key="TabItemExt.FilledHeader.Active.CaptionButton.MouseOver.Border" Color="#ffffff" />
    
    <tools_controls:CloseButtonTypeToVisibilityConverter x:Key="TabControlExtCloseButtonTypeToVisibilityConverter" />
    <windows_shared:BooleanToVisibilityConverterEx x:Key="TabControlExtBooleanToVisibilityConverterEx" />
    <tools_controls:ImageAlignmentToDockConverter x:Key="TabControlExtImageAlignmentToDockConverter" />
    <windows_shared:ObjectNullToVisibilityConverter x:Key="TabControlExtObjectNullToVisibilityConverterEx"/>

    <DataTemplate x:Key="TabControlExtHeaderDataTemplate" DataType="{x:Type tools_controls:TabItemExt}">
        <TextBlock
            x:Name="HeaderText"
            Padding="1,0,0,1"
            Focusable="False"
            IsHitTestVisible="False"
            Text="{Binding FallbackValue=''}"
            TextTrimming="CharacterEllipsis" />
    </DataTemplate>

    <DataTemplate x:Key="TabControlExtEditableDataTemplate" DataType="{x:Type tools_controls:TabItemExt}">
        <TextBox
            x:Name="EditableHeader"
            Padding="4,1,4,1"
            Focusable="True"
            IsReadOnly="False"
            Style="{Binding Path=EditHeaderItemStyle, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}"
            Text="{Binding Path=Header, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, Mode=TwoWay}" />
    </DataTemplate>

    <DataTemplate x:Key="SyncfusionTabNewButtonDataTemplate">
        <Border Height="16" Width="16" x:Name="NewButtonContentBorder"
                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" 
                Background="Transparent" >
        <Grid >
                <Path
                        x:Name="NewButtonPath"
                        Width="9"
                        Margin="0"
                        Height="9"
                        StrokeThickness="1"
                        SnapsToDevicePixels="True"
                        Stretch="Fill" >
                    <Path.Data>
                        <PathGeometry>M12 6C12 6.125 11.9543 6.23317 11.863 6.32452C11.7716 6.41587 11.6635 6.46154 11.5385 6.46154H6.46154V11.5385C6.46154 11.6635 6.41587 11.7716 6.32452 11.863C6.23317 11.9543 6.125 12 6 12C5.875 12 5.76683 11.9543 5.67548 11.863C5.58414 11.7716 5.53846 11.6635 5.53846 11.5385V6.46154H0.46154C0.33654 6.46154 0.228367 6.41587 0.13702 6.32452C0.0456734 6.23317 0 6.125 0 6C0 5.875 0.0456734 5.76683 0.13702 5.67548C0.228367 5.58414 0.33654 5.53846 0.46154 5.53846H5.53846V0.46154C5.53846 0.33654 5.58414 0.228367 5.67548 0.13702C5.76683 0.0456734 5.875 0 6 0C6.125 0 6.23317 0.0456734 6.32452 0.13702C6.41587 0.228367 6.46154 0.33654 6.46154 0.46154V5.53846H11.5385C11.6635 5.53846 11.7716 5.58414 11.863 5.67548C11.9543 5.76683 12 5.875 12 6Z</PathGeometry>
                    </Path.Data>
                    <Path.Style>
                        <Style TargetType="Path">
                            <Setter Property="Fill" Value="{Binding Path=Foreground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}}"/>
                        </Style>
                    </Path.Style>
                </Path>
            </Grid>
        </Border>
        <DataTemplate.Triggers>
            <Trigger Property="IsMouseOver" SourceName="NewButtonContentBorder" Value="True">
                <Setter Property="Background" TargetName="NewButtonContentBorder" Value="{StaticResource ContentBackgroundHovered}"/>
                <Setter Property="BorderBrush" TargetName="NewButtonContentBorder" Value="{StaticResource ContentBackgroundHovered}"/>
            </Trigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <Style x:Key="SyncfusionNewButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0, 0, 0, 1"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="Foreground" Value="{StaticResource IconColor}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border 
                        x:Name="NewButtonBorder" 
                        CornerRadius="0"
                        Background="{TemplateBinding Background}" 
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Padding="{TemplateBinding Padding}"
                        SnapsToDevicePixels="true">
                        <ContentPresenter x:Name="contentPresenter" Focusable="False"  RecognizesAccessKey="True" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource IconColorHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource IconColorSelected}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="NewButtonBorder" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="NewButtonBorder" Property="Background" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="TabItemCaptionButtonsStyle"
        BasedOn="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
        TargetType="{x:Type ToggleButton}">
        <Setter Property="MinWidth" Value="16"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border
                        Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            RecognizesAccessKey="True"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource HoveredForeground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource SelectedForeground}" />
                        </Trigger>
                        <DataTrigger Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                        </DataTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="TabItem" />
                                <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource HoveredForeground}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="TabItem" />
                                <Condition Binding="{Binding IsPressed, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource SelectedForeground}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="TabItem" />
                                <Condition Binding="{Binding IsEnabled, RelativeSource={RelativeSource Mode=Self}}" Value="False" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="FilledHeaderTabItem" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryBackground}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TabItemExt.FilledHeader.Active.CaptionButton.Static.Border}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="FilledHeaderTabItem" />
                                <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryBackgroundOpacity1}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TabItemExt.FilledHeader.Active.CaptionButton.MouseOver.Border}" />
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="FilledHeaderTabItem" />
                                <Condition Binding="{Binding IsPressed, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryBackgroundOpacity2}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBorderSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForegroundOpacity1}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="FilledHeaderTabItem" />
                                <Condition Binding="{Binding IsEnabled, RelativeSource={RelativeSource Mode=Self}}" Value="False" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource BorderAlt4}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource BorderAlt4}" />
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="TabPanelCaptionButtonsStyle"
        BasedOn="{StaticResource WPFGlyphButtonStyle}"
        TargetType="{x:Type Button}">
        <Setter Property="MinHeight" Value="16"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            RecognizesAccessKey="True"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="TabItem" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="TabItem" />
                                <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource HoveredForeground}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="TabItem" />
                                <Condition Binding="{Binding IsPressed, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource SelectedForeground}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="TabItem" />
                                <Condition Binding="{Binding IsEnabled, RelativeSource={RelativeSource Mode=Self}}" Value="False" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="FilledHeaderTabItem" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryBackground}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TabItemExt.FilledHeader.Active.CaptionButton.Static.Border}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="FilledHeaderTabItem" />
                                <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryBackgroundOpacity1}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TabItemExt.FilledHeader.Active.CaptionButton.MouseOver.Border}" />
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="FilledHeaderTabItem" />
                                <Condition Binding="{Binding IsPressed, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryBackgroundOpacity2}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBorderSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForegroundOpacity1}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="FilledHeaderTabItem" />
                                <Condition Binding="{Binding IsEnabled, RelativeSource={RelativeSource Mode=Self}}" Value="False" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource BorderAlt4}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource BorderAlt4}" />
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionTabControlExtTabPanelAdvStyle" TargetType="{x:Type tools_controls:TabPanelAdv}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:TabPanelAdv}">
                    <Border
                        x:Name="Bord"
                        Margin="{TemplateBinding Margin}"
                        Padding="{TemplateBinding Padding}"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Top"
                        Background="{Binding TabPanelBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Transparent}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                        <Grid>
                            <Border x:Name="border"
                                BorderThickness="{TemplateBinding BorderThickness}"/>
                            <DockPanel
                                LastChildFill="False"
                                Grid.Column="5">
                                <ContentPresenter
                                    x:Name="PART_CustomItems"
                                    MaxHeight="{Binding Path=ActualHeight, ElementName=PART_TabItems}"
                                    Margin="2,0,0,0"
                                    AllowDrop="False"
                                    Content="{Binding Path=TabPanelItem, Mode=OneWay, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=''}"
                                    ContentSource="TabPanelItem"
                                    DockPanel.Dock="Right" />
                                <Button
                                    x:Name="PART_CloseButton"
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,8,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    AllowDrop="True"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    BorderThickness="0"
                                    Command="tools_controls:TabControlCommands.CloseCurrentTabItem"
                                    DockPanel.Dock="Right"
                                    Focusable="False"
                                    Style="{StaticResource TabPanelCaptionButtonsStyle}"
                                    Visibility="{Binding Path=CloseButtonType, Converter={StaticResource TabControlExtCloseButtonTypeToVisibilityConverter}, Mode=OneWay, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Collapsed}">
                                    <Button.ToolTip>
                                        <ToolTip x:Name="tooltip">
                                            <ToolTip.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                            </ToolTip.Resources>
                                            <TextBlock x:Name="tooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=CloseButtonTooltipText}" />
                                        </ToolTip>
                                    </Button.ToolTip>
                                    <Button.Content>
                                        <Path
                                            x:Name="CloseButtonPath2"
                                            Width="8"
                                            Height="8"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Fill="{Binding Foreground, ElementName=PART_CloseButton}"
                                            SnapsToDevicePixels="True"
                                            Stretch="Fill" 
                                            Data="M9.49512 8.87988L5.42773 4.8125L9.49512 0.745117C9.5817 0.658527 9.625 0.555988 9.625 0.4375C9.625 0.319012 9.5817 0.216473 9.49512 0.129883C9.40854 0.043293 9.306 0 9.1875 0C9.069 0 8.96646 0.043293 8.87988 0.129883L4.8125 4.19727L0.745117 0.129883C0.658539 0.043293 0.556 0 0.4375 0C0.319 0 0.216461 0.043293 0.129883 0.129883C0.0433044 0.216473 0 0.319012 0 0.4375C0 0.555988 0.0433044 0.658527 0.129883 0.745117L4.19727 4.8125L0.129883 8.87988C0.0433044 8.96647 0 9.06901 0 9.1875C0 9.30599 0.0433044 9.40853 0.129883 9.49512C0.216461 9.58171 0.319 9.625 0.4375 9.625C0.556 9.625 0.658539 9.58171 0.745117 9.49512L4.8125 5.42773L8.87988 9.49512C8.96646 9.58171 9.069 9.625 9.1875 9.625C9.306 9.625 9.40854 9.58171 9.49512 9.49512C9.5817 9.40853 9.625 9.30599 9.625 9.1875C9.625 9.06901 9.5817 8.96647 9.49512 8.87988Z"/>
                                    </Button.Content>
                                </Button>
                                <Button
                                    x:Name="PART_MenuButton"
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,8,0"
                                    AllowDrop="True"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    BorderThickness="0"
                                    Command="tools_controls:TabControlCommands.OpenContextMenu"
                                    DockPanel.Dock="Right"
                                    Focusable="False"
                                    Style="{StaticResource TabPanelCaptionButtonsStyle}"
                                    Visibility="Visible">
                                    <Button.ToolTip>
                                        <ToolTip x:Name="PART_ContextMenuButtonTooltip">
                                            <ToolTip.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                            </ToolTip.Resources>
                                            <TextBlock x:Name="PART_ContextMenuButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=MoreTabs}" />
                                        </ToolTip>
                                    </Button.ToolTip>
                                    <Button.Content>
                                        <Path
                                            x:Name="MenuButtonPath2"
                                            Width="8"
                                            Height="4"
                                            Fill="{Binding Foreground, ElementName=PART_MenuButton}"
                                            RenderTransformOrigin="0.5,0.5"
                                            SnapsToDevicePixels="True"
                                            Stretch="Fill" 
                                            Data="M1.71387 0.286133C1.62728 0.199544 1.52474 0.15625 1.40625 0.15625C1.28776 0.15625 1.18522 0.199544 1.09863 0.286133C1.01204 0.372721 0.96875 0.475261 0.96875 0.59375C0.96875 0.71224 1.01204 0.814779 1.09863 0.901367L5.91113 5.71387C5.99772 5.80046 6.10026 5.84375 6.21875 5.84375C6.33724 5.84375 6.43978 5.80046 6.52637 5.71387L11.3389 0.901367C11.4255 0.814779 11.4688 0.71224 11.4688 0.59375C11.4688 0.47526 11.4255 0.372721 11.3389 0.286133C11.2523 0.199544 11.1497 0.15625 11.0312 0.15625C10.9128 0.15625 10.8102 0.199544 10.7236 0.286133L6.21875 4.78418L1.71387 0.286133Z"/>
                                    </Button.Content>
                                </Button>
                                <Button
                                    x:Name="PART_LastTab"
                                    Width="16"
                                    MinHeight="16"
                                    Margin="0,0,8,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    DockPanel.Dock="Right"
                                    Focusable="False"
                                    Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}"
                                    Tag="LastTab"
                                    Visibility="Collapsed">
                                    <Button.ToolTip>
                                        <ToolTip Name="LastTabToolTip">
                                            <ToolTip.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                            </ToolTip.Resources>
                                            <TextBlock Name="LastToolTipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=LastTab}"/>
                                        </ToolTip>
                                    </Button.ToolTip>
                                    <Button.Content>
                                        <Path
                                            Width="6"
                                            Height="8"
                                            Fill="{Binding Foreground, ElementName=PART_LastTab}"
                                            SnapsToDevicePixels="True"
                                            Stretch="Fill" 
                                            Data="M5.6875 5.25C5.6875 5.13151 5.64421 5.02897 5.55762 4.94238L0.745117 0.129883C0.658529 0.0432943 0.55599 0 0.4375 0C0.31901 0 0.216471 0.0432943 0.129883 0.129883C0.0432943 0.216471 0 0.31901 0 0.4375C0 0.55599 0.0432943 0.658529 0.129883 0.745117L4.62793 5.25L0.129883 9.75488C0.0432943 9.84147 0 9.94401 0 10.0625C0 10.181 0.0432943 10.2835 0.129883 10.3701C0.216471 10.4567 0.31901 10.5 0.4375 10.5C0.55599 10.5 0.658529 10.4567 0.745117 10.3701L5.55762 5.55762C5.64421 5.47103 5.6875 5.36849 5.6875 5.25ZM7.5 0.25C7.22386 0.25 7 0.473858 7 0.75V5.25V6.25L7 9.75C7 10.0261 7.22386 10.25 7.5 10.25C7.77614 10.25 8 10.0261 8 9.75V0.75C8 0.473858 7.77614 0.25 7.5 0.25Z"/>
                                    </Button.Content>
                                </Button>
                                <Button
                                    x:Name="PART_NextTab"
                                    Width="16"
                                    MinHeight="16"
                                    Margin="0,0,8,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    DockPanel.Dock="Right"
                                    Focusable="False"
                                    Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}"
                                    Tag="NextTab"
                                    Visibility="Collapsed">
                                    <Button.ToolTip>
                                        <ToolTip Name="NextTabToolTip">
                                            <ToolTip.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                            </ToolTip.Resources>
                                            <TextBlock Name="NextTabToolTipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=NextTab}"/>
                                        </ToolTip>
                                    </Button.ToolTip>
                                    <Button.Content>
                                        <Path
                                            Width="4"
                                            Height="8"
                                            Fill="{Binding Foreground, ElementName=PART_NextTab}"
                                            SnapsToDevicePixels="True"
                                            Stretch="Fill"
                                            Data="M0.504883 10.5049C0.418289 10.5915 0.375 10.694 0.375 10.8125C0.375 10.931 0.418289 11.0335 0.504883 11.1201C0.591476 11.2067 0.694016 11.25 0.8125 11.25C0.930984 11.25 1.03352 11.2067 1.12012 11.1201L5.93262 6.30762C6.01921 6.22103 6.0625 6.11849 6.0625 6C6.0625 5.88151 6.01921 5.77897 5.93262 5.69238L1.12012 0.879883C1.03352 0.793293 0.930984 0.75 0.8125 0.75C0.694016 0.75 0.591476 0.793293 0.504883 0.879883C0.418289 0.966473 0.375 1.06901 0.375 1.1875C0.375 1.30599 0.418289 1.40853 0.504883 1.49512L5.00293 6H5V6.00293L0.504883 10.5049Z"/>
                                    </Button.Content>
                                </Button>
                                <Button
                                    x:Name="PART_PrevTab"
                                    Width="16"
                                    MinHeight="16"
                                    Margin="0,0,8,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    DockPanel.Dock="Right"
                                    Focusable="False"
                                    Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}"
                                    Tag="PrevTab"
                                    Visibility="Collapsed">
                                    <Button.ToolTip>
                                        <ToolTip Name="PreviousTabToolTip">
                                            <ToolTip.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                            </ToolTip.Resources>
                                            <TextBlock Name="PreviousTabToolTipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=PreviousTab}"/>
                                        </ToolTip>
                                    </Button.ToolTip>
                                    <Button.Content>
                                        <Path
                                            Width="4"
                                            Height="8"
                                            Fill="{Binding Foreground, ElementName=PART_PrevTab}"
                                            SnapsToDevicePixels="True"
                                            Stretch="Fill"
                                            Data="M1.06738 5.69238C0.980794 5.77897 0.9375 5.88151 0.9375 6C0.9375 6.11849 0.980794 6.22103 1.06738 6.30762L5.87988 11.1201C5.96647 11.2067 6.06901 11.25 6.1875 11.25C6.30599 11.25 6.40853 11.2067 6.49512 11.1201C6.58171 11.0335 6.625 10.931 6.625 10.8125C6.625 10.694 6.58171 10.5915 6.49512 10.5049L2 6.00293V6H1.99707L6.49512 1.49512C6.58171 1.40853 6.625 1.30599 6.625 1.1875C6.625 1.06901 6.58171 0.966471 6.49512 0.879883C6.40853 0.793294 6.30599 0.75 6.1875 0.75C6.06901 0.75 5.96647 0.793294 5.87988 0.879883L1.06738 5.69238Z"/>
                                    </Button.Content>
                                </Button>
                                <Button
                                    x:Name="PART_NextPage"
                                    Width="16"
                                    MinHeight="16"
                                    Margin="0,0,8,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    DockPanel.Dock="Right"
                                    Focusable="False"
                                    Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}"
                                    Tag="NextPage"
                                    Visibility="Collapsed">
                                    <Button.ToolTip>
                                        <ToolTip Name="NextPageToolTip">
                                            <ToolTip.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                            </ToolTip.Resources>
                                            <TextBlock Name="NextPageToolTipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=NextPage}"/>
                                        </ToolTip>
                                    </Button.ToolTip>
                                    <Button.Content>
                                        <Path
                                            Width="6"
                                            Height="8"
                                            Fill="{Binding Foreground, ElementName=PART_NextPage}"
                                            SnapsToDevicePixels="True"
                                            Stretch="Fill"
                                            Data="M0 10.8125C0 10.694 0.0432892 10.5915 0.129883 10.5049L4.62793 6L0.129883 1.49512C0.0432892 1.40853 0 1.30599 0 1.1875C0 1.06901 0.0432892 0.966473 0.129883 0.879883C0.216476 0.793293 0.319016 0.75 0.4375 0.75C0.555984 0.75 0.658524 0.793293 0.745117 0.879883L5.55762 5.69238C5.64421 5.77897 5.6875 5.88151 5.6875 6C5.6875 6.11849 5.64421 6.22103 5.55762 6.30762L0.745117 11.1201C0.658524 11.2067 0.555984 11.25 0.4375 11.25C0.319016 11.25 0.216476 11.2067 0.129883 11.1201C0.0432892 11.0335 0 10.931 0 10.8125ZM3 10.8125C3 10.694 3.04329 10.5915 3.12988 10.5049L7.62793 6L3.12988 1.49512C3.04329 1.40853 3 1.30599 3 1.1875C3 1.06901 3.04329 0.966473 3.12988 0.879883C3.21648 0.793293 3.31902 0.75 3.4375 0.75C3.55598 0.75 3.65852 0.793293 3.74512 0.879883L8.55762 5.69238C8.64421 5.77897 8.6875 5.88151 8.6875 6C8.6875 6.11849 8.64421 6.22103 8.55762 6.30762L3.74512 11.1201C3.65852 11.2067 3.55598 11.25 3.4375 11.25C3.31902 11.25 3.21648 11.2067 3.12988 11.1201C3.04329 11.0335 3 10.931 3 10.8125Z"/>
                                    </Button.Content>
                                </Button>
                                <Button
                                    x:Name="PART_PrevPage"
                                    Width="16"
                                    MinHeight="16"
                                    Margin="0,0,8,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    DockPanel.Dock="Right"
                                    Focusable="False"
                                    Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}"
                                    Tag="PrevPage"
                                    Visibility="Collapsed">
                                    <Button.ToolTip>
                                        <ToolTip Name="PreviousPageToolTip">
                                            <ToolTip.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                            </ToolTip.Resources>
                                            <TextBlock Name="PreviousPageToolTipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=PreviousPage}"/>
                                        </ToolTip>
                                    </Button.ToolTip>
                                    <Button.Content>
                                        <Path
                                            Width="6"
                                            Height="8"
                                            Fill="{Binding Foreground, ElementName=PART_PrevPage}"
                                            SnapsToDevicePixels="True"
                                            Stretch="Fill" 
                                            Data="M0 6C0 5.88151 0.0432968 5.77897 0.129883 5.69238L4.94238 0.879883C5.02897 0.793293 5.13151 0.75 5.25 0.75C5.36849 0.75 5.47103 0.793293 5.55762 0.879883C5.6442 0.966473 5.6875 1.06901 5.6875 1.1875C5.6875 1.30599 5.6442 1.40853 5.55762 1.49512L1.05957 6L5.55762 10.5049C5.6442 10.5915 5.6875 10.694 5.6875 10.8125C5.6875 10.931 5.6442 11.0335 5.55762 11.1201C5.47103 11.2067 5.36849 11.25 5.25 11.25C5.13151 11.25 5.02897 11.2067 4.94238 11.1201L0.129883 6.30762C0.0432968 6.22103 0 6.11849 0 6ZM3 6C3 5.88151 3.0433 5.77897 3.12988 5.69238L7.94238 0.879883C8.02897 0.793293 8.13151 0.75 8.25 0.75C8.36849 0.75 8.47103 0.793293 8.55762 0.879883C8.6442 0.966473 8.6875 1.06901 8.6875 1.1875C8.6875 1.30599 8.6442 1.40853 8.55762 1.49512L4.05957 6L8.55762 10.5049C8.6442 10.5915 8.6875 10.694 8.6875 10.8125C8.6875 10.931 8.6442 11.0335 8.55762 11.1201C8.47103 11.2067 8.36849 11.25 8.25 11.25C8.13151 11.25 8.02897 11.2067 7.94238 11.1201L3.12988 6.30762C3.0433 6.22103 3 6.11849 3 6Z"/>
                                    </Button.Content>
                                </Button>
                                <Button
                                    x:Name="PART_FirstTab"
                                    Width="16"
                                    MinHeight="16"
                                    Margin="0,0,8,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    DockPanel.Dock="Right"
                                    Focusable="False"
                                    Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}"
                                    Tag="FirstTab"
                                    Visibility="Collapsed">
                                    <Button.ToolTip>
                                        <ToolTip Name="FirstTabToolTip">
                                            <ToolTip.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                            </ToolTip.Resources>
                                            <TextBlock Name="FirstTabToolTipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=FirstTab}"/>
                                        </ToolTip>
                                    </Button.ToolTip>
                                    <Button.Content>
                                        <Path
                                            Width="6"
                                            Height="8"
                                            Fill="{Binding Foreground, ElementName=PART_FirstTab}"
                                            SnapsToDevicePixels="True"
                                            Stretch="Fill" 
                                            Data="M2 6C2 5.88151 2.04329 5.77897 2.12988 5.69238L6.94238 0.879883C7.02897 0.793294 7.13151 0.75 7.25 0.75C7.36849 0.75 7.47103 0.793294 7.55762 0.879883C7.64421 0.966471 7.6875 1.06901 7.6875 1.1875C7.6875 1.30599 7.64421 1.40853 7.55762 1.49512L3.05957 6L7.55762 10.5049C7.64421 10.5915 7.6875 10.694 7.6875 10.8125C7.6875 10.931 7.64421 11.0335 7.55762 11.1201C7.47103 11.2067 7.36849 11.25 7.25 11.25C7.13151 11.25 7.02897 11.2067 6.94238 11.1201L2.12988 6.30762C2.04329 6.22103 2 6.11849 2 6ZM0.5 1C0.776142 1 1 1.22386 1 1.5V4V5L1 10.5C1 10.7761 0.776142 11 0.5 11C0.223857 11 0 10.7761 0 10.5V5V4V1.5C0 1.22386 0.223858 1 0.5 1Z"/>
                                    </Button.Content>
                                </Button>
                                <Border
                                    x:Name="PART_Separator"
                                    Width="1.5"
                                    Height="14"
                                    Margin="0,0,8,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Background="{StaticResource BorderAlt}"
                                    DockPanel.Dock="Right"
                                    Focusable="False"
                                    Visibility="Collapsed" />
                                <DockPanel LastChildFill="False">
                                    <Grid 
                                        x:Name="NewButtonPosition"
                                        DockPanel.Dock="Left"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center">
                                        <tools_controls:NewTabLayout Visibility="Visible">
                                            <ContentPresenter x:Name="PART_ToolBarTray" 
											Visibility="{Binding Path=ToolBarTray , Converter={StaticResource TabControlExtObjectNullToVisibilityConverterEx}, Mode=OneWay , RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Collapsed}"
											Content="{Binding Path=ToolBarTray, Mode=OneWay, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=''}" />
                                            <Button 
                                                x:Name="PART_NewTab"
												Visibility="{Binding Path=IsNewButtonEnabled, Converter={StaticResource TabControlExtBooleanToVisibilityConverterEx}, Mode=OneWay, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}},FallbackValue=Collapsed}"
                                                BorderBrush="Transparent"
                                                ContentTemplate="{Binding Path=NewTabButtonTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}"
                                                Background="{Binding Path=NewButtonBackground, RelativeSource={RelativeSource AncestorType=tools_controls:TabControlExt}}"    
                                                BorderThickness="{Binding Path=NewButtonBorderThickness, RelativeSource={RelativeSource AncestorType=tools_controls:TabControlExt}}"    
                                                Style="{StaticResource SyncfusionNewButtonStyle}"
                                                DockPanel.Dock="Top"/>
                                        </tools_controls:NewTabLayout>
                                    </Grid>
                                    <Button 
                                        x:Name="PART_NextAutoScrollButton"
                                        DockPanel.Dock="Right"
                                        Width="16"
                                        MinHeight="16"
                                        Margin="0,0,8,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        BorderBrush="Transparent"
                                        Focusable="False"
                                        Tag="RightAutoScroll"
                                        Style="{Binding ScrollingButtonStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}"
                                        Visibility="Collapsed">
                                        <Button.Content>
                                            <Path 
                                                Data="M18,1.4210855E-14 C19.104004,-1.1920928E-07 20,0.89599597 20,1.9999999 20,3.1040038 19.104004,3.9999999 18,3.9999999 16.895996,3.9999999 16,3.1040038 16,1.9999999 16,0.89599597 16.895996,-1.1920928E-07 18,1.4210855E-14 z M10,1.4210855E-14 C11.104004,-1.1920928E-07 12,0.89599597 12,1.9999999 12,3.1040038 11.104004,3.9999999 10,3.9999999 8.8959961,3.9999999 8,3.1040038 8,1.9999999 8,0.89599597 8.8959961,-1.1920928E-07 10,1.4210855E-14 z M1.9999999,1.4210855E-14 C3.1040039,-1.1920928E-07 4,0.89599597 4,1.9999999 4,3.1040038 3.1040039,3.9999999 1.9999999,3.9999999 0.89599609,3.9999999 0,3.1040038 0,1.9999999 0,0.89599597 0.89599609,-1.1920928E-07 1.9999999,1.4210855E-14 z" 
                                                Fill="{Binding Foreground, ElementName=PART_NextAutoScrollButton}"
                                                Height="2"  
                                                SnapsToDevicePixels="True"
                                                Stretch="Fill"
                                                Width="8"/>
                                        </Button.Content>
                                    </Button>
                                    <Button
                                        x:Name="PART_PrevAutoScrollButton"
                                        DockPanel.Dock="Left"
                                        Width="16"
                                        MinHeight="16"
                                        Margin="0,0,8,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        BorderBrush="Transparent"
                                        Focusable="False"
                                        Tag="LeftAutoScroll"
                                        Style="{Binding ScrollingButtonStyle,RelativeSource={RelativeSource Mode=FindAncestor,AncestorType={x:Type tools_controls:TabControlExt}}}"
                                        Visibility="Collapsed">
                                        <Button.Content>
                                            <Path Data="M18,1.4210855E-14 C19.104004,-1.1920928E-07 20,0.89599597 20,1.9999999 20,3.1040038 19.104004,3.9999999 18,3.9999999 16.895996,3.9999999 16,3.1040038 16,1.9999999 16,0.89599597 16.895996,-1.1920928E-07 18,1.4210855E-14 z M10,1.4210855E-14 C11.104004,-1.1920928E-07 12,0.89599597 12,1.9999999 12,3.1040038 11.104004,3.9999999 10,3.9999999 8.8959961,3.9999999 8,3.1040038 8,1.9999999 8,0.89599597 8.8959961,-1.1920928E-07 10,1.4210855E-14 z M1.9999999,1.4210855E-14 C3.1040039,-1.1920928E-07 4,0.89599597 4,1.9999999 4,3.1040038 3.1040039,3.9999999 1.9999999,3.9999999 0.89599609,3.9999999 0,3.1040038 0,1.9999999 0,0.89599597 0.89599609,-1.1920928E-07 1.9999999,1.4210855E-14 z" 
                                            Fill="{Binding Foreground, ElementName=PART_PrevAutoScrollButton}"
                                            Height="2"  
                                            SnapsToDevicePixels="True"
                                            Stretch="Fill"
                                            Width="8"/>
                                        </Button.Content>
                                    </Button>
                                    <ContentPresenter 
                                        DockPanel.Dock="Left"
                                        x:Name="PART_TabItems"
                                        Content="{TemplateBinding Content}"
                                        ContentSource="TabItems"/>
                                </DockPanel>
                            </DockPanel>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Path=TabItemHeaderStyle, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Underline}" Value="Underline">
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="BorderThickness"  Value="0" TargetName="border" />
                            <Setter Property="BorderBrush" Value="Transparent" TargetName="border"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=TabItemHeaderStyle, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Underline}" Value="Curve">
                            <Setter Property="BorderThickness" Value="0 0 0 1"/>
                            <Setter Property="BorderThickness"  Value="0" TargetName="Bord" />
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" TargetName="border"/>
                        </DataTrigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="PART_MenuButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_CloseButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_PrevAutoScrollButton" Property="Width" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_NextAutoScrollButton" Property="Width" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_PrevPage" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_NextPage" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_PrevTab" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_NextTab" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_LastTab" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_FirstTab" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_PrevPage" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_NextPage" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_PrevTab" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_NextTab" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_LastTab" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_FirstTab" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <DataTrigger Binding="{Binding Path=TabListContextMenuOptions,RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}" Value="None">
                            <Setter Property="Visibility" TargetName="PART_MenuButton" Value="Collapsed"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Bottom">
                            <Setter TargetName="PART_CustomItems" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="-180" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_CloseButton" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="-180" />
                                </Setter.Value>
                            </Setter>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Left">
                            <Setter TargetName="PART_MenuButton" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="-180" />
                                </Setter.Value>
                            </Setter>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=NewButtonAlignment,RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}},FallbackValue=First}" Value="Last">
                            <Setter Property="DockPanel.Dock" Value="Right" TargetName="NewButtonPosition"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=IsAllTabsClosed, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=false}" Value="True">
                            <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Hidden" />
                            <Setter TargetName="PART_MenuButton" Property="Visibility" Value="Hidden" />
                            <!--<Setter TargetName="BottomBorder" Property="Height" Value="0" />-->
                        </DataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType={x:Type tools_controls:TabControlExt}}}" Value="Underline"/>
                                <Condition Binding="{Binding Path=NewButtonAlignment,RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}},FallbackValue=First}" Value="Last"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Margin" Value="0 0 6 0" TargetName="PART_NewTab"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType={x:Type tools_controls:TabControlExt}}}" Value="Curve"/>
                                <Condition Binding="{Binding Path=NewButtonAlignment,RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}},FallbackValue=First}" Value="Last"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Margin" Value="0" TargetName="PART_NewTab"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType={x:Type tools_controls:TabControlExt}}}" Value="Underline"/>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Right"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="0"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType={x:Type tools_controls:TabControlExt}}}" Value="Curve"/>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Right"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="1 0 0 0"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType={x:Type tools_controls:TabControlExt}}}" Value="Underline"/>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Left"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="0"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType={x:Type tools_controls:TabControlExt}}}" Value="Curve"/>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Left"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="0 0 1 0"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType={x:Type tools_controls:TabControlExt}}}" Value="Underline"/>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Bottom"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="0"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType={x:Type tools_controls:TabControlExt}}}" Value="Curve"/>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Bottom"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="0 1 0 0"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=Items.Count, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=0}" Value="1" />
                                <Condition Binding="{Binding Path=HideHeaderOnSingleChild, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}" Value="True" />
                                <Condition Binding="{Binding Path=TabListContextMenuOptions,RelativeSource={RelativeSource FindAncestor,AncestorType={x:Type tools_controls:TabControlExt}}}" Value="None" />
                                <Condition Binding="{Binding ElementName=PART_CloseButton, Path=Visibility}" Value="Collapsed" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="Bord" Property="Visibility" Value="Collapsed" />
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionDragMarkerTemplatedAdornerInternalControlStyle" TargetType="{x:Type windows_shared:TemplatedAdornerInternalControl}">
        <Setter Property="MinWidth" Value="15" />
        <Setter Property="MinHeight" Value="15" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="SnapsToDevicePixels" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type windows_shared:TemplatedAdornerInternalControl}">
                    <DockPanel
                        x:Name="PART_DockPanel"
                        Height="{Binding Path=MinHeight, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}"
                        LastChildFill="True">
                        <Canvas
                            x:Name="PART_Top"
                            Width="5"
                            Height="2" Margin="0,1,0,0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                            DockPanel.Dock="Top">
                            <Path
                                Data="M0,0L2.5,3 5,0L0,0L2.5,3"
                                Fill="{Binding Path=MarkerColor, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}"
                                Stroke="{Binding Path=MarkerColor, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" />
                        </Canvas>
                        <Border
                            x:Name="PART_Center"
                            MinHeight="{Binding Path=ActualHeight, ElementName=PART_DockPanel}"
                            Margin="2,0,-3,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Top"
                            BorderBrush="{Binding Path=MarkerColor, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}"
                            BorderThickness="{StaticResource Windows11Dark.BorderThickness1000}" />
                        <Canvas
                            x:Name="PART_Bottom"
                            Width="5"
                            Height="2" Margin="0,0,0,2"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Bottom"
                            DockPanel.Dock="Bottom">
                            <Path
                                Data="M0,3L2.5,0 5,3L0,3L2.5,0"
                                Fill="{Binding Path=MarkerColor, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}"
                                Stroke="{Binding Path=MarkerColor, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" />
                        </Canvas>
                    </DockPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.5" />
                        </Trigger>
                        <!--  Top  -->
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}, FallbackValue=Top}" Value="Top" />
                                <Condition Binding="{Binding Path=AdornerAlignment, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" Value="LeftSide" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Top" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="6" Y="-3" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Center" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="8" Y="-3" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Bottom" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="0" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}, FallbackValue=Top}" Value="Top" />
                                <Condition Binding="{Binding Path=AdornerAlignment, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" Value="RightSide" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Top" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-3.5" Y="-3" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Center" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-1.5" Y="-2" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Bottom" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-4.5" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>
                        <!--  End Top  -->

                        <!--  Bottom  -->
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}, FallbackValue=Top}" Value="Bottom" />
                                <Condition Binding="{Binding Path=AdornerAlignment, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" Value="LeftSide" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Top" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="8.5" Y="-2" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Center" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="10.5" Y="-2" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Bottom" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="7.5" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}, FallbackValue=Top}" Value="Bottom" />
                                <Condition Binding="{Binding Path=AdornerAlignment, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" Value="RightSide" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Top" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-3" Y="-2" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Center" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-1" Y="-2" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Bottom" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-4" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>
                        <!--  End Bottom  -->

                        <!--  Left  -->
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}, FallbackValue=Top}" Value="Left" />
                                <Condition Binding="{Binding Path=AdornerAlignment, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" Value="RightSide" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Top" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-2" Y="-3" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Center" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="0" Y="-3" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Bottom" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-3" Y="0" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}, FallbackValue=Top}" Value="Left" />
                                <Condition Binding="{Binding Path=AdornerAlignment, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" Value="LeftSide" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Top" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="9" Y="-3" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Center" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="11" Y="-3" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Bottom" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="8" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>
                        <!--  End Left  -->

                        <!--  Right  -->
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}, FallbackValue=Top}" Value="Right" />
                                <Condition Binding="{Binding Path=AdornerAlignment, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" Value="RightSide" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Top" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-3" Y="-3" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Center" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-1" Y="-3" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Bottom" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-4" Y="0" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}, FallbackValue=Top}" Value="Right" />
                                <Condition Binding="{Binding Path=AdornerAlignment, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" Value="LeftSide" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Top" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="6.5" Y="-3" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Center" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="8.5" Y="-3" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Bottom" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="5.5" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=RotateTextWhenVertical, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}, FallbackValue=false}" Value="True" />
                                <Condition Binding="{Binding Path=AdornerAlignment, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" Value="RightSide" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_DockPanel" Property="Height" Value="{Binding Path=MinWidth, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" />
                            <Setter TargetName="PART_DockPanel" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="90" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Top" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-6" Y="0" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Center" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-4" Y="0" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Bottom" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-7" Y="3" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=RotateTextWhenVertical, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}, FallbackValue=false}" Value="True" />
                                <Condition Binding="{Binding Path=AdornerAlignment, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" Value="LeftSide" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_DockPanel" Property="Height" Value="{Binding Path=MinWidth, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DragMarkerAdorner}}}" />
                            <Setter TargetName="PART_DockPanel" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="90" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Top" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-4" Y="0" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Center" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="0" Y="0" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Bottom" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform X="-4" Y="3" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="SyncfusionCustomMenuItemSeparatorControlTemplate" TargetType="{x:Type tools_controls:CustomMenuItem}">
        <Border Background="{TemplateBinding Background}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="26" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Border
                    x:Name="lineBorder"
                    Grid.Column="1"
                    Height="{TemplateBinding BorderThickness}"
                    Margin="2"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}" />
            </Grid>
        </Border>
    </ControlTemplate>

    <Style
        x:Key="SyncfusionCustomMenuItemStyle"
        BasedOn="{StaticResource WPFMenuItemStyle}"
        TargetType="tools_controls:CustomMenuItem">
        <Style.Triggers>
            <DataTrigger Binding="{Binding Path=IsSeparator, RelativeSource={RelativeSource Self}, FallbackValue=False}" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="BorderThickness" Value="0.5" />
                <Setter Property="Control.Template" Value="{StaticResource SyncfusionCustomMenuItemSeparatorControlTemplate}" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="SyncfusionMenuItemStyle"
        BasedOn="{StaticResource WPFMenuItemStyle}"
        TargetType="MenuItem">
        <Style.Triggers>
            <Trigger Property="Tag"  Value="Separator">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Template" Value="{StaticResource SyncfusionCustomMenuItemSeparatorControlTemplate}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="TabItemContextMenu"
        BasedOn="{StaticResource WPFContextMenuStyle}"
        TargetType="{x:Type ContextMenu}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ContextMenu}">
                    <Border
                        Name="Shdw"
                        Background="Transparent"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Effect="{StaticResource Default.ShadowDepth3}"
                        SnapsToDevicePixels="true">
                        <Border
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <Grid x:Name="PART_Grid">
                                <Rectangle
                                    Width="28"
                                    Margin="2"
                                    HorizontalAlignment="Left"
                                    Fill="{TemplateBinding Background}"
                                    RadiusX="2"
                                    RadiusY="2" />
                                <Rectangle
                                    Width="1"
                                    Margin="30,2,0,2"
                                    HorizontalAlignment="Left"
                                    Fill="{TemplateBinding BorderBrush}" />
                                <Rectangle
                                    Width="1"
                                    Margin="31,2,0,2"
                                    HorizontalAlignment="Left"
                                    Fill="{TemplateBinding BorderBrush}" />
                                <ScrollViewer
                                    Grid.ColumnSpan="2"
                                    Margin="1,0"
                                    CanContentScroll="true"
                                    Style="{StaticResource WPFMenuScrollViewer}">
                                    <StackPanel>
                                        <tools_controls:CustomMenuItem
                                            x:Name="PART_CloseTab"
                                            Margin="2"
                                            Command="tools_controls:TabControlCommands.CloseTabItem"
                                            CommandTarget="{TemplateBinding PlacementTarget}"
                                            Header="{Binding Path=MenuItemCloseTabsName, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Close}"
                                            HeaderTemplate="{Binding Path=TabItemContextMenuItemTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue={x:Null}}"
                                            IsEnabled="{Binding Path=EnableCloseMenuItem, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue=true}"
                                            Style="{Binding Path=TabItemContextMenuItemStyle, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue={x:Null}}"
                                            Visibility="{Binding Path=CloseMenuItemVisibility, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}}" />
                                        <tools_controls:CustomMenuItem
                                            x:Name="PART_CloseOtherTab"
                                            Margin="2"
                                            Command="tools_controls:TabControlCommands.CloseTabs"
                                            CommandParameter="{Binding RelativeSource={RelativeSource Self}}"
                                            CommandTarget="{TemplateBinding PlacementTarget}"
                                            Header="{Binding Path=MenuItemCloseOtherTabsName, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=CloseAllButThis}"
                                            HeaderTemplate="{Binding Path=TabItemContextMenuItemTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue={x:Null}}"
                                            Style="{Binding Path=TabItemContextMenuItemStyle, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue={x:Null}}"
                                            Visibility="{Binding Path=CloseAllButThisMenuItemVisibility, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}}" />
                                        <tools_controls:CustomMenuItem
                                            x:Name="PART_CloseAllTabs"
                                            Margin="2"
                                            Command="tools_controls:TabControlCommands.CloseTabs"
                                            CommandParameter="{Binding RelativeSource={RelativeSource Self}}"
                                            CommandTarget="{TemplateBinding PlacementTarget}"
                                            Header="{Binding Path=MenuItemCloseAllTabsName, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=CloseAll}"
                                            HeaderTemplate="{Binding Path=TabItemContextMenuItemTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue={x:Null}}"
                                            Style="{Binding Path=TabItemContextMenuItemStyle, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue={x:Null}}"
                                            Tag="All"
                                            Visibility="{Binding Path=CloseAllMenuItemVisibility, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}}" />
                                        <tools_controls:CustomMenuItem
                                            x:Name="PART_PinTab"
                                            Margin="2"
                                            Command="tools_controls:TabControlCommands.PinTabItem"
                                            CommandParameter="{Binding RelativeSource={RelativeSource Self}}"
                                            CommandTarget="{TemplateBinding PlacementTarget}"
                                            Header="{Binding Path=PinMenuItemHeader, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue=PinTab}"
                                            HeaderTemplate="{Binding Path=TabItemContextMenuItemTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue={x:Null}}"
                                            Style="{Binding Path=TabItemContextMenuItemStyle, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue={x:Null}}"
                                            Tag="Pin"
                                            Visibility="{Binding Path=PinTabMenuItemVisibility, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue=Collapsed}" />
                                        <ItemsPresenter
                                            Margin="{TemplateBinding Control.Padding}"
                                            KeyboardNavigation.DirectionalNavigation="Cycle"
                                            SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}" />
                                    </StackPanel>
                                </ScrollViewer>
                            </Grid>
                        </Border>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="HasDropShadow" Value="true">
                            <Setter TargetName="Shdw" Property="Margin" Value="14,0,14,14" />
                            <Setter TargetName="Shdw" Property="Background" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderBrush" Value="{StaticResource PopupBackground}" />
                <Setter Property="Foreground" Value="{StaticResource PopupDisabledForeground}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <ControlTemplate x:Key="SyncfusionFilledTabItemExtControlTemplate" TargetType="{x:Type tools_controls:TabItemExt}">
        <Grid x:Name="templateRoot" SnapsToDevicePixels="true">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition x:Name="topRow" />
                    <RowDefinition x:Name="bottomRow" Height="Auto" />
                </Grid.RowDefinitions>
                <Border
            x:Name="Bd"
            Margin="{TemplateBinding Margin}"
            Padding="{TemplateBinding Padding}"
            Background="{TemplateBinding Background}"
            BorderBrush="{TemplateBinding BorderBrush}"
            BorderThickness="{TemplateBinding BorderThickness}"
            SnapsToDevicePixels="True"
            ToolTip="{Binding Path=ItemToolTip, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue=''}">
                    <DockPanel x:Name="PART_DockPanel" LastChildFill="True">
                        <Button
        x:Name="PART_CloseButton"
        Width="{Binding ElementName=Content, Path=ActualHeight}"
        Height="{Binding ElementName=Content, Path=ActualHeight}"
        Margin="1"
        HorizontalAlignment="Center"
        VerticalAlignment="Center"
        Background="Transparent"
        BorderBrush="Transparent"
        BorderThickness="0"
        Command="tools_controls:TabControlCommands.CloseTabItem"
        DockPanel.Dock="Right"
        Focusable="False"
        Style="{StaticResource TabPanelCaptionButtonsStyle}"
        Tag="FilledHeaderTabItem"
        Visibility="Collapsed">
                            <Button.Content>
                                <Path
                x:Name="CloseButtonPath2"
                Width="8"
                Height="8"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Fill="{Binding Foreground, ElementName=PART_CloseButton}"
                SnapsToDevicePixels="True"
                Stretch="Fill" 
                Data="M9.49512 8.87988L5.42773 4.8125L9.49512 0.745117C9.5817 0.658527 9.625 0.555988 9.625 0.4375C9.625 0.319012 9.5817 0.216473 9.49512 0.129883C9.40854 0.043293 9.306 0 9.1875 0C9.069 0 8.96646 0.043293 8.87988 0.129883L4.8125 4.19727L0.745117 0.129883C0.658539 0.043293 0.556 0 0.4375 0C0.319 0 0.216461 0.043293 0.129883 0.129883C0.0433044 0.216473 0 0.319012 0 0.4375C0 0.555988 0.0433044 0.658527 0.129883 0.745117L4.19727 4.8125L0.129883 8.87988C0.0433044 8.96647 0 9.06901 0 9.1875C0 9.30599 0.0433044 9.40853 0.129883 9.49512C0.216461 9.58171 0.319 9.625 0.4375 9.625C0.556 9.625 0.658539 9.58171 0.745117 9.49512L4.8125 5.42773L8.87988 9.49512C8.96646 9.58171 9.069 9.625 9.1875 9.625C9.306 9.625 9.40854 9.58171 9.49512 9.49512C9.5817 9.40853 9.625 9.30599 9.625 9.1875C9.625 9.06901 9.5817 8.96647 9.49512 8.87988Z"/>
                            </Button.Content>
                        </Button>
                        <ToggleButton
        x:Name="PART_PinButton"
        Width="{Binding ElementName=Content, Path=ActualHeight}"
        Height="{Binding ElementName=Content, Path=ActualHeight}"
        Margin="1"
        HorizontalAlignment="Center"
        VerticalAlignment="Center"
        Background="Transparent"
        BorderBrush="Transparent"
        BorderThickness="0"
        Command="tools_controls:TabControlCommands.PinTabItem"
        CommandParameter="{Binding IsChecked, RelativeSource={RelativeSource Self}}"
        DockPanel.Dock="Right"
        Tag="FilledHeaderTabItem"
        Style="{StaticResource TabItemCaptionButtonsStyle}"
        Visibility="{Binding ShowPin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}, Converter={StaticResource TabControlExtCloseButtonTypeToVisibilityConverter}}">
                            <ToggleButton.Content>
                                <Path
                x:Name="PinButtonPath2"
                Width="10"
                Height="10"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Fill="{Binding Foreground, ElementName=PART_PinButton}"
                Stretch="Fill" 
                Data="M7.74219 3.74609C7.82552 3.62109 7.86719 3.48308 7.86719 3.33203C7.86719 3.23308 7.84766 3.13672 7.80859 3.04297C7.77214 2.94922 7.71875 2.86719 7.64844 2.79688L5.1875 0.332031C5.11979 0.264328 5.03906 0.210938 4.94531 0.171875C4.85417 0.132812 4.75911 0.113281 4.66016 0.113281C4.51172 0.113281 4.375 0.153641 4.25 0.234375C4.125 0.3125 4.03255 0.419266 3.97266 0.554688L3.10547 2.46484C3.07682 2.52734 3.03255 2.57031 2.97266 2.59375L0.65625 3.51953C0.609375 3.53777 0.571615 3.5677 0.542969 3.60938C0.514322 3.65105 0.5 3.69661 0.5 3.74609C0.5 3.81641 0.52474 3.8763 0.574219 3.92578L2.14844 5.5L0 7.64453V8H0.351562L2.5 5.85156L4.07422 7.42578C4.1237 7.47527 4.18229 7.5 4.25 7.5C4.30729 7.5 4.35677 7.48308 4.39844 7.44922C4.44271 7.41536 4.47266 7.37239 4.48828 7.32031L5.17188 5.04688C5.18229 5.01042 5.20052 4.97917 5.22656 4.95312C5.25261 4.92448 5.28255 4.90364 5.31641 4.89062L7.40625 4.01953C7.54688 3.96223 7.65886 3.87109 7.74219 3.74609ZM4.51953 0.652344C4.5612 0.626297 4.60807 0.613281 4.66016 0.613281C4.72526 0.613281 4.78255 0.638016 4.83203 0.6875L7.29688 3.14844C7.34375 3.19531 7.36719 3.25391 7.36719 3.32422C7.36719 3.3763 7.35286 3.42317 7.32422 3.46484C7.29818 3.50391 7.26172 3.53386 7.21484 3.55469L5.12109 4.42969C5.01953 4.47395 4.92969 4.53777 4.85156 4.62109C4.77604 4.70442 4.72266 4.79948 4.69141 4.90625L4.12891 6.77734L1.19531 3.83984L3.15625 3.05859C3.24739 3.02214 3.32812 2.97136 3.39844 2.90625C3.46875 2.83855 3.52344 2.76042 3.5625 2.67188L4.42969 0.761719C4.45052 0.714844 4.48047 0.678391 4.51953 0.652344Z"/>
                            </ToggleButton.Content>
                            <ToggleButton.LayoutTransform>
                                <RotateTransform Angle="0" />
                            </ToggleButton.LayoutTransform>
                        </ToggleButton>
                        <Image
                    x:Name="PART_Image"
                    Width="{TemplateBinding tools_controls:TabControlExt.ImageWidth}"
                    Height="{TemplateBinding tools_controls:TabControlExt.ImageHeight}"
                    Margin="{TemplateBinding IconMargin}"
                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                    DockPanel.Dock="{Binding Path=(tools_controls:TabControlExt.ImageAlignment), Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TabControlExtImageAlignmentToDockConverter}}"
                    SnapsToDevicePixels="True"
                    Source="{TemplateBinding tools_controls:TabControlExt.Image}"
                    Stretch="Fill" />
                        <ContentPresenter
                    x:Name="Content"
                    Margin="{TemplateBinding HeaderMargin}"
                    HorizontalAlignment="{Binding HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                    VerticalAlignment="{Binding VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                    Content="{TemplateBinding HeaderedContentControl.Header}"
                    ContentSource="Header"
                    ContentTemplate="{TemplateBinding HeaderedContentControl.HeaderTemplate}"
                    RecognizesAccessKey="False"
                    SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                    TextElement.Foreground="{TemplateBinding Foreground}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                        <ContentPresenter
                    x:Name="PART_EditHeader"
                    Margin="{TemplateBinding HeaderMargin}"
                    HorizontalAlignment="{Binding HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                    VerticalAlignment="{Binding VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                    Content="{TemplateBinding HeaderedContentControl.Header}"
                    ContentSource="Header"
                    RecognizesAccessKey="True"
                    SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                    TextElement.FontWeight="{TemplateBinding FontWeight}"
                    TextElement.Foreground="{TemplateBinding Foreground}"
                    Visibility="Collapsed" />
                    </DockPanel>
                </Border>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="HeaderMargin" Value="12,9,12,8" />
                <Setter Property="IconMargin" Value="12,10,0,12" />
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                <Setter TargetName="PART_CloseButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                <Setter TargetName="PART_CloseButton" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                <Setter TargetName="PART_PinButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                <Setter TargetName="PART_PinButton" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                <Setter TargetName="PART_CloseButton" Property="VerticalAlignment" Value="Center" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True">
                <Setter TargetName="PART_EditHeader" Property="ContentTemplate" Value="{Binding EditHeaderTemplate, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}" />
            </DataTrigger>
            <Trigger Property="tools_controls:TabControlExt.ImageHeight" Value="{x:Static sys:Double.NaN}">
                <Setter TargetName="PART_Image" Property="Stretch" Value="Uniform" />
            </Trigger>
            <Trigger Property="tools_controls:TabControlExt.ImageWidth" Value="{x:Static sys:Double.NaN}">
                <Setter TargetName="PART_Image" Property="Stretch" Value="Uniform" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="tools_controls:TabControlExt.ImageHeight" Value="{x:Static sys:Double.NaN}" />
                    <Condition Property="tools_controls:TabControlExt.ImageWidth" Value="{x:Static sys:Double.NaN}" />
                </MultiTrigger.Conditions>
                <Setter TargetName="PART_Image" Property="Stretch" Value="None" />
            </MultiTrigger>
            <Trigger SourceName="Bd" Property="IsMouseOver" Value="true">
                <Setter Property="Background" Value="{Binding Path=HoverBackground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="BorderBrush" Value="{Binding Path=TabControlExt.TabItemHoverBorderBrush, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="Foreground" Value="{Binding Path=TabControlExt.TabItemHoverForeground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="HoverBackground" Value="{x:Null}"/>
                    <Condition Property="IsMouseOver" Value="True"/>
                </MultiTrigger.Conditions>
                <Setter Property="Background" Value="{Binding Path=TabControlExt.TabItemHoverBackground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="BorderBrush" Value="{Binding Path=TabControlExt.TabItemHoverBorderBrush, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="Foreground" Value="{Binding Path=TabControlExt.TabItemHoverForeground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
            </MultiTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Background" Value="{Binding Path=TabItemSelectedBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Transparent}" />
                <Setter Property="BorderBrush" Value="{Binding Path=TabItemSelectedBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Transparent}" />
                <Setter Property="Foreground" Value="{Binding Path=TabItemSelectedForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Transparent}" />
                <Setter Property="FontWeight" Value="{Binding Path=SelectedItemFontWeight, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=SemiBold}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="Both" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="Individual" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="IndividualOnMouseOver" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Hidden" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="Extended" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Hidden" />
            </MultiDataTrigger>
            <Trigger SourceName="PART_PinButton" Property="IsChecked" Value="True">
                <!--<Setter TargetName="PinButtonPath" Property="Height" Value="10" />
            <Setter TargetName="PinButtonPath" Property="Width" Value="7.5" />-->
                <!--<Setter TargetName="PinButtonPath" Property="Data" Value="M1,0 L9,0 9,2 8,2 8,8 10,10 10,11 6,11 6,16 4,16 4,11 0,11 0,10 2,8 2,2 1,2 z" />-->
                <Setter TargetName="PinButtonPath2" Property="Data" Value="M7.87289 3.685C7.95763 3.5582 8 3.4182 8 3.26498C8 3.1646 7.98014 3.06686 7.94042 2.97177C7.90334 2.87667 7.84906 2.79346 7.77756 2.72214L5.27507 0.221893C5.20622 0.153214 5.12413 0.0990601 5.0288 0.059433C4.93612 0.0198135 4.83946 0 4.73883 0C4.58788 0 4.44886 0.040947 4.32175 0.122833C4.19464 0.20208 4.10063 0.310387 4.03972 0.447746L3.15789 2.38534C3.12877 2.44874 3.08375 2.49232 3.02284 2.5161L0.667328 3.45517C0.619663 3.47366 0.581264 3.50404 0.552135 3.54631C0.523006 3.58858 0.508442 3.6348 0.508442 3.685C0.508442 3.75632 0.5336 3.81708 0.583912 3.86726L2.18471 5.4641L0 7.63942V8H0.357498L2.54221 5.82071L4.143 7.41753C4.19331 7.46773 4.2529 7.49281 4.32175 7.49281C4.38 7.49281 4.43032 7.47565 4.47269 7.44131C4.51771 7.40697 4.54816 7.36339 4.56405 7.31055L5.25919 5.00446C5.26978 4.96747 5.28831 4.93578 5.3148 4.90936C5.34128 4.88031 5.37173 4.85918 5.40616 4.84596L7.53128 3.96236C7.67428 3.90424 7.78815 3.81178 7.87289 3.685Z"/>
                <Setter TargetName="PART_PinButton" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="0" />
                    </Setter.Value>
                </Setter>
            </Trigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="IndividualOnMouseOver" />
                    <Condition Binding="{Binding Path=IsMouseOver, ElementName=Bd}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="Extended" />
                    <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="Extended" />
                    <Condition Binding="{Binding Path=IsMouseOver, ElementName=Bd}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:TabControlExt.Image), RelativeSource={RelativeSource Self}}" Value="{x:Null}">
                <Setter TargetName="PART_Image" Property="UIElement.Visibility" Value="Collapsed" />
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Top}" Value="Left" />
                    <Condition Binding="{Binding Path=RotateTextWhenVertical, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=false}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="Bd" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="90" />
                    </Setter.Value>
                </Setter>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Top}" Value="Right" />
                    <Condition Binding="{Binding Path=RotateTextWhenVertical, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=false}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="Bd" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="-90" />
                    </Setter.Value>
                </Setter>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Top}" Value="Left" />
                </MultiDataTrigger.Conditions>
                <Setter Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="180" />
                    </Setter.Value>
                </Setter>
            </MultiDataTrigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:TabControlExt.IsEditing), RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_EditHeader" Property="Visibility" Value="Visible" />
                <Setter TargetName="Content" Property="Visibility" Value="Collapsed" />
            </MultiDataTrigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background" Value="{StaticResource ContentBackground}" />
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
            <Trigger Property="IsNewTab" Value="True">
                <Setter TargetName="Content" Property="ContentTemplate" Value="{Binding Path=NewTabButtonTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}" />
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Collapsed" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate TargetType="{x:Type tools_controls:TabItemExt}" x:Key="SyncfusionTabItemExtControlTemplate">
        <Grid x:Name="templateRoot" 
                      SnapsToDevicePixels="true">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition x:Name="topRow"/>
                    <RowDefinition x:Name="bottomRow" Height="Auto"/>
                </Grid.RowDefinitions>
                <Border
                            x:Name="Bd"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True"
                            ToolTip="{TemplateBinding ItemToolTip}">
                    <DockPanel x:Name="PART_DockPanel" LastChildFill="True">
                        <Button
                                x:Name="PART_CloseButton"
                                Width="{Binding ElementName=Content, Path=ActualHeight}"
                                Height="{Binding ElementName=Content, Path=ActualHeight}"
                                Margin="1"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                BorderThickness="0"
                                Command="tools_controls:TabControlCommands.CloseTabItem"
                                DockPanel.Dock="Right"
                                Focusable="False"
                                Style="{StaticResource TabPanelCaptionButtonsStyle}"
                                Tag="TabItem"
                                Visibility="Collapsed">
                            <Button.Content>
                                <Path
                                        x:Name="CloseButtonPath2"
                                        Width="8"
                                        Height="8"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Fill="{Binding Foreground, ElementName=PART_CloseButton}"
                                        SnapsToDevicePixels="True"
                                        Stretch="Fill" 
                                        Data="M9.49512 8.87988L5.42773 4.8125L9.49512 0.745117C9.5817 0.658527 9.625 0.555988 9.625 0.4375C9.625 0.319012 9.5817 0.216473 9.49512 0.129883C9.40854 0.043293 9.306 0 9.1875 0C9.069 0 8.96646 0.043293 8.87988 0.129883L4.8125 4.19727L0.745117 0.129883C0.658539 0.043293 0.556 0 0.4375 0C0.319 0 0.216461 0.043293 0.129883 0.129883C0.0433044 0.216473 0 0.319012 0 0.4375C0 0.555988 0.0433044 0.658527 0.129883 0.745117L4.19727 4.8125L0.129883 8.87988C0.0433044 8.96647 0 9.06901 0 9.1875C0 9.30599 0.0433044 9.40853 0.129883 9.49512C0.216461 9.58171 0.319 9.625 0.4375 9.625C0.556 9.625 0.658539 9.58171 0.745117 9.49512L4.8125 5.42773L8.87988 9.49512C8.96646 9.58171 9.069 9.625 9.1875 9.625C9.306 9.625 9.40854 9.58171 9.49512 9.49512C9.5817 9.40853 9.625 9.30599 9.625 9.1875C9.625 9.06901 9.5817 8.96647 9.49512 8.87988Z"/>
                            </Button.Content>
                        </Button>
                        <ToggleButton
                                x:Name="PART_PinButton"
                                Width="{Binding ElementName=Content, Path=ActualHeight}"
                                Height="{Binding ElementName=Content, Path=ActualHeight}"
                                Margin="1"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                BorderThickness="0"
                                Command="tools_controls:TabControlCommands.PinTabItem"
                                CommandParameter="{Binding IsChecked, RelativeSource={RelativeSource Self}}"
                                DockPanel.Dock="Right"
                                Tag="TabItem"
                                Style="{StaticResource TabItemCaptionButtonsStyle}"
                                Visibility="{Binding ShowPin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}, Converter={StaticResource TabControlExtCloseButtonTypeToVisibilityConverter}}">
                            <ToggleButton.Content>
                                <Path
                                        x:Name="PinButtonPath2"
                                        Width="10"
                                        Height="10"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Fill="{Binding Foreground, ElementName=PART_PinButton}"
                                        Stretch="Fill" 
                                        Data="M7.74219 3.74609C7.82552 3.62109 7.86719 3.48308 7.86719 3.33203C7.86719 3.23308 7.84766 3.13672 7.80859 3.04297C7.77214 2.94922 7.71875 2.86719 7.64844 2.79688L5.1875 0.332031C5.11979 0.264328 5.03906 0.210938 4.94531 0.171875C4.85417 0.132812 4.75911 0.113281 4.66016 0.113281C4.51172 0.113281 4.375 0.153641 4.25 0.234375C4.125 0.3125 4.03255 0.419266 3.97266 0.554688L3.10547 2.46484C3.07682 2.52734 3.03255 2.57031 2.97266 2.59375L0.65625 3.51953C0.609375 3.53777 0.571615 3.5677 0.542969 3.60938C0.514322 3.65105 0.5 3.69661 0.5 3.74609C0.5 3.81641 0.52474 3.8763 0.574219 3.92578L2.14844 5.5L0 7.64453V8H0.351562L2.5 5.85156L4.07422 7.42578C4.1237 7.47527 4.18229 7.5 4.25 7.5C4.30729 7.5 4.35677 7.48308 4.39844 7.44922C4.44271 7.41536 4.47266 7.37239 4.48828 7.32031L5.17188 5.04688C5.18229 5.01042 5.20052 4.97917 5.22656 4.95312C5.25261 4.92448 5.28255 4.90364 5.31641 4.89062L7.40625 4.01953C7.54688 3.96223 7.65886 3.87109 7.74219 3.74609ZM4.51953 0.652344C4.5612 0.626297 4.60807 0.613281 4.66016 0.613281C4.72526 0.613281 4.78255 0.638016 4.83203 0.6875L7.29688 3.14844C7.34375 3.19531 7.36719 3.25391 7.36719 3.32422C7.36719 3.3763 7.35286 3.42317 7.32422 3.46484C7.29818 3.50391 7.26172 3.53386 7.21484 3.55469L5.12109 4.42969C5.01953 4.47395 4.92969 4.53777 4.85156 4.62109C4.77604 4.70442 4.72266 4.79948 4.69141 4.90625L4.12891 6.77734L1.19531 3.83984L3.15625 3.05859C3.24739 3.02214 3.32812 2.97136 3.39844 2.90625C3.46875 2.83855 3.52344 2.76042 3.5625 2.67188L4.42969 0.761719C4.45052 0.714844 4.48047 0.678391 4.51953 0.652344Z"/>
                            </ToggleButton.Content>
                            <ToggleButton.LayoutTransform>
                                <RotateTransform Angle="0" />
                            </ToggleButton.LayoutTransform>
                        </ToggleButton>
                        <Image
                                x:Name="PART_Image"
                                Width="{TemplateBinding tools_controls:TabControlExt.ImageWidth}"
                                Height="{TemplateBinding tools_controls:TabControlExt.ImageHeight}"
                                Margin="{TemplateBinding IconMargin}"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                DockPanel.Dock="{Binding Path=(tools_controls:TabControlExt.ImageAlignment), Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TabControlExtImageAlignmentToDockConverter}}"
                                SnapsToDevicePixels="True"
                                Source="{TemplateBinding tools_controls:TabControlExt.Image}"
                                Stretch="Fill" />
                        <Grid x:Name="HeaderGrid">
                            <ContentPresenter
                                x:Name="Content"
                                Margin="{TemplateBinding HeaderMargin}"
                                HorizontalAlignment="{Binding HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                                VerticalAlignment="{Binding VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                                Content="{TemplateBinding HeaderedContentControl.Header}"
                                ContentSource="Header"
                                ContentTemplate="{TemplateBinding HeaderedContentControl.HeaderTemplate}"
                                RecognizesAccessKey="False"
                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                                TextElement.Foreground="{TemplateBinding Foreground}">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                            <ContentPresenter
                                x:Name="PART_EditHeader"
                                Margin="{TemplateBinding HeaderMargin}"
                                HorizontalAlignment="{Binding HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                                VerticalAlignment="{Binding VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                                Content="{TemplateBinding HeaderedContentControl.Header}"
                                ContentSource="Header"
                                RecognizesAccessKey="True"
                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                                TextElement.FontWeight="{TemplateBinding FontWeight}"
                                TextElement.Foreground="{TemplateBinding Foreground}"
                                Visibility="Collapsed" />
                        </Grid>
                    </DockPanel>
                </Border>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="HeaderMargin" Value="12,9,12,8" />
                <Setter Property="IconMargin" Value="12,10,0,12" />
                <Setter TargetName="PART_CloseButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                <Setter TargetName="PART_CloseButton" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                <Setter TargetName="PART_PinButton" Property="MinWidth" Value="{StaticResource TouchMode.MinHeight}" />
                <Setter TargetName="PART_PinButton" Property="MinHeight" Value="{StaticResource TouchMode.MinSize}" />
                <Setter TargetName="PART_CloseButton" Property="VerticalAlignment" Value="Center" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.TabItemHeaderStyle, RelativeSource={RelativeSource Self}, FallbackValue=Underline}" Value="Underline">
                <Setter Property="BorderBrush" Value="Transparent" />
                <Setter Property="HeaderMargin" Value="6 0" />
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness0002}"/>
            </DataTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition SourceName="PART_PinButton" Property="IsChecked" Value="True"/>
                    <Condition Property="sfskin:SfSkinManager.SizeMode" Value="Touch"/>
                </MultiTrigger.Conditions>
                <Setter TargetName="PART_PinButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                <Setter TargetName="PART_PinButton" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition SourceName="PART_PinButton" Property="IsChecked" Value="True"/>
                    <Condition Property="sfskin:SfSkinManager.SizeMode" Value="Default"/>
                </MultiTrigger.Conditions>
                <Setter TargetName="PART_PinButton" Property="MinHeight" Value="16" />
                <Setter TargetName="PART_PinButton" Property="MinWidth" Value="16" />
            </MultiTrigger>
            <DataTrigger Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" >
                <Setter TargetName="PART_EditHeader" Property="ContentTemplate" Value="{Binding EditHeaderTemplate, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}" />
            </DataTrigger>
            <Trigger Property="tools_controls:TabControlExt.ImageHeight" Value="{x:Static sys:Double.NaN}">
                <Setter TargetName="PART_Image" Property="Stretch" Value="Uniform" />
            </Trigger>

            <Trigger Property="tools_controls:TabControlExt.ImageWidth" Value="{x:Static sys:Double.NaN}">
                <Setter TargetName="PART_Image" Property="Stretch" Value="Uniform" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="tools_controls:TabControlExt.ImageHeight" Value="{x:Static sys:Double.NaN}" />
                    <Condition Property="tools_controls:TabControlExt.ImageWidth" Value="{x:Static sys:Double.NaN}" />
                </MultiTrigger.Conditions>
                <Setter TargetName="PART_Image" Property="Stretch" Value="None" />
            </MultiTrigger>
            <Trigger SourceName="Bd" Property="IsMouseOver" Value="true">
                <Setter Property="Background" Value="{Binding Path=HoverBackground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="BorderBrush" Value="{Binding Path=TabControlExt.TabItemHoverBorderBrush, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="Foreground" Value="{Binding Path=TabControlExt.TabItemHoverForeground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="HoverBackground" Value="{x:Null}"/>
                    <Condition Property="IsMouseOver" Value="True"/>
                </MultiTrigger.Conditions>
                <Setter Property="Background" Value="{Binding Path=TabControlExt.TabItemHoverBackground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="BorderBrush" Value="{Binding Path=TabControlExt.TabItemHoverBorderBrush, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="Foreground" Value="{Binding Path=TabControlExt.TabItemHoverForeground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
            </MultiTrigger>
            <DataTrigger Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True">
                <Setter Property="Background" Value="{Binding Path=TabControlExt.TabItemSelectedBackground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="BorderBrush" Value="{Binding Path=TabControlExt.TabItemSelectedBorderBrush, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="Foreground" Value="{Binding Path=TabControlExt.TabItemSelectedForeground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="TextElement.FontWeight" TargetName="Content" Value="{Binding Path=TabControlExt.SelectedItemFontWeight, RelativeSource={RelativeSource Mode=TemplatedParent},FallbackValue=SemiBold}" />
                <Setter Property="TextElement.FontWeight" TargetName="PART_EditHeader" Value="{Binding Path=TabControlExt.SelectedItemFontWeight, RelativeSource={RelativeSource Mode=TemplatedParent},FallbackValue=SemiBold}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.CloseButtonType, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="Both">
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.CloseButtonType, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="Individual">
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.CloseButtonType, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="IndividualOnMouseOver">
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Hidden" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.CloseButtonType, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="Extended">
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Hidden" />
            </DataTrigger>
            <Trigger SourceName="PART_PinButton" Property="IsChecked" Value="True">
                <Setter TargetName="PinButtonPath2" Property="Data" Value="M7.87289 3.685C7.95763 3.5582 8 3.4182 8 3.26498C8 3.1646 7.98014 3.06686 7.94042 2.97177C7.90334 2.87667 7.84906 2.79346 7.77756 2.72214L5.27507 0.221893C5.20622 0.153214 5.12413 0.0990601 5.0288 0.059433C4.93612 0.0198135 4.83946 0 4.73883 0C4.58788 0 4.44886 0.040947 4.32175 0.122833C4.19464 0.20208 4.10063 0.310387 4.03972 0.447746L3.15789 2.38534C3.12877 2.44874 3.08375 2.49232 3.02284 2.5161L0.667328 3.45517C0.619663 3.47366 0.581264 3.50404 0.552135 3.54631C0.523006 3.58858 0.508442 3.6348 0.508442 3.685C0.508442 3.75632 0.5336 3.81708 0.583912 3.86726L2.18471 5.4641L0 7.63942V8H0.357498L2.54221 5.82071L4.143 7.41753C4.19331 7.46773 4.2529 7.49281 4.32175 7.49281C4.38 7.49281 4.43032 7.47565 4.47269 7.44131C4.51771 7.40697 4.54816 7.36339 4.56405 7.31055L5.25919 5.00446C5.26978 4.96747 5.28831 4.93578 5.3148 4.90936C5.34128 4.88031 5.37173 4.85918 5.40616 4.84596L7.53128 3.96236C7.67428 3.90424 7.78815 3.81178 7.87289 3.685Z"/>
                <Setter TargetName="PART_PinButton" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="0" />
                    </Setter.Value>
                </Setter>
            </Trigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=TabControlExt.CloseButtonType, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="IndividualOnMouseOver" />
                    <Condition Binding="{Binding Path=IsMouseOver, ElementName=Bd}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=TabControlExt.CloseButtonType, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="Extended" />
                    <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=TabControlExt.CloseButtonType,RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="Extended" />
                    <Condition Binding="{Binding Path=IsMouseOver, ElementName=Bd}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:TabControlExt.Image), RelativeSource={RelativeSource Self}}" Value="{x:Null}">
                <Setter TargetName="PART_Image" Property="UIElement.Visibility" Value="Collapsed" />
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=TabControlExt.TabStripPlacement, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Top}" Value="Left" />
                    <Condition Binding="{Binding Path=RotateTextWhenVertical, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=false}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="Bd" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="90" />
                    </Setter.Value>
                </Setter>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=TabControlExt.TabStripPlacement,  RelativeSource={RelativeSource Mode=Self}, FallbackValue=Top}" Value="Right" />
                    <Condition Binding="{Binding Path=RotateTextWhenVertical, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=false}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="Bd" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="-90" />
                    </Setter.Value>
                </Setter>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=TabControlExt.TabStripPlacement, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Top}" Value="Left" />
                </MultiDataTrigger.Conditions>
                <Setter Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="180" />
                    </Setter.Value>
                </Setter>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:TabControlExt.IsEditing), RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_EditHeader" Property="Visibility" Value="Visible" />
                <Setter TargetName="Content" Property="Visibility" Value="Collapsed" />
            </MultiDataTrigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                <Setter Property="BorderThickness" TargetName="Bd" Value="{StaticResource Windows11Dark.BorderThickness1110}"/>
            </Trigger>
            <Trigger Property="IsNewTab" Value="True">
                <Setter TargetName="Content" Property="ContentTemplate" Value="{Binding Path=NewTabButtonTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}" />
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Collapsed" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate TargetType="{x:Type tools_controls:TabItemExt}" x:Key="SyncfusionCurvedTabItemExtControlTemplate">
        <Grid x:Name="templateRoot" 
                      SnapsToDevicePixels="true">
            <Rectangle x:Name="TabSeparator"
                        HorizontalAlignment="Right"
                        Width="1"
                        SnapsToDevicePixels="True"
                        Visibility="Visible"    
                        Stroke="{StaticResource BorderAlt}" 
                        StrokeThickness="1"
                        Margin="0,4,0,4">
                <Rectangle.RenderTransform>
                    <TranslateTransform X="1"/>
                </Rectangle.RenderTransform>
            </Rectangle>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition x:Name="topRow"/>
                    <RowDefinition x:Name="bottomRow" Height="Auto"/>
                </Grid.RowDefinitions>
                <Border
                            x:Name="Bd"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True"
                            ToolTip="{TemplateBinding ItemToolTip}" CornerRadius="4,4,0,0">
                    <DockPanel x:Name="PART_DockPanel" LastChildFill="True">
                        <Button
                                x:Name="PART_CloseButton"
                                Width="{Binding ElementName=Content, Path=ActualHeight}"
                                Height="{Binding ElementName=Content, Path=ActualHeight}"
                                Margin="1"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                BorderThickness="0"
                                Command="tools_controls:TabControlCommands.CloseTabItem"
                                DockPanel.Dock="Right"
                                Focusable="False"
                                Style="{StaticResource TabPanelCaptionButtonsStyle}"
                                Tag="TabItem"
                                Visibility="Collapsed">
                            <Button.Content>
                                <Path
                                        x:Name="CloseButtonPath2"
                                        Width="8"
                                        Height="8"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Fill="{Binding Foreground, ElementName=PART_CloseButton}"
                                        SnapsToDevicePixels="True"
                                        Stretch="Fill" 
                                        Data="M9.49512 8.87988L5.42773 4.8125L9.49512 0.745117C9.5817 0.658527 9.625 0.555988 9.625 0.4375C9.625 0.319012 9.5817 0.216473 9.49512 0.129883C9.40854 0.043293 9.306 0 9.1875 0C9.069 0 8.96646 0.043293 8.87988 0.129883L4.8125 4.19727L0.745117 0.129883C0.658539 0.043293 0.556 0 0.4375 0C0.319 0 0.216461 0.043293 0.129883 0.129883C0.0433044 0.216473 0 0.319012 0 0.4375C0 0.555988 0.0433044 0.658527 0.129883 0.745117L4.19727 4.8125L0.129883 8.87988C0.0433044 8.96647 0 9.06901 0 9.1875C0 9.30599 0.0433044 9.40853 0.129883 9.49512C0.216461 9.58171 0.319 9.625 0.4375 9.625C0.556 9.625 0.658539 9.58171 0.745117 9.49512L4.8125 5.42773L8.87988 9.49512C8.96646 9.58171 9.069 9.625 9.1875 9.625C9.306 9.625 9.40854 9.58171 9.49512 9.49512C9.5817 9.40853 9.625 9.30599 9.625 9.1875C9.625 9.06901 9.5817 8.96647 9.49512 8.87988Z"/>
                            </Button.Content>
                        </Button>
                        <ToggleButton
                                x:Name="PART_PinButton"
                                Width="{Binding ElementName=Content, Path=ActualHeight}"
                                Height="{Binding ElementName=Content, Path=ActualHeight}"
                                Margin="1"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                BorderThickness="0"
                                Command="tools_controls:TabControlCommands.PinTabItem"
                                CommandParameter="{Binding IsChecked, RelativeSource={RelativeSource Self}}"
                                DockPanel.Dock="Right"
                                Tag="TabItem"
                                Style="{StaticResource TabItemCaptionButtonsStyle}"
                                Visibility="{Binding ShowPin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}, Converter={StaticResource TabControlExtCloseButtonTypeToVisibilityConverter}}">
                            <ToggleButton.Content>
                                <Path
                                        x:Name="PinButtonPath2"
                                        Width="10"
                                        Height="10"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Fill="{Binding Foreground, ElementName=PART_PinButton}"
                                        Stretch="Fill" 
                                        Data="M7.74219 3.74609C7.82552 3.62109 7.86719 3.48308 7.86719 3.33203C7.86719 3.23308 7.84766 3.13672 7.80859 3.04297C7.77214 2.94922 7.71875 2.86719 7.64844 2.79688L5.1875 0.332031C5.11979 0.264328 5.03906 0.210938 4.94531 0.171875C4.85417 0.132812 4.75911 0.113281 4.66016 0.113281C4.51172 0.113281 4.375 0.153641 4.25 0.234375C4.125 0.3125 4.03255 0.419266 3.97266 0.554688L3.10547 2.46484C3.07682 2.52734 3.03255 2.57031 2.97266 2.59375L0.65625 3.51953C0.609375 3.53777 0.571615 3.5677 0.542969 3.60938C0.514322 3.65105 0.5 3.69661 0.5 3.74609C0.5 3.81641 0.52474 3.8763 0.574219 3.92578L2.14844 5.5L0 7.64453V8H0.351562L2.5 5.85156L4.07422 7.42578C4.1237 7.47527 4.18229 7.5 4.25 7.5C4.30729 7.5 4.35677 7.48308 4.39844 7.44922C4.44271 7.41536 4.47266 7.37239 4.48828 7.32031L5.17188 5.04688C5.18229 5.01042 5.20052 4.97917 5.22656 4.95312C5.25261 4.92448 5.28255 4.90364 5.31641 4.89062L7.40625 4.01953C7.54688 3.96223 7.65886 3.87109 7.74219 3.74609ZM4.51953 0.652344C4.5612 0.626297 4.60807 0.613281 4.66016 0.613281C4.72526 0.613281 4.78255 0.638016 4.83203 0.6875L7.29688 3.14844C7.34375 3.19531 7.36719 3.25391 7.36719 3.32422C7.36719 3.3763 7.35286 3.42317 7.32422 3.46484C7.29818 3.50391 7.26172 3.53386 7.21484 3.55469L5.12109 4.42969C5.01953 4.47395 4.92969 4.53777 4.85156 4.62109C4.77604 4.70442 4.72266 4.79948 4.69141 4.90625L4.12891 6.77734L1.19531 3.83984L3.15625 3.05859C3.24739 3.02214 3.32812 2.97136 3.39844 2.90625C3.46875 2.83855 3.52344 2.76042 3.5625 2.67188L4.42969 0.761719C4.45052 0.714844 4.48047 0.678391 4.51953 0.652344Z"/>
                            </ToggleButton.Content>
                            <ToggleButton.LayoutTransform>
                                <RotateTransform Angle="0" />
                            </ToggleButton.LayoutTransform>
                        </ToggleButton>
                        <Image
                                x:Name="PART_Image"
                                Width="{TemplateBinding tools_controls:TabControlExt.ImageWidth}"
                                Height="{TemplateBinding tools_controls:TabControlExt.ImageHeight}"
                                Margin="{TemplateBinding IconMargin}"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                DockPanel.Dock="{Binding Path=(tools_controls:TabControlExt.ImageAlignment), Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TabControlExtImageAlignmentToDockConverter}}"
                                SnapsToDevicePixels="True"
                                Source="{TemplateBinding tools_controls:TabControlExt.Image}"
                                Stretch="Fill" />
                        <Grid x:Name="HeaderGrid">
                            <ContentPresenter
                                x:Name="Content"
                                Margin="{TemplateBinding HeaderMargin}"
                                HorizontalAlignment="{Binding HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                                VerticalAlignment="{Binding VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                                Content="{TemplateBinding HeaderedContentControl.Header}"
                                ContentSource="Header"
                                ContentTemplate="{TemplateBinding HeaderedContentControl.HeaderTemplate}"
                                RecognizesAccessKey="False"
                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                                TextElement.Foreground="{TemplateBinding Foreground}">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                            <ContentPresenter
                                x:Name="PART_EditHeader"
                                Margin="{TemplateBinding HeaderMargin}"
                                HorizontalAlignment="{Binding HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                                VerticalAlignment="{Binding VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                                Content="{TemplateBinding HeaderedContentControl.Header}"
                                ContentSource="Header"
                                RecognizesAccessKey="True"
                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                                TextElement.FontWeight="{TemplateBinding FontWeight}"
                                TextElement.Foreground="{TemplateBinding Foreground}"
                                Visibility="Collapsed" />
                        </Grid>
                    </DockPanel>
                </Border>
                <Grid
                            Grid.Row="1"
                            Name="Curved"
                            Visibility="Collapsed"
                            Background="{TemplateBinding Background}">
                    <Path
                                HorizontalAlignment="Left"
                                VerticalAlignment="Bottom"
                                Data="M0,4 C 2,4 4,2 4,0 L 4,4 0,4z"
                                Fill="{TemplateBinding Background}"
                                Stroke="{TemplateBinding Background}"
                                StrokeLineJoin="Round">
                        <Path.RenderTransform>
                            <TranslateTransform X="-4"/>
                        </Path.RenderTransform>
                    </Path>
                    <Path
                                HorizontalAlignment="Left"
                                VerticalAlignment="Bottom"
                                Data="M0,4 C 2,4 4,2 4,0"
                                StrokeThickness="1"
                                Stroke="{TemplateBinding BorderBrush}"
                                StrokeLineJoin="Round">
                        <Path.RenderTransform>
                            <TranslateTransform X="-4"/>
                        </Path.RenderTransform>
                    </Path>
                    <Path
                                HorizontalAlignment="Right"
                                VerticalAlignment="Bottom"
                                Data="M0,0 C 0,2 2,4 4,4 L 0,4 0,0z"
                                Fill="{TemplateBinding Background}"
                                Stroke="{TemplateBinding Background}"
                                StrokeLineJoin="Round">
                        <Path.RenderTransform>
                            <TranslateTransform X="4"/>
                        </Path.RenderTransform>
                    </Path>
                    <Path
                                HorizontalAlignment="Right"
                                VerticalAlignment="Bottom"
                                Data="M0,0 C 0,2 2,4 4,4"
                                StrokeThickness="1"
                                Stroke="{TemplateBinding BorderBrush}"
                                StrokeLineJoin="Round">
                        <Path.RenderTransform>
                            <TranslateTransform X="4"/>
                        </Path.RenderTransform>
                    </Path>
                </Grid>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="HeaderMargin" Value="12,9,12,8" />
                <Setter Property="IconMargin" Value="12,10,0,12" />
                <Setter TargetName="PART_CloseButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                <Setter TargetName="PART_CloseButton" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                <Setter TargetName="PART_PinButton" Property="MinWidth" Value="{StaticResource TouchMode.MinHeight}" />
                <Setter TargetName="PART_PinButton" Property="MinHeight" Value="{StaticResource TouchMode.MinSize}" />
                <Setter TargetName="PART_CloseButton" Property="VerticalAlignment" Value="Center" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.TabItemHeaderStyle, RelativeSource={RelativeSource Self}, FallbackValue=Underline}" Value="Curve">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="HeaderMargin" Value="8 0" />
                <Setter Property="BorderThickness" Value="0 0 0 1"/>
            </DataTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition SourceName="PART_PinButton" Property="IsChecked" Value="True"/>
                    <Condition Property="sfskin:SfSkinManager.SizeMode" Value="Touch"/>
                </MultiTrigger.Conditions>
                <Setter TargetName="PART_PinButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                <Setter TargetName="PART_PinButton" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition SourceName="PART_PinButton" Property="IsChecked" Value="True"/>
                    <Condition Property="sfskin:SfSkinManager.SizeMode" Value="Default"/>
                </MultiTrigger.Conditions>
                <Setter TargetName="PART_PinButton" Property="MinHeight" Value="16" />
                <Setter TargetName="PART_PinButton" Property="MinWidth" Value="16" />
            </MultiTrigger>
            <DataTrigger Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" >
                <Setter TargetName="PART_EditHeader" Property="ContentTemplate" Value="{Binding EditHeaderTemplate, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}" />
            </DataTrigger>
            <Trigger Property="tools_controls:TabControlExt.ImageHeight" Value="{x:Static sys:Double.NaN}">
                <Setter TargetName="PART_Image" Property="Stretch" Value="Uniform" />
            </Trigger>

            <Trigger Property="tools_controls:TabControlExt.ImageWidth" Value="{x:Static sys:Double.NaN}">
                <Setter TargetName="PART_Image" Property="Stretch" Value="Uniform" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="tools_controls:TabControlExt.ImageHeight" Value="{x:Static sys:Double.NaN}" />
                    <Condition Property="tools_controls:TabControlExt.ImageWidth" Value="{x:Static sys:Double.NaN}" />
                </MultiTrigger.Conditions>
                <Setter TargetName="PART_Image" Property="Stretch" Value="None" />
            </MultiTrigger>
            <Trigger SourceName="Bd" Property="IsMouseOver" Value="true">
                <Setter Property="Background" Value="{Binding Path=HoverBackground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="BorderBrush" Value="{Binding Path=TabControlExt.TabItemHoverBorderBrush, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="Foreground" Value="{Binding Path=TabControlExt.TabItemHoverForeground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="Opacity" TargetName="TabSeparator" Value="0"/>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="HoverBackground" Value="{x:Null}"/>
                    <Condition Property="IsMouseOver" Value="True"/>
                </MultiTrigger.Conditions>
                <Setter Property="Background" Value="{Binding Path=TabControlExt.TabItemHoverBackground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="BorderBrush" Value="{Binding Path=TabControlExt.TabItemHoverBorderBrush, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="Foreground" Value="{Binding Path=TabControlExt.TabItemHoverForeground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="Opacity" TargetName="TabSeparator" Value="0"/>
            </MultiTrigger>
            <DataTrigger Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True">
                <Setter Property="Background" Value="{Binding Path=TabControlExt.TabItemSelectedBackground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="BorderBrush" Value="{Binding Path=TabControlExt.TabItemSelectedBorderBrush, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="Foreground" Value="{Binding Path=TabControlExt.TabItemSelectedForeground, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Transparent}" />
                <Setter Property="TextElement.FontWeight" TargetName="Content" Value="{Binding Path=TabControlExt.SelectedItemFontWeight, RelativeSource={RelativeSource Mode=TemplatedParent},FallbackValue=SemiBold}" />
                <Setter Property="TextElement.FontWeight" TargetName="PART_EditHeader" Value="{Binding Path=TabControlExt.SelectedItemFontWeight, RelativeSource={RelativeSource Mode=TemplatedParent},FallbackValue=SemiBold}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.TabStripPlacement, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Top}" Value="Bottom">
                <Setter TargetName="topRow" Property="Height" Value="Auto"/>
                <Setter TargetName="bottomRow" Property="Height" Value="*"/>
                <Setter TargetName="Bd" Property="Grid.Row" Value="1"/>
                <Setter TargetName="Curved" Property="Grid.Row" Value="0"/>
                <Setter TargetName="Curved" Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="1" ScaleY="-1"/>
                    </Setter.Value>
                </Setter>
                <Setter Property="Padding" Value="1 0 1 1" />
                <Setter Property="BorderThickness" Value="0 1 0 0"/>
                <Setter TargetName="Curved" Property="RenderTransformOrigin" Value="0.5,0.5"/>
                <Setter TargetName="Bd" Property="CornerRadius" Value="0,0,4,4"/>
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True"/>
                    <Condition Binding="{Binding Path=TabControlExt.TabStripPlacement, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Top}" Value="Top"/>
                </MultiDataTrigger.Conditions>
                <Setter Property="BorderThickness" Value="1,1,1,0"/>
                <Setter Property="Padding" Value="0,0,0,1"/>
                <Setter Property="Margin" TargetName="PART_DockPanel" Value="0,0,0,-4.5"/>
                <Setter Property="Opacity" TargetName="TabSeparator" Value="0"/>
                <Setter Property="Visibility" TargetName="Curved" Value="Visible"/>
            </MultiDataTrigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True"/>
                    <Condition Binding="{Binding Path=TabControlExt.TabStripPlacement, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Top}" Value="Left"/>
                </MultiDataTrigger.Conditions>
                <Setter Property="BorderThickness" Value="1,1,1,0"/>
                <Setter Property="Padding" Value="0,0,0,1"/>
                <Setter Property="Margin" TargetName="PART_DockPanel" Value="0,0,0,-4.5"/>
                <Setter Property="Opacity" TargetName="TabSeparator" Value="0"/>
                <Setter Property="Visibility" TargetName="Curved" Value="Visible"/>
            </MultiDataTrigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True"/>
                    <Condition Binding="{Binding Path=TabControlExt.TabStripPlacement, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Top}" Value="Bottom"/>
                </MultiDataTrigger.Conditions>
                <Setter Property="BorderThickness" Value="1,0,1,1"/>
                <Setter Property="Padding" Value="0,1,0,0"/>
                <Setter Property="Margin" TargetName="PART_DockPanel" Value="0,-4.5,0,0"/>
                <Setter Property="Opacity" TargetName="TabSeparator" Value="0"/>
                <Setter Property="Visibility" TargetName="Curved" Value="Visible"/>
            </MultiDataTrigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True"/>
                    <Condition Binding="{Binding Path=TabControlExt.TabStripPlacement, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Top}" Value="Right"/>
                </MultiDataTrigger.Conditions>
                <Setter Property="BorderThickness" Value="1,1,1,0"/>
                <Setter Property="Padding" Value="0,0,0,1"/>
                <Setter Property="Margin" TargetName="PART_DockPanel" Value="0,0,0,-4.5"/>
                <Setter Property="Opacity" TargetName="TabSeparator" Value="0"/>
                <Setter Property="Visibility" TargetName="Curved" Value="Visible"/>
            </MultiDataTrigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.CloseButtonType, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="Both">
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.CloseButtonType, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="Individual">
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.CloseButtonType, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="IndividualOnMouseOver">
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Hidden" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.CloseButtonType, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="Extended">
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Hidden" />
            </DataTrigger>
            <Trigger SourceName="PART_PinButton" Property="IsChecked" Value="True">
                <Setter TargetName="PinButtonPath2" Property="Data" Value="M7.87289 3.685C7.95763 3.5582 8 3.4182 8 3.26498C8 3.1646 7.98014 3.06686 7.94042 2.97177C7.90334 2.87667 7.84906 2.79346 7.77756 2.72214L5.27507 0.221893C5.20622 0.153214 5.12413 0.0990601 5.0288 0.059433C4.93612 0.0198135 4.83946 0 4.73883 0C4.58788 0 4.44886 0.040947 4.32175 0.122833C4.19464 0.20208 4.10063 0.310387 4.03972 0.447746L3.15789 2.38534C3.12877 2.44874 3.08375 2.49232 3.02284 2.5161L0.667328 3.45517C0.619663 3.47366 0.581264 3.50404 0.552135 3.54631C0.523006 3.58858 0.508442 3.6348 0.508442 3.685C0.508442 3.75632 0.5336 3.81708 0.583912 3.86726L2.18471 5.4641L0 7.63942V8H0.357498L2.54221 5.82071L4.143 7.41753C4.19331 7.46773 4.2529 7.49281 4.32175 7.49281C4.38 7.49281 4.43032 7.47565 4.47269 7.44131C4.51771 7.40697 4.54816 7.36339 4.56405 7.31055L5.25919 5.00446C5.26978 4.96747 5.28831 4.93578 5.3148 4.90936C5.34128 4.88031 5.37173 4.85918 5.40616 4.84596L7.53128 3.96236C7.67428 3.90424 7.78815 3.81178 7.87289 3.685Z"/>
                <Setter TargetName="PART_PinButton" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="0" />
                    </Setter.Value>
                </Setter>
            </Trigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=TabControlExt.CloseButtonType, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="IndividualOnMouseOver" />
                    <Condition Binding="{Binding Path=IsMouseOver, ElementName=Bd}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=TabControlExt.CloseButtonType, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="Extended" />
                    <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=TabControlExt.CloseButtonType,RelativeSource={RelativeSource Mode=Self}, FallbackValue=Common}" Value="Extended" />
                    <Condition Binding="{Binding Path=IsMouseOver, ElementName=Bd}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:TabControlExt.Image), RelativeSource={RelativeSource Self}}" Value="{x:Null}">
                <Setter TargetName="PART_Image" Property="UIElement.Visibility" Value="Collapsed" />
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=TabControlExt.TabStripPlacement, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Top}" Value="Left" />
                    <Condition Binding="{Binding Path=RotateTextWhenVertical, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=false}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="Bd" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="90" />
                    </Setter.Value>
                </Setter>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=TabControlExt.TabStripPlacement,  RelativeSource={RelativeSource Mode=Self}, FallbackValue=Top}" Value="Right" />
                    <Condition Binding="{Binding Path=RotateTextWhenVertical, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=false}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="Bd" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="-90" />
                    </Setter.Value>
                </Setter>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=TabControlExt.TabStripPlacement, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Top}" Value="Left" />
                </MultiDataTrigger.Conditions>
                <Setter Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="180" />
                    </Setter.Value>
                </Setter>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:TabControlExt.IsEditing), RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_EditHeader" Property="Visibility" Value="Visible" />
                <Setter TargetName="Content" Property="Visibility" Value="Collapsed" />
            </MultiDataTrigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                <Setter Property="BorderThickness" TargetName="Bd" Value="{StaticResource Windows11Dark.BorderThickness1110}"/>
            </Trigger>
            <Trigger Property="IsNewTab" Value="True">
                <Setter TargetName="Content" Property="ContentTemplate" Value="{Binding Path=NewTabButtonTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}" />
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Collapsed" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SyncfusionTabItemExtStyle" TargetType="{x:Type tools_controls:TabItemExt}">
        <Setter Property="TextElement.Foreground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="TabItemContextMenuStyle" Value="{StaticResource TabItemContextMenu}" />
        <Setter Property="TabItemContextMenuItemStyle" Value="{StaticResource SyncfusionCustomMenuItemStyle}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="MinHeight" Value="24" />
        <Setter Property="IconMargin" Value="6 0" />
        <Setter Property="Margin" Value="0" />
        <Setter Property="Padding" Value="1 1 1 0" />
        <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="Control.VerticalContentAlignment" Value="Stretch" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="HeaderMargin" Value="6 0" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness0002}"/>
        <Setter Property="Template" Value="{StaticResource SyncfusionTabItemExtControlTemplate}"/>
        <Setter Property="FrameworkElement.FocusVisualStyle">
            <Setter.Value>
                <Style TargetType="IFrameworkInputElement">
                    <Setter Property="Control.Template">
                        <Setter.Value>
                            <x:Null />
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Path=TabControlExt.TabItemHeaderStyle, RelativeSource={RelativeSource Self}, FallbackValue=Underline}" Value="Curve">
                <Setter Property="Template" Value="{StaticResource SyncfusionCurvedTabItemExtControlTemplate}"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.TabItemHeaderStyle, RelativeSource={RelativeSource Self}, FallbackValue=Underline}" Value="Underline">
                <Setter Property="Template" Value="{StaticResource SyncfusionTabItemExtControlTemplate}"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabControlExt.TabItemHeaderStyle, RelativeSource={RelativeSource Self}, FallbackValue=Underline}" Value="Fill">
                <Setter Property="Template" Value="{StaticResource SyncfusionFilledTabItemExtControlTemplate}"/>
                <Setter Property="BorderThickness" Value="0"/>
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:TabControlExt.UseCustomEditableTemplate), RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:TabControlExt.IsEditing), RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="HeaderTemplate" Value="{Binding Path=(tools_controls:TabControlExt.CustomEditableTemplate), RelativeSource={RelativeSource Self}}" />
            </MultiDataTrigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CurveKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTabItemExtStyle}" TargetType="{x:Type tools_controls:TabItemExt}" />

    <Style TargetType="{x:Type tools_controls:TabLayoutPanel}" x:Key="SyncfusionTabLayoutPanelStyle">
        <Style.Triggers>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=TabItemHeaderStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Underline}" Value="Underline"/>
                </MultiDataTrigger.Conditions>
                <Setter Property="Margin"  Value="0" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=TabItemHeaderStyle, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Underline}" Value="Curve"/>
                </MultiDataTrigger.Conditions>
                <Setter Property="Margin"  Value="4,0" />
            </MultiDataTrigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="{x:Type tools_controls:TabLayoutPanel}" BasedOn="{StaticResource SyncfusionTabLayoutPanelStyle}"/>

    <Style x:Key="SyncfusionTabControlExtStyle" TargetType="{x:Type tools_controls:TabControlExt}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="TabPanelStyle" Value="{StaticResource SyncfusionTabControlExtTabPanelAdvStyle}" />
        <Setter Property="TabItemSelectedBackground" Value="Transparent" />
        <Setter Property="TabItemSelectedBorderBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="TabItemHoverBackground" Value="{StaticResource ContentBackgroundHovered}" />
        <Setter Property="TabItemHoverForeground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="TabItemHoverBorderBrush" Value="Transparent" />
        <Setter Property="TabItemSelectedForeground" Value="{StaticResource ContentForeground}" />
        <Setter Property="SelectedItemFontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="DragMarkerColor" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="ScrollingButtonStyle" Value="{StaticResource WPFGlyphButtonStyle}" />
        <Setter Property="TabPanelBackground" Value="Transparent" />
        <Setter Property="TabListContextMenuStyle" Value="{StaticResource WPFContextMenuStyle}" />
        <Setter Property="TabListContextMenuItemStyle" Value="{StaticResource SyncfusionMenuItemStyle}" />
        <Setter Property="AllowDrop" Value="True" />
        <Setter Property="Panel.Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="DragMarkerStyle" Value="{StaticResource SyncfusionDragMarkerTemplatedAdornerInternalControlStyle}" />
        <Setter Property="NewTabButtonTemplate" Value="{StaticResource SyncfusionTabNewButtonDataTemplate}" />
        <Setter Property="NewButtonStyle" Value="{StaticResource SyncfusionNewButtonStyle}"/>
        <Setter Property="NewButtonBackground" Value="Transparent" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="TabItemHeaderStyle" Value="Curve" />
        <Setter Property="ItemContainerStyle" Value="{StaticResource SyncfusionTabItemExtStyle}" />
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:TabControlExt}">
                    <Grid
                                x:Name="TabControlGrid"
                                ClipToBounds="False"
                                KeyboardNavigation.TabNavigation="Local"
                                SnapsToDevicePixels="True">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition x:Name="ColumnDefinition0" />
                            <ColumnDefinition x:Name="ColumnDefinition1" Width="0" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition x:Name="RowDefinition0" Height="Auto" />
                            <RowDefinition x:Name="RowDefinition1" Height="*" />
                        </Grid.RowDefinitions>
                        <tools_controls:HeaderPanel
                                    x:Name="HeaderPanel"
                                    Grid.Row="0"
                                    Grid.Column="0"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    Panel.ZIndex="1"
                                    Background="{TemplateBinding TabPanelBackground}"
                                    Focusable="False">
                            <tools_controls:TabPanelAdv
                                        x:Name="PART_TabPanel"
                                        HorizontalAlignment="Stretch"
                                        VerticalAlignment="Stretch"
                                        DockPanel.Dock="Right"
                                        Focusable="False"
                                        Style="{TemplateBinding TabPanelStyle}">
                                <tools_controls:TabLayoutPanel
                                            x:Name="PART_TabLayoutPanel"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Top"
                                            AllowDrop="True"
                                            IsItemsHost="True"
                                            KeyboardNavigation.TabIndex="1" />
                            </tools_controls:TabPanelAdv>
                        </tools_controls:HeaderPanel>
                        <Border
                                    x:Name="ContentPanel"
                                    Grid.Row="1"
                                    Grid.Column="0"
                                    AllowDrop="True"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    KeyboardNavigation.DirectionalNavigation="Contained"
                                    KeyboardNavigation.TabIndex="2"
                                    KeyboardNavigation.TabNavigation="Local">
                            <Border
                                        x:Name="PART_ContentPanelBorder"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="0,0,0,0">
                                <Border
                                            x:Name="PART_ContentPanelInnerBorder"
                                            Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}">
                                    <Grid>
                                        <Grid
                                                x:Name="PART_ContentHolder"
                                                Margin="{TemplateBinding Control.Padding}"
                                                AllowDrop="True"
                                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                                                Visibility="Visible"/>

                                        <ContentPresenter
                                                x:Name="PART_SelectedContentHost"
                                                Margin="{TemplateBinding Control.Padding}"
                                                AllowDrop="True"
                                                Content="{TemplateBinding SelectedContent}"
                                                ContentSource="SelectedContent"
                                                ContentTemplate="{TemplateBinding SelectedContentTemplate}"
                                                ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}"
                                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}" Visibility="Collapsed"/>
                                    </Grid>
                                </Border>
                            </Border>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsDisableUnloadTabItemExtContent" Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Visibility" TargetName="PART_SelectedContentHost" Value="Visible"/>
                            <Setter Property="Visibility" TargetName="PART_ContentHolder" Value="Collapsed"/>
                        </MultiTrigger>
                        <Trigger Property="TabControl.TabStripPlacement" Value="Bottom">
                            <Setter TargetName="HeaderPanel" Property="Grid.Row" Value="1" />
                            <Setter TargetName="ContentPanel" Property="Grid.Row" Value="0" />
                            <Setter TargetName="RowDefinition0" Property="RowDefinition.Height" Value="*" />
                            <Setter TargetName="RowDefinition1" Property="RowDefinition.Height" Value="Auto" />
                            <Setter TargetName="HeaderPanel" Property="Margin">
                                <Setter.Value>
                                    <Thickness Left="1" />
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="TabControl.TabStripPlacement" Value="Left">
                            <Setter TargetName="HeaderPanel" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="90" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="HeaderPanel" Property="Grid.Row" Value="0" />
                            <Setter TargetName="ContentPanel" Property="Grid.Row" Value="0" />
                            <Setter TargetName="HeaderPanel" Property="Grid.Column" Value="0" />
                            <Setter TargetName="ContentPanel" Property="Grid.Column" Value="1" />
                            <Setter TargetName="ColumnDefinition0" Property="ColumnDefinition.Width" Value="Auto" />
                            <Setter TargetName="ColumnDefinition1" Property="ColumnDefinition.Width" Value="*" />
                            <Setter TargetName="RowDefinition0" Property="RowDefinition.Height" Value="*" />
                            <Setter TargetName="RowDefinition1" Property="RowDefinition.Height" Value="0" />
                            <Setter TargetName="HeaderPanel" Property="VerticalAlignment" Value="Bottom" />
                        </Trigger>
                        <Trigger Property="TabControl.TabStripPlacement" Value="Right">
                            <Setter TargetName="HeaderPanel" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="90" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="HeaderPanel" Property="Grid.Row" Value="0" />
                            <Setter TargetName="ContentPanel" Property="Grid.Row" Value="0" />
                            <Setter TargetName="HeaderPanel" Property="Grid.Column" Value="1" />
                            <Setter TargetName="ContentPanel" Property="Grid.Column" Value="0" />
                            <Setter TargetName="ColumnDefinition0" Property="ColumnDefinition.Width" Value="*" />
                            <Setter TargetName="ColumnDefinition1" Property="ColumnDefinition.Width" Value="Auto" />
                            <Setter TargetName="RowDefinition0" Property="RowDefinition.Height" Value="*" />
                            <Setter TargetName="RowDefinition1" Property="RowDefinition.Height" Value="0" />
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="Underline"/>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Right"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="1"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="Underline"/>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Right"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="1 1 0 1"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="Underline"/>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Left"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="1"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="Underline"/>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Left"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="0 1 1 1"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="Underline"/>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Bottom"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="1"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="Underline"/>
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Bottom"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="1 1 1 0"/>
                        </MultiDataTrigger>
                        <Trigger Property="UIElement.IsEnabled" Value="False">
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Path=TabItemHeaderStyle, RelativeSource={RelativeSource Self}, FallbackValue=Underline}" Value="Curve">
                <Setter Property="TabItemSelectedBackground"  Value="{StaticResource ContentBackgroundAlt1}" />
                <Setter Property="TabItemSelectedBorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="TabPanelBackground" Value="{StaticResource ContentBackgroundAlt2}" />
                <Setter Property="BorderThickness" Value="1 0 1 1"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabItemHeaderStyle, RelativeSource={RelativeSource Self}, FallbackValue=Underline}" Value="Underline">
                <Setter Property="TabItemSelectedBackground"  Value="Transparent" />
                <Setter Property="TabItemSelectedBorderBrush" Value="{StaticResource PrimaryBackground}" />
                <Setter Property="TabPanelBackground" Value="Transparent" />
                <Setter Property="BorderThickness" Value="1"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabItemHeaderStyle, RelativeSource={RelativeSource Self}, FallbackValue=Underline}" Value="Fill">
                <Setter Property="TabItemSelectedBackground" Value="{StaticResource PrimaryBackground}" />
                <Setter Property="TabItemSelectedForeground" Value="{StaticResource PrimaryForeground}" />
                <Setter Property="TabItemSelectedBorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="TabPanelBackground" Value="Transparent" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=TabItemLayout, RelativeSource={RelativeSource Self}}" Value="SingleLine">
                <Setter Property="Control.Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:TabControlExt}">
                            <Grid
                                x:Name="TabControlGrid"
                                ClipToBounds="False"
                                KeyboardNavigation.TabNavigation="Local"
                                SnapsToDevicePixels="True">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition x:Name="ColumnDefinition0" />
                                    <ColumnDefinition x:Name="ColumnDefinition1" Width="0" />
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition x:Name="RowDefinition0" Height="Auto" />
                                    <RowDefinition x:Name="RowDefinition1" Height="*" />
                                </Grid.RowDefinitions>
                                <tools_controls:HeaderPanel
                                    x:Name="HeaderPanel"
                                    Grid.Row="0"
                                    Grid.Column="0"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    Panel.ZIndex="1"
                                    Background="{TemplateBinding TabPanelBackground}"
                                    Focusable="False">
                                    <tools_controls:TabPanelAdv
                                        x:Name="PART_TabPanel"
                                        HorizontalAlignment="Stretch"
                                        VerticalAlignment="Stretch"
                                        DockPanel.Dock="Right"
                                        Focusable="False"
                                        Style="{TemplateBinding TabPanelStyle}">
                                        <tools_controls:TabScrollViewer
                                            x:Name="PART_ScrollViewer"
                                            Margin="{Binding Margin, RelativeSource={RelativeSource FindAncestor, AncestorType=ContentPresenter}}"
                                            HorizontalScrollBarVisibility="Hidden"
                                            VerticalScrollBarVisibility="Hidden">
                                            <tools_controls:TabLayoutPanel
                                                x:Name="PART_TabLayoutPanel"
                                                HorizontalAlignment="Left"
                                                VerticalAlignment="Top"
                                                AllowDrop="True"
                                                IsItemsHost="True"
                                                KeyboardNavigation.TabIndex="1" />
                                        </tools_controls:TabScrollViewer>
                                    </tools_controls:TabPanelAdv>
                                </tools_controls:HeaderPanel>
                                <Border
                                    x:Name="ContentPanel"
                                    Grid.Row="1"
                                    Grid.Column="0"
                                    AllowDrop="True"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    KeyboardNavigation.DirectionalNavigation="Contained"
                                    KeyboardNavigation.TabIndex="2"
                                    KeyboardNavigation.TabNavigation="Local">
                                    <Border
                                        x:Name="PART_ContentPanelBorder"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="0,0,0,0">
                                        <Border
                                            x:Name="PART_ContentPanelInnerBorder"
                                            Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}">
                                            <Grid>
                                                <Grid
                                                x:Name="PART_ContentHolder"
                                                Margin="{TemplateBinding Control.Padding}"
                                                AllowDrop="True"
                                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                                                Visibility="Visible"/>

                                                <ContentPresenter
                                                x:Name="PART_SelectedContentHost"
                                                Margin="{TemplateBinding Control.Padding}"
                                                AllowDrop="True"
                                                Content="{TemplateBinding SelectedContent}"
                                                ContentSource="SelectedContent"
                                                ContentTemplate="{TemplateBinding SelectedContentTemplate}"
                                                ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}"
                                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}" Visibility="Collapsed"/>
                                            </Grid>
                                        </Border>
                                    </Border>
                                </Border>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsDisableUnloadTabItemExtContent" Value="false"/>
                                    </MultiTrigger.Conditions>
                                    <Setter Property="Visibility" TargetName="PART_SelectedContentHost" Value="Visible"/>
                                    <Setter Property="Visibility" TargetName="PART_ContentHolder" Value="Collapsed"/>
                                </MultiTrigger>
                                <Trigger Property="TabControl.TabStripPlacement" Value="Bottom">
                                    <Setter TargetName="HeaderPanel" Property="Grid.Row" Value="1" />
                                    <Setter TargetName="ContentPanel" Property="Grid.Row" Value="0" />
                                    <Setter TargetName="RowDefinition0" Property="RowDefinition.Height" Value="*" />
                                    <Setter TargetName="RowDefinition1" Property="RowDefinition.Height" Value="Auto" />
                                    <Setter TargetName="HeaderPanel" Property="Margin">
                                        <Setter.Value>
                                            <Thickness Left="1" />
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                                <Trigger Property="TabControl.TabStripPlacement" Value="Left">
                                    <Setter TargetName="HeaderPanel" Property="LayoutTransform">
                                        <Setter.Value>
                                            <RotateTransform Angle="90" />
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="HeaderPanel" Property="Grid.Row" Value="0" />
                                    <Setter TargetName="ContentPanel" Property="Grid.Row" Value="0" />
                                    <Setter TargetName="HeaderPanel" Property="Grid.Column" Value="0" />
                                    <Setter TargetName="ContentPanel" Property="Grid.Column" Value="1" />
                                    <Setter TargetName="ColumnDefinition0" Property="ColumnDefinition.Width" Value="Auto" />
                                    <Setter TargetName="ColumnDefinition1" Property="ColumnDefinition.Width" Value="*" />
                                    <Setter TargetName="RowDefinition0" Property="RowDefinition.Height" Value="*" />
                                    <Setter TargetName="RowDefinition1" Property="RowDefinition.Height" Value="0" />
                                    <Setter TargetName="HeaderPanel" Property="VerticalAlignment" Value="Stretch" />
                                </Trigger>
                                <Trigger Property="TabControl.TabStripPlacement" Value="Right">
                                    <Setter TargetName="HeaderPanel" Property="LayoutTransform">
                                        <Setter.Value>
                                            <RotateTransform Angle="90" />
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="HeaderPanel" Property="Grid.Row" Value="0" />
                                    <Setter TargetName="ContentPanel" Property="Grid.Row" Value="0" />
                                    <Setter TargetName="HeaderPanel" Property="Grid.Column" Value="1" />
                                    <Setter TargetName="ContentPanel" Property="Grid.Column" Value="0" />
                                    <Setter TargetName="ColumnDefinition0" Property="ColumnDefinition.Width" Value="*" />
                                    <Setter TargetName="ColumnDefinition1" Property="ColumnDefinition.Width" Value="Auto" />
                                    <Setter TargetName="RowDefinition0" Property="RowDefinition.Height" Value="*" />
                                    <Setter TargetName="RowDefinition1" Property="RowDefinition.Height" Value="0" />
                                </Trigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="Underline"/>
                                        <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Right"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="BorderThickness" Value="1"/>
                                </MultiDataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="Curve"/>
                                        <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Right"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="BorderThickness" Value="1 1 0 1"/>
                                </MultiDataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="Underline"/>
                                        <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Left"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="BorderThickness" Value="1"/>
                                </MultiDataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="Curve"/>
                                        <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Left"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="BorderThickness" Value="0 1 1 1"/>
                                </MultiDataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="Underline"/>
                                        <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Bottom"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="BorderThickness" Value="1"/>
                                </MultiDataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding TabItemHeaderStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="Curve"/>
                                        <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type TabControl}}, FallbackValue=Top}" Value="Bottom"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="BorderThickness" Value="1 1 1 0"/>
                                </MultiDataTrigger>
                                <Trigger Property="UIElement.IsEnabled" Value="False">
                                    <Setter Property="TextElement.Foreground" Value="{DynamicResource ResourceKey={x:Static SystemColors.GrayTextBrushKey}}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </DataTrigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionTabControlExtStyle}" TargetType="{x:Type tools_controls:TabControlExt}" />
</ResourceDictionary>
