<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib"
                    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:scheduler="clr-namespace:Syncfusion.UI.Xaml.Scheduler;assembly=Syncfusion.SfScheduler.WPF">
    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="SfScheduler.TodayHighlight.Static.Background" Color="Green"/>

    <scheduler:TodayBorderSizeConverter x:Key="TodayBorderSizeConverter" FontSize="{StaticResource Windows11Dark.BodyTextStyle}" />

    <Style x:Key="SyncfusionViewHeaderControlStyle" TargetType="scheduler:ViewHeaderControl">
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,0,1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:ViewHeaderControl">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}">
                        <Border.BorderThickness>
                            <Thickness>0,0,0,1</Thickness>
                        </Border.BorderThickness>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Border BorderBrush="{TemplateBinding BorderBrush}" >
                                <Border.BorderThickness>
                                    <Thickness>0,0,1,0</Thickness>
                                </Border.BorderThickness>
                            </Border>
                            <ScrollViewer x:Name="PART_ViewHeaderScrollViewer" 
                                          Grid.Column="1"
                                  HorizontalScrollBarVisibility="Hidden" 
                                  IsDeferredScrollingEnabled="False" 
                                  VerticalScrollBarVisibility="Hidden">

                                <ScrollViewer.Resources>
                                    <Style TargetType="{x:Type ScrollBar}">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="{x:Type ScrollBar}">
                                                    <Border Background="Transparent" />
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </ScrollViewer.Resources>

                                <Grid x:Name="PART_ViewHeaderGrid">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="auto"/>
                                        <RowDefinition Height="auto"/>
                                    </Grid.RowDefinitions>

                                    <scheduler:ResourceHeaderPanel x:Name="PART_ResourcePanel" />

                                    <scheduler:ViewHeaderRowPanel x:Name="PART_ViewHeaderRowPanel" />
                                </Grid>

                            </ScrollViewer>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionViewHeaderControlStyle}" TargetType="scheduler:ViewHeaderControl" />

    <Style x:Key="SyncfusionViewHeaderBaseStyle" TargetType="scheduler:ViewHeaderBase">
        <Style.Triggers>
            <Trigger Property="IsToday" Value="True">
                <Setter Property="Foreground" Value="{StaticResource PrimaryColorForeground}"/>
            </Trigger>
        </Style.Triggers>
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Padding" Value="8,0,0,0"/>
        <Setter Property="IsEnabled" Value="True"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:ViewHeaderBase">
                    <Border  BorderThickness="{TemplateBinding BorderThickness}"
                        BorderBrush="{TemplateBinding BorderBrush}">
                        <ContentPresenter
                            Content="{TemplateBinding DataContext}"
                            ContentTemplate="{TemplateBinding ContentTemplate}"
							Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionViewHeaderBaseStyle}" TargetType="scheduler:ViewHeaderBase" />

    <DataTemplate x:Key="DayViewHeaderWindows11Template">
        <Grid x:Name="PART_Grid" 
      Background="{Binding Background}">
            <Border 
                x:Name="PART_TodayHightlightBorder"
                BorderBrush="{StaticResource PrimaryColorForeground}" 
                Margin="0,0,1,0"
                BorderThickness="0,3,0,0" 
                Visibility="Collapsed"/>
            <Grid HorizontalAlignment="Left"
                  Background="Transparent"
                  VerticalAlignment="{Binding VerticalContentAlignment}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="27"  />
                </Grid.RowDefinitions>
                <TextBlock x:Name="PART_DayText"
                           VerticalAlignment="Center"
                           Margin="8,3,0,0"
                           FontFamily="{Binding FontFamily}"
                           FontSize="{Binding FontSize}"
                           FontStyle="{Binding FontStyle}"
                           Foreground="{Binding Foreground}"
                           Text="{Binding DayText}" />
                <TextBlock x:Name="PART_DateText"
                           Grid.Row="1"
                           VerticalAlignment="Top"
                           TextAlignment="Left"
                           Margin="8,0,0,0"
                           FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                           FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                           Foreground="{StaticResource ContentForeground}"
                           FontStyle="{Binding FontStyle}"
                           Text="{Binding DateText}" >
                    <TextBlock.Style>
                        <Style>
                            <Style.Triggers>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding ElementName=PART_DateText, Path=IsMouseOver}" Value="True" />
                                        <Condition Binding="{Binding Path=IsDayView}" Value="False" />
                                        <Condition Binding="{Binding Path=AllowViewNavigation}" Value="True" />
                                    </MultiDataTrigger.Conditions>
                                    <MultiDataTrigger.Setters>
                                        <Setter  Property="TextBlock.TextDecorations" Value="Underline"/>
                                        <Setter  Property="TextBlock.Cursor" Value="Hand"/>
                                    </MultiDataTrigger.Setters>
                                </MultiDataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </Grid>
        </Grid>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding IsToday}" Value="True">
                <Setter TargetName="PART_DateText" Property="Foreground" Value="{StaticResource PrimaryColorForeground}"/>
                <Setter TargetName="PART_DateText" Property="FontWeight" Value="SemiBold"/>
                <Setter TargetName="PART_DayText" Property="FontWeight" Value="SemiBold"/>
                <Setter TargetName="PART_TodayHightlightBorder" Property="Visibility" Value="Visible"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                <Setter TargetName="PART_DayText" Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                <Setter TargetName="PART_DateText" Property="Foreground" Value="{StaticResource DisabledForeground}"/>
            </DataTrigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter TargetName="PART_TodayHightlightBorder" Property="BorderBrush" Value="{StaticResource BorderAlt4}"/>
                <Setter TargetName="PART_TodayHightlightBorder" Property="Visibility" Value="Visible"/>
            </Trigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding ElementName=PART_Grid, Path=IsMouseOver}" Value="true"/>
                    <Condition Binding="{Binding IsToday}" Value="True"/>
                </MultiDataTrigger.Conditions>
                <MultiDataTrigger.Setters>
                    <Setter TargetName="PART_TodayHightlightBorder" Property="BorderBrush" Value="{StaticResource PrimaryColorLight1}"/>
                </MultiDataTrigger.Setters>
            </MultiDataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <Style x:Key="SyncfusionDayViewHeaderStyle" TargetType="scheduler:DayViewHeader" BasedOn="{StaticResource SyncfusionViewHeaderBaseStyle}">
        <Setter Property="Padding" Value="0"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="VerticalAlignment" Value="Stretch"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="ContentTemplate" Value="{StaticResource DayViewHeaderWindows11Template}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:DayViewHeader">
                    <Border
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        HorizontalAlignment="Stretch" 
                        VerticalAlignment="Stretch"
                        BorderThickness="{StaticResource Windows11Dark.BorderThickness}">
                        <Grid>
                            <Border x:Name="PART_DayViewHeaderTopBorder" 
                                    BorderBrush="{StaticResource BorderAlt}"
                                    HorizontalAlignment="Stretch">
                                <Border.BorderThickness>
                                    <Thickness>0,0,1,0</Thickness>
                                </Border.BorderThickness>
                            </Border>
                            <ContentPresenter x:Name="PART_DayViewHeaderContentPresenter" 
                                              Height="{TemplateBinding Height}"
                                              Margin="0"
                                              Width="{TemplateBinding Width}"
                                              DataContext="{TemplateBinding DataContext}"
                                              Content="{TemplateBinding DataContext}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              VerticalAlignment="Stretch"
                                              HorizontalAlignment="Stretch"/>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionDayViewHeaderStyle}" TargetType="scheduler:DayViewHeader"  />

    <Style x:Key="SyncfusionTimelineViewHeaderStyle" TargetType="scheduler:TimelineViewHeader" BasedOn="{StaticResource SyncfusionViewHeaderBaseStyle}">
        <Style.Triggers>
            <Trigger Property="IsToday" Value="True">
                <Setter Property="FontWeight" Value="SemiBold"/>
            </Trigger>
        </Style.Triggers>
        <Setter Property="ContentTemplate">
            <Setter.Value>
                <DataTemplate>
                    <Grid HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Background="{Binding Background}">
                        <TextBlock x:Name="PART_DateText" 
                                   HorizontalAlignment="Left"
                                   VerticalAlignment="Center" 
                                   FontFamily="{Binding FontFamily}" 
                                   FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                   FontStyle="{Binding FontStyle}"
                                   Foreground="{Binding Foreground}"
                                   Text="{Binding DateText}" >
                            <TextBlock.Style>
                                <Style>
                                    <Style.Triggers>
                                        <MultiDataTrigger>
                                            <MultiDataTrigger.Conditions>
                                                <Condition Binding="{Binding ElementName=PART_DateText, Path=IsMouseOver}" Value="True" />
                                                <Condition Binding="{Binding Path=IsDayView}" Value="False" />
                                                <Condition Binding="{Binding Path=AllowViewNavigation}" Value="True" />
                                            </MultiDataTrigger.Conditions>
                                            <MultiDataTrigger.Setters>
                                                <Setter  Property="TextBlock.TextDecorations" Value="Underline"/>
                                                <Setter  Property="TextBlock.Cursor" Value="Hand"/>
                                            </MultiDataTrigger.Setters>
                                        </MultiDataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                    <DataTemplate.Triggers>
                        <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                            <Setter TargetName="PART_DateText"  Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding IsHitTestVisible}" Value="False">
                            <Setter TargetName="PART_DateText"  Property="TextDecorations" Value="Strikethrough"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ViewType}" Value="TimelineMonth">
                            <Setter TargetName="PART_DateText"  Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
                            <Setter TargetName="PART_DateText" Property="HorizontalAlignment" Value="Center"/>
                        </DataTrigger>
                    </DataTemplate.Triggers>
                </DataTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionTimelineViewHeaderStyle}" TargetType="scheduler:TimelineViewHeader" />

    <Style x:Key="SyncfusionMonthViewHeaderStyle" TargetType="scheduler:MonthViewHeader" BasedOn="{StaticResource SyncfusionViewHeaderBaseStyle}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate >
                    <Grid  Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                        <TextBlock HorizontalAlignment="Left" Padding="8,0,0,0"
                        FontFamily="{Binding FontFamily}"
                        FontSize="{Binding FontSize}"
                        FontStyle="{Binding FontStyle}"
                        Foreground="{Binding Foreground}"
                        VerticalAlignment="Center"
                        Text="{Binding DayText}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionMonthViewHeaderStyle}" TargetType="scheduler:MonthViewHeader" />
    
</ResourceDictionary>
