# 商用空调监控调试软件 - 项目架构文档

## 文档信息
- **文档版本**: 1.0
- **创建日期**: 2024-12-09
- **最后更新**: 2024-12-09
- **文档状态**: 草稿

## 1. 架构概述

### 1.1 架构原则
- **单一职责原则**: 每个模块只负责一个特定的功能领域
- **依赖倒置原则**: 高层模块不依赖低层模块，都依赖于抽象
- **开闭原则**: 对扩展开放，对修改关闭
- **接口隔离原则**: 使用多个专门的接口，而不是单一的总接口
- **松耦合高内聚**: 模块间依赖最小化，模块内功能高度相关

### 1.2 架构风格
- **分层架构**: 采用经典的分层架构模式
- **MVVM模式**: WPF层采用Model-View-ViewModel模式
- **依赖注入**: 使用DI容器管理对象生命周期
- **事件驱动**: 基于事件的松耦合通信机制

## 2. 系统架构

### 2.1 整体架构图
```
┌─────────────────────────────────────┐  ┌─────────────────────────────────────┐
│          AirMonitor.WPF             │  │     AirMonitor.LicenseGenerator     │
│  ┌─────────────┐  ┌─────────────┐   │  │  ┌─────────────┐  ┌─────────────┐   │
│  │    Views    │  │ ViewModels  │   │  │  │    Views    │  │ ViewModels  │   │
│  │             │  │             │   │  │  │             │  │             │   │
│  │  Controls   │  │ Converters  │   │  │  │ Converters  │  │  Services   │   │
│  └─────────────┘  └─────────────┘   │  │  └─────────────┘  └─────────────┘   │
└─────────────────────────────────────┘  └─────────────────────────────────────┘
                  │                                        │
                  │                                        │
                  └──────────────┐          ┌──────────────┘
                                 │          │
                                 ▼          ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                           AirMonitor.Core                                   │
│                          (公共类库)                                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Models    │  │ Interfaces  │  │   Enums     │  │ Constants   │        │
│  │             │  │             │  │             │  │             │        │
│  │ Utilities   │  │ Extensions  │  │ Validators  │  │   Helpers   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────────────────────┘
                                 ▲
                                 │
┌─────────────────────────────────────────────────────────────┐
│                  AirMonitor.Services                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Communication│  │ Data Collection│ │ License   │         │
│  │   Service   │  │   Service    │  │  Service    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              ▲
┌─────────────────────────────────────────────────────────────┐
│                AirMonitor.Infrastructure                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Data Access │  │ Serial Port │  │ Protocol    │         │
│  │   Layer     │  │   Handler   │  │   Parser    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 项目结构

#### 2.2.1 AirMonitor.Core (公共类库)
**职责**: 主程序和注册机项目的公共类库，提供共享的数据模型、接口和工具类
**依赖**: 无外部依赖
**被依赖**: AirMonitor.WPF, AirMonitor.LicenseGenerator, AirMonitor.Infrastructure, AirMonitor.Services
**内容**:
- **Models/**: 共享数据模型
  - `AirConditionerUnit.cs`: 空调机组模型
  - `SensorData.cs`: 传感器数据模型
  - `AlarmRecord.cs`: 报警记录模型
  - `ProtocolFrame.cs`: 协议帧模型
  - `LicenseInfo.cs`: 授权信息模型（主程序和注册机共享）
  - `HardwareFingerprintInfo.cs`: 硬件指纹信息模型
  - `ValidationResult.cs`: 验证结果模型
- **Interfaces/**: 共享服务接口定义
  - `ISerialCommunicationService.cs`: 串口通信服务接口
  - `IProtocolParserService.cs`: 协议解析服务接口
  - `IDataCollectionService.cs`: 数据采集服务接口
  - `ILicenseService.cs`: 授权服务接口（主程序和注册机共享）
  - `ICryptoService.cs`: 加密服务接口（主程序和注册机共享）
  - `IHardwareFingerprintService.cs`: 硬件指纹服务接口（主程序和注册机共享）
  - `IDatabaseService.cs`: 数据库服务接口
- **Enums/**: 共享枚举定义
  - `UnitStatus.cs`: 机组状态枚举
  - `SensorType.cs`: 传感器类型枚举
  - `AlarmLevel.cs`: 报警级别枚举
  - `LicenseType.cs`: 许可证类型枚举（主程序和注册机共享）
  - `LicenseErrorCode.cs`: 许可证错误代码枚举（主程序和注册机共享）
- **Constants/**: 共享常量定义
  - `LicenseConstants.cs`: 许可证相关常量
  - `CryptoConstants.cs`: 加密相关常量
  - `ApplicationConstants.cs`: 应用程序常量
- **Utilities/**: 共享工具类
  - `JsonHelper.cs`: JSON序列化工具
  - `FileHelper.cs`: 文件操作工具
  - `StringExtensions.cs`: 字符串扩展方法

#### 2.2.2 AirMonitor.Infrastructure (基础设施层)
**职责**: 提供技术基础设施和外部系统集成
**依赖**: AirMonitor.Core
**内容**:
- **Data/**: 数据访问层
  - `AirMonitorDbContext.cs`: EF Core数据库上下文
  - `Repositories/`: 仓储模式实现
- **Communication/**: 通信基础设施
  - `SerialPortHandler.cs`: 串口处理器
  - `ProtocolParser.cs`: 协议解析器
- **Configuration/**: 配置管理
  - `ProtocolConfigLoader.cs`: 协议配置加载器
  - `DatabaseConfigManager.cs`: 数据库配置管理器

#### 2.2.3 AirMonitor.Services (服务层)
**职责**: 实现业务逻辑和应用服务
**依赖**: AirMonitor.Core, AirMonitor.Infrastructure
**内容**:
- **Communication/**: 通信服务
  - `SerialCommunicationService.cs`: 串口通信服务实现
  - `ProtocolParserService.cs`: 协议解析服务实现
- **Data/**: 数据服务
  - `DataCollectionService.cs`: 数据采集服务
  - `DatabaseService.cs`: 数据库管理服务
- **License/**: 授权服务
  - `LicenseService.cs`: 授权验证服务
  - `LicenseValidator.cs`: 授权验证器
- **Alarm/**: 报警服务
  - `AlarmService.cs`: 报警处理服务

#### 2.2.4 AirMonitor.WPF (表示层)
**职责**: 用户界面和用户交互
**依赖**: AirMonitor.Services, AirMonitor.Core
**内容**:
- **Views/**: XAML视图文件
- **ViewModels/**: 视图模型
- **Controls/**: 自定义控件
- **Converters/**: 值转换器
- **Resources/**: 资源文件

## 3. 技术架构

### 3.1 技术栈
- **.NET 8.0**: 应用程序框架
- **WPF**: 用户界面框架
- **Syncfusion 29.2.9**: UI控件库
- **Entity Framework Core 9.0.5**: ORM框架
- **SQLite**: 数据库
- **CommunityToolkit.Mvvm 8.4.0**: MVVM框架
- **AutoMapper 14.0.0**: 对象映射
- **Serilog 4.2.0**: 日志框架

### 3.2 设计模式

#### 3.2.1 MVVM模式
- **Model**: 业务数据和逻辑
- **View**: 用户界面(XAML)
- **ViewModel**: 视图逻辑和数据绑定

#### 3.2.2 仓储模式
- 抽象数据访问逻辑
- 提供统一的数据操作接口
- 支持单元测试

#### 3.2.3 依赖注入模式
- 使用Microsoft.Extensions.DependencyInjection
- 管理对象生命周期
- 实现松耦合

#### 3.2.4 观察者模式
- 事件驱动的通信机制
- 实时数据更新通知
- UI响应式更新

### 3.3 数据流架构

```
Hardware → Serial Port → Protocol Parser → Data Service → Database
    ↓                                           ↓
UI ← ViewModel ← Service Layer ← Infrastructure ← Data Access
```

## 4. 安全架构

### 4.1 授权安全
- **License文件加密**: RSA+AES混合加密
- **硬件绑定**: CPU和主板序列号绑定
- **离线验证**: 本地授权验证，无需网络连接
- **防篡改**: 数字签名验证

### 4.2 数据安全
- **数据库加密**: SQLite数据库文件加密
- **通信安全**: 串口通信CRC校验
- **配置安全**: 敏感配置信息加密存储

## 5. 性能架构

### 5.1 性能要求
- **响应时间**: UI操作响应时间 < 200ms
- **数据采集**: 支持200-500ms高频采集
- **内存使用**: 运行时内存 < 500MB
- **启动时间**: 应用启动时间 < 5秒

### 5.2 性能优化策略
- **UI虚拟化**: 大数据量控件虚拟化
- **异步操作**: 数据库和I/O操作异步化
- **数据缓存**: 热点数据内存缓存
- **连接池**: 数据库连接池管理

## 6. 可扩展性架构

### 6.1 协议扩展
- **配置化协议**: JSON配置文件定义协议
- **插件化解析**: 支持新协议插件
- **动态加载**: 运行时加载协议定义

### 6.2 功能扩展
- **模块化设计**: 功能模块独立开发
- **接口抽象**: 基于接口的扩展点
- **事件机制**: 松耦合的功能集成

## 7. 部署架构

### 7.1 部署模式
- **单机部署**: 独立的桌面应用程序
- **便携式部署**: 支持绿色版部署
- **安装包部署**: MSI安装包部署

### 7.2 配置管理
- **应用配置**: appsettings.json
- **协议配置**: protocol_config.json
- **用户配置**: 用户个性化设置
- **License配置**: 授权文件管理

## 8. 监控和日志架构

### 8.1 日志策略
- **分级日志**: Debug/Info/Warning/Error/Fatal
- **结构化日志**: JSON格式日志
- **文件轮转**: 按大小和时间轮转
- **性能日志**: 关键操作性能监控

### 8.2 监控指标
- **系统指标**: CPU、内存、磁盘使用率
- **应用指标**: 响应时间、错误率、吞吐量
- **业务指标**: 数据采集成功率、设备在线率

## 9. 测试架构

### 9.1 测试策略
- **单元测试**: NUnit框架，覆盖率>80%
- **集成测试**: 模块间集成测试
- **UI测试**: 自动化UI测试
- **性能测试**: 负载和压力测试

### 9.2 测试工具
- **NUnit**: 单元测试框架
- **Moq**: 模拟对象框架
- **InMemory Database**: 内存数据库测试
- **Benchmark.NET**: 性能基准测试

## 10. 版本控制和CI/CD

### 10.1 版本控制
- **Git**: 源代码版本控制
- **语义化版本**: Major.Minor.Patch版本号
- **分支策略**: GitFlow工作流

### 10.2 持续集成
- **自动构建**: 代码提交自动构建
- **自动测试**: 单元测试和集成测试
- **代码质量**: 静态代码分析
- **自动部署**: 测试环境自动部署
