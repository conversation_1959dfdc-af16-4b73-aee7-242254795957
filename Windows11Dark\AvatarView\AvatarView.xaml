<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:local="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF"
                    xmlns:resources="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:theme="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
                    xmlns:vsm="clr-namespace:Syncfusion.Windows;assembly=Syncfusion.Shared.WPF">

                <ResourceDictionary.MergedDictionaries>
                    <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
                    <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
                </ResourceDictionary.MergedDictionaries>

                <Style x:Key="SyncfusionAvatarViewStyle" TargetType="shared:SfAvatarView">
                    <Setter Property="Background" Value="{StaticResource Series8}"/>
                    <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
                    <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1}"/>
                    <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                    <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate  TargetType="{x:Type shared:SfAvatarView}">
                                <Border x:Name="rootBorder" 
                                                        CornerRadius="{TemplateBinding CornerRadius}"  
                                                        BorderThickness="{TemplateBinding BorderThickness}" 
                                                        Background="{TemplateBinding Background}">
                                    <ContentPresenter x:Name="contentPresenter"/>
                                </Border>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
                <Style BasedOn="{StaticResource SyncfusionAvatarViewStyle}" TargetType="{x:Type shared:SfAvatarView}"/>
</ResourceDictionary>
