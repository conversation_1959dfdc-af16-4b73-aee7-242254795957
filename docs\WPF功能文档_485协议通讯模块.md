# WPF功能文档：485协议通讯模块

## 1. 功能概述
- **功能名称**：485协议通讯模块
- **功能描述**：实现RS485串口通讯功能，支持主从轮询模式的设备通信，提供数据帧的发送和接收功能，与通用协议解析器配合完成完整的协议通信
- **目标用户**：空调维护工程师和技术人员
- **优先级**：高
- **预估工作量**：5-7个工作日
- **依赖的WPF技术**：数据绑定、命令绑定、后台线程处理、异步操作

## 2. WPF功能需求详解

### 2.1 核心功能点
- **串口配置管理**：提供RS485串口参数配置，包括端口选择、波特率、数据位、停止位、校验位设置
- **数据帧收发**：实现原始数据帧的发送和接收功能，支持异步通信
- **CRC16校验**：实现Modbus CRC16校验算法，确保数据传输的可靠性
- **主从轮询通信**：实现主设备对从设备的轮询机制，支持设备点播和状态回复
- **通信监控**：提供实时通信数据监控，显示发送和接收的原始数据帧
- **协议解析集成**：与通用协议解析器配合，将原始数据帧解析为有意义的设备参数

### 2.2 用户操作流程
1. 用户打开串口配置界面，选择COM端口和通信参数
2. 点击"连接"按钮建立串口连接
3. 系统自动开始主从轮询通信，点播各从设备
4. 实时显示通信状态和接收到的设备数据
5. 用户可以手动发送特定命令或参数读写请求
6. 系统解析接收到的数据并更新设备状态显示

### 2.3 数据流和绑定规范
- **输入数据**：串口配置参数、手动发送的命令数据、设备地址配置
- **输出数据**：解析后的设备参数、通信状态、错误信息、原始数据帧
- **数据绑定**：串口状态绑定到UI指示器、设备数据绑定到监控界面、通信日志绑定到日志显示控件
- **命令绑定**：连接/断开串口命令、发送数据命令、清除日志命令、导出通信数据命令

## 3. WPF界面设计规范

### 3.1 XAML布局设计
- **主容器**：Grid布局，分为配置区域和监控区域
- **配置区域**：使用StackPanel垂直排列串口参数控件
- **监控区域**：使用DockPanel，上方为状态指示，下方为数据显示
- **响应式布局**：支持窗口大小调整，配置区域固定宽度，监控区域自适应

### 3.2 视觉设计要求
- **主题风格**：Syncfusion Windows11Dark/Light主题
- **色彩方案**：
  - 连接状态：绿色表示已连接，红色表示断开，黄色表示连接中
  - 数据方向：蓝色表示发送数据，橙色表示接收数据
  - 错误状态：红色背景高亮显示通信错误
- **字体规范**：微软雅黑字体，标题16px，正文14px，数据显示12px等宽字体
- **间距规范**：控件间距8px，分组间距16px，边距12px

### 3.3 WPF控件设计
- **串口配置**：ComboBox选择端口，NumericUpDown设置波特率等参数
- **连接控制**：ToggleButton实现连接/断开功能，带状态指示
- **数据监控**：DataGrid显示通信日志，支持筛选和导出
- **参数显示**：自定义UserControl显示解析后的参数值

## 4. WPF交互设计

### 4.1 用户操作流程
1. **串口配置**：用户在配置面板选择COM端口、波特率等参数
2. **建立连接**：点击连接按钮，系统尝试打开串口并显示连接状态
3. **自动轮询**：连接成功后自动开始设备轮询，实时更新设备状态
4. **手动操作**：用户可以手动发送命令或读写特定参数
5. **数据监控**：实时查看通信数据和解析结果

### 4.2 WPF交互反馈
- **成功状态**：连接成功时显示绿色指示灯，播放成功提示音
- **错误处理**：通信错误时显示红色警告，记录错误日志
- **加载状态**：数据传输时显示进度指示器
- **确认操作**：重要操作（如断开连接）显示确认对话框

### 4.3 WPF快捷操作
- **键盘快捷键**：F5刷新端口列表，Ctrl+D连接/断开，Ctrl+S保存配置
- **右键菜单**：数据网格支持复制、导出、清除等操作
- **拖拽操作**：支持拖拽配置文件到界面进行快速配置

## 5. MVVM技术实现方案

### 5.1 WPF架构设计
- **View层**：ConnectionView.xaml（串口配置界面）、ProtocolMonitorView.xaml（协议监控界面）
- **ViewModel层**：ConnectionViewModel（连接管理）、ProtocolMonitorViewModel（数据监控）
- **Model层**：ProtocolFrame（协议帧模型）、SerialPortConfig（串口配置模型）
- **Service层**：SerialCommunicationService（串口通信）

### 5.2 数据绑定策略
- **属性绑定**：串口状态、设备列表、通信日志等使用ObservableCollection
- **集合绑定**：通信数据使用ObservableCollection<ProtocolFrame>实现实时更新
- **命令绑定**：连接命令、发送命令、清除命令等使用RelayCommand实现
- **转换器**：状态到颜色转换器、字节数组到十六进制字符串转换器

### 5.3 WPF数据模型
```csharp
// 协议帧模型
public class ProtocolFrame
{
    public byte Header { get; set; }
    public byte SourceAddress { get; set; }
    public byte TargetAddress { get; set; }
    public byte CommandCode { get; set; }
    public byte MessageLength { get; set; }
    public byte[] Data { get; set; }
    public ushort CRC16 { get; set; }
    public DateTime Timestamp { get; set; }
    public bool IsValid { get; set; }
    public FrameDirection Direction { get; set; }
}

// 串口配置模型
public class SerialPortConfig
{
    public string PortName { get; set; }
    public int BaudRate { get; set; }
    public int DataBits { get; set; }
    public Parity Parity { get; set; }
    public StopBits StopBits { get; set; }
}

// 原始数据帧模型（用于与协议解析器交互）
public class RawDataFrame
{
    public byte[] Data { get; set; }
    public DateTime Timestamp { get; set; }
    public FrameDirection Direction { get; set; }
    public bool IsValid { get; set; }
}
```

### 5.4 服务接口设计
```csharp
// 串口通信服务接口
public interface ISerialCommunicationService
{
    Task<bool> ConnectAsync(SerialPortConfig config);
    Task DisconnectAsync();
    Task<bool> SendFrameAsync(ProtocolFrame frame);
    event EventHandler<ProtocolFrame> FrameReceived;
    bool IsConnected { get; }
}

// CRC16校验服务接口
public interface ICrc16Service
{
    ushort CalculateCRC16(byte[] data);
    bool ValidateCRC16(byte[] frameData);
}
```

## 6. WPF实现步骤

### 6.1 开发阶段划分
- **阶段一**：Model和Service层实现（CRC16计算、串口通信基础功能）
- **阶段二**：ViewModel层开发（连接管理、数据绑定、命令处理）
- **阶段三**：XAML界面设计（串口配置界面、协议监控界面）
- **阶段四**：数据绑定和交互实现（实时数据更新、用户操作响应）
- **阶段五**：样式美化和动画效果（Syncfusion主题应用、状态指示动画）
- **阶段六**：测试与性能优化（通信稳定性测试、内存泄漏检查）

### 6.2 详细任务清单
- [ ] 创建ProtocolFrame、SerialPortConfig、RawDataFrame等数据模型类
- [ ] 实现ISerialCommunicationService串口通信服务
- [ ] 实现ICrc16Service CRC16校验服务
- [ ] 集成通用协议解析器接口
- [ ] 开发ConnectionViewModel连接管理视图模型
- [ ] 开发ProtocolMonitorViewModel监控视图模型
- [ ] 设计ConnectionView串口配置XAML界面
- [ ] 设计ProtocolMonitorView协议监控XAML界面
- [ ] 实现数据绑定和命令绑定
- [ ] 添加状态指示和错误处理
- [ ] 应用Syncfusion样式和主题
- [ ] 实现通信日志的导出功能
- [ ] 编写单元测试和集成测试
- [ ] 性能优化和内存管理

## 7. WPF测试方案

### 7.1 ViewModel单元测试
- **连接测试**：验证串口连接/断开功能的正确性
- **命令测试**：测试发送命令、清除日志等命令的执行
- **数据绑定测试**：验证通信状态、设备数据的属性通知

### 7.2 UI自动化测试
- **界面元素测试**：验证串口配置控件的存在和可见性
- **交互测试**：模拟用户点击连接按钮、选择端口等操作
- **数据显示测试**：验证通信数据在界面上的正确显示

### 7.3 串口通信测试
- **数据收发测试**：验证原始数据帧的发送和接收功能
- **CRC16测试**：验证CRC16校验算法的准确性
- **错误处理测试**：测试通信中断、数据丢失等异常情况的处理

## 8. WPF验收标准
- **功能完整性**：串口连接、数据收发、通信监控等功能正常工作
- **界面美观性**：符合Syncfusion主题设计，使用微软雅黑字体
- **性能要求**：支持高频率数据传输（≥10Hz），响应时间<100ms
- **兼容性**：支持Windows 10/11，兼容各种RS485转USB适配器
- **可维护性**：代码结构清晰，遵循MVVM模式，注释完整

## 9. WPF风险评估

### 9.1 技术风险
- **串口通信稳定性**：不同硬件适配器的兼容性问题
- **数据传输可靠性**：高频数据传输时的数据完整性和准确性
- **实时性能**：高频数据传输时的UI响应性能
- **错误恢复**：通信中断后的自动重连机制

### 9.2 时间风险
- **协议调试时间**：与实际设备联调的时间投入
- **性能优化**：大量数据处理的性能调优时间
- **兼容性测试**：不同硬件环境的测试时间

## 10. WPF后续优化方向
- **性能优化**：实现数据缓冲和批量处理机制
- **用户体验**：增加通信状态的可视化指示
- **功能扩展**：支持多串口同时通信
- **智能诊断**：自动检测通信问题并提供解决建议
- **配置管理**：支持通信配置的保存和导入
