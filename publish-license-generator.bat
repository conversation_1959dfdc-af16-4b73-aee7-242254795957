@echo off
echo Publishing AirMonitor License Generator...
echo.

cd /d "%~dp0"

if not exist "src\AirMonitor.LicenseGenerator\AirMonitor.LicenseGenerator.csproj" (
    echo Error: Project file not found!
    echo Please make sure you are running this script from the repository root.
    pause
    exit /b 1
)

set OUTPUT_DIR=publish\LicenseGenerator
set PROJECT_PATH=src\AirMonitor.LicenseGenerator

echo Cleaning previous build...
if exist "%OUTPUT_DIR%" rmdir /s /q "%OUTPUT_DIR%"

echo.
echo Publishing project...
dotnet publish "%PROJECT_PATH%" ^
    --configuration Release ^
    --output "%OUTPUT_DIR%" ^
    --self-contained true ^
    --runtime win-x64 ^
    --verbosity minimal ^
    /p:PublishSingleFile=true ^
    /p:IncludeNativeLibrariesForSelfExtract=true ^
    /p:PublishTrimmed=false

if %ERRORLEVEL% neq 0 (
    echo Publish failed!
    pause
    exit /b 1
)

echo.
echo Creating additional directories...
mkdir "%OUTPUT_DIR%\keys" 2>nul
mkdir "%OUTPUT_DIR%\templates" 2>nul
mkdir "%OUTPUT_DIR%\output" 2>nul
mkdir "%OUTPUT_DIR%\backup" 2>nul
mkdir "%OUTPUT_DIR%\logs" 2>nul

echo.
echo Copying additional files...
copy "README.md" "%OUTPUT_DIR%\" >nul 2>&1
copy "src\AirMonitor.LicenseGenerator\README.md" "%OUTPUT_DIR%\LicenseGenerator-README.md" >nul 2>&1

echo.
echo Creating sample configuration...
echo { > "%OUTPUT_DIR%\sample-config.json"
echo   "License": { >> "%OUTPUT_DIR%\sample-config.json"
echo     "DefaultValidityDays": 365, >> "%OUTPUT_DIR%\sample-config.json"
echo     "KeysDirectory": "keys", >> "%OUTPUT_DIR%\sample-config.json"
echo     "TemplatesDirectory": "templates", >> "%OUTPUT_DIR%\sample-config.json"
echo     "OutputDirectory": "output" >> "%OUTPUT_DIR%\sample-config.json"
echo   } >> "%OUTPUT_DIR%\sample-config.json"
echo } >> "%OUTPUT_DIR%\sample-config.json"

echo.
echo ========================================
echo Publish completed successfully!
echo.
echo Output directory: %OUTPUT_DIR%
echo Executable: %OUTPUT_DIR%\AirMonitor.LicenseGenerator.exe
echo.
echo You can now distribute the contents of the '%OUTPUT_DIR%' folder.
echo ========================================
echo.

pause
