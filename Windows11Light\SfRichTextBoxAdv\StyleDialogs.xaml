<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:richtextboxadv="clr-namespace:Syncfusion.Windows.Controls.RichTextBoxAdv;assembly=Syncfusion.SfRichTextBoxAdv.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:tools_controls_shared="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.shared.WPF"
					xmlns:Syncfusion="http://schemas.syncfusion.com/wpf"
                    
                    xmlns:resources="clr-namespace:Syncfusion.Windows.Controls.RichTextBoxAdv;assembly=Syncfusion.SfRichTextBoxAdv.WPF">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/SfRichTextBoxAdv/SfRichTextBoxCommon.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/PrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatPrimaryButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="richtextboxadv:StylesDialog">
        <Setter Property="Width" Value="306"/>
        <Setter Property="Height" Value="240"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="richtextboxadv:StylesDialog">
                    <Grid x:Name="PART_Root" Background="{StaticResource ContentBackground}">
                        <StackPanel>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="12"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="4"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="12"></RowDefinition>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="12"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="24"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="12"/>
                                </Grid.ColumnDefinitions>
                                <Label Grid.Row="1" 
                                       Grid.Column="1" 
                                       FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                       FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                       Padding="0 5"
                                       Height="25" 
                                       HorizontalAlignment="Left"
                                       Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=StylesDialogGeneralHeader}"
                                       TabIndex="0"/>
                                <ListBox Grid.Column="1"  
                                         Grid.Row="3" 
                                         BorderThickness="1" 
                                         FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                         FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                         x:Name="PART_StlyeList" 
                                         ScrollViewer.VerticalScrollBarVisibility="Auto"  
                                         HorizontalAlignment="Left"
                                         Width="189" 
                                         Height="169">
                                </ListBox>
                                <StackPanel Orientation="Vertical"
                                            Grid.Row="3" 
                                            HorizontalAlignment="Center"
                                            Grid.Column="3" >
                                    <Button x:Name="PART_NewButton" 
                                            FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                            FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                            Height="24" 
                                            Width="69"
                                            HorizontalAlignment="Center"
                                            Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=New}"/>
                                    <Button x:Name="PART_ModifyButton" 
                                            FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                            FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                            Height="24" 
                                            Width="69"
                                            Margin="0 12"
                                            HorizontalAlignment="Center"
                                            Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=Modify}" />
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="richtextboxadv:StyleDialog">
        <Setter Property="Width" Value="391"/>
        <Setter Property="Height" Value="311"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="richtextboxadv:StyleDialog">
                    <Grid x:Name="PART_Root" Background="{StaticResource ContentBackground}">
                        <Grid.Resources>
                            <richtextboxadv:StringFontFamilyConverter x:Key="StringFontFamilyConverter"/>
                            <richtextboxadv:LeftAlignmentToggleConverter x:Key="LeftAlignmentToggleConverter"></richtextboxadv:LeftAlignmentToggleConverter>
                            <richtextboxadv:RightAlignmentToggleConverter x:Key="RightAlignmentToggleConverter"></richtextboxadv:RightAlignmentToggleConverter>
                            <richtextboxadv:JustifyAlignmentToggleConverter x:Key="JustifyAlignmentToggleConverter"></richtextboxadv:JustifyAlignmentToggleConverter>
                            <richtextboxadv:CenterAlignmentToggleConverter x:Key="CenterAlignmentToggleConverter"></richtextboxadv:CenterAlignmentToggleConverter>
                            <richtextboxadv:BrushOpacityConverter x:Key="BrushOpacityConverter"/>
                        </Grid.Resources>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="11"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="12"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="12"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="12"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="24"/>
                                <RowDefinition Height="38"/>
                            </Grid.RowDefinitions>
                            <Grid Grid.Row="1" Grid.Column="1">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="12"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="4"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="12"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="4"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"></ColumnDefinition>
                                    <ColumnDefinition Width="13"></ColumnDefinition>
                                    <ColumnDefinition Width="*"></ColumnDefinition>
                                </Grid.ColumnDefinitions>
                                <Label  Grid.Row="0"
                                        Padding="0 2 0 2"
                                        Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=StyleDialogGeneralHeader}"
                                        VerticalAlignment="Center" Foreground="{StaticResource ContentForeground}"
                                        FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"/>
                                <Label  Grid.Row="2"
                                        Grid.Column="0"
                                        Padding="0 2 0 2"
                                        Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=StyleDialogName}" 
                                        VerticalAlignment="Center"
                                        FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"/>
                                <TextBox  x:Name="PART_StyleName" 
                                         Focusable="True"
                                         Grid.Row="4" 
                                         FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                         FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                         Grid.Column="0" 
                                         Height="24"
                                         Width="169"
                                         Padding="1 1 1 1"
                                         HorizontalAlignment="Left">
                                </TextBox>
                                <Label  Grid.Row="6"
                                        Grid.Column="0"
                                        Padding="0 2 0 2" 
                                        Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=StyleDialogBasedOn}"
                                        VerticalAlignment="Center" 
                                        FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Light.ThemeFontFamily}">
                                </Label>
                                <StackPanel Grid.Row="8" 
                                            Grid.Column="0">
                                    <ComboBox Margin="0" 
                                              HorizontalAlignment="Left"
                                              SelectedIndex="0" 
                                              Padding="4 2 2 2"
                                              x:Name="PART_BasedOnStyleComboBox"
                                              Width="169"
                                              Height="24"
                                              FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                              FontFamily="{StaticResource Windows11Light.ThemeFontFamily}" />
                                </StackPanel>
                                <Label  Grid.Row="2"
                                        Grid.Column="2"
                                        Padding="0 2 0 2"
                                        Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=StyleDialogStyleType}" 
                                        VerticalAlignment="Center" 
                                        FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"></Label>
                                <StackPanel Grid.Row="4"
                                            Grid.Column="2">
                                    <ComboBox  HorizontalAlignment="Left"
                                               x:Name="PART_StyleTypeComboBox" 
                                               SelectedIndex="1"
                                               Padding="4 2 2 2"
                                               Width="169" 
                                               Height="24"  
                                               ItemsSource="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=StyleTypeCollection}" 
                                               FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                               FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"/>
                                </StackPanel>
                                <Label  Grid.Row="6"
                                        Grid.Column="2"
                                        Padding="0 2 0 2"
                                        Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=StyleDialogFollowingParagraph}"
                                        VerticalAlignment="Center" 
                                        FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"></Label>
                                <StackPanel  Grid.Row="8"
                                             Grid.Column="2">
                                    <ComboBox HorizontalAlignment="Left"
                                                x:Name="PART_NextStyleComboBox"
                                                SelectedIndex="0"
                                                Padding="4 2 2 2"
                                                Height="24"
                                                Width="169"
                                                FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                                FontFamily="{StaticResource Windows11Light.ThemeFontFamily}" />
                                </StackPanel>
                            </Grid>
                            <Border  Grid.Row="2"
                                     Grid.Column="1" 
                                     Height="1"
                                     Margin="5 12 5 -7"
                                     Padding="4 2 2 2" 
                                     BorderThickness="0 0 0 1" 
                                     Opacity="0.5" 
                                     VerticalAlignment="Center"/>
                            <Grid Grid.Row="3" Grid.Column="1" >
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="5"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="13"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="8"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                </Grid.ColumnDefinitions>
                                <Label Grid.Row="0"
                                       Grid.Column="0" 
                                       Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=StyleDialogFormattingHeader}" 
                                       Padding="0 2 0 2"
                                       VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}" 
                                       FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                       FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"/>
                                <StackPanel  Grid.Row="2" 
                                             Grid.Column="0">
                                    <ComboBox x:Name="PART_FontFamilyComboBox" 
                                              Padding="4 2 2 2" 
                                              HorizontalAlignment="Left" 
                                              Width="169"
                                              Height="24"
                                              IsEditable="True"
                                              ToolTipService.ShowOnDisabled="True"
                                              SelectedIndex="0" 
                                              FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                              FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"></ComboBox>
                                </StackPanel>
                                <StackPanel   Grid.Row="2"
                                              Grid.Column="2">
                                    <ComboBox x:Name="PART_FontSizeComboBox"
                                              Width="69"
                                              Height="24"
                                              Padding="4 2 2 2"  
                                              FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                              FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                              IsEditable="True" ToolTipService.ShowOnDisabled="True" SelectedIndex="0" ></ComboBox>
                                </StackPanel>
                                <Border Grid.Row="1" 
                                        Grid.Column="2" 
                                        Width="1" 
                                        Margin="5 5 5 5"
                                        Padding="4 2 2 2"
                                        BorderThickness="0 0 1 0" 
                                        Opacity="0.5"
                                        HorizontalAlignment="Center"
                                        Background="{TemplateBinding BorderBrush}"/>
                                <StackPanel  Grid.Row="2"
                                             Grid.Column="4">
                                    <Syncfusion:ColorPickerPalette x:Name="PART_ColorPicker"
                                                                   Padding="4 2 2 2"
                                                                   HorizontalContentAlignment="Left" 
                                                                   BorderThickness="1"
                                                                   BorderBrush="{StaticResource BorderAlt}"
                                                                   Height="24"
                                                                   Width="92"
                                                                   RecentlyUsedPanelVisibility="Collapsed"
                                                                   MoreColorOptionVisibility="Collapsed"
                                                                   BlackWhiteVisibility="Both"  />
                                </StackPanel>
                            </Grid>
                            <Grid Grid.Row="5" Grid.ColumnSpan="2" x:Name="PART_ParaFormats" Grid.Column="1" >
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                </Grid.ColumnDefinitions>
                                <StackPanel  Grid.Column="0">
                                    <ToggleButton x:Name="PART_StyleBold" 
                                                  Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"  
                                                  Padding="4 2 2 2" 
                                                  Margin="0 0 5 0"
                                                  Width="24" 
                                                  Height="24" 
                                                  Background="Transparent">
                                        <ToggleButton.Content>
                                            <Path x:Name="Bold1" 
                                                  Fill="{StaticResource IconColor}"
                                                  HorizontalAlignment="Right" 
                                                  Height="11"
                                                  Stretch="Fill" 
                                                  VerticalAlignment="Top" 
                                                  Width="7">
                                                <Path.Data>
                                                    <PathGeometry>M1.22656 13.9062C1.0599 13.9062 0.901042 13.875 0.75 13.8125C0.604167 13.75 0.473958 13.6641 0.359375 13.5547C0.25 13.4453 0.161458 13.3177 0.09375 13.1719C0.03125 13.026 0 12.8698 0 12.7031V1.29688C0 1.125 0.0338542 0.960938 0.101562 0.804688C0.174479 0.648438 0.270833 0.510417 0.390625 0.390625C0.510417 0.270833 0.648438 0.177083 0.804688 0.109375C0.960938 0.0364583 1.125 0 1.29688 0H4.79688C5.34896 0 5.86719 0.106771 6.35156 0.320312C6.83594 0.528646 7.25781 0.815104 7.61719 1.17969C7.98177 1.53906 8.26823 1.96094 8.47656 2.44531C8.6901 2.92969 8.79688 3.44792 8.79688 4C8.79688 4.44271 8.72656 4.86458 8.58594 5.26562C8.45052 5.66146 8.25521 6.03906 8 6.39844C8.52083 6.80469 8.91667 7.29167 9.1875 7.85938C9.46354 8.42708 9.60156 9.04167 9.60156 9.70312C9.60156 10.1146 9.55208 10.5182 9.45312 10.9141C9.35417 11.3047 9.1875 11.6719 8.95312 12.0156C8.75521 12.3073 8.52344 12.5703 8.25781 12.8047C7.9974 13.0339 7.71354 13.2318 7.40625 13.3984C7.09896 13.5599 6.77344 13.6849 6.42969 13.7734C6.09115 13.8568 5.7474 13.8984 5.39844 13.8984C4.70052 13.8984 4.00521 13.901 3.3125 13.9062C2.61979 13.9062 1.92448 13.9062 1.22656 13.9062ZM4.70312 5.5C4.91146 5.5 5.10677 5.46094 5.28906 5.38281C5.47135 5.30469 5.63021 5.19792 5.76562 5.0625C5.90104 4.92708 6.00781 4.76823 6.08594 4.58594C6.16406 4.40365 6.20312 4.20833 6.20312 4C6.20312 3.79167 6.16406 3.59635 6.08594 3.41406C6.00781 3.23177 5.90104 3.07292 5.76562 2.9375C5.63021 2.80208 5.47135 2.69531 5.28906 2.61719C5.10677 2.53906 4.91146 2.5 4.70312 2.5H2.5V5.5H4.70312ZM5.29688 11.5C5.54167 11.5 5.76823 11.4557 5.97656 11.3672C6.1849 11.2734 6.36458 11.1484 6.51562 10.9922C6.66667 10.8359 6.78385 10.6536 6.86719 10.4453C6.95573 10.2318 7 10.0026 7 9.75781C7 9.52865 6.95312 9.3099 6.85938 9.10156C6.77083 8.88802 6.64844 8.70052 6.49219 8.53906C6.34115 8.3724 6.16146 8.24219 5.95312 8.14844C5.75 8.04948 5.53125 8 5.29688 8H2.5V11.5H5.29688Z</PathGeometry>
                                                </Path.Data>
                                            </Path>
                                        </ToggleButton.Content>
                                    </ToggleButton>
                                </StackPanel>
                                <StackPanel  Grid.Column="1">
                                    <ToggleButton x:Name="PART_StyleItalic" 
                                                  Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                  Padding="4 2 2 2" 
                                                  Margin="0 0 5 0"
                                                  Height="24" 
                                                  Width="24" 
                                                  Background="Transparent">
                                        <ToggleButton.Content>
                                            <Path x:Name="Italic1" 
                                                  Fill="{StaticResource IconColor}"
                                                  HorizontalAlignment="Right" 
                                                  Height="10" 
                                                  Stretch="Fill" 
                                                  VerticalAlignment="Top" 
                                                  Width="6">
                                                <Path.Data>
                                                    <PathGeometry>M12.5 0C12.6354 0 12.7526 0.0494792 12.8516 0.148438C12.9505 0.247396 13 0.364583 13 0.5C13 0.635417 12.9505 0.752604 12.8516 0.851562C12.7526 0.950521 12.6354 1 12.5 1H9.34375L4.72656 13H8C8.13542 13 8.2526 13.0495 8.35156 13.1484C8.45052 13.2474 8.5 13.3646 8.5 13.5C8.5 13.6354 8.45052 13.7526 8.35156 13.8516C8.2526 13.9505 8.13542 14 8 14H0.5C0.364583 14 0.247396 13.9505 0.148438 13.8516C0.0494792 13.7526 0 13.6354 0 13.5C0 13.3646 0.0494792 13.2474 0.148438 13.1484C0.247396 13.0495 0.364583 13 0.5 13H3.65625L8.27344 1H5C4.86458 1 4.7474 0.950521 4.64844 0.851562C4.54948 0.752604 4.5 0.635417 4.5 0.5C4.5 0.364583 4.54948 0.247396 4.64844 0.148438C4.7474 0.0494792 4.86458 0 5 0H12.5Z</PathGeometry>
                                                </Path.Data>
                                            </Path>
                                        </ToggleButton.Content>
                                    </ToggleButton>
                                </StackPanel>
                                <StackPanel  Grid.Column="2">
                                    <ToggleButton x:Name="PART_StyleUnderline" 
                                                  Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                  Padding="4 2 2 2" 
                                                   Margin="0 0 5 0"
                                                  Width="24" 
                                                  Height="24" 
                                                  Background="Transparent">
                                        <ToggleButton.Content>
                                            <Path x:Name="Underline1" 
                                                  Fill="{StaticResource IconColor}"
                                                  Height="12"
                                                  Stretch="Fill"
                                                  VerticalAlignment="Top"
                                                  HorizontalAlignment="Left"
                                                  Width="9">
                                                <Path.Data>
                                                    <PathGeometry>M0 6.95312V0.5C0 0.364583 0.0494792 0.247396 0.148438 0.148438C0.247396 0.0494792 0.364583 0 0.5 0C0.635417 0 0.752604 0.0494792 0.851562 0.148438C0.950521 0.247396 1 0.364583 1 0.5V7.03125C1 7.57292 1.10677 8.08594 1.32031 8.57031C1.53385 9.04948 1.82292 9.46875 2.1875 9.82812C2.55208 10.1875 2.97656 10.474 3.46094 10.6875C3.94531 10.8958 4.45833 11 5 11C5.55729 11 6.07812 10.8932 6.5625 10.6797C7.05208 10.4661 7.47656 10.1771 7.83594 9.8125C8.19531 9.44271 8.47917 9.01302 8.6875 8.52344C8.89583 8.03385 9 7.51042 9 6.95312V0.5C9 0.364583 9.04948 0.247396 9.14844 0.148438C9.2474 0.0494792 9.36458 0 9.5 0C9.63542 0 9.7526 0.0494792 9.85156 0.148438C9.95052 0.247396 10 0.364583 10 0.5V6.95312C10 7.41667 9.9401 7.86198 9.82031 8.28906C9.70573 8.71615 9.53906 9.11719 9.32031 9.49219C9.10677 9.86719 8.84635 10.2083 8.53906 10.5156C8.23698 10.8229 7.90104 11.0859 7.53125 11.3047C7.16146 11.5234 6.76302 11.6953 6.33594 11.8203C5.90885 11.9401 5.46354 12 5 12C4.53646 12 4.09115 11.9401 3.66406 11.8203C3.23698 11.6953 2.83854 11.5234 2.46875 11.3047C2.09896 11.0859 1.76042 10.8229 1.45312 10.5156C1.15104 10.2083 0.890625 9.86719 0.671875 9.49219C0.458333 9.11719 0.291667 8.71615 0.171875 8.28906C0.0572917 7.85677 0 7.41146 0 6.95312ZM0.5 14C0.364583 14 0.247396 13.9505 0.148438 13.8516C0.0494792 13.7526 0 13.6354 0 13.5C0 13.3646 0.0494792 13.2474 0.148438 13.1484C0.247396 13.0495 0.364583 13 0.5 13H9.5C9.63542 13 9.7526 13.0495 9.85156 13.1484C9.95052 13.2474 10 13.3646 10 13.5C10 13.6354 9.95052 13.7526 9.85156 13.8516C9.7526 13.9505 9.63542 14 9.5 14H0.5Z</PathGeometry>
                                                </Path.Data>
                                            </Path>
                                        </ToggleButton.Content>
                                    </ToggleButton>
                                </StackPanel>
                                <Border  Grid.Column="3"
                                         Width="1" 
                                         Margin="0 0 8 0" 
                                         Padding="4 2 2 2" 
                                         BorderThickness="0 0 1 0" 
                                         Opacity="0.5" 
                                         HorizontalAlignment="Center"
                                         Background="{StaticResource BorderAlt}"/>
                                <StackPanel Grid.Column="4">
                                    <ToggleButton x:Name="PART_AlignLeft" 
                                                  IsChecked="True" 
                                                  ToolTipService.ShowOnDisabled="True" 
                                                  Padding="2 2 2 2" 
                                                  Margin="0 0 5 0"
                                                  Width="24" 
                                                  Height="24" 
                                                  Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                  Background="Transparent">
                                        <ToggleButton.Content>
                                            <Grid>
                                                <Path Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M0.5 1C0.364583 1 0.247396 0.950521 0.148438 0.851562C0.0494792 0.752604 0 0.635417 0 0.5C0 0.364583 0.0494792 0.247396 0.148438 0.148438C0.247396 0.0494792 0.364583 0 0.5 0H11.5C11.6354 0 11.7526 0.0494792 11.8516 0.148438C11.9505 0.247396 12 0.364583 12 0.5C12 0.635417 11.9505 0.752604 11.8516 0.851562C11.7526 0.950521 11.6354 1 11.5 1H0.5ZM0.5 6C0.364583 6 0.247396 5.95052 0.148438 5.85156C0.0494792 5.7526 0 5.63542 0 5.5C0 5.36458 0.0494792 5.2474 0.148438 5.14844C0.247396 5.04948 0.364583 5 0.5 5H15.5C15.6354 5 15.7526 5.04948 15.8516 5.14844C15.9505 5.2474 16 5.36458 16 5.5C16 5.63542 15.9505 5.7526 15.8516 5.85156C15.7526 5.95052 15.6354 6 15.5 6H0.5ZM0.5 11C0.364583 11 0.247396 10.9505 0.148438 10.8516C0.0494792 10.7526 0 10.6354 0 10.5C0 10.3646 0.0494792 10.2474 0.148438 10.1484C0.247396 10.0495 0.364583 10 0.5 10H9.5C9.63542 10 9.7526 10.0495 9.85156 10.1484C9.95052 10.2474 10 10.3646 10 10.5C10 10.6354 9.95052 10.7526 9.85156 10.8516C9.7526 10.9505 9.63542 11 9.5 11H0.5Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </ToggleButton.Content>
                                    </ToggleButton>
                                </StackPanel>
                                <StackPanel Grid.Column="5">
                                    <ToggleButton x:Name="PART_AlignRight" 
                                                  Padding="2 2 2 2" 
                                                  Margin="0 0 5 0"
                                                  Width="24" 
                                                  Height="24" 
                                                  ToolTipService.ShowOnDisabled="True"
                                                  Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                  Background="Transparent">
                                        <ToggleButton.Content>
                                            <Grid>
                                                <Path Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M2.5 1C2.36458 1 2.2474 0.950521 2.14844 0.851562C2.04948 0.752604 2 0.635417 2 0.5C2 0.364583 2.04948 0.247396 2.14844 0.148438C2.2474 0.0494792 2.36458 0 2.5 0H13.5C13.6354 0 13.7526 0.0494792 13.8516 0.148438C13.9505 0.247396 14 0.364583 14 0.5C14 0.635417 13.9505 0.752604 13.8516 0.851562C13.7526 0.950521 13.6354 1 13.5 1H2.5ZM0.5 6C0.364583 6 0.247396 5.95052 0.148438 5.85156C0.0494792 5.7526 0 5.63542 0 5.5C0 5.36458 0.0494792 5.2474 0.148438 5.14844C0.247396 5.04948 0.364583 5 0.5 5H15.5C15.6354 5 15.7526 5.04948 15.8516 5.14844C15.9505 5.2474 16 5.36458 16 5.5C16 5.63542 15.9505 5.7526 15.8516 5.85156C15.7526 5.95052 15.6354 6 15.5 6H0.5ZM4.5 11C4.36458 11 4.2474 10.9505 4.14844 10.8516C4.04948 10.7526 4 10.6354 4 10.5C4 10.3646 4.04948 10.2474 4.14844 10.1484C4.2474 10.0495 4.36458 10 4.5 10H11.5C11.6354 10 11.7526 10.0495 11.8516 10.1484C11.9505 10.2474 12 10.3646 12 10.5C12 10.6354 11.9505 10.7526 11.8516 10.8516C11.7526 10.9505 11.6354 11 11.5 11H4.5Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </ToggleButton.Content>
                                    </ToggleButton>
                                </StackPanel>
                                <StackPanel Grid.Column="6">
                                    <ToggleButton x:Name="PART_AlignCenter" 
                                                  ToolTipService.ShowOnDisabled="True" 
                                                  Padding="2 2 2 2" 
                                                   Margin="0 0 5 0"
                                                  Width="24" 
                                                  Height="24" 
                                                  Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                  Background="Transparent">
                                        <ToggleButton.Content>
                                            <Grid>
                                                <Path Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M4.5 1C4.36458 1 4.2474 0.950521 4.14844 0.851562C4.04948 0.752604 4 0.635417 4 0.5C4 0.364583 4.04948 0.247396 4.14844 0.148438C4.2474 0.0494792 4.36458 0 4.5 0H15.5C15.6354 0 15.7526 0.0494792 15.8516 0.148438C15.9505 0.247396 16 0.364583 16 0.5C16 0.635417 15.9505 0.752604 15.8516 0.851562C15.7526 0.950521 15.6354 1 15.5 1H4.5ZM0.5 6C0.364583 6 0.247396 5.95052 0.148438 5.85156C0.0494792 5.7526 0 5.63542 0 5.5C0 5.36458 0.0494792 5.2474 0.148438 5.14844C0.247396 5.04948 0.364583 5 0.5 5H15.5C15.6354 5 15.7526 5.04948 15.8516 5.14844C15.9505 5.2474 16 5.36458 16 5.5C16 5.63542 15.9505 5.7526 15.8516 5.85156C15.7526 5.95052 15.6354 6 15.5 6H0.5ZM7.5 11C7.36458 11 7.2474 10.9505 7.14844 10.8516C7.04948 10.7526 7 10.6354 7 10.5C7 10.3646 7.04948 10.2474 7.14844 10.1484C7.2474 10.0495 7.36458 10 7.5 10H15.5C15.6354 10 15.7526 10.0495 15.8516 10.1484C15.9505 10.2474 16 10.3646 16 10.5C16 10.6354 15.9505 10.7526 15.8516 10.8516C15.7526 10.9505 15.6354 11 15.5 11H7.5Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </ToggleButton.Content>
                                    </ToggleButton>
                                </StackPanel>
                                <StackPanel Grid.Column="7">
                                    <ToggleButton x:Name="PART_AlignJustify" 
                                                  ToolTipService.ShowOnDisabled="True"
                                                  Padding="2 2 2 2" 
                                                   Margin="0 0 5 0"
                                                  Width="24" 
                                                  Height="24" 
                                                  Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                  Background="Transparent">
                                        <ToggleButton.Content>
                                            <Grid>
                                                <Path Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M0.5 1C0.364583 1 0.247396 0.950521 0.148438 0.851562C0.0494792 0.752604 0 0.635417 0 0.5C0 0.364583 0.0494792 0.247396 0.148438 0.148438C0.247396 0.0494792 0.364583 0 0.5 0H15.5C15.6354 0 15.7526 0.0494792 15.8516 0.148438C15.9505 0.247396 16 0.364583 16 0.5C16 0.635417 15.9505 0.752604 15.8516 0.851562C15.7526 0.950521 15.6354 1 15.5 1H0.5ZM0.5 6C0.364583 6 0.247396 5.95052 0.148438 5.85156C0.0494792 5.7526 0 5.63542 0 5.5C0 5.36458 0.0494792 5.2474 0.148438 5.14844C0.247396 5.04948 0.364583 5 0.5 5H15.5C15.6354 5 15.7526 5.04948 15.8516 5.14844C15.9505 5.2474 16 5.36458 16 5.5C16 5.63542 15.9505 5.7526 15.8516 5.85156C15.7526 5.95052 15.6354 6 15.5 6H0.5ZM0.5 11C0.364583 11 0.247396 10.9505 0.148438 10.8516C0.0494792 10.7526 0 10.6354 0 10.5C0 10.3646 0.0494792 10.2474 0.148438 10.1484C0.247396 10.0495 0.364583 10 0.5 10H15.5C15.6354 10 15.7526 10.0495 15.8516 10.1484C15.9505 10.2474 16 10.3646 16 10.5C16 10.6354 15.9505 10.7526 15.8516 10.8516C15.7526 10.9505 15.6354 11 15.5 11H0.5Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </ToggleButton.Content>
                                    </ToggleButton>
                                </StackPanel>
                                <Border Grid.Column="8" 
                                        Width="1"
                                        Margin="0 0 8 0" 
                                        Padding="4 2 2 2"
                                        BorderThickness="0 0 1 0"
                                        Opacity="0.5" 
                                        HorizontalAlignment="Center"
                                        Background="{StaticResource BorderAlt}"/>
                                <StackPanel Grid.Column="9">
                                    <ToggleButton x:Name="PART_IncreaseSpace" 
                                                  ToolTipService.ShowOnDisabled="True" 
                                                  Padding="2 2 2 2" 
                                                  Margin="0 0 5 0" 
                                                  Width="24" 
                                                  Height="24" 
                                                  Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                  Background="Transparent">
                                        <ToggleButton.Content>
                                            <Grid>
                                                <Path Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M7 3C6.86458 3 6.7474 2.95052 6.64844 2.85156C6.54948 2.7526 6.5 2.63542 6.5 2.5C6.5 2.36458 6.54948 2.2474 6.64844 2.14844C6.7474 2.04948 6.86458 2 7 2H14C14.1354 2 14.2526 2.04948 14.3516 2.14844C14.4505 2.2474 14.5 2.36458 14.5 2.5C14.5 2.63542 14.4505 2.7526 14.3516 2.85156C14.2526 2.95052 14.1354 3 14 3H7ZM9 6C8.86458 6 8.7474 5.95052 8.64844 5.85156C8.54948 5.7526 8.5 5.63542 8.5 5.5C8.5 5.36458 8.54948 5.2474 8.64844 5.14844C8.7474 5.04948 8.86458 5 9 5H14C14.1354 5 14.2526 5.04948 14.3516 5.14844C14.4505 5.2474 14.5 5.36458 14.5 5.5C14.5 5.63542 14.4505 5.7526 14.3516 5.85156C14.2526 5.95052 14.1354 6 14 6H9Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                                <Path Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M7 10C6.86458 10 6.7474 9.95052 6.64844 9.85156C6.54948 9.7526 6.5 9.63542 6.5 9.5C6.5 9.36458 6.54948 9.2474 6.64844 9.14844C6.7474 9.04948 6.86458 9 7 9H14C14.1354 9 14.2526 9.04948 14.3516 9.14844C14.4505 9.2474 14.5 9.36458 14.5 9.5C14.5 9.63542 14.4505 9.7526 14.3516 9.85156C14.2526 9.95052 14.1354 10 14 10H7ZM9 13C8.86458 13 8.7474 12.9505 8.64844 12.8516C8.54948 12.7526 8.5 12.6354 8.5 12.5C8.5 12.3646 8.54948 12.2474 8.64844 12.1484C8.7474 12.0495 8.86458 12 9 12H14C14.1354 12 14.2526 12.0495 14.3516 12.1484C14.4505 12.2474 14.5 12.3646 14.5 12.5C14.5 12.6354 14.4505 12.7526 14.3516 12.8516C14.2526 12.9505 14.1354 13 14 13H9Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                                <Path Fill="{StaticResource PrimaryBackground}">
                                                    <Path.Data>
                                                        <PathGeometry>M2.14645 0.146447C2.34171 -0.0488155 2.65829 -0.0488155 2.85355 0.146447L4.85355 2.14645C5.04882 2.34171 5.04882 2.65829 4.85355 2.85355C4.65829 3.04882 4.34171 3.04882 4.14645 2.85355L3 1.70711V5.5C3 5.77614 2.77614 6 2.5 6C2.22386 6 2 5.77614 2 5.5V1.70711L0.853553 2.85355C0.658291 3.04882 0.341709 3.04882 0.146447 2.85355C-0.0488155 2.65829 -0.0488155 2.34171 0.146447 2.14645L2.14645 0.146447Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                                <Path Fill="{StaticResource PrimaryBackground}">
                                                    <Path.Data>
                                                        <PathGeometry>M2.14645 14.8536C2.34171 15.0488 2.65829 15.0488 2.85355 14.8536L4.85355 12.8536C5.04882 12.6583 5.04882 12.3417 4.85355 12.1464C4.65829 11.9512 4.34171 11.9512 4.14645 12.1464L3 13.2929V9.5C3 9.22386 2.77614 9 2.5 9C2.22386 9 2 9.22386 2 9.5V13.2929L0.853553 12.1464C0.658291 11.9512 0.341709 11.9512 0.146447 12.1464C-0.0488155 12.3417 -0.0488155 12.6583 0.146447 12.8536L2.14645 14.8536Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </ToggleButton.Content>
                                    </ToggleButton>
                                </StackPanel>
                                <StackPanel Grid.Column="10">
                                    <ToggleButton x:Name="PART_DecreaseSpace" 
                                                  ToolTipService.ShowOnDisabled="True" 
                                                  Padding="2 2 2 2" 
                                                   Margin="0 0 5 0"
                                                  Width="24" 
                                                  Height="24"  
                                                  Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                  Background="Transparent">
                                        <ToggleButton.Content>
                                            <Grid>
                                                <Path Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M7 3C6.86458 3 6.7474 2.95052 6.64844 2.85156C6.54948 2.7526 6.5 2.63542 6.5 2.5C6.5 2.36458 6.54948 2.2474 6.64844 2.14844C6.7474 2.04948 6.86458 2 7 2H14C14.1354 2 14.2526 2.04948 14.3516 2.14844C14.4505 2.2474 14.5 2.36458 14.5 2.5C14.5 2.63542 14.4505 2.7526 14.3516 2.85156C14.2526 2.95052 14.1354 3 14 3H7ZM9 6C8.86458 6 8.7474 5.95052 8.64844 5.85156C8.54948 5.7526 8.5 5.63542 8.5 5.5C8.5 5.36458 8.54948 5.2474 8.64844 5.14844C8.7474 5.04948 8.86458 5 9 5H14C14.1354 5 14.2526 5.04948 14.3516 5.14844C14.4505 5.2474 14.5 5.36458 14.5 5.5C14.5 5.63542 14.4505 5.7526 14.3516 5.85156C14.2526 5.95052 14.1354 6 14 6H9Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                                <Path Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M7 10C6.86458 10 6.7474 9.95052 6.64844 9.85156C6.54948 9.7526 6.5 9.63542 6.5 9.5C6.5 9.36458 6.54948 9.2474 6.64844 9.14844C6.7474 9.04948 6.86458 9 7 9H14C14.1354 9 14.2526 9.04948 14.3516 9.14844C14.4505 9.2474 14.5 9.36458 14.5 9.5C14.5 9.63542 14.4505 9.7526 14.3516 9.85156C14.2526 9.95052 14.1354 10 14 10H7ZM9 13C8.86458 13 8.7474 12.9505 8.64844 12.8516C8.54948 12.7526 8.5 12.6354 8.5 12.5C8.5 12.3646 8.54948 12.2474 8.64844 12.1484C8.7474 12.0495 8.86458 12 9 12H14C14.1354 12 14.2526 12.0495 14.3516 12.1484C14.4505 12.2474 14.5 12.3646 14.5 12.5C14.5 12.6354 14.4505 12.7526 14.3516 12.8516C14.2526 12.9505 14.1354 13 14 13H9Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                                <Path Fill="{StaticResource PrimaryBackground}">
                                                    <Path.Data>
                                                        <PathGeometry>M2.14645 5.85355C2.34171 6.04882 2.65829 6.04882 2.85355 5.85355L4.85355 3.85355C5.04882 3.65829 5.04882 3.34171 4.85355 3.14645C4.65829 2.95118 4.34171 2.95118 4.14645 3.14645L3 4.29289V0.5C3 0.223857 2.77614 0 2.5 0C2.22386 0 2 0.223857 2 0.5V4.29289L0.853553 3.14645C0.658291 2.95118 0.341709 2.95118 0.146447 3.14645C-0.0488155 3.34171 -0.0488155 3.65829 0.146447 3.85355L2.14645 5.85355Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                                <Path Fill="{StaticResource PrimaryBackground}">
                                                    <Path.Data>
                                                        <PathGeometry>M2.14645 9.14645C2.34171 8.95118 2.65829 8.95118 2.85355 9.14645L4.85355 11.1464C5.04882 11.3417 5.04882 11.6583 4.85355 11.8536C4.65829 12.0488 4.34171 12.0488 4.14645 11.8536L3 10.7071V14.5C3 14.7761 2.77614 15 2.5 15C2.22386 15 2 14.7761 2 14.5V10.7071L0.853553 11.8536C0.658291 12.0488 0.341709 12.0488 0.146447 11.8536C-0.0488155 11.6583 -0.0488155 11.3417 0.146447 11.1464L2.14645 9.14645Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </ToggleButton.Content>
                                    </ToggleButton>
                                </StackPanel>
                                <Border Grid.Column="11"
                                        Width="1"
                                        Margin="5 5 5 5"
                                        Padding="4 2 2 2" 
                                        BorderThickness="0 0 1 0"
                                        Opacity="0.5"
                                        HorizontalAlignment="Center"
                                        Background="{StaticResource BorderAlt}"/>
                                <StackPanel Grid.Column="12">
                                    <ToggleButton x:Name="PART_Leftindent" 
                                                  ToolTipService.ShowOnDisabled="True" 
                                                  Padding="2 2 2 2" 
                                                   Margin="0 0 5 0"
                                                  Width="24" 
                                                  Height="24"  
                                                  Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                  Background="Transparent">
                                        <ToggleButton.Content>
                                            <Grid>
                                                <Path Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M0.5 0C0.223858 0 7.45058e-09 0.223858 7.45058e-09 0.5C7.45058e-09 0.776142 0.223858 1 0.5 1H15.5C15.7761 1 16 0.776142 16 0.5C16 0.223858 15.7761 0 15.5 0H0.5ZM8 4.5C8 4.77614 8.22386 5 8.5 5H15.5C15.7761 5 16 4.77614 16 4.5C16 4.22386 15.7761 4 15.5 4H8.5C8.22386 4 8 4.22386 8 4.5ZM8.5 8C8.22386 8 8 8.22386 8 8.5C8 8.77614 8.22386 9 8.5 9H15.5C15.7761 9 16 8.77614 16 8.5C16 8.22386 15.7761 8 15.5 8H8.5ZM0.5 12C0.223858 12 7.45058e-09 12.2239 7.45058e-09 12.5C7.45058e-09 12.7761 0.223858 13 0.5 13H15.5C15.7761 13 16 12.7761 16 12.5C16 12.2239 15.7761 12 15.5 12H0.5Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                                <Path Fill="{StaticResource PrimaryBackground}">
                                                    <Path.Data>
                                                        <PathGeometry>M2.85355 4.85355C3.04882 4.65829 3.04882 4.34171 2.85355 4.14645C2.65829 3.95118 2.34171 3.95118 2.14645 4.14645L0.146447 6.14645C-0.0488155 6.34171 -0.0488155 6.65829 0.146447 6.85355L2.14645 8.85355C2.34171 9.04882 2.65829 9.04882 2.85355 8.85355C3.04882 8.65829 3.04882 8.34171 2.85355 8.14645L1.70711 7H5.5C5.77614 7 6 6.77614 6 6.5C6 6.22386 5.77614 6 5.5 6H1.70711L2.85355 4.85355Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </ToggleButton.Content>
                                    </ToggleButton>
                                </StackPanel>
                                <StackPanel Grid.Column="13">
                                    <ToggleButton x:Name="PART_Rightindent" 
                                                  ToolTipService.ShowOnDisabled="True" 
                                                  Padding="2 2 2 2" 
                                                   Margin="0 0 5 0"
                                                  Width="24" 
                                                  Height="24"
                                                  Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                  Background="Transparent">
                                        <ToggleButton.Content>
                                            <Grid>
                                                <Path Fill="{StaticResource IconColor}">
                                                    <Path.Data>
                                                        <PathGeometry>M0.5 0C0.223858 0 0 0.223858 0 0.5C0 0.776142 0.223858 1 0.5 1H15.5C15.7761 1 16 0.776142 16 0.5C16 0.223858 15.7761 0 15.5 0H0.5ZM8 4.5C8 4.77614 8.22386 5 8.5 5H15.5C15.7761 5 16 4.77614 16 4.5C16 4.22386 15.7761 4 15.5 4H8.5C8.22386 4 8 4.22386 8 4.5ZM8.5 8C8.22386 8 8 8.22386 8 8.5C8 8.77614 8.22386 9 8.5 9H15.5C15.7761 9 16 8.77614 16 8.5C16 8.22386 15.7761 8 15.5 8H8.5ZM0.5 12C0.223858 12 0 12.2239 0 12.5C0 12.7761 0.223858 13 0.5 13H15.5C15.7761 13 16 12.7761 16 12.5C16 12.2239 15.7761 12 15.5 12H0.5Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                                <Path Fill="{StaticResource PrimaryBackground}">
                                                    <Path.Data>
                                                        <PathGeometry>M3.85355 4.14645C3.65829 3.95118 3.34171 3.95118 3.14645 4.14645C2.95118 4.34171 2.95118 4.65829 3.14645 4.85355L4.29289 6H0.5C0.223858 6 0 6.22386 0 6.5C0 6.77614 0.223858 7 0.5 7H4.29289L3.14645 8.14645C2.95118 8.34171 2.95118 8.65829 3.14645 8.85355C3.34171 9.04882 3.65829 9.04882 3.85355 8.85355L5.85355 6.85355C6.04882 6.65829 6.04882 6.34171 5.85355 6.14645L3.85355 4.14645Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Grid>
                                        </ToggleButton.Content>
                                    </ToggleButton>
                                </StackPanel>
                            </Grid>
                            <Border Grid.Row="6"
                                    Grid.Column="1"
                                    CornerRadius="0" 
                                    Height="1"
                                    Margin="5 12 5 5"
                                    Padding="4 2 2 2"
                                    BorderThickness="0 0 0 1"
                                    Opacity="0.5"
                                    VerticalAlignment="Center"/>
                            <Border Grid.Row="7"
                                    Grid.ColumnSpan="3"
                                    Width="Auto"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    Background="{StaticResource PopupBackground}" BorderThickness="0 1 0 0" >
                                <Grid Grid.Row="7" 
                                      Grid.Column="1"
                                      Grid.ColumnSpan="3"
                                      Height="33">
                                    <StackPanel  Orientation="Horizontal" 
                                                 HorizontalAlignment="Left" 
                                                 Height="26">
                                        <Syncfusion:DropDownButtonAdv x:Name="PART_Format" 
                                                          Grid.Row="7"
                                                          Grid.Column="1" 
                                                          Height="24"
                                                          HorizontalContentAlignment="Left"
                                                          HorizontalAlignment="Left" 
                                                          FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                                          FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                                          Padding="4 2 2 2"
                                                          Margin="10 -4 6 0"
                                                          IconHeight="0"
                                                          IconWidth="0"
                                                          Label="Format"
                                                          SizeMode="Normal"
                                                          Width="88">
                                            <ListBox HorizontalContentAlignment="Left" Width="100" x:Name="PART_FormatListBox">
                                                <ListBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ContextMenuFontDialog}" ></ListBoxItem>
                                                <ListBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=ContextMenuParagraphDialog}" ></ListBoxItem>
                                                <ListBoxItem Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNumberingToolTip}" ></ListBoxItem>
                                            </ListBox>
                                        </Syncfusion:DropDownButtonAdv>
                                    </StackPanel>
                                    <StackPanel   Orientation="Horizontal" 
                                                  HorizontalAlignment="Right"
                                                  Height="26">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <Button x:Name="PART_ApplyStyleFormatButton" 
                                                Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOk}"
                                                Grid.Column="0"
                                                Width="52" 
                                                IsDefault="True" 
                                                Height="24"
                                                Padding="12 2 12 2"
                                                Margin="0 -5 6 0"
                                                FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                                FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                                HorizontalAlignment="Right"
                                                VerticalAlignment="Center"
                                                TabIndex="10"
                                                Style="{StaticResource WPFPrimaryButtonStyle}"/>
                                            <Button x:Name="PART_CancelButton" 
                                                 Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxCancel}"
                                                 Grid.Column="1"
                                                 Width="69" 
                                                 Height="24"
                                                 Padding="12 2 12 2"
                                                 Margin="0 -4 6 0"
                                                 FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                                 FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                                 HorizontalAlignment="Right"
                                                 VerticalAlignment="Center"
                                                 TabIndex="11"
                                                 />
                                        </Grid>
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
