using System.Collections.ObjectModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using AirMonitor.Core.Constants;
using AirMonitor.Core.Enums;
using AirMonitor.Core.Interfaces;
using AirMonitor.Core.Models;
using Microsoft.Extensions.Logging;

namespace AirMonitor.LicenseGenerator.ViewModels;

/// <summary>
/// 许可证生成器视图模型
/// 管理许可证生成相关的界面逻辑
/// </summary>
public partial class LicenseGeneratorViewModel : ViewModelBase
{
    private readonly ILicenseGeneratorService _licenseGeneratorService;
    private readonly IHardwareFingerprintService _hardwareFingerprintService;
    private readonly ILogger<LicenseGeneratorViewModel> _logger;

    // 基本信息
    private string _licenseId = string.Empty;
    private string _productName = LicenseConstants.ProductName;
    private string _productVersion = LicenseConstants.ProductVersion;
    private LicenseType _selectedLicenseType = LicenseType.Standard;
    private string _customerName = string.Empty;
    private string _customerEmail = string.Empty;

    // 授权配置
    private DateTime _issuedDate = DateTime.Now;
    private DateTime _expiryDate = DateTime.Now.AddDays(LicenseConstants.DefaultValidityDays);
    private bool _isPermanent = false;
    private int _validityDays = LicenseConstants.DefaultValidityDays;
    private int _maxDeviceCount = LicenseConstants.UnlimitedDevices;
    private bool _isTrial = false;
    private int _trialDays = 0;

    // 硬件绑定
    private string _hardwareFingerprint = string.Empty;
    private HardwareFingerprintInfo? _hardwareFingerprintInfo;

    // 生成结果
    private string _generatedLicenseContent = string.Empty;
    private string _outputFilePath = string.Empty;

    public LicenseGeneratorViewModel(
        ILicenseGeneratorService licenseGeneratorService,
        IHardwareFingerprintService hardwareFingerprintService,
        ILogger<LicenseGeneratorViewModel> logger)
    {
        _licenseGeneratorService = licenseGeneratorService;
        _hardwareFingerprintService = hardwareFingerprintService;
        _logger = logger;

        InitializeCommands();
        InitializeFeatures();
        GenerateNewLicenseId();

        Title = "许可证生成器";
        StatusMessage = "请填写许可证信息";
    }

    #region 属性

    /// <summary>
    /// 许可证ID
    /// </summary>
    public string LicenseId
    {
        get => _licenseId;
        set => SetProperty(ref _licenseId, value);
    }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName
    {
        get => _productName;
        set => SetProperty(ref _productName, value);
    }

    /// <summary>
    /// 产品版本
    /// </summary>
    public string ProductVersion
    {
        get => _productVersion;
        set => SetProperty(ref _productVersion, value);
    }

    /// <summary>
    /// 选中的许可证类型
    /// </summary>
    public LicenseType SelectedLicenseType
    {
        get => _selectedLicenseType;
        set
        {
            if (SetProperty(ref _selectedLicenseType, value))
            {
                OnLicenseTypeChanged(value);
            }
        }
    }

    /// <summary>
    /// 客户名称
    /// </summary>
    public string CustomerName
    {
        get => _customerName;
        set => SetProperty(ref _customerName, value);
    }

    /// <summary>
    /// 客户邮箱
    /// </summary>
    public string CustomerEmail
    {
        get => _customerEmail;
        set => SetProperty(ref _customerEmail, value);
    }

    /// <summary>
    /// 签发日期
    /// </summary>
    public DateTime IssuedDate
    {
        get => _issuedDate;
        set => SetProperty(ref _issuedDate, value);
    }

    /// <summary>
    /// 过期日期
    /// </summary>
    public DateTime ExpiryDate
    {
        get => _expiryDate;
        set => SetProperty(ref _expiryDate, value);
    }

    /// <summary>
    /// 是否为永久许可证
    /// </summary>
    public bool IsPermanent
    {
        get => _isPermanent;
        set
        {
            if (SetProperty(ref _isPermanent, value))
            {
                if (value)
                {
                    ExpiryDate = DateTime.MaxValue;
                }
                else
                {
                    ExpiryDate = IssuedDate.AddDays(ValidityDays);
                }
            }
        }
    }

    /// <summary>
    /// 有效期天数
    /// </summary>
    public int ValidityDays
    {
        get => _validityDays;
        set
        {
            if (SetProperty(ref _validityDays, value) && !IsPermanent)
            {
                ExpiryDate = IssuedDate.AddDays(value);
            }
        }
    }

    /// <summary>
    /// 最大设备数量
    /// </summary>
    public int MaxDeviceCount
    {
        get => _maxDeviceCount;
        set => SetProperty(ref _maxDeviceCount, value);
    }

    /// <summary>
    /// 是否为试用版
    /// </summary>
    public bool IsTrial
    {
        get => _isTrial;
        set => SetProperty(ref _isTrial, value);
    }

    /// <summary>
    /// 试用天数
    /// </summary>
    public int TrialDays
    {
        get => _trialDays;
        set => SetProperty(ref _trialDays, value);
    }

    /// <summary>
    /// 硬件指纹
    /// </summary>
    public string HardwareFingerprint
    {
        get => _hardwareFingerprint;
        set => SetProperty(ref _hardwareFingerprint, value);
    }

    /// <summary>
    /// 硬件指纹信息
    /// </summary>
    public HardwareFingerprintInfo? HardwareFingerprintInfo
    {
        get => _hardwareFingerprintInfo;
        set => SetProperty(ref _hardwareFingerprintInfo, value);
    }

    /// <summary>
    /// 生成的许可证内容
    /// </summary>
    public string GeneratedLicenseContent
    {
        get => _generatedLicenseContent;
        set => SetProperty(ref _generatedLicenseContent, value);
    }

    /// <summary>
    /// 输出文件路径
    /// </summary>
    public string OutputFilePath
    {
        get => _outputFilePath;
        set => SetProperty(ref _outputFilePath, value);
    }

    /// <summary>
    /// 许可证类型列表
    /// </summary>
    public ObservableCollection<LicenseTypeItem> LicenseTypes { get; } = new();

    /// <summary>
    /// 功能特性列表
    /// </summary>
    public ObservableCollection<FeatureItem> Features { get; } = new();

    #endregion

    #region 命令

    /// <summary>
    /// 生成新许可证ID命令
    /// </summary>
    public ICommand GenerateNewIdCommand { get; private set; } = null!;

    /// <summary>
    /// 获取本地硬件指纹命令
    /// </summary>
    public ICommand GetLocalFingerprintCommand { get; private set; } = null!;

    /// <summary>
    /// 导入硬件指纹命令
    /// </summary>
    public ICommand ImportFingerprintCommand { get; private set; } = null!;

    /// <summary>
    /// 生成许可证命令
    /// </summary>
    public ICommand GenerateLicenseCommand { get; private set; } = null!;

    /// <summary>
    /// 保存许可证命令
    /// </summary>
    public ICommand SaveLicenseCommand { get; private set; } = null!;

    /// <summary>
    /// 预览许可证命令
    /// </summary>
    public ICommand PreviewLicenseCommand { get; private set; } = null!;

    /// <summary>
    /// 清除表单命令
    /// </summary>
    public ICommand ClearFormCommand { get; private set; } = null!;

    #endregion

    /// <summary>
    /// 初始化命令
    /// </summary>
    private void InitializeCommands()
    {
        GenerateNewIdCommand = new RelayCommand(GenerateNewLicenseId);
        GetLocalFingerprintCommand = new AsyncRelayCommand(GetLocalFingerprintAsync);
        ImportFingerprintCommand = new AsyncRelayCommand(ImportFingerprintAsync);
        GenerateLicenseCommand = new AsyncRelayCommand(GenerateLicenseAsync, CanGenerateLicense);
        SaveLicenseCommand = new AsyncRelayCommand(SaveLicenseAsync, CanSaveLicense);
        PreviewLicenseCommand = new RelayCommand(PreviewLicense, CanPreviewLicense);
        ClearFormCommand = new RelayCommand(ClearForm);
    }

    /// <summary>
    /// 初始化功能特性
    /// </summary>
    private void InitializeFeatures()
    {
        // 添加许可证类型
        LicenseTypes.Add(new LicenseTypeItem { Type = LicenseType.Standard, Name = "普通版", Description = "基础监控功能，1年有效期" });
        LicenseTypes.Add(new LicenseTypeItem { Type = LicenseType.AfterSales, Name = "售后版", Description = "售后服务专用功能，1年有效期" });
        LicenseTypes.Add(new LicenseTypeItem { Type = LicenseType.Development, Name = "研发版", Description = "所有功能，永久有效期" });
        LicenseTypes.Add(new LicenseTypeItem { Type = LicenseType.Management, Name = "管理版", Description = "所有功能+管理权限，永久有效期" });

        // 添加功能特性
        Features.Add(new FeatureItem { Code = FeatureCode.SerialCommunication.ToString(), Name = "串口通信", Description = "RS485串口通信功能", IsSelected = true });
        Features.Add(new FeatureItem { Code = FeatureCode.DataCollection.ToString(), Name = "数据采集", Description = "实时数据采集功能", IsSelected = true });
        Features.Add(new FeatureItem { Code = FeatureCode.RealTimeMonitoring.ToString(), Name = "实时监控", Description = "实时数据显示和监控", IsSelected = true });
        Features.Add(new FeatureItem { Code = FeatureCode.HistoryData.ToString(), Name = "历史数据", Description = "历史数据查询和分析", IsSelected = true });
        Features.Add(new FeatureItem { Code = FeatureCode.DataPlayback.ToString(), Name = "数据回放", Description = "历史数据回放功能", IsSelected = false });
        Features.Add(new FeatureItem { Code = FeatureCode.AlarmManagement.ToString(), Name = "报警管理", Description = "故障报警和管理功能", IsSelected = true });
        Features.Add(new FeatureItem { Code = FeatureCode.DataExport.ToString(), Name = "数据导出", Description = "数据导出功能", IsSelected = false });
        Features.Add(new FeatureItem { Code = FeatureCode.AdvancedAnalysis.ToString(), Name = "高级分析", Description = "高级数据分析功能", IsSelected = false });
        Features.Add(new FeatureItem { Code = FeatureCode.MultiDevice.ToString(), Name = "多设备支持", Description = "支持多设备同时监控", IsSelected = true });
        Features.Add(new FeatureItem { Code = FeatureCode.CustomProtocol.ToString(), Name = "自定义协议", Description = "自定义协议解析功能", IsSelected = false });
        Features.Add(new FeatureItem { Code = FeatureCode.SystemConfiguration.ToString(), Name = "系统配置", Description = "系统配置管理功能", IsSelected = false });
        Features.Add(new FeatureItem { Code = FeatureCode.UserManagement.ToString(), Name = "用户管理", Description = "用户管理功能", IsSelected = false });
        Features.Add(new FeatureItem { Code = FeatureCode.LicenseManagement.ToString(), Name = "许可证管理", Description = "许可证管理功能", IsSelected = false });
    }

    /// <summary>
    /// 生成新的许可证ID
    /// </summary>
    private void GenerateNewLicenseId()
    {
        try
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd");
            var random = new Random().Next(100000, 999999);
            LicenseId = $"{LicenseConstants.LicenseIdPrefix}-{timestamp}-{random}";
            
            SetInfoStatus("已生成新的许可证ID");
            _logger.LogDebug("生成新许可证ID: {LicenseId}", LicenseId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成许可证ID失败");
            SetErrorStatus("生成许可证ID失败");
        }
    }

    /// <summary>
    /// 获取本地硬件指纹
    /// </summary>
    private async Task GetLocalFingerprintAsync()
    {
        await ExecuteAsync(async () =>
        {
            HardwareFingerprintInfo = await _hardwareFingerprintService.GetDetailedFingerprintAsync();
            HardwareFingerprint = HardwareFingerprintInfo.Fingerprint;
            
            _logger.LogDebug("获取本地硬件指纹: {Fingerprint}", HardwareFingerprint);
        }, "正在获取硬件指纹...", "硬件指纹获取成功");
    }

    /// <summary>
    /// 导入硬件指纹
    /// </summary>
    private async Task ImportFingerprintAsync()
    {
        // TODO: 实现文件选择对话框
        await Task.Delay(100);
        SetInfoStatus("硬件指纹导入功能待实现");
    }

    /// <summary>
    /// 生成许可证
    /// </summary>
    private async Task GenerateLicenseAsync()
    {
        await ExecuteAsync(async () =>
        {
            var licenseInfo = CreateLicenseInfo();
            
            // TODO: 获取私钥（从配置或密钥文件）
            var privateKey = "临时私钥"; // 这里需要实际的私钥
            
            var result = await _licenseGeneratorService.GenerateLicenseAsync(licenseInfo, privateKey);
            
            if (result.IsSuccess)
            {
                GeneratedLicenseContent = result.LicenseContent ?? string.Empty;
                _logger.LogInformation("许可证生成成功: {LicenseId}", licenseInfo.LicenseId);
            }
            else
            {
                throw new InvalidOperationException(result.ErrorMessage);
            }
        }, "正在生成许可证...", "许可证生成成功");
    }

    /// <summary>
    /// 保存许可证
    /// </summary>
    private async Task SaveLicenseAsync()
    {
        // TODO: 实现文件保存对话框
        await Task.Delay(100);
        SetInfoStatus("许可证保存功能待实现");
    }

    /// <summary>
    /// 预览许可证
    /// </summary>
    private void PreviewLicense()
    {
        try
        {
            var licenseInfo = CreateLicenseInfo();
            // TODO: 显示预览对话框
            SetInfoStatus("许可证预览功能待实现");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预览许可证失败");
            SetErrorStatus("预览许可证失败");
        }
    }

    /// <summary>
    /// 清除表单
    /// </summary>
    private void ClearForm()
    {
        try
        {
            LicenseId = string.Empty;
            CustomerName = string.Empty;
            CustomerEmail = string.Empty;
            HardwareFingerprint = string.Empty;
            HardwareFingerprintInfo = null;
            GeneratedLicenseContent = string.Empty;
            OutputFilePath = string.Empty;
            
            // 重置日期
            IssuedDate = DateTime.Now;
            ExpiryDate = DateTime.Now.AddDays(LicenseConstants.DefaultValidityDays);
            IsPermanent = false;
            ValidityDays = LicenseConstants.DefaultValidityDays;
            
            // 重置功能选择
            foreach (var feature in Features)
            {
                feature.IsSelected = feature.Code == FeatureCode.SerialCommunication.ToString() ||
                                   feature.Code == FeatureCode.DataCollection.ToString() ||
                                   feature.Code == FeatureCode.RealTimeMonitoring.ToString() ||
                                   feature.Code == FeatureCode.HistoryData.ToString() ||
                                   feature.Code == FeatureCode.AlarmManagement.ToString() ||
                                   feature.Code == FeatureCode.MultiDevice.ToString();
            }
            
            GenerateNewLicenseId();
            SetInfoStatus("表单已清除");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除表单失败");
            SetErrorStatus("清除表单失败");
        }
    }

    /// <summary>
    /// 许可证类型变更处理
    /// </summary>
    private void OnLicenseTypeChanged(LicenseType licenseType)
    {
        try
        {
            // 根据许可证类型自动配置功能权限
            switch (licenseType)
            {
                case LicenseType.Standard:
                    ConfigureStandardFeatures();
                    IsPermanent = false;
                    ValidityDays = 365;
                    break;
                    
                case LicenseType.AfterSales:
                    ConfigureAfterSalesFeatures();
                    IsPermanent = false;
                    ValidityDays = 365;
                    break;
                    
                case LicenseType.Development:
                    ConfigureAllFeatures();
                    IsPermanent = true;
                    break;
                    
                case LicenseType.Management:
                    ConfigureAllFeatures();
                    IsPermanent = true;
                    break;
            }
            
            SetInfoStatus($"已切换到 {licenseType} 许可证类型");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换许可证类型失败");
            SetErrorStatus("切换许可证类型失败");
        }
    }

    /// <summary>
    /// 配置标准版功能
    /// </summary>
    private void ConfigureStandardFeatures()
    {
        var standardFeatures = new[]
        {
            FeatureCode.SerialCommunication.ToString(),
            FeatureCode.DataCollection.ToString(),
            FeatureCode.RealTimeMonitoring.ToString(),
            FeatureCode.HistoryData.ToString(),
            FeatureCode.AlarmManagement.ToString(),
            FeatureCode.MultiDevice.ToString()
        };

        foreach (var feature in Features)
        {
            feature.IsSelected = standardFeatures.Contains(feature.Code);
        }
    }

    /// <summary>
    /// 配置售后版功能
    /// </summary>
    private void ConfigureAfterSalesFeatures()
    {
        var afterSalesFeatures = new[]
        {
            FeatureCode.SerialCommunication.ToString(),
            FeatureCode.DataCollection.ToString(),
            FeatureCode.RealTimeMonitoring.ToString(),
            FeatureCode.HistoryData.ToString(),
            FeatureCode.DataPlayback.ToString(),
            FeatureCode.AlarmManagement.ToString(),
            FeatureCode.DataExport.ToString(),
            FeatureCode.AdvancedAnalysis.ToString(),
            FeatureCode.MultiDevice.ToString()
        };

        foreach (var feature in Features)
        {
            feature.IsSelected = afterSalesFeatures.Contains(feature.Code);
        }
    }

    /// <summary>
    /// 配置所有功能
    /// </summary>
    private void ConfigureAllFeatures()
    {
        foreach (var feature in Features)
        {
            feature.IsSelected = true;
        }
    }

    /// <summary>
    /// 创建许可证信息对象
    /// </summary>
    private LicenseInfo CreateLicenseInfo()
    {
        var selectedFeatures = Features.Where(f => f.IsSelected).Select(f => f.Code).ToList();
        
        return new LicenseInfo
        {
            LicenseId = LicenseId,
            ProductName = ProductName,
            ProductVersion = ProductVersion,
            CustomerName = CustomerName,
            CustomerEmail = CustomerEmail,
            LicenseType = SelectedLicenseType,
            AuthorizedFeatures = selectedFeatures,
            HardwareFingerprint = HardwareFingerprint,
            IssuedDate = IssuedDate,
            ExpiryDate = IsPermanent ? DateTime.MaxValue : ExpiryDate,
            MaxDeviceCount = MaxDeviceCount,
            IsTrial = IsTrial,
            TrialDays = TrialDays,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    #region 命令可执行性检查

    /// <summary>
    /// 检查是否可以生成许可证
    /// </summary>
    private bool CanGenerateLicense()
    {
        return !string.IsNullOrWhiteSpace(LicenseId) &&
               !string.IsNullOrWhiteSpace(CustomerName) &&
               !string.IsNullOrWhiteSpace(HardwareFingerprint) &&
               Features.Any(f => f.IsSelected);
    }

    /// <summary>
    /// 检查是否可以保存许可证
    /// </summary>
    private bool CanSaveLicense()
    {
        return !string.IsNullOrWhiteSpace(GeneratedLicenseContent);
    }

    /// <summary>
    /// 检查是否可以预览许可证
    /// </summary>
    private bool CanPreviewLicense()
    {
        return CanGenerateLicense();
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 激活时调用
    /// </summary>
    public void OnActivated()
    {
        SetInfoStatus("许可证生成器已激活");
    }

    /// <summary>
    /// 新建许可证
    /// </summary>
    public void NewLicense()
    {
        ClearForm();
    }

    /// <summary>
    /// 保存许可证
    /// </summary>
    public async void SaveLicense()
    {
        await SaveLicenseAsync();
    }

    /// <summary>
    /// 另存为许可证
    /// </summary>
    public async void SaveAsLicense()
    {
        await SaveLicenseAsync();
    }

    #endregion
}

/// <summary>
/// 许可证类型项
/// </summary>
public class LicenseTypeItem
{
    public LicenseType Type { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 功能项
/// </summary>
public class FeatureItem
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsSelected { get; set; }
}
