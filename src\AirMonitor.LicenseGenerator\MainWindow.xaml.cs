﻿using System.Windows;
using AirMonitor.LicenseGenerator.ViewModels;
using Microsoft.Extensions.Logging;

namespace AirMonitor.LicenseGenerator;

/// <summary>
/// 主窗口代码隐藏
/// </summary>
public partial class MainWindow : Window
{
    private readonly ILogger<MainWindow> _logger;

    public MainWindow(
        MainViewModel mainViewModel,
        LicenseGeneratorViewModel licenseGeneratorViewModel,
        LicenseValidatorViewModel licenseValidatorViewModel,
        TemplateManagerViewModel templateManagerViewModel,
        ILogger<MainWindow> logger)
    {
        _logger = logger;

        InitializeComponent();

        // 设置数据上下文
        DataContext = mainViewModel;

        // 设置子视图模型
        mainViewModel.SetChildViewModels(
            licenseGeneratorViewModel,
            licenseValidatorViewModel,
            templateManagerViewModel);

        _logger.LogInformation("主窗口已初始化");
    }

    /// <summary>
    /// 窗口加载完成事件
    /// </summary>
    private void Window_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            _logger.LogInformation("主窗口已加载");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "窗口加载时发生错误");
        }
    }

    /// <summary>
    /// 窗口关闭事件
    /// </summary>
    private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
    {
        try
        {
            // 检查是否有未保存的更改
            // TODO: 实现未保存更改检查逻辑

            _logger.LogInformation("主窗口正在关闭");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "窗口关闭时发生错误");
        }
    }
}