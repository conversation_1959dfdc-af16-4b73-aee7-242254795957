<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:surfaceChart="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF" 
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfSurfaceChart/SurfaceAxis.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <surfaceChart:BooleanToVisibilityConverter x:Key="booleanToVisibilityConverter" />

    <!--SfSurfaceChart-->

    <!--WallBrush-->

    <!--WireframeStroke-->

    <!--ColorBar-->

    <Style x:Key="SyncfusionSurfaceChartStyle" TargetType="surfaceChart:SfSurfaceChart">
        <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="LeftWallBrush" Value="{StaticResource ContentBackground}"/>
        <Setter Property="BackWallBrush" Value="Transparent"/>
        <Setter Property="BottomWallBrush" Value="{StaticResource ContentBackground}"/>
        <Setter Property="WireframeStroke" Value="{StaticResource BorderAlt}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="surfaceChart:SfSurfaceChart">
                    <Border BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid Background="{TemplateBinding Background}">
                            <surfaceChart:ChartDockPanel x:Name="Part_DockPanel"
                                                         LastChildFill="False"
                                                         KeyboardNavigation.TabNavigation="None">
                                <ContentPresenter x:Name="Header"
                                                  Content="{TemplateBinding Header}"
                                                  surfaceChart:ChartDockPanel.Dock="Top"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center" />
                                <surfaceChart:ChartDockPanel.RootElement>
                                    <surfaceChart:ChartRootPanel x:Name="Part_LayoutRoot">
                                        <ContentControl x:Name="Part_Container" />
                                        <Canvas x:Name="Part_Canvas"/>
                                    </surfaceChart:ChartRootPanel>
                                </surfaceChart:ChartDockPanel.RootElement>
                            </surfaceChart:ChartDockPanel>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSurfaceChartStyle}" TargetType="surfaceChart:SfSurfaceChart" />

    <Style x:Key="SyncfusionChartColorBarStyle" TargetType="surfaceChart:ChartColorBar">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <StackPanel Orientation="Horizontal"/>
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="ItemTemplate">
            <Setter.Value>
                <DataTemplate>
                    <StackPanel Margin="5" 
                                Orientation="{Binding Orientation}">
                        <Rectangle Height="{Binding IconHeight}"
                                   Width="{Binding IconWidth}"
                                   Fill="{Binding Background}"/>
                        <TextBlock Visibility="{Binding ShowLabel, Converter={StaticResource booleanToVisibilityConverter}}"
                                   Foreground="{StaticResource ContentForeground}"
                                   FontSize="{StaticResource Windows11Dark.CaptionText}"
                                   FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                                   FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                   Margin="3,0,0,0"  
                                   Text="{Binding Label}"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </DataTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="surfaceChart:ChartColorBar">
                    <Border Background="{TemplateBinding Background}"
                            HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalAlignment}">
                        <ItemsPresenter />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionChartColorBarStyle}" TargetType="surfaceChart:ChartColorBar" />

</ResourceDictionary>
