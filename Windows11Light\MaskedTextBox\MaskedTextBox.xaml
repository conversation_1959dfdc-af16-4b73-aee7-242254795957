<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Microsoft_Windows_Aero="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="MaskedTextBox.Static.Border" Color="#ababab" />
    <SolidColorBrush x:Key="MaskedTextBox.MouseOver.Border" Color="#ababab" />

    <LinearGradientBrush x:Key="MaskedTextBoxBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="MaskedTextBoxBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="MaskedTextBoxBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <DataTemplate x:Key="WatermarkContentTemplate">
        <TextBlock Text="{Binding}" Style="{x:Null}"/>
    </DataTemplate>
    
    <Style x:Key="SyncfusionMaskedTextBoxStyle" TargetType="{x:Type shared:MaskedTextBox}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="SelectionBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="BorderBrush" Value="{StaticResource MaskedTextBoxBorderBrush}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FocusedBorderBrush" Value="{StaticResource BorderAlt2}" />
        <Setter Property="WatermarkTextForeground" Value="{StaticResource PlaceholderForeground}" />
        <Setter Property="WatermarkBackground" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="WatermarkTemplate" Value="{StaticResource WatermarkContentTemplate}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}" />
        <Setter Property="CornerRadius" Value="{StaticResource Windows11Light.ThemeCornerRadiusVariant1}" />
        <Setter Property="Cursor" Value="IBeam" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="WatermarkOpacity" Value="1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:MaskedTextBox}">
                    <Grid x:Name="ContentHost">
                        <Border
                            x:Name="Border"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            SnapsToDevicePixels="True">
                            <Grid x:Name="InnerContentHost">
                                <ScrollViewer
                                    x:Name="PART_ContentHost"
                                    VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Background="Transparent"
                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                    Visibility="{TemplateBinding ContentElementVisibility}" 
                                    Margin="4,0,0,0">
                                </ScrollViewer>
                                <ContentControl
                                    x:Name="PART_Watermark"
                                    Grid.Row="0"
                                    Margin="6,0,0,0"
                                    VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Background="{TemplateBinding WatermarkBackground}"
                                    Content="{TemplateBinding WatermarkText}"
                                    ContentTemplate="{TemplateBinding WatermarkTemplate}"
                                    Cursor="IBeam"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                    FontStretch="{TemplateBinding FontStretch}"
                                    FontStyle="{TemplateBinding FontStyle}"
                                    FontWeight="{StaticResource Windows11Light.FontWeightNormal}"
                                    Foreground="{TemplateBinding WatermarkTextForeground}"
                                    IsHitTestVisible="False"
                                    IsTabStop="False"
                                    Opacity="{TemplateBinding WatermarkOpacity}"
                                    Visibility="{TemplateBinding WatermarkVisibility}" />
                            </Grid>
                        </Border>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="PART_Watermark" Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter TargetName="PART_Watermark" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource MaskedTextBoxBorderBrushHovered}" />
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt5}" />
                            <Setter TargetName="PART_ContentHost" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                            <Setter TargetName="PART_Watermark" Property="Background" Value="{StaticResource ContentBackgroundAlt5}" />
                            <Setter TargetName="PART_Watermark" Property="Foreground" Value="{StaticResource PlaceholderForeground}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="true">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource MaskedTextBoxBorderBrushFocused}" />
                            <Setter Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="PART_ContentHost" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}" />
                            <Setter Property="Padding" Value="0,0,0,-1" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionMaskedTextBoxStyle}" TargetType="{x:Type shared:MaskedTextBox}" />

</ResourceDictionary>
