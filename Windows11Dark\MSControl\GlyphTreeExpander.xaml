<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="WPFGlyphTreeExpanderToggleStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="Width" Value="16"/>
        <Setter Property="Height" Value="16"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border x:Name="border" 
                            Background="Transparent">
                        <TextBlock x:Name="ExpandPath"
                                   VerticalAlignment="Center" 
                                   HorizontalAlignment="Center" 
                                   Text="&#xe704;"
                                   Foreground="{StaticResource ContentForegroundAlt1}"
                                   FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinSize}"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter Property="Foreground" TargetName="ExpandPath" Value="{StaticResource IconColorSelected}"/>
                            <Setter Property="TextBlock.Text" TargetName="ExpandPath" Value="&#xe701;"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Foreground" TargetName="ExpandPath" Value="{StaticResource IconColor}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="IsChecked" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Foreground" TargetName="ExpandPath" Value="{StaticResource IconColorHovered}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="IsChecked" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Foreground" TargetName="ExpandPath" Value="{StaticResource IconColorDisabled}"/>
                            <Setter Property="Opacity" Value="1"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False"/>
                                <Condition Property="IsChecked" Value="False"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Foreground" TargetName="ExpandPath" Value="{StaticResource IconColorDisabled}"/>
                            <Setter Property="Opacity" Value="1"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="sfskin:SfSkinManager.SizeMode" Value="Touch"/>
                                <Condition Property="IsChecked" Value="False"/>
                            </MultiTrigger.Conditions>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="sfskin:SfSkinManager.SizeMode" Value="Touch"/>
                                <Condition Property="IsChecked" Value="True"/>
                            </MultiTrigger.Conditions>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
