<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:input="clr-namespace:Syncfusion.Windows.Controls.Input;assembly=Syncfusion.SfInput.WPF"
    xmlns:sfshared="clr-namespace:Syncfusion.Windows.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:input_controls="clr-namespace:Syncfusion.Windows.Controls;assembly=Syncfusion.SfInput.WPF"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphRepeatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/RepeatButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="DomainUpDown.MouseOver.Border" Color="#d1eaf5" />

    <SolidColorBrush x:Key="DomainUpDown.Focused.Border" Color="#d1eaf5" />

    <LinearGradientBrush x:Key="DomainUpDownBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DomainUpDownBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DomainUpDownBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style
        x:Key="UpDownRepeatButtonStyle"
        BasedOn="{StaticResource WPFGlyphRepeatButtonStyle}"
        TargetType="{x:Type RepeatButton}">
        <Setter Property="ContentTemplate">
            <Setter.Value>
                <DataTemplate>
                    <Path
                        x:Name="downbuttonpath"
                        Width="10"
                        Height="4"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Stroke="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                        StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}"
                        SnapsToDevicePixels="True"
                        Stretch="Uniform" 
                        Data="M0.5 1L4 4.5L7.5 1"/>
                </DataTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfUpDownStyle" TargetType="input_controls:SfUpDown">
        <Setter Property="AccentBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness}" />
        <Setter Property="BorderBrush" Value="{StaticResource Border}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="input_controls:SfUpDown">
                    <Border x:Name="Border" Focusable="False" SnapsToDevicePixels="True">
                        <Grid x:Name="PART_OuterGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <ContentControl x:Name="PART_Content" IsTabStop="False" Focusable="False" Content="{TemplateBinding UpDownContent}" 
                                            Margin="4,0,0,0"/>
                            <RepeatButton
                                x:Name="PART_DownButton"
                                Grid.Column="1"
                                Width="24"
                                Height="18"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch"
                                Background="{TemplateBinding AccentBrush}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Command="{TemplateBinding DownCommand}"
                                IsTabStop="False"
                                Style="{StaticResource WPFRepeatButtonStyle}">
                                <RepeatButton.Content>
                                    <Path x:Name="downbuttonpath"
                                                    Width="10"
                                                    Height="6"
                                                    HorizontalAlignment="Stretch"
                                                    VerticalAlignment="Stretch"
                                                    StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}"
                                                    SnapsToDevicePixels="True"
                                                    Stretch="Uniform" >
                                        <Path.Data>
                                            <PathGeometry>M0.5 1.125C0.5 1.02344 0.537109 0.935547 0.611328 0.861328C0.685547 0.787109 0.773438 0.75 0.875 0.75C0.976562 0.75 1.06445 0.787109 1.13867 0.861328L5 4.7168L8.86133 0.861328C8.93555 0.787109 9.02344 0.75 9.125 0.75C9.22656 0.75 9.31445 0.787109 9.38867 0.861328C9.46289 0.935547 9.5 1.02344 9.5 1.125C9.5 1.22656 9.46289 1.31445 9.38867 1.38867L5.26367 5.51367C5.18945 5.58789 5.10156 5.625 5 5.625C4.89844 5.625 4.81055 5.58789 4.73633 5.51367L0.611328 1.38867C0.537109 1.31445 0.5 1.22656 0.5 1.125Z</PathGeometry>
                                        </Path.Data>
                                        <Path.Style>
                                            <Style TargetType="Path">
                                                <Setter Property="Fill" Value="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"/>
                                            </Style>
                                        </Path.Style>
                                    </Path>
                                </RepeatButton.Content>
                            </RepeatButton>
                            <RepeatButton
                                x:Name="PART_UpButton"
                                Grid.Column="2"
                                Width="24"
                                Height="18"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch"
                                Background="{TemplateBinding AccentBrush}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Command="{TemplateBinding UpCommand}"
                                IsTabStop="False"
                                Style="{StaticResource WPFRepeatButtonStyle}">
                                <RepeatButton.Content>
                                    <Path
                                                    x:Name="upbuttonpath"
                                                    Width="10"
                                                    Height="6"
                                                    HorizontalAlignment="Stretch"
                                                    VerticalAlignment="Stretch"
                                                    StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}"
                                                    SnapsToDevicePixels="True"
                                                    Stretch="Uniform" >
                                        <Path.Data>
                                            <PathGeometry>M0.5 4.875C0.5 4.77344 0.537109 4.68555 0.611328 4.61133L4.73633 0.486328C4.81055 0.412109 4.89844 0.375 5 0.375C5.10156 0.375 5.18945 0.412109 5.26367 0.486328L9.38867 4.61133C9.46289 4.68555 9.5 4.77344 9.5 4.875C9.5 4.97656 9.46289 5.06445 9.38867 5.13867C9.31445 5.21289 9.22656 5.25 9.125 5.25C9.02344 5.25 8.93555 5.21289 8.86133 5.13867L5 1.2832L1.13867 5.13867C1.06445 5.21289 0.976562 5.25 0.875 5.25C0.773438 5.25 0.685547 5.21289 0.611328 5.13867C0.537109 5.06445 0.5 4.97656 0.5 4.875Z</PathGeometry>
                                        </Path.Data>
                                        <Path.Style>
                                            <Style TargetType="Path">
                                                <Setter Property="Fill" Value="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"/>
                                            </Style>
                                        </Path.Style>
                                    </Path>
                                </RepeatButton.Content>
                            </RepeatButton>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsKeyboardFocusWithin" Value="True">
                            <Setter Property="Margin" Value="0"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfDomainUpDownStyle" TargetType="input:SfDomainUpDown">
        <Setter Property="AccentBrush" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="BorderBrush" Value="{StaticResource DomainUpDownBorderBrush}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant1}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Hidden" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Hidden" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="input:SfDomainUpDown">
                    <Border
                        x:Name="Border"
                        Padding="{TemplateBinding Padding}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        Background="{TemplateBinding Background}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                        SnapsToDevicePixels="True">
                        <Grid Focusable="False">
                            <input_controls:SfUpDown
                                x:Name="PART_UpDown"
                                Grid.Column="1"
                                AccentBrush="{TemplateBinding AccentBrush}"
                                DownCommand="{Binding DecrementCommand, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                IsTabStop="False" Focusable="False"
                                SpinButtonsAlignment="{Binding SpinButtonsAlignment, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                Style="{StaticResource SyncfusionSfUpDownStyle}"
                                UpCommand="{Binding IncrementCommand, RelativeSource={RelativeSource Mode=TemplatedParent}}">
                                <input_controls:SfUpDown.UpDownContent>
                                    <Grid x:Name="InnerGrid">
                                        <sfshared:TransitionContentControl
                                            x:Name="PART_Content"
                                            Padding="{TemplateBinding Padding}"
                                            Margin="2,0,0,0" Focusable="False" IsTabStop="False"
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            Content="{TemplateBinding Value}"
                                            ContentTemplate="{TemplateBinding ContentTemplate}"
                                            EnableAnimation="{TemplateBinding EnableSpinAnimation}">
                                            <sfshared:TransitionContentControl.Transition>
                                                <sfshared:SlideTransition Direction="Up" />
                                            </sfshared:TransitionContentControl.Transition>
                                        </sfshared:TransitionContentControl>
                                    </Grid>
                                </input_controls:SfUpDown.UpDownContent>
                            </input_controls:SfUpDown>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter TargetName="PART_Content" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource DomainUpDownBorderBrushHovered}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocusWithin" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource DomainUpDownBorderBrushFocused}" />
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="Border" Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}" />
                            <Setter Property="Padding" Value="0,0,0,-2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfDomainUpDownStyle}" TargetType="input:SfDomainUpDown" />
</ResourceDictionary>
