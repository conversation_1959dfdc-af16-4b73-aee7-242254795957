<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:input="http://schemas.microsoft.com/netfx/2007/xaml/presentation"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>
  
    <Style x:Key="HyperlinkFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="{StaticResource Windows11Dark.FocusMargin}" SnapsToDevicePixels="true" Stroke="{StaticResource BorderAlt3}" StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}" StrokeDashArray="{StaticResource Windows11Dark.StrokeDashArray}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFHyperlinkStyle"
           TargetType="{x:Type Hyperlink}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource HyperlinkFocusVisual}"/>
        <Setter Property="Background"                                
                Value="Transparent"/>
        <Setter Property="Foreground"
                Value="{StaticResource LinkForeground}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="TextDecorations" Value="Underline"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{StaticResource LinkForegroundHovered}"/>
                <Setter Property="TextDecorations" Value="None" />
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="TextDecorations"  Value="Underline" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource WPFHyperlinkStyle}" TargetType="{x:Type Hyperlink}"/>

</ResourceDictionary>
