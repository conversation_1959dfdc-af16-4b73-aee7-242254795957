<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:circularGauge="clr-namespace:Syncfusion.UI.Xaml.Gauges;assembly=Syncfusion.SfGauge.WPF"
                    xmlns:system="clr-namespace:System;assembly=mscorlib"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="SyncfusionSfCircularGaugeStyle"
           TargetType="circularGauge:SfCircularGauge">
        <Setter Property="Background" Value="Transparent"></Setter>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="BorderThickness" Value="0"></Setter>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"></Setter>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}"></Setter>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}"></Setter>
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.CaptionText}"></Setter>
    </Style>

    <Style x:Key="SyncfusionCircularScaleStyle"
           TargetType="circularGauge:CircularScale">
        <Setter Property="RimStroke" Value="{StaticResource Series8}"></Setter>
        <Setter Property="TickStroke" Value="{StaticResource BorderAlt1}"></Setter>
        <Setter Property="LabelStroke" Value="{StaticResource ContentForeground}"></Setter>
        <Setter Property="SmallTickStroke" Value="{StaticResource BorderAlt1}"></Setter>
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.CaptionText}"></Setter>
        <Setter Property="IsTabStop" Value="False"/>
    </Style>

    <Style x:Key="SyncfusionCircularRangeStyle"
           TargetType="circularGauge:CircularRange">
        <Setter Property="Stroke" Value="{StaticResource Series1}"></Setter>
        <Setter Property="IsTabStop" Value="False"/>
    </Style>

    <Style x:Key="SyncfusionCircularPointerStyle"
           TargetType="circularGauge:CircularPointer">
        <Setter Property="RangePointerStroke" Value="{StaticResource Series9}"></Setter>
        <Setter Property="SymbolPointerStroke" Value="{StaticResource Series5}"></Setter>
        <Setter Property="NeedlePointerStroke" Value="{StaticResource BorderAlt3}"></Setter>
        <Setter Property="KnobStroke" Value="{StaticResource BorderAlt3}"></Setter>
        <Setter Property="KnobFill" Value="{StaticResource BorderAlt3}"></Setter>
        <Setter Property="TailFill" Value="{StaticResource BorderAlt3}"></Setter>
        <Setter Property="TailStroke" Value="{StaticResource BorderAlt3}"></Setter>
        <Setter Property="IsTabStop" Value="False"/>
    </Style>

    <Style TargetType="circularGauge:SfCircularGauge"
           BasedOn="{StaticResource SyncfusionSfCircularGaugeStyle}" />

    <Style TargetType="circularGauge:CircularScale"
           BasedOn="{StaticResource SyncfusionCircularScaleStyle}" />

    <Style TargetType="circularGauge:CircularRange"
           BasedOn="{StaticResource SyncfusionCircularRangeStyle}" />
    
    <Style TargetType="circularGauge:CircularPointer"
           BasedOn="{StaticResource SyncfusionCircularPointerStyle}" />
    
</ResourceDictionary>
