namespace AirMonitor.Core.Constants;

/// <summary>
/// 加密相关常量定义
/// </summary>
public static class CryptoConstants
{
    /// <summary>
    /// RSA密钥大小（位）
    /// </summary>
    public const int RSAKeySize = 2048;

    /// <summary>
    /// AES密钥大小（位）
    /// </summary>
    public const int AESKeySize = 256;

    /// <summary>
    /// AES块大小（字节）
    /// </summary>
    public const int AESBlockSize = 16;

    /// <summary>
    /// SHA256哈希长度（字节）
    /// </summary>
    public const int SHA256HashLength = 32;

    /// <summary>
    /// 盐值长度（字节）
    /// </summary>
    public const int SaltLength = 16;

    /// <summary>
    /// 迭代次数
    /// </summary>
    public const int IterationCount = 10000;

    /// <summary>
    /// RSA填充模式
    /// </summary>
    public const string RSAPadding = "OAEP";

    /// <summary>
    /// AES加密模式
    /// </summary>
    public const string AESMode = "CBC";

    /// <summary>
    /// 哈希算法名称
    /// </summary>
    public const string HashAlgorithm = "SHA256";

    /// <summary>
    /// 签名算法名称
    /// </summary>
    public const string SignatureAlgorithm = "SHA256withRSA";
}
