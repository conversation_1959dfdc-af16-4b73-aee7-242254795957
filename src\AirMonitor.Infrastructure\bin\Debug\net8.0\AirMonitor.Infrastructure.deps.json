{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AirMonitor.Infrastructure/1.0.0": {"dependencies": {"AirMonitor.Core": "1.0.0", "Microsoft.EntityFrameworkCore.Design": "9.0.5", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "System.IO.Ports": "9.0.5", "System.Text.Json": "9.0.5"}, "runtime": {"AirMonitor.Infrastructure.dll": {}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Build.Framework/17.8.3": {}, "Microsoft.Build.Locator/1.7.8": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "*******", "fileVersion": "1.7.8.28074"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0", "System.IO.Pipelines": "9.0.5", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Data.Sqlite.Core/9.0.5": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore/9.0.5": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.5", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.5": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.5": {}, "Microsoft.EntityFrameworkCore.Design/9.0.5": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyModel": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.5": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.5": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyModel": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.5"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.5": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.5", "Microsoft.EntityFrameworkCore.Relational": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyModel": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Caching.Memory/9.0.5": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyModel/9.0.5": {"dependencies": {"System.Text.Encodings.Web": "9.0.5", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "System.Diagnostics.DiagnosticSource": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Options/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Primitives/9.0.5": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.1"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System.IO.Ports/9.0.5": {"dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.5", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.5", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.5", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.5", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.5", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.5", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.5", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.5", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.5"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.5": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.core/2.1.10": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"rid": "linux-musl-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Collections.Immutable/7.0.0": {}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Diagnostics.DiagnosticSource/9.0.5": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.IO.Pipelines/9.0.5": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.IO.Ports/9.0.5": {"dependencies": {"runtime.native.System.IO.Ports": "9.0.5"}, "runtime": {"lib/net8.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.IO.Ports.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}, "runtimes/win/lib/net8.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Memory/4.5.3": {}, "System.Reflection.Metadata/7.0.0": {"dependencies": {"System.Collections.Immutable": "7.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encodings.Web/9.0.5": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Text.Json/9.0.5": {"dependencies": {"System.IO.Pipelines": "9.0.5", "System.Text.Encodings.Web": "9.0.5"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Threading.Channels/7.0.0": {}, "AirMonitor.Core/1.0.0": {"runtime": {"AirMonitor.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"AirMonitor.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "path": "microsoft.build.framework/17.8.3", "hashPath": "microsoft.build.framework.17.8.3.nupkg.sha512"}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "serviceable": true, "sha512": "sha512-sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "path": "microsoft.build.locator/1.7.8", "hashPath": "microsoft.build.locator.1.7.8.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cP5eBSqra4Ae80X72g0h2N+jdrA4BgoMQmz9JaQmKAEXUHw9N21DPIBqIyMjOo2fK9ISiGytlAOxBAJf1hEvqg==", "path": "microsoft.data.sqlite.core/9.0.5", "hashPath": "microsoft.data.sqlite.core.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-TeCtb/vc+jxvgkVAqeJlZKOoG5w/w8AigWQQyOmeJsJ7+0SkONX8bqEV/wB+ojnT0sXuJrrfXQOEC3ws6asEng==", "path": "microsoft.entityframeworkcore/9.0.5", "hashPath": "microsoft.entityframeworkcore.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-81fGyIibhGc4rq4ZxmVZE/1CFSvGMQOZqdRyCBLKz/Hb8eE973dmSfcdXpXhQ/5f+nbax4VGkWhwPGxWUNWaCQ==", "path": "microsoft.entityframeworkcore.abstractions/9.0.5", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-kWRrD69qCXo7lahPZPt7C127UfK0I024laFZEDMfT3JbALB1EWneFvq1utWM0cNKPFuYis1E1oaYTuRGI/9inQ==", "path": "microsoft.entityframeworkcore.analyzers/9.0.5", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-xOVWCGRF8DpOIoZ196/g7bdghc2e7Fp6R2vZPKndWv8A64bSDSaS7F2CUoqZpmSphUeT+1HDRpNYFRBQd8H71g==", "path": "microsoft.entityframeworkcore.design/9.0.5", "hashPath": "microsoft.entityframeworkcore.design.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-6eErbrZFd9yNnncemtDdmHZ3KC792OQCIYITuMsjK2oh4CLzlYo8mzNsozgUzQ+utHnne11/3eV8zMWbYF5Puw==", "path": "microsoft.entityframeworkcore.relational/9.0.5", "hashPath": "microsoft.entityframeworkcore.relational.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-dy9e3TUiU9COlFTyut6e12bZALM25PDskd6Kk10gbS3rAPYsuaKTkgq3mHDIQOR2bb3WEX7cdNpNF1+r2BIBMg==", "path": "microsoft.entityframeworkcore.sqlite/9.0.5", "hashPath": "microsoft.entityframeworkcore.sqlite.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-YU11QeQz53xKvr9hV9XABU9nwMMPOJFYuLB5bWgPMoE73ibiprksFzpnWhifRQu0c35jwVTj7kxHIAi/800CXA==", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.5", "hashPath": "microsoft.entityframeworkcore.sqlite.core.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-RV6wOTvH5BeVRs6cvxFuaV1ut05Dklpvq19XRO1JxAayfLWYIEP7K94aamY0iSUhoehWk1X5H6gMcbZkHuBjew==", "path": "microsoft.extensions.caching.abstractions/9.0.5", "hashPath": "microsoft.extensions.caching.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-qDmoAzIUBup5KZG1Abv51ifbHMCWFnaXbt05l+Sd92mLOpF9OwHOuoxu3XhzXaPGfq0Ns3pv1df5l8zuKjFgGw==", "path": "microsoft.extensions.caching.memory/9.0.5", "hashPath": "microsoft.extensions.caching.memory.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-ew0G6gIznnyAkbIa67wXspkDFcVektjN3xaDAfBDIPbWph+rbuGaaohFxUSGw28ht7wdcWtTtElKnzfkcDDbOQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.5", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "path": "microsoft.extensions.dependencyinjection/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-+jdJ9Vz+5Ia21l3KjahtmeHCIgQ7urfkdcJPxSfeqB40Jqryi27Lt4fKBmKyvc0YrfTUJ0cEB7QmoQRlU8FH0g==", "path": "microsoft.extensions.dependencymodel/9.0.5", "hashPath": "microsoft.extensions.dependencymodel.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "path": "microsoft.extensions.logging/9.0.5", "hashPath": "microsoft.extensions.logging.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "path": "microsoft.extensions.logging.abstractions/9.0.5", "hashPath": "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "path": "microsoft.extensions.options/9.0.5", "hashPath": "microsoft.extensions.options.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ==", "path": "microsoft.extensions.primitives/9.0.5", "hashPath": "microsoft.extensions.primitives.9.0.5.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-jdX92XbJ6rBTSPb3VwYRxDs6s5Vj9KcVDP6++6pVNF/D7OI13hgklUSVGMBRELpiC7SDEp/Bpsu980liACzC3g==", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.android-arm.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-z1LP33lTie6vWZR6dUXip5hOTobZ1WuXO+xpPCRcaKZo0bNkmEGaDatrwKnEUYxm/3PFBgFkH+M/s7uEub0SKQ==", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.android-arm64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-7xLefsg9LjqPIIhlFjVmJ41rIygrUxCvmOlrdWBoCJZLCK1uii7OovtX1iPwDe3I1Z1zZeEAPsMMN38Q5kJjPw==", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.android-x64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-p20gfW2eN8CvUBwkrkHJGvY4+6vERGqmGiYmViv2of29MgzkWYdrOM7BaFIRPtmN0zNp1QGGIgmXl1W1cti//Q==", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.android-x86.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Psp2JYBZ6CjEBew2qioj7zrSfwXjujPuuh8fuKnfMfkABP9F55pMT3UNfZen7gKVUoiPNhvAj0DrwUY0lKtmxA==", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rv3UX69IGI6VLM55OIYvoqbeAsicoit5gfk5T8VYy54B3lOIejwYa2QFcut9MW5cjxMH+dFCZ658fMd9jL+faA==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-8r4n05KMOeBkIiTyLP0jYCPwUVJ40RMEEUBTN/rrS+0X+Pj00zd8Qw48VW7JJ3PS7RFeJLiAP8q7uPCnAiFYPg==", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-1hcUPsq3RkJFp/4EYmhiS6NrjvpbaiqIUWYrn3gCs/pJIzEPB52GyyeeeabRrz720PkewEwUWtCXghCCtXrNqA==", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-jRLQt8O72nEtSelkrY8KL4Tj9ojbXl6zSQhmZ0NAQB1yZmihcFfqaHeqckreyUJ2xET/vb+tXZy1mcDHKop6Bw==", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-1yN18dV+F0Y/dMaAWCJ3eYVrBOi1j9u4TDA//oX5FqVjCHkYm8wG718EDXLhUYe9+JNH7ijFDl/+Ac2WAz2p+A==", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-l13ArjVyln6Zp4dHNXQDv38wWI6uT6LMiAygcQuYYU7fuEwM33WPBD7yNIaZEM2u5y+80u10p9TijIKHzDndrw==", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Hwr1s9Yxv07IFKUnR04ZVnUvukGCbpCtB0Lu3tfrRTEChLU8aCICCd6nPu37HBVNv7Cmpl78zLaFUHM7MA6EBw==", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-ML0BtpUQ39NhTM1PWihbtPpY1gZraLTXd14oyx46KmCWi0se8Xw3ph0zonvp3NXr86kAhvXwSnOIQqmZ+GMuXA==", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-nYwoFTuKG1i48QHaGI3xoXRtWhMdvcwaP/zgnrtaLpDYlF3K+XPDPsMZdILU5EOkqFu9WPYhcP3Lf78yUOCFIg==", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-VBaRoAdPdpwvFf8r/A9Ob3GEuXebdqBCw339+UbLeSZZZJf4Rn0vFSZv078G85MBwLLGkSOURKAjljsa7EOmRA==", "path": "runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ibaf8gfeFmlzHDFt6wHimINiKm60gtd7lQmx1YzVUZyp75z3He1bPDl/eNM4o0iMe//gYftrnFrEQGEVYPnq4Q==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-IzJV9kWskpB4KJbVxixUR404Sp/ABNXL3tmqWhb7nLSvrU7PZ3Hm7c9ycX0MRY4WmGkrFLICisDq1dmL9cxooA==", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.5", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.9.0.5.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections.Immutable/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "path": "system.collections.immutable/7.0.0", "hashPath": "system.collections.immutable.7.0.0.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-WoI5or8kY2VxFdDmsaRZ5yaYvvb+4MCyy66eXo79Cy1uMa7qXeGIlYmZx7R9Zy5S4xZjmqvkk2V8L6/vDwAAEA==", "path": "system.diagnostics.diagnosticsource/9.0.5", "hashPath": "system.diagnostics.diagnosticsource.9.0.5.nupkg.sha512"}, "System.IO.Pipelines/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-5WXo+3MGcnYn54+1ojf+kRzKq1Q6sDUnovujNJ2ky1nl1/kP3+PMil9LPbFvZ2mkhvAGmQcY07G2sfHat/v0Fw==", "path": "system.io.pipelines/9.0.5", "hashPath": "system.io.pipelines.9.0.5.nupkg.sha512"}, "System.IO.Ports/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ni0EW9jqQVgnx+v5XH/TFbahQF2lRXLQRe4OYFwxuMLLTOkkdOO8zGV3DiG18tCCo2ojpRqBaDGkt0MADQ4ITw==", "path": "system.io.ports/9.0.5", "hashPath": "system.io.ports.9.0.5.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Reflection.Metadata/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "path": "system.reflection.metadata/7.0.0", "hashPath": "system.reflection.metadata.7.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-HJPmqP2FsE+WVUUlTsZ4IFRSyzw40yz0ubiTnsaqm+Xo5fFZhVRvx6Zn8tLXj92/6pbre6OA4QL2A2vnCSKxJA==", "path": "system.text.encodings.web/9.0.5", "hashPath": "system.text.encodings.web.9.0.5.nupkg.sha512"}, "System.Text.Json/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rnP61ZfloTgPQPe7ecr36loNiGX3g1PocxlKHdY/FUpDSsExKkTxpMAlB4X35wNEPr1X7mkYZuQvW3Lhxmu7KA==", "path": "system.text.json/9.0.5", "hashPath": "system.text.json.9.0.5.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "AirMonitor.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}