<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:po="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options"
    xmlns:syncfusion="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphPrimaryToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ComboBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/DropDownButtonAdv/DropDownButtonAdv.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Menu.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/CheckBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Separator.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/RadioButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="ToolBarAdv.OverflowButton.Disabled.Path.Fill " Color="Transparent" />

    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

    <Style
        x:Key="SyncfusionToolBarAdvDropDownButtonAdvStyle"
        BasedOn="{StaticResource SyncfusionDropDownButtonAdvStyle}"
        TargetType="{x:Type syncfusion:DropDownButtonAdv}">
        <Setter Property="SmallIcon" Value="{x:Null}"/>
        <Setter Property="LargeIcon" Value="{x:Null}"/>
    </Style>

    <Style
        x:Key="SyncfusionToolBarAdvDropDownMenuItemStyle"
        BasedOn="{StaticResource SyncfusionDropDownMenuItemStyle}"
        TargetType="syncfusion:DropDownMenuItem">
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}" />
        <Setter Property="IsCheckable" Value="True" />
        <Setter Property="Background" Value="{StaticResource PopupBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="syncfusion:DropDownMenuItem">
                    <Grid>
                        <Grid.Resources>
                            <BooleanToVisibilityConverter x:Key="BoolConverter" />
                        </Grid.Resources>
                        <Border
                            Name="Bd"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="24" />
                                    <ColumnDefinition Width="24" />
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="24" />
                                </Grid.ColumnDefinitions>

                                <ContentControl
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    Grid.Column="2"
                                    Padding="6,2,2,2"
                                    Content="{TemplateBinding Header}" />
                                <ContentControl
                                    Grid.Column="3"
                                    Padding="2"
                                    Content="4"
                                    Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=HasItems, Converter={StaticResource BoolConverter}}" />

                                <ContentControl x:Name="iconContent"
                                        Width="16"
                                        Height="16"
                                        Grid.Column="1"
                                        Padding="2"
                                        Content="{TemplateBinding Icon}" />
                                <Border
                                    x:Name="PART_CheckedBorder"
                                    Width="24"
                                    Height="24"
                                    CornerRadius="3"
                                    Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IsChecked, Converter={StaticResource BoolConverter}}">
                                    <Path
                                        x:Name="CheckIcon"
                                        Width="12"
                                        Height="12"     
                                        Fill="{TemplateBinding Foreground}"
                                        FlowDirection="LeftToRight" >
                                        <Path.Data>
                                            <PathGeometry>M0.834961 3.41504C0.834961 3.30111 0.875651 3.20345 0.957031 3.12207C1.03841 3.04069 1.13607 3 1.25 3C1.36393 3 1.46159 3.04069 1.54297 3.12207L3.75 5.3291L8.45703 0.62207C8.53841 0.54069 8.63607 0.5 8.75 0.5C8.80859 0.5 8.8623 0.511393 8.91113 0.53418C8.96322 0.553711 9.00716 0.583008 9.04297 0.62207C9.08203 0.657878 9.11296 0.701823 9.13574 0.753906C9.15853 0.802734 9.16992 0.856445 9.16992 0.915039C9.16992 1.02897 9.1276 1.12826 9.04297 1.21289L4.04297 6.21289C3.96159 6.29427 3.86393 6.33496 3.75 6.33496C3.63607 6.33496 3.53841 6.29427 3.45703 6.21289L0.957031 3.71289C0.875651 3.63151 0.834961 3.53223 0.834961 3.41504Z</PathGeometry>
                                        </Path.Data>
                                    </Path>
                                </Border>
                            </Grid>
                        </Border>

                        <Popup x:Name="PART_DropDown" Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=HasItems, Converter={StaticResource BoolConverter}}">
                            <Grid x:Name="PART_poup" Opacity="0">
                                <Border
                                    Background="{StaticResource PopupBackground}"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    CornerRadius="0"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                    >
                                    <ItemsPresenter />
                                </Border>
                            </Grid>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Bd" Property="Background" Value="{StaticResource PopupHoveredBackground}" />
                            <Setter TargetName="Bd" Property="BorderBrush" Value="{StaticResource PopupHoveredBackground}" />
                            <Setter Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
                        </Trigger>
                        <Trigger Property="Icon" Value="{x:Null}">
                            <Setter TargetName="iconContent" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="SyncfusionOverflowButtonStyle"
        BasedOn="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
        TargetType="{x:Type ToggleButton}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Stretch" />
        <Setter Property="ClickMode" Value="Release" />
        <Setter Property="Padding" Value="1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border
                        x:Name="Chrome"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{StaticResource Windows11Light.BorderThickness}">
                        <ContentPresenter
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            RecognizesAccessKey="True"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Opacity" Value="0.3" />
                            <Setter Property="Background" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="SyncfusionToolBarAdvControlTemplate" TargetType="{x:Type syncfusion:ToolBarAdv}">
        <ControlTemplate.Resources>
            <DataTemplate x:Key="dropDownIconTemplate">
                <Image Source="{Binding Icon}"/>
            </DataTemplate>
        </ControlTemplate.Resources>
        <Border
            x:Name="mainborder"
            Background="{TemplateBinding Background}"
            BorderBrush="{TemplateBinding BorderBrush}"
            BorderThickness="0"
            CornerRadius="3"
            SnapsToDevicePixels="True">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Border
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{StaticResource Windows11Light.BorderThickness1110}"
                    CornerRadius="4"
                    SnapsToDevicePixels="True">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid
                            x:Name="PART_DragThumb"
                            Margin="2,3,4,2"
							Height="8"
                            Background="Transparent"
                            Cursor="SizeAll"
                            Visibility="{Binding GripperVisibility, RelativeSource={RelativeSource TemplatedParent}}">
                            <Path
                                x:Name="verticalGripper"
                                Width="14"
                                Height="2"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Data="M12.5,0.5 L13.5,0.5 L13.5,1.5 L12.5,1.5 z M8.5,0.5 L9.5,0.5 L9.5,1.5 L8.5,1.5 z M4.5,0.5 L5.5,0.5 L5.5,1.5 L4.5,1.5 z M0.5,0.5 L1.5,0.5 L1.5,1.5 L0.5,1.5 z"
                                Fill="{StaticResource IconColor}"
                                Stretch="Fill"
                                Stroke="{StaticResource IconColor}" />
                        </Grid>
                        <syncfusion:ToolBarPanelAdv
                            x:Name="PART_ToolBarPanel"
                            Grid.Row="1"
                            Margin="1"
                            Background="Transparent" />
                    </Grid>
                </Border>

                <ToggleButton
                    x:Name="PART_OverflowButton"
                    Grid.Row="1"
                    Height="16"
                    Margin="2,0,2,0"
                    Style="{StaticResource SyncfusionOverflowButtonStyle}"
                    HorizontalContentAlignment="Stretch"
                    Visibility="{Binding OverflowButtonVisibility, RelativeSource={RelativeSource TemplatedParent}}">
                    <Grid>
                        <Grid x:Name="verticalPath">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Path
                                x:Name="verticalDownPath"
                                Grid.Column="2"
                                Width="4"
                                Height="6"
                                Margin="0,0,4,0"
                                HorizontalAlignment="Left"
                                Data="M0.5,20.5 L0.5,0.5 L13.499992,10.582473 z"
                                Fill="{StaticResource IconColor}"
                                Stretch="Fill"
                                Stroke="{StaticResource IconColor}" />
                            <Path
                                x:Name="verticalDownPath1"
                                Grid.Column="1"
                                Width="1"
                                Height="6"
                                Margin="0,0,2,0"
                                HorizontalAlignment="Right"
                                Data="M0.5,0.5 L0.5,20"
                                Fill="{StaticResource IconColor}"
                                Stretch="Fill"
                                Stroke="{StaticResource IconColor}" />
                            <Path
                                x:Name="verticalTopOverflowPath"
                                Width="3"
                                Height="2.5"
                                Margin="3,2,3,2"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Top"
                                Data="M77,166.375 L96.111671,166.37529 L86.436386,174.8792 z"
                                Fill="{StaticResource IconColor}"
                                Stretch="Fill"
                                Stroke="{StaticResource IconColor}"
                                Visibility="Collapsed" />
                            <Path
                                x:Name="verticalBottomOverflowPath"
                                Width="3"
                                Height="2.5"
                                Margin="3,0,3,2"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Bottom"
                                Data="M77,166.375 L96.111671,166.37529 L86.436386,174.8792 z"
                                Fill="{StaticResource IconColor}"
                                Stretch="Fill"
                                Stroke="{StaticResource IconColor}"
                                Visibility="Collapsed" />
                        </Grid>
                    </Grid>
                </ToggleButton>
                <Popup
                    x:Name="PART_OverflowPopup"
                    PlacementTarget="{Binding ElementName=PART_OverflowButton}"
                    StaysOpen="False">
                    <Border
                        x:Name="PopupBorder"
                        MaxWidth="150"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        CornerRadius="8"
                        BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                        Effect="{StaticResource Default.ShadowDepth4}">
                        <StackPanel>
                            <syncfusion:ToolBarOverflowPanel x:Name="PART_ToolBarOverflowPanel" Margin="2" />
                            <syncfusion:DropDownButtonAdv
                                x:Name="PART_AddRemoveButtons"
                                Margin="2,0,2,2"
                                DropDirection="Right"
                                Label="Add or remove buttons"
                                Style="{StaticResource SyncfusionToolBarAdvDropDownButtonAdvStyle}"
                                Visibility="{Binding Path=EnableAddRemoveButton, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:ToolBarAdv}}, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <syncfusion:DropDownMenuGroup
                                    x:Name="PART_AddRemoveItems"
                                    Margin="0,2,0,2"
                                    IconBarEnabled="False"
                                    ItemsSource="{TemplateBinding ToolBarItemInfoCollection}"
                                    Style="{StaticResource SyncfusionDropDownMenuGroupStyle}">
                                    <syncfusion:DropDownMenuGroup.ItemTemplate>
                                        <DataTemplate>
                                            <syncfusion:DropDownMenuItem
                                                Header="{Binding Label}"
                                                IsChecked="{Binding IsChecked, Mode=TwoWay}"
                                                Style="{StaticResource SyncfusionToolBarAdvDropDownMenuItemStyle}">
                                                <syncfusion:DropDownMenuItem.Icon>
                                                    <ContentPresenter ContentTemplate="{Binding IconTemplate}"/>
                                                </syncfusion:DropDownMenuItem.Icon>
                                            </syncfusion:DropDownMenuItem>
                                        </DataTemplate>
                                    </syncfusion:DropDownMenuGroup.ItemTemplate>
                                </syncfusion:DropDownMenuGroup>
                            </syncfusion:DropDownButtonAdv>
                        </StackPanel>
                    </Border>
                </Popup>
            </Grid>
        </Border>
        <ControlTemplate.Triggers>

            <Trigger SourceName="PART_OverflowButton" Property="IsPressed" Value="True">
                <Setter TargetName="PART_OverflowButton" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="verticalDownPath" Property="Fill" Value="{StaticResource IconColorHovered}" />
                <Setter TargetName="verticalDownPath1" Property="Fill" Value="{StaticResource IconColorHovered}" />
                <Setter TargetName="verticalTopOverflowPath" Property="Fill" Value="{StaticResource IconColorHovered}" />
                <Setter TargetName="verticalBottomOverflowPath" Property="Fill" Value="{StaticResource IconColorHovered}" />
            </Trigger>

            <Trigger SourceName="PART_OverflowButton" Property="IsPressed" Value="True">
                <Setter TargetName="PART_OverflowButton" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                <Setter TargetName="verticalDownPath" Property="Fill" Value="{StaticResource IconColorSelected}" />
                <Setter TargetName="verticalDownPath1" Property="Fill" Value="{StaticResource IconColorSelected}" />
                <Setter TargetName="verticalTopOverflowPath" Property="Fill" Value="{StaticResource IconColorSelected}" />
                <Setter TargetName="verticalBottomOverflowPath" Property="Fill" Value="{StaticResource IconColorSelected}" />
            </Trigger>

            <Trigger SourceName="PART_OverflowButton" Property="IsFocused" Value="True">
                <Setter TargetName="PART_OverflowButton" Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
                <Setter TargetName="verticalDownPath" Property="Fill" Value="{StaticResource HoveredForeground}" />
                <Setter TargetName="verticalDownPath1" Property="Fill" Value="{StaticResource HoveredForeground}" />
                <Setter TargetName="verticalTopOverflowPath" Property="Fill" Value="{StaticResource HoveredForeground}" />
                <Setter TargetName="verticalBottomOverflowPath" Property="Fill" Value="{StaticResource HoveredForeground}" />
            </Trigger>

            <Trigger SourceName="PART_OverflowButton" Property="IsChecked" Value="True">
                <Setter TargetName="PART_OverflowButton" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                <Setter TargetName="verticalDownPath" Property="Fill" Value="{StaticResource IconColorSelected}" />
                <Setter TargetName="verticalDownPath1" Property="Fill" Value="{StaticResource IconColorSelected}" />
                <Setter TargetName="verticalTopOverflowPath" Property="Fill" Value="{StaticResource IconColorSelected}" />
                <Setter TargetName="verticalBottomOverflowPath" Property="Fill" Value="{StaticResource IconColorSelected}" />
            </Trigger>

            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter TargetName="PART_OverflowButton" Property="MinHeight" Value="{StaticResource TouchMode.MinSize}" />
                <Setter Property="MinWidth" TargetName="PART_OverflowButton" Value="36" />
                <Setter Property="MinWidth" Value="40" />
                <Setter Property="MaxWidth" TargetName="PopupBorder" Value="190"/>
            </Trigger>

            <Trigger SourceName="mainborder" Property="IsEnabled" Value="False">
                <Setter TargetName="verticalGripper" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                <Setter TargetName="verticalDownPath" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                <Setter TargetName="verticalDownPath1" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                <Setter TargetName="verticalTopOverflowPath" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                <Setter TargetName="verticalBottomOverflowPath" Property="Fill" Value="{StaticResource IconColorDisabled}" />
            </Trigger>

        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SyncfusionToolBarAdvStyle" TargetType="{x:Type syncfusion:ToolBarAdv}">
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}" />
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="MinWidth" Value="28" />
        <Setter Property="MinHeight" Value="32" />
        <Setter Property="ControlsResourceDictionary">
            <Setter.Value>
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/ToolBarAdv/ToolBarResources.xaml" />
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type syncfusion:ToolBarAdv}">
                    <ControlTemplate.Resources>
                        <DataTemplate x:Key="dropDownIconTemplate">
                            <Image Source="{Binding Icon}"/>
                        </DataTemplate>
                    </ControlTemplate.Resources>
                    <Border
                        x:Name="mainborder"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="8">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>

                                <Border
                                    x:Name="border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="3">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <Grid
                                            x:Name="PART_DragThumb"
                                            Margin="4,2,2,0"
											Width="8"
                                            Background="Transparent"
                                            Cursor="SizeAll"
                                            Visibility="{Binding GripperVisibility, RelativeSource={RelativeSource TemplatedParent}}">
                                            <Path
                                                x:Name="horizontalGripper"
                                                Width="2"
                                                Height="14"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Data="M0.5,12.5 L1.5000002,12.5 L1.5000002,13.5 L0.5,13.5 z M0.50000137,8.5 L1.5000012,8.5 L1.5000012,9.5 L0.50000137,9.5 z M0.50000137,4.5 L1.5000012,4.5 L1.5000012,5.5 L0.50000137,5.5 z M0.50000137,0.5 L1.5000014,0.5 L1.5000014,1.5 L0.50000137,1.5 z"
                                                Fill="{StaticResource IconColor}"
                                                Stretch="Fill"
                                                Stroke="{StaticResource IconColor}" />
                                        </Grid>
                                        <syncfusion:ToolBarPanelAdv
                                            x:Name="PART_ToolBarPanel"
                                            Grid.Column="1"
                                            Background="Transparent">
                                            <syncfusion:ToolBarPanelAdv.Margin>
                                              <Thickness>0,4,0,4</Thickness>
                                            </syncfusion:ToolBarPanelAdv.Margin>
                                        </syncfusion:ToolBarPanelAdv>
                                    </Grid>
                                </Border>

                                <ToggleButton
                                    x:Name="PART_OverflowButton"
                                    Grid.Column="1"
                                    Width="16"
                                    Margin="0,2,0,2"
                                    Style="{StaticResource SyncfusionOverflowButtonStyle}"
                                    Visibility="{Binding OverflowButtonVisibility, RelativeSource={RelativeSource TemplatedParent}}">
                                    <Grid x:Name="horizontalPath">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                    <Path
                                            x:Name="horizontalDownPath1"
                                            Grid.Row="1"
                                            Width="8"
                                            Height="2"
                                            Margin="0,0,0,2"
                                            VerticalAlignment="Bottom"
                                            Stretch="Fill"
                                            Stroke="{StaticResource IconColor}"
                                            StrokeThickness="1">
                                        <Path.Data>
                                            <PathGeometry>M0 0H8C12.4183 0 16 3.58172 16 8V24C16 28.4183 12.4183 32 8 32H0V0Z</PathGeometry>
                                        </Path.Data>
                                    </Path>
                                    <Path
                                            x:Name="horizontalDownPath"
                                            Grid.Row="2"
                                            Width="8"
                                            Height="8"
                                            Margin="0,0,0,4"
                                            VerticalAlignment="Top"
                                            Fill="{StaticResource IconColor}"
                                            Stretch="Uniform"
                                            Stroke="{StaticResource IconColor}" >
                                        <Path.Data>
                                            <PathGeometry>M1.50195 0.00195271C1.63867 0.00195272 1.75586 0.0507808 1.85352 0.148437L5 3.29492L8.14648 0.148437C8.24414 0.0507811 8.36133 0.00195301 8.49805 0.00195302C8.63477 0.00195302 8.75195 0.048828 8.84961 0.142578C8.95117 0.240234 9.00195 0.359375 9.00195 0.5C9.00195 0.636719 8.95312 0.753906 8.85547 0.851562L5.35156 4.35547C5.25391 4.45312 5.13672 4.50195 5 4.50195C4.86328 4.50195 4.74609 4.45312 4.64844 4.35547L1.14453 0.851562C1.04687 0.753906 0.998047 0.636718 0.998047 0.5C0.998047 0.359375 1.04687 0.240234 1.14453 0.142578C1.24609 0.0488277 1.36523 0.0019527 1.50195 0.00195271Z</PathGeometry>
                                        </Path.Data>
                                    </Path>
                                    <Path
                                            x:Name="horizontalLeftOverflowPath"
                                            Width="6"
                                            Height="6"
                                            Margin="0,3,1,3"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Top"
                                            Fill="{StaticResource IconColor}"
                                            Stretch="Uniform"
                                            Stroke="{StaticResource IconColor}" >
                                        <Path.Data>
                                            <PathGeometry>M0.75 9.125C0.75 9.02344 0.787109 8.93555 0.861328 8.86133L4.7168 5L0.861328 1.13867C0.787109 1.06445 0.75 0.976562 0.75 0.875C0.75 0.773438 0.787109 0.685547 0.861328 0.611328C0.935547 0.537109 1.02344 0.5 1.125 0.5C1.22656 0.5 1.31445 0.537109 1.38867 0.611328L5.51367 4.73633C5.58789 4.81055 5.625 4.89844 5.625 5C5.625 5.10156 5.58789 5.18945 5.51367 5.26367L1.38867 9.38867C1.31445 9.46289 1.22656 9.5 1.125 9.5C1.02344 9.5 0.935547 9.46289 0.861328 9.38867C0.787109 9.31445 0.75 9.22656 0.75 9.125Z</PathGeometry>
                                        </Path.Data>
                                    </Path>
                                    <Path
                                            x:Name="horizontalRightOverflowPath"
                                            Width="6"
                                            Height="6"
                                            Margin="0,3,0,3"
                                            HorizontalAlignment="Right"
                                            VerticalAlignment="Top"
                                            Fill="{StaticResource IconColor}"
                                            Stretch="Uniform"
                                            Stroke="{StaticResource IconColor}" >
                                        <Path.Data>
                                            <PathGeometry>M0.75 9.125C0.75 9.02344 0.787109 8.93555 0.861328 8.86133L4.7168 5L0.861328 1.13867C0.787109 1.06445 0.75 0.976562 0.75 0.875C0.75 0.773438 0.787109 0.685547 0.861328 0.611328C0.935547 0.537109 1.02344 0.5 1.125 0.5C1.22656 0.5 1.31445 0.537109 1.38867 0.611328L5.51367 4.73633C5.58789 4.81055 5.625 4.89844 5.625 5C5.625 5.10156 5.58789 5.18945 5.51367 5.26367L1.38867 9.38867C1.31445 9.46289 1.22656 9.5 1.125 9.5C1.02344 9.5 0.935547 9.46289 0.861328 9.38867C0.787109 9.31445 0.75 9.22656 0.75 9.125Z</PathGeometry>
                                        </Path.Data>
                                    </Path>
                                    </Grid>
                                </ToggleButton>

                                <Popup
                                    x:Name="PART_OverflowPopup"
                                    AllowsTransparency="True"
                                    PlacementTarget="{Binding ElementName=PART_OverflowButton}"
                                    StaysOpen="False">
                                    <Border
                                        x:Name="PopupBorder"
                                        MaxWidth="170"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        CornerRadius="8"
                                        BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                        Effect="{StaticResource Default.ShadowDepth4}">
                                        <Border.Margin>
                                        <Thickness>6,0,6,6</Thickness>
                                    </Border.Margin>
                                    <StackPanel>
                                            <syncfusion:ToolBarOverflowPanel x:Name="PART_ToolBarOverflowPanel" Margin="2" />
                                            <syncfusion:DropDownButtonAdv
                                                x:Name="PART_AddRemoveButtons"
                                                Margin="0,-4,0,0"
                                                DropDirection="Right"
                                                Label="Add or remove buttons"
                                                Style="{StaticResource SyncfusionToolBarAdvDropDownButtonAdvStyle}"
                                                Visibility="{Binding Path=EnableAddRemoveButton, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:ToolBarAdv}}, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                <syncfusion:DropDownMenuGroup
                                                    x:Name="PART_AddRemoveItems"
                                                    BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                                    IconBarEnabled="False"
                                                    ItemsSource="{TemplateBinding ToolBarItemInfoCollection}"
                                                    Style="{StaticResource SyncfusionDropDownMenuGroupStyle}">
                                                    <syncfusion:DropDownMenuGroup.ItemTemplate>
                                                        <DataTemplate>
                                                            <syncfusion:DropDownMenuItem
                                                                Header="{Binding Label}"
                                                                IsChecked="{Binding IsChecked, Mode=TwoWay}"
                                                                Style="{StaticResource SyncfusionToolBarAdvDropDownMenuItemStyle}">
                                                                <syncfusion:DropDownMenuItem.Icon>
                                                                    <ContentPresenter ContentTemplate="{Binding IconTemplate}"/>
                                                            </syncfusion:DropDownMenuItem.Icon>
                                                            </syncfusion:DropDownMenuItem>
                                                        </DataTemplate>
                                                    </syncfusion:DropDownMenuGroup.ItemTemplate>
                                                </syncfusion:DropDownMenuGroup>
                                            </syncfusion:DropDownButtonAdv>
                                        </StackPanel>
                                    </Border>
                                </Popup>
                            </Grid>
                    </Border>
                    <ControlTemplate.Triggers>

                        <Trigger SourceName="PART_OverflowButton" Property="IsMouseOver" Value="True">
                            <Setter TargetName="PART_OverflowButton" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                        </Trigger>

                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="PART_OverflowButton" Property="MinHeight" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter Property="MinHeight" TargetName="PART_OverflowButton" Value="36" />
                            <Setter Property="MinHeight" Value="40" />
                            <Setter Property="MaxWidth" TargetName="PopupBorder" Value="210"/>
                        </Trigger>

                        <Trigger SourceName="PART_OverflowButton" Property="IsPressed" Value="True">
                            <Setter TargetName="PART_OverflowButton" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="horizontalDownPath" Property="Fill" Value="{StaticResource IconColorSelected}" />
                            <Setter TargetName="horizontalDownPath1" Property="Fill" Value="{StaticResource IconColorSelected}" />
                            <Setter TargetName="horizontalLeftOverflowPath" Property="Fill" Value="{StaticResource IconColorSelected}" />
                            <Setter TargetName="horizontalRightOverflowPath" Property="Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>

                        <Trigger SourceName="PART_OverflowButton" Property="IsChecked" Value="True">
                            <Setter TargetName="PART_OverflowButton" Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
                            <Setter TargetName="horizontalDownPath" Property="Fill" Value="{StaticResource HoveredForeground}" />
                            <Setter TargetName="horizontalDownPath1" Property="Fill" Value="{StaticResource HoveredForeground}" />
                            <Setter TargetName="horizontalLeftOverflowPath" Property="Fill" Value="{StaticResource HoveredForeground}" />
                            <Setter TargetName="horizontalRightOverflowPath" Property="Fill" Value="{StaticResource HoveredForeground}" />
                        </Trigger>

                        <Trigger SourceName="mainborder" Property="IsEnabled" Value="False">
                            <Setter TargetName="horizontalGripper" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="horizontalDownPath" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="horizontalDownPath1" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="horizontalLeftOverflowPath" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="horizontalRightOverflowPath" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
            <Trigger Property="Orientation" Value="Vertical">
                <Setter Property="Template" Value="{StaticResource SyncfusionToolBarAdvControlTemplate}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionToolBarAdvStyle}" TargetType="syncfusion:ToolBarAdv" />

    <Style x:Key="SyncfusionToolBarItemSeparatorStyle" TargetType="syncfusion:ToolBarItemSeparator">
        <Setter Property="Background" Value="{StaticResource PopupBorder}" />
        <Setter Property="BorderBrush" Value="{StaticResource PopupBorder}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="BorderThickness" Value="1,1,0,0" />
        <Setter Property="Margin" Value="1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="syncfusion:ToolBarItemSeparator">
                    <StackPanel Orientation="{TemplateBinding Orientation}">
                        <Border
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}" />
                    </StackPanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionToolBarItemSeparatorStyle}" TargetType="syncfusion:ToolBarItemSeparator" />

    <Style
        x:Key="SyncfusionCloseButtonStyle"
        BasedOn="{StaticResource WPFFlatButtonStyle}"
        TargetType="{x:Type Button}">
        <Setter Property="IsTabStop" Value="false" />
        <Setter Property="MinWidth" Value="16" />
        <Setter Property="MinHeight" Value="16" />
        <Setter Property="Padding" Value="3" />
        <Setter Property="Cursor" Value="Arrow" />
    </Style>

    <Style x:Key="SyncfusionFloatingToolBarStyle" TargetType="{x:Type syncfusion:FloatingToolBar}">
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="ControlsResourceDictionary">
            <Setter.Value>
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/ToolBarAdv/ToolBarResources.xaml" />
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type syncfusion:FloatingToolBar}">
                    <ControlTemplate.Resources>
                        <DataTemplate x:Key="dropDownIconTemplate">
                            <Image Source="{Binding Icon}"/>
                        </DataTemplate>
                    </ControlTemplate.Resources>
                    <Grid x:Name="PART_LayoutGrid" Background="{TemplateBinding Background}">
                        <Grid.Margin>
                            <Thickness>14,0,14,14</Thickness>
                        </Grid.Margin>
                        <Border
                            Name="_Border"
                            Background="{StaticResource ContentBackground}"
                            BorderBrush="{StaticResource BorderAlt}"
                            BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                            Effect="{StaticResource Default.ShadowDepth4}">
                            <Border.CornerRadius>
                                <CornerRadius>8,8,8,8</CornerRadius>
                            </Border.CornerRadius>
                            <Grid x:Name="PART_Root">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>

                                <Border
                                    Grid.Row="1"
                                    Margin="5"
                                    Padding="5"
                                    Background="{StaticResource ContentBackgroundAlt1}"
                                    BorderBrush="Transparent"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness}">
                                    <ContentPresenter />
                                </Border>

                                <ContentControl x:Name="PART_Client" Grid.Row="1" />

                                <Border
                                    x:Name="PART_Resizegrip"
                                    Grid.Row="1"
                                    Margin="3"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Bottom"
                                    Background="Transparent"
                                    Cursor="SizeNWSE"
                                    Visibility="Collapsed">
                                    <Grid
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Bottom"
                                        Background="Transparent">
                                        <Path Data="M 9,1 L 11,1 L 11,3 L 9,3 Z M 5,5 L 7,5 L 7,7 L 5,7 Z M 9,5 L 11,5 L 11,7 L 9,7 Z M 1,9 L 3,9 L 3,11 L 1,11 Z M 5,9 L 7,9 L 7,11 L 5,11 Z M 9,9 L 11,9 L 11,11 L 9,11 Z" Fill="White" />
                                        <Path Data="M 8,0 L 10,0 L 10,2 L 8,2 Z M 4,4 L 6,4 L 6,6 L 4,6 Z M 8,4 L 10,4 L 10,6 L 8,6 Z M 0,8 L 2,8 L 2,10 L 0,10 Z M 4,8 L 6,8 L 6,10 L 4,10 Z M 8,8 L 10,8 L 10,10 L 8,10 Z" Fill="Gray" />
                                        <Thumb
                                            x:Name="PART_ResizeGripThumb"
                                            Cursor="SizeNWSE"
                                            Opacity="0.0" />
                                    </Grid>
                                </Border>

                                <Border
                                    x:Name="PART_Title"
                                    Margin="0"
                                    Padding="5"
                                    VerticalAlignment="Center"
                                    Background="{StaticResource ContentBackground}"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness1}">
                                    <Border.CornerRadius>
                                        <CornerRadius>8,8,0,0</CornerRadius>
                                    </Border.CornerRadius>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock
                                            Grid.Column="1"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Center"
                                            FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                            FontWeight="{StaticResource Windows11Light.FontWeightMedium}"
                                            Foreground="{TemplateBinding Foreground}"
                                            Text="{TemplateBinding Title}"
                                            TextTrimming="WordEllipsis" >
                                        </TextBlock>
                                        <Thumb
                                            x:Name="PART_TitleFloatingThumb"
                                            Grid.ColumnSpan="2"
                                            Opacity="0" >
                                        </Thumb>

                                        <Border
                                            Grid.Column="2"
                                            Width="Auto"
                                            Margin="0"
                                            VerticalAlignment="Center"
                                            Background="Transparent"
                                            CornerRadius="8">
                                            <StackPanel Orientation="Horizontal">
                                                <ToggleButton
                                                    x:Name="PART_AddRemoveButton"
                                                    Grid.Row="2" 
                                                    Margin="2"
                                                    Style="{StaticResource WPFFlatToggleButtonStyle}">
                                                    <Path
                                                        Width="8"
                                                        Height="4"
                                                        Margin="3"
                                                        HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"                                                      
                                                        Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                        Stretch="Uniform">
                                                        <Path.Data>
                                                            <PathGeometry>M5 5.625C4.89844 5.625 4.81055 5.58789 4.73633 5.51367L0.611328 1.38867C0.537109 1.31445 0.5 1.22656 0.5 1.125C0.5 1.02344 0.537109 0.935547 0.611328 0.861328C0.685547 0.787109 0.773437 0.75 0.875 0.75C0.976562 0.75 1.06445 0.787109 1.13867 0.861328L5 4.7168L8.86133 0.861328C8.93555 0.787109 9.02344 0.75 9.125 0.75C9.22656 0.75 9.31445 0.787109 9.38867 0.861328C9.46289 0.935546 9.5 1.02344 9.5 1.125C9.5 1.22656 9.46289 1.31445 9.38867 1.38867L5.26367 5.51367C5.18945 5.58789 5.10156 5.625 5 5.625Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                    
                                                </ToggleButton>
                                                <Popup
                                                    x:Name="PART_OverflowPopup"
                                                    AllowsTransparency="True"
                                                    IsOpen="{Binding ElementName=PART_AddRemoveButton, Path=IsChecked}"
                                                    PlacementTarget="{Binding ElementName=PART_AddRemoveButton}"
                                                    StaysOpen="False">
                                                    <Border
                                                        MaxWidth="170"
                                                        Background="{StaticResource ContentBackgroundAlt1}"
                                                        BorderBrush="{StaticResource BorderAlt}"
                                                        CornerRadius="8"
                                                        BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                                        >

                                                        <StackPanel>
                                                            <syncfusion:ToolBarOverflowPanel x:Name="PART_ToolBarOverflowPanel" Margin="2" />
                                                            <syncfusion:DropDownButtonAdv
                                                                Margin="0,-4,0,0"
                                                                DropDirection="Right"
                                                                Label="Add or remove buttons"
                                                                Style="{StaticResource SyncfusionToolBarAdvDropDownButtonAdvStyle}">
                                                                <syncfusion:DropDownMenuGroup
                                                                    x:Name="PART_AddRemoveItems"
                                                                    BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                                                    IconBarEnabled="False"
                                                                    ItemsSource="{Binding ToolBarItemInfoCollection, RelativeSource={RelativeSource TemplatedParent}}"
                                                                    Style="{StaticResource SyncfusionDropDownMenuGroupStyle}">
                                                                    <syncfusion:DropDownMenuGroup.ItemTemplate>
                                                                        <DataTemplate>
                                                                            <syncfusion:DropDownMenuItem
                                                                                Header="{Binding Label}"
                                                                                IsChecked="{Binding IsChecked, Mode=TwoWay}"
                                                                                Style="{StaticResource SyncfusionToolBarAdvDropDownMenuItemStyle}">
                                                                                <syncfusion:DropDownMenuItem.Icon>
                                                                                    <ContentPresenter ContentTemplate="{Binding IconTemplate}"/>
                                                                                </syncfusion:DropDownMenuItem.Icon>
                                                                            </syncfusion:DropDownMenuItem>
                                                                        </DataTemplate>
                                                                    </syncfusion:DropDownMenuGroup.ItemTemplate>
                                                                </syncfusion:DropDownMenuGroup>
                                                            </syncfusion:DropDownButtonAdv>
                                                        </StackPanel>
                                                    </Border>
                                                </Popup>
                                                <Button
                                                    x:Name="PART_CloseButton"
                                                    Margin="2"
                                                    Style="{StaticResource SyncfusionCloseButtonStyle}">
                                                    <Path
                                                        Width="9"
                                                        Height="8"
                                                        HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                        SnapsToDevicePixels="False"
                                                        Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M5 5.82129L0.854492 9.9668C0.756836 10.0645 0.639648 10.1133 0.50293 10.1133C0.359701 10.1133 0.239258 10.0661 0.141602 9.97168C0.0472005 9.87402 0 9.75358 0 9.61035C0 9.47363 0.0488281 9.35645 0.146484 9.25879L4.29199 5.11328L0.146484 0.967773C0.0488281 0.870117 0 0.751302 0 0.611328C0 0.542969 0.0130208 0.477865 0.0390625 0.416016C0.0651042 0.354167 0.100911 0.302083 0.146484 0.259766C0.192057 0.214193 0.245768 0.178385 0.307617 0.152344C0.369466 0.126302 0.43457 0.113281 0.50293 0.113281C0.639648 0.113281 0.756836 0.162109 0.854492 0.259766L5 4.40527L9.14551 0.259766C9.24316 0.162109 9.36198 0.113281 9.50195 0.113281C9.57031 0.113281 9.63379 0.126302 9.69238 0.152344C9.75423 0.178385 9.80794 0.214193 9.85352 0.259766C9.89909 0.305339 9.9349 0.359049 9.96094 0.420898C9.98698 0.479492 10 0.542969 10 0.611328C10 0.751302 9.95117 0.870117 9.85352 0.967773L5.70801 5.11328L9.85352 9.25879C9.95117 9.35645 10 9.47363 10 9.61035C10 9.67871 9.98698 9.74382 9.96094 9.80566C9.9349 9.86751 9.89909 9.92122 9.85352 9.9668C9.8112 10.0124 9.75911 10.0482 9.69727 10.0742C9.63542 10.1003 9.57031 10.1133 9.50195 10.1133C9.36198 10.1133 9.24316 10.0645 9.14551 9.9668L5 5.82129Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Button>
                                            </StackPanel>
                                        </Border>
                                        <Border
                                            x:Name="PART_SystemMenu"
                                            Grid.ColumnSpan="3"
                                            HorizontalAlignment="Left"
                                            Background="Transparent"
                                            Visibility="Collapsed" />
                                    </Grid>
                                </Border>
                            </Grid>
                        </Border>

                        <Thumb
                            x:Name="PART_TopThumb"
                            Height="5"
                            Margin="0,1,0,0"
                            VerticalAlignment="Top"
                            Cursor="SizeNS"
                            Opacity="0" />
                        <Thumb
                            x:Name="PART_RightThumb"
                            Width="5"
                            Margin="0,0,4,0"
                            HorizontalAlignment="Right"
                            Cursor="SizeWE"
                            Opacity="0" />
                        <Thumb
                            x:Name="PART_BottomThumb"
                            Height="5"
                            Margin="0,0,0,4"
                            VerticalAlignment="Bottom"
                            Cursor="SizeNS"
                            Opacity="0" />
                        <Thumb
                            x:Name="PART_LeftThumb"
                            Width="5"
                            Margin="4,0,0,0"
                            HorizontalAlignment="Left"
                            Cursor="SizeWE"
                            Opacity="0" />
                        <Thumb
                            x:Name="PART_TopLeftThumb"
                            Width="8"
                            Height="8"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                            Cursor="SizeNWSE"
                            Opacity="0" />
                        <Thumb
                            x:Name="PART_TopRightThumb"
                            Width="8"
                            Height="8"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Top"
                            Cursor="SizeNESW"
                            Opacity="0" />
                        <Thumb
                            x:Name="PART_BottomLeftThumb"
                            Width="8"
                            Height="8"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Bottom"
                            Cursor="SizeNESW"
                            Opacity="0" />
                        <Thumb
                            x:Name="PART_BottomRightThumb"
                            Width="8"
                            Height="8"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Bottom"
                            Cursor="SizeNWSE"
                            Opacity="0" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionFloatingToolBarStyle}" TargetType="{x:Type syncfusion:FloatingToolBar}" />

    <Style x:Key="SyncfusionToolBarManagerStyle" TargetType="syncfusion:ToolBarManager">
        <Setter Property="FloatingToolBarStyle" Value="{StaticResource SyncfusionFloatingToolBarStyle}" />
        <Setter Property="FocusManager.IsFocusScope" Value="True" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="syncfusion:ToolBarManager">
                    <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                            CornerRadius="8"> 
                        <syncfusion:ToolBarManagerPanel x:Name="PART_Panel">
                            <ContentControl x:Name="PART_Content" Content="{TemplateBinding Content}" />
                        </syncfusion:ToolBarManagerPanel>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

    </Style>

    <Style BasedOn="{StaticResource SyncfusionToolBarManagerStyle}" TargetType="syncfusion:ToolBarManager" />

    <Style x:Key="SyncfusionToolBarTrayAdvStyle" TargetType="{x:Type syncfusion:ToolBarTrayAdv}">
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="Margin" Value="2"/>
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionToolBarTrayAdvStyle}" TargetType="syncfusion:ToolBarTrayAdv" />

</ResourceDictionary>
