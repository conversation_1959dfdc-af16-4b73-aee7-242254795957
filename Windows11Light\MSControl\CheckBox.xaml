<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

	<ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>
    
    <Style x:Key="CheckBoxOptionMarkFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="17,0,0,0" 
                               Stroke="{StaticResource BorderAlt3}" 
                               StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Light.StrokeDashArray}"
                               SnapsToDevicePixels="true"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style x:Key="CheckBoxFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="{StaticResource Windows11Light.FocusMargin}" 
                               Stroke="{StaticResource BorderAlt3}" 
                               StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Light.StrokeDashArray}"
                               SnapsToDevicePixels="true"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <Style x:Key="WPFCheckBoxStyle" TargetType="{x:Type CheckBox}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource CheckBoxFocusVisual}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt1}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type CheckBox}">
                    <Grid x:Name="templateRoot" 
                          Background="Transparent"
                          SnapsToDevicePixels="True">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Ellipse x:Name="OverlayCircle"
                                 Height="24"
                                 Width="24"
                                 Fill="{StaticResource PrimaryBackgroundOpacity2}"
                                 Visibility="Collapsed"/>
                        <Border x:Name="checkBoxBorder"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}" 
                                Margin="1"  
                                Width="14"
                                Height="14"
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                Background="{TemplateBinding Background}"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius2}">
                            <Grid x:Name="markGrid">
                                <Path x:Name="optionMark"
                                      StrokeThickness="1.5"
                                      Width="10"
                                      Margin="1" 
                                      Opacity="0"
                                      Stroke="{StaticResource IconColor}"
                                      StrokeStartLineCap="Round"
                                      StrokeEndLineCap="Round"
                                      StrokeLineJoin="Round"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      Stretch="Uniform"
                                      StrokeDashOffset="0"
                                      StrokeDashArray="100">
                                    <Path.Data>
                                        <PathGeometry>M1 3L3 5L7 1</PathGeometry>
                                    </Path.Data>
                                </Path>
                                <TextBlock x:Name="indeterminateMark" 
                                           Text="&#xe70e;"
                                           FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                           FontSize="14"
                                           Foreground="{StaticResource IconColor}"
                                           Opacity="0"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                            </Grid>
                        </Border>
                        <ContentPresenter x:Name="contentPresenter" 
                                          Grid.Column="1" 
                                          Focusable="False"  
                                          Margin="{TemplateBinding Padding}" RecognizesAccessKey="True" 
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style TargetType="{x:Type TextBlock}" BasedOn="{x:Null}"/>
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="FlowDirection" Value="RightToLeft">
                            <Setter Property="FlowDirection" TargetName="optionMark" Value="LeftToRight"/>
                        </Trigger>
                        <Trigger Property="HasContent" Value="true">
                            <Setter Property="Padding" Value="4,0,0,0"/>
                            <Setter Property="FocusVisualStyle" Value="{StaticResource CheckBoxOptionMarkFocusVisual}"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard >
                                        <DoubleAnimation BeginTime="00:00:00" 
                                                             Storyboard.TargetName="optionMark" 
                                                             Storyboard.TargetProperty="StrokeDashOffset"
                                                             Duration="0:0:2" From="100" To="0"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition  Property="IsMouseOver" Value="true"/>
                                <Condition Property="IsChecked" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="BorderThickness" TargetName="checkBoxBorder" Value="{StaticResource Windows11Light.BorderThickness}"/>
                            <Setter Property="Stroke" TargetName="optionMark" Value="{StaticResource PrimaryForeground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition  Property="IsMouseOver" Value="true"/>
                                <Condition Property="IsChecked" Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource BorderAlt1}"/>
                            <Setter Property="Stroke" TargetName="optionMark" Value="Transparent"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition  Property="IsMouseOver" Value="true"/>
                                <Condition Property="IsChecked" Value="{x:Null}"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="BorderThickness" TargetName="checkBoxBorder" Value="{StaticResource Windows11Light.BorderThickness}"/>
                            <Setter Property="Foreground" TargetName="indeterminateMark" Value="{StaticResource PrimaryForeground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition  Property="IsPressed" Value="true"/>
                                <Condition Property="IsChecked" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                            <Setter Property="BorderThickness" TargetName="checkBoxBorder" Value="{StaticResource Windows11Light.BorderThickness}"/>
                            <Setter Property="Stroke" TargetName="optionMark" Value="{StaticResource PrimaryForegroundOpacity1}"/>
                            <Setter Property="Foreground" TargetName="indeterminateMark" Value="{StaticResource PrimaryForegroundOpacity1}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition  Property="IsPressed" Value="true"/>
                                <Condition Property="IsChecked" Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource PopupBorder}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="Stroke" TargetName="optionMark" Value="Transparent"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition  Property="IsPressed" Value="true"/>
                                <Condition Property="IsChecked" Value="{x:Null}"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                            <Setter Property="BorderThickness" TargetName="checkBoxBorder" Value="{StaticResource Windows11Light.BorderThickness}"/>
                            <Setter Property="Foreground" TargetName="indeterminateMark" Value="{StaticResource PrimaryForegroundOpacity1}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                        </MultiTrigger>
                        <Trigger Property="IsChecked" Value="{x:Null}">
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="BorderThickness" TargetName="checkBoxBorder" Value="{StaticResource Windows11Light.BorderThickness}"/>
                            <Setter Property="Foreground" TargetName="indeterminateMark" Value="{StaticResource PrimaryForeground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="Opacity" TargetName="optionMark" Value="0"/>
                            <Setter Property="Opacity" TargetName="indeterminateMark" Value="1"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="true">
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="BorderThickness" TargetName="checkBoxBorder" Value="{StaticResource Windows11Light.BorderThickness}"/>
                            <Setter Property="Opacity" TargetName="optionMark" Value="1"/>
                            <Setter Property="Opacity" TargetName="indeterminateMark" Value="0"/>
                            <Setter Property="Stroke" TargetName="optionMark" Value="{StaticResource PrimaryForeground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="false"/>
                                <Condition Property="IsChecked" Value="{x:Null}"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="Foreground" TargetName="indeterminateMark" Value="{StaticResource PrimaryForeground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource DisabledForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="false"/>
                                <Condition Property="IsChecked" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="Stroke" TargetName="optionMark" Value="{StaticResource PrimaryForeground}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource DisabledForeground}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="false"/>
                                <Condition Property="IsChecked" Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="Stroke" TargetName="optionMark" Value="Transparent"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource DisabledForeground}"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource WPFCheckBoxStyle}" TargetType="{x:Type CheckBox}"/>
</ResourceDictionary>
