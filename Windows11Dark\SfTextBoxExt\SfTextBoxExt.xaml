<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Input="clr-namespace:Syncfusion.Windows.Controls.Input;assembly=Syncfusion.SfInput.WPF"
    xmlns:Microsoft_Windows_Themes="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shared_converters="clr-namespace:Syncfusion.Windows.Converters;assembly=Syncfusion.SfInput.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphDropdownExpander.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphEditableDropdownExpander.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphButton.xaml" />

    </ResourceDictionary.MergedDictionaries>

    <BooleanToVisibilityConverter x:Key="BooleanVisibilityConverter" />

    <SolidColorBrush x:Key="TextBoxExt.Static.Border" Color="#9E9E9E" />
    <SolidColorBrush x:Key="TextBoxExt.Focused.Border" Color="#0279FF" />
    <SolidColorBrush x:Key="TextBoxExt.MouseOver.Border" Color="#757575" />
    
    <Thickness x:Key="DropDownButtonMargin">1,1,1,2</Thickness>
    <Thickness x:Key="DeleteButtonMargin">1,1,0,1</Thickness>
    <Thickness x:Key="ToggleButtonMargin">0,1,1,1</Thickness>
    <Thickness x:Key="Windows11TextMargin">10,0,0,0</Thickness>
    <Thickness x:Key="TextMargin">4,0,0,0</Thickness>
    <Thickness x:Key="WaterMarkMargin">2,0,0,0</Thickness>
    <Thickness x:Key="TokenBorderMargin">2,0,2,4</Thickness>
    <Thickness x:Key="SystemThemeTokenBorderMargin">2,3,2,2</Thickness>
    <Thickness x:Key="FluentThemeTokenBorderMargin">2,3,2,3</Thickness>
    <Thickness x:Key="WrapPanelMarginFluent">1,0,0,0</Thickness>

    <LinearGradientBrush x:Key="TextBoxExtBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.49"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="TextBoxExtBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.49"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="TextBoxExtBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <ControlTemplate x:Key="SyncfusionSfTextBoxExtDefaultControlTemplate" TargetType="{x:Type Input:SfTextBoxExt}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <Border
                x:Name="Border"
                Grid.ColumnSpan="3"
                Background="{TemplateBinding Background}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}"
                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                SnapsToDevicePixels="true">
                <Grid>
                    <ScrollViewer
                        x:Name="PART_ContentHost"
                        Margin="{StaticResource Windows11TextMargin}"
                        VerticalAlignment="{TemplateBinding VerticalAlignment}"
                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                        Background="Transparent"
                        Foreground="{TemplateBinding Foreground}"
                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />

                    <ContentControl
                        x:Name="PART_Watermark"
                        Grid.Row="0"
                        Margin="10,0,0,0"
                        Padding="{TemplateBinding Padding}"
                        VerticalAlignment="{TemplateBinding VerticalAlignment}"
                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                        Background="{StaticResource ContentBackgroundAlt4}"
                        Content="{TemplateBinding Watermark}"
                        ContentTemplate="{TemplateBinding WatermarkTemplate}"
                        ContentTemplateSelector="{TemplateBinding WatermarkTemplateSelector}"
                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                        FontStretch="{TemplateBinding FontStretch}"
                        FontStyle="{TemplateBinding FontStyle}"
                        FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                        Foreground="{StaticResource PlaceholderForeground}"
                        IsHitTestVisible="False"
                        IsTabStop="False"
                        Visibility="{TemplateBinding Visibility}" />
                </Grid>
            </Border>
            <Button
                Name="deleteButton"
                Grid.Column="1"
                Height="18"
                Width="24"
                Margin="1"
                Padding="5,0,5,0"
                Background="Transparent"
                BorderBrush="Transparent"
                BorderThickness="0"
                IsTabStop="False"
                Style="{StaticResource WPFGlyphDeleteButtonStyle}"
                Visibility="Collapsed">
                <Path
                    x:Name="ClearButtonContent"
                    Width="8"
                    Height="8"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Data="M1.4139423,0 L7.0029922,5.5845888 12.592018,0 14.006015,1.4149939 8.4180527,6.9985202 14.006,12.582007 12.591996,13.997001 7.0030056,8.4124444 1.4140122,13.997001 1.5026823E-05,12.582007 5.5879484,6.9985092 0,1.4149939 z"
                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                    Stretch="Fill"
                    UseLayoutRounding="True" />
            </Button>
            <ToggleButton
                Name="toggleButton"
                Grid.Column="2"
                Width="24"
                Height="18"
                HorizontalAlignment="Center"
                HorizontalContentAlignment="Center"
                IsChecked="{Binding Path=IsSuggestionOpen, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type Input:SfTextBoxExt}}}"
                IsTabStop="False"
                Style="{StaticResource WPFGlyphEditableDropdownExpanderStyle}"
                Visibility="{Binding ShowDropDownButton, Converter={StaticResource BooleanVisibilityConverter}, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                Padding="0,0,0,2"
                Margin="0,0,3,0">

            </ToggleButton>
            <Popup
                x:Name="PART_Popup"
                AllowsTransparency="true"
                IsOpen="{Binding IsSuggestionOpen, RelativeSource={RelativeSource TemplatedParent}}"
                SnapsToDevicePixels="True">
                <Border
                    x:Name="DropDownBorder"
                    BorderBrush="{StaticResource BorderAlt}"
                    BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                    Effect="{StaticResource Default.ShadowDepth4}"
                    CornerRadius="0,0,4,4"
                    Margin="14,0,14,14">
                    <Grid>
                        <Input:SuggestionBox
                            x:Name="PART_SuggestionBox"
                            MaxHeight="{TemplateBinding MaxDropDownHeight}"
                            Background="{TemplateBinding DropDownBackground}"
                            BorderBrush="{StaticResource BorderAlt}"
                            BorderThickness="0"
                            ItemsSource="{TemplateBinding Suggestions}"
                            ScrollViewer.VerticalScrollBarVisibility="Auto"
                            Padding="2">
                            <Input:SuggestionBox.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="{StaticResource Windows11Dark.CornerRadius4}"/>
                                </Style>
                            </Input:SuggestionBox.Resources>
                        </Input:SuggestionBox>
                    </Grid>
                </Border>
            </Popup>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource TextBoxExtBorderBrushHovered}" />
                <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt5}" />
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="{StaticResource ContentBackground}" />
                <Setter Property="BorderBrush" Value="{StaticResource TextBoxExtBorderBrushFocused}" />
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}" />
                <Setter TargetName="toggleButton" Property="Margin" Value="0,0,3,0" />
            </Trigger>
            <Trigger Property="IsKeyboardFocused" Value="true">
                <Setter Property="BorderBrush" Value="{StaticResource TextBoxExtBorderBrushFocused}" />
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}" />
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
                <Setter Property="Padding" Value="-1,-1,-1,-2" />
            </Trigger>
            <Trigger Property="IsSuggestionOpen" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource TextBoxExtBorderBrushFocused}" />
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}"/>
                <Setter TargetName="Border" Property="CornerRadius" Value="4,4,0,0"/>
                <Setter Property="Padding" Value="-1,-1,-1,-2"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionSfTextBoxExtTokenControlTemplate" TargetType="{x:Type Input:SfTextBoxExt}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <Border
                x:Name="Border"
                Grid.ColumnSpan="2"
                MinHeight="24"
                Padding="0"
                Background="{TemplateBinding Background}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}"
                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                SnapsToDevicePixels="true">
                <Grid Margin="{TemplateBinding Padding}">
                    <ScrollViewer x:Name="MultiPanelScrollViewer"  HorizontalAlignment="Left" Foreground="{TemplateBinding Foreground}">
                        <WrapPanel 
                            x:Name="Multi_Panel"
                            MinHeight="20"
                            VerticalAlignment="Center"
                            Orientation="Horizontal">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*" MinHeight="20"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>

                                <ScrollViewer
                                    x:Name="PART_ContentHost"
                                    Grid.Row="0" 
                                    Background="Transparent"
                                    Foreground="{TemplateBinding Foreground}"
                                    Grid.Column="1" 
                                    Margin="{StaticResource Windows11TextMargin}"
                                    VerticalAlignment="Center"
                                    VerticalContentAlignment="Center"
                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />

                                <ContentControl
                                    x:Name="PART_Watermark"
                                    Grid.Row="0"
                                    Grid.ColumnSpan="2"
                                    Margin="10,0,0,0"
                                    Padding="{TemplateBinding Padding}"
                                    VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Background="{StaticResource ContentBackgroundAlt4}"
                                    Content="{TemplateBinding Watermark}"
                                    ContentTemplate="{TemplateBinding WatermarkTemplate}"
                                    ContentTemplateSelector="{TemplateBinding WatermarkTemplateSelector}"
                                    FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                    FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                    FontStretch="{TemplateBinding FontStretch}"
                                    FontStyle="{TemplateBinding FontStyle}"
                                    FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                                    Foreground="{StaticResource PlaceholderForeground}"
                                    IsHitTestVisible="False"
                                    IsTabStop="False"
                                    Visibility="{TemplateBinding Visibility}" />
                            </Grid>
                        </WrapPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Name="deleteButton" 
                        Height="18"
                        Width="24"
                        Padding="5,0,5,0" 
                        Margin="1"
                        Visibility="Collapsed" 
                        Background="Transparent" 
                        BorderBrush="Transparent"
                        Style="{StaticResource WPFGlyphDeleteButtonStyle}"
                        BorderThickness="0"
                        IsTabStop="False">
                    <Path x:Name="ClearButtonContent"
                        Width="8"
                        Height="8"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Data="M1.4139423,0 L7.0029922,5.5845888 12.592018,0 14.006015,1.4149939 8.4180527,6.9985202 14.006,12.582007 12.591996,13.997001 7.0030056,8.4124444 1.4140122,13.997001 1.5026823E-05,12.582007 5.5879484,6.9985092 0,1.4149939 z"
                        Fill="{Binding Path=Foreground,RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                        Stretch="Fill"
                        UseLayoutRounding="True" />
                </Button>
                <ToggleButton
                    Name="toggleButton"
                    Width="24"
                    Height="18"
                    HorizontalAlignment="Center"
                    HorizontalContentAlignment="Center"
                    IsChecked="{Binding Path=IsSuggestionOpen, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type Input:SfTextBoxExt}}}"
                    IsTabStop="False"
                    Style="{StaticResource WPFGlyphEditableDropdownExpanderStyle}"
                    Visibility="{Binding ShowDropDownButton, Converter={StaticResource BooleanVisibilityConverter}, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                    Padding="0,0,0,2"
                    Margin="0,0,3,0">
                </ToggleButton>
            </StackPanel>
            <Popup
                x:Name="PART_Popup"
                AllowsTransparency="true"
                IsOpen="{Binding IsSuggestionOpen, RelativeSource={RelativeSource TemplatedParent}}"
                SnapsToDevicePixels="True">
                <Border
                    x:Name="DropDownBorder"
                    BorderBrush="{StaticResource BorderAlt}"
                    BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                    Effect="{StaticResource Default.ShadowDepth4}" 
                    CornerRadius="0,0,4,4"
                    Margin="14,0,14,14">
                    <Grid>
                        <Input:SuggestionBox
                            x:Name="PART_SuggestionBox"
                            MaxHeight="{TemplateBinding MaxDropDownHeight}"
                            Background="{TemplateBinding DropDownBackground}"
                            BorderBrush="{StaticResource BorderAlt}"
                            BorderThickness="0"
                            ItemsSource="{TemplateBinding Suggestions}"
                            ScrollViewer.VerticalScrollBarVisibility="Auto"
                            Padding="2">
                            <Input:SuggestionBox.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="{StaticResource Windows11Dark.CornerRadius4}"/>
                                </Style>
                            </Input:SuggestionBox.Resources>
                        </Input:SuggestionBox>
                    </Grid>
                </Border>
            </Popup>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter TargetName="deleteButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource TextBoxExtBorderBrushHovered}" />
                <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt5}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition  Property="sfskin:SfSkinManager.SizeMode" Value="Default" />
                    <Condition Property="IsKeyboardFocused" Value="true" />
                </MultiTrigger.Conditions>
                <Setter Property="Padding" Value="-1,-1,-1,-2"/>
            </MultiTrigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="{StaticResource ContentBackground}" />
                <Setter Property="BorderBrush" Value="{StaticResource TextBoxExtBorderBrushFocused}" />
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}"/>
                <Setter TargetName="deleteButton" Property="Margin" Value="1"/>
                <Setter TargetName="toggleButton" Property="Margin" Value="0,0,3,0"/>
            </Trigger>
            <Trigger Property="IsKeyboardFocused" Value="true">
                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource TextBoxExtBorderBrushFocused}" />
                <Setter TargetName="Border" Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}"/>
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
                <Setter Property="Padding" Value="-1,-1,-1,-2"/>
            </Trigger>
            
            <Trigger Property="IsFocused" Value="False">
                <Setter Property="BorderBrush" Value="{StaticResource TextBoxExtBorderBrush}" />
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1}"/>
            </Trigger>
            <Trigger Property="IsSuggestionOpen" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource TextBoxExtBorderBrushFocused}" />
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}" />
                <Setter TargetName="Border" Property="CornerRadius" Value="4,4,0,0"/>
                <Setter Property="Padding" Value="-1,-1,-1,-2"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                <Setter TargetName="MultiPanelScrollViewer" Property="Background" Value="Transparent" />
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SyncfusionSfTextBoxExtStyle" TargetType="{x:Type Input:SfTextBoxExt}">
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="SelectionBackgroundColor" Value="{StaticResource PopupSelectedBackground}"/>
        <Setter Property="HighlightedTextColor" Value="{StaticResource PopupHoveredForeground}"/>
        <Setter Property="SelectionBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="BorderBrush" Value="{StaticResource TextBoxExtBorderBrush}" />
        <Setter Property="DropDownBackground" Value="{StaticResource PopupBackground}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant1}" />
        <Setter Property="Padding" Value="-1" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}" />
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
            </Trigger>
            <Trigger Property="MultiSelectMode" Value="None">
                <Setter Property="Template" Value="{StaticResource SyncfusionSfTextBoxExtDefaultControlTemplate}" />
            </Trigger>
            <Trigger Property="MultiSelectMode" Value="Delimiter">
                <Setter Property="Template" Value="{StaticResource SyncfusionSfTextBoxExtDefaultControlTemplate}" />
            </Trigger>
            <Trigger Property="MultiSelectMode" Value="Token">
                <Setter Property="Template" Value="{StaticResource SyncfusionSfTextBoxExtTokenControlTemplate}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfTextBoxExtStyle}" TargetType="{x:Type Input:SfTextBoxExt}" />

    <Style x:Key="SyncfusionSfTextBoxExtTokenItemStyle" TargetType="Input:TokenItem">
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Input:TokenItem">
                    <Border
                        x:Name="TokenBorder"
                        Height="18"
                        MinHeight="0"
                        Background="{StaticResource ContentBackgroundAlt2}"
                        BorderBrush="{StaticResource ContentBackgroundAlt2}"
                        CornerRadius="4"
                        BorderThickness="0"
                        Margin="10,2,2,2">
                        <Grid x:Name="TokenGrid" Margin="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="auto" />
                            </Grid.ColumnDefinitions>
                            <Image
                                x:Name="TokenImage"
                                MaxHeight="20"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Source="{TemplateBinding Image}" />
                            <TextBlock
                                x:Name="TokenTextBlock"
                                Grid.Column="1"
                                Height="18"
                                Padding="0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Bottom"
                                Foreground="{StaticResource ContentForeground}"
                                Margin="4,1,0,0"
                                Text="{TemplateBinding Text}" />
                            <Button
                                x:Name="TokenCloseButton"
                                IsTabStop="False" 
                                Grid.Column="2"
                                Width="16"
                                Height="16"
                                Style="{StaticResource WPFGlyphWindows11ButtonStyle}"
                                Margin="4,0,1,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                HorizontalContentAlignment="Center"
                                VerticalContentAlignment="Center"
                                CommandParameter="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}" >
                                <Path x:Name="TokenButtonContent"
                                    Width="7"
                                    Height="7"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="M1.4139423,0 L7.0029922,5.5845888 12.592018,0 14.006015,1.4149939 8.4180527,6.9985202 14.006,12.582007 12.591996,13.997001 7.0030056,8.4124444 1.4140122,13.997001 1.5026823E-05,12.582007 5.5879484,6.9985092 0,1.4149939 z"
                                    Stretch="Fill"
                                                    UseLayoutRounding="True"
                                Fill="{Binding Path=Foreground,RelativeSource={RelativeSource AncestorType={x:Type Button}}}"/>
                            </Button>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="TokenBorder" Property="MinHeight" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="TokenBorder" Property="MinWidth" Value="{StaticResource TouchMode.MinWidth}" />
                            <Setter TargetName="TokenCloseButton" Property="MinWidth" Value="16" />
                            <Setter TargetName="TokenCloseButton" Property="MinWidth" Value="16" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfTextBoxExtTokenItemStyle}" TargetType="Input:TokenItem" />
</ResourceDictionary>
