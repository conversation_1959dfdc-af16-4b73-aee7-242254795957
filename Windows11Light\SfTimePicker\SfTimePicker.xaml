<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Input="clr-namespace:Syncfusion.Windows.Controls.Input;assembly=Syncfusion.SfInput.WPF"
    xmlns:Microsoft_Windows_Themes="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:Sync_Resource="clr-namespace:Syncfusion.Windows.Controls.Input.Resources;assembly=Syncfusion.SfInput.WPF"
    xmlns:converter="clr-namespace:Syncfusion.Windows.Converters;assembly=Syncfusion.SfInput.WPF"
    xmlns:shared_Brushconverter="clr-namespace:Syncfusion.Windows.Converters;assembly=Syncfusion.Shared.WPF"
    xmlns:input_controls="clr-namespace:Syncfusion.Windows.Controls;assembly=Syncfusion.SfInput.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:Syncfusion.Windows.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Primitives;assembly=Syncfusion.SfInput.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphRepeatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/RepeatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/SfTimeSelector/SfTimeSelector.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Menu.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBox.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="TimePicker.MouseOver.Border" Color="DarkGray" />

    <SolidColorBrush x:Key="TimePicker.Focused.Border" Color="DarkGray" />

    <LinearGradientBrush x:Key="TimePickerBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="TimeSpanEditBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="TimeSpanEditBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <BooleanToVisibilityConverter x:Key="BooleanVisibilityConverter" />

    <converter:InverseBooleanToVisibilityConverter x:Key="InverseBooleanVisibilityConverter" />

    <shared_Brushconverter:BrushToColorConverter x:Key="BrushConverter" />

    <Input:NameToScopeConverter x:Key="NameToScope" />

    <Style
        x:Key="SyncfusionSfTimePickerDropDownButtonStyle"
        BasedOn="{StaticResource WPFGlyphRepeatButtonStyle}"
        TargetType="{x:Type RepeatButton}">
        <Setter Property="ContentTemplate">
            <Setter.Value>
                <DataTemplate>
                    <Path
                        x:Name="path"
                        Width="12"
                        Height="12"
                        Margin="0,0,0,0"
                        Stroke="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                        StrokeThickness="1"
                        RenderTransformOrigin="0.5,0.5"
                        Stretch="Uniform" 
                        Data="M7 4V8H10M13 7C13 10.3137 10.3137 13 7 13C3.68629 13 1 10.3137 1 7C1 3.68629 3.68629 1 7 1C10.3137 1 13 3.68629 13 7Z"/>
                </DataTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfTimePickerStyle" TargetType="{x:Type Input:SfTimePicker}">
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="AccentBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="BorderBrush" Value="{StaticResource TimePickerBorderBrush}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="DropDownHeight" Value="320" />
        <Setter Property="FormatString" Value="hh:mm tt" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Height" Value="24" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="SelectorStyle" Value="{StaticResource SyncfusionSfTimeSelectorStyle}" />
        <Setter Property="SelectorItemHeight" Value="32"/>
        <Setter Property="SelectorItemWidth" Value="64"/>

        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate>
                    <Grid x:Name="TimePickerGrid">
                        <Border
                            x:Name="TimePickerBorder"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            SnapsToDevicePixels="True">
                            <Grid x:Name="TimePickerInnerGrid">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition x:Name="DropDownColumnDefinition" Width="auto" />
                                </Grid.ColumnDefinitions>
                                <Input:SfTextBoxExt
                                    x:Name="PART_TextBlock"
                                    Grid.Column="0"
                                    MinWidth="{TemplateBinding MinWidth}"
                                    MinHeight="{TemplateBinding MinHeight}"
                                    Margin="1,0"
                                    Padding="-7, 0, 0, 0"
                                    HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    AllowPointerEvents="True"
                                    Background="Transparent"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="0"
                                    CaretBrush="{StaticResource IconColor}"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    FontSize="{TemplateBinding FontSize}"
                                    FontStretch="{TemplateBinding FontStretch}"
                                    FontStyle="{TemplateBinding FontStyle}"
                                    FontWeight="{TemplateBinding FontWeight}"
                                    Foreground="{TemplateBinding Foreground}"
                                    VerticalAlignment="Center"
                                    IsReadOnly="True"
                                    IsTabStop="True"
                                    SelectionBrush="{StaticResource PrimaryBackground}"
                                    Text="{Binding Value, Mode=OneWay, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                    Visibility="{Binding AllowInlineEditing, Converter={StaticResource InverseBooleanVisibilityConverter}, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                    Watermark="{Binding Watermark, RelativeSource={RelativeSource TemplatedParent}}"
                                    WatermarkTemplate="{Binding WatermarkTemplate, RelativeSource={RelativeSource TemplatedParent}}" />
                                <Input:SfTextBoxExt
                                    x:Name="PART_TextBoxExt"
                                    Grid.Column="0"
                                    MinWidth="{TemplateBinding MinWidth}"
                                    MinHeight="{TemplateBinding MinHeight}"
                                    Margin="1,0"
                                    Padding="-7, 0, 0, 0"
                                    HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    VerticalAlignment="Center"
                                    AllowPointerEvents="True"
                                    Background="Transparent"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="0"
                                    CaretBrush="{StaticResource IconColor}"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    FontSize="{TemplateBinding FontSize}"
                                    FontStretch="{TemplateBinding FontStretch}"
                                    FontStyle="{TemplateBinding FontStyle}"
                                    FontWeight="{TemplateBinding FontWeight}"
                                    Foreground="{TemplateBinding Foreground}"
                                    InputScope="{Binding InputScope, RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource NameToScope}}"
                                    IsReadOnly="true"
                                    IsTabStop="{Binding AllowInlineEditing, RelativeSource={RelativeSource TemplatedParent}}"
                                    SelectionBrush="{StaticResource PrimaryBackground}"
                                    Text="{Binding Value, Mode=OneWay, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                    Visibility="{Binding AllowInlineEditing, Converter={StaticResource BooleanVisibilityConverter}, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                    Watermark="{Binding Watermark, RelativeSource={RelativeSource TemplatedParent}}"
                                    WatermarkTemplate="{Binding WatermarkTemplate, RelativeSource={RelativeSource TemplatedParent}}" />
                                <RepeatButton
                                    x:Name="PART_DropDownButton"
                                    Grid.Column="1"
                                    Width="NaN"
                                    Padding="5 0 5 0"
                                    Margin="4, 2, 4, 2"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    Background="Transparent"
                                    IsTabStop="False"
                                    Style="{StaticResource WPFRepeatButtonStyle}"
                                    Visibility="{Binding ShowDropDownButton, Converter={StaticResource BooleanVisibilityConverter}, RelativeSource={RelativeSource Mode=TemplatedParent}}" >
                                    <RepeatButton.Content>
                                        <Path
                                            x:Name="path"
                                            Width="12"
                                            Height="12"
                                            Margin="0,0,0,0"
                                            Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                            StrokeThickness="1"
                                            RenderTransformOrigin="0.5,0.5"
                                            Stretch="Uniform" 
                                            Data="M0 5.625C0 5.10547 0.0664062 4.60742 0.199219 4.13086C0.332031 3.65039 0.519531 3.20117 0.761719 2.7832C1.00781 2.36523 1.30078 1.98633 1.64062 1.64648C1.98438 1.30273 2.36523 1.00977 2.7832 0.767578C3.20117 0.521484 3.64844 0.332031 4.125 0.199219C4.60547 0.0664062 5.10547 0 5.625 0C6.14062 0 6.63672 0.0683594 7.11328 0.205078C7.59375 0.337891 8.04297 0.527344 8.46094 0.773438C8.87891 1.01562 9.25781 1.30859 9.59766 1.65234C9.94141 1.99219 10.2344 2.37109 10.4766 2.78906C10.7227 3.20703 10.9121 3.65625 11.0449 4.13672C11.1816 4.61328 11.25 5.10938 11.25 5.625C11.25 6.14453 11.1816 6.64453 11.0449 7.125C10.9121 7.60156 10.7227 8.04883 10.4766 8.4668C10.2344 8.88086 9.94141 9.25977 9.59766 9.60352C9.25781 9.94336 8.87891 10.2363 8.46094 10.4824C8.04688 10.7246 7.59961 10.9141 7.11914 11.0508C6.63867 11.1836 6.14062 11.25 5.625 11.25C5.10547 11.25 4.60547 11.1836 4.125 11.0508C3.64453 10.918 3.19531 10.7305 2.77734 10.4883C2.36328 10.2422 1.98438 9.94922 1.64062 9.60938C1.30078 9.26562 1.00781 8.88672 0.761719 8.47266C0.519531 8.05469 0.332031 7.60547 0.199219 7.125C0.0664062 6.64453 0 6.14453 0 5.625ZM10.5 5.625C10.5 5.17578 10.4414 4.74414 10.3242 4.33008C10.2109 3.91211 10.0469 3.52344 9.83203 3.16406C9.62109 2.80078 9.36719 2.47266 9.07031 2.17969C8.77734 1.88281 8.44922 1.62891 8.08594 1.41797C7.72656 1.20312 7.33789 1.03906 6.91992 0.925781C6.50586 0.808594 6.07422 0.75 5.625 0.75C5.17578 0.75 4.74219 0.808594 4.32422 0.925781C3.91016 1.03906 3.52148 1.20312 3.1582 1.41797C2.79883 1.62891 2.4707 1.88281 2.17383 2.17969C1.88086 2.47266 1.62695 2.80078 1.41211 3.16406C1.20117 3.52344 1.03711 3.91211 0.919922 4.33008C0.806641 4.74414 0.75 5.17578 0.75 5.625C0.75 6.07422 0.806641 6.50781 0.919922 6.92578C1.03711 7.33984 1.20117 7.72852 1.41211 8.0918C1.62305 8.45117 1.87695 8.7793 2.17383 9.07617C2.4707 9.37305 2.79883 9.62695 3.1582 9.83789C3.52148 10.0488 3.91016 10.2129 4.32422 10.3301C4.74219 10.4434 5.17578 10.5 5.625 10.5C6.07422 10.5 6.50586 10.4434 6.91992 10.3301C7.33789 10.2129 7.72656 10.0488 8.08594 9.83789C8.44922 9.62695 8.7793 9.37305 9.07617 9.07617C9.37305 8.7793 9.62695 8.45117 9.83789 8.0918C10.0488 7.72852 10.2109 7.33984 10.3242 6.92578C10.4414 6.50781 10.5 6.07422 10.5 5.625ZM5.625 6C5.52344 6 5.43555 5.96289 5.36133 5.88867C5.28711 5.81445 5.25 5.72656 5.25 5.625V2.625C5.25 2.52344 5.28711 2.43555 5.36133 2.36133C5.43555 2.28711 5.52344 2.25 5.625 2.25C5.72656 2.25 5.81445 2.28711 5.88867 2.36133C5.96289 2.43555 6 2.52344 6 2.625V5.25H7.875C7.97656 5.25 8.06445 5.28711 8.13867 5.36133C8.21289 5.43555 8.25 5.52344 8.25 5.625C8.25 5.72656 8.21289 5.81445 8.13867 5.88867C8.06445 5.96289 7.97656 6 7.875 6H5.625Z"/>
                                    </RepeatButton.Content>
                                </RepeatButton>
                            </Grid>
                        </Border>
                        <Popup
                            x:Name="PART_DropDown"
                            AllowsTransparency="True"
                            FlowDirection="LeftToRight"
                            Placement="Bottom"
                            SnapsToDevicePixels="True"
                            StaysOpen="False">
                            <Border
                                Margin="16, 1, 16, 16"
                                BorderBrush="{StaticResource Border}"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                Effect="{StaticResource Default.ShadowDepth4}"
                                CornerRadius="4">
                                <Border.RenderTransform>
                                    <TranslateTransform/>
                                </Border.RenderTransform>
                                <!--<Border.Triggers>
                                    <EventTrigger RoutedEvent="Border.Loaded">
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimationUsingKeyFrames
                                                    BeginTime="00:00:00"
                                                    Storyboard.TargetProperty="(RenderTransform).(TranslateTransform.Y)">
                                                    <SplineDoubleKeyFrame
                                                       KeyTime="00:00:00.1"
                                                       Value="-800" />
                                                    <SplineDoubleKeyFrame
                                                       KeyTime="00:00:0.2"
                                                       Value="0"
                                                       KeySpline="0.0, 0.1, 0.0, 1.0" />
                                                </DoubleAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger>
                                </Border.Triggers>-->
                                <Grid x:Name="PART_TimePickerPage" Height="{Binding DropDownHeight, RelativeSource={RelativeSource Mode=TemplatedParent}}">
                                    <Input:SfTimeSelector
                                        x:Name="PART_TimeSelector"
                                        Height="{Binding DropDownHeight, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        AccentBrush="{Binding AccentBrush, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Background="{StaticResource PopupBackground}"
                                        BorderBrush="{StaticResource Border}"
                                        BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                        FlowDirection="{TemplateBinding FlowDirection}"
                                        Header="{x:Null}"
                                        IsEnabled="{Binding IsEnabled, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        IsTabStop="{Binding IsTabStop, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        SelectedTime="{Binding Value, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Style="{Binding SelectorStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" />
                                </Grid>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="Width" TargetName="DropDownColumnDefinition" Value="Auto" />
                            <Setter TargetName="PART_DropDownButton" Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="MinWidth" TargetName="PART_DropDownButton" Value="{StaticResource TouchMode.MinSize}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="PART_TextBlock" Property="Background" Value="Transparent" />
                            <Setter TargetName="TimePickerBorder" Property="BorderBrush" Value="{StaticResource TimeSpanEditBorderBrushHovered}" />
                            <Setter TargetName="PART_TextBlock" Property="Foreground" Value="{StaticResource ContentForeground}" />
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt5}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="TimePickerBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="PART_TextBlock" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="TimePickerBorder" Property="Background" Value="{StaticResource ContentBackgroundAlt6}"/>
                            <Setter TargetName="PART_TextBlock" Property="Background" Value="Transparent"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="PART_TextBlock" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="TimePickerBorder" Property="BorderBrush" Value="{StaticResource TimeSpanEditBorderBrushFocused}" />
                            <Setter TargetName="PART_TextBlock" Property="Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocusWithin" Value="True">
                            <Setter TargetName="TimePickerBorder" Property="BorderBrush" Value="{StaticResource TimeSpanEditBorderBrushFocused}" />
                            <Setter TargetName="TimePickerBorder" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}" />
                            <Setter TargetName="PART_DropDownButton" Property="Padding" Value="5 0 5 -1"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSfTimePickerStyle}" TargetType="{x:Type Input:SfTimePicker}" />
</ResourceDictionary>
