# 商用空调监控调试软件 - 依赖项清单

## 文档信息
- **文档版本**: 1.0
- **创建日期**: 2024-12-09
- **最后更新**: 2024-12-09
- **文档状态**: 正式版

## 1. 运行时依赖

### 1.1 .NET 运行时
- **.NET 8.0 Runtime**: 8.0.0 或更高版本
- **Windows Desktop Runtime**: 8.0.0 或更高版本
- **目标框架**: net8.0-windows

### 1.2 操作系统要求
- **Windows 10**: 版本1903 (Build 18362) 或更高
- **Windows 11**: 所有版本
- **架构**: x64 (64位)

## 2. NuGet 包依赖

### 2.1 Syncfusion WPF Controls (版本 29.2.9)
```xml
<!-- 数据网格控件 -->
<PackageReference Include="Syncfusion.SfDataGrid.WPF" Version="29.2.9" />

<!-- 仪表盘控件 -->
<PackageReference Include="Syncfusion.SfGauge.WPF" Version="29.2.9" />

<!-- 调度器控件 -->
<PackageReference Include="Syncfusion.SfScheduler.WPF" Version="29.2.9" />

<!-- Windows 11 深色主题 -->
<PackageReference Include="Syncfusion.Themes.Windows11Dark.WPF" Version="29.2.9" />

<!-- Windows 11 浅色主题 -->
<PackageReference Include="Syncfusion.Themes.Windows11Light.WPF" Version="29.2.9" />
```

**许可证要求**: 需要有效的Syncfusion商业许可证
**用途**: 提供现代化的WPF控件和Windows 11主题

### 2.2 OxyPlot.WPF (版本 2.2.0)
```xml
<!-- 图表控件 -->
<PackageReference Include="OxyPlot.WPF" Version="2.2.0" />
```

**许可证要求**: MIT开源许可证，免费使用
**用途**: 提供高性能的图表控件，支持实时曲线和历史数据可视化

### 2.3 数据库和ORM (版本 9.0.5)
```xml
<!-- Entity Framework Core SQLite -->
<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.5" />

<!-- EF Core 设计时工具 -->
<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5" />

<!-- SQLite 核心库 -->
<PackageReference Include="Microsoft.Data.Sqlite.Core" Version="9.0.5" />
```

**用途**: 数据持久化和对象关系映射

### 2.4 依赖注入和配置 (版本 9.0.5)
```xml
<!-- 依赖注入容器 -->
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />

<!-- 配置系统 -->
<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />

<!-- JSON 配置提供程序 -->
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />

<!-- 日志抽象 -->
<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
```

**用途**: 应用程序配置管理和依赖注入

### 2.5 MVVM 框架 (版本 8.4.0)
```xml
<!-- Community Toolkit MVVM -->
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
```

**用途**: 现代化的MVVM框架，提供命令、属性通知等功能

### 2.6 日志框架 (版本 4.2.0+)
```xml
<!-- Serilog 核心 -->
<PackageReference Include="Serilog" Version="4.2.0" />

<!-- 文件输出 -->
<PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />

<!-- Microsoft 日志扩展 -->
<PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
```

**用途**: 结构化日志记录和文件输出

### 2.7 串口通信 (版本 9.0.5)
```xml
<!-- 串口通信 -->
<PackageReference Include="System.IO.Ports" Version="9.0.5" />
```

**用途**: RS485串口通信功能

### 2.8 工具库
```xml
<!-- 对象映射 -->
<PackageReference Include="AutoMapper" Version="14.0.0" />

<!-- 数据验证 -->
<PackageReference Include="FluentValidation" Version="12.0.0" />

<!-- 重试策略 -->
<PackageReference Include="Polly" Version="8.5.2" />

<!-- JSON 序列化 -->
<PackageReference Include="System.Text.Json" Version="9.0.5" />
```

**用途**: 通用工具和辅助功能

### 2.9 测试框架
```xml
<!-- 单元测试框架 -->
<PackageReference Include="NUnit" Version="4.2.2" />

<!-- 测试适配器 -->
<PackageReference Include="NUnit3TestAdapter" Version="4.6.0" />

<!-- 测试 SDK -->
<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />

<!-- 模拟框架 -->
<PackageReference Include="Moq" Version="4.20.72" />

<!-- 内存数据库 -->
<PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.5" />

<!-- 代码覆盖率 -->
<PackageReference Include="coverlet.collector" Version="6.0.2" />
```

**用途**: 单元测试和集成测试

## 3. 开发工具依赖

### 3.1 必需的开发工具
- **Visual Studio 2022**: 版本17.8或更高
  - 工作负载: .NET 桌面开发
  - 工作负载: ASP.NET 和 Web 开发 (可选)
- **.NET 8.0 SDK**: 8.0.100 或更高版本
- **Git**: 版本控制工具

### 3.2 推荐的开发工具
- **Visual Studio Code**: 轻量级代码编辑器
- **Syncfusion Essential Studio**: Syncfusion控件设计器
- **SQLite Browser**: SQLite数据库查看工具
- **Postman**: API测试工具 (如果需要)

### 3.3 可选的开发工具
- **ReSharper**: 代码分析和重构工具
- **dotMemory**: 内存分析工具
- **dotTrace**: 性能分析工具
- **NuGet Package Explorer**: NuGet包管理工具

## 4. 硬件依赖

### 4.1 最低硬件要求
- **处理器**: Intel/AMD x64 双核处理器
- **内存**: 4GB RAM
- **硬盘**: 500MB 可用空间
- **显示器**: 1024x768 分辨率
- **串口**: RS485转USB适配器

### 4.2 推荐硬件配置
- **处理器**: Intel/AMD x64 四核处理器
- **内存**: 8GB RAM 或更高
- **硬盘**: 2GB 可用空间 (SSD推荐)
- **显示器**: 1920x1080 分辨率或更高
- **串口**: 高质量RS485转USB适配器

## 5. 外部服务依赖

### 5.1 无外部服务依赖
本软件设计为完全离线运行，不依赖任何外部服务：
- 无需互联网连接
- 无需云服务
- 无需在线激活
- 无需远程数据库

### 5.2 本地服务依赖
- **SQLite数据库**: 本地文件数据库
- **文件系统**: 配置文件和日志文件存储
- **串口服务**: Windows串口驱动

## 6. 许可证依赖

### 6.1 商业许可证
- **Syncfusion Essential Studio**: 需要有效的商业许可证
  - 开发许可证: 用于开发环境
  - 部署许可证: 用于生产部署
  - 许可证文件: 需要在应用程序中注册

### 6.2 开源许可证
以下组件使用开源许可证，无需额外费用：
- **.NET 8.0**: MIT许可证
- **Entity Framework Core**: MIT许可证
- **CommunityToolkit.Mvvm**: MIT许可证
- **Serilog**: Apache 2.0许可证
- **AutoMapper**: MIT许可证
- **FluentValidation**: Apache 2.0许可证
- **Polly**: BSD 3-Clause许可证
- **NUnit**: MIT许可证

## 7. 版本兼容性

### 7.1 向前兼容性
- 支持从旧版本升级到新版本
- 配置文件自动迁移
- 数据库架构自动升级

### 7.2 向后兼容性
- 新版本生成的数据文件可能不兼容旧版本
- 建议在升级前备份数据
- 提供数据导出功能确保数据可移植性

### 7.3 依赖版本锁定
为确保稳定性，所有依赖项版本都已锁定：
- 主版本号锁定，防止破坏性更改
- 定期评估和更新依赖项
- 安全更新优先处理

## 8. 安装和部署依赖

### 8.1 安装包依赖
- **WiX Toolset**: 用于创建MSI安装包
- **Inno Setup**: 备选安装包制作工具
- **代码签名证书**: 用于应用程序签名

### 8.2 部署环境依赖
- **Windows Installer**: 用于MSI包安装
- **Visual C++ Redistributable**: 某些组件可能需要
- **串口驱动**: RS485转USB适配器驱动

### 8.3 运行时检查
应用程序启动时会检查以下依赖项：
- .NET 8.0 Runtime 可用性
- 必需的DLL文件完整性
- 配置文件有效性
- 数据库连接可用性
- 串口设备可用性

## 9. 故障排除

### 9.1 常见依赖问题
- **.NET Runtime 缺失**: 提示用户安装.NET 8.0 Runtime
- **Syncfusion许可证**: 检查许可证文件和注册状态
- **串口驱动**: 检查设备管理器中的串口设备
- **权限问题**: 确保应用程序有足够的文件系统权限

### 9.2 依赖检查工具
- **系统信息检查**: 启动时自动检查系统环境
- **依赖项验证**: 验证所有必需的DLL文件
- **配置验证**: 检查配置文件格式和内容
- **硬件检测**: 检测串口设备和驱动状态

## 10. 更新策略

### 10.1 依赖项更新原则
- **安全更新**: 优先处理安全漏洞修复
- **稳定性优先**: 避免频繁的主版本更新
- **测试验证**: 所有更新都需要完整测试
- **向后兼容**: 确保更新不破坏现有功能

### 10.2 更新时间表
- **季度评估**: 每季度评估依赖项更新
- **安全补丁**: 发现安全问题立即更新
- **主版本升级**: 年度主版本升级计划
- **LTS版本**: 优先选择长期支持版本

### 10.3 更新流程
1. **依赖项分析**: 分析新版本的变更内容
2. **兼容性测试**: 测试新版本的兼容性
3. **回归测试**: 执行完整的回归测试
4. **文档更新**: 更新相关文档和说明
5. **发布部署**: 发布包含更新的新版本
