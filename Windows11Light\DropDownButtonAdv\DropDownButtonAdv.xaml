<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	
    xmlns:local="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:skin="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
   >

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="DropDownButtonAdv.Static.Border" Color="#FAFAFA" />
    
    <local:SizeToDoubleConverter x:Key="sizeToDoubleConverter"/>

    <LinearGradientBrush x:Key="DropDownButtonAdvBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource SecondaryBorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource SecondaryBorder.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DropDownButtonAdvBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource SecondaryBorderHoveredGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource SecondaryBorderHovered.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <DataTemplate x:Key="LargeIconTemplate">
        <Image x:Name="LargeImage"
            Width="{Binding Width, RelativeSource={RelativeSource TemplatedParent}}"
            Height="{Binding Height, RelativeSource={RelativeSource TemplatedParent}}"
            Source="{Binding Tag, RelativeSource={RelativeSource TemplatedParent}}" />
    </DataTemplate>

    <DataTemplate x:Key="SmallIconTemplate">
        <Image x:Name="smallimage"
            Width="{Binding Width, RelativeSource={RelativeSource TemplatedParent}}"
            Height="{Binding Height, RelativeSource={RelativeSource TemplatedParent}}"
            Source="{Binding Tag, RelativeSource={RelativeSource TemplatedParent}}" />
    </DataTemplate>

    <ControlTemplate x:Key="SyncfusionDropDownButtonAdvControlTemplate" TargetType="{x:Type local:DropDownButtonAdv}">
        <Grid>
            <Border
                Name="ItemBorder1"
                MinHeight="{StaticResource Windows11Light.MinHeight}"
                Background="{TemplateBinding Background}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}"
                CornerRadius="4"
                
                SnapsToDevicePixels="True"
                >
                <StackPanel
                    Margin="{TemplateBinding Padding}"
                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                    <ContentPresenter x:Name="LargeIconContent" ContentTemplate="{TemplateBinding IconTemplate}" Content="{Binding }" Margin="2"
                                                              MinWidth="{TemplateBinding MinWidth}"
                                                              MinHeight="{TemplateBinding MinHeight}"
                                                              ContentTemplateSelector="{TemplateBinding IconTemplateSelector}"/>
                    <TextBlock
                        x:Name="PART_TextAreaLarge"
                        Margin="2,0,4,0"
						Text="{TemplateBinding Label}"
                        TextBlock.FontFamily="{TemplateBinding FontFamily}"
                        TextBlock.FontSize="{TemplateBinding FontSize}"
                        TextBlock.FontWeight="{TemplateBinding FontWeight}"
                        HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                        VerticalAlignment="{TemplateBinding VerticalAlignment}" >
                    </TextBlock>
                    <Path
                                    x:Name="Arrow"
                                    Grid.Column="1"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Width="10"
                                    Height="10"
                                    Fill="{StaticResource IconColor}">
                        <Path.Data>
                            <PathGeometry>M5.24264 7.77817L9.13173 3.88908L9.83884 4.59619L5.24264 9.19239L0.646448 4.59619L1.35356 3.88908L5.24264 7.77817Z</PathGeometry>
                        </Path.Data>
                        <Path.Margin>
                            <Thickness>0</Thickness>
                        </Path.Margin>
                        <Path.LayoutTransform>
                            <RotateTransform Angle="0"/>
                        </Path.LayoutTransform>
                    </Path>
                </StackPanel>
            </Border>
            <Popup
                x:Name="PART_DropDown"
                AllowsTransparency="True"
                Placement="Bottom"
                StaysOpen="{TemplateBinding StaysOpen}">
                <Border
                    MinWidth="{TemplateBinding MinWidth}"
                    MinHeight="{TemplateBinding MinHeight}"
                    Background="{StaticResource PopupBackground}"
                    BorderBrush="{StaticResource BorderAlt}"
                    CornerRadius="0"
                    BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                    Effect="{StaticResource Default.ShadowDepth4}">
                    <Border.Margin>
                        <Thickness>16,1,16,16</Thickness>
                    </Border.Margin>
                    <ContentPresenter/>
                    <Border.RenderTransform>
                        <TranslateTransform/>
                    </Border.RenderTransform>
                    <Border.Triggers>
                        <EventTrigger RoutedEvent="Border.Loaded">
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimationUsingKeyFrames
                                                    BeginTime="00:00:00"
                                                    Storyboard.TargetProperty="(RenderTransform).(TranslateTransform.Y)">
                                        <SplineDoubleKeyFrame
                                                       KeyTime="00:00:00.01"
                                                       Value="-800" />
                                        <SplineDoubleKeyFrame
                                                       KeyTime="00:00:0.2"
                                                       Value="0"
                                                       KeySpline="0.0, 0.1, 0.0, 1.0" />
                                    </DoubleAnimationUsingKeyFrames>
                                    <DoubleAnimationUsingKeyFrames
                                                        Storyboard.TargetProperty="(Effect).Opacity">
                                        <SplineDoubleKeyFrame
                                                        KeyTime="00:00:00.01"
                                                        Value="0" />
                                        <SplineDoubleKeyFrame
                                                        KeyTime="00:00:0.3"
                                                        Value="0" />
                                        <SplineDoubleKeyFrame
                                                        KeyTime="00:00:0.4"
                                                        Value="0.17" />
                                    </DoubleAnimationUsingKeyFrames>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Border.Triggers>
                </Border>
            </Popup>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                <Setter TargetName="PART_TextAreaLarge" Property="TextBlock.Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderThickness" Value="2"/>
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColor}" />
            </Trigger>
            <Trigger Property="IsMultiLine" Value="True">
                <Setter Property="TextBlock.TextWrapping" Value="Wrap" TargetName="PART_TextAreaLarge"/>
            </Trigger>
            <Trigger Property="IsMultiLine" Value="False">
                <Setter Property="TextBlock.TextWrapping" Value="NoWrap" TargetName="PART_TextAreaLarge"/>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                <Setter TargetName="PART_TextAreaLarge" Property="TextBlock.Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorHovered}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                <Setter TargetName="PART_TextAreaLarge" Property="TextBlock.Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}"/>
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="LargeIconContent" Property="Opacity" Value="0.38" />
                <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundDisabled}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderDisabled}" />
                <Setter TargetName="PART_TextAreaLarge" Property="TextBlock.Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IconTemplate" Value="{x:Null}"/>
                    <Condition Property="IconTemplateSelector" Value="{x:Null}"/>
                </MultiTrigger.Conditions>
                <Setter Property="ContentTemplate" TargetName="LargeIconContent" Value="{StaticResource LargeIconTemplate}"/>
                <Setter Property="Tag" TargetName="LargeIconContent" Value="{Binding LargeIcon, RelativeSource={RelativeSource TemplatedParent}}"/>
            </MultiTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SyncfusionDropDownButtonAdvStyle" TargetType="{x:Type local:DropDownButtonAdv}">
        <Setter Property="SmallIcon" Value="/Syncfusion.Shared.WPF;component//Controls/ButtonControls/Images/WordArt16.png" />
        <Setter Property="LargeIcon" Value="/Syncfusion.Shared.WPF;component//Controls/ButtonControls/Images/WordArt32.png" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Label" Value="Dropdown Button" />
        <Setter Property="Background" Value="{StaticResource SecondaryBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource DropDownButtonAdvBorderBrush}" />
        <Setter Property="Foreground" Value="{StaticResource SecondaryForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:DropDownButtonAdv}">
                    <Grid>
                        <Border
                            Name="ItemBorder"
                            MinHeight="{StaticResource Windows11Light.MinHeight}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            
                            SnapsToDevicePixels="True"
                            >
                            <Grid Margin="{TemplateBinding Padding}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition x:Name="dropDownColumn" Width="30" />
                                </Grid.ColumnDefinitions>
                                <Grid HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <ContentPresenter x:Name="SmallIconContent" ContentTemplate="{TemplateBinding IconTemplate}" Content="{Binding }" Margin="2"
                                                              ContentTemplateSelector="{TemplateBinding IconTemplateSelector}"/>
                                    <TextBlock
                                        x:Name="PART_Text"
                                        Grid.Column="1"
                                        Margin="2,0,4,0"
                                        HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        FontFamily="{TemplateBinding FontFamily}"
                                        FontSize="{TemplateBinding FontSize}"
                                        Foreground="{TemplateBinding Foreground}"
                                        FontWeight="{TemplateBinding FontWeight}"
                                        Text="{TemplateBinding Label}" />
                                </Grid>
                                <Path
                                    x:Name="Arrow"
                                    Grid.Column="1"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Width="10"
                                    Height="10"
                                    Fill="{StaticResource IconColor}">
                                    <Path.Data>
                                        <PathGeometry>M5.24264 7.77817L9.13173 3.88908L9.83884 4.59619L5.24264 9.19239L0.646448 4.59619L1.35356 3.88908L5.24264 7.77817Z</PathGeometry>
                                    </Path.Data>
                                    <Path.Margin>
                                        <Thickness>0</Thickness>
                                    </Path.Margin>
                                    <Path.LayoutTransform>
                                        <RotateTransform Angle="0"/>
                                    </Path.LayoutTransform>
                                </Path>
                            </Grid>
                        </Border>

                        <Popup
                            x:Name="PART_DropDown"
                            AllowsTransparency="True"
                            Placement="Bottom"
                            StaysOpen="{TemplateBinding StaysOpen}">
                            <Border
                                MinWidth="{TemplateBinding MinWidth}"
                                MinHeight="{TemplateBinding MinHeight}"
                                Background="{StaticResource PopupBackground}"
                                BorderBrush="{StaticResource BorderAlt}"
                                CornerRadius="4"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                Effect="{StaticResource Default.ShadowDepth4}">
                                <Border.Margin>
                                    <Thickness>16,1,16,16</Thickness>
                                </Border.Margin>
                                <ContentPresenter/>
                                <Border.RenderTransform>
                                    <TranslateTransform/>
                                </Border.RenderTransform>
                                <Border.Triggers>
                                    <EventTrigger RoutedEvent="Border.Loaded">
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimationUsingKeyFrames
                                                    BeginTime="00:00:00"
                                                    Storyboard.TargetProperty="(RenderTransform).(TranslateTransform.Y)">
                                                    <SplineDoubleKeyFrame
                                                       KeyTime="00:00:00.01"
                                                       Value="-800" />
                                                    <SplineDoubleKeyFrame
                                                       KeyTime="00:00:0.2"
                                                       Value="0"
                                                       KeySpline="0.0, 0.1, 0.0, 1.0" />
                                                </DoubleAnimationUsingKeyFrames>
                                                <DoubleAnimationUsingKeyFrames
                                                        Storyboard.TargetProperty="(Effect).Opacity">
                                                    <SplineDoubleKeyFrame
                                                        KeyTime="00:00:00.01"
                                                        Value="0" />
                                                    <SplineDoubleKeyFrame
                                                        KeyTime="00:00:0.3"
                                                        Value="0" />
                                                    <SplineDoubleKeyFrame
                                                        KeyTime="00:00:0.4"
                                                        Value="0.17" />
                                                </DoubleAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger>
                                </Border.Triggers>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="SizeMode" Value="Small">
                            <Setter TargetName="dropDownColumn" Property="Width" Value="Auto"/>
                            <Setter TargetName="Arrow" Property="Margin" Value="2,1,2,1"/>
                            <Setter TargetName="PART_Text" Property="Visibility" Value="Collapsed"/>
                        </Trigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderHovered}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderThickness" Value="0" />
                            <Setter TargetName="PART_Text" Property="TextBlock.Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColor}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource DropDownButtonAdvBorderBrushHovered}" />
                            <Setter TargetName="PART_Text" Property="TextBlock.Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                            <Setter TargetName="PART_Text" Property="TextBlock.Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="SmallIconContent" Property="Border.Opacity" Value="0.38" />
                            <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundDisabled}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderDisabled}" />
                            <Setter TargetName="PART_Text" Property="TextBlock.Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                            <Setter TargetName="Arrow" Property="Path.Fill" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IconTemplate" Value="{x:Null}"/>
                                <Condition Property="IconTemplateSelector" Value="{x:Null}"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="ContentTemplate" TargetName="SmallIconContent" Value="{StaticResource SmallIconTemplate}"/>
                            <Setter Property="Tag" TargetName="SmallIconContent" Value="{Binding SmallIcon, RelativeSource={RelativeSource TemplatedParent}}"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="SizeMode" Value="Large">
                <Setter Property="Template" Value="{StaticResource SyncfusionDropDownButtonAdvControlTemplate}" />
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionDropDownButtonAdvStyle}" TargetType="{x:Type local:DropDownButtonAdv}" />

    <Style x:Key="SyncfusionDropDownMenuGroupStyle" TargetType="{x:Type local:DropDownMenuGroup}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource SecondaryForeground}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:DropDownMenuGroup}">
                 <Border  Padding="0 4 0 4" Background="{TemplateBinding Background}" CornerRadius="4">
                    <Grid Name="gri">
                        <Grid.Resources>
                            <local:TextToVisibilityConverter x:Key="TextConverter" />
                            <BooleanToVisibilityConverter x:Key="BoolConverter" />
                            <local:VisibilityToThicknessConverter x:Key="ThicknessConverter" />
                        </Grid.Resources>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Border
                            x:Name="PART_Header"
                            Grid.Row="0"
                            Height="20"
                            HorizontalAlignment="Stretch"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="0"
                            Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Header, Converter={StaticResource TextConverter}}">
                            <ContentControl
                                x:Name="header"
                                Margin="5,0,0,0"
                                VerticalAlignment="Center"
                                HorizontalContentAlignment="Left"
                                Content="{TemplateBinding Header}"
                                FontWeight="Bold"
                                Foreground="{TemplateBinding Foreground}" />
                        </Border>
                        <Border Grid.Row="1">
                            <Grid>
                                <Border
                                    x:Name="IconTray"
                                    Width="23"
                                    HorizontalAlignment="Left"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness0010}"
                                    Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IconBarEnabled, Converter={StaticResource BoolConverter}}" />
                                <Border>
                                    <ScrollViewer
                                        x:Name="PART_ScrollViewer"
                                        BorderThickness="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=ScrollBarVisibility, Converter={StaticResource ThicknessConverter}}"
                                        KeyboardNavigation.DirectionalNavigation="Cycle"
                                        VerticalScrollBarVisibility="{TemplateBinding ScrollBarVisibility}">
                                        <ItemsPresenter />
                                    </ScrollViewer>
                                </Border>
                            </Grid>
                        </Border>
                        <Border Grid.Row="2">
                            <Grid>
                                <Border
                                    Width="23" x:Name="MoreitemBar"
                                    HorizontalAlignment="Left"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness0010}"
                                    Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IsMoreItemsIconTrayEnabled, Converter={StaticResource BoolConverter}}" />
                                <ItemsControl ItemsSource="{TemplateBinding MoreItems}" />
                            </Grid>
                        </Border>
                        <Border
                            Grid.Row="3"
                            Width="{Binding ElementName=PART_ResizeThumb, Path=ActualWidth}"
                            HorizontalAlignment="Stretch"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="0,1,0,0"
                            Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IsResizable, Converter={StaticResource BoolConverter}}">
                            <Grid>
                                <Border HorizontalAlignment="Center">
                                    <StackPanel Orientation="Horizontal">
                                        <Border
                                            Width="3"
                                            Height="3"
                                            Background="Transparent" />
                                        <Border
                                            Width="3"
                                            Height="3"
                                            Margin="-5,0,0,0"
                                            Background="{StaticResource IconColor}"
                                            SnapsToDevicePixels="True" />
                                        <Border
                                            Width="3"
                                            Height="3"
                                            Margin="3,0,0,0"
                                            Background="Transparent"
                                            CornerRadius="2" />
                                        <Border
                                            Width="3"
                                            Height="3"
                                            Margin="-5,0,0,0"
                                            Background="{StaticResource IconColor}"
                                            SnapsToDevicePixels="True" />
                                        <Border
                                            Width="3"
                                            Height="3"
                                            Margin="3,0,0,0"
                                            Background="Transparent"
                                            CornerRadius="2" />
                                        <Border
                                            Width="3"
                                            Height="3"
                                            Margin="-5,0,0,0"
                                            Background="{StaticResource IconColor}"
                                            SnapsToDevicePixels="True" />
                                    </StackPanel>
                                </Border>
                                <Thumb
                                    x:Name="PART_ResizeThumb"
                                    Cursor="SizeNS"
                                    Opacity="0" />
                            </Grid>
                        </Border>
                    </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="IconTray" Property="Width" Value="{StaticResource Windows11Light.IconPanelSize}" />
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionDropDownMenuGroupStyle}" TargetType="{x:Type local:DropDownMenuGroup}" />

    <Style x:Key="SyncfusionDropDownMenuItemStyle" TargetType="{x:Type local:DropDownMenuItem}">
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Background" Value="{StaticResource PopupBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="MinHeight" Value="24" />
        <Setter Property="Padding">
            <Setter.Value>
                <Thickness>4,2,4,2</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="Margin">
            <Setter.Value>
                <Thickness>4,2,4,2</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:DropDownMenuItem">
                    <Grid>
                        <Border x:Name="SelectionIndicator"
                                        HorizontalAlignment="Left"            
                                        CornerRadius="1.5"
                                        Height="12"
                                        Width="2"
                                        Visibility="Collapsed"     
                                        Background="{StaticResource PrimaryBackground}" />
                        <Border
                        Name="Bd"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                            <Grid
                            x:Name="DropDownTray"
                            Margin="0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition x:Name="Icontraywidth" Width="25" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition x:Name="SubItemPath" Width="20" />
                                </Grid.ColumnDefinitions>
                                <Grid.Resources>
                                    <BooleanToVisibilityConverter x:Key="BoolConverter" />
                                </Grid.Resources>
                                <Border Grid.Column="1">
                                    <ContentControl
                                    Name="content"
                                    Margin="0"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Center"
                                    Background="Transparent"
                                    Content="{TemplateBinding Header}"
                                    ContentTemplate="{TemplateBinding HeaderTemplate}"
                                    Foreground="{TemplateBinding Foreground}"
                                    IsTabStop="False" />
                                </Border>
                                <Border Grid.Column="2">
                                    <Path     
                                        Name="path"    
                                        Height="5" Width="3"
                                        Margin="0,0,7,0"
                                        HorizontalAlignment="Right"
                                        Stretch="Fill"
                                        Fill="{StaticResource IconColor}" 
										Stroke="{StaticResource IconColor}"
                                        Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=HasItems, Converter={StaticResource BoolConverter}}">
                                        <Path.Data>
                                            <PathGeometry>M129,173 L129,282 L246,222 z</PathGeometry>
                                        </Path.Data>
                                    </Path>
                                </Border>
                                <Border Margin="2">
                                    <ContentControl
                                    x:Name="Part_Icon"
                                    Width="{TemplateBinding IconSize, Converter= {StaticResource sizeToDoubleConverter}, ConverterParameter=width}"
                                    Height="{TemplateBinding IconSize, Converter= {StaticResource sizeToDoubleConverter}, ConverterParameter=height}" 
                                    Margin="1"
                                    BorderThickness="2"
                                    Content="{TemplateBinding Icon}"
                                    IsTabStop="False" />
                                </Border>
                                <Border
                                x:Name="PART_CheckedBorder"
                                Width="18"
                                Height="18"
                                Margin="2"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="1"
                                SnapsToDevicePixels="True"
                                Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IsChecked, Converter={StaticResource BoolConverter}}">
                                    <Path
                                    x:Name="CheckIcon"
                                    Width="10.5"
                                    Height="10"
                                    Data="M102.03442,598.79645 L105.22962,597.78918 L106.78825,600.42358 C106.78825,600.42358 108.51028,595.74304 110.21724,593.60419 C112.00967,591.35822 114.89314,591.42316 114.89314,591.42316 C114.89314,591.42316 112.67844,593.42645 111.93174,594.44464 C110.7449,596.06293 107.15683,604.13837 107.15683,604.13837 z"
                                    Fill="{TemplateBinding Foreground}"
                                    FlowDirection="LeftToRight"
                                    Stretch="Fill" />
                                </Border>
                                <Popup
                                x:Name="PART_DropDown"
                                Margin="2,0,0,0"
                                AllowsTransparency="True"
                                IsOpen="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IsMouseOver, Mode=OneWay}"
                                Placement="Right"
                                VerticalOffset="1"
                                Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=HasItems, Converter={StaticResource BoolConverter}}">
                                    <Grid x:Name="PART_poup">
                                        <Border
                                        Background="{StaticResource PopupBackground}"
                                        BorderBrush="{StaticResource BorderAlt}"
                                        BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                        CornerRadius="4"
                                        SnapsToDevicePixels="True">
                                            <ItemsPresenter Margin="2"/>
                                        </Border>
                                    </Grid>
                                </Popup>
                            </Grid>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="Width" TargetName="Icontraywidth" Value="32"/>
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="Bd" Property="Border.Background" Value="{StaticResource PopupHoveredBackground}" />
                            <Setter TargetName="Bd" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="content" Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
                            <Setter TargetName="path" Property="Fill" Value="{StaticResource IconColorHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
                            <Setter Property="Background" Value="{StaticResource PopupHoveredBackground}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Bd" Property="Border.Background" Value="{StaticResource PopupHoveredBackground}" />
                            <Setter TargetName="Bd" Property="Border.BorderBrush" Value="{StaticResource PopupHoveredBackground}" />
                            <Setter TargetName="content" Property="Background" Value="{StaticResource PopupHoveredBackground}" />
                            <Setter TargetName="content" Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
                            <Setter TargetName="path" Property="Fill" Value="{StaticResource IconColorHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
                            <Setter Property="Background" Value="{StaticResource PopupHoveredBackground}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsPressed" Value="True" />
                                <Condition Property="IsMouseOver" Value="True" />
                            </MultiTrigger.Conditions>
                            <MultiTrigger.Setters>
                                <Setter TargetName="Bd" Property="Border.Background" Value="{StaticResource PopupSelectedBackground}" />
                                <Setter TargetName="Bd" Property="Border.BorderBrush" Value="{StaticResource PopupSelectedBackground}" />
                                <Setter TargetName="content" Property="Foreground" Value="{StaticResource PopupSelectedForeground}" />
                                <Setter TargetName="path" Property="Fill" Value="{StaticResource IconColorSelected}" />
                                <Setter Property="Foreground" Value="{StaticResource PopupSelectedForeground}" />
                                <Setter Property="Background" Value="{StaticResource PopupSelectedBackground}" />
                                <Setter Property="Visibility" TargetName="SelectionIndicator" Value="Visible"/>
                            </MultiTrigger.Setters>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Bd" Property="Border.Background" Value="{StaticResource PopupDisabledBackground}" />
                            <Setter TargetName="Bd" Property="Border.BorderBrush" Value="Transparent" />
                            <Setter TargetName="content" Property="Foreground" Value="{StaticResource PopupDisabledForeground}" />
                            <Setter TargetName="path" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                            <Setter Property="Foreground" Value="{StaticResource PopupDisabledForeground}" />
                            <Setter Property="Background" Value="{StaticResource PopupDisabledBackground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionDropDownMenuItemStyle}" TargetType="{x:Type local:DropDownMenuItem}" />

</ResourceDictionary>
