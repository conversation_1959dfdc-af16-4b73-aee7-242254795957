<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:Sync_Shared_Resources="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:tools_shared="clr-namespace:Syncfusion.Windows.Tools;assembly=Syncfusion.Shared.WPF">
    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <FontWeight x:Key="CalendarItem.Header.TextButton.Static.FontWeight">Normal</FontWeight>

    <tools_shared:CultureToCalendarConverter x:Key="CultureToCalendarConverter" />
    <shared:BooleanToVisibilityConverterEx x:Key="Boolean_To_VisibilityConverter" />

    <!--  DayCellTemplate  -->
    <DataTemplate x:Key="SyncfusionDayCellTemplate" DataType="shared:DayCell">
        <TextBlock
            x:Name="DayCellText"
            Text="{Binding Day, Mode=OneWay}"
            TextBlock.FontFamily="{Binding Path=FontFamily, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.FontSize="{Binding Path=FontSize, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.FontStyle="{Binding Path=FontStyle, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.FontWeight="{Binding Path=FontWeight, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            HorizontalAlignment="Center"
            VerticalAlignment="Center">
            <TextBlock.Resources>
                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
            </TextBlock.Resources>
        </TextBlock>
    </DataTemplate>

    <!--  DayNameCellTemplate  -->
    <ControlTemplate x:Key="SyncfusionDayNameCellControlTemplate" TargetType="{x:Type shared:DayNameCell}">
        <Border
            x:Name="Border"
            Margin="2"
            Width="30"
            Height="30"
            Background="{TemplateBinding Background}"
            BorderBrush="{TemplateBinding Border.BorderBrush}"
            BorderThickness="{TemplateBinding Border.BorderThickness}"
            CornerRadius="{TemplateBinding Border.CornerRadius}"
            TextBlock.FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
            TextBlock.FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
            TextBlock.FontWeight="{StaticResource Windows11Dark.FontWeightMedium}"
            TextBlock.Foreground="{StaticResource ContentForeground}">
            <ContentPresenter
                x:Name="DayNameCellText"
                HorizontalAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                VerticalAlignment="{TemplateBinding Control.VerticalContentAlignment}">
                <ContentPresenter.Resources>
                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                </ContentPresenter.Resources>
            </ContentPresenter>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="TextBlock.Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  WeekNumberCellTemplate  -->
    <ControlTemplate x:Key="SyncfusionWeekNumberCellControlTemplate" TargetType="{x:Type shared:WeekNumberCell}">
        <Border
            x:Name="Border"
            Background="{Binding Path=WeekNumberBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            BorderBrush="{Binding Path=WeekNumberBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            BorderThickness="{Binding Path=WeekNumberBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            CornerRadius="{Binding Path=WeekNumberCornerRadius, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.FontFamily="{Binding Path=FontFamily, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.FontSize="{Binding Path=FontSize, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.FontStyle="{Binding Path=FontStyle, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.FontWeight="{Binding Path=FontWeight, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.Foreground="{Binding Path=WeekNumberForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}">
            <ContentPresenter
                x:Name="WeekNumberCellText"
                HorizontalAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                VerticalAlignment="{TemplateBinding Control.VerticalContentAlignment}"
                ContentTemplate="{TemplateBinding ContentTemplate}"
                ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}">
                <ContentPresenter.Resources>
                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                </ContentPresenter.Resources>
            </ContentPresenter>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter TargetName="Border" Property="BorderBrush" Value="{Binding Path=WeekNumberHoverBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                <Setter TargetName="Border" Property="Background" Value="{Binding Path=WeekNumberHoverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                <Setter TargetName="Border" Property="TextBlock.Foreground" Value="{Binding Path=WeekNumberHoverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
            </Trigger>

            <Trigger Property="IsSelected" Value="true">
                <Setter TargetName="Border" Property="BorderBrush" Value="{Binding Path=WeekNumberSelectionBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                <Setter TargetName="Border" Property="Background" Value="{Binding Path=WeekNumberSelectionBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                <Setter TargetName="Border" Property="TextBlock.Foreground" Value="{Binding Path=WeekNumberSelectionForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                <Setter TargetName="Border" Property="BorderThickness" Value="{Binding Path=WeekNumberSelectionBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                <Setter TargetName="Border" Property="CornerRadius" Value="{Binding Path=WeekNumberSelectionBorderCornerRadius, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
            </Trigger>

            <Trigger Property="IsFocused" Value="true">
                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt3}" />
                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                <Setter TargetName="Border" Property="TextBlock.Foreground" Value="{StaticResource PrimaryForeground}" />
            </Trigger>

            <Trigger Property="IsEnabled" Value="false">
                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                <Setter TargetName="Border" Property="TextBlock.Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  WeekNumberCellPanelTemplate  -->
    <ControlTemplate x:Key="SyncfusionWeekNumberCellPanelControlTemplate" TargetType="{x:Type shared:WeekNumberCellPanel}">
        <Border
            x:Name="Border"
            Background="{Binding Path=WeekNumberBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            BorderBrush="{Binding Path=WeekNumberBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            BorderThickness="{Binding Path=WeekNumberBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            CornerRadius="{Binding Path=WeekNumberCornerRadius, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.FontFamily="{Binding Path=FontFamily, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.FontSize="{Binding Path=FontSize, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.FontStyle="{Binding Path=FontStyle, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.FontWeight="{Binding Path=FontWeight, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
            TextBlock.Foreground="{Binding Path=WeekNumberForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}">

            <ContentPresenter
                x:Name="WeekNumberCellPanelText"
                HorizontalAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                VerticalAlignment="{TemplateBinding Control.VerticalContentAlignment}"
                ContentTemplate="{TemplateBinding ContentTemplate}"
                ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}">
                <ContentPresenter.Resources>
                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                </ContentPresenter.Resources>
            </ContentPresenter>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter TargetName="Border" Property="BorderBrush" Value="{Binding Path=WeekNumberHoverBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                <Setter TargetName="Border" Property="Background" Value="{Binding Path=WeekNumberHoverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                <Setter TargetName="Border" Property="TextBlock.Foreground" Value="{Binding Path=WeekNumberHoverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
            </Trigger>

            <Trigger Property="IsSelected" Value="true">
                <Setter TargetName="Border" Property="BorderBrush" Value="{Binding Path=WeekNumberSelectionBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                <Setter TargetName="Border" Property="Background" Value="{Binding Path=WeekNumberSelectionBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                <Setter TargetName="Border" Property="TextBlock.Foreground" Value="{Binding Path=WeekNumberSelectionForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                <Setter TargetName="Border" Property="BorderThickness" Value="{Binding Path=WeekNumberSelectionBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                <Setter TargetName="Border" Property="CornerRadius" Value="{Binding Path=WeekNumberSelectionBorderCornerRadius, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
            </Trigger>

            <Trigger Property="IsFocused" Value="true">
                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt3}" />
                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                <Setter TargetName="Border" Property="TextBlock.Foreground" Value="{StaticResource PrimaryForeground}" />
            </Trigger>

            <Trigger Property="IsEnabled" Value="false">
                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                <Setter TargetName="Border" Property="TextBlock.Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  WeekNumberCellPanel  -->
    <Style x:Key="SyncfusionWeekNumberCellPanelStyle" TargetType="{x:Type shared:WeekNumberCellPanel}">
        <Setter Property="Control.HorizontalContentAlignment" Value="{x:Static HorizontalAlignment.Center}" />
        <Setter Property="Control.VerticalContentAlignment" Value="{x:Static VerticalAlignment.Center}" />
        <Setter Property="BorderThickness" Value="0,0,0.5,0" />
        <Setter Property="Control.Template" Value="{StaticResource SyncfusionWeekNumberCellPanelControlTemplate}" />
        <Setter Property="Control.Focusable" Value="False" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionWeekNumberCellPanelStyle}" TargetType="{x:Type shared:WeekNumberCellPanel}" />

    <!--  DayCellStyle  -->
    <Style x:Key="SyncfusionDayCellStyle" TargetType="{x:Type shared:DayCell}">
        <Setter Property="Control.HorizontalContentAlignment" Value="{x:Static HorizontalAlignment.Stretch}" />
        <Setter Property="Control.VerticalContentAlignment" Value="{x:Static VerticalAlignment.Stretch}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="ContentTemplate" Value="{StaticResource SyncfusionDayCellTemplate}" />
        <Setter Property="Width" Value="30"/>
        <Setter Property="Height" Value="30"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:DayCell}">
                    <shared:DayCellPanel>
                        <Grid x:Name="DayCellGrid">
                            <Border 
                                x:Name="TodayBackground"
                                Background="{Binding Path=TodayCellBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
                                BorderBrush="{Binding Path=TodayCellBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
                                BorderThickness="1"
                                Opacity="0"
                                CornerRadius="100"/>
                            <Border x:Name="SelectedBackground"
                                 Background="{Binding Path=SelectedDayCellBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
                                 Opacity="0"
                                 CornerRadius="100"/>
                            <Border
                                x:Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}" />
                            <Border x:Name="HighlightBackground"
                                 Background="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
                                 BorderBrush="{StaticResource PrimaryBackground}"
                                 BorderThickness="1"
                                 Opacity="0"
                                 CornerRadius="100"/>
                            <Path
                                x:Name="BlockoutDate"
                                Height="1"
                                Margin="4"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch"
                                Data="M0,0 L16,0 16,2 0,2 z"
                                Fill="{Binding Path=BlackoutDatesBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
                                Opacity="0"
                                RenderTransformOrigin="0.5,0.5"
                                Stretch="Fill"
                                Stroke="{Binding Path=BlackoutDatesBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" >
                                <Path.RenderTransform>
                                    <RotateTransform CenterX="0" CenterY="0" Angle="45" />
                                </Path.RenderTransform>
                            </Path>
                            <ContentPresenter
                                x:Name="DayContentPresenter"
                                HorizontalAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding Control.VerticalContentAlignment}"
                                TextBlock.FontSize="{TemplateBinding FontSize}"
                                TextBlock.Foreground="{TemplateBinding Foreground}">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                            <Border x:Name="DayButtonFocusVisual"
                                 IsHitTestVisible="false"
                                 BorderBrush="{Binding Path=SelectedDayCellBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
                                 BorderThickness="1"
                                 Visibility="Collapsed" 
                                 CornerRadius="14">
                            </Border>
                        </Grid>
                    </shared:DayCellPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsDate" Value="true">
                            <Setter TargetName="SelectedBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="DayButtonFocusVisual" Property="Visibility" Value="Visible" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{Binding Path=SelectionForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True" />
                                <Condition Property="IsInvalidDate" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="SelectedBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="DayButtonFocusVisual" Property="Visibility" Value="Visible" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{Binding Path=SelectionForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsToday" Value="True" />
                                <Condition Property="IsInvalidDate" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter Property="IsTabStop" Value="True" />
                            <Setter TargetName="TodayBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{Binding Path=TodayCellForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsCurrentMonth" Value="False" />
                                <Condition Property="IsInvalidDate" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="HighlightBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="HighlightBackground" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="HighlightBackground" Property="BorderBrush" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{Binding Path=NotCurrentMonthForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </MultiTrigger>

                        <Trigger Property="IsInvalidDate" Value="True">
                            <Setter TargetName="BlockoutDate" Property="Opacity" Value="1" />
                            <Setter TargetName="BlockoutDate" Property="Stroke" Value="{Binding Path=BlackoutDatesCrossBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{Binding Path=BlackoutDatesForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True" />
                                <Condition Property="IsInvalidDate" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="HighlightBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{Binding Path=MouseOverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="true" />
                                <Condition Property="IsToday" Value="false" />
                                <Condition Property="IsMouseOver" Value="false" />
                                <Condition Property="IsInvalidDate" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter Property="IsTabStop" Value="True" />
                        </MultiTrigger>

                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="HighlightBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="HighlightBackground" Property="Background" Value="Transparent" />
                            <Setter TargetName="HighlightBackground" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True" />
                                <Condition Property="IsToday" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter Property="IsTabStop" Value="True" />
                            <Setter TargetName="SelectedBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="TodayBackground" Property="Opacity" Value="0" />
                            <Setter TargetName="SelectedBackground" Property="Width" Value="Auto" />
                            <Setter TargetName="SelectedBackground" Property="Height" Value="Auto" />
                            <Setter TargetName="SelectedBackground" Property="Margin" Value="3" />
                            <Setter TargetName="DayButtonFocusVisual" Property="Visibility" Value="Visible" />
                            <Setter TargetName="DayButtonFocusVisual" Property="BorderBrush" Value="{Binding Path=TodayCellSelectedBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="SelectedBackground" Property="Background" Value="{Binding Path=TodayCellSelectedBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource PrimaryForeground}" />
                        </MultiTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=IsVisible,RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding Path=IsDate,RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding Path=IsToday,RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding Path=AllowMultiplySelection, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType={x:Type shared:CalendarEdit}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="IsTabStop" Value="True" />
                            <Setter TargetName="SelectedBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="TodayBackground" Property="Opacity" Value="0" />
                            <Setter TargetName="SelectedBackground" Property="Width" Value="Auto" />
                            <Setter TargetName="SelectedBackground" Property="Height" Value="Auto" />
                            <Setter TargetName="SelectedBackground" Property="BorderThickness" Value="3" />
                            <Setter TargetName="DayButtonFocusVisual" Property="Visibility" Value="Visible" />
                            <Setter TargetName="DayButtonFocusVisual" Property="BorderBrush" Value="{Binding Path=TodayCellSelectedBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="SelectedBackground" Property="Background" Value="{Binding Path=TodayCellSelectedBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource PrimaryForeground}" />
                        </MultiDataTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True" />
                                <Condition Property="IsToday" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="SelectedBackground" Property="Opacity" Value="0" />
                            <Setter TargetName="HighlightBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="HighlightBackground" Property="Background" Value="{StaticResource PrimaryBackground}" />
                            <Setter TargetName="HighlightBackground" Property="BorderBrush" Value="{StaticResource PrimaryBackground}" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource PrimaryForeground}" />
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False" />
                                <Condition Property="IsToday" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="HighlightBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="HighlightBackground" Property="Background" Value="{StaticResource BorderAlt4}" />
                            <Setter TargetName="HighlightBackground" Property="BorderBrush" Value="{StaticResource BorderAlt4}" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True" />
                                <Condition Property="IsCurrentMonth" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="SelectedBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="DayButtonFocusVisual" Property="Visibility" Value="Visible" />
                            <Setter TargetName="SelectedBackground" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="DayButtonFocusVisual" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource ContentForegroundAlt1}" />
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True" />
                                <Condition Property="IsCurrentMonth" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="HighlightBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="HighlightBackground" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="HighlightBackground" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource ContentForegroundAlt1}" />
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False" />
                                <Condition Property="IsCurrentMonth" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="HighlightBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="HighlightBackground" Property="Background" Value="Transparent" />
                            <Setter TargetName="HighlightBackground" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False" />
                                <Condition Property="IsInvalidDate" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="BlockoutDate" Property="Opacity" Value="1" />
                            <Setter TargetName="HighlightBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="HighlightBackground" Property="Background" Value="Transparent" />
                            <Setter TargetName="HighlightBackground" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="BlockoutDate" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False" />
                                <Condition Property="IsToday" Value="True" />
                                <Condition Property="IsInvalidDate" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="BlockoutDate" Property="Opacity" Value="1" />
                            <Setter TargetName="HighlightBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="HighlightBackground" Property="Background" Value="Transparent" />
                            <Setter TargetName="HighlightBackground" Property="BorderBrush" Value="{StaticResource BorderAlt4}" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="BlockoutDate" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="False" />
                                <Condition Property="IsCurrentMonth" Value="False" />
                                <Condition Property="IsInvalidDate" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="BlockoutDate" Property="Opacity" Value="1" />
                            <Setter TargetName="HighlightBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="HighlightBackground" Property="Background" Value="Transparent" />
                            <Setter TargetName="HighlightBackground" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="BlockoutDate" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="false" />
                                <Condition Property="IsToday" Value="true" />
                                <Condition Property="IsMouseOver" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter Property="Focusable" Value="False" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CircleKeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default">
                <Setter Property="FocusVisualStyle" Value="{StaticResource DottedCircleKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionDayCellStyle}" TargetType="{x:Type shared:DayCell}" />

    <!--  MonthCellStyle  -->
    <Style x:Key="SyncfusionCalendarMonthCellStyle" TargetType="{x:Type shared:MonthCell}">
        <Setter Property="Control.HorizontalContentAlignment" Value="{x:Static HorizontalAlignment.Center}" />
        <Setter Property="Control.VerticalContentAlignment" Value="{x:Static VerticalAlignment.Center}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Width" Value="54" />
        <Setter Property="Height" Value="54" />
        <Setter Property="Margin" Value="2"/>
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="CornerRadius" Value="{StaticResource Windows11Dark.ThemeCornerRadiusVariant1}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:MonthCell}">
                    <shared:DayCellPanel>
                        <Grid x:Name="MonthCellGrid">
                            <Rectangle
                                x:Name="SelectedBackground"
                                Fill="Transparent"
                                Opacity="0"
                                RadiusX="100"
                                RadiusY="100"/>
                            <Rectangle
                                x:Name="Background"
                                Fill="{TemplateBinding Background}"
                                Opacity="0"
                                RadiusX="100"
                                RadiusY="100"
                                Stroke="{TemplateBinding BorderBrush}" />
                            <ContentPresenter
                                x:Name="DayContentPresenter"
                                Margin="1,0,1,1"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                TextElement.Foreground="{TemplateBinding Foreground}">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                            <Rectangle
                                x:Name="CalendarButtonFocusVisual"
                                RadiusX="100"
                                RadiusY="100"
                                Stroke="{StaticResource PrimaryBackground}"
                                Visibility="Collapsed" />
                        </Grid>
                    </shared:DayCellPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="IsTabStop" Value="True" />
                            <Setter TargetName="CalendarButtonFocusVisual" Property="Visibility" Value="Visible" />
                            <Setter TargetName="SelectedBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="SelectedBackground" Property="Fill" Value="{Binding Path=SelectedDayCellBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="DayContentPresenter" Property="TextBlock.Foreground" Value="{Binding Path=SelectionForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="SelectedBackground" Property="Stroke" Value="{Binding Path=SelectedDayCellBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </Trigger>

                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Background" Property="Opacity" Value="1" />
                            <Setter TargetName="DayContentPresenter" Property="TextBlock.Foreground" Value="{Binding Path=MouseOverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="Background" Property="Fill" Value="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="Background" Property="Stroke" Value="{Binding Path=MouseOverBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="true" />
                                <Condition Property="IsMouseOver" Value="true" />
                            </MultiTrigger.Conditions>
                            <Setter Property="IsTabStop" Value="True" />
                            <Setter TargetName="Background" Property="Opacity" Value="1" />
                            <Setter TargetName="DayContentPresenter" Property="TextBlock.Foreground" Value="{Binding Path=MouseOverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="Background" Property="Fill" Value="{Binding Path=SelectedDayCellHoverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="Background" Property="Stroke" Value="{Binding Path=SelectedDayCellBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CircleKeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default">
                <Setter Property="FocusVisualStyle" Value="{StaticResource DottedCircleKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionCalendarMonthCellStyle}" TargetType="{x:Type shared:MonthCell}" />

    <!--  YearCellStyle  -->
    <Style x:Key="SyncfusionYearCellStyle" TargetType="{x:Type shared:YearCell}">
        <Setter Property="Control.HorizontalContentAlignment" Value="{x:Static HorizontalAlignment.Center}" />
        <Setter Property="Control.VerticalContentAlignment" Value="{x:Static VerticalAlignment.Center}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Width" Value="54" />
        <Setter Property="Height" Value="54" />
        <Setter Property="Margin" Value="2"/>
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="CornerRadius" Value="{StaticResource Windows11Dark.ThemeCornerRadiusVariant1}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:YearCell}">
                    <shared:DayCellPanel>
                        <Grid x:Name="YearCellGrid">
                            <Rectangle
                                x:Name="SelectedBackground"
                                Fill="Transparent"
                                Opacity="0"
                                RadiusX="100"
                                RadiusY="100"/>
                            <Rectangle
                                x:Name="Background"
                                Fill="{TemplateBinding Background}"
                                Opacity="0"
                                RadiusX="100"
                                RadiusY="100"
                                Stroke="{TemplateBinding BorderBrush}" />
                            <ContentPresenter
                                x:Name="DayContentPresenter"
                                Margin="1,0,1,1"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                TextElement.Foreground="{TemplateBinding Foreground}">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                            <Rectangle
                                x:Name="CalendarButtonFocusVisual"
                                IsHitTestVisible="false"
                                RadiusX="100"
                                RadiusY="100"
                                Stroke="{StaticResource PrimaryBackground}"
                                Visibility="Collapsed" />
                        </Grid>
                    </shared:DayCellPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="IsTabStop" Value="True" />
                            <Setter TargetName="CalendarButtonFocusVisual" Property="Visibility" Value="Visible" />
                            <Setter TargetName="SelectedBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="SelectedBackground" Property="Fill" Value="{Binding Path=SelectedDayCellBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="DayContentPresenter" Property="TextBlock.Foreground" Value="{Binding Path=SelectionForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="SelectedBackground" Property="Stroke" Value="{Binding Path=SelectedDayCellBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </Trigger>

                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Background" Property="Opacity" Value="1" />
                            <Setter TargetName="DayContentPresenter" Property="TextBlock.Foreground" Value="{Binding Path=MouseOverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="Background" Property="Fill" Value="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="Background" Property="Stroke" Value="{Binding Path=MouseOverBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </Trigger>

                        <Trigger Property="IsBelongToCurrentRange" Value="False">
                            <Setter TargetName="Background" Property="Opacity" Value="1" />
                            <Setter TargetName="Background" Property="Fill" Value="Transparent" />
                            <Setter TargetName="Background" Property="Stroke" Value="Transparent" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="true" />
                                <Condition Property="IsMouseOver" Value="true" />
                            </MultiTrigger.Conditions>
                            <Setter Property="IsTabStop" Value="True" />
                            <Setter TargetName="Background" Property="Opacity" Value="1" />
                            <Setter TargetName="DayContentPresenter" Property="TextBlock.Foreground" Value="{Binding Path=MouseOverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="Background" Property="Fill" Value="{Binding Path=SelectedDayCellHoverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="Background" Property="Stroke" Value="{Binding Path=SelectedDayCellBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CircleKeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default">
                <Setter Property="FocusVisualStyle" Value="{StaticResource DottedCircleKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionYearCellStyle}" TargetType="{x:Type shared:YearCell}" />

    <!--  YearRangeCellStyle  -->
    <Style x:Key="SyncfusionYearRangeCellStyle" TargetType="{x:Type shared:YearRangeCell}">
        <Setter Property="Control.HorizontalContentAlignment" Value="{x:Static HorizontalAlignment.Center}" />
        <Setter Property="Control.VerticalContentAlignment" Value="{x:Static VerticalAlignment.Center}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Width" Value="54" />
        <Setter Property="Height" Value="54" />
        <Setter Property="Margin" Value="2"/>
        <Setter Property="Focusable" Value="True" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="CornerRadius" Value="{StaticResource Windows11Dark.ThemeCornerRadiusVariant1}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:YearRangeCell}">
                    <shared:DayCellPanel>
                        <Grid x:Name="YearRangeCellGrid">
                            <Rectangle
                                x:Name="SelectedBackground"
                                Fill="Transparent"
                                Opacity="0"
                                RadiusX="100"
                                RadiusY="100"/>
                            <Rectangle
                                x:Name="Background"
                                Fill="{TemplateBinding Background}"
                                Opacity="0"
                                RadiusX="100"
                                RadiusY="100"
                                Stroke="{TemplateBinding BorderBrush}" />
                            <ContentPresenter
                                x:Name="DayContentPresenter"
                                Margin="1,0,1,1"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                TextElement.Foreground="{TemplateBinding Foreground}">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                            <Rectangle
                                x:Name="CalendarButtonFocusVisual"
                                IsHitTestVisible="false"
                                RadiusX="100"
                                RadiusY="100"
                                Stroke="{StaticResource PrimaryBackground}"
                                Visibility="Collapsed" />
                        </Grid>
                    </shared:DayCellPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="IsTabStop" Value="True" />
                            <Setter TargetName="CalendarButtonFocusVisual" Property="Visibility" Value="Visible" />
                            <Setter TargetName="SelectedBackground" Property="Opacity" Value="1" />
                            <Setter TargetName="SelectedBackground" Property="Fill" Value="{Binding Path=SelectedDayCellBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="DayContentPresenter" Property="TextBlock.Foreground" Value="{Binding Path=SelectionForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="SelectedBackground" Property="Stroke" Value="{Binding Path=SelectedDayCellBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </Trigger>

                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Background" Property="Opacity" Value="1" />
                            <Setter TargetName="DayContentPresenter" Property="TextBlock.Foreground" Value="{Binding Path=MouseOverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="Background" Property="Fill" Value="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="Background" Property="Stroke" Value="{Binding Path=MouseOverBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </Trigger>

                        <Trigger Property="IsBelongToCurrentRange" Value="False">
                            <Setter TargetName="Background" Property="Opacity" Value="1" />
                            <Setter TargetName="Background" Property="Fill" Value="Transparent" />
                            <Setter TargetName="Background" Property="Stroke" Value="Transparent" />
                            <Setter TargetName="DayContentPresenter" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="true" />
                                <Condition Property="IsMouseOver" Value="true" />
                            </MultiTrigger.Conditions>
                            <Setter Property="IsTabStop" Value="True" />
                            <Setter TargetName="Background" Property="Opacity" Value="1" />
                            <Setter TargetName="DayContentPresenter" Property="TextBlock.Foreground" Value="{Binding Path=MouseOverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="Background" Property="Fill" Value="{Binding Path=SelectedDayCellHoverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                            <Setter TargetName="Background" Property="Stroke" Value="{Binding Path=SelectedDayCellBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CircleKeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default">
                <Setter Property="FocusVisualStyle" Value="{StaticResource DottedCircleKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionYearRangeCellStyle}" TargetType="{x:Type shared:YearRangeCell}" />
    <!--  End ControlTemplates  -->

    <!--  DayNameCellStyle  -->
    <Style x:Key="SyncfusionDayNameCellStyle"  TargetType="{x:Type shared:DayNameCell}">
        <Setter Property="Control.HorizontalContentAlignment" Value="{x:Static HorizontalAlignment.Center}" />
        <Setter Property="Control.VerticalContentAlignment" Value="{x:Static VerticalAlignment.Center}" />
        <Setter Property="BorderThickness" Value="0,0,0,0" />
        <Setter Property="Control.Template" Value="{StaticResource SyncfusionDayNameCellControlTemplate}" />
        <Setter Property="Width" Value="30"/>
        <Setter Property="Height" Value="30"/>
        <Setter Property="Margin" Value="2"/>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionDayNameCellStyle}" TargetType="{x:Type shared:DayNameCell}" />

    <!--  DayNamesGridStyle  -->
    <Style x:Key="SyncfusionDayNamesGridStyle" TargetType="{x:Type shared:DayNamesGrid}">
        <Setter Property="OverridesDefaultStyle" Value="True"/>
        <Setter Property="Control.Focusable" Value="False" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionDayNamesGridStyle}" TargetType="{x:Type shared:DayNamesGrid}" />

    <!--  WeekNumberCellStyle  -->
    <Style x:Key="SyncfusionCalendarWeekNumberCellStyle" TargetType="{x:Type shared:WeekNumberCell}">
        <Setter Property="Control.HorizontalContentAlignment" Value="{x:Static HorizontalAlignment.Center}" />
        <Setter Property="Control.VerticalContentAlignment" Value="{x:Static VerticalAlignment.Center}" />
        <Setter Property="BorderThickness" Value="0,0,0.5,0" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Control.Template" Value="{StaticResource SyncfusionWeekNumberCellControlTemplate}" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="Width" Value="24"/>
        <Setter Property="Margin" Value="2"/>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionCalendarWeekNumberCellStyle}" TargetType="{x:Type shared:WeekNumberCell}" />

    <!--  WeekNumberGrid  -->
    <Style x:Key="SyncfusionWeekNumbersGridStyle" TargetType="{x:Type shared:WeekNumbersGrid}">
        <Setter Property="Control.Focusable" Value="True" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionWeekNumbersGridStyle}" TargetType="{x:Type shared:WeekNumbersGrid}" />

    <!--  DayGrid  -->
    <Style x:Key="SyncfusionDayGridStyle" TargetType="{x:Type shared:DayGrid}">
        <Setter Property="Control.Focusable" Value="False" />
        <Setter Property="KeyboardNavigation.IsTabStop" Value="False" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionDayGridStyle}" TargetType="{x:Type shared:DayGrid}" />
    
    <!--  MonthGrid  -->
    <Style x:Key="SyncfusionMonthGridStyle" TargetType="{x:Type shared:MonthGrid}">
        <Setter Property="Control.Focusable" Value="False" />
        <Setter Property="KeyboardNavigation.IsTabStop" Value="False" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionMonthGridStyle}" TargetType="{x:Type shared:MonthGrid}" />

    <!--  YearGrid  -->
    <Style x:Key="SyncfusionYearGridStyle" TargetType="{x:Type shared:YearGrid}">
        <Setter Property="Control.Focusable" Value="False" />
        <Setter Property="KeyboardNavigation.IsTabStop" Value="False" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionYearGridStyle}" TargetType="{x:Type shared:YearGrid}" />

    <!--  YearRangeGrid  -->
    <Style x:Key="SyncfusionYearRangeGridStyle" TargetType="{x:Type shared:YearRangeGrid}">
        <Setter Property="Control.Focusable" Value="False" />
        <Setter Property="KeyboardNavigation.IsTabStop" Value="False" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionYearRangeGridStyle}" TargetType="{x:Type shared:YearRangeGrid}" />

    <!--  NavigateButtonBase  -->
    <Style x:Key="SyncfusionNavigateButtonBaseStyle" TargetType="{x:Type shared:NavigateButtonBase}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness}" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource IconColor}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:NavigateButtonBase}">
                    <Border
                        x:Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="4"
                        SnapsToDevicePixels="true">
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            Focusable="False"
                            RecognizesAccessKey="True"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionNavigateButtonBaseStyle}" TargetType="{x:Type shared:NavigateButtonBase}" />

    <!--  NavigateButton  -->
    <Style x:Key="SyncfusionNavigateButtonStyle" TargetType="{x:Type shared:NavigateButton}">
        <Setter Property="Control.HorizontalContentAlignment" Value="{x:Static HorizontalAlignment.Center}" />
        <Setter Property="Control.VerticalContentAlignment" Value="{x:Static VerticalAlignment.Center}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:NavigateButton}">
                    <shared:NavigateButtonBase
                            x:Name="Button" Content="{TemplateBinding Content}"
                            Width="{TemplateBinding Width}"
                            Height="{TemplateBinding Height}"
                            IsTabStop="False">
                    </shared:NavigateButtonBase>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Tag" Value="NextMonthButton">
                            <Setter TargetName="Button" Property="Command" Value="shared:CalendarEdit.NextCommand" />
                        </Trigger>
                        <Trigger Property="Tag" Value="PreviousMonthButton">
                            <Setter TargetName="Button" Property="Command" Value="shared:CalendarEdit.PrevCommand" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionNavigateButtonStyle}" TargetType="{x:Type shared:NavigateButton}" />

    <Style x:Key="SyncfusionMonthButtonStyle" TargetType="{x:Type shared:MonthButton}">
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="{x:Static VerticalAlignment.Center}" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="Padding" Value="4, 0, 4, 0" />
        <Setter Property="TextBlock.FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="TextBlock.FontSize" Value="{StaticResource Windows11Dark.TitleTextStyle}" />
        <Setter Property="TextBlock.FontWeight" Value="SemiBold" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Focusable" Value="false" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:MonthButton}">
                    <Border
                        x:Name="border"
                        CornerRadius="4"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        Padding="{TemplateBinding Padding}">
                        <ContentPresenter
                            x:Name="buttonContent"
                            HorizontalAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding Control.VerticalContentAlignment}"
                            TextBlock.Foreground="{Binding Path=HeaderForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type shared:CalendarEdit}}}"
                            Content="{TemplateBinding ContentControl.Content}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="buttonContent" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackgroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="buttonContent" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionMonthButtonStyle}" TargetType="{x:Type shared:MonthButton}" />

    <Style x:Key="SyncfusionCalendarEditStyle" TargetType="{x:Type shared:CalendarEdit}">
        <!--<Setter Property="Width" Value="208" />
        <Setter Property="Height" Value="219" />-->
        <Setter Property="Control.MinWidth" Value="208" />
        <Setter Property="Control.MinHeight" Value="219" />
        <Setter Property="Padding" Value="2"/>
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="WeekNumberBorderThickness" Value="1" />
        <Setter Property="WeekNumberCornerRadius" Value="2" />
        <Setter Property="WeekNumberSelectionBorderThickness" Value="1" />
        <Setter Property="WeekNumberSelectionBorderCornerRadius" Value="2" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="HeaderBackground" Value="Transparent" />
        <Setter Property="HeaderForeground" Value="{StaticResource ContentForeground}" />
        <Setter Property="WeekNumberBackground" Value="Transparent" />
        <Setter Property="WeekNumberForeground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="WeekNumberBorderBrush" Value="Transparent" />
        <Setter Property="WeekNumberHoverBorderBrush" Value="{StaticResource PrimaryBackgroundOpacity1}" />
        <Setter Property="WeekNumberHoverBackground" Value="{StaticResource PrimaryBackgroundOpacity1}" />
        <Setter Property="WeekNumberHoverForeground" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="WeekNumberSelectionBorderBrush" Value="{StaticResource PrimaryBackgroundOpacity2}" />
        <Setter Property="WeekNumberSelectionBackground" Value="{StaticResource PrimaryBackgroundOpacity2}" />
        <Setter Property="WeekNumberSelectionForeground" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="MouseOverBackground" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="MouseOverBorderBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="MouseOverForeground" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="SelectionForeground" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="SelectedDayCellBorderBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="SelectedDayCellBackground" Value="Transparent" />
        <Setter Property="SelectedDayCellHoverBackground" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="NotCurrentMonthForeground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="TodayCellForeground" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="TodayCellBorderBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="TodayCellBackground" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="TodayCellSelectedBorderBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="TodayCellSelectedBackground" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="SelectionBorderBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="SelectedDayCellForeground" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="BlackoutDatesBackground" Value="Transparent" />
        <Setter Property="BlackoutDatesBorderBrush" Value="Transparent" />
        <Setter Property="BlackoutDatesCrossBrush" Value="{StaticResource BorderAlt1}" />
        <Setter Property="BlackoutDatesForeground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="DayCellsStyle" Value="{StaticResource SyncfusionDayCellStyle}" />
        <Setter Property="DayNameCellsStyle" Value="{StaticResource SyncfusionDayNameCellStyle}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:CalendarEdit}">
                    <Border
                        Name="Border"
                        Width="{TemplateBinding Width}"
                        Height="{TemplateBinding Height}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Padding="{TemplateBinding Padding}"
                        CornerRadius="{StaticResource Windows11Dark.CornerRadius8}"
                        SnapsToDevicePixels="True">
                        <Grid x:Name="MainGrid" FlowDirection="{TemplateBinding FlowDirection}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <Grid
                                x:Name="InnerGrid"
                                Grid.Row="0"
                                Grid.Column="1"
                                Grid.ColumnSpan="6"
                                Margin="2">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Border Grid.ColumnSpan="3" 
                                            SnapsToDevicePixels="True"
                                            Visibility="Visible"
                                            BorderThickness="0,0,0,1"
                                            BorderBrush="{TemplateBinding BorderBrush}"/>
                                <shared:NavigateButton
                                    x:Name="PART_NextMonthButton"
                                    Grid.Row="0"
                                    Grid.Column="2"
                                    Width="24"
                                    Height="24"
                                    MinWidth="5"
                                    MinHeight="5"
                                    Margin="3"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center"
                                    Tag="NextMonthButton"
                                    Background="{Binding Path=HeaderBackground, RelativeSource={RelativeSource TemplatedParent}}"
                                    Foreground="{Binding Path=HeaderForeground, RelativeSource={RelativeSource TemplatedParent}}"
                                    IsTabStop="False" >
                                    <shared:NavigateButton.Content>
                                        <Path 
                                            x:Name="NextButtonPath" 
                                            Width="8"
                                            Height="8"
                                            Margin="0"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Stretch="Fill"
                                            StrokeThickness="1">
                                            <Path.Data>
                                                <PathGeometry>M1 8C0.864563 8 0.735657 7.97412 0.613281 7.92188C0.490906 7.86963 0.385437 7.79932 0.296875 7.71094C0.254456 7.66846 0.216309 7.62305 0.182373 7.57422C0.143311 7.51855 0.109863 7.4585 0.0820312 7.39453C0.0273438 7.2749 0 7.146 0 7.00781V0.988281C0 0.933105 0.0043335 0.878906 0.0130615 0.826172C0.0256348 0.75 0.0473022 0.67627 0.078125 0.605469C0.130188 0.48291 0.201843 0.377441 0.292969 0.289062C0.348511 0.235352 0.409912 0.187988 0.477112 0.147461C0.520081 0.121582 0.565491 0.0986328 0.613281 0.078125C0.677124 0.050293 0.743164 0.0297852 0.811462 0.0170898C0.871338 0.00585938 0.932861 0 0.996094 0C1.10547 0 1.20575 0.015625 1.29688 0.046875C1.388 0.0756836 1.47919 0.119629 1.57031 0.179688L5.35547 2.78125C5.55597 2.91943 5.71356 3.09619 5.82812 3.3125C5.94269 3.52588 6 3.75537 6 4C6 4.16406 5.97412 4.32227 5.92249 4.47363C5.89722 4.54785 5.86578 4.62012 5.82812 4.69141C5.75012 4.83643 5.65222 4.96436 5.53442 5.0752C5.47913 5.12695 5.41949 5.1748 5.35547 5.21875L1.57031 7.82031C1.53448 7.84473 1.49841 7.8667 1.46216 7.88623C1.40881 7.91504 1.35504 7.93848 1.30078 7.95703C1.20966 7.98584 1.10938 8 1 8Z</PathGeometry>
                                            </Path.Data>
                                            <Path.Style>
                                                <Style TargetType="Path">
                                                    <Setter Property="Fill" Value="{Binding RelativeSource={RelativeSource AncestorType=shared:NavigateButton}, Path=Foreground}"/>
                                                </Style>
                                            </Path.Style>
                                        </Path>
                                    </shared:NavigateButton.Content>
                                </shared:NavigateButton>

                                <shared:NavigateButton
                                    x:Name="PART_PrevMonthButton"
                                    Grid.Row="0"
                                    Grid.Column="1"
                                    Width="24"
                                    Height="24"
                                    MinWidth="5"
                                    MinHeight="5"
                                    Margin="3"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    Tag="PreviousMonthButton"
                                    Background="{Binding Path=HeaderBackground, RelativeSource={RelativeSource TemplatedParent}}"
                                    Foreground="{Binding Path=HeaderForeground, RelativeSource={RelativeSource TemplatedParent}}"
                                    IsTabStop="False">
                                    <shared:NavigateButton.Content>
                                        <Path 
                                            x:Name="PreviousButtonPath" 
                                            Width="8"
                                            Height="8"
                                            Margin="0"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Stretch="Fill"
                                            StrokeThickness="1">
                                            <Path.Data>
                                                <PathGeometry>M6 7.00781C6 7.14583 5.97396 7.27474 5.92188 7.39453C5.86719 7.51432 5.79557 7.61979 5.70703 7.71094C5.61589 7.79948 5.51042 7.86979 5.39062 7.92188C5.26823 7.97396 5.13802 8 5 8C4.89062 8 4.79036 7.98568 4.69922 7.95703C4.60807 7.92578 4.51823 7.88021 4.42969 7.82031L0.644531 5.21875C0.545573 5.15104 0.458333 5.07161 0.382812 4.98047C0.307292 4.88932 0.244792 4.79167 0.195312 4.6875C0.143229 4.58073 0.104167 4.46875 0.0781248 4.35156C0.0520832 4.23438 0.0390625 4.11719 0.0390625 4C0.0390625 3.88281 0.0520831 3.76562 0.0781248 3.64844C0.104166 3.53125 0.143229 3.42057 0.195312 3.31641C0.244791 3.20964 0.307291 3.11068 0.382812 3.01953C0.458333 2.92839 0.545573 2.84896 0.644531 2.78125L4.42969 0.179687C4.51823 0.119791 4.60807 0.0755208 4.69922 0.0468748C4.79036 0.0156248 4.89062 0 5 0C5.13802 0 5.26823 0.0260413 5.39062 0.0781248C5.51042 0.130208 5.61588 0.200521 5.70703 0.289062C5.79557 0.377604 5.86719 0.483072 5.92187 0.605469C5.97396 0.72526 6 0.852865 6 0.988281L6 7.00781Z</PathGeometry>
                                            </Path.Data>
                                            <Path.Style>
                                                <Style TargetType="Path">
                                                    <Setter Property="Fill" Value="{Binding RelativeSource={RelativeSource AncestorType=shared:NavigateButton}, Path=Foreground}"/>
                                                </Style>
                                            </Path.Style>
                                        </Path>
                                    </shared:NavigateButton.Content>
                                </shared:NavigateButton>

                                <shared:MonthButton
                                    Name="PART_Month1"
                                    Grid.Row="0"
                                    Grid.Column="0"
                                    Height="24" 
                                    Cursor="Hand"
                                    VerticalAlignment="Center"
                                    HorizontalAlignment="Stretch"
                                    Background="{Binding Path=HeaderBackground, RelativeSource={RelativeSource TemplatedParent}}"
                                    Foreground="{Binding Path=HeaderForeground, RelativeSource={RelativeSource TemplatedParent}}"
                                    Style="{StaticResource SyncfusionMonthButtonStyle}" >
                                </shared:MonthButton>
                                <shared:MonthButton
                                    Name="PART_Month2"
                                    Grid.Row="0"
                                    Height="24"
                                    Grid.Column="0"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Center"
                                    Background="{Binding Path=HeaderBackground, RelativeSource={RelativeSource TemplatedParent}}"
                                    Foreground="{Binding Path=HeaderForeground, RelativeSource={RelativeSource TemplatedParent}}"
                                    Style="{StaticResource SyncfusionMonthButtonStyle}" >
                                </shared:MonthButton>
                                <StackPanel
                                    Name="PART_YearUpDownPanel"
                                    Grid.Row="0"
                                    Grid.Column="1"
                                    HorizontalAlignment="Center"
                                    Orientation="Horizontal"
                                    Visibility="Collapsed">
                                    <TextBlock
                                        Name="PART_EditMonthName"
                                        Padding="1"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center" />
                                    <shared:UpDown
                                        Name="PART_YearUpDown"
                                        Width="45"
                                        Height="18"
                                        HorizontalAlignment="Center"
                                        BorderThickness="0"
                                        MaxValue="{Binding Path=MaxDate.Year, RelativeSource={RelativeSource AncestorType={x:Type shared:CalendarEdit}}}"
                                        MinValue="{Binding Path=MinDate.Year, RelativeSource={RelativeSource AncestorType={x:Type shared:CalendarEdit}}}"
                                        NumberDecimalDigits="0" />
                                </StackPanel>
                            </Grid>

                            <Popup
                                Name="PART_MonthPopup"
                                Grid.Row="0"
                                Grid.Column="0"
                                Grid.ColumnSpan="6"
                                Placement="Center">
                                <ListBox Background="{TemplateBinding Background}"
                                         BorderThickness="{TemplateBinding BorderThickness}"
                                         Width="{TemplateBinding Width}" 
                                         HorizontalContentAlignment="Center" />
                            </Popup>

                            <ContentPresenter
                                Name="PART_WeekNumbers"
                                Grid.Row="2"
                                Grid.Column="1"
                                ClipToBounds="True"
                                IsHitTestVisible="False"
                                Content="{Binding Path=WeekNumbersGrid, RelativeSource={RelativeSource TemplatedParent}}"
                                Visibility="Collapsed" />

                            <ContentPresenter
                                Name="DayNamesGrid"
                                Grid.Row="1"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                Height="Auto"
                                VerticalAlignment="Center"
                                Content="{Binding Path=DayNamesGrid, RelativeSource={RelativeSource TemplatedParent}}" />
                            <ContentPresenter
                                Name="CurrentDayGrid"
                                Grid.Row="2"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                ClipToBounds="True"
                                Content="{Binding Path=CurrentDayGrid, RelativeSource={RelativeSource TemplatedParent}}" />
                            <ContentPresenter
                                Name="WeekNumbersForYearCurrent"
                                Grid.Row="2"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                ClipToBounds="True"
                                Content="{Binding Path=CurrentWeekNumbersGrid, RelativeSource={RelativeSource TemplatedParent}}" />
                            <ContentPresenter
                                Name="WeekNumbersForYearFollow"
                                Grid.Row="2"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                ClipToBounds="True"
                                Content="{Binding Path=FollowingWeekNumbersGrid, RelativeSource={RelativeSource TemplatedParent}}" />
                            <ContentPresenter
                                Name="FollowingDayGrid"
                                Grid.Row="2"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                ClipToBounds="True"
                                Content="{Binding Path=FollowingDayGrid, RelativeSource={RelativeSource TemplatedParent}}" />
                            <ContentPresenter
                                Name="CurrentMonthGrid"
                                Grid.Row="1"
                                Grid.RowSpan="2"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                ClipToBounds="True"
                                VerticalAlignment="Center"
                                Content="{Binding Path=CurrentMonthGrid, RelativeSource={RelativeSource TemplatedParent}}" />
                            <ContentPresenter
                                Name="CurrentYearGrid"
                                Grid.Row="1"
                                Grid.RowSpan="2"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                ClipToBounds="True"
                                VerticalAlignment="Center"
                                Content="{Binding Path=CurrentYearGrid, RelativeSource={RelativeSource TemplatedParent}}" />
                            <ContentPresenter
                                Name="CurrentYearRangeGrid"
                                Grid.Row="1"
                                Grid.RowSpan="2"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                ClipToBounds="True"
                                VerticalAlignment="Center"
                                Content="{Binding Path=CurrentYearRangeGrid, RelativeSource={RelativeSource TemplatedParent}}" />
                            <ContentPresenter
                                Name="FollowingMonthGrid"
                                Grid.Row="1"
                                Grid.RowSpan="2"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                Margin="0,4,0,0"
                                ClipToBounds="True"
                                VerticalAlignment="Center"
                                Content="{Binding Path=FollowingMonthGrid, RelativeSource={RelativeSource TemplatedParent}}" />
                            <ContentPresenter
                                Name="FollowingYearGrid"
                                Grid.Row="1"
                                Grid.RowSpan="2"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                ClipToBounds="True"
                                VerticalAlignment="Center"
                                Content="{Binding Path=FollowingYearGrid, RelativeSource={RelativeSource TemplatedParent}}" />
                            <ContentPresenter
                                Name="FollowingYearRangeGrid"
                                Grid.Row="1"
                                Grid.RowSpan="2"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                ClipToBounds="True"
                                VerticalAlignment="Center"
                                Content="{Binding Path=FollowingYearRangeGrid, RelativeSource={RelativeSource TemplatedParent}}" />
                            <Grid
                                Name="PART_TodayGrid"
                                Grid.Row="3"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                Background="Transparent"
                                Visibility="{Binding Path=TodayRowIsVisible, Converter={StaticResource Boolean_To_VisibilityConverter}, RelativeSource={RelativeSource TemplatedParent}}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Button
                                    Name="PART_TodayButton"
                                    x:Uid="AccessTodayTextID"
                                    Grid.Column="0"
                                    Padding="2"
                                    Margin="2"
                                    Content="{Sync_Shared_Resources:SharedLocalizationResourceExtension ResourceName=AccessTodayText}" />
                                <Border
                                    Grid.Column="1"
                                    Margin="2"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center">
                                    <TextBlock
                                        x:Name="TodayText"
                                        HorizontalAlignment="Center"
                                        Text="{Binding Path=TodayDate, RelativeSource={RelativeSource TemplatedParent}}" />
                                </Border>
                            </Grid>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="MonthChangeDirection" Value="Vertical">
                            <Setter TargetName="PreviousButtonPath" Property="Data" Value="M0.605469 5.92188C0.725281 5.97412 0.854187 6 0.992188 6H7.01172C7.14716 6 7.27472 5.97412 7.39453 5.92188C7.51691 5.86719 7.62238 5.79541 7.71094 5.70703C7.7995 5.61572 7.86981 5.51025 7.92188 5.39062C7.97394 5.26807 8 5.13818 8 5C8 4.89062 7.98438 4.79053 7.95312 4.69922C7.9245 4.60791 7.88019 4.51807 7.82031 4.42969L5.21875 0.644531C5.15106 0.54541 5.07159 0.458496 4.98047 0.382812C4.88934 0.307129 4.79034 0.244629 4.68359 0.195312C4.57941 0.143066 4.46875 0.104004 4.35156 0.078125C4.23438 0.0522461 4.11719 0.0390625 4 0.0390625C3.88281 0.0390625 3.76562 0.0522461 3.64844 0.078125C3.53125 0.104004 3.41925 0.143066 3.3125 0.195312C3.20831 0.244629 3.11066 0.307129 3.01953 0.382812C2.92841 0.458496 2.84894 0.54541 2.78125 0.644531L0.179688 4.42969C0.119812 4.51807 0.0742188 4.60791 0.0429688 4.69922C0.0143433 4.79053 0 4.89062 0 5C0 5.13818 0.026062 5.26807 0.078125 5.39062C0.130188 5.51025 0.2005 5.61572 0.289062 5.70703C0.380188 5.79541 0.485657 5.86719 0.605469 5.92188Z"/>
                            <Setter TargetName="NextButtonPath" Property="RenderTransformOrigin" Value="0.5,0.5"/>
                            <Setter TargetName="NextButtonPath" Property="Width" Value="8"/>
                            <Setter TargetName="NextButtonPath" Property="Height" Value="8"/>
                            <Setter TargetName="PreviousButtonPath" Property="Width" Value="8"/>
                            <Setter TargetName="PreviousButtonPath" Property="Height" Value="8"/>
                            <Setter TargetName="NextButtonPath" Property="Data" Value="M0.078125 0.613281C0.026062 0.73584 0 0.864746 0 1C0 1.05029 0.00305176 1.09863 0.00909424 1.14453C0.0161743 1.19922 0.0274658 1.25146 0.0429688 1.30078C0.0742188 1.39209 0.119812 1.48193 0.179688 1.57031L2.78125 5.35547C2.85974 5.46973 2.9505 5.56982 3.05353 5.65576C3.13153 5.72119 3.21655 5.77881 3.30859 5.82812C3.52472 5.94287 3.75519 6 4 6C4.14905 6 4.29224 5.979 4.42969 5.93652C4.51801 5.90918 4.60394 5.87305 4.6875 5.82812C4.90363 5.71338 5.08075 5.55615 5.21875 5.35547L7.82031 1.57031C7.88019 1.479 7.9245 1.38818 7.95312 1.29688C7.98438 1.20557 8 1.10547 8 0.996094C8 0.86084 7.97394 0.73291 7.92188 0.613281C7.86981 0.490723 7.7995 0.384277 7.71094 0.292969C7.62238 0.20166 7.51691 0.130371 7.39453 0.078125C7.27472 0.0258789 7.14716 0 7.01172 0H0.992188C0.854187 0 0.725281 0.0273438 0.605469 0.0820312C0.485657 0.134277 0.380188 0.205566 0.289062 0.296875C0.2005 0.385254 0.130188 0.490723 0.078125 0.613281Z"/>
                            <Setter TargetName="PreviousButtonPath" Property="RenderTransformOrigin" Value="0.5,0.5"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Border" Property="Opacity" Value="0.5" />
                            <Setter Property="HeaderBackground" Value="Transparent" />
                        </Trigger>
                        <Trigger Property="ShowWeekNumbers" Value="True">
                            <Setter Property="MinWidth" Value="224" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionCalendarEditStyle}" TargetType="{x:Type shared:CalendarEdit}" />
</ResourceDictionary>
