<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.Wpf"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <local:BooleanToOpacityConverter x:Key="BooleanToOpacityConvert" />
    <Style x:Key="SyncfusionTabNavigationRoundButtonStyle" BasedOn="{StaticResource WPFRoundedButtonStyle}" TargetType="{x:Type Button}" >
        <Setter Property="Width" Value="24"/>
        <Setter Property="Height" Value="24"/>
    </Style>

    <Style x:Key="SyncfusionTabNavigationFlatButtonStyle"
           TargetType="{x:Type Button}">
        <Setter Property="FocusVisualStyle" 
                Value="{x:Null}"/>
        <Setter Property="Background" 
                Value="{StaticResource SecondaryBackground}"/>
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Light.BorderThickness1}"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource SecondaryBorder}"/>
        <Setter Property="Foreground"
                Value="{StaticResource SecondaryForeground}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="HorizontalContentAlignment" 
                Value="Center"/>
        <Setter Property="VerticalContentAlignment" 
                Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Grid>
                        <Border CornerRadius="3">
                        <Rectangle
                            x:Name="rectangle"
                            Height="30"
                            Width="14"
                            
                            Fill="{TemplateBinding Background}"
                            Stroke="{TemplateBinding BorderBrush}"
                            StrokeThickness="{TemplateBinding BorderThickness}"                                 
                            />
                        </Border>
                        <ContentPresenter x:Name="contentPresenter" 
                                          Focusable="False"
                                          Margin="{TemplateBinding Padding}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" 
                                          RecognizesAccessKey="True">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsDefaulted" Value="true">
                            <Setter Property="Stroke" TargetName="rectangle" Value="{StaticResource BorderAlt3}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default"/>
                                <Condition Property="IsFocused" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Fill" TargetName="rectangle" Value="{StaticResource SecondaryBackgroundHovered}"/>
                            <Setter Property="Stroke" TargetName="rectangle" Value="{StaticResource BorderAlt3}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Fill" TargetName="rectangle" Value="{StaticResource SecondaryBackgroundHovered}"/>
                            <Setter Property="Stroke" TargetName="rectangle" Value="{StaticResource SecondaryBorderHovered}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Fill" TargetName="rectangle" Value="{StaticResource SecondaryBackgroundSelected}"/>
                            <Setter Property="Stroke" TargetName="rectangle" Value="{StaticResource SecondaryBorderSelected}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Fill" TargetName="rectangle" Value="{StaticResource SecondaryBackgroundDisabled}"/>
                            <Setter Property="Stroke" TargetName="rectangle" Value="{StaticResource SecondaryBorderDisabled}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CircleKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
 
    <Style x:Key="SyncfusionTabStripButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Grid Cursor="Hand">
                        <Ellipse
                            x:Name="Ellipse"
                            Width="8"
                            Height="8"
                            Fill="{StaticResource ContentBackgroundAlt3}"
                            Stroke="{StaticResource SecondaryBorder}"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter  TargetName="Ellipse" Property="Height" Value="15" />
                            <Setter  TargetName="Ellipse" Property="Width" Value="15" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Ellipse" Property="Fill" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="Ellipse" Property="Stroke" Value="{StaticResource SecondaryBorderHovered}" />
                        </Trigger>
                        <DataTrigger Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:TabNavigationItem}}}" Value="True">
                            <Setter TargetName="Ellipse" Property="Fill" Value="{StaticResource PrimaryBackground}" />
                            <Setter TargetName="Ellipse" Property="Stroke" Value="{StaticResource PrimaryBackground}" />
                        </DataTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Ellipse" Property="Fill" Value="{StaticResource SecondaryBackground}" />
                            <Setter TargetName="Ellipse" Property="Stroke" Value="{StaticResource SecondaryBackground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CircleKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionTabNavigationItemStyle" TargetType="local:TabNavigationItem">
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:TabNavigationItem">
                    <Grid>
                        <Button
                            Margin="3"
                            Background="Transparent"
                            Command="{Binding Path=SelectCommand, RelativeSource={RelativeSource TemplatedParent}}"
                            Style="{StaticResource SyncfusionTabStripButtonStyle}"
                            Visibility="{Binding Path=TabStripVisibility, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:TabNavigationControl}}}" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="IsTabStop" Value="True" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTabNavigationItemStyle}" TargetType="local:TabNavigationItem" />

    <Style x:Key="SyncfusionTabNavigationControlStyle" TargetType="local:TabNavigationControl">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:TabNavigationControl">
                    <Grid FocusVisualStyle="{x:Null}">
                        <Border
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            FocusVisualStyle="{x:Null}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius8}">
                            <Grid x:Name="Root" FocusVisualStyle="{x:Null}">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Border
                                    Height="32"
                                    Background="{StaticResource ContentBackgroundInactive}"
                                    
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    Visibility="{TemplateBinding HeaderVisibility}">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Setter Property="CornerRadius" Value="8,8,0,0"/>
                                            <Style.Triggers>
                                                <MultiDataTrigger>
                                                    <MultiDataTrigger.Conditions>
                                                        <Condition Binding="{Binding TabStripPosition, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:TabNavigationControl}}}" Value="Bottom" />
                                                        <Condition Binding="{Binding TabStripVisibility, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:TabNavigationControl}}}" Value="Visible" />
                                                    </MultiDataTrigger.Conditions>
                                                    <Setter Property="CornerRadius" Value="0,0,8,8" />
                                                    <Setter Property="Grid.Row" Value="2"/>
                                                </MultiDataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    </Border>
                                <Grid  Grid.Row="0" Height="32" Visibility="{TemplateBinding HeaderVisibility}">
                                        <ContentPresenter x:Name="header"
                                            Margin="12,3,3,3"
                                            VerticalAlignment="Center"
                                            Content="{Binding Path=SelectedTabItem.Header, RelativeSource={RelativeSource TemplatedParent}}"
                                            TextElement.Foreground="{StaticResource ContentForeground}"
                                            TextElement.FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                            TextElement.FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                            TextElement.FontWeight="{StaticResource Windows11Light.FontWeightMedium}">
                                            <ContentPresenter.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="TextBlock" />
                                            </ContentPresenter.Resources>
                                        </ContentPresenter>
                                    </Grid>
                                <ItemsPresenter
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    IsHitTestVisible="{TemplateBinding IsTabStripVisible}"
                                    Opacity="{Binding Path=IsTabStripVisible, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BooleanToOpacityConvert}}" >
                                    <ItemsPresenter.Style>
                                        <Style TargetType="ItemsPresenter">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding TabStripPosition, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:TabNavigationControl}}}" Value="Bottom">
                                                    <Setter Property="Grid.Row" Value="2" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ItemsPresenter.Style>
                                </ItemsPresenter>
                                <ContentControl
                                    x:Name="PART_OldContent"
                                    Grid.Row="1"                               
                                    IsTabStop="False" >
                                    <ContentControl.Margin>
                                        <Thickness>18,12,18,12</Thickness>
                                    </ContentControl.Margin>
                                </ContentControl>
                                <ContentControl
                                    x:Name="PART_SelectedContent"
                                    Grid.Row="1"
                                    HorizontalContentAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    Content="{Binding Path=SelectedTabItem.Content, RelativeSource={RelativeSource TemplatedParent}}"
                                    ContentTemplate="{Binding Path=ItemTemplate, RelativeSource={RelativeSource TemplatedParent}}"
                                    IsTabStop="False" >
                                    <ContentControl.Margin>
                                        <Thickness>18,12,18,12</Thickness>
                                    </ContentControl.Margin>
                                </ContentControl>
                                <ContentControl
                                    x:Name="PART_FrontOldContent"
                                    Grid.Row="1"
                                    IsHitTestVisible="False"
                                    IsTabStop="False" >
                                    <ContentControl.Margin>
                                        <Thickness>18,12,18,12</Thickness>
                                    </ContentControl.Margin>
                                </ContentControl>
                            </Grid>
                        </Border>
                        <Button
                            x:Name="PART_LeftSidButton"
                            Grid.Row="1"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"                           
                            Command="{Binding Path=PreviousCommand, RelativeSource={RelativeSource TemplatedParent}}"
                            Style="{StaticResource SyncfusionTabNavigationFlatButtonStyle}"
                            Visibility="{TemplateBinding NavigationButtonVisibility}">
                            <Button.Margin>
                                <Thickness>0,0,0,0</Thickness>
                            </Button.Margin>
                            <Path Name="LeftNavigationPath"
                                Width="5"
                                Height="8"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Fill="{Binding Path=Foreground,RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                Stretch="Fill"
                                StrokeThickness="0">
                                 <Path.Data>
                                    <PathGeometry>M5.10547 1.10352C5.20312 1.00586 5.25195 0.888672 5.25195 0.751953C5.25195 0.615234 5.20508 0.496094 5.11133 0.394531C5.01367 0.296875 4.89453 0.248047 4.75391 0.248047C4.61719 0.248047 4.5 0.296875 4.40234 0.394531L0.898438 3.89844C0.800781 3.99609 0.751953 4.11328 0.751953 4.25C0.751953 4.38672 0.800781 4.50391 0.898437 4.60156L4.40234 8.10547C4.5 8.20312 4.61719 8.25195 4.75391 8.25195C4.89453 8.25195 5.01367 8.20117 5.11133 8.09961C5.20508 8.00195 5.25195 7.88477 5.25195 7.74805C5.25195 7.61133 5.20312 7.49414 5.10547 7.39648L1.95898 4.25L5.10547 1.10352Z</PathGeometry>
                                </Path.Data>
                            </Path>
                        </Button>
                        <Button
                             x:Name="PART_RightSidButton"
                            Grid.Row="1"                            
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Command="{Binding Path=NextCommand, RelativeSource={RelativeSource TemplatedParent}}"
                            Style="{StaticResource SyncfusionTabNavigationFlatButtonStyle}"
                            Visibility="{TemplateBinding NavigationButtonVisibility}">
                            <Button.Margin>
                                <Thickness>0,0,0,0</Thickness>
                            </Button.Margin>
                            <Path Name="RightNavigationPath"
                                Width="5"
                                Height="8"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Fill="{Binding Path=Foreground,RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                Stretch="Fill"
                                StrokeThickness="0">
                                <Path.Data>
                                    <PathGeometry>M0 8.49805C0 8.36133 0.0488281 8.24414 0.146484 8.14648L3.29297 5L0.146484 1.85352C0.0488281 1.75586 0 1.63867 0 1.50195C0 1.36523 0.046875 1.24805 0.140625 1.15039C0.238281 1.04883 0.357422 0.998047 0.498047 0.998047C0.634766 0.998047 0.751953 1.04688 0.849609 1.14453L4.35352 4.64844C4.45117 4.74609 4.5 4.86328 4.5 5C4.5 5.13672 4.45117 5.25391 4.35352 5.35156L0.849609 8.85547C0.751953 8.95312 0.634766 9.00195 0.498047 9.00195C0.357422 9.00195 0.238281 8.95312 0.140625 8.85547C0.046875 8.75391 0 8.63477 0 8.49805Z</PathGeometry>
                                </Path.Data>    
                            </Path>
                        </Button>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="PART_LeftSidButton" Property="Margin" Value="-15,0,0,0" />
                            <Setter TargetName="PART_RightSidButton" Property="Margin" Value="0,0,-15,0" />
                            <Setter TargetName="PART_LeftSidButton" Property="MinWidth" Value="{StaticResource TouchMode.MinWidth}" />
                            <Setter TargetName="PART_RightSidButton" Property="MinWidth" Value="{StaticResource TouchMode.MinWidth}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="header" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                        <DataTrigger Binding="{Binding ElementName=PART_LeftSidButton,Path=IsPressed}" Value="True">
                            <Setter TargetName="LeftNavigationPath" Property="Height" Value="7"/>
                            <Setter TargetName="LeftNavigationPath" Property="Width" Value="4"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ElementName=PART_RightSidButton,Path=IsPressed}" Value="True">
                            <Setter TargetName="RightNavigationPath" Property="Height" Value="7"/>
                            <Setter TargetName="RightNavigationPath" Property="Width" Value="4"/>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <VirtualizingStackPanel Orientation="Horizontal" Visibility="{Binding Path=TabStripVisibility, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:TabNavigationControl}}}" />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTabNavigationControlStyle}" TargetType="local:TabNavigationControl" />

</ResourceDictionary>
