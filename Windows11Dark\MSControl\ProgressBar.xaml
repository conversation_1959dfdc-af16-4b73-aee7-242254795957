<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF" 
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:Microsoft_Windows_Aero="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
                    >
    
    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="WPFProgressBarStyle" TargetType="{x:Type ProgressBar}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBackground}"/>
        <Setter Property="Background" Value="{StaticResource BorderAlt1}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt1}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness}"/>
        <Setter Property="Height" Value="3"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ProgressBar}">
                    <Grid x:Name="TemplateRoot">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
								<VisualState x:Name="Determinate"/>
								<VisualState x:Name="Indeterminate">
                                    <Storyboard RepeatBehavior="Forever" SpeedRatio="2.3">
										<DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" Storyboard.TargetName="Animation">
											<EasingDoubleKeyFrame KeyTime="0" Value="0.25"/>
                                            <EasingDoubleKeyFrame KeyTime="0:0:1" Value="0.50"/>
											<EasingDoubleKeyFrame KeyTime="0:0:2" Value="0.25"/>
										</DoubleAnimationUsingKeyFrames>
										<PointAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransformOrigin)" Storyboard.TargetName="Animation">
											<EasingPointKeyFrame KeyTime="0" Value="-0.5,0.5"/>
											<EasingPointKeyFrame KeyTime="0:0:1" Value="0.5,0.5"/>
											<EasingPointKeyFrame KeyTime="0:0:2" Value="1.5,0.5"/>
										</PointAnimationUsingKeyFrames>
									</Storyboard>
								</VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                Background="{TemplateBinding Background}"
								Height="1"
                                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" />
                        <Rectangle x:Name="PART_Track" />
                        <Grid x:Name="PART_Indicator" 
                              ClipToBounds="true" 
                              HorizontalAlignment="Left">
                            <Rectangle x:Name="Indicator" 
                                       RadiusX="4"
                                       RadiusY="4"
                                       Fill="{TemplateBinding Foreground}"/>
                            <Rectangle x:Name="Animation"
                                       RadiusX="4"
                                       RadiusY="4"
                                       Fill="{TemplateBinding Foreground}" 
                                       RenderTransformOrigin="0.5,0.5">
                                <Rectangle.RenderTransform>
                                    <TransformGroup>
                                        <ScaleTransform/>
                                        <SkewTransform/>
                                        <RotateTransform/>
                                        <TranslateTransform/>
                                    </TransformGroup>
                                </Rectangle.RenderTransform>
                            </Rectangle>
                        </Grid>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="8"/>
                        </Trigger>                        
                        <Trigger Property="Orientation" Value="Vertical">
                            <Setter Property="LayoutTransform" TargetName="TemplateRoot">
                                <Setter.Value>
                                    <RotateTransform Angle="-90"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsIndeterminate" Value="true">
                            <Setter Property="Visibility" TargetName="Indicator" Value="Collapsed"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Foreground" Value="{StaticResource PrimaryColorLight1}"/>
                <Setter Property="Background" Value="{StaticResource BorderAlt1}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt1}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="{StaticResource BorderAlt1}"/>
                <Setter Property="Background" Value="{StaticResource BorderAlt}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource WPFProgressBarStyle}"  TargetType="{x:Type ProgressBar}"/>
</ResourceDictionary>
