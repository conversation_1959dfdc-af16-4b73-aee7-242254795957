using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using AirMonitor.Core.Constants;
using AirMonitor.Core.Interfaces;
using AirMonitor.Core.Models;
using AirMonitor.Core.Utilities;

namespace AirMonitor.LicenseGenerator.Services;

/// <summary>
/// 加密服务实现
/// 实现RSA+AES混合加密和数字签名功能
/// </summary>
public class CryptoService : ICryptoService
{
    /// <summary>
    /// 生成RSA密钥对
    /// </summary>
    /// <param name="keySize">密钥大小（位）</param>
    /// <returns>密钥对（公钥，私钥）</returns>
    public async Task<(string PublicKey, string PrivateKey)> GenerateRSAKeyPairAsync(int keySize = 2048)
    {
        return await Task.Run(() =>
        {
            using var rsa = RSA.Create(keySize);
            var publicKey = Convert.ToBase64String(rsa.ExportRSAPublicKey());
            var privateKey = Convert.ToBase64String(rsa.ExportRSAPrivateKey());
            return (publicKey, privateKey);
        });
    }

    /// <summary>
    /// 加密数据
    /// </summary>
    /// <param name="data">要加密的数据</param>
    /// <param name="publicKey">RSA公钥</param>
    /// <returns>加密后的数据</returns>
    public async Task<string> EncryptAsync(string data, string publicKey)
    {
        return await Task.Run(() =>
        {
            try
            {
                // 生成AES密钥和IV
                using var aes = Aes.Create();
                aes.KeySize = CryptoConstants.AESKeySize;
                aes.GenerateKey();
                aes.GenerateIV();

                // 使用AES加密数据
                var encryptedData = AESEncryptInternal(data, aes.Key, aes.IV);

                // 使用RSA加密AES密钥
                using var rsa = RSA.Create();
                rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey));
                var encryptedKey = rsa.Encrypt(aes.Key, RSAEncryptionPadding.OaepSHA256);
                var encryptedIV = rsa.Encrypt(aes.IV, RSAEncryptionPadding.OaepSHA256);

                // 组合加密结果
                var result = new
                {
                    EncryptedData = encryptedData,
                    EncryptedKey = Convert.ToBase64String(encryptedKey),
                    EncryptedIV = Convert.ToBase64String(encryptedIV)
                };

                return JsonHelper.Serialize(result, JsonHelper.CompactOptions);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加密失败: {ex.Message}", ex);
            }
        });
    }

    /// <summary>
    /// 解密数据
    /// </summary>
    /// <param name="encryptedData">加密的数据</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <returns>解密后的数据</returns>
    public async Task<string> DecryptAsync(string encryptedData, string privateKey)
    {
        return await Task.Run(() =>
        {
            try
            {
                // 解析加密数据
                var encryptedInfo = JsonHelper.Deserialize<dynamic>(encryptedData);
                if (encryptedInfo == null)
                {
                    throw new InvalidOperationException("无效的加密数据格式");
                }

                var dataElement = ((JsonElement)encryptedInfo).GetProperty("encryptedData").GetString();
                var keyElement = ((JsonElement)encryptedInfo).GetProperty("encryptedKey").GetString();
                var ivElement = ((JsonElement)encryptedInfo).GetProperty("encryptedIV").GetString();

                if (string.IsNullOrEmpty(dataElement) || string.IsNullOrEmpty(keyElement) || string.IsNullOrEmpty(ivElement))
                {
                    throw new InvalidOperationException("加密数据不完整");
                }

                // 使用RSA解密AES密钥和IV
                using var rsa = RSA.Create();
                rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey));
                var aesKey = rsa.Decrypt(Convert.FromBase64String(keyElement), RSAEncryptionPadding.OaepSHA256);
                var aesIV = rsa.Decrypt(Convert.FromBase64String(ivElement), RSAEncryptionPadding.OaepSHA256);

                // 使用AES解密数据
                return AESDecryptInternal(dataElement, aesKey, aesIV);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"解密失败: {ex.Message}", ex);
            }
        });
    }

    /// <summary>
    /// 生成数字签名
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <returns>数字签名</returns>
    public async Task<string> SignAsync(string data, string privateKey)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var rsa = RSA.Create();
                rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey));
                
                var dataBytes = Encoding.UTF8.GetBytes(data);
                var signature = rsa.SignData(dataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
                
                return Convert.ToBase64String(signature);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"签名失败: {ex.Message}", ex);
            }
        });
    }

    /// <summary>
    /// 验证数字签名
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">数字签名</param>
    /// <param name="publicKey">RSA公钥</param>
    /// <returns>true表示验证成功，false表示验证失败</returns>
    public async Task<bool> VerifySignatureAsync(string data, string signature, string publicKey)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var rsa = RSA.Create();
                rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey));
                
                var dataBytes = Encoding.UTF8.GetBytes(data);
                var signatureBytes = Convert.FromBase64String(signature);
                
                return rsa.VerifyData(dataBytes, signatureBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            }
            catch
            {
                return false;
            }
        });
    }

    /// <summary>
    /// 验证许可证签名
    /// </summary>
    /// <param name="license">许可证信息</param>
    /// <param name="publicKey">RSA公钥</param>
    /// <returns>true表示验证成功，false表示验证失败</returns>
    public async Task<bool> VerifyLicenseSignatureAsync(LicenseInfo license, string publicKey)
    {
        try
        {
            // 创建许可证副本并清除签名
            var licenseForSigning = new LicenseInfo
            {
                LicenseId = license.LicenseId,
                ProductName = license.ProductName,
                ProductVersion = license.ProductVersion,
                CustomerName = license.CustomerName,
                CustomerEmail = license.CustomerEmail,
                LicenseType = license.LicenseType,
                AuthorizedFeatures = new List<string>(license.AuthorizedFeatures),
                HardwareFingerprint = license.HardwareFingerprint,
                IssuedDate = license.IssuedDate,
                ExpiryDate = license.ExpiryDate,
                MaxDeviceCount = license.MaxDeviceCount,
                IsTrial = license.IsTrial,
                TrialDays = license.TrialDays,
                CreatedAt = license.CreatedAt,
                UpdatedAt = license.UpdatedAt,
                Signature = string.Empty // 清除签名
            };

            var dataToVerify = JsonHelper.Serialize(licenseForSigning, JsonHelper.CompactOptions);
            return await VerifySignatureAsync(dataToVerify, license.Signature, publicKey);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 计算哈希值
    /// </summary>
    /// <param name="data">要计算哈希的数据</param>
    /// <param name="algorithm">哈希算法（默认SHA256）</param>
    /// <returns>哈希值</returns>
    public async Task<string> ComputeHashAsync(string data, string algorithm = "SHA256")
    {
        return await Task.Run(() =>
        {
            try
            {
                using var hashAlgorithm = algorithm.ToUpper() switch
                {
                    "SHA256" => SHA256.Create(),
                    "SHA1" => SHA1.Create(),
                    "MD5" => MD5.Create(),
                    _ => throw new ArgumentException($"不支持的哈希算法: {algorithm}")
                };

                var dataBytes = Encoding.UTF8.GetBytes(data);
                var hashBytes = hashAlgorithm.ComputeHash(dataBytes);
                return Convert.ToHexString(hashBytes).ToLowerInvariant();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"计算哈希失败: {ex.Message}", ex);
            }
        });
    }

    /// <summary>
    /// 生成随机盐值
    /// </summary>
    /// <param name="length">盐值长度</param>
    /// <returns>随机盐值</returns>
    public string GenerateSalt(int length = 16)
    {
        var salt = new byte[length];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(salt);
        return Convert.ToBase64String(salt);
    }

    /// <summary>
    /// 使用密码派生密钥
    /// </summary>
    /// <param name="password">密码</param>
    /// <param name="salt">盐值</param>
    /// <param name="iterations">迭代次数</param>
    /// <param name="keyLength">密钥长度</param>
    /// <returns>派生的密钥</returns>
    public async Task<byte[]> DeriveKeyAsync(string password, byte[] salt, int iterations = 10000, int keyLength = 32)
    {
        return await Task.Run(() =>
        {
            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, iterations, HashAlgorithmName.SHA256);
            return pbkdf2.GetBytes(keyLength);
        });
    }

    /// <summary>
    /// AES加密
    /// </summary>
    /// <param name="data">要加密的数据</param>
    /// <param name="key">AES密钥</param>
    /// <param name="iv">初始化向量</param>
    /// <returns>加密后的数据</returns>
    public async Task<string> AESEncryptAsync(string data, byte[] key, byte[] iv)
    {
        return await Task.Run(() => AESEncryptInternal(data, key, iv));
    }

    /// <summary>
    /// AES解密
    /// </summary>
    /// <param name="encryptedData">加密的数据</param>
    /// <param name="key">AES密钥</param>
    /// <param name="iv">初始化向量</param>
    /// <returns>解密后的数据</returns>
    public async Task<string> AESDecryptAsync(string encryptedData, byte[] key, byte[] iv)
    {
        return await Task.Run(() => AESDecryptInternal(encryptedData, key, iv));
    }

    /// <summary>
    /// 生成随机IV
    /// </summary>
    /// <param name="length">IV长度</param>
    /// <returns>随机IV</returns>
    public byte[] GenerateIV(int length = 16)
    {
        var iv = new byte[length];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(iv);
        return iv;
    }

    /// <summary>
    /// 安全比较两个字节数组
    /// </summary>
    /// <param name="a">字节数组A</param>
    /// <param name="b">字节数组B</param>
    /// <returns>true表示相等，false表示不相等</returns>
    public bool SecureCompare(byte[] a, byte[] b)
    {
        if (a.Length != b.Length)
        {
            return false;
        }

        var result = 0;
        for (int i = 0; i < a.Length; i++)
        {
            result |= a[i] ^ b[i];
        }

        return result == 0;
    }

    /// <summary>
    /// 清除敏感数据
    /// </summary>
    /// <param name="data">要清除的字节数组</param>
    public void ClearSensitiveData(byte[] data)
    {
        if (data != null)
        {
            Array.Clear(data, 0, data.Length);
        }
    }

    #region 私有方法

    /// <summary>
    /// AES加密内部实现
    /// </summary>
    private string AESEncryptInternal(string data, byte[] key, byte[] iv)
    {
        try
        {
            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;

            using var encryptor = aes.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            using var swEncrypt = new StreamWriter(csEncrypt);

            swEncrypt.Write(data);
            swEncrypt.Close();

            return Convert.ToBase64String(msEncrypt.ToArray());
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"AES加密失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// AES解密内部实现
    /// </summary>
    private string AESDecryptInternal(string encryptedData, byte[] key, byte[] iv)
    {
        try
        {
            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;

            using var decryptor = aes.CreateDecryptor();
            using var msDecrypt = new MemoryStream(Convert.FromBase64String(encryptedData));
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var srDecrypt = new StreamReader(csDecrypt);

            return srDecrypt.ReadToEnd();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"AES解密失败: {ex.Message}", ex);
        }
    }

    #endregion
}
