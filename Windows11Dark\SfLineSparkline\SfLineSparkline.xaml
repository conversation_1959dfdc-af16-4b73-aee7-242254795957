<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:LineSparkline="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="SfLineSparklineAxisLineStyle" TargetType="Line">
        <Setter Property="Stroke" Value="{StaticResource Series10}" />
        <Setter Property="StrokeThickness" Value="2"></Setter>
    </Style>

    <Style x:Key="SfLineSparklineTrackBallSymbolStyle" TargetType="Ellipse">
        <Setter Property="Stroke" Value="{StaticResource Series10}"/>
        <Setter Property="StrokeThickness" Value="1.5"/>
        <Setter Property="Fill" Value="{StaticResource ContentBackground}"></Setter>
        <Setter Property="Height" Value="12"></Setter>
        <Setter Property="Width" Value="12"></Setter>
    </Style>

    <Style x:Key="SfLineSparklineTrackBallLineStyle" TargetType="Line">
        <Setter Property="Stroke" Value="{StaticResource Series10}" />
        <Setter Property="StrokeThickness" Value="1.5"></Setter>
    </Style>

    <Style x:Key="SyncfusionSfLineSparklineStyle"
           TargetType="LineSparkline:SfLineSparkline">
        <Setter Property="Background" Value="Transparent"></Setter>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="BorderThickness" Value="0"></Setter>
        <Setter Property="Interior" Value="{StaticResource Series1}"></Setter>
        <Setter Property="RangeBandBrush" Value="{StaticResource Series10}"></Setter>
        <Setter Property="AxisStyle" Value="{StaticResource SfLineSparklineAxisLineStyle}"></Setter>
        <Setter Property="TrackBallStyle" Value="{StaticResource SfLineSparklineTrackBallSymbolStyle}"></Setter>
        <Setter Property="LineStyle" Value="{StaticResource SfLineSparklineTrackBallLineStyle}"></Setter>
    </Style>

    <Style TargetType="LineSparkline:SfLineSparkline"
           BasedOn="{StaticResource SyncfusionSfLineSparklineStyle}" />

</ResourceDictionary>
