using System.Collections.ObjectModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using AirMonitor.Core.Models;
using Microsoft.Extensions.Logging;

namespace AirMonitor.LicenseGenerator.ViewModels;

/// <summary>
/// 模板管理器视图模型
/// 管理许可证模板相关的界面逻辑
/// </summary>
public partial class TemplateManagerViewModel : ViewModelBase
{
    private readonly ILogger<TemplateManagerViewModel> _logger;

    private LicenseTemplate? _selectedTemplate;
    private string _templateName = string.Empty;
    private string _templateDescription = string.Empty;

    public TemplateManagerViewModel(ILogger<TemplateManagerViewModel> logger)
    {
        _logger = logger;

        InitializeCommands();
        LoadTemplates();

        Title = "模板管理器";
        StatusMessage = "请选择或创建许可证模板";
    }

    #region 属性

    /// <summary>
    /// 模板列表
    /// </summary>
    public ObservableCollection<LicenseTemplate> Templates { get; } = new();

    /// <summary>
    /// 选中的模板
    /// </summary>
    public LicenseTemplate? SelectedTemplate
    {
        get => _selectedTemplate;
        set
        {
            if (SetProperty(ref _selectedTemplate, value))
            {
                OnTemplateSelected(value);
            }
        }
    }

    /// <summary>
    /// 模板名称
    /// </summary>
    public string TemplateName
    {
        get => _templateName;
        set => SetProperty(ref _templateName, value);
    }

    /// <summary>
    /// 模板描述
    /// </summary>
    public string TemplateDescription
    {
        get => _templateDescription;
        set => SetProperty(ref _templateDescription, value);
    }

    #endregion

    #region 命令

    /// <summary>
    /// 新建模板命令
    /// </summary>
    public ICommand NewTemplateCommand { get; private set; } = null!;

    /// <summary>
    /// 编辑模板命令
    /// </summary>
    public ICommand EditTemplateCommand { get; private set; } = null!;

    /// <summary>
    /// 删除模板命令
    /// </summary>
    public ICommand DeleteTemplateCommand { get; private set; } = null!;

    /// <summary>
    /// 保存模板命令
    /// </summary>
    public ICommand SaveTemplateCommand { get; private set; } = null!;

    /// <summary>
    /// 导入模板命令
    /// </summary>
    public ICommand ImportTemplateCommand { get; private set; } = null!;

    /// <summary>
    /// 导出模板命令
    /// </summary>
    public ICommand ExportTemplateCommand { get; private set; } = null!;

    #endregion

    /// <summary>
    /// 初始化命令
    /// </summary>
    private void InitializeCommands()
    {
        NewTemplateCommand = new RelayCommand(NewTemplate);
        EditTemplateCommand = new RelayCommand(EditTemplate, CanEditTemplate);
        DeleteTemplateCommand = new RelayCommand(DeleteTemplate, CanDeleteTemplate);
        SaveTemplateCommand = new AsyncRelayCommand(SaveTemplateAsync, CanSaveTemplate);
        ImportTemplateCommand = new AsyncRelayCommand(ImportTemplateAsync);
        ExportTemplateCommand = new AsyncRelayCommand(ExportTemplateAsync, CanExportTemplate);
    }

    /// <summary>
    /// 加载模板
    /// </summary>
    private void LoadTemplates()
    {
        try
        {
            // TODO: 从文件或数据库加载模板
            // 这里添加一些示例模板
            Templates.Add(new LicenseTemplate
            {
                TemplateId = "TEMP-001",
                TemplateName = "标准版模板",
                Description = "标准版许可证模板，包含基础功能",
                LicenseType = Core.Enums.LicenseType.Standard,
                ValidityDays = 365,
                IsSystemTemplate = true,
                CreatedAt = DateTime.Now
            });

            Templates.Add(new LicenseTemplate
            {
                TemplateId = "TEMP-002",
                TemplateName = "研发版模板",
                Description = "研发版许可证模板，包含所有功能",
                LicenseType = Core.Enums.LicenseType.Development,
                ValidityDays = -1,
                IsSystemTemplate = true,
                CreatedAt = DateTime.Now
            });

            SetInfoStatus($"已加载 {Templates.Count} 个模板");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载模板失败");
            SetErrorStatus("加载模板失败");
        }
    }

    /// <summary>
    /// 模板选择处理
    /// </summary>
    private void OnTemplateSelected(LicenseTemplate? template)
    {
        if (template != null)
        {
            TemplateName = template.TemplateName;
            TemplateDescription = template.Description;
            SetInfoStatus($"已选择模板: {template.TemplateName}");
        }
        else
        {
            TemplateName = string.Empty;
            TemplateDescription = string.Empty;
            ClearStatus();
        }
    }

    /// <summary>
    /// 新建模板
    /// </summary>
    private void NewTemplate()
    {
        try
        {
            SelectedTemplate = null;
            TemplateName = string.Empty;
            TemplateDescription = string.Empty;
            
            SetInfoStatus("请填写新模板信息");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "新建模板失败");
            SetErrorStatus("新建模板失败");
        }
    }

    /// <summary>
    /// 编辑模板
    /// </summary>
    private void EditTemplate()
    {
        try
        {
            if (SelectedTemplate != null)
            {
                SetInfoStatus($"正在编辑模板: {SelectedTemplate.TemplateName}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "编辑模板失败");
            SetErrorStatus("编辑模板失败");
        }
    }

    /// <summary>
    /// 删除模板
    /// </summary>
    private void DeleteTemplate()
    {
        try
        {
            if (SelectedTemplate != null)
            {
                // TODO: 显示确认对话框
                Templates.Remove(SelectedTemplate);
                SelectedTemplate = null;
                SetSuccessStatus("模板已删除");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除模板失败");
            SetErrorStatus("删除模板失败");
        }
    }

    /// <summary>
    /// 保存模板
    /// </summary>
    private async Task SaveTemplateAsync()
    {
        await ExecuteAsync(async () =>
        {
            // TODO: 实现模板保存逻辑
            await Task.Delay(100);
            SetSuccessStatus("模板保存成功");
        }, "正在保存模板...", "模板保存成功");
    }

    /// <summary>
    /// 导入模板
    /// </summary>
    private async Task ImportTemplateAsync()
    {
        await ExecuteAsync(async () =>
        {
            // TODO: 实现模板导入逻辑
            await Task.Delay(100);
            SetInfoStatus("模板导入功能待实现");
        }, "正在导入模板...", "模板导入完成");
    }

    /// <summary>
    /// 导出模板
    /// </summary>
    private async Task ExportTemplateAsync()
    {
        await ExecuteAsync(async () =>
        {
            // TODO: 实现模板导出逻辑
            await Task.Delay(100);
            SetInfoStatus("模板导出功能待实现");
        }, "正在导出模板...", "模板导出完成");
    }

    #region 命令可执行性检查

    /// <summary>
    /// 检查是否可以编辑模板
    /// </summary>
    private bool CanEditTemplate()
    {
        return SelectedTemplate != null;
    }

    /// <summary>
    /// 检查是否可以删除模板
    /// </summary>
    private bool CanDeleteTemplate()
    {
        return SelectedTemplate != null && !SelectedTemplate.IsSystemTemplate;
    }

    /// <summary>
    /// 检查是否可以保存模板
    /// </summary>
    private bool CanSaveTemplate()
    {
        return !string.IsNullOrWhiteSpace(TemplateName);
    }

    /// <summary>
    /// 检查是否可以导出模板
    /// </summary>
    private bool CanExportTemplate()
    {
        return SelectedTemplate != null;
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 激活时调用
    /// </summary>
    public void OnActivated()
    {
        SetInfoStatus("模板管理器已激活");
    }

    /// <summary>
    /// 新建模板（公共方法）
    /// </summary>
    public void NewTemplatePublic()
    {
        NewTemplate();
    }

    /// <summary>
    /// 打开模板
    /// </summary>
    public async void OpenTemplate()
    {
        await ImportTemplateAsync();
    }

    /// <summary>
    /// 保存模板
    /// </summary>
    public async void SaveTemplate()
    {
        await SaveTemplateAsync();
    }

    /// <summary>
    /// 另存为模板
    /// </summary>
    public async void SaveAsTemplate()
    {
        await SaveTemplateAsync();
    }

    #endregion
}
