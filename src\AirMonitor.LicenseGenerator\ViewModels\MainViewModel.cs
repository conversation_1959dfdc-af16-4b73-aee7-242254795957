using System.Collections.ObjectModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using AirMonitor.Core.Constants;
using Microsoft.Extensions.Logging;

namespace AirMonitor.LicenseGenerator.ViewModels;

/// <summary>
/// 主窗口视图模型
/// 管理主界面的导航和状态
/// </summary>
public partial class MainViewModel : ViewModelBase
{
    private readonly ILogger<MainViewModel> _logger;
    private int _selectedTabIndex;
    private string _applicationTitle;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志服务</param>
    public MainViewModel(ILogger<MainViewModel> logger)
    {
        _logger = logger;
        _applicationTitle = ApplicationConstants.LicenseGeneratorName;
        
        InitializeCommands();
        InitializeTabs();
        
        Title = _applicationTitle;
        StatusMessage = "就绪";
        
        _logger.LogInformation("主窗口视图模型已初始化");
    }

    /// <summary>
    /// 应用程序标题
    /// </summary>
    public string ApplicationTitle
    {
        get => _applicationTitle;
        set => SetProperty(ref _applicationTitle, value);
    }

    /// <summary>
    /// 选中的选项卡索引
    /// </summary>
    public int SelectedTabIndex
    {
        get => _selectedTabIndex;
        set
        {
            if (SetProperty(ref _selectedTabIndex, value))
            {
                OnTabChanged(value);
            }
        }
    }

    /// <summary>
    /// 选项卡集合
    /// </summary>
    public ObservableCollection<TabItemViewModel> Tabs { get; } = new();

    /// <summary>
    /// 许可证生成器视图模型
    /// </summary>
    public LicenseGeneratorViewModel? LicenseGeneratorViewModel { get; set; }

    /// <summary>
    /// 许可证验证器视图模型
    /// </summary>
    public LicenseValidatorViewModel? LicenseValidatorViewModel { get; set; }

    /// <summary>
    /// 模板管理器视图模型
    /// </summary>
    public TemplateManagerViewModel? TemplateManagerViewModel { get; set; }

    #region 命令

    /// <summary>
    /// 新建命令
    /// </summary>
    public ICommand NewCommand { get; private set; } = null!;

    /// <summary>
    /// 打开命令
    /// </summary>
    public ICommand OpenCommand { get; private set; } = null!;

    /// <summary>
    /// 保存命令
    /// </summary>
    public ICommand SaveCommand { get; private set; } = null!;

    /// <summary>
    /// 另存为命令
    /// </summary>
    public ICommand SaveAsCommand { get; private set; } = null!;

    /// <summary>
    /// 退出命令
    /// </summary>
    public ICommand ExitCommand { get; private set; } = null!;

    /// <summary>
    /// 关于命令
    /// </summary>
    public ICommand AboutCommand { get; private set; } = null!;

    /// <summary>
    /// 设置命令
    /// </summary>
    public ICommand SettingsCommand { get; private set; } = null!;

    /// <summary>
    /// 帮助命令
    /// </summary>
    public ICommand HelpCommand { get; private set; } = null!;

    #endregion

    /// <summary>
    /// 初始化命令
    /// </summary>
    private void InitializeCommands()
    {
        NewCommand = new RelayCommand(OnNew);
        OpenCommand = new RelayCommand(OnOpen);
        SaveCommand = new RelayCommand(OnSave, CanSave);
        SaveAsCommand = new RelayCommand(OnSaveAs, CanSaveAs);
        ExitCommand = new RelayCommand(OnExit);
        AboutCommand = new RelayCommand(OnAbout);
        SettingsCommand = new RelayCommand(OnSettings);
        HelpCommand = new RelayCommand(OnHelp);
    }

    /// <summary>
    /// 初始化选项卡
    /// </summary>
    private void InitializeTabs()
    {
        Tabs.Add(new TabItemViewModel
        {
            Header = "许可证生成",
            Icon = "🔑",
            ToolTip = "生成新的许可证文件"
        });

        Tabs.Add(new TabItemViewModel
        {
            Header = "许可证验证",
            Icon = "✅",
            ToolTip = "验证现有的许可证文件"
        });

        Tabs.Add(new TabItemViewModel
        {
            Header = "模板管理",
            Icon = "📋",
            ToolTip = "管理许可证模板"
        });

        // 默认选中第一个选项卡
        SelectedTabIndex = 0;
    }

    /// <summary>
    /// 选项卡切换事件处理
    /// </summary>
    /// <param name="tabIndex">选项卡索引</param>
    private void OnTabChanged(int tabIndex)
    {
        try
        {
            var tabName = tabIndex switch
            {
                0 => "许可证生成",
                1 => "许可证验证",
                2 => "模板管理",
                _ => "未知"
            };

            SetInfoStatus($"切换到 {tabName} 选项卡");
            _logger.LogDebug("切换到选项卡: {TabName} (索引: {TabIndex})", tabName, tabIndex);

            // 通知当前选项卡的ViewModel
            switch (tabIndex)
            {
                case 0:
                    LicenseGeneratorViewModel?.OnActivated();
                    break;
                case 1:
                    LicenseValidatorViewModel?.OnActivated();
                    break;
                case 2:
                    TemplateManagerViewModel?.OnActivated();
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换选项卡时发生错误");
            SetErrorStatus("切换选项卡失败");
        }
    }

    #region 命令处理方法

    /// <summary>
    /// 新建处理
    /// </summary>
    private void OnNew()
    {
        try
        {
            // 根据当前选项卡执行相应的新建操作
            switch (SelectedTabIndex)
            {
                case 0:
                    LicenseGeneratorViewModel?.NewLicense();
                    break;
                case 2:
                    TemplateManagerViewModel?.NewTemplatePublic();
                    break;
            }

            SetInfoStatus("新建操作完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "新建操作失败");
            SetErrorStatus("新建操作失败");
        }
    }

    /// <summary>
    /// 打开处理
    /// </summary>
    private void OnOpen()
    {
        try
        {
            // 根据当前选项卡执行相应的打开操作
            switch (SelectedTabIndex)
            {
                case 1:
                    LicenseValidatorViewModel?.OpenLicense();
                    break;
                case 2:
                    TemplateManagerViewModel?.OpenTemplate();
                    break;
            }

            SetInfoStatus("打开操作完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开操作失败");
            SetErrorStatus("打开操作失败");
        }
    }

    /// <summary>
    /// 保存处理
    /// </summary>
    private void OnSave()
    {
        try
        {
            // 根据当前选项卡执行相应的保存操作
            switch (SelectedTabIndex)
            {
                case 0:
                    LicenseGeneratorViewModel?.SaveLicense();
                    break;
                case 2:
                    TemplateManagerViewModel?.SaveTemplate();
                    break;
            }

            SetSuccessStatus("保存成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存操作失败");
            SetErrorStatus("保存操作失败");
        }
    }

    /// <summary>
    /// 另存为处理
    /// </summary>
    private void OnSaveAs()
    {
        try
        {
            // 根据当前选项卡执行相应的另存为操作
            switch (SelectedTabIndex)
            {
                case 0:
                    LicenseGeneratorViewModel?.SaveAsLicense();
                    break;
                case 2:
                    TemplateManagerViewModel?.SaveAsTemplate();
                    break;
            }

            SetSuccessStatus("另存为成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "另存为操作失败");
            SetErrorStatus("另存为操作失败");
        }
    }

    /// <summary>
    /// 退出处理
    /// </summary>
    private void OnExit()
    {
        try
        {
            // 检查是否有未保存的更改
            var hasUnsavedChanges = CheckUnsavedChanges();
            if (hasUnsavedChanges)
            {
                // TODO: 显示确认对话框
                // 这里需要通过事件或服务来显示对话框
            }

            // 触发应用程序退出
            System.Windows.Application.Current.Shutdown();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "退出应用程序时发生错误");
        }
    }

    /// <summary>
    /// 关于处理
    /// </summary>
    private void OnAbout()
    {
        try
        {
            // TODO: 显示关于对话框
            SetInfoStatus($"关于 {ApplicationConstants.LicenseGeneratorName}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示关于信息时发生错误");
            SetErrorStatus("显示关于信息失败");
        }
    }

    /// <summary>
    /// 设置处理
    /// </summary>
    private void OnSettings()
    {
        try
        {
            // TODO: 显示设置对话框
            SetInfoStatus("打开设置");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开设置时发生错误");
            SetErrorStatus("打开设置失败");
        }
    }

    /// <summary>
    /// 帮助处理
    /// </summary>
    private void OnHelp()
    {
        try
        {
            // TODO: 显示帮助文档
            SetInfoStatus("打开帮助文档");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开帮助时发生错误");
            SetErrorStatus("打开帮助失败");
        }
    }

    #endregion

    #region 命令可执行性检查

    /// <summary>
    /// 检查是否可以保存
    /// </summary>
    private bool CanSave()
    {
        return SelectedTabIndex == 0 || SelectedTabIndex == 2;
    }

    /// <summary>
    /// 检查是否可以另存为
    /// </summary>
    private bool CanSaveAs()
    {
        return SelectedTabIndex == 0 || SelectedTabIndex == 2;
    }

    #endregion

    /// <summary>
    /// 检查是否有未保存的更改
    /// </summary>
    private bool CheckUnsavedChanges()
    {
        // TODO: 实现检查未保存更改的逻辑
        return false;
    }

    /// <summary>
    /// 设置子视图模型
    /// </summary>
    public void SetChildViewModels(
        LicenseGeneratorViewModel licenseGeneratorViewModel,
        LicenseValidatorViewModel licenseValidatorViewModel,
        TemplateManagerViewModel templateManagerViewModel)
    {
        LicenseGeneratorViewModel = licenseGeneratorViewModel;
        LicenseValidatorViewModel = licenseValidatorViewModel;
        TemplateManagerViewModel = templateManagerViewModel;
    }
}

/// <summary>
/// 选项卡项视图模型
/// </summary>
public class TabItemViewModel
{
    /// <summary>
    /// 标题
    /// </summary>
    public string Header { get; set; } = string.Empty;

    /// <summary>
    /// 图标
    /// </summary>
    public string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 工具提示
    /// </summary>
    public string ToolTip { get; set; } = string.Empty;
}
