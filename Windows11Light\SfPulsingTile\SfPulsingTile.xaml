<ResourceDictionary  
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:Syncfusion.Windows.Controls.Notification;assembly=Syncfusion.SfHubTile.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <DataTemplate x:Key="ImageTileContentTemplate">
        <Grid>
            <Rectangle Fill="{Binding Background}" />
            <Rectangle Fill="White" Opacity="{Binding Opacity}" />
            <Image
                Width="{Binding ImageWidth}"
                Height="{Binding ImageHeight}"
                HorizontalAlignment="{Binding HorizontalImageAlignment}"
                VerticalAlignment="{Binding VerticalImageAlignment}"
                Source="{Binding Image}"
                Stretch="UniformToFill" />
        </Grid>
    </DataTemplate>

    <Style x:Key="PulsingTileHeaderStyle" TargetType="ContentPresenter">
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Bottom" />
        <Setter Property="TextElement.Foreground" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="Margin" Value="10,5" />
        <Setter Property="TextElement.FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="TextElement.FontWeight" Value="{StaticResource Windows11Light.FontWeightMedium}" />
        <Setter Property="TextElement.FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="TextElement.Foreground" Value="{StaticResource PrimaryBackground}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="PulsingTileTitleStyle" TargetType="ContentPresenter">
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="TextElement.Foreground" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="Margin" Value="5" />
        <Setter Property="TextElement.FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="TextElement.FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="TextElement.FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="TextElement.Foreground" Value="{StaticResource PrimaryForegroundDisabled}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionSfPulsingTileStyle" TargetType="local:SfPulsingTile">
        <Setter Property="Padding" Value="4" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Stretch" />
        <Setter Property="Background" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="HeaderStyle" Value="{StaticResource PulsingTileHeaderStyle}" />
        <Setter Property="TitleStyle" Value="{StaticResource PulsingTileTitleStyle}" />
        <Setter Property="Width" Value="150" />
        <Setter Property="Height" Value="150" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:SfPulsingTile">
                    <Grid x:Name="PART_Layout">
                        <Rectangle
                            x:Name="PointerOveRect"
                            Margin="2"
                            RadiusX="8"
                            RadiusY="8"
                            Fill="Transparent" />
                        <Border
                            x:Name="PART_Border"
                            Margin="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            CornerRadius="8"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <Grid Margin="{TemplateBinding Padding}">
                                <Grid ClipToBounds="true">
                                    <Border x:Name="MaskBorder" Background="{TemplateBinding Background}" CornerRadius="{Binding ElementName=PART_Border,Path=CornerRadius}"/>
                                    <ContentPresenter
                                    x:Name="PART_Content"
                                    HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                    VerticalAlignment="{TemplateBinding VerticalAlignment}" />
                                    <ContentPresenter Content="{TemplateBinding Title}" Style="{TemplateBinding TitleStyle}">
                                    <ContentPresenter.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="TextBlock" />
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                                <ContentPresenter
                                    Content="{TemplateBinding Header}"
                                    ContentTemplate="{TemplateBinding HeaderTemplate}"
                                    ContentTemplateSelector="{TemplateBinding HeaderTemplateSelector}"
                                    Style="{TemplateBinding HeaderStyle}">
                                    <ContentPresenter.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="TextBlock" />
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                                <Grid
                                    x:Name="ImageGrid"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Visibility="Collapsed">
                                    <Image Source="{TemplateBinding ImageSource}" Stretch="None" />
                                </Grid>
                                    <Grid.OpacityMask>
                                        <VisualBrush Visual="{Binding Source={x:Reference MaskBorder}}"/>
                                    </Grid.OpacityMask>
                                </Grid>
                            </Grid>
                        </Border>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="PointerOver" />
                                <VisualState x:Name="PointerPressed" />
                                <VisualState x:Name="Disabled" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Content" Value="{x:Null}">
                            <Setter TargetName="ImageGrid" Property="Visibility" Value="Visible" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="PointerOveRect" Property="Fill" Value="{StaticResource PrimaryButtonBorder}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource PrimaryBackground}" />
                            <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource PrimaryBackground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfPulsingTileStyle}" TargetType="{x:Type local:SfPulsingTile}" />

</ResourceDictionary>
