<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Microsoft_Windows_Aero="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:Microsoft_Windows_Luna="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Luna"
    xmlns:Sync_Resources="clr-namespace:Syncfusion.Windows.Tools.Controls.Resources;assembly=Syncfusion.Tools.WPF"
    xmlns:pc="clr-namespace:System.Windows;assembly=PresentationCore"
    xmlns:pf="clr-namespace:System.Windows;assembly=PresentationFramework"
    xmlns:syncfusion="clr-namespace:Syncfusion.Windows;assembly=Syncfusion.Shared.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:tools_controls="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
    xmlns:tools_resources="clr-namespace:Syncfusion.Windows.Tools.Controls.Resources;assembly=Syncfusion.Tools.WPF"
    xmlns:windows_shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphRepeatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Menu.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/TabControlExt/TabControlExt.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/DocumentContainer/DocumentContainer.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <tools_controls:DockConverter x:Key="dockConverter" />
    <tools_controls:DockTrimmingTemplate x:Key="DockTrimmingTemplate" />
    <tools_controls:TabItemTrimmingTemplate x:Key="TabItemTrimmingTemplate" />
    <tools_controls:SideToCoordinateConverter x:Key="SideToCoordinate" />
    <tools_controls:ActualWidthSubstractBorderConverter x:Key="ActualWidthSubstractBorder" />
    <tools_controls:InvertDockConvertor x:Key="InvertDock" />
    <tools_controls:DockToOrientationConverter x:Key="DockToOrientation" />
    <tools_controls:ThicknessToSizeConverter x:Key="ThicknessToSize" />
    <tools_controls:BoolToVisibilityConverter x:Key="BoolToVisible" />
    <tools_controls:DockAbilityToBoolConverter x:Key="DockAbilityToBool" />
    <tools_controls:HeaderVisibilityConverter x:Key="HeaderVisibilityConverter"/>

    <Duration x:Key="ShadowDuration">0:0:0.4</Duration>
    <sys:TimeSpan x:Key="ShadowTimeSpan">0:0:0.2</sys:TimeSpan>
    <tools_controls:FloatTrimmingTemplate x:Key="FloatTrimmingTemplate" />

    <Style
        x:Key="SyncfusionDockingManagerTabItemCaptionButtonsStyle"
        BasedOn="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
        TargetType="{x:Type ToggleButton}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border
                        Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{StaticResource Windows11Light.ThemeCornerRadiusVariant1}">
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            RecognizesAccessKey="True"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Foreground" Value="{StaticResource IconColor}" />
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Foreground" Value="{StaticResource IconColor}" />
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                        </MultiDataTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColor}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColor}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorHovered}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColor}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsPressed, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorSelected}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsPressed, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorSelected}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsEnabled, RelativeSource={RelativeSource Mode=Self}}" Value="False" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsEnabled, RelativeSource={RelativeSource Mode=Self}}" Value="False" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <Style
        x:Key="SyncfusionDockingManagerTabItemCaptionCloseButtonStyle"
        BasedOn="{StaticResource WPFGlyphButtonStyle}"
        TargetType="{x:Type Button}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{StaticResource Windows11Light.ThemeCornerRadiusVariant1}">
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            RecognizesAccessKey="True"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Foreground" Value="{StaticResource IconColor}" />
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Foreground" Value="{StaticResource IconColor}" />
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                        </MultiDataTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColor}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColor}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorHovered}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColor}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsPressed, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorSelected}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsPressed, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorSelected}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsEnabled, RelativeSource={RelativeSource Mode=Self}}" Value="False" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsEnabled, RelativeSource={RelativeSource Mode=Self}}" Value="False" />
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="True" />
                                <Condition Binding="{Binding IsKeyboardFocusWithin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionDocumentTabItemExtStyle" TargetType="{x:Type tools_controls:TabItemExt}">
        <Setter Property="TextElement.Foreground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="TabItemContextMenuStyle" Value="{StaticResource TabItemContextMenu}" />
        <Setter Property="TabItemContextMenuItemStyle" Value="{StaticResource SyncfusionCustomMenuItemStyle}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Height" Value="24" />
        <Setter Property="HeaderMargin" Value="6 0" />
        <Setter Property="IconMargin" Value="6 0" />
        <Setter Property="BorderThickness" Value="0,0,0,0" />
        <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="Control.VerticalContentAlignment" Value="Stretch" />
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Padding" Value="1 1 1 0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:TabItemExt}">
                    <Grid x:Name="templateRoot" 
                          SnapsToDevicePixels="true">
                        <Rectangle x:Name="TabSeparator"
                            HorizontalAlignment="Right"
                            Width="1"
                            SnapsToDevicePixels="True"
                            Visibility="Visible"    
                            Stroke="{StaticResource BorderAlt}" 
                            StrokeThickness="1"
                            Margin="0,4,0,4">
                            <Rectangle.RenderTransform>
                                <TranslateTransform X="1"/>
                            </Rectangle.RenderTransform>
                        </Rectangle>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition x:Name="topRow"/>
                                <RowDefinition x:Name="bottomRow" Height="Auto"/>
                            </Grid.RowDefinitions>
                        <Border
                        x:Name="Bd"
                        Margin="{TemplateBinding Margin}"
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        SnapsToDevicePixels="True"
                        ToolTip="{Binding Path=ItemToolTip, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabItemExt}}, FallbackValue=''}"
                            CornerRadius="4,4,0,0">
                            <DockPanel x:Name="PART_DockPanel" LastChildFill="True">
                                <Button
                                x:Name="PART_CloseButton"
                                Width="{Binding ElementName=Content, Path=ActualHeight}"
                                Height="{Binding ElementName=Content, Path=ActualHeight}"
                                Margin="0,0,6,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                BorderThickness="0"
                                Command="tools_controls:TabControlCommands.CloseTabItem"
                                DockPanel.Dock="Right"
                                Focusable="False"
                                Style="{StaticResource SyncfusionDockingManagerTabItemCaptionCloseButtonStyle}"
                                Tag="TabItem"
                                Visibility="Collapsed">
                                    <Button.Content>
                                        <Path
                                        x:Name="CloseButtonPath1"
                                        Width="8"
                                        Height="8"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stroke="{Binding Foreground, ElementName=PART_CloseButton}"
                                        StrokeThickness="1"
                                        SnapsToDevicePixels="True"
                                        Stretch="Fill" 
                                        Data="M1 11L6 6M6 6L11 1M6 6L11 11M6 6L1 1"/>
                                    </Button.Content>
                                </Button>
                                <ToggleButton
                                x:Name="PART_PinButton"
                                Width="{Binding ElementName=Content, Path=ActualHeight}"
                                Height="{Binding ElementName=Content, Path=ActualHeight}"
                                Margin="0,0,6,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                BorderThickness="0"
                                Command="tools_controls:TabControlCommands.PinTabItem"
                                CommandParameter="{Binding IsChecked, RelativeSource={RelativeSource Self}}"
                                DockPanel.Dock="Right"
                                Style="{StaticResource SyncfusionDockingManagerTabItemCaptionButtonsStyle}"
                                Visibility="{Binding ShowPin, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabItemExt}}, Converter={StaticResource TabControlExtCloseButtonTypeToVisibilityConverter}}">
                                    <ToggleButton.Content>
                                        <Path
                                        x:Name="PinButtonPath2"
                                        Width="10"
                                        Height="10"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Fill="{Binding Foreground, ElementName=PART_PinButton}"
                                        Stretch="Fill" 
                                        Data="M7.74219 3.74609C7.82552 3.62109 7.86719 3.48308 7.86719 3.33203C7.86719 3.23308 7.84766 3.13672 7.80859 3.04297C7.77214 2.94922 7.71875 2.86719 7.64844 2.79688L5.1875 0.332031C5.11979 0.264328 5.03906 0.210938 4.94531 0.171875C4.85417 0.132812 4.75911 0.113281 4.66016 0.113281C4.51172 0.113281 4.375 0.153641 4.25 0.234375C4.125 0.3125 4.03255 0.419266 3.97266 0.554688L3.10547 2.46484C3.07682 2.52734 3.03255 2.57031 2.97266 2.59375L0.65625 3.51953C0.609375 3.53777 0.571615 3.5677 0.542969 3.60938C0.514322 3.65105 0.5 3.69661 0.5 3.74609C0.5 3.81641 0.52474 3.8763 0.574219 3.92578L2.14844 5.5L0 7.64453V8H0.351562L2.5 5.85156L4.07422 7.42578C4.1237 7.47527 4.18229 7.5 4.25 7.5C4.30729 7.5 4.35677 7.48308 4.39844 7.44922C4.44271 7.41536 4.47266 7.37239 4.48828 7.32031L5.17188 5.04688C5.18229 5.01042 5.20052 4.97917 5.22656 4.95312C5.25261 4.92448 5.28255 4.90364 5.31641 4.89062L7.40625 4.01953C7.54688 3.96223 7.65886 3.87109 7.74219 3.74609ZM4.51953 0.652344C4.5612 0.626297 4.60807 0.613281 4.66016 0.613281C4.72526 0.613281 4.78255 0.638016 4.83203 0.6875L7.29688 3.14844C7.34375 3.19531 7.36719 3.25391 7.36719 3.32422C7.36719 3.3763 7.35286 3.42317 7.32422 3.46484C7.29818 3.50391 7.26172 3.53386 7.21484 3.55469L5.12109 4.42969C5.01953 4.47395 4.92969 4.53777 4.85156 4.62109C4.77604 4.70442 4.72266 4.79948 4.69141 4.90625L4.12891 6.77734L1.19531 3.83984L3.15625 3.05859C3.24739 3.02214 3.32812 2.97136 3.39844 2.90625C3.46875 2.83855 3.52344 2.76042 3.5625 2.67188L4.42969 0.761719C4.45052 0.714844 4.48047 0.678391 4.51953 0.652344Z"/>
                                    </ToggleButton.Content>
                                </ToggleButton>
                                <Image
                                x:Name="PART_Image"
                                Width="{TemplateBinding tools_controls:TabControlExt.ImageWidth}"
                                Height="{TemplateBinding tools_controls:TabControlExt.ImageHeight}"
                                Margin="{TemplateBinding IconMargin}"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                DockPanel.Dock="{Binding Path=(tools_controls:TabControlExt.ImageAlignment), Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TabControlExtImageAlignmentToDockConverter}}"
                                SnapsToDevicePixels="True"
                                Source="{TemplateBinding tools_controls:TabControlExt.Image}"
                                Stretch="Fill" />
                                <ContentPresenter
                                x:Name="Content"
                                Margin="{TemplateBinding HeaderMargin}"
                                HorizontalAlignment="{Binding HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                                VerticalAlignment="{Binding VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                                Content="{TemplateBinding HeaderedContentControl.Header}"
                                ContentSource="Header"
                                ContentTemplate="{TemplateBinding HeaderedContentControl.HeaderTemplate}"
                                RecognizesAccessKey="False"
                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                                TextElement.Foreground="{TemplateBinding Foreground}">
                                    <ContentPresenter.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                                <ContentPresenter
                                x:Name="PART_EditHeader"
                                Margin="{TemplateBinding HeaderMargin}"
                                HorizontalAlignment="{Binding HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                                VerticalAlignment="{Binding VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                                Content="{TemplateBinding HeaderedContentControl.Header}"
                                ContentSource="Header"
                                RecognizesAccessKey="True"
                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                                TextElement.FontWeight="{TemplateBinding FontWeight}"
                                TextElement.Foreground="{TemplateBinding Foreground}"
                                Visibility="Collapsed" />
                            </DockPanel>
                        </Border>
                            <Grid
                                Grid.Row="1"
                                Name="Curve"
                                Visibility="Collapsed"
                                Background="{TemplateBinding Background}">
                                <Path
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Bottom"
                                    Data="M0,4 C 2,4 4,2 4,0 L 4,4 0,4z"
                                    Fill="{TemplateBinding Background}"
                                    Stroke="{TemplateBinding Background}"
                                    StrokeLineJoin="Round">
                                    <Path.RenderTransform>
                                        <TranslateTransform X="-4"/>
                                    </Path.RenderTransform>
                                </Path>
                                <Path
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Bottom"
                                    Data="M0,4 C 2,4 4,2 4,0"
                                    StrokeThickness="1"
                                    Stroke="{TemplateBinding BorderBrush}"
                                    StrokeLineJoin="Round">
                                    <Path.RenderTransform>
                                        <TranslateTransform X="-4"/>
                                    </Path.RenderTransform>
                                </Path>
                                <Path
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Bottom"
                                    Data="M0,0 C 0,2 2,4 4,4 L 0,4 0,0z"
                                    Fill="{TemplateBinding Background}"
                                    Stroke="{TemplateBinding Background}"
                                    StrokeLineJoin="Round">
                                    <Path.RenderTransform>
                                        <TranslateTransform X="4"/>
                                    </Path.RenderTransform>
                                </Path>
                                <Path
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Bottom"
                                    Data="M0,0 C 0,2 2,4 4,4"
                                    StrokeThickness="1"
                                    Stroke="{TemplateBinding BorderBrush}"
                                    StrokeLineJoin="Round">
                                    <Path.RenderTransform>
                                        <TranslateTransform X="4"/>
                                    </Path.RenderTransform>
                                </Path>
                            </Grid>
                        </Grid>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="HeaderMargin" Value="12,9,12,8" />
                            <Setter Property="IconMargin" Value="12,10,0,12" />
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter TargetName="PART_CloseButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_CloseButton" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_PinButton" Property="MinWidth" Value="{StaticResource TouchMode.MinSize}" />
                            <Setter TargetName="PART_PinButton" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_CloseButton" Property="VerticalAlignment" Value="Center" />
                        </Trigger>
                        <DataTrigger Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" >
                            <Setter TargetName="PART_EditHeader" Property="ContentTemplate" Value="{Binding EditHeaderTemplate, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}}" />
                        </DataTrigger>
                        <Trigger Property="tools_controls:TabControlExt.ImageHeight" Value="{x:Static sys:Double.NaN}">
                            <Setter TargetName="PART_Image" Property="Stretch" Value="Uniform" />
                        </Trigger>
                        <Trigger Property="tools_controls:TabControlExt.ImageWidth" Value="{x:Static sys:Double.NaN}">
                            <Setter TargetName="PART_Image" Property="Stretch" Value="Uniform" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="tools_controls:TabControlExt.ImageHeight" Value="{x:Static sys:Double.NaN}" />
                                <Condition Property="tools_controls:TabControlExt.ImageWidth" Value="{x:Static sys:Double.NaN}" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Image" Property="Stretch" Value="None" />
                        </MultiTrigger>

                        <Trigger SourceName="Bd" Property="IsMouseOver" Value="true">
                            <Setter Property="Background" Value="{Binding Path=TabItemHoverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Transparent}" />
                            <Setter Property="BorderBrush" Value="{Binding Path=TabItemHoverBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Transparent}" />
                            <Setter Property="Foreground" Value="{Binding Path=TabItemHoverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Transparent}" />
                            <Setter Property="Opacity" TargetName="TabSeparator" Value="0"/>
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" Value="{Binding Path=TabItemSelectedBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Transparent}" />
                            <Setter Property="BorderBrush" Value="{Binding Path=TabItemSelectedBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Transparent}" />
                            <Setter Property="Foreground" Value="{Binding Path=TabItemSelectedForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Transparent}" />
                            <Setter Property="FontWeight" Value="{Binding Path=SelectedItemFontWeight, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=SemiBold}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1110}"/>
                            <Setter Property="Padding" Value="0 0 0 1"/>
                            <Setter Property="Margin" TargetName="PART_DockPanel" Value="0,0,0,-4.5"/>
                            <Setter Property="Opacity" TargetName="TabSeparator" Value="0"/>
                            <Setter Property="Visibility" TargetName="Curve" Value="Visible"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource Mode=Self}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundInactive}" />
                            <Setter Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
                            <Setter Property="FontWeight" Value="{Binding Path=SelectedItemFontWeight, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=SemiBold}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="Both" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="Individual" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="IndividualOnMouseOver" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Hidden" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="Extended" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Hidden" />
                        </MultiDataTrigger>
                        <Trigger SourceName="PART_PinButton" Property="IsChecked" Value="True">
                            <Setter TargetName="PinButtonPath2" Property="Height" Value="10" />
                            <Setter TargetName="PinButtonPath2" Property="Width" Value="10" />
                            <Setter TargetName="PinButtonPath2" Property="Data" Value="M7.87289 3.685C7.95763 3.5582 8 3.4182 8 3.26498C8 3.1646 7.98014 3.06686 7.94042 2.97177C7.90334 2.87667 7.84906 2.79346 7.77756 2.72214L5.27507 0.221893C5.20622 0.153214 5.12413 0.0990601 5.0288 0.059433C4.93612 0.0198135 4.83946 0 4.73883 0C4.58788 0 4.44886 0.040947 4.32175 0.122833C4.19464 0.20208 4.10063 0.310387 4.03972 0.447746L3.15789 2.38534C3.12877 2.44874 3.08375 2.49232 3.02284 2.5161L0.667328 3.45517C0.619663 3.47366 0.581264 3.50404 0.552135 3.54631C0.523006 3.58858 0.508442 3.6348 0.508442 3.685C0.508442 3.75632 0.5336 3.81708 0.583912 3.86726L2.18471 5.4641L0 7.63942V8H0.357498L2.54221 5.82071L4.143 7.41753C4.19331 7.46773 4.2529 7.49281 4.32175 7.49281C4.38 7.49281 4.43032 7.47565 4.47269 7.44131C4.51771 7.40697 4.54816 7.36339 4.56405 7.31055L5.25919 5.00446C5.26978 4.96747 5.28831 4.93578 5.3148 4.90936C5.34128 4.88031 5.37173 4.85918 5.40616 4.84596L7.53128 3.96236C7.67428 3.90424 7.78815 3.81178 7.87289 3.685Z"/>
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="IndividualOnMouseOver" />
                                <Condition Binding="{Binding Path=IsMouseOver, ElementName=Bd}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="Extended" />
                                <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=CloseButtonType, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Common}" Value="Extended" />
                                <Condition Binding="{Binding Path=IsMouseOver, ElementName=Bd}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Visible" />
                        </MultiDataTrigger>
                        <DataTrigger Binding="{Binding Path=(tools_controls:TabControlExt.Image), RelativeSource={RelativeSource Self}}" Value="{x:Null}">
                            <Setter TargetName="PART_Image" Property="UIElement.Visibility" Value="Collapsed" />
                        </DataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Top}" Value="Left" />
                                <Condition Binding="{Binding Path=RotateTextWhenVertical, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=false}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="Bd" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="90" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Top}" Value="Right" />
                                <Condition Binding="{Binding Path=RotateTextWhenVertical, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=false}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="Bd" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="-90" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Top}" Value="Left" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="180" />
                                </Setter.Value>
                            </Setter>
                        </MultiDataTrigger>
                        <DataTrigger Binding="{Binding Path=TabStripPlacement, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TabControlExt}}, FallbackValue=Top}" Value="Bottom">
                            <Setter TargetName="topRow" Property="Height" Value="Auto"/>
                            <Setter TargetName="bottomRow" Property="Height" Value="*"/>
                            <Setter TargetName="Bd" Property="Grid.Row" Value="1"/>
                            <Setter TargetName="Curve" Property="Grid.Row" Value="0"/>
                            <Setter TargetName="Curve" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="1" ScaleY="-1"/>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Padding" Value="1 0 1 1" />
                            <Setter Property="BorderThickness" Value="0 1 0 0"/>
                            <Setter TargetName="Curve" Property="RenderTransformOrigin" Value="0.5,0.5"/>
                            <Setter TargetName="Bd" Property="CornerRadius" Value="0,0,4,4"/>
                        </DataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True"/>
                                <Condition Binding="{Binding Path=TabControlExt.TabStripPlacement, RelativeSource={RelativeSource Mode=Self}, FallbackValue=Top}" Value="Bottom"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" Value="1,0,1,1"/>
                            <Setter Property="Padding" Value="0,1,0,0"/>
                            <Setter Property="Margin" TargetName="PART_DockPanel" Value="0,-4.5,0,0"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition  Binding="{Binding Path=(tools_controls:TabControlExt.IsEditing), RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_EditHeader" Property="Visibility" Value="Visible" />
                            <Setter TargetName="Content" Property="Visibility" Value="Collapsed" />
                        </MultiDataTrigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter Property="BorderThickness" TargetName="Bd" Value="{StaticResource Windows11Light.BorderThickness1110}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="FrameworkElement.FocusVisualStyle">
            <Setter.Value>
                <Style TargetType="IFrameworkInputElement">
                    <Setter Property="Control.Template">
                        <Setter.Value>
                            <x:Null />
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition  Binding="{Binding Path=IsVisible, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:TabControlExt.UseCustomEditableTemplate), RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:TabControlExt.IsEditing), RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="HeaderTemplate" Value="{Binding Path=(tools_controls:TabControlExt.CustomEditableTemplate), RelativeSource={RelativeSource Self}}" />
            </MultiDataTrigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionDocumentTabItemExtStyle}" TargetType="{x:Type tools_controls:TabItemExt}" />

    <Style
        x:Key="SyncfusionDocumentTabControlExtStyle"
        BasedOn="{StaticResource SyncfusionTabControlExtStyle}"
        TargetType="{x:Type tools_controls:TabControlExt}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="TabItemHeaderStyle" Value="Curve" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionDocumentTabControlExtStyle}" TargetType="{x:Type tools_controls:TabControlExt}" />

    <DataTemplate x:Key="DockHintPath">
        <Grid x:Name="OuterGrid">
            <Grid.Resources>
                <Style TargetType="Path">
                    <Style.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.3" />
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Grid.Resources>
            <Path
                x:Name="OuterImgBg"
                Margin="0.5"
                Data="F1M257.5,446.5L286.5,446.5C287.052,446.5,287.5,446.053,287.5,445.5L287.5,416.5C287.5,415.947,287.052,415.5,286.5,415.5L257.5,415.5C256.948,415.5,256.5,415.947,256.5,416.5L256.5,445.5C256.5,446.053,256.948,446.5,257.5,446.5"
                Fill="{StaticResource ContentBackground}"
                Stretch="Fill" />
            <Path
                x:Name="OuterImgBd"
                Data="F1M257.5,416C257.224,416,257,416.225,257,416.5L257,445.5C257,445.775,257.224,446,257.5,446L286.5,446C286.776,446,287,445.775,287,445.5L287,416.5C287,416.225,286.776,416,286.5,416z M286.5,447L257.5,447C256.673,447,256,446.327,256,445.5L256,416.5C256,415.673,256.673,415,257.5,415L286.5,415C287.327,415,288,415.673,288,416.5L288,445.5C288,446.327,287.327,447,286.5,447"
                Fill="{StaticResource BorderAlt}"
                Stretch="Fill" />
            <Path
                x:Name="OuterImgInnerBg"
                Margin="4.021,4.009,3.979,15.991"
                Data="F1M283.021,320.947L261.021,320.947L261.021,312.947L283.021,312.947z M260.021,321.947L284.021,321.947L284.021,309.947L260.021,309.947z"
                Fill="{StaticResource PrimaryBackground}"
                Stretch="Fill" />
            <Path
                x:Name="OuterImgInnerLayer"
                Height="8"
                Margin="5.021,7.009,4.979,0"
                VerticalAlignment="Top"
                Data="F1M261.021,312.947L283.021,312.947L283.021,320.947L261.021,320.947z"
                Fill="{StaticResource PrimaryBackgroundOpacity3}"
                Stretch="Fill" />
            <Path
                x:Name="OuterImgArrow"
                Height="4"
                Margin="12.309,0,11.691,5.991"
                VerticalAlignment="Bottom"
                Data="F1M276.3086,331.9473L268.3086,331.9473L272.3086,327.9473z"
                Fill="{StaticResource IconColor}"
                Stretch="Fill" />
        </Grid>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Tag, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type ContentControl}}}" Value="True">
                <Setter TargetName="OuterImgBg" Property="Fill" Value="{StaticResource ContentBackgroundAlt2}" />
                <Setter TargetName="OuterImgBd" Property="Fill" Value="{StaticResource BorderAlt}" />
                <Setter TargetName="OuterImgInnerBg" Property="Fill" Value="{StaticResource PrimaryBackgroundOpacity1}" />
                <Setter TargetName="OuterImgInnerLayer" Property="Fill" Value="{StaticResource PrimaryColorLight1}" />
                <Setter TargetName="OuterImgArrow" Property="Fill" Value="{StaticResource IconColorHovered}" />
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <DataTemplate x:Key="DocumentHintPath">
        <Grid>
            <Grid.Resources>
                <Style TargetType="Path">
                    <Style.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.3" />
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Grid.Resources>
            <Path
                x:Name="ImgBg"
                Margin="0.5"
                Data="F1M257.5,340.5L286.5,340.5C287.052,340.5,287.5,340.947,287.5,341.5L287.5,370.5C287.5,371.053,287.052,371.5,286.5,371.5L257.5,371.5C256.948,371.5,256.5,371.053,256.5,370.5L256.5,341.5C256.5,340.947,256.948,340.5,257.5,340.5"
                Fill="{StaticResource ContentBackground}"
                Stretch="Fill" />
            <Path
                x:Name="ImgBd"
                Data="F1M257.5,341C257.224,341,257,341.225,257,341.5L257,370.5C257,370.775,257.224,371,257.5,371L286.5,371C286.776,371,287,370.775,287,370.5L287,341.5C287,341.225,286.776,341,286.5,341z M286.5,372L257.5,372C256.673,372,256,371.327,256,370.5L256,341.5C256,340.673,256.673,340,257.5,340L286.5,340C287.327,340,288,340.673,288,341.5L288,370.5C288,371.327,287.327,372,286.5,372"
                Fill="{StaticResource BorderAlt}"
                Stretch="Fill" />
            <Path
                x:Name="ImgInnerBg"
                Margin="4"
                Data="F1M283,367L261,367L261,347L283,347z M260,368L284,368L284,344L260,344z"
                Fill="{StaticResource PrimaryBackground}"
                Stretch="Fill" />
            <Path
                x:Name="ImgInnerLayer1"
                Height="10"
                Margin="5,0,5,5"
                VerticalAlignment="Bottom"
                Data="F1M261,357L283,357L283,367L261,367z"
                Fill="{StaticResource ContentBackground}"
                Stretch="Fill" />
            <Path
                x:Name="ImgInnerLayer2"
                Margin="5,7,5,15"
                Data="F1M261,347L283,347L283,357L261,357z"
                Fill="{StaticResource PrimaryBackgroundOpacity3}"
                Stretch="Fill" />
            <Path
                x:Name="ImgDividerDots"
                Height="1"
                Margin="7,0,6,15"
                VerticalAlignment="Bottom"
                Data="M18,0 L19,0 19,1.0000002 18,1.0000002 z M16,0 L17,0 17,1.0000002 16,1.0000002 z M14,0 L15,0 15,1.0000002 14,1.0000002 z M12,0 L13,0 13,1.0000002 12,1.0000002 z M10,0 L11,0 11,1.0000002 10,1.0000002 z M8,0 L9,0 9,1.0000002 8,1.0000002 z M6,0 L7,0 7,1.0000002 6,1.0000002 z M4,0 L5,0 5,1.0000002 4,1.0000002 z M2,0 L3,0 3,1.0000002 2,1.0000002 z M0,0 L1,0 1,1.0000002 0,1.0000002 z"
                Fill="{StaticResource PrimaryBackground}"
                Stretch="Fill" />
        </Grid>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Tag, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type ContentControl}}}" Value="True">
                <Setter TargetName="ImgBg" Property="Fill" Value="{StaticResource ContentBackgroundAlt2}" />
                <Setter TargetName="ImgBd" Property="Fill" Value="{StaticResource BorderAlt}" />
                <Setter TargetName="ImgInnerBg" Property="Fill" Value="{StaticResource PrimaryBackgroundOpacity1}" />
                <Setter TargetName="ImgInnerLayer1" Property="Fill" Value="{StaticResource ContentBackgroundAlt2}" />
                <Setter TargetName="ImgInnerLayer2" Property="Fill" Value="{StaticResource PrimaryColorLight1}" />
                <Setter TargetName="ImgDividerDots" Property="Fill" Value="{StaticResource PrimaryBackgroundOpacity1}" />
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <DataTemplate x:Key="CenterDockHintPath">
        <Grid>
            <Grid.Resources>
                <Style TargetType="Path">
                    <Style.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.3" />
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Grid.Resources>
            <Path
                x:Name="CenterImgBg"
                Margin="0.5"
                Data="F1M256.5,407.5L256.5,378.5C256.5,377.947,256.948,377.5,257.5,377.5L286.5,377.5C287.052,377.5,287.5,377.947,287.5,378.5L287.5,407.5C287.5,408.053,287.052,408.5,286.5,408.5L257.5,408.5C256.948,408.5,256.5,408.053,256.5,407.5"
                Fill="{StaticResource ContentBackground}"
                Stretch="Fill" />
            <Path
                x:Name="CenterImgBd"
                Data="F1M257.5,378C257.224,378,257,378.225,257,378.5L257,407.5C257,407.775,257.224,408,257.5,408L286.5,408C286.776,408,287,407.775,287,407.5L287,378.5C287,378.225,286.776,378,286.5,378z M286.5,409L257.5,409C256.673,409,256,408.327,256,407.5L256,378.5C256,377.673,256.673,377,257.5,377L286.5,377C287.327,377,288,377.673,288,378.5L288,407.5C288,408.327,287.327,409,286.5,409"
                Fill="{StaticResource BorderAlt}"
                Stretch="Fill" />
            <Path
                x:Name="CenterImgInnerBg"
                Margin="4.083,4,4.117,4"
                Data="F1M282.85,404L261.117,404L261.117,384L282.85,384z M260.083,405L283.883,405L283.883,381L260.083,381z"
                Fill="{StaticResource PrimaryBackground}"
                Stretch="Fill" />
            <Path
                x:Name="CenterImgInnerLayer"
                Margin="5.117,7,5.15,5"
                Data="F1M261.117,384L282.85,384L282.85,404L261.117,404z"
                Fill="{StaticResource PrimaryBackgroundOpacity3}"
                Stretch="Fill" />
        </Grid>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Tag, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type ContentControl}}}" Value="True">
                <Setter TargetName="CenterImgBg" Property="Fill" Value="{StaticResource ContentBackgroundAlt2}" />
                <Setter TargetName="CenterImgBd" Property="Fill" Value="{StaticResource BorderAlt}" />
                <Setter TargetName="CenterImgInnerBg" Property="Fill" Value="{StaticResource PrimaryBackgroundOpacity1}" />
                <Setter TargetName="CenterImgInnerLayer" Property="Fill" Value="{StaticResource PrimaryColorLight1}" />
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <ControlTemplate x:Key="DockingDockPreviewCenterButtonVS2008Template" TargetType="{x:Type ContentControl}">
        <Grid x:Name="CenterPreviewButtonGrid">
            <Grid>
                <Grid x:Name="DockProviderBackground" Visibility="Collapsed">
                    <Path
                        Margin="0.5"
                        Data="M38,0 L77,0 77,22.999999 91,36.999999 115,36.999999 115,75.999999 91,75.999999 77,89.999999 77,114 38,114 38,89.999999 24,75.999999 0,75.999999 0,36.999999 24,36.999999 38,22.999999 z"
                        Fill="{StaticResource ContentBackground}"
                        Opacity="0.4"
                        Stretch="Fill" />
                    <Path
                        Data="M39,1.0000005 L39,23.707001 24.707001,38.000001 1.0000005,38.000001 1.0000005,76.000001 24.707001,76.000001 39,90.293 39,114 77,114 77,90.293 91.292999,76.000001 115,76.000001 115,38.000001 91.292999,38.000001 77,23.707001 77,1.0000005 z M38,0 L78,0 78,23.293 91.707001,37.000001 116,37.000001 116,77.000001 91.707001,77.000001 78,90.707001 78,115 38,115 38,90.707001 24.292999,77.000001 0,77.000001 0,37.000001 24.292999,37.000001 38,23.293 z"
                        Fill="{StaticResource BorderAlt}"
                        Stretch="Fill" />
                </Grid>
                <Grid x:Name="DocumentProviderBackground" Visibility="{Binding IsDocumentTargeted, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}, Converter={StaticResource BoolToVisible}}">
                    <Path
                        Margin="0.5"
                        Data="F1M365.5,412.5L365.5,373.5L305.5,373.5L291.5,359.5L291.5,301.5L252.5,301.5L252.5,359.5L238.5,373.5L178.5,373.5L178.5,412.5L238.5,412.5L252.5,426.5L252.5,486.5L291.5,486.5L291.5,426.5L305.5,412.5z"
                        Fill="{StaticResource ContentBackground}"
                        Opacity="0.4"
                        Stretch="Fill" />
                    <Path
                        Data="F1M253,486L291,486L291,426.293L305.293,412L365,412L365,374L305.293,374L291,359.707L291,302L253,302L253,359.707L238.707,374L179,374L179,412L238.707,412L253,426.293z M292,487L252,487L252,426.707L238.293,413L178,413L178,373L238.293,373L252,359.293L252,301L292,301L292,359.293L305.707,373L366,373L366,413L305.707,413L292,426.707z"
                        Fill="{StaticResource BorderAlt}"
                        Stretch="Fill" />
                </Grid>
            </Grid>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition x:Name="documentRow1" Height="5" />
                    <RowDefinition x:Name="documentRow2" Height="33" />
                    <RowDefinition x:Name="dockRow1" Height="10" />
                    <RowDefinition Height="33" />
                    <RowDefinition Height="10" />
                    <RowDefinition Height="33" />
                    <RowDefinition Height="10" />
                    <RowDefinition Height="33" />
                    <RowDefinition x:Name="dockRow2" Height="10" />
                    <RowDefinition x:Name="documentRow3" Height="33" />
                    <RowDefinition x:Name="documentRow4" Height="5" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition x:Name="documentCol1" Width="5" />
                    <ColumnDefinition x:Name="documentCol2" Width="33" />
                    <ColumnDefinition x:Name="dockCol1" Width="10" />
                    <ColumnDefinition Width="33" />
                    <ColumnDefinition Width="10" />
                    <ColumnDefinition Width="33" />
                    <ColumnDefinition Width="10" />
                    <ColumnDefinition Width="33" />
                    <ColumnDefinition x:Name="dockCol2" Width="10" />
                    <ColumnDefinition x:Name="documentCol3" Width="33" />
                    <ColumnDefinition x:Name="documentCol4" Width="5" />
                </Grid.ColumnDefinitions>
                <Grid
                    x:Name="TopOuterImg"
                    Grid.Row="1"
                    Grid.Column="5"
                    Width="32"
                    Height="32"
                    tools_controls:DockPreviewManagerVS2005.ProviderAction="Top">
                    <Grid.IsEnabled>
                        <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="Top">
                            <Binding Path="IsDocumentTargeted" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="DockAbility" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="ParentDockingManager.DocContainer.Items.Count" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                        </MultiBinding>
                    </Grid.IsEnabled>
                    <ContentControl ContentTemplate="{StaticResource DockHintPath}">
                        <ContentControl.Tag>
                            <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="Top">
                                <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            </MultiBinding>
                        </ContentControl.Tag>
                    </ContentControl>
                </Grid>
                <Grid
                    x:Name="TopImg"
                    Grid.Row="3"
                    Grid.Column="5"
                    Width="32"
                    Height="32"
                    tools_controls:DockPreviewManagerVS2005.ProviderAction="DocumentTop">
                    <Grid.IsEnabled>
                        <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="DocumentTop">
                            <Binding Path="IsDocumentTargeted" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="DockAbility" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="ParentDockingManager.DocContainer.Items.Count" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                        </MultiBinding>
                    </Grid.IsEnabled>
                    <ContentControl ContentTemplate="{StaticResource DocumentHintPath}">
                        <ContentControl.Tag>
                            <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="DocumentTop">
                                <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            </MultiBinding>
                        </ContentControl.Tag>
                    </ContentControl>
                </Grid>
                <Grid
                    x:Name="LeftOuterImg"
                    Grid.Row="5"
                    Grid.Column="1"
                    Width="32"
                    Height="32"
                    tools_controls:DockPreviewManagerVS2005.ProviderAction="Left">
                    <Grid.IsEnabled>
                        <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="Left">
                            <Binding Path="IsDocumentTargeted" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="DockAbility" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="ParentDockingManager.DocContainer.Items.Count" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                        </MultiBinding>
                    </Grid.IsEnabled>
                    <ContentControl ContentTemplate="{StaticResource DockHintPath}">
                        <ContentControl.Tag>
                            <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="Left">
                                <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            </MultiBinding>
                        </ContentControl.Tag>
                        <ContentControl.LayoutTransform>
                            <RotateTransform Angle="270" />
                        </ContentControl.LayoutTransform>
                    </ContentControl>
                </Grid>
                <Grid
                    x:Name="LeftImg"
                    Grid.Row="5"
                    Grid.Column="3"
                    Width="32"
                    Height="32"
                    tools_controls:DockPreviewManagerVS2005.ProviderAction="DocumentLeft">
                    <Grid.IsEnabled>
                        <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="DocumentLeft">
                            <Binding Path="IsDocumentTargeted" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="DockAbility" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="ParentDockingManager.DocContainer.Items.Count" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                        </MultiBinding>
                    </Grid.IsEnabled>
                    <ContentControl ContentTemplate="{StaticResource DocumentHintPath}">
                        <ContentControl.Tag>
                            <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="DocumentLeft">
                                <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            </MultiBinding>
                        </ContentControl.Tag>
                        <ContentControl.LayoutTransform>
                            <RotateTransform Angle="270" />
                        </ContentControl.LayoutTransform>
                    </ContentControl>
                </Grid>
                <Grid
                    x:Name="CenterImg"
                    Grid.Row="5"
                    Grid.Column="5"
                    Width="32"
                    Height="32"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    tools_controls:DockPreviewManagerVS2005.ProviderAction="Center">
                    <Grid.IsEnabled>
                        <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="Center">
                            <Binding Path="IsDocumentTargeted" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="DockAbility" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="ParentDockingManager.DocContainer.Items.Count" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                        </MultiBinding>
                    </Grid.IsEnabled>
                    <ContentControl ContentTemplate="{StaticResource CenterDockHintPath}">
                        <ContentControl.Tag>
                            <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="Tabbed">
                                <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            </MultiBinding>
                        </ContentControl.Tag>
                    </ContentControl>
                </Grid>
                <Grid
                    x:Name="RightOuterImg"
                    Grid.Row="5"
                    Grid.Column="9"
                    Width="32"
                    Height="32"
                    tools_controls:DockPreviewManagerVS2005.ProviderAction="Right">
                    <Grid.IsEnabled>
                        <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="Right">
                            <Binding Path="IsDocumentTargeted" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="DockAbility" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="ParentDockingManager.DocContainer.Items.Count" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                        </MultiBinding>
                    </Grid.IsEnabled>
                    <ContentControl ContentTemplate="{StaticResource DockHintPath}">
                        <ContentControl.Tag>
                            <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="Right">
                                <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            </MultiBinding>
                        </ContentControl.Tag>
                        <ContentControl.LayoutTransform>
                            <RotateTransform Angle="90" />
                        </ContentControl.LayoutTransform>
                    </ContentControl>
                </Grid>
                <Grid
                    x:Name="RightImg"
                    Grid.Row="5"
                    Grid.Column="7"
                    Width="32"
                    Height="32"
                    tools_controls:DockPreviewManagerVS2005.ProviderAction="DocumentRight">
                    <Grid.IsEnabled>
                        <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="DocumentRight">
                            <Binding Path="IsDocumentTargeted" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="DockAbility" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="ParentDockingManager.DocContainer.Items.Count" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                        </MultiBinding>
                    </Grid.IsEnabled>
                    <ContentControl ContentTemplate="{StaticResource DocumentHintPath}">
                        <ContentControl.Tag>
                            <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="DocumentRight">
                                <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            </MultiBinding>
                        </ContentControl.Tag>
                        <ContentControl.LayoutTransform>
                            <RotateTransform Angle="90" />
                        </ContentControl.LayoutTransform>
                    </ContentControl>
                </Grid>
                <Grid
                    x:Name="BottomOuterImg"
                    Grid.Row="9"
                    Grid.Column="5"
                    Width="32"
                    Height="32"
                    tools_controls:DockPreviewManagerVS2005.ProviderAction="Bottom">
                    <Grid.IsEnabled>
                        <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="Bottom">
                            <Binding Path="IsDocumentTargeted" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="DockAbility" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="ParentDockingManager.DocContainer.Items.Count" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                        </MultiBinding>
                    </Grid.IsEnabled>
                    <ContentControl ContentTemplate="{StaticResource DockHintPath}">
                        <ContentControl.Tag>
                            <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="Bottom">
                                <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            </MultiBinding>
                        </ContentControl.Tag>
                        <ContentControl.LayoutTransform>
                            <RotateTransform Angle="180" />
                        </ContentControl.LayoutTransform>
                    </ContentControl>
                </Grid>
                <Grid
                    x:Name="BottomImg"
                    Grid.Row="7"
                    Grid.Column="5"
                    Width="32"
                    Height="32"
                    tools_controls:DockPreviewManagerVS2005.ProviderAction="DocumentBottom">
                    <Grid.IsEnabled>
                        <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="DocumentBottom">
                            <Binding Path="IsDocumentTargeted" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="DockAbility" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            <Binding Path="ParentDockingManager.DocContainer.Items.Count" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                        </MultiBinding>
                    </Grid.IsEnabled>
                    <ContentControl ContentTemplate="{StaticResource DocumentHintPath}">
                        <ContentControl.Tag>
                            <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="DocumentBottom">
                                <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                            </MultiBinding>
                        </ContentControl.Tag>
                        <ContentControl.LayoutTransform>
                            <RotateTransform Angle="180" />
                        </ContentControl.LayoutTransform>
                    </ContentControl>
                </Grid>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=IsDocumentTargeted, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}}" Value="False">
                <Setter TargetName="TopOuterImg" Property="Grid.Row" Value="3" />
                <Setter TargetName="TopOuterImg" Property="Grid.Column" Value="5" />
                <Setter TargetName="RightOuterImg" Property="Grid.Row" Value="5" />
                <Setter TargetName="RightOuterImg" Property="Grid.Column" Value="7" />
                <Setter TargetName="BottomOuterImg" Property="Grid.Row" Value="7" />
                <Setter TargetName="BottomOuterImg" Property="Grid.Column" Value="5" />
                <Setter TargetName="LeftOuterImg" Property="Grid.Row" Value="5" />
                <Setter TargetName="LeftOuterImg" Property="Grid.Column" Value="3" />
                <Setter TargetName="DockProviderBackground" Property="Visibility" Value="Visible" />
                <Setter TargetName="documentRow1" Property="Height" Value="0" />
                <Setter TargetName="documentRow2" Property="Height" Value="0" />
                <Setter TargetName="documentRow3" Property="Height" Value="0" />
                <Setter TargetName="documentRow4" Property="Height" Value="0" />
                <Setter TargetName="documentCol1" Property="Width" Value="0" />
                <Setter TargetName="documentCol2" Property="Width" Value="0" />
                <Setter TargetName="documentCol3" Property="Width" Value="0" />
                <Setter TargetName="documentCol4" Property="Width" Value="0" />
                <Setter TargetName="dockRow1" Property="Height" Value="5" />
                <Setter TargetName="dockRow2" Property="Height" Value="5" />
                <Setter TargetName="dockCol1" Property="Width" Value="5" />
                <Setter TargetName="dockCol2" Property="Width" Value="5" />
                <Setter TargetName="BottomImg" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="LeftImg" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="TopImg" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="RightImg" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=CanDocument, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}}" Value="False">
                <Setter TargetName="BottomImg" Property="IsEnabled" Value="False" />
                <Setter TargetName="CenterImg" Property="IsEnabled" Value="False" />
                <Setter TargetName="LeftImg" Property="IsEnabled" Value="False" />
                <Setter TargetName="TopImg" Property="IsEnabled" Value="False" />
                <Setter TargetName="RightImg" Property="IsEnabled" Value="False" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=CanDock, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}}" Value="False">
                <Setter TargetName="BottomOuterImg" Property="IsEnabled" Value="False" />
                <Setter TargetName="LeftOuterImg" Property="IsEnabled" Value="False" />
                <Setter TargetName="TopOuterImg" Property="IsEnabled" Value="False" />
                <Setter TargetName="RightOuterImg" Property="IsEnabled" Value="False" />
            </DataTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="DockingDockPreviewTopButtonVS2008Template" TargetType="{x:Type ContentControl}">
        <Grid
            x:Name="TopImg"
            Width="32"
            Height="32"
            tools_controls:DockPreviewManagerVS2005.ProviderAction="GlobalTop">
            <ContentControl ContentTemplate="{StaticResource DockHintPath}">
                <ContentControl.Tag>
                    <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="GlobalTop">
                        <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                    </MultiBinding>
                </ContentControl.Tag>
            </ContentControl>
        </Grid>
    </ControlTemplate>

    <ControlTemplate x:Key="DockingDockPreviewLeftButtonVS2008Template" TargetType="{x:Type ContentControl}">
        <Grid
            x:Name="LeftImg"
            Width="32"
            Height="32"
            tools_controls:DockPreviewManagerVS2005.ProviderAction="GlobalLeft">
            <ContentControl ContentTemplate="{StaticResource DockHintPath}">
                <ContentControl.Tag>
                    <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="GlobalLeft">
                        <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                    </MultiBinding>
                </ContentControl.Tag>
                <ContentControl.LayoutTransform>
                    <RotateTransform Angle="270" />
                </ContentControl.LayoutTransform>
            </ContentControl>
        </Grid>
    </ControlTemplate>

    <ControlTemplate x:Key="DockingDockPreviewRightButtonVS2008Template" TargetType="{x:Type ContentControl}">
        <Grid
            x:Name="RightImg"
            Width="32"
            Height="32"
            tools_controls:DockPreviewManagerVS2005.ProviderAction="GlobalRight">
            <ContentControl ContentTemplate="{StaticResource DockHintPath}">
                <ContentControl.Tag>
                    <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="GlobalRight">
                        <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                    </MultiBinding>
                </ContentControl.Tag>
                <ContentControl.LayoutTransform>
                    <RotateTransform Angle="90" />
                </ContentControl.LayoutTransform>
            </ContentControl>
        </Grid>
    </ControlTemplate>

    <ControlTemplate x:Key="DockingDockPreviewBottomButtonVS2008Template" TargetType="{x:Type ContentControl}">
        <Grid
            x:Name="BottomImg"
            Width="32"
            Height="32"
            tools_controls:DockPreviewManagerVS2005.ProviderAction="GlobalBottom">
            <ContentControl ContentTemplate="{StaticResource DockHintPath}">
                <ContentControl.Tag>
                    <MultiBinding Converter="{StaticResource DockAbilityToBool}" ConverterParameter="GlobalBottom">
                        <Binding Path="ActiveSide" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:DockPreviewMainButtonVS2005}}" />
                    </MultiBinding>
                </ContentControl.Tag>
                <ContentControl.LayoutTransform>
                    <RotateTransform Angle="180" />
                </ContentControl.LayoutTransform>
            </ContentControl>
        </Grid>
    </ControlTemplate>

    <Style x:Key="SyncfusionSplitterStyle" TargetType="{x:Type tools_controls:Splitter}">
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource BorderAlt1}" />
            </Trigger>
        </Style.Triggers>
        <Setter Property="MinWidth" Value="{Binding Path=DockingManager.SplitterSize, RelativeSource={RelativeSource Self}}" />
        <Setter Property="MinHeight" Value="{Binding Path=DockingManager.SplitterSize, RelativeSource={RelativeSource Self}}" />
        <Setter Property="Background" Value="{Binding Path=DockingManager.SplitterBackground, RelativeSource={RelativeSource Self}}" />
        <Setter Property="Margin" Value="0" />
        <Setter Property="Cursor" Value="SizeNS" />
        <Setter Property="AdornerTemplate">
            <Setter.Value>
                <ControlTemplate>
                    <StackPanel x:Name="SplitterStackPanel" Orientation="Horizontal">
                        <Border
                            MinWidth="{TemplateBinding MinWidth}"
                            MinHeight="{TemplateBinding MinHeight}"
                            Background="{StaticResource PrimaryBackground}" />
                    </StackPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="tools_controls:SplitterAdorner.Orientation" Value="Horizontal">
                            <Setter TargetName="SplitterStackPanel" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="-90" />
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:Splitter}">
                    <Border
                        x:Name="splitterBorder"
                        MinWidth="{TemplateBinding MinWidth}"
                        MinHeight="{TemplateBinding MinHeight}"
                        Margin="0"
                        Background="{TemplateBinding Background}" />
                </ControlTemplate>

            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSplitterStyle}" TargetType="{x:Type tools_controls:Splitter}" />

    <ControlTemplate x:Key="SidePanelMenuItemTemplate" TargetType="{x:Type MenuItem}">
        <Grid>
            <Border
                     Name="Bg"
                     Background="{TemplateBinding Background}"
                     BorderBrush="Transparent"
                     BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                     CornerRadius="{StaticResource Windows11Light.ThemeCornerRadiusVariant1}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition
                            Width="Auto"
                            MinWidth="24"
                            SharedSizeGroup="MenuItemIconColumnGroup" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" SharedSizeGroup="MenuItemIGTColumnGroup" />
                        <ColumnDefinition Width="17" />
                    </Grid.ColumnDefinitions>
                    <TextBlock
                            Name="text"
                            Margin="6,3,6,3"
                            Foreground="{TemplateBinding Foreground}"
                            Text="{Binding Path=(FrameworkElement.DataContext).(tools_controls:DockingManager.Header), RelativeSource={RelativeSource Self}}" />
                </Grid>
            </Border>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsHighlighted" Value="true">
                <Setter TargetName="Bg" Property="Background" Value="{StaticResource PopupHoveredBackground}" />
                <Setter TargetName="Bg" Property="BorderBrush" Value="{StaticResource PopupHoveredBackground}" />
                <Setter Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style
        x:Key="MaterialSideContextMenuStyle"
        BasedOn="{StaticResource WPFContextMenuStyle}"
        TargetType="{x:Type ContextMenu}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="ItemContainerStyle">
            <Setter.Value>
                <Style BasedOn="{StaticResource WPFMenuItemStyle}" TargetType="{x:Type MenuItem}">
                    <Setter Property="Focusable" Value="True" />
                    <Setter Property="Template" Value="{StaticResource SidePanelMenuItemTemplate}" />

                    <Style.Triggers>
                        <Trigger Property="Role" Value="SubmenuItem">
                            <Setter Property="Template" Value="{StaticResource SidePanelMenuItemTemplate}"/>
                        </Trigger>

                        <Trigger Property="Role" Value="SubmenuHeader">
                            <Setter Property="Template" Value="{StaticResource SidePanelMenuItemTemplate}"/>
                        </Trigger>

                        <Trigger Property="Role" Value="TopLevelItem">
                            <Setter Property="Template" Value="{StaticResource SidePanelMenuItemTemplate}"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="SyncfusionCustomContextMenuStyle"
        BasedOn="{StaticResource WPFContextMenuStyle}"
        TargetType="{x:Type tools_controls:CustomContextMenu}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="ItemContainerStyle" Value="{StaticResource SyncfusionCustomMenuItemStyle}" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionCustomContextMenuStyle}" TargetType="{x:Type tools_controls:CustomContextMenu}" />

    <Style
        x:Key="SyncfusionDockingContextMenuStyle"
        BasedOn="{StaticResource WPFContextMenuStyle}"
        TargetType="{x:Type ContextMenu}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Focusable" Value="True" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionDockingContextMenuStyle}" TargetType="{x:Type ContextMenu}" />

    <ControlTemplate x:Key="CaptionButtonsTemplate" TargetType="{x:Type ToggleButton}">
        <StackPanel>
            <Border
                x:Name="ButtonBorder"
                Width="{TemplateBinding Width}"
                Height="{TemplateBinding Height}"
                Background="{TemplateBinding Background}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="0"
                CornerRadius="{StaticResource Windows11Light.CornerRadius4}">
                <ContentPresenter
                    Margin="{TemplateBinding Padding}"
                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                    RecognizesAccessKey="True"
                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                    <ContentPresenter.Resources>
                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                    </ContentPresenter.Resources>
                </ContentPresenter>
            </Border>
        </StackPanel>
        <ControlTemplate.Triggers>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Float" />
                    <Condition Binding="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.IsTouchEnabled), RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Dock" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.IsTouchEnabled), RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Dock" />
                    <Condition Binding="{Binding Path=IsTemplateParenKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockHeaderPresenter}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                <Setter TargetName="ButtonBorder" Property="Background" Value="Transparent" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="Transparent" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Dock" />
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockHeaderPresenter}}}" Value="True" />
                    <Condition Binding="{Binding Path=IsTemplateParenKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockHeaderPresenter}}}" Value="True" />
                    <Condition Binding="{Binding Path=DockingManager.EnableMouseHoverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockingManager}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Float" />
                    <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                <Setter TargetName="ButtonBorder" Property="Background" Value="Transparent" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="Transparent" />
            </MultiDataTrigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter Property="Foreground" Value="{StaticResource IconColorHovered}" />
            </Trigger>
            <Trigger Property="IsKeyboardFocused" Value="True">
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter Property="Foreground" Value="{StaticResource PrimaryForegroundOpacity1}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
                <Setter Property="Foreground" Value="{StaticResource IconColorSelected}" />
            </Trigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Dock" />
                    <Condition Binding="{Binding Path=IsTemplateParenKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockHeaderPresenter}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource PrimaryColorDark1}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="ButtonBorder" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}"/>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Float" />
                    <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource PrimaryColorDark1}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsPressed, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Dock" />
                    <Condition Binding="{Binding Path=IsTemplateParenKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockHeaderPresenter}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PrimaryForegroundOpacity1}" />
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource PrimaryColorDark2}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsPressed, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Float" />
                    <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PrimaryForegroundOpacity1}" />
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource PrimaryColorDark2}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
            </MultiDataTrigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="ButtonBorder" Property="Background" Value="Transparent" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="Transparent" />
                <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="NativeCaptionButtonsTemplate" TargetType="{x:Type ToggleButton}">
        <StackPanel>
            <Border
                x:Name="ButtonBorder"
                Width="{TemplateBinding Width}"
                Height="{TemplateBinding Height}"
                Background="{TemplateBinding Background}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="0"
                CornerRadius="{StaticResource Windows11Light.CornerRadius4}">
                <ContentPresenter
                    Margin="{TemplateBinding Padding}"
                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                    RecognizesAccessKey="True"
                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                    <ContentPresenter.Resources>
                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                    </ContentPresenter.Resources>
                </ContentPresenter>
            </Border>
        </StackPanel>
        <ControlTemplate.Triggers>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Float" />
                    <Condition Binding="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.IsTouchEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Dock" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.IsTouchEnabled), RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Dock" />
                    <Condition Binding="{Binding Path=IsTemplateParenKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockHeaderPresenter}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                <Setter TargetName="ButtonBorder" Property="Background" Value="Transparent" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="Transparent" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Dock" />
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockHeaderPresenter}}}" Value="True" />
                    <Condition Binding="{Binding Path=IsTemplateParenKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockHeaderPresenter}}}" Value="True" />
                    <Condition Binding="{Binding Path=DockingManager.EnableMouseHoverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockingManager}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Float" />
                    <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                <Setter TargetName="ButtonBorder" Property="Background" Value="Transparent" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="Transparent" />
            </MultiDataTrigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter Property="Foreground" Value="{StaticResource IconColorHovered}" />
            </Trigger>
            <Trigger Property="IsKeyboardFocused" Value="True">
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
                <Setter Property="Foreground" Value="{StaticResource IconColorSelected}" />
            </Trigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Dock" />
                    <Condition Binding="{Binding Path=IsTemplateParenKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockHeaderPresenter}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource PrimaryColorDark1}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="ButtonBorder" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}"/>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Float" />
                    <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource PrimaryColorDark1}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsPressed, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Dock" />
                    <Condition Binding="{Binding Path=IsTemplateParenKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockHeaderPresenter}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PrimaryForegroundOpacity1}" />
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource PrimaryColorDark2}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsPressed, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Tag, RelativeSource={RelativeSource Mode=Self}}" Value="Float" />
                    <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource PrimaryForegroundOpacity1}" />
                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource PrimaryColorDark2}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
            </MultiDataTrigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="ButtonBorder" Property="Background" Value="Transparent" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="Transparent" />
                <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="CaptionButtonStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource IconColor}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="WindowChrome.IsHitTestVisibleInChrome" Value="True"/>
    </Style>

    <Style x:Key="{x:Type tools_controls:FloatWindowBorder}" TargetType="{x:Type tools_controls:FloatWindowBorder}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="ParentWindow" Value="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="MinWindowWidth" Value="50.0" />
        <Setter Property="MinWindowHeight" Value="25" />
        <Setter Property="Height" Value="Auto" />
        <Setter Property="Width" Value="Auto" />
        <Style.Triggers>
            <Trigger Property="BorderMode" Value="Header">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:FloatWindowBorder}">
                            <Border
                                x:Name="borderTop"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="0">
                                <Border.ContextMenu>
                                    <tools_controls:CustomContextMenu x:Name="PART_ContextMenu" Focusable="True" />
                                </Border.ContextMenu>
                                <DockPanel x:Name="FloatWindowHeader" LastChildFill="True">
                                    <ToggleButton
                                        x:Name="button"
                                        Width="16"
                                        Height="16"
                                        Margin="0,0,8,0"
                                        Padding="0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Command="tools_controls:FloatWindowBorder.ChangeStateCommand"
                                        DockPanel.Dock="Right"
                                        FocusVisualStyle="{x:Null}"
                                        Focusable="True"
                                        Style="{StaticResource CaptionButtonStyle}"
                                        Tag="Float"
                                        Template="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.CloseButtonTemplate), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}">
                                        <ToggleButton.ToolTip>
                                            <ToolTip x:Name="CloseButtonTooltip">
                                                <ToolTip.Resources>
                                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                                </ToolTip.Resources>
                                                <TextBlock x:Name="CloseButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=CloseButtonTooltipText}" />
                                            </ToolTip>
                                        </ToggleButton.ToolTip>
                                        <ToggleButton.Content>
                                            <Path
                                                x:Name="CloseButtonPath1"
                                                Width="8"
                                                Height="8"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Stroke="{Binding Foreground, ElementName=button}"
                                                StrokeThickness="1"
                                                SnapsToDevicePixels="True"
                                                Stretch="Fill" 
                                                Data="M1 11L6 6M6 6L11 1M6 6L11 11M6 6L1 1"/>
                                        </ToggleButton.Content>
                                    </ToggleButton>
                                    <ContentPresenter
                                        x:Name="FloatWindowHeaderContent"
                                        Margin="4,0,0,0"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Content="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.Header), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}"
                                        ContentTemplate="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.HeaderTemplate), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}"
                                        ContentTemplateSelector="{StaticResource FloatTrimmingTemplate}"
                                        IsHitTestVisible="True"
                                        TextBlock.Foreground="{StaticResource ContentForeground}">
                                        <ContentPresenter.Resources>
                                            <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                        </ContentPresenter.Resources>
                                    </ContentPresenter>
                                </DockPanel>
                            </Border>

                            <ControlTemplate.Triggers>
                                <DataTrigger Binding="{Binding Path=IsTouchEnabled, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockingManager}}}" Value="True">
                                    <Setter Property="FontSize" Value="18" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.CanDocument), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False">
                                    <Setter TargetName="PART_ContextMenu" Property="IsEnabledTabbedMenuItem" Value="False" />
                                </DataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.CanClose), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False" />
                                        <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False" />
                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
                                    <Setter TargetName="button" Property="IsEnabled" Value="False" />
                                </MultiDataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.CloseTabs), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" Value="CloseActive" />
                                        <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanClose), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False" />
                                        <Condition Binding="{Binding Path=(FrameworkElement.DataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
                                    <Setter TargetName="button" Property="IsEnabled" Value="False" />
                                </MultiDataTrigger>
                                <DataTrigger Binding="{Binding Path=(PrimaryElement).(tools_controls:DockingManager.NoHeader), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="PART_ContextMenu" Property="Visibility" Value="Visible" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Path=IsMultiHostsContainer, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="FloatWindowHeaderContent" Property="Visibility" Value="Hidden" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False">
                                    <Setter TargetName="FloatWindowHeader" Property="Background" Value="{StaticResource PrimaryBackground}" />
                                </DataTrigger>

                                <Trigger SourceName="FloatWindowHeader" Property="Visibility" Value="Visible">
                                    <Setter TargetName="FloatWindowHeader" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.FloatWindowHeaderBackground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                    <Setter TargetName="FloatWindowHeaderContent" Property="TextBlock.Foreground" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.FloatWindowHeaderForeground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </Trigger>
                                <DataTrigger Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="FloatWindowHeader" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.FloatWindowSelectedHeaderBackground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                    <Setter TargetName="FloatWindowHeaderContent" Property="TextBlock.Foreground" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.FloatWindowSelectedHeaderForeground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                    <Setter TargetName="button" Property="Background" Value="Transparent" />
                                    <Setter TargetName="button" Property="BorderBrush" Value="Transparent" />
                                    <Setter TargetName="button" Property="Foreground" Value="{StaticResource PrimaryForeground}" />
                                </DataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.IsEnableHotTracking), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False" />

                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="FloatWindowHeader" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.FloatWindowMouseOverHeaderBackground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                    <Setter TargetName="FloatWindowHeaderContent" Property="TextBlock.Foreground" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.FloatWindowMouseOverHeaderForeground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </MultiDataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.IsEnableHotTracking), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />

                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="FloatWindowHeader" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.FloatWindowSelectedHeaderBackground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                    <Setter TargetName="FloatWindowHeaderContent" Property="TextBlock.Foreground" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.FloatWindowSelectedHeaderForeground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </MultiDataTrigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="BorderMode" Value="LeftTop">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:FloatWindowBorder}">
                            <Border
                                x:Name="LeftTopHeaderBorder"
                                Margin="0"
                                Background="{StaticResource BorderAlt}"
                                BorderThickness="0" />
                            <ControlTemplate.Triggers>
                                <Trigger SourceName="LeftTopHeaderBorder" Property="Visibility" Value="Visible">
                                    <Setter TargetName="LeftTopHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatWindowTopBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </Trigger>

                                <DataTrigger Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="LeftTopHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatWindowSelectedTopBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />

                                </DataTrigger>
                                <DataTrigger Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="LeftTopHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatMouseOverTopBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </DataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.IsEnableHotTracking), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False" />
                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="LeftTopHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatMouseOverTopBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </MultiDataTrigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="BorderMode" Value="RightTop">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:FloatWindowBorder}">
                            <Border
                                x:Name="RightTopHeaderBorder"
                                Margin="0"
                                Background="{StaticResource BorderAlt}"
                                BorderThickness="0" />
                            <ControlTemplate.Triggers>
                                <Trigger SourceName="RightTopHeaderBorder" Property="Visibility" Value="Visible">
                                    <Setter TargetName="RightTopHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.RightFloatWindowTopBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </Trigger>

                                <DataTrigger Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="RightTopHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.RightFloatWindowSelectedTopBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />

                                </DataTrigger>
                                <DataTrigger Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="RightTopHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.RightFloatMouseOverTopBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />

                                </DataTrigger>

                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.IsEnableHotTracking), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False" />
                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="RightTopHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.RightFloatMouseOverTopBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </MultiDataTrigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="BorderMode" Value="Left">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:FloatWindowBorder}">
                            <Border
                                x:Name="LeftHeaderBorder"
                                Margin="0"
                                Background="{StaticResource BorderAlt}"
                                BorderThickness="0" />
                            <ControlTemplate.Triggers>

                                <Trigger SourceName="LeftHeaderBorder" Property="Visibility" Value="Visible">
                                    <Setter TargetName="LeftHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatWindowBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </Trigger>

                                <DataTrigger Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="LeftHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatWindowSelectedBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </DataTrigger>

                                <DataTrigger Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="LeftHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatMouseOverBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </DataTrigger>

                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.IsEnableHotTracking), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False" />
                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="LeftHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatMouseOverBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </MultiDataTrigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="BorderMode" Value="Right">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:FloatWindowBorder}">
                            <Border
                                x:Name="RightHeaderBorder"
                                Margin="0"
                                Background="{StaticResource BorderAlt}"
                                BorderThickness="0" />
                            <ControlTemplate.Triggers>
                                <Trigger SourceName="RightHeaderBorder" Property="Visibility" Value="Visible">
                                    <Setter TargetName="RightHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatWindowBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </Trigger>

                                <DataTrigger Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="RightHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatWindowSelectedBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />

                                </DataTrigger>

                                <DataTrigger Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="RightHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatMouseOverBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </DataTrigger>

                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.IsEnableHotTracking), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False" />
                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="RightHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatMouseOverBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </MultiDataTrigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="BorderMode" Value="LeftBottom">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:FloatWindowBorder}">
                            <Border
                                x:Name="LeftBottomHeaderBorder"
                                Margin="0"
                                Background="{StaticResource BorderAlt}"
                                BorderThickness="0" />
                            <ControlTemplate.Triggers>
                                <Trigger SourceName="LeftBottomHeaderBorder" Property="Visibility" Value="Visible">
                                    <Setter TargetName="LeftBottomHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatWindowBottomBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </Trigger>
                                <DataTrigger Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="LeftBottomHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatWindowSelectedBottomBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="LeftBottomHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatMouseOverBottomBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </DataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.IsEnableHotTracking), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False" />
                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="LeftBottomHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatMouseOverBottomBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </MultiDataTrigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="BorderMode" Value="Bottom">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:FloatWindowBorder}">
                            <Border
                                x:Name="BottomHeaderBorder"
                                Margin="0"
                                Background="{StaticResource BorderAlt}"
                                BorderThickness="0" />
                            <ControlTemplate.Triggers>
                                <Trigger SourceName="BottomHeaderBorder" Property="Visibility" Value="Visible">
                                    <Setter TargetName="BottomHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.FloatWindowBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </Trigger>
                                <DataTrigger Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="BottomHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatWindowSelectedBottomBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="BottomHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.LeftFloatMouseOverBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </DataTrigger>

                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.IsEnableHotTracking), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False" />
                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="BottomHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.BottomFloatMouseOverBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </MultiDataTrigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="BorderMode" Value="RightBottom">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:FloatWindowBorder}">
                            <Border
                                x:Name="RightBottomHeaderBorder"
                                Margin="0"
                                Background="{StaticResource BorderAlt}"
                                BorderThickness="0" />
                            <ControlTemplate.Triggers>
                                <Trigger SourceName="RightBottomHeaderBorder" Property="Visibility" Value="Visible">
                                    <Setter TargetName="RightBottomHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.RightFloatWindowBottomBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </Trigger>
                                <DataTrigger Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="RightBottomHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.RightFloatWindowSelectedBottomBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True">
                                    <Setter TargetName="RightBottomHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.RightFloatMouseOverBottomBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </DataTrigger>

                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.IsEnableHotTracking), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="True" />
                                        <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:IWindow}}}" Value="False" />
                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="RightBottomHeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:IWindow.DockingManager).(tools_controls:DockingManager.RightFloatMouseOverBottomBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" />
                                </MultiDataTrigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="{x:Type tools_controls:DraggedElementPopup}">
        <Setter Property="ContentControl.SnapsToDevicePixels" Value="true" />
        <Setter Property="ContentControl.Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ContentControl}">
                    <Grid x:Name="DraggedElementGrid" Focusable="False">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="4" />
                            <RowDefinition Height="*" />
                            <RowDefinition Height="4" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="4" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="4" />
                        </Grid.ColumnDefinitions>
                        <Border
                            x:Name="LeftTopBorder"
                            Grid.Row="0"
                            Grid.Column="0"
                            Background="{StaticResource PrimaryBackgroundOpacity3}" />
                        <Border
                            x:Name="TopBorder"
                            Grid.Row="0"
                            Grid.Column="1"
                            Background="{StaticResource PrimaryBackgroundOpacity3}" />
                        <Border
                            x:Name="RightTopBorder"
                            Grid.Row="0"
                            Grid.Column="2"
                            Background="{StaticResource PrimaryBackgroundOpacity3}" />
                        <Border
                            x:Name="LeftBorder"
                            Grid.Row="1"
                            Grid.Column="0"
                            Background="{StaticResource PrimaryBackgroundOpacity3}" />
                        <Border
                            x:Name="MiddleBorder"
                            Grid.Row="1"
                            Grid.Column="1"
                            Background="{StaticResource PrimaryBackgroundOpacity3}" />
                        <Border
                            x:Name="RightBorder"
                            Grid.Row="1"
                            Grid.Column="2"
                            Background="{StaticResource PrimaryBackgroundOpacity3}" />
                        <Border
                            x:Name="LeftBottomBorder"
                            Grid.Row="2"
                            Grid.Column="0"
                            Background="{StaticResource PrimaryBackgroundOpacity3}" />
                        <Border
                            x:Name="BottomBorder"
                            Grid.Row="2"
                            Grid.Column="1"
                            Background="{StaticResource PrimaryBackgroundOpacity3}" />
                        <Border
                            x:Name="RightBottomBorder"
                            Grid.Row="2"
                            Grid.Column="2"
                            Background="{StaticResource PrimaryBackgroundOpacity3}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Path=DragType, RelativeSource={RelativeSource Mode=Self}}" Value="ShadowDragging">
                <Setter Property="ContentControl.Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type ContentControl}">
                            <Border
                                x:Name="ShadowDragBorder"
                                Background="{StaticResource PrimaryBackgroundOpacity3}"
                                BorderThickness="0" />
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <ControlTemplate x:Key="SideTabItemTemplate" TargetType="{x:Type TabItem}">
        <Border
            x:Name="Border"
            Background="{TemplateBinding Background}"
            BorderBrush="{TemplateBinding BorderBrush}"
            BorderThickness="{TemplateBinding BorderThickness}"
            CornerRadius="7,7,0,0">
            <Border.LayoutTransform>
                <RotateTransform Angle="90" />
            </Border.LayoutTransform>
            <Grid>
                <DockPanel LastChildFill="True">
                    <Rectangle
                        Width="Auto"
                        Height="Auto"
                        Fill="Transparent"
                        RadiusX="5"
                        RadiusY="5" />
                    <Border
                        x:Name="Icon"
                        Width="16"
                        Margin="1"
                        Background="{Binding Path=(TabItem.Content).(tools_controls:DockingManager.Icon), RelativeSource={RelativeSource TemplatedParent}}"
                        DockPanel.Dock="Left" />
                    <ContentPresenter
                        x:Name="TabItemHeader"
                        Margin="6,2"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        ContentSource="Header"
                        ContentTemplate="{Binding Path=(Border.DataContext).(tools_controls:DockingManager.HeaderTemplate), ElementName=Border}"
                        ContentTemplateSelector="{StaticResource TabItemTrimmingTemplate}"
                        RecognizesAccessKey="True"
                        TextBlock.Foreground="{TemplateBinding Foreground}">
                        <ContentPresenter.Resources>
                            <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                        </ContentPresenter.Resources>
                    </ContentPresenter>
                </DockPanel>
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger SourceName="Icon" Property="Background" Value="{x:Null}">
                <Setter TargetName="Icon" Property="Visibility" Value="Collapsed" />
            </Trigger>
            <Trigger Property="TabItem.TabStripPlacement" Value="Top">
                <Setter TargetName="Border" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="0" />
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="TabItem.TabStripPlacement" Value="Bottom">
                <Setter TargetName="Border" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="0" />
                    </Setter.Value>
                </Setter>
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1110}" />
            </Trigger>
            <Trigger Property="TabItem.TabStripPlacement" Value="Left">
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1110}" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=(Border.DataContext).(tools_controls:DockingManager.IsSideTabItemBackgroundEnabled), RelativeSource={RelativeSource Self}}" Value="True">
                <Setter TargetName="Border" Property="Background" Value="{Binding Path=(Border.DataContext).(tools_controls:DockingManager.SideTabItemBackground), RelativeSource={RelativeSource Self}}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(Border.DataContext).(tools_controls:DockingManager.IsSideTabItemForegroundEnabled), RelativeSource={RelativeSource Self}}" Value="True">
                <Setter TargetName="TabItemHeader" Property="TextElement.Foreground" Value="{Binding Path=(Border.DataContext).(tools_controls:DockingManager.SideTabItemForeground), ElementName=Border}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=IsLoaded, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="True">
                <Setter Property="MinWidth" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.SidePanelSize), RelativeSource={RelativeSource Mode=Self}}" />
                <Setter Property="MinHeight" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.SidePanelSize), RelativeSource={RelativeSource Mode=Self}}" />
            </DataTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SideTabItemStyle" TargetType="{x:Type TabItem}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Header" Value="{Binding Path=(tools_controls:DockingManager.Header)}" />
        <Setter Property="ToolTip" Value="{Binding Path=(tools_controls:DockingManager.CaptionToolTip)}" />
        <Setter Property="tools_controls:DockingManager.ListenTabItemEvents" Value="True" />
        <Setter Property="MinWidth" Value="24" />
        <Setter Property="MinHeight" Value="24" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Template" Value="{StaticResource SideTabItemTemplate}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1110}" />
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
            </Trigger>
            <Trigger Property="Visibility" Value="Visible">
                <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
                <Setter Property="Background" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.SideItemsBackground), RelativeSource={RelativeSource Mode=Self}}" />
                <Setter Property="BorderBrush" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.SideItemsBorderBrush), RelativeSource={RelativeSource Mode=Self}}" />
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                <Setter Property="Background" Value="{StaticResource ContentBackground}" />
            </Trigger>
            <Trigger Property="IsKeyboardFocused" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                <Setter Property="Background" Value="{StaticResource ContentBackground}" />
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                <Setter Property="Background" Value="{StaticResource ContentBackground}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="BorderBrush" Value="Transparent" />
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                <Setter Property="Background" Value="Transparent" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="DockTabControlStyle" TargetType="{x:Type TabControl}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="ItemContainerStyle" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.TabItemStyle), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TabControl}">
                    <Grid
                        x:Name="RootGrid"
                        ClipToBounds="True"
                        KeyboardNavigation.TabNavigation="Local">
                        <Grid.RowDefinitions>
                            <RowDefinition x:Name="FirstRow" />
                            <RowDefinition x:Name="SecondRow" Height="0" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition x:Name="FirstCol" />
                            <ColumnDefinition x:Name="SecondCol" Width="0" />
                        </Grid.ColumnDefinitions>
                        <Border
                            x:Name="WrapBorder"
                            Grid.Row="1"
                            Grid.Column="0"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{StaticResource BorderAlt}"
                            BorderThickness="0">
                            <tools_controls:DirectTabPanel
                                x:Name="PART_TabPanel"
                                Panel.ZIndex="1"
                                Background="Transparent"
                                IsItemsHost="True"
                                KeyboardNavigation.TabIndex="1" />
                        </Border>
                        <Border
                            x:Name="SelectedContentBorder"
                            Grid.Row="0"
                            Grid.Column="0"
                            BorderBrush="{StaticResource BorderAlt}"
                            BorderThickness="0"
                            KeyboardNavigation.DirectionalNavigation="Contained"
                            KeyboardNavigation.TabIndex="2"
                            KeyboardNavigation.TabNavigation="Local">
                            <ContentPresenter
                                x:Name="PART_SelectedContentHost"
                                Margin="{TemplateBinding Margin}"
                                ContentSource="SelectedContent" />
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.IsTouchEnabled), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" Value="True">
                            <Setter Property="FontSize" Value="18" />
                        </DataTrigger>
                        <Trigger Property="TabStripPlacement" Value="Top">
                            <Setter TargetName="FirstRow" Property="Height" Value="Auto" />
                            <Setter TargetName="SecondRow" Property="Height" Value="*" />
                            <Setter TargetName="WrapBorder" Property="Grid.Row" Value="0" />
                            <Setter TargetName="SelectedContentBorder" Property="Grid.Row" Value="1" />
                        </Trigger>
                        <Trigger Property="TabStripPlacement" Value="Bottom">
                            <Setter TargetName="FirstRow" Property="Height" Value="*" />
                            <Setter TargetName="SecondRow" Property="Height" Value="Auto" />
                        </Trigger>
                        <Trigger Property="TabStripPlacement" Value="Left">
                            <Setter TargetName="FirstCol" Property="Width" Value="Auto" />
                            <Setter TargetName="SecondCol" Property="Width" Value="*" />
                            <Setter TargetName="SelectedContentBorder" Property="Grid.Column" Value="1" />
                            <Setter TargetName="WrapBorder" Property="Grid.Row" Value="0" />
                        </Trigger>
                        <Trigger Property="TabStripPlacement" Value="Right">
                            <Setter TargetName="FirstCol" Property="Width" Value="*" />
                            <Setter TargetName="SecondCol" Property="Width" Value="Auto" />
                            <Setter TargetName="WrapBorder" Property="Grid.Column" Value="1" />
                            <Setter TargetName="WrapBorder" Property="Grid.Row" Value="0" />
                        </Trigger>

                        <Trigger SourceName="WrapBorder" Property="Visibility" Value="Visible">
                            <Setter TargetName="WrapBorder" Property="BorderBrush" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.TabPanelBorderBrush), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" />
                            <Setter TargetName="WrapBorder" Property="BorderThickness" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.TabPanelBorderThickness), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Visibility" Value="Visible">
                <Setter Property="Background" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.TabPanelBackground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionDockedElementTabbedHostStyle" TargetType="{x:Type tools_controls:DockedElementTabbedHost}">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="tools_controls:DockingManager.InternalDataContext" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.HostedElement), RelativeSource={RelativeSource Self}}" />
        <Setter Property="FocusVisualStyle" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.FocusVisualStyle), RelativeSource={RelativeSource TemplatedParent}}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:DockedElementTabbedHost}">
                    <Border
                        x:Name="BorderWrap"
                        Width="Auto"
                        Background="Transparent"
                        BorderBrush="{StaticResource BorderAlt}"
                        BorderThickness="1"
                        FocusVisualStyle="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.FocusVisualStyle), RelativeSource={RelativeSource TemplatedParent}}"
                        SnapsToDevicePixels="True">
                        <DockPanel
                            x:Name="DockPanel"
                            Width="Auto"
                            Focusable="False"
                            LastChildFill="True">
                            <tools_controls:DockHeaderPresenter
                                x:Name="PART_Header"
                                DockPanel.Dock="Top"
                                IsTemplateParenKeyboardFocusWithin="{TemplateBinding IsKeyboardFocusWithin}"
                                RenderTransformOrigin="0.5,0.5"
                                Style="{Binding Path=(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.HeaderStyle), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" >
                                
                            </tools_controls:DockHeaderPresenter>
                            <Grid>
                                <ContentPresenter
                                    x:Name="HostedElement"
                                    ClipToBounds="True"
                                    ContentSource="HostedElement"
                                    ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}"
                                    ContentTemplateSelector="{TemplateBinding ContentControl.ContentTemplateSelector}" />
                                <Border
                                    x:Name="PART_DragBorder"
                                    Background="Transparent"
                                    Visibility="Collapsed" />
                            </Grid>
                        </DockPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Path=(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.NoHeader), RelativeSource={RelativeSource Self}}" Value="True">
                            <Setter TargetName="PART_Header" Property="Visibility" Value="Collapsed" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.IsDragging), RelativeSource={RelativeSource TemplatedParent}}" Value="True">
                            <Setter TargetName="PART_DragBorder" Property="Visibility" Value="Visible" />
                        </DataTrigger>
                        <Trigger Property="MarkAsFrozen" Value="True">
                            <Setter TargetName="PART_DragBorder" Property="Visibility" Value="Visible" />
                            <Setter TargetName="HostedElement" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger SourceName="BorderWrap" Property="Visibility" Value="Visible">
                            <Setter TargetName="BorderWrap" Property="BorderThickness" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.ElementBorderThickness), RelativeSource={RelativeSource TemplatedParent}}" />
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.UseNativeFloatWindow), RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.State), RelativeSource={RelativeSource Self}}" Value="Float" />
                                <Condition Binding="{Binding Path=IsMultiHostsContainer, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="BorderWrap" Property="BorderThickness" Value="0,0,0,0" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.UseNativeFloatWindow), RelativeSource={RelativeSource Self}}" Value="False" />
                                <Condition Binding="{Binding Path=(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.State), RelativeSource={RelativeSource Self}}" Value="Float" />
                                <Condition Binding="{Binding Path=IsMultiHostsContainer, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="BorderWrap" Property="BorderThickness" Value="0,0,0,0" />
                        </MultiDataTrigger>
                        <Trigger Property="Tag" Value="DocumentContainer">
                            <Setter TargetName="BorderWrap" Property="BorderThickness" Value="0" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.IsNative), RelativeSource={RelativeSource Self}}" Value="True">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:DockedElementTabbedHost}">
                            <Border
                                x:Name="BorderWrap"
                                Width="Auto"
                                Background="Transparent"
                                BorderBrush="{StaticResource BorderAlt}"
                                BorderThickness="1"
                                FocusVisualStyle="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.FocusVisualStyle), RelativeSource={RelativeSource TemplatedParent}}"
                                Focusable="True"
                                SnapsToDevicePixels="True">
                                <DockPanel
                                    x:Name="DockPanel"
                                    Width="Auto"
                                    Focusable="False"
                                    LastChildFill="True">
                                    <tools_controls:DockHeaderPresenter
                                        x:Name="PART_Header"
                                        DockPanel.Dock="Top"
                                        IsTemplateParenKeyboardFocusWithin="{TemplateBinding IsKeyboardFocusWithin}"
                                        RenderTransformOrigin="0.5,0.5"
                                        Style="{Binding Path=(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.HeaderStyle), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" >
                                        
                                    </tools_controls:DockHeaderPresenter>
                                    <Grid x:Name="hostgrid">
                                        <TabPanel>
                                            <ContentPresenter
                                                x:Name="HostedElement"
                                                Width="{Binding Path=ActualWidth, ElementName=hostgrid}"
                                                Height="{Binding Path=ActualHeight, ElementName=hostgrid}"
                                                ClipToBounds="True"
                                                ContentSource="HostedElement"
                                                ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}"
                                                ContentTemplateSelector="{TemplateBinding ContentControl.ContentTemplateSelector}" />
                                            <Border
                                                x:Name="PART_DragBorder"
                                                Background="Transparent"
                                                Visibility="Collapsed" />
                                        </TabPanel>
                                    </Grid>
                                </DockPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <DataTrigger Binding="{Binding Path=(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.NoHeader), RelativeSource={RelativeSource Self}}" Value="True">
                                    <Setter TargetName="PART_Header" Property="Visibility" Value="Collapsed" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.IsDragging), RelativeSource={RelativeSource TemplatedParent}}" Value="True">
                                    <Setter TargetName="PART_DragBorder" Property="Visibility" Value="Visible" />
                                </DataTrigger>
                                <Trigger Property="MarkAsFrozen" Value="True">
                                    <Setter TargetName="PART_DragBorder" Property="Visibility" Value="Visible" />
                                    <Setter TargetName="HostedElement" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <Trigger SourceName="BorderWrap" Property="Visibility" Value="Visible">
                                    <Setter TargetName="BorderWrap" Property="BorderThickness" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.ElementBorderThickness), RelativeSource={RelativeSource TemplatedParent}}" />
                                </Trigger>
                                <Trigger Property="Tag" Value="DocumentContainer">
                                    <Setter TargetName="BorderWrap" Property="BorderThickness" Value="0" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </DataTrigger>
            <Trigger Property="ShowTabs" Value="True">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:DockedElementTabbedHost}">
                            <Border
                                x:Name="BorderWrapForTab"
                                Width="Auto"
                                Background="Transparent"
                                BorderBrush="{StaticResource BorderAlt}"
                                BorderThickness="1"
                                SnapsToDevicePixels="True">
                                <DockPanel
                                    x:Name="DockPanel"
                                    Width="Auto"
                                    LastChildFill="True">
                                    <tools_controls:DockHeaderPresenter
                                        x:Name="PART_Header"
                                        DockPanel.Dock="Top"
                                        IsTemplateParenKeyboardFocusWithin="{TemplateBinding IsKeyboardFocusWithin}"
                                        RenderTransformOrigin="0.5,0.5"
                                        Style="{Binding Path=(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.HeaderStyle), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" >
                                        
                                    </tools_controls:DockHeaderPresenter>
                                    <Grid>
                                        <TabControl
                                            x:Name="PART_TabControl"
                                            Padding="0"
                                            AutomationProperties.Name="TabGroup"
                                            IsSynchronizedWithCurrentItem="True"
                                            ItemsSource="{TemplateBinding TabChildren}"
                                            Style="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.TabControlStyle), RelativeSource={RelativeSource TemplatedParent}}"
                                            TabStripPlacement="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.DockTabAlignment), RelativeSource={RelativeSource TemplatedParent}}" />
                                        <Border
                                            x:Name="PART_DragBorder"
                                            Background="Transparent"
                                            Visibility="Collapsed" />
                                    </Grid>
                                </DockPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <DataTrigger Binding="{Binding Path=(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.NoHeader), RelativeSource={RelativeSource Self}}" Value="True">
                                    <Setter TargetName="PART_Header" Property="Visibility" Value="Collapsed" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.IsDragging), RelativeSource={RelativeSource TemplatedParent}}" Value="True">
                                    <Setter TargetName="PART_DragBorder" Property="Visibility" Value="Visible" />
                                </DataTrigger>
                                <Trigger Property="MarkAsFrozen" Value="True">
                                    <Setter TargetName="PART_DragBorder" Property="Visibility" Value="Visible" />
                                    <Setter TargetName="PART_TabControl" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <Trigger SourceName="BorderWrapForTab" Property="Visibility" Value="Visible">
                                    <Setter TargetName="BorderWrapForTab" Property="BorderThickness" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.ElementBorderThickness), RelativeSource={RelativeSource TemplatedParent}}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <ControlTemplate x:Key="SyncfusionSidePanelControlTemplate" TargetType="{x:Type tools_controls:SidePanel}">
        <Canvas ClipToBounds="False" KeyboardNavigation.TabNavigation="Local">
            <Border
                x:Name="PART_BorderName"
                Width="{TemplateBinding ActualWidth}"
                Height="{TemplateBinding ActualHeight}"
                Margin="0"
                Panel.ZIndex="1"
                Background="{TemplateBinding Background}"
                BorderBrush="{StaticResource BorderAlt}"
                BorderThickness="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelBorderThickness), RelativeSource={RelativeSource TemplatedParent}}"
                ClipToBounds="True">
                <Border.ContextMenu>
                    <ContextMenu
                        x:Name="PART_SidePanelContextMenu"
                        ItemsSource="{TemplateBinding TabChildren}"
                        Style="{StaticResource MaterialSideContextMenuStyle}" />
                </Border.ContextMenu>
                <tools_controls:DirectTabPanel
                    x:Name="PART_PanelName"
                    Focusable="False"
                    IsItemsHost="True" />
            </Border>
            <tools_controls:OpacityDockPanel
                x:Name="PART_OpacityDockPanel"
                LastChildFill="True"
                Opacity="{Binding Path=ContentOpacity, RelativeSource={RelativeSource TemplatedParent}}">
                <DockPanel.RenderTransform>
                    <TransformGroup>
                        <TranslateTransform>
                            <TranslateTransform.X>
                                <MultiBinding Converter="{StaticResource SideToCoordinate}" ConverterParameter="XCoordinate">
                                    <Binding Path="TabStripPlacement" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(Selector.SelectedItem).(tools_controls:DockingManager.DesiredWidthInDockedMode)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="ContentRenderTransformX" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(ShadowBorder).(FrameworkElement.Width)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelBorderThickness)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelSize)" RelativeSource="{RelativeSource TemplatedParent}" />
                                </MultiBinding>
                            </TranslateTransform.X>
                            <TranslateTransform.Y>
                                <MultiBinding Converter="{StaticResource SideToCoordinate}" ConverterParameter="YCoordinate">
                                    <Binding Path="TabStripPlacement" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(Selector.SelectedItem).(tools_controls:DockingManager.DesiredHeightInDockedMode)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="ContentRenderTransformY" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(ShadowBorder).(FrameworkElement.Height)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelBorderThickness)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelSize)" RelativeSource="{RelativeSource TemplatedParent}" />
                                </MultiBinding>
                            </TranslateTransform.Y>
                        </TranslateTransform>
                        <ScaleTransform ScaleX="{Binding Path=ContentScaleX, RelativeSource={RelativeSource TemplatedParent}}" ScaleY="{Binding Path=ContentScaleY, RelativeSource={RelativeSource TemplatedParent}}" />
                    </TransformGroup>
                </DockPanel.RenderTransform>
                <Border
                    x:Name="PART_Shadow"
                    Background="Transparent"
                    DockPanel.Dock="{Binding Path=PanelSide, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource InvertDock}}" />
                <DockPanel x:Name="PART_SideDockPanel">
                    <tools_controls:Splitter
                        x:Name="PART_SideSplitter"
                        DockPanel.Dock="{Binding Path=PanelSide, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource InvertDock}}"
                        Orientation="{Binding Path=PanelSide, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource DockToOrientation}}" />
                    <Border
                        x:Name="WrapBorder"
                        Background="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.PreviewPanelBackground), RelativeSource={RelativeSource TemplatedParent}}"
                        BorderBrush="{StaticResource BorderAlt}"
                        BorderThickness="1">
                        <DockPanel
                            x:Name="FlyPanel"
                            Focusable="True"
                            KeyboardNavigation.TabNavigation="Cycle">
                            <DockPanel.Width>
                                <MultiBinding
                                    Converter="{StaticResource ActualWidthSubstractBorder}"
                                    ConverterParameter="ContentWidthParameter"
                                    UpdateSourceTrigger="PropertyChanged">
                                    <Binding
                                        Path="(Selector.SelectedItem).(tools_controls:DockingManager.DesiredWidthInDockedMode)"
                                        RelativeSource="{RelativeSource TemplatedParent}"
                                        UpdateSourceTrigger="PropertyChanged" />
                                    <Binding Path="ActualWidth" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding ElementName="PART_SideSplitter" Path="ActualWidth" />
                                    <Binding Path="PanelSide" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding ElementName="WrapBorder" Path="BorderThickness" />
                                </MultiBinding>
                            </DockPanel.Width>
                            <DockPanel.Height>
                                <MultiBinding Converter="{StaticResource ActualWidthSubstractBorder}" ConverterParameter="ContentHeightParameter">
                                    <Binding Path="(Selector.SelectedItem).(tools_controls:DockingManager.DesiredHeightInDockedMode)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="ActualHeight" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding ElementName="PART_SideSplitter" Path="ActualHeight" />
                                    <Binding Path="PanelSide" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding ElementName="WrapBorder" Path="BorderThickness" />
                                </MultiBinding>
                            </DockPanel.Height>
                            <tools_controls:DockHeaderPresenter
                                x:Name="PART_Header"
                                DockPanel.Dock="Top"
                                IsRichHeader="False"
                                IsTemplateParenKeyboardFocusWithin="{TemplateBinding IsShowedFocusedItem}"
                                Style="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.HeaderStyle), RelativeSource={RelativeSource Self}}" >
                                
                            </tools_controls:DockHeaderPresenter>
                            <Border x:Name="Content">
                                <ContentPresenter
                                    x:Name="PART_SelectedContent"
                                    Margin="{TemplateBinding Margin}"
                                    ContentSource="SelectedContent" />
                            </Border>
                        </DockPanel>
                    </Border>
                </DockPanel>
            </tools_controls:OpacityDockPanel>
        </Canvas>
        <ControlTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.IsDragging), RelativeSource={RelativeSource TemplatedParent}}" Value="True">
                <Setter Property="IsEnabled" Value="False" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.NoHeader), RelativeSource={RelativeSource Self}}" Value="True" >
                <Setter TargetName="PART_Header" Property="Visibility" Value="Collapsed"/>
            </DataTrigger>
            <Trigger Property="PanelSide" Value="Top">
                <Setter TargetName="PART_Shadow" Property="Height" Value="4" />
                <Setter TargetName="PART_Shadow" Property="Background" Value="Transparent" />
            </Trigger>
            <Trigger Property="PanelSide" Value="Bottom">
                <Setter TargetName="PART_Shadow" Property="Height" Value="4" />
                <Setter TargetName="PART_Shadow" Property="Background" Value="Transparent" />
            </Trigger>
            <Trigger Property="PanelSide" Value="Right">
                <Setter TargetName="PART_Shadow" Property="Width" Value="4" />
                <Setter TargetName="PART_Shadow" Property="Background" Value="Transparent" />
            </Trigger>
            <Trigger Property="PanelSide" Value="Left">
                <Setter TargetName="PART_Shadow" Property="Width" Value="4" />
            </Trigger>
            <Trigger Property="IsContentHiden" Value="True">
                <Setter TargetName="FlyPanel" Property="Visibility" Value="Hidden" />
            </Trigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.AutoHideAnimationMode), RelativeSource={RelativeSource Self}}" Value="Slide" />
                    <Condition Binding="{Binding Path=IsContentHiden, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=PanelSide, RelativeSource={RelativeSource Self}, Converter={StaticResource DockToOrientation}}" Value="Vertical" />
                </MultiDataTrigger.Conditions>
                <MultiDataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation
                                Storyboard.TargetName="PART_Shadow"
                                Storyboard.TargetProperty="Width"
                                To="0"
                                Duration="{StaticResource ShadowDuration}" />
                        </Storyboard>
                    </BeginStoryboard>
                </MultiDataTrigger.EnterActions>
                <MultiDataTrigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation
                                Storyboard.TargetName="PART_Shadow"
                                Storyboard.TargetProperty="Width"
                                To="4"
                                Duration="{StaticResource ShadowDuration}" />
                        </Storyboard>
                    </BeginStoryboard>
                </MultiDataTrigger.ExitActions>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.AutoHideAnimationMode), RelativeSource={RelativeSource Self}}" Value="Slide" />
                    <Condition Binding="{Binding Path=IsContentHiden, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=PanelSide, RelativeSource={RelativeSource Self}, Converter={StaticResource DockToOrientation}}" Value="Horizontal" />
                </MultiDataTrigger.Conditions>
                <MultiDataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation
                                Storyboard.TargetName="PART_Shadow"
                                Storyboard.TargetProperty="Height"
                                To="0"
                                Duration="{StaticResource ShadowDuration}" />
                        </Storyboard>
                    </BeginStoryboard>
                </MultiDataTrigger.EnterActions>
                <MultiDataTrigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation
                                Storyboard.TargetName="PART_Shadow"
                                Storyboard.TargetProperty="Height"
                                To="4"
                                Duration="{StaticResource ShadowDuration}" />
                        </Storyboard>
                    </BeginStoryboard>
                </MultiDataTrigger.ExitActions>
            </MultiDataTrigger>
            <Trigger SourceName="WrapBorder" Property="Visibility" Value="Visible">
                <Setter TargetName="WrapBorder" Property="BorderThickness" Value="{Binding Path=ElementBorderThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" />
                <Setter TargetName="PART_BorderName" Property="BorderBrush" Value="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelBorderBrush), RelativeSource={RelativeSource TemplatedParent}}" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.ShowShadowOnSidePanel), RelativeSource={RelativeSource Self}}" Value="False">
                <Setter TargetName="PART_Shadow" Property="Background" Value="Transparent" />
            </DataTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionPopupSidePanelControlTemplate" TargetType="{x:Type tools_controls:SidePanel}">
        <Canvas ClipToBounds="False" KeyboardNavigation.TabNavigation="Local">
            <Border
                x:Name="PART_BorderName"
                Width="{TemplateBinding ActualWidth}"
                Height="{TemplateBinding ActualHeight}"
                Margin="0"
                Panel.ZIndex="1"
                Background="{TemplateBinding Background}"
                BorderBrush="{StaticResource BorderAlt}"
                BorderThickness="{Binding Path=SidePanelBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockingManager}}}"
                ClipToBounds="True">
                <Border.ContextMenu>
                    <ContextMenu
                        x:Name="PART_SidePanelContextMenu"
                        ItemsSource="{TemplateBinding TabChildren}"
                        Style="{StaticResource MaterialSideContextMenuStyle}" />
                </Border.ContextMenu>
                <tools_controls:DirectTabPanel
                    x:Name="PART_PanelName"
                    IsItemsHost="True"
                    KeyboardNavigation.TabNavigation="Local" />

            </Border>
            <tools_controls:PopupSidePanel x:Name="PART_PopupPanel">
                <DockPanel x:Name="PART_SideDockPanel">
                    <tools_controls:Splitter
                        x:Name="PART_SideSplitter"
                        DockPanel.Dock="{Binding Path=PanelSide, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource InvertDock}}"
                        Orientation="{Binding Path=PanelSide, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource DockToOrientation}}" />
                    <Border
                        x:Name="WrapBorder"
                        Background="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.PreviewPanelBackground), RelativeSource={RelativeSource TemplatedParent}}"
                        BorderBrush="{StaticResource BorderAlt}"
                        BorderThickness="1">
                        <DockPanel
                            x:Name="FlyPanel"
                            Focusable="True"
                            KeyboardNavigation.TabNavigation="Cycle">
                            <DockPanel.Width>
                                <MultiBinding
                                    Converter="{StaticResource ActualWidthSubstractBorder}"
                                    ConverterParameter="ContentWidthParameter"
                                    UpdateSourceTrigger="PropertyChanged">
                                    <Binding
                                        Path="(Selector.SelectedItem).(tools_controls:DockingManager.DesiredWidthInDockedMode)"
                                        RelativeSource="{RelativeSource TemplatedParent}"
                                        UpdateSourceTrigger="PropertyChanged" />
                                    <Binding Path="ActualWidth" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding ElementName="PART_SideSplitter" Path="ActualWidth" />
                                    <Binding Path="PanelSide" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding ElementName="WrapBorder" Path="BorderThickness" />
                                </MultiBinding>
                            </DockPanel.Width>
                            <DockPanel.Height>
                                <MultiBinding Converter="{StaticResource ActualWidthSubstractBorder}" ConverterParameter="ContentHeightParameter">
                                    <Binding Path="(Selector.SelectedItem).(tools_controls:DockingManager.DesiredHeightInDockedMode)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="ActualHeight" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding ElementName="PART_SideSplitter" Path="ActualHeight" />
                                    <Binding Path="PanelSide" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding ElementName="WrapBorder" Path="BorderThickness" />
                                </MultiBinding>
                            </DockPanel.Height>
                            <tools_controls:DockHeaderPresenter
                                x:Name="PART_Header"
                                DockPanel.Dock="Top"
                                IsRichHeader="False"
                                IsTemplateParenKeyboardFocusWithin="{TemplateBinding IsShowedFocusedItem}"
                                Style="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.HeaderStyle), RelativeSource={RelativeSource Self}}" >
                               
                            </tools_controls:DockHeaderPresenter>
                            <Border x:Name="Content">
                                <ContentPresenter
                                    x:Name="PART_SelectedContent"
                                    Margin="{TemplateBinding Margin}"
                                    ContentSource="SelectedContent" />
                            </Border>
                        </DockPanel>
                    </Border>
                </DockPanel>
            </tools_controls:PopupSidePanel>
        </Canvas>
        <ControlTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.IsDragging), RelativeSource={RelativeSource TemplatedParent}}" Value="True">
                <Setter Property="IsEnabled" Value="False" />
            </DataTrigger>

            <Trigger SourceName="WrapBorder" Property="Visibility" Value="Visible">
                <Setter TargetName="WrapBorder" Property="BorderThickness" Value="{Binding Path=ElementBorderThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" />
                <Setter TargetName="PART_BorderName" Property="BorderBrush" Value="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelBorderBrush), RelativeSource={RelativeSource TemplatedParent}}" />
            </Trigger>
            <Trigger Property="IsContentHiden" Value="True">
                <Setter TargetName="FlyPanel" Property="Visibility" Value="Hidden" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SyncfusionSidePanelStyle" TargetType="{x:Type tools_controls:SidePanel}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="ItemContainerStyle" Value="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SideItemStyle), RelativeSource={RelativeSource Self}}" />
        <Setter Property="Visibility" Value="Collapsed" />
        <Setter Property="tools_controls:DockingManager.InternalDataContext" Value="{Binding Path=SelectedItem, RelativeSource={RelativeSource Self}}" />
        <Setter Property="Template" Value="{StaticResource SyncfusionSidePanelControlTemplate}" />
        <Style.Triggers>
            <DataTrigger Binding="{Binding Path=HasItems, RelativeSource={RelativeSource Self}}" Value="True">
                <Setter Property="Visibility" Value="Visible" />
            </DataTrigger>
            <Trigger Property="Visibility" Value="Visible">
                <Setter Property="Background" Value="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelBackground), RelativeSource={RelativeSource Self}}" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.UsePopupAutoHidePreview), RelativeSource={RelativeSource Self}}" Value="True">
                <Setter Property="Template" Value="{StaticResource SyncfusionPopupSidePanelControlTemplate}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.IsTouchEnabled), RelativeSource={RelativeSource Self}}" Value="True">
                <Setter Property="FontSize" Value="18" />
            </DataTrigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSidePanelStyle}" TargetType="{x:Type tools_controls:SidePanel}" />

    <ControlTemplate x:Key="TabItemTemplate" TargetType="{x:Type TabItem}">
        <Grid x:Name="RootGrid">
            <tools_controls:ContextMenuBorder
                x:Name="Border"
                Background="{TemplateBinding Background}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}"
                CornerRadius="7,7,0,0">
                <Border.ContextMenu>
                    <tools_controls:CustomContextMenu x:Name="PART_ContextMenu" Focusable="false" />
                </Border.ContextMenu>
                <Border.LayoutTransform>
                    <RotateTransform Angle="0" />
                </Border.LayoutTransform>
                <DockPanel x:Name="PART_DockPanel" LastChildFill="True">
                    <Border
                        x:Name="Icon"
                        Width="16"
                        Margin="1"
                        Background="{Binding Path=(TabItem.Content).(tools_controls:DockingManager.Icon), RelativeSource={RelativeSource TemplatedParent}}"
                        DockPanel.Dock="Left" />
                    <ContentPresenter
                        x:Name="TabItemHeader"
                        Margin="9,5"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        ContentSource="Header"
                        ContentTemplate="{Binding Path=(TabItem.Content).(tools_controls:DockingManager.HeaderTemplate), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                        ContentTemplateSelector="{StaticResource TabItemTrimmingTemplate}"
                        RecognizesAccessKey="True">
                        <ContentPresenter.Resources>
                            <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                        </ContentPresenter.Resources>
                    </ContentPresenter>
                </DockPanel>
            </tools_controls:ContextMenuBorder>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger SourceName="Icon" Property="Background" Value="{x:Null}">
                <Setter TargetName="Icon" Property="Visibility" Value="Collapsed" />
            </Trigger>
            <Trigger Property="TabItem.TabStripPlacement" Value="Left">
                <Setter TargetName="Border" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="90" />
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="TabItem.TabStripPlacement" Value="Right">
                <Setter TargetName="Border" Property="LayoutTransform">
                    <Setter.Value>
                        <RotateTransform Angle="90" />
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter TargetName="TabItemHeader" Property="Panel.ZIndex" Value="100" />
                <Setter TargetName="TabItemHeader" Property="TextBlock.Foreground" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.TabItemForegroundSelected),RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" />
                <Setter TargetName="Border" Property="Background" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.TabItemBackgroundSelected),RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" />
                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter TargetName="Border" Property="BorderThickness" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.TabItemsBorderThicknessSelected),RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" />
                <Setter Property="Margin" TargetName="PART_DockPanel" Value="-1,-1,-1,0"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="False">
                <Setter TargetName="TabItemHeader" Property="TextBlock.Foreground" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.TabItemsForeground),RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" />
                <Setter TargetName="Border" Property="BorderThickness" Value="{Binding Path=(tools_controls:DockedElementTabbedHost.DockingManager).(tools_controls:DockingManager.TabItemBorderThickness),RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=(Border.DataContext).(tools_controls:DockingManager.CanClose), ElementName=Border}" Value="False">
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(Border.DataContext).(tools_controls:DockingManager.State), ElementName=Border}" Value="Float">
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(Border.DataContext).(tools_controls:DockingManager.CanFloat), ElementName=Border}" Value="False">
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledFloatingMenuItem" Value="False" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(Border.DataContext).(tools_controls:DockingManager.CanAutoHide), ElementName=Border}" Value="False">
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(Border.DataContext).(tools_controls:DockingManager.CanDocument), ElementName=Border}" Value="False">
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledTabbedMenuItem" Value="False" />
            </DataTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="TabItemStyle" TargetType="{x:Type TabItem}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Header" Value="{Binding Path=(tools_controls:DockingManager.Header)}" />
        <Setter Property="ToolTip" Value="{Binding Path=(tools_controls:DockingManager.CaptionToolTip)}" />
        <Setter Property="tools_controls:DockingManager.ListenTabItemEvents" Value="True" />
        <Setter Property="MinWidth" Value="24" />
        <Setter Property="MinHeight" Value="24" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Tag" Value="IsInternalTabItem" />
        <Setter Property="Template" Value="{StaticResource TabItemTemplate}" />
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinHeight}" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.IsTouchEnabled), RelativeSource={RelativeSource Self}}" Value="True">
                <Setter Property="MinHeight" Value="35" />
                <Setter Property="MinWidth" Value="35" />
            </DataTrigger>
            <Trigger Property="Visibility" Value="Visible">
                <Setter Property="Foreground" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.TabItemsForeground), RelativeSource={RelativeSource Mode=Self}}" />
                <Setter Property="Background" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.TabItemsBackground), RelativeSource={RelativeSource Mode=Self}}" />
                <Setter Property="BorderBrush" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.TabItemsBorderBrush), RelativeSource={RelativeSource Mode=Self}}" />
                <Setter Property="BorderThickness" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.TabItemBorderThickness), RelativeSource={RelativeSource Mode=Self}}" />
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource ContentBackground}" />
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderBrush" Value="Transparent" />
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="BorderThickness" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.TabItemsBorderThicknessSelected), RelativeSource={RelativeSource Mode=Self}}" />
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter Property="Foreground" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.TabItemForegroundSelected), RelativeSource={RelativeSource Mode=Self}}" />
                <Setter Property="Background" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.TabItemBackgroundSelected), RelativeSource={RelativeSource Mode=Self}}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="RowDefinitionStyle" TargetType="RowDefinition">
        <Setter Property="Height" Value="22" />
        <Style.Triggers>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.IsTouchEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True">
                <Setter Property="Height" Value="35" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <ControlTemplate x:Key="FloatWindowTemplate" TargetType="{x:Type ContentControl}">
        <AdornerDecorator>
            <DockPanel Focusable="False" Opacity="{Binding Path=Opacity, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:IWindow}}}">
                <Border
                    x:Name="FloatWindowOutBorder"
                    Margin="0"
                    Background="{StaticResource ContentBackground}"
                    BorderBrush="{StaticResource BorderAlt}"
                    BorderThickness="1"
                    Focusable="False">
                    <Grid Focusable="False">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="0.1" />
                            <RowDefinition x:Name="TopRow" Style="{StaticResource RowDefinitionStyle}" />
                            <RowDefinition Height="*" />
                            <RowDefinition x:Name="BottomRow" Height="0.1" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition x:Name="LeftCol" Width="0.1" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition x:Name="RightCol" Width="0.1" />
                        </Grid.ColumnDefinitions>
                        <tools_controls:FloatWindowBorder
                            x:Name="Top"
                            Grid.Row="0"
                            Grid.Column="0"
                            Grid.ColumnSpan="3"
                            BorderMode="Bottom" />
                        <tools_controls:FloatWindowBorder
                            x:Name="BorderLeftTop"
                            Grid.Row="1"
                            Grid.Column="0"
                            BorderMode="LeftTop" />
                        <tools_controls:FloatWindowBorder
                            x:Name="BorderHeader"
                            Grid.Row="1"
                            Grid.Column="1"
                            BorderMode="Header" />
                        <tools_controls:FloatWindowBorder
                            x:Name="BorderRightTop"
                            Grid.Row="1"
                            Grid.Column="2"
                            BorderMode="RightTop" />
                        <tools_controls:FloatWindowBorder
                            x:Name="BorderLeft"
                            Grid.Row="2"
                            Grid.Column="0"
                            BorderMode="Left" />
                        <ContentPresenter
                            x:Name="ContentPresenter"
                            Grid.Row="2"
                            Grid.Column="1"
                            Content="{TemplateBinding ContentControl.Content}"
                            ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}" />
                        <tools_controls:FloatWindowBorder
                            x:Name="BorderRight"
                            Grid.Row="2"
                            Grid.Column="2"
                            BorderMode="Right" />
                        <tools_controls:FloatWindowBorder
                            x:Name="BorderLeftBottom"
                            Grid.Row="3"
                            Grid.Column="0"
                            BorderMode="LeftBottom" />
                        <tools_controls:FloatWindowBorder
                            x:Name="BorderBottom"
                            Grid.Row="3"
                            Grid.Column="1"
                            BorderMode="Bottom" />
                        <tools_controls:FloatWindowBorder
                            x:Name="BorderRightBottom"
                            Grid.Row="3"
                            Grid.Column="2"
                            BorderMode="RightBottom" />
                    </Grid>
                </Border>
            </DockPanel>
        </AdornerDecorator>

    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionDockHeaderPresenterControlTemplate" TargetType="{x:Type tools_controls:DockHeaderPresenter}">
        <Border
            x:Name="PART_HeaderBorder"
            Width="Auto"
            Height="Auto"
            Background="{TemplateBinding Background}"
            BorderBrush="{TemplateBinding BorderBrush}"
            BorderThickness="{TemplateBinding BorderThickness}">
            <Border.ContextMenu>
                <tools_controls:CustomContextMenu x:Name="PART_ContextMenu" Focusable="True" />
            </Border.ContextMenu>
            <Grid>
                <DockPanel LastChildFill="True">
                    <ToggleButton
                        x:Name="PART_CloseButton"
                        Width="16"
                        Height="16"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        AutomationProperties.Name="Hide"
                        Command="tools_controls:DockHeaderPresenter.CloseCommand"
                        DockPanel.Dock="Right"
                        Focusable="True"
                        KeyboardNavigation.TabIndex="6"
                        Style="{StaticResource CaptionButtonStyle}"
                        Tag="Dock"
                        Template="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.CloseButtonTemplate), RelativeSource={RelativeSource TemplatedParent}}">
                        <ToggleButton.ToolTip>
                            <ToolTip x:Name="CloseButtonTooltip">
                                <ToolTip.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ToolTip.Resources>
                                <TextBlock x:Name="PART_CloseButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=CloseButtonTooltipText}" />
                            </ToolTip>
                        </ToggleButton.ToolTip>
                        <ToggleButton.Content>
                            <Path
                                x:Name="CloseButtonPath1"
                                Width="8"
                                Height="8"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Stroke="{Binding Foreground, ElementName=PART_CloseButton}"
                                StrokeThickness="1"
                                SnapsToDevicePixels="True"
                                Stretch="Fill" 
                                Data="M1 11L6 6M6 6L11 1M6 6L11 11M6 6L1 1"/>
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ToggleButton
                        x:Name="PART_AwlButton"
                        Width="16"
                        Height="16"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        AutomationProperties.Name="AutoHide"
                        Command="tools_controls:DockHeaderPresenter.ChangeAwlStateCommand"
                        DockPanel.Dock="Right"
                        Focusable="True"
                        KeyboardNavigation.TabIndex="5"
                        Style="{StaticResource CaptionButtonStyle}"
                        Tag="Dock"
                        Template="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.AwlButtonTemplate), RelativeSource={RelativeSource TemplatedParent}}">
                        <ToggleButton.ToolTip>
                            <ToolTip x:Name="PART_AwlButtonTooltip">
                                <ToolTip.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ToolTip.Resources>
                                <TextBlock x:Name="PART_AwlButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=AwlButtonTooltipText}" />
                            </ToolTip>
                        </ToggleButton.ToolTip>
                        <ToggleButton.Content>
                            <Path
                                x:Name="PinButtonPath2"
                                Width="10"
                                Height="10"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Fill="{Binding Foreground, ElementName=PART_AwlButton}"
                                Stretch="Fill"
                                Data="M7.74219 3.74609C7.82552 3.62109 7.86719 3.48308 7.86719 3.33203C7.86719 3.23308 7.84766 3.13672 7.80859 3.04297C7.77214 2.94922 7.71875 2.86719 7.64844 2.79688L5.1875 0.332031C5.11979 0.264328 5.03906 0.210938 4.94531 0.171875C4.85417 0.132812 4.75911 0.113281 4.66016 0.113281C4.51172 0.113281 4.375 0.153641 4.25 0.234375C4.125 0.3125 4.03255 0.419266 3.97266 0.554688L3.10547 2.46484C3.07682 2.52734 3.03255 2.57031 2.97266 2.59375L0.65625 3.51953C0.609375 3.53777 0.571615 3.5677 0.542969 3.60938C0.514322 3.65105 0.5 3.69661 0.5 3.74609C0.5 3.81641 0.52474 3.8763 0.574219 3.92578L2.14844 5.5L0 7.64453V8H0.351562L2.5 5.85156L4.07422 7.42578C4.1237 7.47527 4.18229 7.5 4.25 7.5C4.30729 7.5 4.35677 7.48308 4.39844 7.44922C4.44271 7.41536 4.47266 7.37239 4.48828 7.32031L5.17188 5.04688C5.18229 5.01042 5.20052 4.97917 5.22656 4.95312C5.25261 4.92448 5.28255 4.90364 5.31641 4.89062L7.40625 4.01953C7.54688 3.96223 7.65886 3.87109 7.74219 3.74609ZM4.51953 0.652344C4.5612 0.626297 4.60807 0.613281 4.66016 0.613281C4.72526 0.613281 4.78255 0.638016 4.83203 0.6875L7.29688 3.14844C7.34375 3.19531 7.36719 3.25391 7.36719 3.32422C7.36719 3.3763 7.35286 3.42317 7.32422 3.46484C7.29818 3.50391 7.26172 3.53386 7.21484 3.55469L5.12109 4.42969C5.01953 4.47395 4.92969 4.53777 4.85156 4.62109C4.77604 4.70442 4.72266 4.79948 4.69141 4.90625L4.12891 6.77734L1.19531 3.83984L3.15625 3.05859C3.24739 3.02214 3.32812 2.97136 3.39844 2.90625C3.46875 2.83855 3.52344 2.76042 3.5625 2.67188L4.42969 0.761719C4.45052 0.714844 4.48047 0.678391 4.51953 0.652344Z"/>
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ToggleButton
                        x:Name="PART_ContextMenuButton"
                        Width="16"
                        Height="16"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        AutomationProperties.Name="WindowMenu"
                        Command="tools_controls:DockHeaderPresenter.OpenContextMenuCommand"
                        DockPanel.Dock="Right"
                        Focusable="True"
                        IsChecked="{Binding Path=IsContextMenuOpen, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockHeaderPresenter}}}"
                        KeyboardNavigation.TabIndex="4"
                        Style="{StaticResource CaptionButtonStyle}"
                        Tag="Dock"
                        Template="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MenuButtonTemplate), RelativeSource={RelativeSource TemplatedParent}}">
                        <ToggleButton.ToolTip>
                            <ToolTip x:Name="PART_ContextMenuButtonTooltip">
                                <ToolTip.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ToolTip.Resources>
                                <TextBlock x:Name="PART_ContextMenuButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=ContextMenuButtonTooltipText}" />
                            </ToolTip>
                        </ToggleButton.ToolTip>
                        <ToggleButton.Content>
                            <Path
                                x:Name="MenuButtonPath1"
                                Width="8"
                                Height="4"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Stroke="{Binding Foreground, ElementName=PART_ContextMenuButton}"
                                StrokeThickness="1"
                                Stretch="Fill"
                                Data="M1 1L5 5L9 1"/>
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ToggleButton
                        x:Name="PART_MaximizeButton"
                        Width="16"
                        Height="16"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        Command="tools_controls:DockHeaderPresenter.MaximizeStateCommand"
                        DockPanel.Dock="Right"
                        Focusable="True"
                        KeyboardNavigation.TabIndex="3"
                        Style="{StaticResource CaptionButtonStyle}"
                        Tag="Dock"
                        Template="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonTemplate), RelativeSource={RelativeSource TemplatedParent}}"
                        Visibility="Collapsed">
                        <ToggleButton.ToolTip>
                            <ToolTip x:Name="PART_MaximizeButtonTooltip">
                                <ToolTip.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ToolTip.Resources>
                                <TextBlock x:Name="PART_MaximizeButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=Maximize}" />
                            </ToolTip>
                        </ToggleButton.ToolTip>
                        <ToggleButton.Content>
                            <Path
                               x:Name="MaximizeButtonPath2"
                               Width="9"
                               Height="9"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Stroke="{Binding Foreground, ElementName=PART_MaximizeButton}"
                               StrokeThickness="1"
                               SnapsToDevicePixels="False"
                               Stretch="Fill"
                               Data="M0.5 1.25C0.5 0.835786 0.835786 0.5 1.25 0.5H7.75C8.16421 0.5 8.5 0.835786 8.5 1.25V7.75C8.5 8.16421 8.16421 8.5 7.75 8.5H1.25C0.835786 8.5 0.5 8.16421 0.5 7.75V1.25Z"/>
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ToggleButton
                        x:Name="PART_RestoreButton"
                        Width="16"
                        Height="16"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        Command="tools_controls:DockHeaderPresenter.RestoreStateCommand"
                        DockPanel.Dock="Right"
                        Focusable="True"
                        KeyboardNavigation.TabIndex="2"
                        Style="{StaticResource CaptionButtonStyle}"
                        Tag="Dock"
                        Template="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.RestoreButtonTemplate), RelativeSource={RelativeSource TemplatedParent}}"
                        Visibility="Collapsed">
                        <ToggleButton.ToolTip>
                            <ToolTip x:Name="PART_RestoreButtonTooltip">
                                <ToolTip.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ToolTip.Resources>
                                <TextBlock x:Name="PART_RestoreButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=Restore}" />
                            </ToolTip>
                        </ToggleButton.ToolTip>
                        <ToggleButton.Content>
                            <Path
                                x:Name="RestoreButtonPath2"
                                Width="10"
                                Height="10"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Stroke="{Binding Foreground, ElementName=PART_RestoreButton}"
                                StrokeThickness="1"
                                SnapsToDevicePixels="False"
                                Stretch="Fill"
                                Data="M2.5 0.5H8C8.82843 0.5 9.5 1.17157 9.5 2V7.5M1.5 9.5H6.5C7.05228 9.5 7.5 9.05228 7.5 8.5V3.5C7.5 2.94772 7.05228 2.5 6.5 2.5H1.5C0.947715 2.5 0.5 2.94772 0.5 3.5V8.5C0.5 9.05228 0.947715 9.5 1.5 9.5Z"/>
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ToggleButton
                        x:Name="PART_MinimizeButton"
                        Width="16"
                        Height="16"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        Command="tools_controls:DockHeaderPresenter.MinimizeStateCommand"
                        DockPanel.Dock="Right"
                        Focusable="True"
                        KeyboardNavigation.TabIndex="1"
                        Style="{StaticResource CaptionButtonStyle}"
                        Tag="Dock"
                        Template="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MinimizeButtonTemplate), RelativeSource={RelativeSource TemplatedParent}}"
                        Visibility="Collapsed">
                        <ToggleButton.ToolTip>
                            <ToolTip x:Name="PART_MinimizeButtonTooltip">
                                <ToolTip.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ToolTip.Resources>
                                <TextBlock x:Name="PART_MinimizeButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=Minimize}" />
                            </ToolTip>
                        </ToggleButton.ToolTip>
                        <ToggleButton.Content>
                            <Path
                                x:Name="MinimizeButtonPath2"
                                Width="10"
                                Height="1"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Stroke="{Binding Foreground, ElementName=PART_MinimizeButton}"
                                StrokeThickness="1"
                                SnapsToDevicePixels="False"
                                Stretch="Fill"
                                Data="M0.5 0.5H10.5"/>
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ContentPresenter
                        x:Name="PART_ContentPresenter"
                        Margin="6,2,2,2"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Content="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.Header), RelativeSource={RelativeSource TemplatedParent}}"
                        ContentTemplate="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.HeaderTemplate), RelativeSource={RelativeSource TemplatedParent}}"
                        ContentTemplateSelector="{StaticResource DockTrimmingTemplate}"
                        IsHitTestVisible="True"
                        ToolTip="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CaptionToolTip), RelativeSource={RelativeSource TemplatedParent}}">
                        <ContentPresenter.Resources>
                            <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                        </ContentPresenter.Resources>
                    </ContentPresenter>
                </DockPanel>
                <Border
                    x:Name="PART_DragBorder"
                    Background="Transparent"
                    Visibility="Collapsed" />
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMaximize), RelativeSource={RelativeSource TemplatedParent}}" Value="false">
                <Setter TargetName="PART_MaximizeButton" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMinimize), RelativeSource={RelativeSource TemplatedParent}}" Value="false">
                <Setter TargetName="PART_MinimizeButton" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_MaximizeButton" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MinimizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_MinimizeButton" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMaximize), RelativeSource={RelativeSource TemplatedParent}}" Value="false" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonMode), RelativeSource={RelativeSource TemplatedParent}}" Value="Disable" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.MaximizeButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Visible" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_MaximizeButton" Property="Visibility" Value="Visible" />
                <Setter TargetName="PART_MaximizeButton" Property="IsEnabled" Value="False" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMaximize), RelativeSource={RelativeSource TemplatedParent}}" Value="true" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonMode), RelativeSource={RelativeSource TemplatedParent}}" Value="Disable" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.MaximizeButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Visible" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_MaximizeButton" Property="IsEnabled" Value="True" />
            </MultiDataTrigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMaximize), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.MaximizeButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Visible" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_MaximizeButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MinimizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMinimize), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.MinimizeButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Visible" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_MinimizeButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMaximize), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.MaximizeButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Collapsed" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_MaximizeButton" Property="Visibility" Value="Collapsed" />
            </MultiDataTrigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MinimizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMinimize), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.MinimizeButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Collapsed" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_MinimizeButton" Property="Visibility" Value="Collapsed" />
            </MultiDataTrigger>

            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.RestoreButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Collapsed">
                <Setter TargetName="PART_RestoreButton" Property="Visibility" Value="Collapsed" />
            </DataTrigger>

            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.RestoreButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Visible">
                <Setter TargetName="PART_RestoreButton" Property="Visibility" Value="Visible" />
                <Setter TargetName="PART_MaximizeButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_MaximizeButton" Property="IsEnabled" Value="True" />
            </DataTrigger>

            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.State), RelativeSource={RelativeSource TemplatedParent}}" Value="Float">
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </DataTrigger>

            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.State), RelativeSource={RelativeSource TemplatedParent}}" Value="AutoHidden">
                <Setter TargetName="PART_AwlButton" Property="IsChecked" Value="true" />
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.State), RelativeSource={RelativeSource TemplatedParent}}" Value="AutoHidden" />
                    <Condition Binding="{Binding Path=IsTouchEnabled, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockingManager}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_AwlButton" Property="IsChecked" Value="False" />
            </MultiDataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanClose), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanFloat), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledFloatingMenuItem" Value="False" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanDock), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanDocument), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledTabbedMenuItem" Value="False" />
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanClose), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.CloseTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="CloseActive" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanClose), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.CloseTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="CloseAll" />
                    <Condition Binding="{Binding Path=CanCloseGroup, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
            </MultiDataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.AutoHideVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.IsDragging), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_DragBorder" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanAutoHide), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanAutoHide), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=AutoHideTabsMode, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="AutoHideGroup" />
                    <Condition Binding="{Binding Path=CanAutoHideGroup, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanAutoHide), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </MultiDataTrigger>
            <Trigger SourceName="PART_AwlButton" Property="IsChecked" Value="True">
                <Setter TargetName="PinButtonPath2" Property="Data" Value="M7.87289 3.685C7.95763 3.5582 8 3.4182 8 3.26498C8 3.1646 7.98014 3.06686 7.94042 2.97177C7.90334 2.87667 7.84906 2.79346 7.77756 2.72214L5.27507 0.221893C5.20622 0.153214 5.12413 0.0990601 5.0288 0.059433C4.93612 0.0198135 4.83946 0 4.73883 0C4.58788 0 4.44886 0.040947 4.32175 0.122833C4.19464 0.20208 4.10063 0.310387 4.03972 0.447746L3.15789 2.38534C3.12877 2.44874 3.08375 2.49232 3.02284 2.5161L0.667328 3.45517C0.619663 3.47366 0.581264 3.50404 0.552135 3.54631C0.523006 3.58858 0.508442 3.6348 0.508442 3.685C0.508442 3.75632 0.5336 3.81708 0.583912 3.86726L2.18471 5.4641L0 7.63942V8H0.357498L2.54221 5.82071L4.143 7.41753C4.19331 7.46773 4.2529 7.49281 4.32175 7.49281C4.38 7.49281 4.43032 7.47565 4.47269 7.44131C4.51771 7.40697 4.54816 7.36339 4.56405 7.31055L5.25919 5.00446C5.26978 4.96747 5.28831 4.93578 5.3148 4.90936C5.34128 4.88031 5.37173 4.85918 5.40616 4.84596L7.53128 3.96236C7.67428 3.90424 7.78815 3.81178 7.87289 3.685Z"/>
                <Setter TargetName="PinButtonPath2" Property="Height" Value="10" />
                <Setter TargetName="PinButtonPath2" Property="Width" Value="10" />
            </Trigger>
            <Trigger Property="Visibility" Value="Collapsed">
                <Setter TargetName="PART_ContentPresenter" Property="Content" Value="{x:Null}" />
            </Trigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.EnableMouseHoverBackground), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_HeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.HeaderMouseOverBackground), RelativeSource={RelativeSource TemplatedParent}}" />
                <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                <Setter TargetName="PART_HeaderBorder" Property="BorderThickness" Value="1" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.IsEnableHotTracking), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_HeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.SelectedHeaderBackground), RelativeSource={RelativeSource TemplatedParent}}" />
                <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource Mode=TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.EnableMouseHoverBackground), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_HeaderBorder" Property="Background" Value="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.SelectedHeaderBackground), RelativeSource={RelativeSource TemplatedParent}}" />
                <Setter TargetName="PART_HeaderBorder" Property="BorderThickness" Value="1" />
                <Setter TargetName="PART_ContentPresenter" Property="TextElement.Foreground" Value="{StaticResource PrimaryForeground}" />
            </MultiDataTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionNativeDockHeaderPresenterControlTemplate" TargetType="{x:Type tools_controls:DockHeaderPresenter}">
        <Border
            x:Name="PART_HeaderBorder"
            Width="Auto"
            Height="Auto"
            Background="{TemplateBinding Background}"
            BorderBrush="{TemplateBinding BorderBrush}"
            BorderThickness="{TemplateBinding BorderThickness}">
            <Border.ContextMenu>
                <tools_controls:CustomContextMenu x:Name="PART_ContextMenu" Focusable="false" />
            </Border.ContextMenu>
            <Grid>
                <DockPanel LastChildFill="True">
                    <ToggleButton
                        x:Name="PART_CloseButton"
                        Width="16"
                        Height="16"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        AutomationProperties.Name="Hide"
                        Command="tools_controls:DockHeaderPresenter.CloseCommand"
                        DockPanel.Dock="Right"
                        Focusable="True"
                        KeyboardNavigation.TabIndex="6"
                        Style="{StaticResource CaptionButtonStyle}"
                        Tag="Dock"
                        Template="{Binding Path=DockingManager.CloseButtonTemplate, RelativeSource={RelativeSource TemplatedParent}}">
                        <ToggleButton.ToolTip>
                            <ToolTip x:Name="CloseButtonTooltip">
                                <ToolTip.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ToolTip.Resources>
                                <TextBlock x:Name="PART_CloseButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=CloseButtonTooltipText}" />
                            </ToolTip>
                        </ToggleButton.ToolTip>
                        <ToggleButton.Content>
                            <Path
                                x:Name="CloseButtonPath1"
                                Width="8"
                                Height="8"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Stroke="{Binding Foreground, ElementName=PART_CloseButton}"
                                StrokeThickness="1"
                                SnapsToDevicePixels="True"
                                Stretch="Fill" 
                                Data="M1 11L6 6M6 6L11 1M6 6L11 11M6 6L1 1"/>
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ToggleButton
                        x:Name="PART_AwlButton"
                        Width="16"
                        Height="16"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        AutomationProperties.Name="AutoHide"
                        Command="tools_controls:DockHeaderPresenter.ChangeAwlStateCommand"
                        DockPanel.Dock="Right"
                        Focusable="True"
                        KeyboardNavigation.TabIndex="5"
                        Style="{StaticResource CaptionButtonStyle}"
                        Tag="Dock"
                        Template="{Binding Path=DockingManager.AwlButtonTemplate, RelativeSource={RelativeSource TemplatedParent}}">
                        <ToggleButton.ToolTip>
                            <ToolTip x:Name="PART_AwlButtonTooltip">
                                <ToolTip.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ToolTip.Resources>
                                <TextBlock x:Name="PART_AwlButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=AwlButtonTooltipText}" />
                            </ToolTip>
                        </ToggleButton.ToolTip>
                        <ToggleButton.Content>
                            <Path
                                x:Name="PinButtonPath2"
                                Width="10"
                                Height="10"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Fill="{Binding Foreground, ElementName=PART_AwlButton}"
                                Stretch="Fill"
                                Data="M7.74219 3.74609C7.82552 3.62109 7.86719 3.48308 7.86719 3.33203C7.86719 3.23308 7.84766 3.13672 7.80859 3.04297C7.77214 2.94922 7.71875 2.86719 7.64844 2.79688L5.1875 0.332031C5.11979 0.264328 5.03906 0.210938 4.94531 0.171875C4.85417 0.132812 4.75911 0.113281 4.66016 0.113281C4.51172 0.113281 4.375 0.153641 4.25 0.234375C4.125 0.3125 4.03255 0.419266 3.97266 0.554688L3.10547 2.46484C3.07682 2.52734 3.03255 2.57031 2.97266 2.59375L0.65625 3.51953C0.609375 3.53777 0.571615 3.5677 0.542969 3.60938C0.514322 3.65105 0.5 3.69661 0.5 3.74609C0.5 3.81641 0.52474 3.8763 0.574219 3.92578L2.14844 5.5L0 7.64453V8H0.351562L2.5 5.85156L4.07422 7.42578C4.1237 7.47527 4.18229 7.5 4.25 7.5C4.30729 7.5 4.35677 7.48308 4.39844 7.44922C4.44271 7.41536 4.47266 7.37239 4.48828 7.32031L5.17188 5.04688C5.18229 5.01042 5.20052 4.97917 5.22656 4.95312C5.25261 4.92448 5.28255 4.90364 5.31641 4.89062L7.40625 4.01953C7.54688 3.96223 7.65886 3.87109 7.74219 3.74609ZM4.51953 0.652344C4.5612 0.626297 4.60807 0.613281 4.66016 0.613281C4.72526 0.613281 4.78255 0.638016 4.83203 0.6875L7.29688 3.14844C7.34375 3.19531 7.36719 3.25391 7.36719 3.32422C7.36719 3.3763 7.35286 3.42317 7.32422 3.46484C7.29818 3.50391 7.26172 3.53386 7.21484 3.55469L5.12109 4.42969C5.01953 4.47395 4.92969 4.53777 4.85156 4.62109C4.77604 4.70442 4.72266 4.79948 4.69141 4.90625L4.12891 6.77734L1.19531 3.83984L3.15625 3.05859C3.24739 3.02214 3.32812 2.97136 3.39844 2.90625C3.46875 2.83855 3.52344 2.76042 3.5625 2.67188L4.42969 0.761719C4.45052 0.714844 4.48047 0.678391 4.51953 0.652344Z"/>
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ToggleButton
                        x:Name="PART_ContextMenuButton"
                        Width="16"
                        Height="16"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        AutomationProperties.Name="WindowMenu"
                        Command="tools_controls:DockHeaderPresenter.OpenContextMenuCommand"
                        DockPanel.Dock="Right"
                        Focusable="True"
                        IsChecked="{Binding Path=IsContextMenuOpen, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockHeaderPresenter}}}"
                        KeyboardNavigation.TabIndex="4"
                        Style="{StaticResource CaptionButtonStyle}"
                        Tag="Dock"
                        Template="{Binding Path=DockingManager.MenuButtonTemplate, RelativeSource={RelativeSource TemplatedParent}}">
                        <ToggleButton.ToolTip>
                            <ToolTip x:Name="PART_ContextMenuButtonTooltip">
                                <ToolTip.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ToolTip.Resources>
                                <TextBlock x:Name="PART_ContextMenuButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=ContextMenuButtonTooltipText}" />
                            </ToolTip>
                        </ToggleButton.ToolTip>
                        <ToggleButton.Content>
                            <Path
                                x:Name="MenuButtonPath2"
                                Width="8"
                                Height="4"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Fill="{Binding Foreground, ElementName=PART_ContextMenuButton}"
                                Stretch="Fill"
                                Data="M1.71387 0.286133C1.62728 0.199544 1.52474 0.15625 1.40625 0.15625C1.28776 0.15625 1.18522 0.199544 1.09863 0.286133C1.01204 0.372721 0.96875 0.475261 0.96875 0.59375C0.96875 0.71224 1.01204 0.814779 1.09863 0.901367L5.91113 5.71387C5.99772 5.80046 6.10026 5.84375 6.21875 5.84375C6.33724 5.84375 6.43978 5.80046 6.52637 5.71387L11.3389 0.901367C11.4255 0.814779 11.4688 0.71224 11.4688 0.59375C11.4688 0.47526 11.4255 0.372721 11.3389 0.286133C11.2523 0.199544 11.1497 0.15625 11.0312 0.15625C10.9128 0.15625 10.8102 0.199544 10.7236 0.286133L6.21875 4.78418L1.71387 0.286133Z"/>
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ToggleButton
                        x:Name="PART_MaximizeButton"
                        Width="16"
                        Height="16"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        Command="tools_controls:DockHeaderPresenter.MaximizeStateCommand"
                        DockPanel.Dock="Right"
                        Focusable="True"
                        KeyboardNavigation.TabIndex="3"
                        Style="{StaticResource CaptionButtonStyle}"
                        Tag="Dock"
                        Template="{Binding Path=DockingManager.MaximizeButtonTemplate, RelativeSource={RelativeSource TemplatedParent}}"
                        Visibility="Collapsed">
                        <ToggleButton.ToolTip>
                            <ToolTip x:Name="PART_MaximizeButtonTooltip">
                                <ToolTip.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ToolTip.Resources>
                                <TextBlock x:Name="PART_MaximizeButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=Maximize}" />
                            </ToolTip>
                        </ToggleButton.ToolTip>
                        <ToggleButton.Content>
                            <Path
                               x:Name="MaximizeButtonPath2"
                               Width="9"
                               Height="9"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Stroke="{Binding Foreground, ElementName=PART_MaximizeButton}"
                               StrokeThickness="1"
                               SnapsToDevicePixels="False"
                               Stretch="Fill"
                               Data="M0.5 1.25C0.5 0.835786 0.835786 0.5 1.25 0.5H7.75C8.16421 0.5 8.5 0.835786 8.5 1.25V7.75C8.5 8.16421 8.16421 8.5 7.75 8.5H1.25C0.835786 8.5 0.5 8.16421 0.5 7.75V1.25Z"/>
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ToggleButton
                        x:Name="PART_RestoreButton"
                        Width="16"
                        Height="16"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        Command="tools_controls:DockHeaderPresenter.RestoreStateCommand"
                        DockPanel.Dock="Right"
                        Focusable="True"
                        KeyboardNavigation.TabIndex="2"
                        Style="{StaticResource CaptionButtonStyle}"
                        Tag="Dock"
                        Template="{Binding Path=DockingManager.RestoreButtonTemplate, RelativeSource={RelativeSource TemplatedParent}}"
                        Visibility="Collapsed">
                        <ToggleButton.ToolTip>
                            <ToolTip x:Name="PART_RestoreButtonTooltip">
                                <ToolTip.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ToolTip.Resources>
                                <TextBlock x:Name="PART_RestoreButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=Restore}" />
                            </ToolTip>
                        </ToggleButton.ToolTip>
                        <ToggleButton.Content>
                            <Path
                                x:Name="RestoreButtonPath2"
                                Width="10"
                                Height="10"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Stroke="{Binding Foreground, ElementName=PART_RestoreButton}"
                                StrokeThickness="1"
                                SnapsToDevicePixels="False"
                                Stretch="Fill"
                                Data="M2.5 0.5H8C8.82843 0.5 9.5 1.17157 9.5 2V7.5M1.5 9.5H6.5C7.05228 9.5 7.5 9.05228 7.5 8.5V3.5C7.5 2.94772 7.05228 2.5 6.5 2.5H1.5C0.947715 2.5 0.5 2.94772 0.5 3.5V8.5C0.5 9.05228 0.947715 9.5 1.5 9.5Z"/>
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ToggleButton
                        x:Name="PART_MinimizeButton"
                        Width="16"
                        Height="16"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        Command="tools_controls:DockHeaderPresenter.MinimizeStateCommand"
                        DockPanel.Dock="Right"
                        Focusable="True"
                        KeyboardNavigation.TabIndex="1"
                        Tag="Dock"
                        Style="{StaticResource CaptionButtonStyle}"
                        Template="{Binding Path=DockingManager.MinimizeButtonTemplate, RelativeSource={RelativeSource TemplatedParent}}"
                        Visibility="Collapsed">
                        <ToggleButton.ToolTip>
                            <ToolTip x:Name="PART_MinimizeButtonTooltip">
                                <ToolTip.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ToolTip.Resources>
                                <TextBlock x:Name="PART_MinimizeButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=Minimize}" />
                            </ToolTip>
                        </ToggleButton.ToolTip>
                        <ToggleButton.Content>
                            <Path
                                x:Name="MinimizeButtonPath2"
                                Width="10"
                                Height="1"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Stroke="{Binding Foreground, ElementName=PART_MinimizeButton}"
                                StrokeThickness="1"
                                SnapsToDevicePixels="False"
                                Stretch="Fill"
                                Data="M0.5 0.5H10.5"/>
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ContentPresenter
                        x:Name="PART_ContentPresenter"
                        Margin="6, 0, 0, 0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Content="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.Header), RelativeSource={RelativeSource TemplatedParent}}"
                        ContentTemplate="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.HeaderTemplate), RelativeSource={RelativeSource TemplatedParent}}"
                        ContentTemplateSelector="{StaticResource DockTrimmingTemplate}"
                        IsHitTestVisible="True"
                        ToolTip="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CaptionToolTip), RelativeSource={RelativeSource TemplatedParent}}">
                        <ContentPresenter.Resources>
                            <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                        </ContentPresenter.Resources>
                    </ContentPresenter>
                </DockPanel>
                <Border
                    x:Name="PART_DragBorder"
                    Background="{StaticResource BorderAlt}"
                    Visibility="Collapsed" />
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMaximize), RelativeSource={RelativeSource TemplatedParent}}" Value="false">
                <Setter TargetName="PART_MaximizeButton" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMinimize), RelativeSource={RelativeSource TemplatedParent}}" Value="false">
                <Setter TargetName="PART_MinimizeButton" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_MaximizeButton" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MinimizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_MinimizeButton" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMaximize), RelativeSource={RelativeSource TemplatedParent}}" Value="false" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonMode), RelativeSource={RelativeSource TemplatedParent}}" Value="Disable" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.MaximizeButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Visible" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_MaximizeButton" Property="Visibility" Value="Visible" />
                <Setter TargetName="PART_MaximizeButton" Property="IsEnabled" Value="False" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMaximize), RelativeSource={RelativeSource TemplatedParent}}" Value="true" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonMode), RelativeSource={RelativeSource TemplatedParent}}" Value="Disable" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.MaximizeButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Visible" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_MaximizeButton" Property="IsEnabled" Value="True" />
            </MultiDataTrigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMaximize), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.MaximizeButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Visible" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_MaximizeButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MinimizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMinimize), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.MinimizeButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Visible" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_MinimizeButton" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MaximizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMaximize), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.MaximizeButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Collapsed" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_MaximizeButton" Property="Visibility" Value="Collapsed" />
            </MultiDataTrigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.MinimizeButtonEnabled), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanMinimize), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.MinimizeButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Collapsed" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_MinimizeButton" Property="Visibility" Value="Collapsed" />
            </MultiDataTrigger>

            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.RestoreButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Collapsed">
                <Setter TargetName="PART_RestoreButton" Property="Visibility" Value="Collapsed" />
            </DataTrigger>

            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.RestoreButtonVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="Visible">
                <Setter TargetName="PART_RestoreButton" Property="Visibility" Value="Visible" />
                <Setter TargetName="PART_MaximizeButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_MaximizeButton" Property="IsEnabled" Value="True" />
            </DataTrigger>

            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.State), RelativeSource={RelativeSource TemplatedParent}}" Value="Float">
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </DataTrigger>

            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.State), RelativeSource={RelativeSource TemplatedParent}}" Value="AutoHidden">
                <Setter TargetName="PART_AwlButton" Property="IsChecked" Value="true" />
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.State), RelativeSource={RelativeSource TemplatedParent}}" Value="AutoHidden" />
                    <Condition Binding="{Binding Path=IsTouchEnabled, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockingManager}}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_AwlButton" Property="IsChecked" Value="false" />
            </MultiDataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanClose), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanFloat), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledFloatingMenuItem" Value="False" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanDock), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanDocument), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledTabbedMenuItem" Value="False" />
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanClose), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CloseTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="CloseActive" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanClose), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CloseTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="CloseAll" />
                    <Condition Binding="{Binding Path=CanCloseGroup, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_CloseButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
            </MultiDataTrigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.DockingManager).(tools_controls:DockingManager.AutoHideVisibility), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.IsDragging), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_DragBorder" Property="Visibility" Value="Visible" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanAutoHide), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanAutoHide), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="True" />
                    <Condition Binding="{Binding Path=AutoHideTabsMode, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="AutoHideGroup" />
                    <Condition Binding="{Binding Path=CanAutoHideGroup, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockedElementTabbedHost}}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                    <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanAutoHide), RelativeSource={RelativeSource TemplatedParent}}" Value="False" />
                </MultiDataTrigger.Conditions>
                <Setter TargetName="PART_AwlButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContextMenu" Property="IsEnabledAutoHideMenuItem" Value="False" />
            </MultiDataTrigger>
            <Trigger SourceName="PART_AwlButton" Property="IsChecked" Value="True">
                <Setter TargetName="PinButtonPath2" Property="Data" Value="M7.87289 3.685C7.95763 3.5582 8 3.4182 8 3.26498C8 3.1646 7.98014 3.06686 7.94042 2.97177C7.90334 2.87667 7.84906 2.79346 7.77756 2.72214L5.27507 0.221893C5.20622 0.153214 5.12413 0.0990601 5.0288 0.059433C4.93612 0.0198135 4.83946 0 4.73883 0C4.58788 0 4.44886 0.040947 4.32175 0.122833C4.19464 0.20208 4.10063 0.310387 4.03972 0.447746L3.15789 2.38534C3.12877 2.44874 3.08375 2.49232 3.02284 2.5161L0.667328 3.45517C0.619663 3.47366 0.581264 3.50404 0.552135 3.54631C0.523006 3.58858 0.508442 3.6348 0.508442 3.685C0.508442 3.75632 0.5336 3.81708 0.583912 3.86726L2.18471 5.4641L0 7.63942V8H0.357498L2.54221 5.82071L4.143 7.41753C4.19331 7.46773 4.2529 7.49281 4.32175 7.49281C4.38 7.49281 4.43032 7.47565 4.47269 7.44131C4.51771 7.40697 4.54816 7.36339 4.56405 7.31055L5.25919 5.00446C5.26978 4.96747 5.28831 4.93578 5.3148 4.90936C5.34128 4.88031 5.37173 4.85918 5.40616 4.84596L7.53128 3.96236C7.67428 3.90424 7.78815 3.81178 7.87289 3.685Z"/>
                <Setter TargetName="PinButtonPath2" Property="Height" Value="10" />
                <Setter TargetName="PinButtonPath2" Property="Width" Value="10" />
            </Trigger>
            <Trigger Property="Visibility" Value="Collapsed">
                <Setter TargetName="PART_ContentPresenter" Property="Content" Value="{x:Null}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SyncfusionDockHeaderPresenterStyle" TargetType="{x:Type tools_controls:DockHeaderPresenter}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="MinHeight" Value="24" />
        <Setter Property="Foreground" Value="{Binding Path=DockingManager.HeaderForeground, RelativeSource={RelativeSource Mode=Self}}" />
        <Setter Property="Background" Value="{Binding Path=DockingManager.HeaderBackground, RelativeSource={RelativeSource Mode=Self}}" />
        <Setter Property="BorderBrush" Value="{Binding Path=DockingManager.HeaderBorderBrush, RelativeSource={RelativeSource Mode=Self}}" />
        <Setter Property="BorderThickness" Value="{Binding Path=DockingManager.HeaderBorderThickness, RelativeSource={RelativeSource Mode=Self}}" />
		<Setter Property="Visibility">
            <Setter.Value>
                <MultiBinding Converter="{StaticResource HeaderVisibilityConverter}">
                    <Binding Path="ElementHost.HostedElement.(tools_controls:DockingManager.NoHeader)"
                                                 RelativeSource="{RelativeSource Mode=Self}"/>
                    <Binding Path="(tools_controls:DockingManager.NoHeader)" RelativeSource="{RelativeSource Mode=Self}"/>
                </MultiBinding>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
            </Trigger>
            <Trigger Property="UseNativeFloatWindow" Value="True">
                <Setter Property="Template" Value="{StaticResource SyncfusionNativeDockHeaderPresenterControlTemplate}" />
            </Trigger>
            <Trigger Property="UseNativeFloatWindow" Value="False">
                <Setter Property="Template" Value="{StaticResource SyncfusionDockHeaderPresenterControlTemplate}" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=IsTemplateParenKeyboardFocusWithin, RelativeSource={RelativeSource Self}}" Value="True">
                <Setter Property="Foreground" Value="{Binding Path=DockingManager.HeaderForegroundSelected, RelativeSource={RelativeSource Mode=Self}}" />
                <Setter Property="Background" Value="{Binding Path=DockingManager.SelectedHeaderBackground, RelativeSource={RelativeSource Mode=Self}}" />
                <Setter Property="BorderBrush" Value="Transparent" />
            </DataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=DockingManager.EnableMouseHoverBackground, RelativeSource={RelativeSource Self}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Background" Value="{Binding Path=DockingManager.HeaderMouseOverBackground, RelativeSource={RelativeSource Mode=Self}}" />
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=DockingManager.IsEnableHotTracking, RelativeSource={RelativeSource Mode=Self}}" Value="True" />
                </MultiDataTrigger.Conditions>
                <Setter Property="Background" Value="{Binding Path=DockingManager.HeaderMouseOverBackground, RelativeSource={RelativeSource Mode=Self}}" />
            </MultiDataTrigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt2}" />
                <Setter Property="BorderBrush" Value="{StaticResource ContentBackgroundAlt2}" />
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionDockHeaderPresenterStyle}" TargetType="{x:Type tools_controls:DockHeaderPresenter}" />

    <Style x:Key="SyncfusionHostAdornerVS2005Style" TargetType="{x:Type tools_controls:HostAdornerVS2005}">
        <Setter Property="Background" Value="{StaticResource PrimaryBackgroundOpacity3}" />
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBackgroundOpacity3}" />
        <Setter Property="BorderThickness" Value="1" />
    </Style>
    <Style BasedOn="{StaticResource SyncfusionHostAdornerVS2005Style}" TargetType="{x:Type tools_controls:HostAdornerVS2005}" />

    <Style x:Key="SyncfusionScrollButtonsBarStyle" TargetType="{x:Type tools_controls:ScrollButtonsBar}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Foreground" Value="{StaticResource IconColor}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:ScrollButtonsBar}">
                    <StackPanel
                        x:Name="ScrollButtonsBarStackPanel"
                        Canvas.Left="0"
                        Canvas.Top="0"
                        Panel.ZIndex="2"
                        Background="{TemplateBinding Background}"
                        Orientation="Horizontal">
                        <RepeatButton
                            x:Name="PART_HomePage"
                            Width="16"
                            Height="16"
                            Padding="0"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            BorderThickness="0"
                            Command="tools_controls:SidePanel.HomePageCommand"
                            Style="{StaticResource WPFGlyphRepeatButtonStyle}">
                            <RepeatButton.Content>
                                <Path
                                    x:Name="First1"
                                    Width="5"
                                    Height="8"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Stroke="{Binding ElementName=PART_HomePage, Path=Foreground}"
                                    StrokeThickness="1"
                                    Stretch="Fill"
                                    Data="M5.5 1L1.5 5L5.5 9M0.5 1V9"/>
                            </RepeatButton.Content>
                        </RepeatButton>
                        <RepeatButton
                            x:Name="PART_PreviousTab"
                            Width="16"
                            Height="16"
                            Padding="0"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            BorderThickness="0"
                            Command="tools_controls:SidePanel.PreviousCommand"
                            Style="{StaticResource WPFGlyphRepeatButtonStyle}">
                            <RepeatButton.Content>
                                <Path
                                    x:Name="Previous1"
                                    Width="4"
                                    Height="8"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Stroke="{Binding ElementName=PART_PreviousTab, Path=Foreground}"
                                    StrokeThickness="1"
                                    Stretch="Fill"
                                    Data="M5 1L1 5L5 9"/>
                            </RepeatButton.Content>
                        </RepeatButton>
                        <RepeatButton
                            x:Name="PART_NextTab"
                            Width="16"
                            Height="16"
                            Padding="0"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            BorderThickness="0"
                            Command="tools_controls:SidePanel.NextCommand"
                            Style="{StaticResource WPFGlyphRepeatButtonStyle}">
                            <RepeatButton.Content>
                                <Path
                                    x:Name="Next1"
                                    Width="4"
                                    Height="8"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Stroke="{Binding ElementName=PART_NextTab, Path=Foreground}"
                                    StrokeThickness="1"
                                    Stretch="Fill"
                                    Data="M1 1L5 5L1 9"/>
                            </RepeatButton.Content>
                        </RepeatButton>
                        <RepeatButton
                            x:Name="PART_EndPage"
                            Width="16"
                            Height="16"
                            Padding="0"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            BorderThickness="0"
                            Command="tools_controls:SidePanel.EndPageCommand"
                            Style="{StaticResource WPFGlyphRepeatButtonStyle}">
                            <RepeatButton.Content>
                                <Path
                                    x:Name="Last1"
                                    Width="5"
                                    Height="8"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Stroke="{Binding ElementName=PART_EndPage, Path=Foreground}"
                                    StrokeThickness="1"
                                    Stretch="Fill"
                                    Data="M0.5 1L4.5 5L0.5 9M5.5 1V9"/>
                            </RepeatButton.Content>
                        </RepeatButton>
                    </StackPanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionScrollButtonsBarStyle}" TargetType="{x:Type tools_controls:ScrollButtonsBar}" />

    <ControlTemplate x:Key="SyncfusionScrollableSidePanelControlTemplate" TargetType="{x:Type tools_controls:SidePanel}">
        <Canvas ClipToBounds="False" KeyboardNavigation.TabNavigation="Local">
                <tools_controls:ScrollButtonsBar
                    x:Name="PART_ScrollButtons"
                    Background="{TemplateBinding Background}"
                    Focusable="False"
                    Canvas.Left="0"
                    Canvas.Top="0" Panel.ZIndex="2"
                    Style="{Binding Path=ScrollButtonsBarStyle, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockingManager}}}"
                    Tag="{Binding Path=PanelSide, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                    Visibility="Collapsed" />
              
            <Border
                    x:Name="PART_BorderSeparator"
                    Width="8"
                    Background="{TemplateBinding Background}"
                    Visibility="Collapsed" />
            <Border
                x:Name="PART_BorderName"
                Width="{TemplateBinding ActualWidth}"
                Height="{TemplateBinding ActualHeight}"
                Margin="0"
                Panel.ZIndex="1"
                Background="{TemplateBinding Background}"
                BorderBrush="{StaticResource BorderAlt}"
                BorderThickness="{Binding Path=SidePanelBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockingManager}}}"
                ClipToBounds="True">
                <Border.ContextMenu>
                    <ContextMenu
                        x:Name="PART_BorderContextMenu"
                        ItemsSource="{TemplateBinding TabChildren}"
                        Style="{StaticResource MaterialSideContextMenuStyle}" />
                </Border.ContextMenu>
                <ScrollViewer
                    x:Name="PART_ScrollPanel"
                    Background="{TemplateBinding Background}"
                    Focusable="False"
                    HorizontalScrollBarVisibility="Hidden"
                    VerticalScrollBarVisibility="Hidden">
                    <tools_controls:DirectTabPanel
                        x:Name="PART_PanelName"
                        Focusable="False"
                        IsItemsHost="True" />
                </ScrollViewer>
            </Border>
            <tools_controls:OpacityDockPanel
                LastChildFill="True"
                Opacity="{Binding Path=ContentOpacity, RelativeSource={RelativeSource TemplatedParent}}"
                Visibility="{Binding ElementName=PART_Shadow, Path=Visibility}">
                <DockPanel.RenderTransform>
                    <TransformGroup>
                        <TranslateTransform>
                            <TranslateTransform.X>
                                <MultiBinding Converter="{StaticResource SideToCoordinate}" ConverterParameter="XCoordinate">
                                    <Binding Path="TabStripPlacement" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(Selector.SelectedItem).(tools_controls:DockingManager.DesiredWidthInDockedMode)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="ContentRenderTransformX" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(ShadowBorder).(FrameworkElement.Width)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelBorderThickness)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelSize)" RelativeSource="{RelativeSource TemplatedParent}" />
                                </MultiBinding>
                            </TranslateTransform.X>
                            <TranslateTransform.Y>
                                <MultiBinding Converter="{StaticResource SideToCoordinate}" ConverterParameter="YCoordinate">
                                    <Binding Path="TabStripPlacement" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(Selector.SelectedItem).(tools_controls:DockingManager.DesiredHeightInDockedMode)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="ContentRenderTransformY" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(ShadowBorder).(FrameworkElement.Height)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelBorderThickness)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelSize)" RelativeSource="{RelativeSource TemplatedParent}" />
                                </MultiBinding>
                            </TranslateTransform.Y>
                        </TranslateTransform>
                        <ScaleTransform ScaleX="{Binding Path=ContentScaleX, RelativeSource={RelativeSource TemplatedParent}}" ScaleY="{Binding Path=ContentScaleY, RelativeSource={RelativeSource TemplatedParent}}" />
                    </TransformGroup>
                </DockPanel.RenderTransform>
                <Border
                    x:Name="PART_Shadow"
                    Background="Transparent"
                    DockPanel.Dock="{Binding Path=PanelSide, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource InvertDock}}" />
                <DockPanel x:Name="PART_SideDockPanel">
                    <tools_controls:Splitter
                        x:Name="PART_SideSplitter"
                        DockPanel.Dock="{Binding Path=PanelSide, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource InvertDock}}"
                        Orientation="{Binding Path=PanelSide, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource DockToOrientation}}" />
                    <Border
                        x:Name="WrapBorder"
                        Background="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.PreviewPanelBackground), RelativeSource={RelativeSource TemplatedParent}}"
                        BorderBrush="{StaticResource BorderAlt}"
                        BorderThickness="1">
                        <DockPanel
                            x:Name="FlyPanel"
                            Focusable="True"
                            KeyboardNavigation.TabNavigation="Cycle">
                            <DockPanel.Width>
                                <MultiBinding
                                    Converter="{StaticResource ActualWidthSubstractBorder}"
                                    ConverterParameter="ContentWidthParameter"
                                    UpdateSourceTrigger="PropertyChanged">
                                    <Binding
                                        Path="(Selector.SelectedItem).(tools_controls:DockingManager.DesiredWidthInDockedMode)"
                                        RelativeSource="{RelativeSource TemplatedParent}"
                                        UpdateSourceTrigger="PropertyChanged" />
                                    <Binding Path="ActualWidth" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding ElementName="PART_SideSplitter" Path="ActualWidth" />
                                    <Binding Path="PanelSide" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding ElementName="WrapBorder" Path="BorderThickness" />
                                </MultiBinding>
                            </DockPanel.Width>
                            <DockPanel.Height>
                                <MultiBinding Converter="{StaticResource ActualWidthSubstractBorder}" ConverterParameter="ContentHeightParameter">
                                    <Binding Path="(Selector.SelectedItem).(tools_controls:DockingManager.DesiredHeightInDockedMode)" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="ActualHeight" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding ElementName="PART_SideSplitter" Path="ActualHeight" />
                                    <Binding Path="PanelSide" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding ElementName="WrapBorder" Path="BorderThickness" />
                                </MultiBinding>
                            </DockPanel.Height>
                            <tools_controls:DockHeaderPresenter
                                x:Name="PART_Header"
                                DockPanel.Dock="Top"
                                IsRichHeader="False"
                                IsTemplateParenKeyboardFocusWithin="{TemplateBinding IsShowedFocusedItem}"
                                Style="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.HeaderStyle), RelativeSource={RelativeSource Self}}" >
                                
                            </tools_controls:DockHeaderPresenter>
                            <Border x:Name="Content">
                                <ContentPresenter
                                    x:Name="PART_SelectedContent"
                                    Margin="{TemplateBinding Margin}"
                                    ContentSource="SelectedContent" />
                            </Border>
                        </DockPanel>
                    </Border>
                </DockPanel>
            </tools_controls:OpacityDockPanel>
        </Canvas>
        <ControlTemplate.Triggers>
            <Trigger SourceName="PART_ScrollButtons" Property="Visibility" Value="Visible">
                <Setter TargetName="PART_BorderName" Property="Canvas.Left" Value="{Binding ActualWidth, ElementName=PART_ScrollButtons}" />
                <Setter TargetName="PART_BorderSeparator" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger SourceName="PART_ScrollButtons" Property="Visibility" Value="Collapsed">
                <Setter TargetName="PART_BorderName" Property="Canvas.Left" Value="0" />
                <Setter TargetName="PART_BorderSeparator" Property="Visibility" Value="Collapsed" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.IsDragging), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="True">
                <Setter Property="IsEnabled" Value="False" />
            </DataTrigger>
            <Trigger Property="PanelSide" Value="Top">
                <Setter TargetName="PART_Shadow" Property="Height" Value="4" />
                <Setter TargetName="PART_Shadow" Property="Background" Value="Transparent" />
                <Setter TargetName="PART_ScrollButtons" Property="Height" Value="25" />
            </Trigger>
            <Trigger Property="PanelSide" Value="Bottom">
                <Setter TargetName="PART_Shadow" Property="Height" Value="4" />
                <Setter TargetName="PART_Shadow" Property="Background" Value="Transparent" />
                <Setter TargetName="PART_ScrollButtons" Property="Height" Value="25" />
            </Trigger>
            <Trigger Property="PanelSide" Value="Right">
                <Setter TargetName="PART_Shadow" Property="Width" Value="4" />
                <Setter TargetName="PART_ScrollButtons" Property="Height" Value="25" />
                <Setter TargetName="PART_BorderSeparator" Property="Width" Value="{Binding Path=ActualWidth, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:SidePanel}} }" />
                <Setter TargetName="PART_BorderSeparator" Property="Height" Value="0" />
                <Setter TargetName="PART_BorderName" Property="Canvas.Left" Value="0" />
                <Setter TargetName="PART_ScrollButtons" Property="Canvas.Left" Value="3" />
                <Setter TargetName="PART_BorderName" Property="Canvas.Top" Value="{Binding ElementName=PART_ScrollButtons, Path=ActualWidth}" />
                <Setter TargetName="PART_ScrollButtons" Property="RenderTransform"  >
                    <Setter.Value>
                        <RotateTransform Angle="90" CenterX="10.5" CenterY="10.5"/>
                    </Setter.Value>
                </Setter>
                <Setter TargetName="PART_Shadow" Property="Background" Value="Transparent" />
            </Trigger>
            <Trigger Property="PanelSide" Value="Left">
                <Setter TargetName="PART_Shadow" Property="Width" Value="4" />
                <Setter TargetName="PART_ScrollButtons" Property="Height" Value="25" />
                <Setter TargetName="PART_BorderSeparator" Property="Width" Value="{Binding Path=ActualWidth, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:SidePanel}} }" />
                <Setter TargetName="PART_BorderSeparator" Property="Height" Value="0" />
                <Setter TargetName="PART_BorderName" Property="Canvas.Left" Value="0" />
                <Setter TargetName="PART_ScrollButtons" Property="Canvas.Left" Value="3" />
                <Setter TargetName="PART_BorderName" Property="Canvas.Top" Value="{Binding ElementName=PART_ScrollButtons, Path=ActualWidth}" />
                <Setter TargetName="PART_ScrollButtons" Property="RenderTransform"  >
                    <Setter.Value>
                        <RotateTransform Angle="90" CenterX="10.5" CenterY="10.5"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding ElementName=PART_ScrollButtons, Path=Visibility}" Value="Collapsed"/>
                </MultiDataTrigger.Conditions>
                <MultiDataTrigger.Setters>
                    <Setter TargetName="PART_BorderName" Property="Canvas.Top" Value="0" />
                </MultiDataTrigger.Setters>
            </MultiDataTrigger>
            <Trigger Property="IsContentHiden" Value="True">
                <Setter TargetName="FlyPanel" Property="Visibility" Value="Hidden" />
            </Trigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.AutoHideAnimationMode), RelativeSource={RelativeSource Self}}" Value="Slide" />
                    <Condition Binding="{Binding Path=IsContentHiden, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=PanelSide, RelativeSource={RelativeSource Self}, Converter={StaticResource DockToOrientation}}" Value="Vertical" />
                </MultiDataTrigger.Conditions>
                <MultiDataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation
                                Storyboard.TargetName="PART_Shadow"
                                Storyboard.TargetProperty="Width"
                                To="0"
                                Duration="{StaticResource ShadowDuration}" />
                        </Storyboard>
                    </BeginStoryboard>
                </MultiDataTrigger.EnterActions>
                <MultiDataTrigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation
                                Storyboard.TargetName="PART_Shadow"
                                Storyboard.TargetProperty="Width"
                                To="4"
                                Duration="{StaticResource ShadowDuration}" />
                        </Storyboard>
                    </BeginStoryboard>
                </MultiDataTrigger.ExitActions>
            </MultiDataTrigger>
            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.AutoHideAnimationMode), RelativeSource={RelativeSource Self}}" Value="Slide" />
                    <Condition Binding="{Binding Path=IsContentHiden, RelativeSource={RelativeSource Self}}" Value="True" />
                    <Condition Binding="{Binding Path=PanelSide, RelativeSource={RelativeSource Self}, Converter={StaticResource DockToOrientation}}" Value="Horizontal" />
                </MultiDataTrigger.Conditions>
                <MultiDataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation
                                Storyboard.TargetName="PART_Shadow"
                                Storyboard.TargetProperty="Height"
                                To="0"
                                Duration="{StaticResource ShadowDuration}" />
                        </Storyboard>
                    </BeginStoryboard>
                </MultiDataTrigger.EnterActions>
                <MultiDataTrigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation
                                Storyboard.TargetName="PART_Shadow"
                                Storyboard.TargetProperty="Height"
                                To="4"
                                Duration="{StaticResource ShadowDuration}" />
                        </Storyboard>
                    </BeginStoryboard>
                </MultiDataTrigger.ExitActions>
            </MultiDataTrigger>
            <Trigger SourceName="WrapBorder" Property="Visibility" Value="Visible">
                <Setter TargetName="WrapBorder" Property="BorderThickness" Value="{Binding Path=ElementBorderThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" />
                <Setter TargetName="PART_BorderName" Property="BorderBrush" Value="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelBorderBrush), RelativeSource={RelativeSource TemplatedParent}}" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.ShowShadowOnSidePanel), RelativeSource={RelativeSource Self}}" Value="False">
                <Setter TargetName="PART_Shadow" Property="Background" Value="Transparent" />
            </DataTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionPopupScrollableSidePanelControlTemplate" TargetType="{x:Type tools_controls:SidePanel}">
        <Canvas ClipToBounds="False" KeyboardNavigation.TabNavigation="Local">
            <tools_controls:ScrollButtonsBar
                    x:Name="PART_ScrollButtons"
                    Background="{TemplateBinding Background}"
                    Focusable="False"
                    Canvas.Left="0"
                    Canvas.Top="0" Panel.ZIndex="2"
                    Style="{Binding Path=ScrollButtonsBarStyle, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockingManager}}}"
                    Visibility="Collapsed" />
            <Border
                    x:Name="PART_BorderSeparator"
                    Width="8"
                    Background="{TemplateBinding Background}"
                    Visibility="Collapsed" />
            <Border
                x:Name="PART_BorderName"
                Width="{TemplateBinding ActualWidth}"
                Height="{TemplateBinding ActualHeight}"
                Margin="0"
                Panel.ZIndex="1"
                Background="{TemplateBinding Background}"
                BorderBrush="{StaticResource BorderAlt}"
                BorderThickness="{Binding Path=SidePanelBorderThickness, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:DockingManager}}}"
                ClipToBounds="True">
                <Border.ContextMenu>
                    <ContextMenu
                        x:Name="PART_BorderContextMenu"
                        ItemsSource="{TemplateBinding TabChildren}"
                        Style="{StaticResource MaterialSideContextMenuStyle}" />
                </Border.ContextMenu>
                <ScrollViewer
                    x:Name="PART_ScrollPanel"
                    Focusable="False"
                    HorizontalScrollBarVisibility="Hidden"
                    VerticalScrollBarVisibility="Hidden">
                    <tools_controls:DirectTabPanel
                        x:Name="PART_PanelName"
                        Focusable="False"
                        IsItemsHost="True" />
                </ScrollViewer>
            </Border>
            <tools_controls:PopupSidePanel x:Name="PART_PopupPanel">
                <DockPanel>
                    <DockPanel x:Name="PART_SideDockPanel">
                        <tools_controls:Splitter
                            x:Name="PART_SideSplitter"
                            DockPanel.Dock="{Binding Path=PanelSide, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource InvertDock}}"
                            Orientation="{Binding Path=PanelSide, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource DockToOrientation}}" />
                        <Border
                            x:Name="WrapBorder"
                            Background="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.PreviewPanelBackground), RelativeSource={RelativeSource TemplatedParent}}"
                            BorderBrush="Transparent"
                            BorderThickness="1">
                            <DockPanel
                                x:Name="FlyPanel"
                                Focusable="True"
                                KeyboardNavigation.TabNavigation="Cycle">
                                <DockPanel.Width>
                                    <MultiBinding
                                        Converter="{StaticResource ActualWidthSubstractBorder}"
                                        ConverterParameter="ContentWidthParameter"
                                        UpdateSourceTrigger="PropertyChanged">
                                        <Binding
                                            Path="(Selector.SelectedItem).(tools_controls:DockingManager.DesiredWidthInDockedMode)"
                                            RelativeSource="{RelativeSource TemplatedParent}"
                                            UpdateSourceTrigger="PropertyChanged" />
                                        <Binding Path="ActualWidth" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding ElementName="PART_SideSplitter" Path="ActualWidth" />
                                        <Binding Path="PanelSide" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding ElementName="WrapBorder" Path="BorderThickness" />
                                    </MultiBinding>
                                </DockPanel.Width>
                                <DockPanel.Height>
                                    <MultiBinding Converter="{StaticResource ActualWidthSubstractBorder}" ConverterParameter="ContentHeightParameter">
                                        <Binding Path="(Selector.SelectedItem).(tools_controls:DockingManager.DesiredHeightInDockedMode)" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="ActualHeight" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding ElementName="PART_SideSplitter" Path="ActualHeight" />
                                        <Binding Path="PanelSide" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding ElementName="WrapBorder" Path="BorderThickness" />
                                    </MultiBinding>
                                </DockPanel.Height>
                                <tools_controls:DockHeaderPresenter
                                    x:Name="PART_Header"
                                    DockPanel.Dock="Top"
                                    IsRichHeader="False"
                                    IsTemplateParenKeyboardFocusWithin="{TemplateBinding IsShowedFocusedItem}"
                                    Style="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.HeaderStyle), RelativeSource={RelativeSource Self}}" >
                                    
                                </tools_controls:DockHeaderPresenter>
                                <Border x:Name="Content">
                                    <ContentPresenter
                                        x:Name="PART_SelectedContent"
                                        Margin="{TemplateBinding Margin}"
                                        ContentSource="SelectedContent" />
                                </Border>
                            </DockPanel>
                        </Border>
                    </DockPanel>
                </DockPanel>
            </tools_controls:PopupSidePanel>
        </Canvas>
        <ControlTemplate.Triggers>
            <Trigger SourceName="PART_ScrollButtons" Property="Visibility" Value="Visible">
                <Setter TargetName="PART_BorderName" Property="Canvas.Left" Value="{Binding ActualWidth, ElementName=PART_ScrollButtons}" />
                <Setter TargetName="PART_BorderSeparator" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger SourceName="PART_ScrollButtons" Property="Visibility" Value="Collapsed">
                <Setter TargetName="PART_BorderName" Property="Canvas.Left" Value="0" />
                <Setter TargetName="PART_BorderSeparator" Property="Visibility" Value="Collapsed" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.IsDragging), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="True">
                <Setter Property="IsEnabled" Value="False" />
            </DataTrigger>
            <Trigger Property="IsContentHiden" Value="True">
                <Setter TargetName="FlyPanel" Property="Visibility" Value="Hidden" />
            </Trigger>
            <Trigger SourceName="WrapBorder" Property="Visibility" Value="Visible">
                <Setter TargetName="WrapBorder" Property="BorderThickness" Value="{Binding Path=ElementBorderThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" />
                <Setter TargetName="PART_BorderName" Property="BorderBrush" Value="{Binding Path=(tools_controls:SidePanel.DockingManager).(tools_controls:DockingManager.SidePanelBorderBrush), RelativeSource={RelativeSource TemplatedParent}}" />
            </Trigger>
            <Trigger Property="PanelSide" Value="Top">
                <Setter TargetName="PART_ScrollButtons" Property="Height" Value="25" />
            </Trigger>
            <Trigger Property="PanelSide" Value="Bottom">
                <Setter TargetName="PART_ScrollButtons" Property="Height" Value="25" />
            </Trigger>
            <Trigger Property="PanelSide" Value="Right">
                <Setter TargetName="PART_ScrollButtons" Property="Height" Value="25" />
                <Setter TargetName="PART_BorderSeparator" Property="Width" Value="{Binding Path=ActualWidth, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:SidePanel}} }" />
                <Setter TargetName="PART_BorderSeparator" Property="Height" Value="0" />
                <Setter TargetName="PART_BorderName" Property="Canvas.Left" Value="0" />
                <Setter TargetName="PART_ScrollButtons" Property="Canvas.Left" Value="3" />
                <Setter TargetName="PART_BorderName" Property="Canvas.Top" Value="{Binding ElementName=PART_ScrollButtons, Path=ActualWidth}" />
                <Setter TargetName="PART_ScrollButtons" Property="RenderTransform"  >
                    <Setter.Value>
                        <RotateTransform Angle="90" CenterX="10.5" CenterY="10.5"/>
                    </Setter.Value>
                </Setter>
            </Trigger>

            <Trigger Property="PanelSide" Value="Left">
                <Setter TargetName="PART_ScrollButtons" Property="Height" Value="25" />
                <Setter TargetName="PART_BorderSeparator" Property="Width" Value="{Binding Path=ActualWidth, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:SidePanel}} }" />
                <Setter TargetName="PART_BorderSeparator" Property="Height" Value="0" />
                <Setter TargetName="PART_BorderName" Property="Canvas.Left" Value="0" />
                <Setter TargetName="PART_ScrollButtons" Property="Canvas.Left" Value="3" />
                <Setter TargetName="PART_BorderName" Property="Canvas.Top" Value="{Binding ElementName=PART_ScrollButtons, Path=ActualWidth}" />
                <Setter TargetName="PART_ScrollButtons" Property="RenderTransform"  >
                    <Setter.Value>
                        <RotateTransform Angle="90" CenterX="10.5" CenterY="10.5"/>
                    </Setter.Value>
                </Setter>
            </Trigger>

            <MultiDataTrigger>
                <MultiDataTrigger.Conditions>
                    <Condition Binding="{Binding ElementName=PART_ScrollButtons, Path=Visibility}" Value="Collapsed"/>
                </MultiDataTrigger.Conditions>
                <MultiDataTrigger.Setters>
                    <Setter TargetName="PART_BorderName" Property="Canvas.Top" Value="0" />
                </MultiDataTrigger.Setters>
            </MultiDataTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SidePanelRowDefinitionStyle" TargetType="RowDefinition">
        <Setter Property="Height">
            <Setter.Value>
                <MultiBinding Converter="{StaticResource ThicknessToSize}" ConverterParameter="XCoordinate">
                    <Binding Path="SidePanelBorderThickness" RelativeSource="{RelativeSource AncestorType={x:Type tools_controls:DockingManager}}" />
                    <Binding Path="SidePanelSize" RelativeSource="{RelativeSource AncestorType={x:Type tools_controls:DockingManager}}" />
                </MultiBinding>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SidePanelColumnDefinitionStyle" TargetType="ColumnDefinition">
        <Setter Property="Width">
            <Setter.Value>
                <MultiBinding Converter="{StaticResource ThicknessToSize}" ConverterParameter="YCoordinate">
                    <Binding Path="SidePanelBorderThickness" RelativeSource="{RelativeSource AncestorType={x:Type tools_controls:DockingManager}}" />
                    <Binding Path="SidePanelSize" RelativeSource="{RelativeSource AncestorType={x:Type tools_controls:DockingManager}}" />
                </MultiBinding>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionMainHostStyle" TargetType="{x:Type tools_controls:MainHost}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FocusVisualStyle" Value="{Binding Path=FocusVisualStyle, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:MainHost}">
                    <Grid
                        x:Name="RootGrid"
                        Focusable="False"
                        ShowGridLines="False">
                        <Grid.RowDefinitions>
                            <RowDefinition x:Name="RowFirst" Style="{StaticResource SidePanelRowDefinitionStyle}" />
                            <RowDefinition />
                            <RowDefinition x:Name="RowLast" Style="{StaticResource SidePanelRowDefinitionStyle}" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition x:Name="ColumnFirst" Style="{StaticResource SidePanelColumnDefinitionStyle}" />
                            <ColumnDefinition />
                            <ColumnDefinition x:Name="ColumnLast" Style="{StaticResource SidePanelColumnDefinitionStyle}" />
                        </Grid.ColumnDefinitions>
                        <ContentPresenter
                            x:Name="PART_ExpandSite"
                            Grid.Row="1"
                            Grid.Column="1"
                            Content="{TemplateBinding ContentControl.Content}"
                            ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}"
                            Focusable="False" />
                        <StackPanel
                            x:Name="LeftTopSquare"
                            Grid.Row="0"
                            Grid.Column="0"
                            Background="{Binding Path=SidePanelBackground, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" />
                        <tools_controls:SidePanel
                            x:Name="PART_TopPanel"
                            Grid.Row="0"
                            Grid.Column="1"
                            ContentRenderTransformY="1"
                            PanelSide="Top"
                            TabStripPlacement="Top">
                            <tools_controls:SidePanel.Triggers>
                                <EventTrigger RoutedEvent="tools_controls:SidePanel.ShowEvent">
                                    <EventTrigger.Actions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation
                                                    x:Name="PART_TopShowAnimation"
                                                    BeginTime="{StaticResource ShadowTimeSpan}"
                                                    Storyboard.TargetProperty="ContentRenderTransformY"
                                                    To="0"
                                                    Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger.Actions>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="tools_controls:SidePanel.HideEvent">
                                    <EventTrigger.Actions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation
                                                    x:Name="PART_TopHideAnimation"
                                                    Storyboard.TargetProperty="ContentRenderTransformY"
                                                    To="1"
                                                    Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger.Actions>
                                </EventTrigger>
                            </tools_controls:SidePanel.Triggers>
                        </tools_controls:SidePanel>
                        <StackPanel
                            x:Name="RightTopSquare"
                            Grid.Row="0"
                            Grid.Column="2"
                            Background="{Binding Path=SidePanelBackground, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" />
                        <tools_controls:SidePanel
                            x:Name="PART_LeftPanel"
                            Grid.Row="1"
                            Grid.Column="0"
                            ContentRenderTransformX="1"
                            PanelSide="Left"
                            TabStripPlacement="Left">
                            <tools_controls:SidePanel.Triggers>
                                <EventTrigger RoutedEvent="tools_controls:SidePanel.ShowEvent">
                                    <EventTrigger.Actions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation
                                                    x:Name="PART_LeftShowAnimation"
                                                    BeginTime="{StaticResource ShadowTimeSpan}"
                                                    Storyboard.TargetProperty="ContentRenderTransformX"
                                                    To="0"
                                                    Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger.Actions>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="tools_controls:SidePanel.HideEvent">
                                    <EventTrigger.Actions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation
                                                    x:Name="PART_LeftHideAnimation"
                                                    Storyboard.TargetProperty="ContentRenderTransformX"
                                                    To="1"
                                                    Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger.Actions>
                                </EventTrigger>
                            </tools_controls:SidePanel.Triggers>
                        </tools_controls:SidePanel>
                        <tools_controls:SidePanel
                            x:Name="PART_RightPanel"
                            Grid.Row="1"
                            Grid.Column="2"
                            ContentRenderTransformX="1"
                            PanelSide="Right"
                            TabStripPlacement="Right">
                            <tools_controls:SidePanel.Triggers>
                                <EventTrigger RoutedEvent="tools_controls:SidePanel.ShowEvent">
                                    <EventTrigger.Actions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation
                                                    x:Name="PART_RightShowAnimation"
                                                    BeginTime="{StaticResource ShadowTimeSpan}"
                                                    Storyboard.TargetProperty="ContentRenderTransformX"
                                                    To="0"
                                                    Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger.Actions>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="tools_controls:SidePanel.HideEvent">
                                    <EventTrigger.Actions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation
                                                    x:Name="PART_RightHideAnimation"
                                                    Storyboard.TargetProperty="ContentRenderTransformX"
                                                    To="1"
                                                    Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger.Actions>
                                </EventTrigger>
                            </tools_controls:SidePanel.Triggers>
                        </tools_controls:SidePanel>
                        <StackPanel
                            x:Name="LeftBottomSquare"
                            Grid.Row="2"
                            Grid.Column="0"
                            Background="{Binding Path=SidePanelBackground, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" />
                        <tools_controls:SidePanel
                            x:Name="PART_BottomPanel"
                            Grid.Row="2"
                            Grid.Column="1"
                            ContentRenderTransformY="1"
                            PanelSide="Bottom"
                            TabStripPlacement="Bottom">
                            <tools_controls:SidePanel.Triggers>
                                <EventTrigger RoutedEvent="tools_controls:SidePanel.ShowEvent">
                                    <EventTrigger.Actions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation
                                                    x:Name="PART_BottomShowAnimation"
                                                    BeginTime="{StaticResource ShadowTimeSpan}"
                                                    Storyboard.TargetProperty="ContentRenderTransformY"
                                                    To="0"
                                                    Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger.Actions>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="tools_controls:SidePanel.HideEvent">
                                    <EventTrigger.Actions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation
                                                    x:Name="PART_BottomHideAnimation"
                                                    Storyboard.TargetProperty="ContentRenderTransformY"
                                                    To="1"
                                                    Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger.Actions>
                                </EventTrigger>
                            </tools_controls:SidePanel.Triggers>
                        </tools_controls:SidePanel>
                        <StackPanel
                            x:Name="RigthBottomSquare"
                            Grid.Row="2"
                            Grid.Column="2"
                            Background="{Binding Path=SidePanelBackground, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Path=EnableScrollableSidePanel, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="True">
                            <Setter TargetName="PART_BottomPanel" Property="Template" Value="{StaticResource SyncfusionScrollableSidePanelControlTemplate}" />
                            <Setter TargetName="PART_RightPanel" Property="Template" Value="{StaticResource SyncfusionScrollableSidePanelControlTemplate}" />
                            <Setter TargetName="PART_TopPanel" Property="Template" Value="{StaticResource SyncfusionScrollableSidePanelControlTemplate}" />
                            <Setter TargetName="PART_LeftPanel" Property="Template" Value="{StaticResource SyncfusionScrollableSidePanelControlTemplate}" />
                        </DataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=EnableScrollableSidePanel, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="True" />
                                <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.UsePopupAutoHidePreview), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_BottomPanel" Property="Template" Value="{StaticResource SyncfusionPopupScrollableSidePanelControlTemplate}" />
                            <Setter TargetName="PART_RightPanel" Property="Template" Value="{StaticResource SyncfusionPopupScrollableSidePanelControlTemplate}" />
                            <Setter TargetName="PART_TopPanel" Property="Template" Value="{StaticResource SyncfusionPopupScrollableSidePanelControlTemplate}" />
                            <Setter TargetName="PART_LeftPanel" Property="Template" Value="{StaticResource SyncfusionPopupScrollableSidePanelControlTemplate}" />
                        </MultiDataTrigger>
                        <Trigger SourceName="PART_TopPanel" Property="Visibility" Value="Collapsed">
                            <Setter TargetName="LeftTopSquare" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="RightTopSquare" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger SourceName="PART_LeftPanel" Property="Visibility" Value="Collapsed">
                            <Setter TargetName="LeftTopSquare" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="LeftBottomSquare" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger SourceName="PART_BottomPanel" Property="Visibility" Value="Collapsed">
                            <Setter TargetName="LeftBottomSquare" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="RigthBottomSquare" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger SourceName="PART_RightPanel" Property="Visibility" Value="Collapsed">
                            <Setter TargetName="RightTopSquare" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="RigthBottomSquare" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition SourceName="LeftTopSquare" Property="Visibility" Value="Collapsed" />
                                <Condition SourceName="RightTopSquare" Property="Visibility" Value="Collapsed" />
                                <Condition SourceName="PART_TopPanel" Property="Visibility" Value="Collapsed" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="RowFirst" Property="Height" Value="0" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition SourceName="LeftTopSquare" Property="Visibility" Value="Collapsed" />
                                <Condition SourceName="LeftBottomSquare" Property="Visibility" Value="Collapsed" />
                                <Condition SourceName="PART_LeftPanel" Property="Visibility" Value="Collapsed" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="ColumnFirst" Property="Width" Value="0" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition SourceName="RigthBottomSquare" Property="Visibility" Value="Collapsed" />
                                <Condition SourceName="LeftBottomSquare" Property="Visibility" Value="Collapsed" />
                                <Condition SourceName="PART_BottomPanel" Property="Visibility" Value="Collapsed" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="RowLast" Property="Height" Value="0" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition SourceName="RightTopSquare" Property="Visibility" Value="Collapsed" />
                                <Condition SourceName="RigthBottomSquare" Property="Visibility" Value="Collapsed" />
                                <Condition SourceName="PART_RightPanel" Property="Visibility" Value="Collapsed" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="ColumnLast" Property="Width" Value="0" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Path=AutoHideAnimationMode, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="Scale">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:MainHost}">
                            <Grid x:Name="RootGrid" ShowGridLines="False">
                                <Grid.RowDefinitions>
                                    <RowDefinition x:Name="RowFirst" Style="{StaticResource SidePanelRowDefinitionStyle}" />
                                    <RowDefinition />
                                    <RowDefinition x:Name="RowLast" Style="{StaticResource SidePanelRowDefinitionStyle}" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition x:Name="ColumnFirst" Style="{StaticResource SidePanelColumnDefinitionStyle}" />
                                    <ColumnDefinition />
                                    <ColumnDefinition x:Name="ColumnLast" Style="{StaticResource SidePanelColumnDefinitionStyle}" />
                                </Grid.ColumnDefinitions>
                                <ContentPresenter
                                    x:Name="PART_ExpandSite"
                                    Grid.Row="1"
                                    Grid.Column="1"
                                    Content="{TemplateBinding ContentControl.Content}"
                                    ContentTemplate="{TemplateBinding ContentTemplate}"
                                    Focusable="False" />
                                <StackPanel
                                    x:Name="LeftTopSquare"
                                    Grid.Row="0"
                                    Grid.Column="0" />
                                <tools_controls:SidePanel
                                    x:Name="PART_TopPanel"
                                    Grid.Row="0"
                                    Grid.Column="1"
                                    ContentScaleY="0"
                                    PanelSide="Top"
                                    TabStripPlacement="Top">
                                    <tools_controls:SidePanel.Triggers>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.ShowEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_TopShowAnimation"
                                                            Storyboard.TargetProperty="ContentScaleY"
                                                            To="1"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.HideEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_TopHideAnimation"
                                                            Storyboard.TargetProperty="ContentScaleY"
                                                            To="0"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                    </tools_controls:SidePanel.Triggers>
                                </tools_controls:SidePanel>
                                <StackPanel
                                    x:Name="RightTopSquare"
                                    Grid.Row="0"
                                    Grid.Column="2" />
                                <tools_controls:SidePanel
                                    x:Name="PART_LeftPanel"
                                    Grid.Row="1"
                                    Grid.Column="0"
                                    ContentScaleX="0"
                                    PanelSide="Left"
                                    TabStripPlacement="Left">
                                    <tools_controls:SidePanel.Triggers>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.ShowEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_LeftShowAnimation"
                                                            Storyboard.TargetProperty="ContentScaleX"
                                                            To="1"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.HideEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_LeftHideAnimation"
                                                            Storyboard.TargetProperty="ContentScaleX"
                                                            To="0"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                    </tools_controls:SidePanel.Triggers>
                                </tools_controls:SidePanel>
                                <tools_controls:SidePanel
                                    x:Name="PART_RightPanel"
                                    Grid.Row="1"
                                    Grid.Column="2"
                                    ContentScaleX="0"
                                    PanelSide="Right"
                                    TabStripPlacement="Right">
                                    <tools_controls:SidePanel.Triggers>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.ShowEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_RightShowAnimation"
                                                            Storyboard.TargetProperty="ContentScaleX"
                                                            To="1"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.HideEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_RightHideAnimation"
                                                            Storyboard.TargetProperty="ContentScaleX"
                                                            To="0"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                    </tools_controls:SidePanel.Triggers>
                                </tools_controls:SidePanel>
                                <StackPanel
                                    x:Name="LeftBottomSquare"
                                    Grid.Row="2"
                                    Grid.Column="0" />
                                <tools_controls:SidePanel
                                    x:Name="PART_BottomPanel"
                                    Grid.Row="2"
                                    Grid.Column="1"
                                    ContentScaleY="0"
                                    PanelSide="Bottom"
                                    TabStripPlacement="Bottom">
                                    <tools_controls:SidePanel.Triggers>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.ShowEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_BottomShowAnimation"
                                                            Storyboard.TargetProperty="ContentScaleY"
                                                            To="1"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.HideEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_BottomHideAnimation"
                                                            Storyboard.TargetProperty="ContentScaleY"
                                                            To="0"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                    </tools_controls:SidePanel.Triggers>
                                </tools_controls:SidePanel>
                                <StackPanel
                                    x:Name="RigthBottomSquare"
                                    Grid.Row="2"
                                    Grid.Column="2" />
                            </Grid>
                            <ControlTemplate.Triggers>
                                <DataTrigger Binding="{Binding Path=EnableScrollableSidePanel, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="True">
                                    <Setter TargetName="PART_BottomPanel" Property="Template" Value="{StaticResource SyncfusionScrollableSidePanelControlTemplate}" />
                                    <Setter TargetName="PART_RightPanel" Property="Template" Value="{StaticResource SyncfusionScrollableSidePanelControlTemplate}" />
                                    <Setter TargetName="PART_TopPanel" Property="Template" Value="{StaticResource SyncfusionScrollableSidePanelControlTemplate}" />
                                    <Setter TargetName="PART_LeftPanel" Property="Template" Value="{StaticResource SyncfusionScrollableSidePanelControlTemplate}" />
                                </DataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=EnableScrollableSidePanel, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="True" />
                                        <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.UsePopupAutoHidePreview), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="True" />
                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="PART_BottomPanel" Property="Template" Value="{StaticResource SyncfusionPopupScrollableSidePanelControlTemplate}" />
                                    <Setter TargetName="PART_RightPanel" Property="Template" Value="{StaticResource SyncfusionPopupScrollableSidePanelControlTemplate}" />
                                    <Setter TargetName="PART_TopPanel" Property="Template" Value="{StaticResource SyncfusionPopupScrollableSidePanelControlTemplate}" />
                                    <Setter TargetName="PART_LeftPanel" Property="Template" Value="{StaticResource SyncfusionPopupScrollableSidePanelControlTemplate}" />
                                </MultiDataTrigger>
                                <Trigger SourceName="PART_TopPanel" Property="Visibility" Value="Collapsed">
                                    <Setter TargetName="LeftTopSquare" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="RightTopSquare" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <Trigger SourceName="PART_LeftPanel" Property="Visibility" Value="Collapsed">
                                    <Setter TargetName="LeftTopSquare" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="LeftBottomSquare" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <Trigger SourceName="PART_BottomPanel" Property="Visibility" Value="Collapsed">
                                    <Setter TargetName="LeftBottomSquare" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="RigthBottomSquare" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <Trigger SourceName="PART_RightPanel" Property="Visibility" Value="Collapsed">
                                    <Setter TargetName="RightTopSquare" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="RigthBottomSquare" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition SourceName="LeftTopSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="RightTopSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="PART_TopPanel" Property="Visibility" Value="Collapsed" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="RowFirst" Property="Height" Value="0" />
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition SourceName="LeftTopSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="LeftBottomSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="PART_LeftPanel" Property="Visibility" Value="Collapsed" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="ColumnFirst" Property="Width" Value="0" />
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition SourceName="RigthBottomSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="LeftBottomSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="PART_BottomPanel" Property="Visibility" Value="Collapsed" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="RowLast" Property="Height" Value="0" />
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition SourceName="RightTopSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="RigthBottomSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="PART_RightPanel" Property="Visibility" Value="Collapsed" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="ColumnLast" Property="Width" Value="0" />
                                </MultiTrigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=AutoHideAnimationMode, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="Fade">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:MainHost}">
                            <Grid x:Name="RootGrid" ShowGridLines="False">
                                <Grid.RowDefinitions>
                                    <RowDefinition x:Name="RowFirst" Style="{StaticResource SidePanelRowDefinitionStyle}" />
                                    <RowDefinition />
                                    <RowDefinition x:Name="RowLast" Style="{StaticResource SidePanelRowDefinitionStyle}" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition x:Name="ColumnFirst" Style="{StaticResource SidePanelColumnDefinitionStyle}" />
                                    <ColumnDefinition />
                                    <ColumnDefinition x:Name="ColumnLast" Style="{StaticResource SidePanelColumnDefinitionStyle}" />
                                </Grid.ColumnDefinitions>
                                <ContentPresenter
                                    x:Name="PART_ExpandSite"
                                    Grid.Row="1"
                                    Grid.Column="1"
                                    Content="{TemplateBinding ContentControl.Content}"
                                    ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}"
                                    Focusable="False" />
                                <StackPanel
                                    x:Name="LeftTopSquare"
                                    Grid.Row="0"
                                    Grid.Column="0" />
                                <tools_controls:SidePanel
                                    x:Name="PART_TopPanel"
                                    Grid.Row="0"
                                    Grid.Column="1"
                                    ContentOpacity="0"
                                    PanelSide="Top"
                                    TabStripPlacement="Top">
                                    <tools_controls:SidePanel.Triggers>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.ShowEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_TopShowAnimation"
                                                            Storyboard.TargetProperty="ContentOpacity"
                                                            To="1"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.HideEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_TopHideAnimation"
                                                            Storyboard.TargetProperty="ContentOpacity"
                                                            To="0"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                    </tools_controls:SidePanel.Triggers>
                                </tools_controls:SidePanel>
                                <StackPanel
                                    x:Name="RightTopSquare"
                                    Grid.Row="0"
                                    Grid.Column="2" />
                                <tools_controls:SidePanel
                                    x:Name="PART_LeftPanel"
                                    Grid.Row="1"
                                    Grid.Column="0"
                                    ContentOpacity="0"
                                    PanelSide="Left"
                                    TabStripPlacement="Left">
                                    <tools_controls:SidePanel.Triggers>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.ShowEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_LeftShowAnimation"
                                                            Storyboard.TargetProperty="ContentOpacity"
                                                            To="1"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.HideEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_LeftHideAnimation"
                                                            Storyboard.TargetProperty="ContentOpacity"
                                                            To="0"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                    </tools_controls:SidePanel.Triggers>
                                </tools_controls:SidePanel>
                                <tools_controls:SidePanel
                                    x:Name="PART_RightPanel"
                                    Grid.Row="1"
                                    Grid.Column="2"
                                    ContentOpacity="0"
                                    PanelSide="Right"
                                    TabStripPlacement="Right">
                                    <tools_controls:SidePanel.Triggers>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.ShowEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_RightShowAnimation"
                                                            Storyboard.TargetProperty="ContentOpacity"
                                                            To="1"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.HideEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_RightHideAnimation"
                                                            Storyboard.TargetProperty="ContentOpacity"
                                                            To="0"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                    </tools_controls:SidePanel.Triggers>
                                </tools_controls:SidePanel>
                                <StackPanel
                                    x:Name="LeftBottomSquare"
                                    Grid.Row="2"
                                    Grid.Column="0" />
                                <tools_controls:SidePanel
                                    x:Name="PART_BottomPanel"
                                    Grid.Row="2"
                                    Grid.Column="1"
                                    ContentOpacity="0"
                                    PanelSide="Bottom"
                                    TabStripPlacement="Bottom">
                                    <tools_controls:SidePanel.Triggers>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.ShowEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_BottomShowAnimation"
                                                            Storyboard.TargetProperty="ContentOpacity"
                                                            To="1"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="tools_controls:SidePanel.HideEvent">
                                            <EventTrigger.Actions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation
                                                            x:Name="PART_BottomHideAnimation"
                                                            Storyboard.TargetProperty="ContentOpacity"
                                                            To="0"
                                                            Duration="{Binding Path=(TabControl.SelectedItem).(tools_controls:DockingManager.AnimationDelay), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:SidePanel}}}" />
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger.Actions>
                                        </EventTrigger>
                                    </tools_controls:SidePanel.Triggers>
                                </tools_controls:SidePanel>
                                <StackPanel
                                    x:Name="RigthBottomSquare"
                                    Grid.Row="2"
                                    Grid.Column="2" />
                            </Grid>
                            <ControlTemplate.Triggers>
                                <DataTrigger Binding="{Binding Path=EnableScrollableSidePanel, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="True">
                                    <Setter TargetName="PART_BottomPanel" Property="Template" Value="{StaticResource SyncfusionScrollableSidePanelControlTemplate}" />
                                    <Setter TargetName="PART_RightPanel" Property="Template" Value="{StaticResource SyncfusionScrollableSidePanelControlTemplate}" />
                                    <Setter TargetName="PART_TopPanel" Property="Template" Value="{StaticResource SyncfusionScrollableSidePanelControlTemplate}" />
                                    <Setter TargetName="PART_LeftPanel" Property="Template" Value="{StaticResource SyncfusionScrollableSidePanelControlTemplate}" />
                                </DataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding Path=EnableScrollableSidePanel, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="True" />
                                        <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.UsePopupAutoHidePreview), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:DockingManager}}}" Value="True" />
                                    </MultiDataTrigger.Conditions>
                                    <Setter TargetName="PART_BottomPanel" Property="Template" Value="{StaticResource SyncfusionPopupScrollableSidePanelControlTemplate}" />
                                    <Setter TargetName="PART_RightPanel" Property="Template" Value="{StaticResource SyncfusionPopupScrollableSidePanelControlTemplate}" />
                                    <Setter TargetName="PART_TopPanel" Property="Template" Value="{StaticResource SyncfusionPopupScrollableSidePanelControlTemplate}" />
                                    <Setter TargetName="PART_LeftPanel" Property="Template" Value="{StaticResource SyncfusionPopupScrollableSidePanelControlTemplate}" />
                                </MultiDataTrigger>
                                <Trigger SourceName="PART_TopPanel" Property="Visibility" Value="Collapsed">
                                    <Setter TargetName="LeftTopSquare" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="RightTopSquare" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <Trigger SourceName="PART_LeftPanel" Property="Visibility" Value="Collapsed">
                                    <Setter TargetName="LeftTopSquare" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="LeftBottomSquare" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <Trigger SourceName="PART_BottomPanel" Property="Visibility" Value="Collapsed">
                                    <Setter TargetName="LeftBottomSquare" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="RigthBottomSquare" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <Trigger SourceName="PART_RightPanel" Property="Visibility" Value="Collapsed">
                                    <Setter TargetName="RightTopSquare" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="RigthBottomSquare" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition SourceName="LeftTopSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="RightTopSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="PART_TopPanel" Property="Visibility" Value="Collapsed" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="RowFirst" Property="Height" Value="0" />
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition SourceName="LeftTopSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="LeftBottomSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="PART_LeftPanel" Property="Visibility" Value="Collapsed" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="ColumnFirst" Property="Width" Value="0" />
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition SourceName="RigthBottomSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="LeftBottomSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="PART_BottomPanel" Property="Visibility" Value="Collapsed" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="RowLast" Property="Height" Value="0" />
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition SourceName="RightTopSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="RigthBottomSquare" Property="Visibility" Value="Collapsed" />
                                        <Condition SourceName="PART_RightPanel" Property="Visibility" Value="Collapsed" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="ColumnLast" Property="Width" Value="0" />
                                </MultiTrigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="Header" TargetType="{x:Type ContentControl}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ContentControl}">
                    <Border x:Name="FloatWindowHeaderBorder" 
                            BorderBrush="{StaticResource BorderAlt}"
                            Background="{TemplateBinding Background}"
                            CornerRadius="4, 4, 0, 0">
                        <Border.ContextMenu>
                            <tools_controls:CustomContextMenu x:Name="PART_ContextMenu" Focusable="false" />
                        </Border.ContextMenu>
                        <DockPanel
                            x:Name="FloatWindowHeader"
                            LastChildFill="True">
                            <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                                <ToggleButton
                                    x:Name="MinimizeButton"
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,4,0"
                                    Padding="0"
                                    VerticalAlignment="Center"
                                    Command="tools_controls:NativeFloatWindow.MinimizeCommand"
                                    Focusable="True"
                                    Style="{StaticResource CaptionButtonStyle}"
                                    Tag="Float"
                                    Template="{StaticResource NativeCaptionButtonsTemplate}">
                                    
                                    <ToggleButton.ToolTip>
                                        <ToolTip x:Name="PART_MinimizeButtonTooltip">
                                            <ToolTip.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                            </ToolTip.Resources>
                                            <TextBlock x:Name="PART_MinimizeButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=Minimize}" />
                                        </ToolTip>
                                    </ToggleButton.ToolTip>
                                    <ToggleButton.Content>
                                        <Path
                                            x:Name="MinimizeButtonPath1"
                                            Width="10"
                                            Height="1"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Stroke="{Binding Foreground, ElementName=MinimizeButton}"
                                            StrokeThickness="1"
                                            SnapsToDevicePixels="False"
                                            Stretch="Fill"
                                            Data="M0 1H10"/>
                                    </ToggleButton.Content>
                                </ToggleButton>
                                <ToggleButton
                                    x:Name="Restorebutton"
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,4,0"
                                    Padding="0"
                                    VerticalAlignment="Center"
                                    Command="tools_controls:NativeFloatWindow.RestoreCommand"
                                    Focusable="True"
                                    Style="{StaticResource CaptionButtonStyle}"
                                    Tag="Float"
                                    Template="{StaticResource NativeCaptionButtonsTemplate}">
                                    <ToggleButton.Content>
                                        <Path
                                            x:Name="RestoreButtonPath1"
                                            Width="10"
                                            Height="10"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Stroke="{Binding Foreground, ElementName=Restorebutton}"
                                            StrokeThickness="1"
                                            SnapsToDevicePixels="False"
                                            Stretch="Fill"
                                            Data="M9 9H11V1H3V3M1 11V3H9V11H1Z"/>
                                    </ToggleButton.Content>
                                    <ToggleButton.ToolTip>
                                        <ToolTip x:Name="PART_RestoreButtonTooltip">
                                            <ToolTip.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                            </ToolTip.Resources>
                                            <TextBlock x:Name="PART_RestoreButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=Restore}" />
                                        </ToolTip>
                                    </ToggleButton.ToolTip>
                                </ToggleButton>
                                <ToggleButton
                                    x:Name="MaximizeButton"
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,4,0"
                                    Padding="0"
                                    VerticalAlignment="Center"
                                    Command="tools_controls:NativeFloatWindow.MaximizeCommand"
                                    Focusable="True"
                                    Style="{StaticResource CaptionButtonStyle}"
                                    Tag="Float"
                                    Template="{StaticResource NativeCaptionButtonsTemplate}">
                                    <ToggleButton.Content>
                                        <Path
                                            x:Name="MaximizeButtonPath1"
                                            Width="10"
                                            Height="10"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Stroke="{Binding Foreground, ElementName=MaximizeButton}"
                                            StrokeThickness="1"
                                            SnapsToDevicePixels="False"
                                            Stretch="Fill"
                                            Data="M1 1V11H11V1H1Z"/>
                                    </ToggleButton.Content>
                                    <ToggleButton.ToolTip>
                                        <ToolTip x:Name="PART_MaximizeButtonTooltip">
                                            <ToolTip.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                            </ToolTip.Resources>
                                            <TextBlock x:Name="PART_MaximizeButtonTooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=Maximize}" />
                                        </ToolTip>
                                    </ToggleButton.ToolTip>
                                </ToggleButton>
                                <ToggleButton
                                    x:Name="button"
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,4,0"
                                    Padding="0"
                                    VerticalAlignment="Center"
                                    Command="tools_controls:NativeFloatWindow.CloseCommand"
                                    Focusable="True"
                                    Style="{StaticResource CaptionButtonStyle}"
                                    Tag="Float"
                                    Template="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.CloseButtonTemplate), RelativeSource={RelativeSource AncestorType=tools_controls:NativeFloatWindow}}">
                                    <ToggleButton.ToolTip>
                                        <ToolTip x:Name="CloseButtonTooltip">
                                            <ToolTip.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                            </ToolTip.Resources>
                                            <TextBlock x:Name="tooltipText" Text="{tools_controls:ToolsLocalizationResourceExtension ResourceName=CloseButtonTooltipText}" />
                                        </ToolTip>
                                    </ToggleButton.ToolTip>
                                    <ToggleButton.Content>
                                        <Path
                                            x:Name="CloseButtonPath1"
                                            Width="8"
                                            Height="8"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Stroke="{Binding Foreground, ElementName=button}"
                                            StrokeThickness="1"
                                            SnapsToDevicePixels="True"
                                            Stretch="Fill" 
                                            Data="M1 11L6 6M6 6L11 1M6 6L11 11M6 6L1 1"/>
                                    </ToggleButton.Content>
                                </ToggleButton>
                            </StackPanel>
                            <Border
                                x:Name="Icon"
                                Width="16"
                                Margin="6,3,2,3"
                                Background="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.Icon), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}"
                                DockPanel.Dock="Left" />
                            <ContentPresenter
                                x:Name="FloatWindowHeaderContent"
                                Margin="4,0,0,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Content="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.Header), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}"
                                ContentTemplate="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.HeaderTemplate), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}"
                                DockPanel.Dock="Left"
                                IsHitTestVisible="True"
                                TextBlock.Foreground="{StaticResource ContentForeground}">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </DockPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger SourceName="Icon" Property="Background" Value="{x:Null}">
                            <Setter TargetName="Icon" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <DataTrigger Binding="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.IsTouchEnabled), RelativeSource={RelativeSource AncestorType=tools_controls:NativeFloatWindow}}" Value="True">
                            <Setter Property="FontSize" Value="18" />
                        </DataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:NativeFloatWindow.MaximizeButtonEnabled), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True" />
                                <Condition Binding="{Binding Path=(tools_controls:NativeFloatWindow.WindowState), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="Maximized" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="MaximizeButton" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="Restorebutton" Property="Visibility" Value="Visible" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:NativeFloatWindow.MaximizeButtonEnabled), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True" />
                                <Condition Binding="{Binding Path=(tools_controls:NativeFloatWindow.WindowState), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="Normal" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="MaximizeButton" Property="Visibility" Value="Visible" />
                            <Setter TargetName="Restorebutton" Property="Visibility" Value="Collapsed" />
                        </MultiDataTrigger>
                        <DataTrigger Binding="{Binding Path=(tools_controls:NativeFloatWindow.MaximizeButtonEnabled), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="False">
                            <Setter TargetName="MaximizeButton" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="Restorebutton" Property="Visibility" Value="Collapsed" />
                        </DataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:NativeFloatWindow.MinimizeButtonVisibility), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True" />
                                <Condition Binding="{Binding Path=(tools_controls:NativeFloatWindow.WindowState), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="Minimized" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="MinimizeButton" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="Restorebutton" Property="Visibility" Value="Visible" />
                        </MultiDataTrigger>
                        <DataTrigger Binding="{Binding Path=(tools_controls:NativeFloatWindow.MinimizeButtonVisibility), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="False">
                            <Setter TargetName="MinimizeButton" Property="Visibility" Value="Collapsed" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.CanDocument), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="False">
                            <Setter TargetName="PART_ContextMenu" Property="IsEnabledTabbedMenuItem" Value="False" />
                        </DataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.HostedElement).(tools_controls:DockingManager.CanClose), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="False" />
                                <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
                            <Setter TargetName="button" Property="IsEnabled" Value="False" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.CloseTabs), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="CloseActive" />
                                <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockingManager.CanClose), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="False" />
                                <Condition Binding="{Binding Path=(tools_controls:DockingManager.InternalDataContext).(tools_controls:DockedElementTabbedHost.ShowTabs), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_ContextMenu" Property="IsEnabledHiddenMenuItem" Value="False" />
                            <Setter TargetName="button" Property="IsEnabled" Value="False" />
                        </MultiDataTrigger>
                        <DataTrigger Binding="{Binding Path=(PrimaryElement).(tools_controls:DockingManager.NoHeader), RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True">
                            <Setter TargetName="PART_ContextMenu" Property="Visibility" Value="Visible" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=IsMultiHostsContainer, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True">
                            <Setter TargetName="FloatWindowHeaderContent" Property="Visibility" Value="Hidden" />
                        </DataTrigger>
                        <Trigger SourceName="FloatWindowHeader" Property="Visibility" Value="Visible">
                            <Setter Property="Background" Value="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.FloatWindowHeaderBackground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" />
                            <Setter TargetName="FloatWindowHeaderContent" Property="TextBlock.Foreground" Value="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.FloatWindowHeaderForeground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" />
                        </Trigger>
                        <DataTrigger Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True">
                            <Setter Property="Background" Value="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.FloatWindowSelectedHeaderBackground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" />
                            <Setter TargetName="FloatWindowHeaderContent" Property="TextBlock.Foreground" Value="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.FloatWindowSelectedHeaderForeground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" />
                        </DataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.IsEnableHotTracking), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True" />
                                <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" Value="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.FloatWindowMouseOverHeaderBackground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" />
                            <Setter TargetName="FloatWindowHeaderContent" Property="TextBlock.Foreground" Value="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.FloatWindowMouseOverHeaderForeground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.IsEnableHotTracking), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True" />
                                <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True" />
                                <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" Value="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.FloatWindowMouseOverHeaderBackground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" />
                            <Setter TargetName="FloatWindowHeaderContent" Property="TextBlock.Foreground" Value="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.FloatWindowMouseOverHeaderForeground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" />
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="True" />
                                <Condition Binding="{Binding Path=IsKeyboardFocusWithin, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:NativeFloatWindow}}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" Value="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.FloatWindowMouseOverHeaderBackground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" />
                            <Setter TargetName="FloatWindowHeaderContent" Property="TextBlock.Foreground" Value="{Binding Path=(tools_controls:NativeFloatWindow.DockingManager).(tools_controls:DockingManager.FloatWindowMouseOverHeaderForeground), RelativeSource={RelativeSource AncestorType={x:Type tools_controls:NativeFloatWindow}}}" />
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionNativeFloatWindowStyle" TargetType="{x:Type tools_controls:NativeFloatWindow}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="WindowStyle" Value="None" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="1" />
        <!--<Setter Property="AllowsTransparency" Value="True" />-->
        <Setter Property="WindowChrome.WindowChrome">
            <Setter.Value>
                <WindowChrome CornerRadius="4" />
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ContentControl}">
                    <AdornerDecorator x:Name="NativeFloatWindowAdorner">
                        <Border
                            x:Name="FloatWindowOutBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            Focusable="False">
                            <!--<Border.Effect>
                                <DropShadowEffect BlurRadius="100" Opacity="0.01" RenderingBias="Performance" />
                            </Border.Effect>-->
                            <Grid Focusable="False">
                                <Grid.RowDefinitions>
                                    <RowDefinition x:Name="TopRow" Style="{StaticResource RowDefinitionStyle}" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Border x:Name="Part_BorderMenu">
                                    <Border.ContextMenu>
                                        <tools_controls:CustomContextMenu
                                        x:Name="Part_CustomContextMenu"
                                        Focusable="True"
                                        Style="{StaticResource SyncfusionCustomContextMenuStyle}" />
                                    </Border.ContextMenu>
                                </Border>
                                <ContentControl
                                    x:Name="BorderHeader"
                                    Grid.Row="0"
                                    Content="{Binding Path=Title, RelativeSource={RelativeSource TemplatedParent}}"
                                    Focusable="False"
                                    Style="{StaticResource Header}" />
                                <Border
                                    Grid.Row="1">
                                    <ContentPresenter
                                        x:Name="ContentPresenter"
                                        Content="{TemplateBinding ContentControl.Content}"
                                        ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}">
                                        <ContentPresenter.Resources>
                                            <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                        </ContentPresenter.Resources>
                                    </ContentPresenter>
                                </Border>
                            </Grid>
                        </Border>
                    </AdornerDecorator>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionNativeFloatWindowStyle}" TargetType="{x:Type tools_controls:NativeFloatWindow}" />

    <Style
        x:Key="SyncfusionDockingDocumentContainerStyle"
        BasedOn="{StaticResource SyncfusionDocumentContainerStyle}"
        TargetType="{x:Type tools_controls:DocumentContainer}">
        <Setter Property="BorderThickness" Value="0,0,0.1,0.1" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="tools_controls:DocumentContainer.DocumentTabControlStyle" Value="{StaticResource SyncfusionDocumentTabControlExtStyle}" />
        <Setter Property="tools_controls:DocumentContainer.DocumentTabItemStyle" Value="{StaticResource SyncfusionDocumentTabItemExtStyle}" />
    </Style>

    <Style x:Key="SyncfusionDockingManagerStyle" TargetType="{x:Type tools_controls:DockingManager}">
        <Setter Property="tools_controls:DockingManager.FocusVisualStyle" Value="{x:Null}" />
       <!--<Setter Property="ContainerStyle" Value="{StaticResource SyncfusionDockingDocumentContainerStyle}" />-->
        <Setter Property="ClipToBounds" Value="True" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="MainHostStyle" Value="{StaticResource SyncfusionMainHostStyle}" />
        <Setter Property="DockWindowContextMenuItemStyle" Value="{StaticResource SyncfusionCustomMenuItemStyle}" />
        <Setter Property="NativeWindowStyle" Value="{StaticResource SyncfusionNativeFloatWindowStyle}" />
        <Setter Property="tools_controls:DockingManager.DocumentTabControlStyle" Value="{StaticResource SyncfusionDocumentTabControlExtStyle}" />
        <Setter Property="tools_controls:DockingManager.DocumentTabItemStyle" Value="{StaticResource SyncfusionDocumentTabItemExtStyle}" />
        <Setter Property="TabControlStyle" Value="{StaticResource DockTabControlStyle}" />
        <Setter Property="TabItemStyle" Value="{StaticResource TabItemStyle}" />
        <Setter Property="CenterDragProvider" Value="{StaticResource DockingDockPreviewCenterButtonVS2008Template}" />
        <Setter Property="LeftDragProvider" Value="{StaticResource DockingDockPreviewLeftButtonVS2008Template}" />
        <Setter Property="TopDragProvider" Value="{StaticResource DockingDockPreviewTopButtonVS2008Template}" />
        <Setter Property="RightDragProvider" Value="{StaticResource DockingDockPreviewRightButtonVS2008Template}" />
        <Setter Property="BottomDragProvider" Value="{StaticResource DockingDockPreviewBottomButtonVS2008Template}" />
        <Setter Property="CloseButtonTemplate" Value="{StaticResource CaptionButtonsTemplate}" />
        <Setter Property="MaximizeButtonTemplate" Value="{StaticResource CaptionButtonsTemplate}" />
        <Setter Property="MinimizeButtonTemplate" Value="{StaticResource CaptionButtonsTemplate}" />
        <Setter Property="RestoreButtonTemplate" Value="{StaticResource CaptionButtonsTemplate}" />
        <Setter Property="MenuButtonTemplate" Value="{StaticResource CaptionButtonsTemplate}" />
        <Setter Property="AwlButtonTemplate" Value="{StaticResource CaptionButtonsTemplate}" />
        <Setter Property="FloatWindowTemplate" Value="{StaticResource FloatWindowTemplate}" />
        <Setter Property="SideTabItemTemplate" Value="{StaticResource SideTabItemTemplate}" />
        <Setter Property="SidePanelTemplate" Value="{StaticResource SyncfusionSidePanelControlTemplate}" />
        <Setter Property="TabItemTemplate" Value="{StaticResource TabItemTemplate}" />
        <Setter Property="DockedElementTabbedHostStyle" Value="{StaticResource SyncfusionDockedElementTabbedHostStyle}" />
        <Setter Property="SidePanelStyle" Value="{StaticResource SyncfusionSidePanelStyle}" />
        <Setter Property="SideItemStyle" Value="{StaticResource SideTabItemStyle}" />
        <Setter Property="ScrollButtonsBarStyle" Value="{StaticResource SyncfusionScrollButtonsBarStyle}" />
        <Setter Property="HeaderBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="HeaderBorderThickness" Value="0" />
        <Setter Property="ElementBorderThickness" Value="1" />
        <Setter Property="SideItemsBorderBrush" Value="Transparent" />
        <Setter Property="SidePanelItemsBorderThickness" Value="1" />
        <Setter Property="SideItemsBackground" Value="Transparent" />
        <Setter Property="TabItemBackgroundSelected" Value="{StaticResource ContentBackground}" />
        <Setter Property="TabItemsBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="HeaderForeground" Value="{StaticResource ContentForeground}" />
        <Setter Property="HeaderBackground" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="HeaderMouseOverBackground" Value="{StaticResource ContentBackgroundAlt2}" />
        <Setter Property="SelectedHeaderBackground" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="HeaderForegroundSelected" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="SidePanelBackground" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="TabPanelBackground" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="TabItemsCornerRadius" Value="0" />
        <Setter Property="TabItemBorderThickness" Value="0" />
        <Setter Property="TabItemsBorderThicknessSelected" Value="{StaticResource Windows11Light.BorderThickness1110}" />
        <Setter Property="TabItemsBackground" Value="Transparent" />
        <Setter Property="tools_controls:DockingManager.SidePanelBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="tools_controls:DockingManager.TabPanelBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="tools_controls:DockingManager.HeaderBorderBrush" Value="{StaticResource BorderAlt}" />

        <Setter Property="tools_controls:DockingManager.FloatWindowHeaderBackground" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="tools_controls:DockingManager.FloatWindowSelectedHeaderBackground" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="tools_controls:DockingManager.FloatWindowMouseOverHeaderBackground" Value="{StaticResource ContentBackgroundAlt2}" />
        <Setter Property="tools_controls:DockingManager.FloatWindowHeaderForeground" Value="{StaticResource ContentForeground}" />
        <Setter Property="tools_controls:DockingManager.FloatWindowSelectedHeaderForeground" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="tools_controls:DockingManager.FloatWindowMouseOverHeaderForeground" Value="{StaticResource ContentForeground}" />
        <Setter Property="tools_controls:DockingManager.FloatWindowBorderBrush" Value="{StaticResource BorderAlt}" />

        <Setter Property="tools_controls:DockingManager.LeftFloatWindowBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="tools_controls:DockingManager.LeftFloatWindowSelectedBorderBrush" Value="{StaticResource Border}" />
        <Setter Property="tools_controls:DockingManager.LeftFloatMouseOverBorderBrush" Value="{StaticResource BorderAlt1}" />

        <Setter Property="tools_controls:DockingManager.LeftFloatWindowTopBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="tools_controls:DockingManager.LeftFloatWindowSelectedTopBorderBrush" Value="{StaticResource Border}" />
        <Setter Property="tools_controls:DockingManager.LeftFloatMouseOverTopBorderBrush" Value="{StaticResource BorderAlt1}" />

        <Setter Property="tools_controls:DockingManager.LeftFloatWindowBottomBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="tools_controls:DockingManager.LeftFloatWindowSelectedBottomBorderBrush" Value="{StaticResource Border}" />
        <Setter Property="tools_controls:DockingManager.LeftFloatMouseOverBottomBorderBrush" Value="{StaticResource BorderAlt1}" />

        <Setter Property="tools_controls:DockingManager.RightFloatWindowBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="tools_controls:DockingManager.RightFloatWindowSelectedBorderBrush" Value="{StaticResource Border}" />
        <Setter Property="tools_controls:DockingManager.RightFloatMouseOverBorderBrush" Value="{StaticResource BorderAlt1}" />

        <Setter Property="tools_controls:DockingManager.RightFloatWindowTopBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="tools_controls:DockingManager.RightFloatWindowSelectedTopBorderBrush" Value="{StaticResource Border}" />
        <Setter Property="tools_controls:DockingManager.RightFloatMouseOverTopBorderBrush" Value="{StaticResource BorderAlt1}" />

        <Setter Property="tools_controls:DockingManager.RightFloatWindowBottomBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="tools_controls:DockingManager.RightFloatWindowSelectedBottomBorderBrush" Value="{StaticResource Border}" />
        <Setter Property="tools_controls:DockingManager.RightFloatMouseOverBottomBorderBrush" Value="{StaticResource BorderAlt1}" />

        <Setter Property="tools_controls:DockingManager.BottomFloatMouseOverBorderBrush" Value="{StaticResource BorderAlt1}" />
        <Setter Property="SplitterSize" Value="5" />
        <Setter Property="FloatWindowLeftBorderWidth" Value="7" />
        <Setter Property="FloatWindowRightBorderWidth" Value="7" />
        <Setter Property="FloatWindowBottomBorderHeight" Value="7" />
        <Setter Property="DockHeaderStyle" Value="{StaticResource SyncfusionDockHeaderPresenterStyle}" />
        <Setter Property="tools_controls:DockingManager.TabItemsForeground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="tools_controls:DockingManager.TabItemForegroundSelected" Value="{StaticResource ContentForeground}" />
        <Setter Property="PreviewPanelBackground" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="tools_controls:DockingManager.HeaderStyle" Value="{Binding DockHeaderStyle, RelativeSource={RelativeSource Mode=Self}}" />
        <Setter Property="SplitterBackground" Value="{StaticResource BorderAlt}" />
        <Setter Property="TextElement.FontWeight" Value="Normal" />
        <Setter Property="SidePanelBorderThickness" Value="0" />
        <Style.Triggers>
            <Trigger Property="UseNativeFloatWindow" Value="True">
                <Setter Property="CloseButtonTemplate" Value="{StaticResource NativeCaptionButtonsTemplate}" />
            </Trigger>
            <Trigger Property="IsTouchEnabled" Value="True">
                <Setter Property="SidePanelSize" Value="35" />
            </Trigger>
            <Trigger Property="IsTouchEnabled" Value="False">
                <Setter Property="SidePanelSize" Value="24" />
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CurveKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionDockingManagerStyle}" TargetType="{x:Type tools_controls:DockingManager}" />
</ResourceDictionary>
