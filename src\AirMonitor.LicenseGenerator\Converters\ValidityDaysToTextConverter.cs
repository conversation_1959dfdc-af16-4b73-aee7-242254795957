using System.Globalization;
using System.Windows.Data;

namespace AirMonitor.LicenseGenerator.Views;

/// <summary>
/// 有效期天数到文本转换器
/// </summary>
public class ValidityDaysToTextConverter : IValueConverter
{
    public static readonly ValidityDaysToTextConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int days)
        {
            return days == -1 ? "永久" : $"{days}天";
        }
        return "未知";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
