# 商用空调监控调试软件 - 技术规格说明

## 文档信息
- **文档版本**: 1.0
- **创建日期**: 2024-12-09
- **最后更新**: 2024-12-09
- **文档状态**: 草稿

## 1. 系统概述

### 1.1 产品定位
商用空调监控调试软件是一款专业的单机版桌面应用程序，专为空调维护工程师和技术人员设计，提供空调机组的实时监控、数据采集、故障诊断和历史数据分析功能。

### 1.2 核心特性
- **单机版设计**: 无需网络连接，支持完全离线运行
- **License授权**: 基于license文件的本地授权验证机制
- **高性能监控**: 支持200-500ms高频数据采集和实时显示
- **现代化界面**: Windows 11风格的深色/浅色主题
- **专业协议**: 完整的自定义485协议解析和通信

## 2. 技术规格

### 2.1 开发平台
- **开发框架**: .NET 8.0
- **UI框架**: WPF (Windows Presentation Foundation)
- **目标平台**: Windows 10/11 (x64)
- **开发工具**: Visual Studio 2022 (17.8+)
- **编程语言**: C# 12.0

### 2.2 第三方组件

#### 2.2.1 Syncfusion WPF Controls 29.2.9
- **SfDataGrid**: 高性能数据表格显示
- **SfGauge**: 仪表盘和指示器控件
- **SfScheduler**: 数据回放时间轴控件
- **Windows11Dark/Light**: 现代化主题包

#### 2.2.2 OxyPlot.WPF 2.2.0
- **PlotView**: 高性能图表控件
- **实时曲线**: 支持实时数据更新和动画
- **历史数据图表**: 支持大数据量的历史数据可视化
- **多种图表类型**: 线图、散点图、柱状图等
- **交互功能**: 缩放、平移、数据点提示

#### 2.2.3 数据访问组件
- **Entity Framework Core 9.0.5**: ORM框架
- **Microsoft.Data.Sqlite 9.0.5**: SQLite数据库驱动
- **Microsoft.EntityFrameworkCore.Design 9.0.5**: EF设计时工具

#### 2.2.4 通信组件
- **System.IO.Ports 9.0.5**: 串口通信
- **自定义485协议解析器**: 支持多种协议格式

#### 2.2.5 MVVM框架
- **CommunityToolkit.Mvvm 8.4.0**: 现代MVVM框架
- **Microsoft.Extensions.DependencyInjection 9.0.5**: 依赖注入容器

#### 2.2.6 工具库
- **AutoMapper 14.0.0**: 对象映射
- **FluentValidation 12.0.0**: 数据验证
- **Polly 8.5.2**: 重试策略和容错
- **Serilog 4.2.0**: 结构化日志
- **System.Text.Json 9.0.5**: JSON序列化

### 2.3 数据库规格
- **数据库引擎**: SQLite 3.x
- **数据库文件**: 动态创建，按机组分库
- **连接方式**: 本地文件连接
- **事务支持**: 完整的ACID事务支持
- **备份策略**: 自动备份和手动备份

## 3. 性能规格

### 3.1 响应时间要求
- **UI响应时间**: < 200ms
- **数据采集间隔**: 200-500ms可配置
- **数据库查询**: < 100ms (单次查询)
- **图表刷新**: < 50ms
- **应用启动时间**: < 5秒

### 3.2 资源使用限制
- **内存使用**: < 500MB (正常运行)
- **CPU使用**: < 10% (空闲状态)
- **磁盘空间**: 最小500MB，推荐2GB
- **数据库大小**: 单库最大1GB

### 3.3 并发处理能力
- **串口连接**: 支持1个主串口连接
- **设备监控**: 支持最多50台设备同时监控
- **数据点**: 每台设备最多100个数据点
- **历史数据**: 支持1年历史数据存储

## 4. 功能规格

### 4.1 串口通信规格
- **协议类型**: 自定义485协议
- **波特率**: 1200-115200 bps可配置
- **数据位**: 7-8位可配置
- **停止位**: 1-2位可配置
- **校验位**: None/Odd/Even可配置
- **流控制**: 无流控制
- **超时设置**: 1-10秒可配置

### 4.2 协议解析规格
- **支持格式**: 两种解析格式
  - 格式1: 固定字节解析 (A1, 01功能码)
  - 格式2: 参数索引解析 (A5, 12, 02, 05功能码)
- **数据类型**: 支持多种数据类型转换
- **位域解析**: 支持位级别的数据解析
- **配置文件**: JSON格式的协议配置
- **扩展性**: 支持新协议的配置化添加

### 4.3 数据采集规格
- **采集频率**: 200ms-10秒可配置
- **数据缓存**: 内存缓存最近1小时数据
- **数据验证**: CRC16校验和数据完整性检查
- **错误处理**: 自动重试和错误恢复机制
- **数据格式**: 支持多种数据格式转换

### 4.4 用户界面规格
- **主题支持**: Windows11Dark/Light主题
- **字体**: 微软雅黑，支持12-20px字号
- **分辨率**: 最小1024x768，推荐1920x1080
- **DPI支持**: 支持高DPI显示器
- **多语言**: 当前支持中文，预留国际化接口

### 4.5 数据存储规格
- **存储格式**: SQLite数据库文件
- **数据压缩**: 历史数据自动压缩
- **索引策略**: 时间戳和设备ID索引
- **分区策略**: 按月分表存储历史数据
- **清理策略**: 自动清理超过1年的历史数据

### 4.6 License注册机UI规格
- **界面风格**: 与主程序保持完全一致的UI风格
- **控件库**: 使用相同的Syncfusion WPF Controls 29.2.9
- **主题系统**: 统一使用Windows11Dark/Light主题
- **字体规范**: 统一使用微软雅黑字体，12-16px字号
- **颜色方案**: 与主程序保持一致的颜色配置
- **布局规范**: 遵循相同的间距、对齐和布局原则
- **交互模式**: 保持一致的用户交互体验
- **代码复用**: 基于AirMonitor.Core公共类库，确保代码一致性

## 5. 安全规格

### 5.1 授权安全
- **授权方式**: License文件本地验证
- **加密算法**: RSA 2048位 + AES 256位
- **硬件绑定**: CPU序列号 + 主板序列号
- **试用期**: 30天试用期支持
- **功能限制**: 支持按功能模块授权

### 5.2 数据安全
- **数据库加密**: SQLite数据库文件加密
- **配置加密**: 敏感配置信息加密存储
- **通信安全**: 串口通信CRC校验
- **日志安全**: 敏感信息脱敏处理

### 5.3 应用安全
- **代码签名**: 应用程序数字签名
- **完整性检查**: 启动时文件完整性验证
- **权限控制**: 最小权限原则
- **异常处理**: 全局异常捕获和处理

## 6. 兼容性规格

### 6.1 操作系统兼容性
- **Windows 10**: 版本1903及以上
- **Windows 11**: 所有版本
- **架构支持**: x64架构
- **.NET运行时**: .NET 8.0 Runtime

### 6.2 硬件兼容性
- **CPU**: Intel/AMD x64处理器
- **内存**: 最小4GB，推荐8GB
- **硬盘**: 最小500MB可用空间
- **串口**: RS485转USB适配器
- **显示器**: 最小1024x768分辨率

### 6.3 设备兼容性
- **空调品牌**: 支持自定义协议的商用空调
- **通信接口**: RS485串口通信
- **协议版本**: 支持多版本协议兼容
- **设备数量**: 单总线最多50台设备

## 7. 部署规格

### 7.1 安装方式
- **MSI安装包**: 标准Windows安装包
- **绿色版**: 免安装便携版本
- **自包含部署**: 包含.NET运行时
- **单文件部署**: 可选单文件发布

### 7.2 配置文件
- **应用配置**: appsettings.json
- **协议配置**: protocol_config.json
- **用户配置**: user_settings.json
- **License文件**: license.lic

### 7.3 目录结构
```
AirMonitor/
├── AirMonitor.exe          # 主程序
├── appsettings.json        # 应用配置
├── protocol_config.json    # 协议配置
├── license.lic            # 授权文件
├── Logs/                  # 日志目录
├── Data/                  # 数据库目录
├── Backup/                # 备份目录
└── Themes/                # 主题资源
```

## 8. 测试规格

### 8.1 单元测试
- **测试框架**: NUnit 4.2.2
- **覆盖率要求**: > 80%
- **模拟框架**: Moq 4.20.72
- **测试数据库**: InMemory Database

### 8.2 集成测试
- **串口测试**: 模拟串口设备测试
- **数据库测试**: 真实数据库集成测试
- **UI测试**: 自动化UI测试
- **性能测试**: 负载和压力测试

### 8.3 验收测试
- **功能测试**: 完整功能验证
- **兼容性测试**: 多环境兼容性验证
- **安全测试**: 安全漏洞扫描
- **用户体验测试**: 易用性测试

## 9. 维护规格

### 9.1 日志规格
- **日志级别**: Debug/Info/Warning/Error/Fatal
- **日志格式**: JSON结构化日志
- **文件大小**: 单文件最大10MB
- **保留策略**: 保留最近30天日志
- **性能日志**: 关键操作性能监控

### 9.2 监控规格
- **系统监控**: CPU、内存、磁盘使用率
- **应用监控**: 响应时间、错误率、吞吐量
- **业务监控**: 数据采集成功率、设备在线率
- **告警机制**: 异常情况自动告警

### 9.3 更新规格
- **版本检查**: 启动时检查新版本
- **增量更新**: 支持增量更新机制
- **回滚机制**: 更新失败自动回滚
- **配置迁移**: 版本升级配置自动迁移

## 10. 文档规格

### 10.1 技术文档
- **API文档**: 接口文档和示例
- **架构文档**: 系统架构设计文档
- **部署文档**: 安装和部署指南
- **开发文档**: 开发环境搭建指南

### 10.2 用户文档
- **用户手册**: 详细的用户操作手册
- **快速入门**: 快速上手指南
- **常见问题**: FAQ和故障排除
- **视频教程**: 操作演示视频

### 10.3 维护文档
- **运维手册**: 系统运维指南
- **故障排除**: 常见故障解决方案
- **性能调优**: 性能优化建议
- **安全指南**: 安全配置和最佳实践
