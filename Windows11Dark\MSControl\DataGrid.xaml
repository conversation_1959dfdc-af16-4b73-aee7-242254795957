<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                     xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:Themes="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero">
    <ResourceDictionary.MergedDictionaries>
     <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
     <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="{x:Type DataGridRow}">
        <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
        <Setter Property="SnapsToDevicePixels" Value="true"/>
        <Setter Property="Validation.ErrorTemplate" Value="{x:Null}"/>
        <Setter Property="ValidationErrorTemplate">
            <Setter.Value>
                <ControlTemplate>
                    <TextBlock Foreground="Red" Margin="2,0,0,0" Text="!" VerticalAlignment="Center"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridRow}">
                    <Border x:Name="DGR_Border" BorderBrush="{TemplateBinding BorderBrush}" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            Background="{TemplateBinding Background}" 
                            SnapsToDevicePixels="True">
                        <SelectiveScrollingGrid>
                            <SelectiveScrollingGrid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </SelectiveScrollingGrid.ColumnDefinitions>
                            <SelectiveScrollingGrid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </SelectiveScrollingGrid.RowDefinitions>
                            <DataGridCellsPresenter Grid.Column="1" 
                                                    ItemsPanel="{TemplateBinding ItemsPanel}" 
                                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                            <DataGridDetailsPresenter Grid.Column="1" 
                                                      Grid.Row="1" 
                                                      SelectiveScrollingGrid.SelectiveScrollingOrientation="{Binding AreRowDetailsFrozen, ConverterParameter={x:Static SelectiveScrollingOrientation.Vertical}, Converter={x:Static DataGrid.RowDetailsScrollingConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" Visibility="{TemplateBinding DetailsVisibility}"/>
                            <DataGridRowHeader Grid.RowSpan="2" 
                                               SelectiveScrollingGrid.SelectiveScrollingOrientation="Vertical" 
                                               Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.Row}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"/>
                        </SelectiveScrollingGrid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <Style TargetType="{x:Type DataGridCell}">
        <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource ContentBackground}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridCell}">
                    <Border BorderBrush="{TemplateBinding BorderBrush}" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            Background="{TemplateBinding Background}" 
                            SnapsToDevicePixels="True">
                        <ContentPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                          VerticalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="true">
                <Setter Property="Background" Value="{StaticResource ContentBackgroundSelected}"/>
                <Setter Property="Foreground" Value="{StaticResource SelectedForeground}"/>
                <Setter Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}"/>
            </Trigger>
            <Trigger Property="IsKeyboardFocusWithin" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt3}"/>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsSelected"
                                           Value="true"/>
                    <Condition Property="Selector.IsSelectionActive"
                                           Value="false"/>
                </MultiTrigger.Conditions>
                <Setter Property="Background"
                                    Value="{StaticResource ContentBackgroundInactive}"/>
                <Setter Property="BorderBrush"
                                    Value="{StaticResource ContentBackgroundInactive}"/>
                <Setter Property="Foreground"
                                    Value="{StaticResource ContentForeground}"/>
            </MultiTrigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="{StaticResource ContentBackgroundDisabled}"/>
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                <Setter Property="BorderBrush" Value="{StaticResource ContentBackgroundDisabled}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    
    <Style x:Key="ColumnHeaderGripperStyle" TargetType="{x:Type Thumb}">
        <Setter Property="Width" Value="8"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Cursor" Value="SizeWE"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border Background="{TemplateBinding Background}" 
                            Padding="{TemplateBinding Padding}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type DataGridColumnHeader}">
        <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1}"/>
        <Setter Property="Padding" Value="2,0,2,0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridColumnHeader}">
                    <Grid>
                        <Themes:DataGridHeaderBorder x:Name="DataGridHeaderBorder"
                                                     BorderBrush="{TemplateBinding BorderBrush}"
                                                     BorderThickness="{TemplateBinding BorderThickness}" 
                                                     Background="{TemplateBinding Background}" 
                                                     IsClickable="{TemplateBinding CanUserSort}" 
                                                     IsPressed="{TemplateBinding IsPressed}" 
                                                     IsHovered="{TemplateBinding IsMouseOver}" 
                                                     Padding="{TemplateBinding Padding}" 
                                                     SortDirection="{TemplateBinding SortDirection}" 
                                                     SeparatorBrush="{TemplateBinding SeparatorBrush}" 
                                                     SeparatorVisibility="{TemplateBinding SeparatorVisibility}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              RecognizesAccessKey="True" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" 
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Themes:DataGridHeaderBorder>
                        <Thumb x:Name="PART_LeftHeaderGripper" HorizontalAlignment="Left" Style="{StaticResource ColumnHeaderGripperStyle}"/>
                        <Thumb x:Name="PART_RightHeaderGripper" HorizontalAlignment="Right" Style="{StaticResource ColumnHeaderGripperStyle}"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>                        
                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Self}, Path=DisplayIndex}" Value="0">
                            <Setter Property="BorderThickness" Value="1,0,1,1"/>
                        </DataTrigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Background" TargetName="DataGridHeaderBorder" Value="{StaticResource TableHoveredBackground}"/>
                            <Setter Property="Foreground" Value="{StaticResource TableHoveredForeground}"/>
                            <Setter Property="BorderBrush" TargetName="DataGridHeaderBorder" Value="{StaticResource BorderAlt}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter Property="Background"  TargetName="DataGridHeaderBorder" Value="{StaticResource TablePressedBackground}"/>
                            <Setter Property="Foreground" Value="{StaticResource TablePressedForeground}"/>
                            <Setter Property="BorderBrush"  TargetName="DataGridHeaderBorder" Value="{StaticResource BorderAlt1}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" TargetName="DataGridHeaderBorder" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                            <Setter Property="BorderBrush" TargetName="DataGridHeaderBorder" Value="{StaticResource BorderAlt}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <BooleanToVisibilityConverter x:Key="bool2VisibilityConverter"/>
    <Style x:Key="RowHeaderGripperStyle" TargetType="{x:Type Thumb}">
        <Setter Property="Height" Value="8"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Cursor" Value="SizeNS"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border Background="{TemplateBinding Background}" Padding="{TemplateBinding Padding}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type DataGridRowHeader}">
        <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" Value="0,0,1,1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridRowHeader}">
                    <Grid>
                        <Themes:DataGridHeaderBorder BorderBrush="{TemplateBinding BorderBrush}" 
                                                     BorderThickness="{TemplateBinding BorderThickness}" 
                                                     Background="{TemplateBinding Background}" 
                                                     IsPressed="{TemplateBinding IsPressed}" 
                                                     IsHovered="{TemplateBinding IsMouseOver}" 
                                                     IsSelected="{TemplateBinding IsRowSelected}" 
                                                     Orientation="Horizontal" 
                                                     Padding="{TemplateBinding Padding}" 
                                                     SeparatorBrush="{TemplateBinding SeparatorBrush}" 
                                                     SeparatorVisibility="{TemplateBinding SeparatorVisibility}">
                            <StackPanel Orientation="Horizontal">
                                <ContentPresenter RecognizesAccessKey="True" 
                                                  SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" 
                                                  VerticalAlignment="Center"/>
                                <Control SnapsToDevicePixels="false" 
                                         Template="{Binding ValidationErrorTemplate, RelativeSource={RelativeSource AncestorType={x:Type DataGridRow}}}">
                                    <Control.Visibility>
                                        <Binding Path="(Validation.HasError)" RelativeSource="{RelativeSource FindAncestor, AncestorLevel=1, AncestorType={x:Type DataGridRow}}">
                                            <Binding.Converter>
                                                <BooleanToVisibilityConverter/>
                                            </Binding.Converter>
                                        </Binding>
                                    </Control.Visibility>
                                </Control>
                            </StackPanel>
                        </Themes:DataGridHeaderBorder>
                        <Thumb x:Name="PART_TopHeaderGripper" Style="{StaticResource RowHeaderGripperStyle}" VerticalAlignment="Top"/>
                        <Thumb x:Name="PART_BottomHeaderGripper" Style="{StaticResource RowHeaderGripperStyle}" VerticalAlignment="Bottom"/>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
            </Trigger>            
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Background" Value="{StaticResource TableHoveredBackground}"/>
                <Setter Property="Foreground" Value="{StaticResource TableHoveredForeground}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="true">
                <Setter Property="Background" Value="{StaticResource TablePressedBackground}"/>
                <Setter Property="Foreground" Value="{StaticResource TablePressedForeground}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt1}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="DataGridFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="{StaticResource Windows11Dark.FocusMargin}" 
                               SnapsToDevicePixels="true" 
                               Stroke="{StaticResource Border}" 
                               StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Dark.StrokeDashArray}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="DefaultDataGridColumnHeadersPresenterStyle" TargetType="{x:Type DataGridColumnHeadersPresenter}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridColumnHeadersPresenter}">
                    <Grid>
                        <DataGridColumnHeader x:Name="PART_FillerColumnHeader" IsHitTestVisible="False"/>
                        <ItemsPresenter/>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="{ComponentResourceKey ResourceId=DataGridSelectAllButtonStyle, TypeInTargetAssembly={x:Type DataGrid}}" TargetType="{x:Type Button}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                        <Grid>
                            <Border x:Name="Border" 
                                    SnapsToDevicePixels="True" 
                                    Background="{StaticResource ContentBackground}"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    BorderThickness="0,0,0,1">
                            </Border>
                            <Polygon x:Name="Arrow"
                                     HorizontalAlignment="Right" 
                                     VerticalAlignment="Bottom" 
                                     Margin="8,8,3,3"
                                     Opacity="0.5"
                                     Points="0,10 10,10 10,0" 
                                     Stretch="Uniform" 
                                     Fill="{StaticResource IconColor}">
                            </Polygon>
                        </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>                        
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="Border" Value="{StaticResource TableHoveredBackground}"/>
                            <Setter Property="BorderBrush" TargetName="Border" Value="{StaticResource TableHoveredBackground}"/>
                            <Setter Property="Fill" TargetName="Arrow" Value="{StaticResource IconColor}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="Border" Value="{StaticResource TablePressedBackground}"/>
                            <Setter Property="BorderBrush" TargetName="Border" Value="{StaticResource TablePressedBackground}"/>
                            <Setter Property="Fill" TargetName="Arrow" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="Border" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="BorderBrush" TargetName="Border" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Fill" TargetName="Arrow" Value="{StaticResource IconColorDisabled}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFDataGridStyle" TargetType="{x:Type DataGrid}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource DataGridFocusVisual}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="AlternatingRowBackground" Value="{StaticResource TableAlternativeBackground}"/>
        <Setter Property="HorizontalGridLinesBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="VerticalGridLinesBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1}"/>
        <Setter Property="RowDetailsVisibilityMode" Value="VisibleWhenSelected"/>
        <Setter Property="ScrollViewer.CanContentScroll" Value="true"/>
        <Setter Property="ScrollViewer.PanningMode" Value="Both"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGrid}">
                    <Border BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" Padding="{TemplateBinding Padding}" SnapsToDevicePixels="True">
                        <ScrollViewer x:Name="DG_ScrollViewer" Focusable="false">
                            <ScrollViewer.Template>
                                <ControlTemplate TargetType="{x:Type ScrollViewer}">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="*"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Button x:Name="Border"
                                                Command="{x:Static DataGrid.SelectAllCommand}" 
                                                BorderThickness="0,0,1,1"
                                                Focusable="false" 
                                                Style="{StaticResource {ComponentResourceKey ResourceId=DataGridSelectAllButtonStyle, TypeInTargetAssembly={x:Type DataGrid}}}" 
                                                Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.All}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" 
                                                Width="{Binding CellsPanelHorizontalOffset, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"/>
                                        <DataGridColumnHeadersPresenter x:Name="PART_ColumnHeadersPresenter" 
                                                                        Grid.Column="1" 
                                                                        Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.Column}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
                                                                        Style="{StaticResource DefaultDataGridColumnHeadersPresenterStyle}"/>
                                        <ScrollContentPresenter x:Name="PART_ScrollContentPresenter" 
                                                                CanContentScroll="{TemplateBinding CanContentScroll}" 
                                                                Grid.ColumnSpan="2"
                                                                Grid.Row="1"/>
                                        <ScrollBar x:Name="PART_VerticalScrollBar" 
                                                   Grid.Column="2" 
                                                   Maximum="{TemplateBinding ScrollableHeight}" 
                                                   Orientation="Vertical" 
                                                   Grid.Row="1" 
                                                   Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}" 
                                                   Value="{Binding VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" 
                                                   ViewportSize="{TemplateBinding ViewportHeight}"/>
                                        <Grid Grid.Column="1" Grid.Row="2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="{Binding NonFrozenColumnsViewportHorizontalOffset, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <ScrollBar x:Name="PART_HorizontalScrollBar" 
                                                       Grid.Column="1"
                                                       Maximum="{TemplateBinding ScrollableWidth}"
                                                       Orientation="Horizontal"
                                                       Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                                       Value="{Binding HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"
                                                       ViewportSize="{TemplateBinding ViewportWidth}"/>
                                        </Grid>
                                    </Grid>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Compact">
                                            <Setter Property="Grid.ColumnSpan" TargetName="PART_ScrollContentPresenter" Value="3"/>
                                            <Setter Property="Grid.ColumnSpan" TargetName="PART_ColumnHeadersPresenter" Value="3"/>
                                            <Setter Property="Grid.RowSpan" TargetName="PART_ScrollContentPresenter" Value="3"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </ScrollViewer.Template>
                            <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsGrouping" Value="true">
                <Setter Property="ScrollViewer.CanContentScroll" Value="false" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource WPFDataGridStyle}" TargetType="{x:Type DataGrid}"/>
</ResourceDictionary>
