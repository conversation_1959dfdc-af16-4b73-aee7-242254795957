<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:diagram_util="clr-namespace:Syncfusion.UI.Xaml.Diagram.Utility;assembly=Syncfusion.SfDiagram.WPF"
                    xmlns:diagram_printing="clr-namespace:Syncfusion.UI.Xaml.Diagram.Controls;assembly=Syncfusion.SfDiagram.WPF">
    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ComboBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBlock.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GroupBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatPrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/PrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatButton.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <diagram_util:TextToIntegerConverter x:Key="TextToIntegerConverter"/>

    <Style TargetType="diagram_printing:PrintPreviewControl" x:Key="SyncfusionPrintPreviewControlStyle">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_printing:PrintPreviewControl">
                    <Border Background="{StaticResource ContentBackground}">
                        <Grid x:Name="PrintableArea"
                              Margin="5" >
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="200"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="30" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="40" />
                            </Grid.RowDefinitions>
                            <GroupBox Header="Print Preview:" 
                                      Grid.RowSpan="2"
                                      Margin="8.5,8.5,8.5,8.5"
                                      Style="{StaticResource WPFGroupBoxStyle}">
                                <Grid Height="520"
                                      Width="520" 
                                      VerticalAlignment="Center"
                                      HorizontalAlignment="Center" 
                                      Background="{StaticResource ContentBackgroundAlt3}" >
                                    <Grid x:Name="PrintPreviewRect" 
                                          Grid.Row="1"
                                          Height="490" 
                                          Width="490"
                                          HorizontalAlignment="Center" 
                                          VerticalAlignment="Center">
                                        <Border x:Name="border"
                                                VerticalAlignment="Center" 
                                                HorizontalAlignment="Center" 
                                                BorderThickness="1" 
                                                BorderBrush="{StaticResource BorderAlt}" 
                                                Background="{StaticResource ContentBackground}" >
                                            <Grid>
                                                <Rectangle x:Name="BackgroundPreviewRect"/>
                                                <Grid>
                                                    <Rectangle x:Name="PageBreakPreviewRect"
                                                               Stroke="{StaticResource BorderAlt}"
                                                               StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}"
                                                               StrokeDashArray="6"/>
                                                    <Viewbox x:Name="PreviewViewbox" 
                                                             HorizontalAlignment="Left"
                                                             VerticalAlignment="Top">
                                                        <Rectangle x:Name="PreviewRect"
                                                                   Fill="{TemplateBinding Visual}"/>
                                                    </Viewbox>
                                                </Grid>
                                            </Grid>
                                        </Border>
                                    </Grid>
                                </Grid>
                            </GroupBox>
                            <diagram_printing:PageNavigator x:Name="PageInfo"
                                                            Grid.Row="2" 
                                                            Grid.Column="0"
                                                            Visibility="Hidden"
                                                            IsEnabled="False" 
                                                            HorizontalAlignment="Center"
                                                            VerticalAlignment="Top"
                                                            Margin="0,0,0,5"                                                    
                                                            CurrentPage="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=CurrentPage, Mode=TwoWay}" />
                            <GroupBox Header="Print Properties:"
                                      Grid.RowSpan="2" 
                                      Grid.Column="1"                                      
                                      Margin="5,8.5,8.5,8.5"
                                      Style="{StaticResource WPFGroupBoxStyle}">
                                <Grid Grid.Row="1"
                                      Grid.Column="1"
                                      HorizontalAlignment="Stretch" 
                                      VerticalAlignment="Stretch">
                                    <StackPanel Grid.Row="0" 
                                                Margin="10,10,10,10">
                                        <TextBlock x:Name="diagram_Stretch"
                                                   Margin="10,10,0,5" 
                                                   Style="{StaticResource WPFTextBlockStyle}" />
                                        <ComboBox x:Name="Print_Stretch"
                                                  KeyboardNavigation.TabIndex="0"
                                                  diagram_util:FocusUtility.FocusOnLoad="True"                                                  
                                                  Margin="10,0,10,10"
                                                  Style="{StaticResource WPFComboBoxStyle}">
                                            <ComboBoxItem Name="diagarm_None" />
                                            <ComboBoxItem Name="diagram_Fill" />
                                            <ComboBoxItem Name="diagram_Uniform" />
                                            <ComboBoxItem Name="diagram_UniformtoFill" />
                                        </ComboBox>
                                        <Button x:Name="diagram_advanced"
                                                KeyboardNavigation.TabIndex="1"
                                                HorizontalAlignment="Right"                                                
                                                Width="80"
                                                Height="25"
                                                Margin="0,5,10,0" 
                                                Style="{StaticResource WPFFlatPrimaryButtonStyle}"
                                                Command="{TemplateBinding AdvancedCommand}"/>
                                    </StackPanel>
                                </Grid>
                            </GroupBox>
                            <Grid Grid.Column="1"
                                  Grid.Row="2" 
                                  VerticalAlignment="Bottom"
                                  HorizontalAlignment="Stretch">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Button x:Name="diagram_print"
                                        Grid.Column="0"
                                        KeyboardNavigation.TabIndex="5"
                                        Width="80"
                                        Height="25"
                                        VerticalAlignment="Bottom"
                                        HorizontalAlignment="Right" 
                                        Style="{StaticResource WPFPrimaryButtonStyle}"
                                        Margin="0,0,0,8"
                                        Command="{TemplateBinding PrintCommand}" />
                                <Button x:Name="diagram_cancel" 
                                        Grid.Column="1" 
                                        KeyboardNavigation.TabIndex="6" 
                                        Width="80"
                                        Height="25" 
                                        VerticalAlignment="Bottom" 
                                        HorizontalAlignment="Center"
                                        Margin="0,0,0,8"
                                        Style="{StaticResource WPFFlatButtonStyle}"
                                        Command="{TemplateBinding CancelCommand}" 
                                        IsCancel="True" />
                            </Grid>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type diagram_printing:PrintPreviewControl}" BasedOn="{StaticResource SyncfusionPrintPreviewControlStyle}"/>

    <Style TargetType="diagram_printing:PageNavigator" x:Key="SyncfusionPageNavigatorStyle">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="diagram_printing:PageNavigator">
                    <StackPanel Name="PageInfo"
                                Visibility="Visible"
                                Margin="0,10,0,0"
                                HorizontalAlignment="Center"
                                Orientation="Horizontal"
                                Height="25">
                        <Button Name="PageDecrease"
                                KeyboardNavigation.TabIndex="2"                                
                                MinWidth="24" 
                                Width="24"
                                Height="24" 
                                Command="{TemplateBinding PageDecreaseCommand}"
                                Style="{StaticResource WPFGlyphButtonStyle}">
                            <Path x:Name="decreasepath"
                                  Fill="{Binding Path=Foreground,RelativeSource={RelativeSource AncestorType={x:Type Button}}}" 
                                  Stretch="Fill"
                                  Width="11"
                                  Height="14.001" 
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center">
                                <Path.Data>
                                    <PathGeometry>M0.72506 6.57243L9.75446 0.580719C10.0613 0.377118 10.5 0.588873 10.5 1.00828L10.5 12.9917C10.5 13.4111 10.0613 13.6229 9.75446 13.4193L0.72506 7.42757C0.42498 7.22844 0.42498 6.77156 0.72506 6.57243Z</PathGeometry>
                                </Path.Data>
                            </Path>
                        </Button>
                        <TextBox x:Name="CurrentPageBox"
                                 diagram_util:FocusUtility.FocusOnLoad="True" 
                                 KeyboardNavigation.TabIndex="3"
                                 Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=CurrentPage, Converter={StaticResource TextToIntegerConverter}, Mode=TwoWay,  FallbackValue=1}" 
                                 TextAlignment="Center"
                                 Style="{StaticResource WPFTextBoxStyle}"
                                 MinWidth="12"
                                 MinHeight="10"
                                 Height="22"
                                 Margin="4,0,4,0">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" 
                                            Command="{Binding RelativeSource={RelativeSource TemplatedParent}, Path= CurrentPageChangedCommand}" 
                                            CommandParameter="{Binding ElementName=CurrentPageBox, Path=Text}"/>
                            </TextBox.InputBindings>
                        </TextBox>
                        <TextBlock Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=PageCount, StringFormat='of {0}'}"
                                   TextAlignment="Center"                                   
                                   VerticalAlignment="Stretch"
                                   Style="{StaticResource WPFTextBlockStyle}"                                   
                                   Margin="0,4,4,0"
                                   Height="22" />
                        <Button Name="PageIncrease"
                                KeyboardNavigation.TabIndex="4"                                 
                                MinWidth="24" 
                                Width="24" 
                                Height="24"
                                Command="{TemplateBinding PageIncreaseCommand}"
                                Style="{StaticResource WPFGlyphButtonStyle}"
                                Tag="#999999">
                            <Path x:Name="increasepath"
                                  Fill="{Binding Path=Foreground,RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                  Stretch="Fill" 
                                  Width="11"
                                  Height="14"
                                  HorizontalAlignment="Center" 
                                  VerticalAlignment="Center">
                                <Path.Data>
                                    <PathGeometry>M10.2749 7.42757L1.24553 13.4193C0.938713 13.6229 0.5 13.4111 0.5 12.9917L0.500001 1.00828C0.500001 0.588873 0.938712 0.377118 1.24554 0.580719L10.2749 6.57244C10.575 6.77156 10.575 7.22844 10.2749 7.42757Z</PathGeometry>
                                </Path.Data>
                            </Path>
                        </Button>
                    </StackPanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type diagram_printing:PageNavigator}" BasedOn="{StaticResource SyncfusionPageNavigatorStyle}"/>
</ResourceDictionary>
