<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib"
                    xmlns:grid="clr-namespace:Syncfusion.UI.Xaml.Grid;assembly=Syncfusion.SfGrid.WPF"  
                    xmlns:skinManager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:gridRowFilter="clr-namespace:Syncfusion.UI.Xaml.Grid.RowFilter;assembly=Syncfusion.SfGrid.WPF">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/ChromelessWindow/ChromelessWindow.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatPrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/PrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphEditableDropdownExpander.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ListBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/DatePicker.xaml" />
        <skinManager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <skinManager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <grid:BoolToVisiblityConverter x:Key="boolToVisiblityConverter" />
    <grid:SortDirectionToVisibilityConverter x:Key="sortDirectionToVisibilityConverter" />
    <grid:SortDirectionToWidthConverter x:Key="sortDirectionToWidthConverter" />
    <grid:VisiblityConverter x:Key="VisiblityConverter" />
    <grid:TextVisibilityConverter x:Key="textBlockVisibilityConverter" />
    <grid:ReverseVisibilityConverter x:Key="reverseVisibilityConverter" />
    <grid:LoadingVisiblityConverter x:Key="loadingVisiblityConverter" />
    <grid:ListItemsVisiblityConverter x:Key="listItemsVisiblityConverter" />
    <grid:HeightToMarginConverter x:Key="heightToMarginConverter" />
    <grid:ResourceNameConverter x:Key="resourceNameConverter" />
    <grid:AdvancedFiltersButtonVisibiltyConverter x:Key="advancedFilterButtonVisibilityConverter" />
    <grid:FilteredFromCheckVisibilityConverter x:Key="filteredFromCheckVisibilityConverter" />
    <grid:FilterValueComboEnableConverter x:Key="filterValueComboEnableConverter" />
    <grid:CaseSensitiveConverter x:Key="casesensitiveConverter" />
    <grid:PageCountFormatConverter x:Key="PageCountFormatConverter" />
    <grid:ZoomFactorFormatConverter x:Key="ZoomFactorFormatConverter" />
    <grid:PrintOrientationConverter x:Key="PrintOrientationConverter" />
    <grid:ScaleOptionsConverter x:Key="ScaleOptionsConverter" />

    <!-- SfDataGrid Material Design Style -->

    <!--GridCell-->

    <!--UnboundRow-->

    <!--HeaderCell-->

    <!--StackedHeaderCell-->

    <!--RowHeaderCell-->

    <!--RowHeaderIndentCell-->

    <!--TableSummaryRow-->

    <!--CaptionSummaryRow-->

    <!--GroupSummaryRow-->

    <!--FilterRow-->

    <!--AddNewRow-->

    <!--GroupDropArea-->

    <!--GroupDropArea.Item-->
    
    <!--DetailsViewExpanderCell-->

    <!--ExpanderCell-->

    <!--ValidationToolTip-->

    <!--DragArrow-->
    
    <!--DragLine-->

    <!--RowDragWindow-->

    <!-- ColumnDragWindow-->
    
    <!--Row-->

    <!--CurrentCell-->

    <!--GridFilterControl-->

    <!--ColumnChooser-->

    <ControlTemplate x:Key="SyncfusionDataGridValidationToolTipTemplate"  TargetType="ToolTip">
        <Grid x:Name="Root"
              Margin="5,0"
              Opacity="0"
              RenderTransformOrigin="0,0">
            <Grid.RenderTransform>
                <TranslateTransform x:Name="xform" X="-25" />
            </Grid.RenderTransform>
            <VisualStateManager.VisualStateGroups>
                <VisualStateGroup Name="OpenStates">
                    <VisualStateGroup.Transitions>
                        <VisualTransition GeneratedDuration="0" />
                        <VisualTransition GeneratedDuration="0:0:0.2" To="Open">
                            <Storyboard>
                                <DoubleAnimation Duration="0:0:0.2"
                                                 Storyboard.TargetName="xform"
                                                 Storyboard.TargetProperty="X"
                                                 To="0">
                                    <DoubleAnimation.EasingFunction>
                                        <BackEase Amplitude=".3" EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                                <DoubleAnimation Duration="0:0:0.2"
                                                 Storyboard.TargetName="Root"
                                                 Storyboard.TargetProperty="Opacity"
                                                 To="1" />
                            </Storyboard>
                        </VisualTransition>
                    </VisualStateGroup.Transitions>
                    <VisualState x:Name="Closed">
                        <Storyboard>
                            <DoubleAnimation Duration="0"
                                             Storyboard.TargetName="Root"
                                             Storyboard.TargetProperty="Opacity"
                                             To="0" />
                        </Storyboard>
                    </VisualState>
                    <VisualState x:Name="Open">
                        <Storyboard>
                            <DoubleAnimation Duration="0"
                                             Storyboard.TargetName="xform"
                                             Storyboard.TargetProperty="X"
                                             To="0" />
                            <DoubleAnimation Duration="0"
                                             Storyboard.TargetName="Root"
                                             Storyboard.TargetProperty="Opacity"
                                             To="1" />
                        </Storyboard>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateManager.VisualStateGroups>

            <!--<Border Margin="4,4,-4,-4"
                    CornerRadius="5" />
            <Border Margin="3,3,-3,-3"
                    CornerRadius="4" />
            <Border Margin="2,2,-2,-2"
                    CornerRadius="3" />
            <Border Margin="1,1,-1,-1"
                    CornerRadius="2" />-->

            <Border Background="{StaticResource ErrorBackground}" CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" />
            <Border CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" BorderThickness="1" BorderBrush="{StaticResource ErrorBackground}">
                <TextBlock MaxWidth="250"
                           Margin="8,4,8,4"
                           FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                           FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                           FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                           Foreground="{StaticResource OnErrorBackground}"
                           Text="{TemplateBinding Tag}"
                           TextWrapping="Wrap"
                           UseLayoutRounding="false" />
            </Border>
        </Grid>
    </ControlTemplate>

    <!--  DataGridControl's Style  -->
    <Style TargetType="grid:SfDataGrid" x:Key="SyncfusionSfDataGridStyle">
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="AllowRowHoverHighlighting" Value="True"/>
        <Setter Property="HeaderRowHeight" Value="24" />
        <Setter Property="RowHeight" Value="24" />
        <Setter Property="RowHoverHighlightingBrush" Value="{StaticResource TableHoveredBackground}"/>
        <Setter Property="RowSelectionBrush" Value="{StaticResource TableSelectedBackground}"/>
        <Setter Property="SelectionForegroundBrush" Value="{StaticResource TableSelectedForeground}"/>
        <Setter Property="GroupRowSelectionBrush" Value="{StaticResource TableSelectedBackground}"/>
        <Setter Property="CurrentCellBorderBrush" Value="{StaticResource BorderAlt3}"/>
        <Setter Property="GridLinesVisibility" Value="None"/>
        <Setter Property="RowDragDropTemplate">
            <Setter.Value>
                <DataTemplate>
                    <Border x:Name="border" Width="205"
                            Margin="14,0,14,14"
                            Background="{StaticResource ContentBackgroundAlt2}" 
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            BorderBrush="{StaticResource ContentBackgroundAlt2}" Height="45"
                            BorderThickness="0"
                            Effect="{StaticResource Default.ShadowDepth2}">
                        <Grid  VerticalAlignment="Center" 
                          HorizontalAlignment="Left">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Padding="12,0,0,0" 
                                       Foreground="{StaticResource ContentForeground}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                                       Text="{grid:GridLocalizationResourceExtension ResourceName=DraggingRowsCount}" />

                            <TextBlock Text="{Binding DraggingRecords.Count}"
                                       Foreground="{StaticResource ContentForeground}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                                       Grid.Column="1" Margin="-100,0,0,0"/>

                            <Separator  Grid.Row="1" Height="1" BorderBrush="{StaticResource BorderAlt}"
                                        HorizontalAlignment="Stretch"  BorderThickness="1"
                                        VerticalAlignment="Stretch"  Width="250"/>

                            <TextBlock Text="{grid:GridLocalizationResourceExtension ResourceName=DropStatus}" 
                                       Foreground="{StaticResource ContentForeground}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                                       Padding="12,0,0,0"
                                       Grid.Row="2"/>

                            <TextBlock Text="{Binding DragCaption}" 
                                       Foreground="{StaticResource ContentForeground}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                                       Margin="-158,0,0,0"
                                       Grid.Row="2" 
                                       Grid.Column="1"/>
                        </Grid>
                    </Border>
                </DataTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="skinManager:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="RowHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                <Setter Property="HeaderRowHeight" Value="36"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style TargetType="grid:SfDataGrid" BasedOn="{StaticResource SyncfusionSfDataGridStyle}"/>
    
    <!--  Header Cell Style  -->
    <Style TargetType="grid:GridHeaderCellControl" x:Key="SyncfusionGridHeaderCellControlStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Padding" Value="5,3,5,3" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="grid:VisualContainer.WantsMouseInput" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:GridHeaderCellControl">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="HiddenColumnsResizingStates">
                                <VisualState x:Name="PreviousColumnHidden">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_HeaderCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="3, 0, 1, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="HorizontalPreviousColumnHidden">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_HeaderCellBorder" Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="VerticalPreviousColumnHidden">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_HeaderCellBorder" Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="3,0,1,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="NonePreviousColumnHidden">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_HeaderCellBorder" Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="HiddenState">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_HeaderCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="3, 0, 3, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="HorizontalHiddenState">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_HeaderCellBorder" Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="VerticalHiddenState">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_HeaderCellBorder" Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="3,0,3,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="NoneHiddenState">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_HeaderCellBorder" Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="NormalState"/>

                                <VisualState x:Name="LastColumnHidden">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_HeaderCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 0, 3, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="HorizontalLastColumnHidden">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_HeaderCellBorder" Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="VerticalLastColumnHidden">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_HeaderCellBorder" Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,3,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="NoneLastColumnHidden">
                                    <Storyboard>
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_HeaderCellBorder" Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_HeaderCellBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource TableHoveredBackground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_InnerHeadercellBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource BorderAlt}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="AscendingSortDirection" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorHovered}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="DescendingSortDirection" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorHovered}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_FilterToggleButton" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorHovered}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SortNumber" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentForegroundAlt1}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="contentPresenter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentForegroundAlt1}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_HeaderCellBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource TablePressedBackground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_InnerHeadercellBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource BorderAlt}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="AscendingSortDirection" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorSelected}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="DescendingSortDirection" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorSelected}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_FilterToggleButton" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorSelected}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SortNumber" Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource TablePressedForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="contentPresenter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource TablePressedForeground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Normal"/>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="BorderStates">
                                <VisualState x:Name="NormalCell"/>
                                <VisualState x:Name="FrozenColumnCell">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_FrozenCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,1,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="FooterColumnCell">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_FooterCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0"
                                                                     Value="1,0,0,0"/>
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="PART_FrozenCellBorder"
                                Background="Transparent"
                                BorderBrush="{TemplateBinding BorderBrush}"/>
                        <Border x:Name="PART_FooterCellBorder"
                                Background="Transparent"
                                BorderBrush="{TemplateBinding BorderBrush}"/>
                        <Border x:Name="PART_HeaderCellBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                SnapsToDevicePixels="True">
                            <Border x:Name="PART_InnerHeadercellBorder" Background="Transparent" BorderThickness="0">
                                <Grid>
                                    <Line x:Name="PART_DragLine" StrokeThickness="2" Visibility="Collapsed"
                                    Stroke="{StaticResource BorderAlt3}"/> 
                                    <Grid Margin="{TemplateBinding Padding}" SnapsToDevicePixels="True">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <ContentPresenter x:Name="contentPresenter"
                                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                Focusable="False" >
                                            <ContentPresenter.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                            </ContentPresenter.Resources>
                                        </ContentPresenter>
                                        <Border x:Name="PART_FilterPopUpPresenter"  CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" />

                                        <Grid x:Name="PART_SortButtonPresenter"
                                    Grid.Column="1"
                                    SnapsToDevicePixels="True">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="0" MinWidth="{Binding Path=SortDirection, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource sortDirectionToWidthConverter}}" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>

                                            <Path x:Name="AscendingSortDirection"
                                      Height="12"
                                      Width="10"
                                      Stretch="Fill" 
                                      VerticalAlignment="Center" 
                                      HorizontalAlignment="Center"
                                      Fill="{StaticResource IconColor}"
                                      SnapsToDevicePixels="True"
                                      Visibility="{Binding Path=SortDirection,
                                                               RelativeSource={RelativeSource TemplatedParent},
                                                               ConverterParameter=Ascending,
                                                               Converter={StaticResource sortDirectionToVisibilityConverter}}">
                                                <Path.Data>
                                                    <PathGeometry>M8 4.5C7.89844 4.5 7.81055 4.46289 7.73633 4.38867L4.625 1.27734L4.625 10.125C4.625 10.2266 4.58789 10.3145 4.51367 10.3887C4.43945 10.4629 4.35156 10.5 4.25 10.5C4.14844 10.5 4.06055 10.4629 3.98633 10.3887C3.91211 10.3145 3.875 10.2266 3.875 10.125L3.875 1.27734L0.763672 4.38867C0.689453 4.46289 0.601563 4.5 0.5 4.5C0.398438 4.5 0.310547 4.46289 0.236328 4.38867C0.162109 4.31445 0.125 4.22656 0.125 4.125C0.125 4.02344 0.162109 3.93555 0.236328 3.86133L3.98633 0.111328C4.06055 0.0371094 4.14844 4.43944e-09 4.25 0C4.35156 -4.43944e-09 4.43945 0.0371094 4.51367 0.111328L8.26367 3.86133C8.33789 3.93555 8.375 4.02344 8.375 4.125C8.375 4.22656 8.33789 4.31445 8.26367 4.38867C8.18945 4.46289 8.10156 4.5 8 4.5Z</PathGeometry>
                                                </Path.Data>
                                                <Path.RenderTransform>
                                                    <TransformGroup>
                                                        <TransformGroup.Children>
                                                            <RotateTransform Angle="0" />
                                                            <ScaleTransform ScaleX="1" ScaleY="1" />
                                                        </TransformGroup.Children>
                                                    </TransformGroup>
                                                </Path.RenderTransform>
                                            </Path>

                                            <Path x:Name="DescendingSortDirection"
                                      Height="12"
                                      Width="10"
                                      Stretch="Fill" 
                                      VerticalAlignment="Center" 
                                      HorizontalAlignment="Center"
                                      Fill="{StaticResource IconColor}"
                                      SnapsToDevicePixels="True"
                                      Visibility="{Binding Path=SortDirection,
                                                               RelativeSource={RelativeSource TemplatedParent},
                                                               ConverterParameter=Decending,
                                                               Converter={StaticResource sortDirectionToVisibilityConverter}}">
                                                <Path.Data>
                                                    <PathGeometry>M0.5 6C0.601562 6 0.689453 6.03711 0.763672 6.11133L3.875 9.22266L3.875 0.375C3.875 0.273438 3.91211 0.185547 3.98633 0.111328C4.06055 0.0371094 4.14844 1.33183e-08 4.25 0C4.35156 -1.33183e-08 4.43945 0.0371094 4.51367 0.111328C4.58789 0.185547 4.625 0.273437 4.625 0.375L4.625 9.22266L7.73633 6.11133C7.81055 6.03711 7.89844 6 8 6C8.10156 6 8.18945 6.03711 8.26367 6.11133C8.33789 6.18555 8.375 6.27344 8.375 6.375C8.375 6.47656 8.33789 6.56445 8.26367 6.63867L4.51367 10.3887C4.43945 10.4629 4.35156 10.5 4.25 10.5C4.14844 10.5 4.06055 10.4629 3.98633 10.3887L0.236328 6.63867C0.162109 6.56445 0.125 6.47656 0.125 6.375C0.125 6.27344 0.162109 6.18555 0.236328 6.11133C0.310547 6.03711 0.398437 6 0.5 6Z</PathGeometry>
                                                </Path.Data>
                                                <Path.RenderTransform>
                                                    <TransformGroup>
                                                        <TransformGroup.Children>
                                                            <RotateTransform Angle="0" />
                                                            <ScaleTransform ScaleX="1" ScaleY="1" />
                                                        </TransformGroup.Children>
                                                    </TransformGroup>
                                                </Path.RenderTransform>
                                            </Path>

                                            <TextBlock x:Name="SortNumber"
                                           Grid.Column="1"
                                           Margin="0,-4,0,0"
                                           VerticalAlignment="Center"
                                           Foreground="{TemplateBinding Foreground}"
                                           SnapsToDevicePixels="True"
                                           Text="{TemplateBinding SortNumber}"
                                           Visibility="{TemplateBinding SortNumberVisibility}" />
                                        </Grid>

                                        <grid:FilterToggleButton x:Name="PART_FilterToggleButton"
                                                    Grid.Column="2"
                                                    HorizontalAlignment="Stretch"
                                                    VerticalAlignment="Stretch"
                                                    SnapsToDevicePixels="True"
                                                    Background="Transparent"
                                                    Visibility="{TemplateBinding FilterIconVisiblity}" />
                                    </Grid>
                                </Grid>
                            </Border>
                        </Border>

                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:GridHeaderCellControl}" BasedOn="{StaticResource SyncfusionGridHeaderCellControlStyle}"/>

    <Style TargetType="{x:Type grid:FilterToggleButton}" x:Key="SyncfusionDataGridFilterToggleButtonStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Height" Value="16" />
        <Setter Property="Width" Value="16" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Foreground" Value="{StaticResource IconColor}" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type grid:FilterToggleButton}">
                    <Grid SnapsToDevicePixels="True">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_FilterToggleButtonBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_FilterToggleButtonBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="FilterStates">
                                <VisualState x:Name="Filtered">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_FilterToggleButtonIndicator" Storyboard.TargetProperty="Data">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Geometry>M10 4C11.1046 4 12 3.10449 12 2C12 0.895508 11.1046 0 10 0C8.89543 0 8 0.895508 8 2C8 3.10449 8.89543 4 10 4ZM7.85156 6.31055L9.30946 4.91992C9.03473 4.85547 8.77458 4.75293 8.53478 4.61816L7.33008 5.76562C7.14648 5.94141 7.00391 6.14844 6.90234 6.38672C6.80078 6.625 6.75 6.87109 6.75 7.125V10.9219L5.25 9.92578V7.125C5.25 6.87109 5.19922 6.625 5.09766 6.38672C4.99609 6.14844 4.85352 5.94141 4.66992 5.76562L0.867188 2.14453C0.789062 2.07422 0.75 1.98242 0.75 1.86914C0.75 1.76758 0.787109 1.68164 0.861328 1.61133C0.935547 1.53711 1.02344 1.5 1.125 1.5H7.04148C7.08564 1.23633 7.16407 0.985352 7.27202 0.75H1.125C0.96875 0.75 0.822266 0.779297 0.685547 0.837891C0.548828 0.896484 0.429688 0.976562 0.328125 1.07812C0.226562 1.17969 0.146484 1.29883 0.0878906 1.43555C0.0292969 1.57227 0 1.71875 0 1.875C0 2.19531 0.117188 2.4668 0.351562 2.68945L4.14844 6.31055C4.20312 6.36523 4.25195 6.42383 4.29492 6.48633C4.3418 6.54492 4.38086 6.60938 4.41211 6.67969C4.4707 6.82031 4.5 6.96875 4.5 7.125V10.125C4.5 10.2578 4.55664 10.3613 4.66992 10.4355L6.91992 11.9355C6.98242 11.9785 7.05078 12 7.125 12C7.22656 12 7.31445 11.9629 7.38867 11.8887C7.46289 11.8145 7.5 11.7266 7.5 11.625V7.125C7.5 6.96484 7.53125 6.81641 7.59375 6.67969C7.625 6.61328 7.66211 6.54883 7.70508 6.48633C7.74805 6.42383 7.79688 6.36523 7.85156 6.31055Z</Geometry>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="UnFiltered">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_FilterToggleButtonIndicator" Storyboard.TargetProperty="Data">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Geometry>M0 1.875C0 1.71875 0.0292969 1.57227 0.0878906 1.43555C0.146484 1.29883 0.226562 1.17969 0.328125 1.07812C0.429688 0.976562 0.548828 0.896484 0.685547 0.837891C0.822266 0.779297 0.96875 0.75 1.125 0.75H10.875C11.0312 0.75 11.1777 0.779297 11.3145 0.837891C11.4512 0.896484 11.5703 0.976562 11.6719 1.07812C11.7734 1.17969 11.8535 1.29883 11.9121 1.43555C11.9707 1.57227 12 1.71875 12 1.875C12 2.19531 11.8828 2.4668 11.6484 2.68945L7.85156 6.31055C7.79688 6.36523 7.74805 6.42383 7.70508 6.48633C7.66211 6.54883 7.625 6.61328 7.59375 6.67969C7.53125 6.81641 7.5 6.96484 7.5 7.125V11.625C7.5 11.7266 7.46289 11.8145 7.38867 11.8887C7.31445 11.9629 7.22656 12 7.125 12C7.05078 12 6.98242 11.9785 6.91992 11.9355L4.66992 10.4355C4.55664 10.3613 4.5 10.2578 4.5 10.125V7.125C4.5 6.96875 4.4707 6.82031 4.41211 6.67969C4.38086 6.60938 4.3418 6.54492 4.29492 6.48633C4.25195 6.42383 4.20312 6.36523 4.14844 6.31055L0.351562 2.68945C0.117188 2.4668 0 2.19531 0 1.875ZM11.25 1.86914C11.25 1.76758 11.2129 1.68164 11.1387 1.61133C11.0645 1.53711 10.9766 1.5 10.875 1.5H1.125C1.02344 1.5 0.935547 1.53711 0.861328 1.61133C0.787109 1.68164 0.75 1.76758 0.75 1.86914C0.75 1.98242 0.789062 2.07422 0.867188 2.14453L4.66992 5.76562C4.85352 5.94141 4.99609 6.14844 5.09766 6.38672C5.19922 6.625 5.25 6.87109 5.25 7.125V9.92578L6.75 10.9219V7.125C6.75 6.87109 6.80078 6.625 6.90234 6.38672C7.00391 6.14844 7.14648 5.94141 7.33008 5.76562L11.1328 2.14453C11.2109 2.07422 11.25 1.98242 11.25 1.86914Z</Geometry>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border Name="PART_FilterToggleButtonBorder"
                                Width="{TemplateBinding Width}"
                                Height="{TemplateBinding Height}"
                                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                Background="{TemplateBinding Background}"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <Path Name="PART_FilterToggleButtonIndicator"
                                  Height="12"
                                  Width="12"
                                  Margin="2"
                                  Fill="{TemplateBinding Foreground}"
                                  SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                  Stretch="Fill">
                                <Path.Data>
                                    <PathGeometry>M0 1.875C0 1.71875 0.0292969 1.57227 0.0878906 1.43555C0.146484 1.29883 0.226562 1.17969 0.328125 1.07812C0.429688 0.976562 0.548828 0.896484 0.685547 0.837891C0.822266 0.779297 0.96875 0.75 1.125 0.75H10.875C11.0312 0.75 11.1777 0.779297 11.3145 0.837891C11.4512 0.896484 11.5703 0.976562 11.6719 1.07812C11.7734 1.17969 11.8535 1.29883 11.9121 1.43555C11.9707 1.57227 12 1.71875 12 1.875C12 2.19531 11.8828 2.4668 11.6484 2.68945L7.85156 6.31055C7.79688 6.36523 7.74805 6.42383 7.70508 6.48633C7.66211 6.54883 7.625 6.61328 7.59375 6.67969C7.53125 6.81641 7.5 6.96484 7.5 7.125V11.625C7.5 11.7266 7.46289 11.8145 7.38867 11.8887C7.31445 11.9629 7.22656 12 7.125 12C7.05078 12 6.98242 11.9785 6.91992 11.9355L4.66992 10.4355C4.55664 10.3613 4.5 10.2578 4.5 10.125V7.125C4.5 6.96875 4.4707 6.82031 4.41211 6.67969C4.38086 6.60938 4.3418 6.54492 4.29492 6.48633C4.25195 6.42383 4.20312 6.36523 4.14844 6.31055L0.351562 2.68945C0.117188 2.4668 0 2.19531 0 1.875ZM11.25 1.86914C11.25 1.76758 11.2129 1.68164 11.1387 1.61133C11.0645 1.53711 10.9766 1.5 10.875 1.5H1.125C1.02344 1.5 0.935547 1.53711 0.861328 1.61133C0.787109 1.68164 0.75 1.76758 0.75 1.86914C0.75 1.98242 0.789062 2.07422 0.867188 2.14453L4.66992 5.76562C4.85352 5.94141 4.99609 6.14844 5.09766 6.38672C5.19922 6.625 5.25 6.87109 5.25 7.125V9.92578L6.75 10.9219V7.125C6.75 6.87109 6.80078 6.625 6.90234 6.38672C7.00391 6.14844 7.14648 5.94141 7.33008 5.76562L11.1328 2.14453C11.2109 2.07422 11.25 1.98242 11.25 1.86914Z</PathGeometry>
                                </Path.Data>
                            </Path>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:FilterToggleButton}" BasedOn="{StaticResource SyncfusionDataGridFilterToggleButtonStyle}"/>
    
    <!-- StackedHeader Cell Style  -->
    <Style TargetType="grid:GridStackedHeaderCellControl" x:Key="SyncfusionGridStackedHeaderCellControlStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type grid:GridStackedHeaderCellControl}">
                    <Border x:Name="PART_StackedHeaderBorder" 
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid Margin="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" >
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:GridStackedHeaderCellControl}" BasedOn="{StaticResource SyncfusionGridStackedHeaderCellControlStyle}"/>

    <!--Rowheader Cell style-->
    <Style TargetType="grid:GridRowHeaderCell" x:Key="SyncfusionGridRowHeaderCellStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="Padding" Value="0,0,0,0" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:GridRowHeaderCell">
                    <Border x:Name="PART_RowHeaderCellBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">

                        <VisualStateManager.VisualStateGroups>

                            <VisualStateGroup x:Name="BorderStates">
                                <VisualState x:Name="NormalCell"/>
                                <VisualState x:Name="TableSummaryCell">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderCellBorder" Storyboard.TargetProperty="(Border.BorderThickness)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderCellBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <SolidColorBrush Color="Transparent" />
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Fixed_NormalCell">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowHeaderCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 1, 1, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>

                            <VisualStateGroup x:Name="IndicationStates">
                                <VisualState x:Name="Normal" >
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicatorGrid" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Error_CurrentRow">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Data">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Geometry>M0.70800017,0 L4.498001,3.7964015 8.2880024,0 8.9960006,0.70600033 5.2044057,4.5039992 8.9960006,8.3019981 8.2880024,9.0079994 4.498001,5.2115974 0.70800017,9.0079994 0,8.3019981 3.7915958,4.5039992 0,0.70600033 z</Geometry>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>

                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ErrorBackground}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Error">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Data">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Geometry>M0.70800017,0 L4.498001,3.7964015 8.2880024,0 8.9960006,0.70600033 5.2044057,4.5039992 8.9960006,8.3019981 8.2880024,9.0079994 4.498001,5.2115974 0.70800017,9.0079994 0,8.3019981 3.7915958,4.5039992 0,0.70600033 z</Geometry>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Fill">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ErrorBackground}"/>
                                        </ObjectAnimationUsingKeyFrames>                                        
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="CurrentRow">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Data">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Geometry>M20.4287,-6.39488462e-14 L37.8887,17.4599 L20.4287,34.9199 L18.7588,33.25 L33.3613,18.6474 L-1.42108547e-14,18.6474 L-1.42108547e-14,16.2724 L33.3613,16.2724 L18.7588,1.6699 L20.4287,-6.39488462e-14 Z</Geometry>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>

                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Width">
                                            <EasingDoubleKeyFrame KeyTime="0">
                                                <EasingDoubleKeyFrame.Value>
                                                    <system:Double>12</system:Double>
                                                </EasingDoubleKeyFrame.Value>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Height">
                                            <EasingDoubleKeyFrame KeyTime="0">
                                                <EasingDoubleKeyFrame.Value>
                                                    <system:Double>11</system:Double>
                                                </EasingDoubleKeyFrame.Value>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>

                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ToolTip" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>

                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColor}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="EditingRow">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Data">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Geometry>M12.209 1.92773C12.209 2.20898 12.1562 2.48242 12.0508 2.74805C11.9453 3.00977 11.793 3.24023 11.5938 3.43945L4.10547 10.9277C4.05859 10.9746 4.00391 11.0137 3.94141 11.0449C3.88281 11.0762 3.82031 11.1016 3.75391 11.1211L0.853516 11.8477C0.814453 11.8555 0.785156 11.8594 0.765625 11.8594C0.664062 11.8594 0.576172 11.8242 0.501953 11.7539C0.427734 11.6797 0.390625 11.5918 0.390625 11.4902C0.390625 11.4668 0.394531 11.4355 0.402344 11.3965L1.12891 8.49609C1.14844 8.42969 1.17383 8.36719 1.20508 8.30859C1.23633 8.24609 1.27539 8.19141 1.32227 8.14453L8.89258 0.574219C9.07617 0.390625 9.28906 0.25 9.53125 0.152344C9.77734 0.0507812 10.0293 0 10.2871 0C10.5566 0 10.8086 0.0507812 11.043 0.152344C11.2773 0.25 11.4805 0.386719 11.6523 0.5625C11.8281 0.734375 11.9648 0.9375 12.0625 1.17188C12.1602 1.40625 12.209 1.6582 12.209 1.92773ZM11.459 1.95117C11.459 1.7832 11.4297 1.62695 11.3711 1.48242C11.3164 1.33398 11.2363 1.20508 11.1309 1.0957C11.0293 0.986328 10.9062 0.902344 10.7617 0.84375C10.6172 0.78125 10.459 0.75 10.2871 0.75C10.1387 0.75 10.0078 0.771484 9.89453 0.814453C9.78125 0.853516 9.67578 0.908203 9.57812 0.978516C9.48438 1.04883 9.39453 1.12891 9.30859 1.21875C9.22266 1.30859 9.13086 1.40234 9.0332 1.5L10.75 3.2168C10.8477 3.12305 10.9395 3.03125 11.0254 2.94141C11.1113 2.85156 11.1855 2.75781 11.248 2.66016C11.3145 2.5625 11.3652 2.45703 11.4004 2.34375C11.4395 2.23047 11.459 2.09961 11.459 1.95117ZM1.28125 10.9688L3.57227 10.3945L10.2227 3.74414L8.50586 2.02734L1.85547 8.67773L1.28125 10.9688Z</Geometry>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>

                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Width">
                                            <EasingDoubleKeyFrame KeyTime="0">
                                                <EasingDoubleKeyFrame.Value>
                                                    <system:Double>12</system:Double>
                                                </EasingDoubleKeyFrame.Value>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>

                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Height">
                                            <EasingDoubleKeyFrame KeyTime="0">
                                                <EasingDoubleKeyFrame.Value>
                                                    <system:Double>12</system:Double>
                                                </EasingDoubleKeyFrame.Value>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ToolTip" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>

                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColor}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="AddNewRow">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Data">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Geometry>M7.0000048,0 L8.0000048,0 8.0000048,7.0000009 15,7.0000009 15,8.0000008 8.0000048,8.0000008 8.0000048,15 7.0000048,15 7.0000048,8.0000008 0,8.0000008 0,7.0000009 7.0000048,7.0000009 z</Geometry>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>

                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Width">
                                            <EasingDoubleKeyFrame KeyTime="0">
                                                <EasingDoubleKeyFrame.Value>
                                                    <system:Double>12</system:Double>
                                                </EasingDoubleKeyFrame.Value>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Height">
                                            <EasingDoubleKeyFrame KeyTime="0">
                                                <EasingDoubleKeyFrame.Value>
                                                    <system:Double>12</system:Double>
                                                </EasingDoubleKeyFrame.Value>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>

                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ToolTip" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>

                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColor}" />
                                        </ObjectAnimationUsingKeyFrames>

                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="FilterRow">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Data">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Geometry>M0 1.875C0 1.71875 0.0292969 1.57227 0.0878906 1.43555C0.146484 1.29883 0.226562 1.17969 0.328125 1.07812C0.429688 0.976562 0.548828 0.896484 0.685547 0.837891C0.822266 0.779297 0.96875 0.75 1.125 0.75H10.875C11.0312 0.75 11.1777 0.779297 11.3145 0.837891C11.4512 0.896484 11.5703 0.976562 11.6719 1.07812C11.7734 1.17969 11.8535 1.29883 11.9121 1.43555C11.9707 1.57227 12 1.71875 12 1.875C12 2.19531 11.8828 2.4668 11.6484 2.68945L7.85156 6.31055C7.79688 6.36523 7.74805 6.42383 7.70508 6.48633C7.66211 6.54883 7.625 6.61328 7.59375 6.67969C7.53125 6.81641 7.5 6.96484 7.5 7.125V11.625C7.5 11.7266 7.46289 11.8145 7.38867 11.8887C7.31445 11.9629 7.22656 12 7.125 12C7.05078 12 6.98242 11.9785 6.91992 11.9355L4.66992 10.4355C4.55664 10.3613 4.5 10.2578 4.5 10.125V7.125C4.5 6.96875 4.4707 6.82031 4.41211 6.67969C4.38086 6.60938 4.3418 6.54492 4.29492 6.48633C4.25195 6.42383 4.20312 6.36523 4.14844 6.31055L0.351562 2.68945C0.117188 2.4668 0 2.19531 0 1.875ZM11.25 1.86914C11.25 1.76758 11.2129 1.68164 11.1387 1.61133C11.0645 1.53711 10.9766 1.5 10.875 1.5H1.125C1.02344 1.5 0.935547 1.53711 0.861328 1.61133C0.787109 1.68164 0.75 1.76758 0.75 1.86914C0.75 1.98242 0.789062 2.07422 0.867188 2.14453L4.66992 5.76562C4.85352 5.94141 4.99609 6.14844 5.09766 6.38672C5.19922 6.625 5.25 6.87109 5.25 7.125V9.92578L6.75 10.9219V7.125C6.75 6.87109 6.80078 6.625 6.90234 6.38672C7.00391 6.14844 7.14648 5.94141 7.33008 5.76562L11.1328 2.14453C11.2109 2.07422 11.25 1.98242 11.25 1.86914Z</Geometry>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>

                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Width">
                                            <EasingDoubleKeyFrame KeyTime="0">
                                                <EasingDoubleKeyFrame.Value>
                                                    <system:Double>12</system:Double>
                                                </EasingDoubleKeyFrame.Value>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>

                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Height">
                                            <EasingDoubleKeyFrame KeyTime="0">
                                                <EasingDoubleKeyFrame.Value>
                                                    <system:Double>12</system:Double>
                                                </EasingDoubleKeyFrame.Value>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ToolTip" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>

                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RowHeaderIndicator" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColor}" />
                                        </ObjectAnimationUsingKeyFrames>

                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid x:Name="PART_RowHeaderIndicatorGrid" Background="Transparent">
                            <Path x:Name="PART_RowHeaderIndicator"
                              Width="8.146"
                              Height="8.146"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              Fill="{StaticResource IconColor}"
                              Stretch="Fill"/>
                            <ToolTipService.ToolTip>

                                <ToolTip x:Name="PART_ToolTip"
                                         Placement="Left"
                                         PlacementRectangle="20,0,0,0"
                                         Tag="{TemplateBinding RowErrorMessage}"
                                         Template="{StaticResource SyncfusionDataGridValidationToolTipTemplate}" />
                            </ToolTipService.ToolTip>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

    </Style>
    <Style TargetType="grid:GridRowHeaderCell" BasedOn="{StaticResource SyncfusionGridRowHeaderCellStyle}"/>

    <Style TargetType="grid:GridHeaderIndentCell" x:Key="SyncfusionGridHeaderIndentCellStyle">
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:GridHeaderIndentCell">
                    <Border x:Name="PART_GridHeaderIndentCellBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="BorderStates">
                                <VisualState x:Name="TableSummaryRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_GridHeaderIndentCellBorder" Storyboard.TargetProperty="(Border.BorderThickness)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_GridHeaderIndentCellBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <SolidColorBrush Color="Transparent" />
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <ContentPresenter VerticalAlignment="Center" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:GridHeaderIndentCell}" BasedOn="{StaticResource SyncfusionGridHeaderIndentCellStyle}"/>
    
    <!--Rowheader Indent style-->
    <Style TargetType="grid:GridRowHeaderIndentCell" x:Key="SyncfusionGridRowHeaderIndentCellStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="Padding" Value="0,0,0,0" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:GridRowHeaderIndentCell">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}" />

                </ControlTemplate>
            </Setter.Value>
        </Setter>

    </Style>
    <Style TargetType="{x:Type grid:GridRowHeaderIndentCell}" BasedOn="{StaticResource SyncfusionGridRowHeaderIndentCellStyle}"/>
    
    <!--  Row Style  -->
    <Style TargetType="grid:VirtualizingCellsControl" x:Key="SyncfusionVirtualizingCellsControlStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="RowHoverForeground" Value="{StaticResource TableHoveredForeground}"/>
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="CurrentFocusBorderMargin" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:VirtualizingCellsControl">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="BorderStates">
                                <VisualState x:Name="NormalRow" />
                                <VisualState x:Name="FrozenRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 0, 0, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="FooterRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">

                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 1, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="Margin">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, -1, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="PART_RowBorder"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        </Border>
                        <Rectangle Clip="{TemplateBinding RowBackgroundClip}" Fill="{TemplateBinding Background}" />
                        <Border Background="{TemplateBinding RowSelectionBrush}"
                                Clip="{TemplateBinding SelectionBorderClipRect}"
                                Visibility="{TemplateBinding SelectionBorderVisiblity}" />
                        <Border Background="{TemplateBinding RowHoverBackgroundBrush}"
                                BorderBrush="{TemplateBinding RowHoverBackgroundBrush}"
                                BorderThickness="{TemplateBinding RowHighlightBorderThickness}"
                                Clip="{TemplateBinding HighlightBorderClipRect}"
                                SnapsToDevicePixels="True"
                                Visibility="{TemplateBinding HighlightSelectionBorderVisiblity}" />
                        <Rectangle x:Name="PART_CurrentFocusRow"
                                   Margin="{TemplateBinding CurrentFocusBorderMargin}"
                                   Stroke="{StaticResource BorderAlt1}"
                                   StrokeThickness="1"
                                   Visibility="{TemplateBinding CurrentFocusRowVisibility}" >
                            <Rectangle.StrokeDashArray>
                                <DoubleCollection>2,2</DoubleCollection>
                            </Rectangle.StrokeDashArray>
                        </Rectangle>
                        <Border BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter>
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                        <Grid x:Name="PART_DragGrid" Height="5" VerticalAlignment="Bottom" Visibility="Collapsed" Margin="1,0,1.5,0">
                            <Line x:Name="PART_DragLeftVerticalLine" HorizontalAlignment="Left" VerticalAlignment="Center" StrokeThickness="1"
                          Y2="5" Stroke="{StaticResource BorderAlt3}"/>
                            <Border x:Name="PART_DragBorder" HorizontalAlignment="Stretch" VerticalAlignment="Center" BorderThickness="0,0,0,1.5"
                            BorderBrush="{StaticResource BorderAlt3}" />
                            <Line x:Name="PART_DragRightVerticalLine" HorizontalAlignment="Right" VerticalAlignment="Center" StrokeThickness="1" 
                           Y2="5"  Stroke="{StaticResource BorderAlt3}"/>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:VirtualizingCellsControl}" BasedOn="{StaticResource SyncfusionVirtualizingCellsControlStyle}"/>

    <!--  Header Row Style  -->
    <Style TargetType="{x:Type grid:HeaderRowControl}" x:Key="SyncfusionHeaderRowControlStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Gray" />
        <Setter Property="BorderThickness" Value="0,0,0,0" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type grid:HeaderRowControl}">
                    <Grid>
                        <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
                            <ContentPresenter />
                        </Border>
                        <Grid x:Name="PART_DragGrid" Height="5" VerticalAlignment="Bottom" Visibility="Collapsed" Margin="1,0,1.5,0">
                            <Line x:Name="PART_DragLeftVerticalLine" HorizontalAlignment="Left" VerticalAlignment="Center" StrokeThickness="1"
                          Y2="5" Stroke="{StaticResource BorderAlt3}"/>
                            <Border x:Name="PART_DragBorder" HorizontalAlignment="Stretch" VerticalAlignment="Center" BorderThickness="0,0,0,1.5" 
                            BorderBrush="{StaticResource BorderAlt3}" />
                            <Line x:Name="PART_DragRightVerticalLine" HorizontalAlignment="Right" VerticalAlignment="Center" StrokeThickness="1"
                           Y2="5"  Stroke="{StaticResource BorderAlt3}"/>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionHeaderRowControlStyle}" TargetType="{x:Type grid:HeaderRowControl}" />

    <!-- UnBound Row Style -->
    <Style TargetType="grid:UnBoundRowControl" x:Key="SyncfusionUnBoundRowControlStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:UnBoundRowControl">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="BorderStates">
                                <VisualState x:Name="NormalRow" />
                                <VisualState x:Name="FrozenRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 0, 0, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="FooterRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">

                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 1, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="Margin">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, -1, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="PART_RowBorder"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}" />
                        <Rectangle Clip="{TemplateBinding RowBackgroundClip}" Fill="{TemplateBinding Background}" />
                        <Rectangle x:Name="PART_CurrentFocusRow"
                                   Margin="{TemplateBinding CurrentFocusBorderMargin}"
                                   Stroke="{StaticResource BorderAlt1}"
                                   StrokeThickness="1"
                                   Visibility="{TemplateBinding CurrentFocusRowVisibility}" >
                            <Rectangle.StrokeDashArray>
                                <DoubleCollection>2,2</DoubleCollection>
                            </Rectangle.StrokeDashArray>
                        </Rectangle>
                        <Border Background="{TemplateBinding RowSelectionBrush}"
                                Clip="{TemplateBinding SelectionBorderClipRect}"
                                Visibility="{TemplateBinding SelectionBorderVisiblity}" />
                        <Border Background="{TemplateBinding RowHoverBackgroundBrush}"
                                BorderBrush="{TemplateBinding RowHoverBackgroundBrush}"
                                BorderThickness="{TemplateBinding RowHighlightBorderThickness}"
                                Clip="{TemplateBinding HighlightBorderClipRect}"
                                SnapsToDevicePixels="True"
                                Visibility="{TemplateBinding HighlightSelectionBorderVisiblity}" />
                        <Border BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter>
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                        <Grid x:Name="PART_DragGrid" Height="5" VerticalAlignment="Bottom" Visibility="Collapsed" Margin="1,0,1.5,0">
                            <Line x:Name="PART_DragLeftVerticalLine" HorizontalAlignment="Left" VerticalAlignment="Center" StrokeThickness="1"
                          Y2="5" Stroke="{StaticResource BorderAlt3}"/>
                            <Border x:Name="PART_DragBorder" HorizontalAlignment="Stretch" VerticalAlignment="Center" BorderThickness="0,0,0,1.5"
                            BorderBrush="{StaticResource BorderAlt3}" />
                            <Line x:Name="PART_DragRightVerticalLine" HorizontalAlignment="Right" VerticalAlignment="Center" StrokeThickness="1"
                           Y2="5"  Stroke="{StaticResource BorderAlt3}"/>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:UnBoundRowControl}" BasedOn="{StaticResource SyncfusionUnBoundRowControlStyle}"/>
    
    <!-- Filter Row Style -->
    <Style TargetType="gridRowFilter:FilterRowControl" x:Key="SyncfusionFilterRowControlStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="gridRowFilter:FilterRowControl">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="BorderStates">
                                <VisualState x:Name="NormalRow" />
                                <VisualState x:Name="FrozenRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 0, 0, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="FooterRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">

                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 1, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="Margin">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, -1, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="PART_RowBorder"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        </Border>
                        <Rectangle Clip="{TemplateBinding RowBackgroundClip}" Fill="{TemplateBinding Background}" />
                        <Rectangle x:Name="PART_CurrentFocusRow"
                                   Margin="{TemplateBinding CurrentFocusBorderMargin}"
                                   Stroke="{StaticResource BorderAlt1}"
                                   StrokeThickness="1"
                                   Visibility="{TemplateBinding CurrentFocusRowVisibility}" >
                            <Rectangle.StrokeDashArray>
                                <DoubleCollection>2,2</DoubleCollection>
                            </Rectangle.StrokeDashArray>
                        </Rectangle>
                        <Border Background="{TemplateBinding RowHoverBackgroundBrush}"
                                BorderBrush="{TemplateBinding RowHoverBackgroundBrush}"
                                BorderThickness="{TemplateBinding RowHighlightBorderThickness}"
                                Clip="{TemplateBinding HighlightBorderClipRect}"
                                SnapsToDevicePixels="True"
                                Visibility="{TemplateBinding HighlightSelectionBorderVisiblity}" />
                        <Border Background="{TemplateBinding RowSelectionBrush}"
                                Clip="{TemplateBinding SelectionBorderClipRect}"
                                Visibility="{TemplateBinding SelectionBorderVisiblity}" />
                        <Border BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter>
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                        <Grid x:Name="PART_DragGrid" Height="5" VerticalAlignment="Bottom" Visibility="Collapsed" Margin="1,0,1.5,0">
                            <Line x:Name="PART_DragLeftVerticalLine" HorizontalAlignment="Left" VerticalAlignment="Center" StrokeThickness="1"
                          Y2="5" Stroke="{StaticResource BorderAlt3}"/>
                            <Border x:Name="PART_DragBorder" HorizontalAlignment="Stretch" VerticalAlignment="Center" BorderThickness="0,0,0,1.5"
                            BorderBrush="{StaticResource BorderAlt3}" />
                            <Line x:Name="PART_DragRightVerticalLine" HorizontalAlignment="Right" VerticalAlignment="Center" StrokeThickness="1"
                           Y2="5"  Stroke="{StaticResource BorderAlt3}"/>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type gridRowFilter:FilterRowControl}" BasedOn="{StaticResource SyncfusionFilterRowControlStyle}"/>
    
    <!--  TableSummaryRow Control Row Style  -->
    <Style TargetType="grid:TableSummaryRowControl" x:Key="SyncfusionTableSummaryRowControlStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="1,0,0,1" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightMedium}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Panel.ZIndex" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:TableSummaryRowControl">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="BorderStates">
                                <VisualState x:Name="FooterRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="1, 1, 0, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="Margin">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="-1, -1, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="NormalRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="Margin">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="-1, 0, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="HorizontalRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_RowBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="1, 1, 0, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_RowBorder"
                                                                       Storyboard.TargetProperty="Margin">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="-1, -1, 0, -1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="VerticalRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_RowBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="1, 1, 0, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_RowBorder"
                                                                       Storyboard.TargetProperty="Margin">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="-1, -1, 0, -1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="NoneRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_RowBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 0, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="PART_RowBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
                            <ContentPresenter>
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                        <Grid x:Name="PART_DragGrid" Height="5" VerticalAlignment="Bottom" Visibility="Collapsed" Margin="1,0,1.5,0">
                            <Line x:Name="PART_DragLeftVerticalLine" HorizontalAlignment="Left" VerticalAlignment="Center" StrokeThickness="1"
                          Y2="5" Stroke="{StaticResource BorderAlt3}"/>
                            <Border x:Name="PART_DragBorder" HorizontalAlignment="Stretch" VerticalAlignment="Center" BorderThickness="0,0,0,1.5"
                            BorderBrush="{StaticResource BorderAlt3}" />
                            <Line x:Name="PART_DragRightVerticalLine" HorizontalAlignment="Right" VerticalAlignment="Center" StrokeThickness="1"
                           Y2="5"  Stroke="{StaticResource BorderAlt3}"/>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:TableSummaryRowControl}" BasedOn="{StaticResource SyncfusionTableSummaryRowControlStyle}"/>

    <!--  Grid Cell Style  -->
    <Style x:Key="SyncfusionGridCellStyle" TargetType="grid:GridCell">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:GridCell">
                    <Grid SnapsToDevicePixels="True">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="IndicationStates">
                                <VisualState x:Name="NoError">
                                    <Storyboard BeginTime="0">
                                        <!--<DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_InValidCellBorder" Storyboard.TargetProperty="Width">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                                            <EasingDoubleKeyFrame KeyTime="0" Value="0" />
                                        </DoubleAnimationUsingKeyFrames>-->
                                        <ObjectAnimationUsingKeyFrames BeginTime="00:00:00"
                                                                       Storyboard.TargetName="PART_InValidCellBorder"
                                                                       Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="00:00:00" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="HasError">
                                    <Storyboard>
                                        <!--<DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_InValidCellBorder" Storyboard.TargetProperty="Width">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="0" />
                                            <EasingDoubleKeyFrame KeyTime="0" Value="10" />
                                        </DoubleAnimationUsingKeyFrames>-->
                                        <ObjectAnimationUsingKeyFrames BeginTime="00:00:00"
                                                                       Storyboard.TargetName="PART_InValidCellBorder"
                                                                       Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="00:00:00" Value="{x:Static Visibility.Visible}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>

                            </VisualStateGroup>
                            <VisualStateGroup x:Name="BorderStates">
                                <VisualState x:Name="NormalCell" />
                                <VisualState x:Name="FrozenColumnCell">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_FrozenCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,1,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="FooterColumnCell">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_FooterCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="1,0,0,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border Background="{TemplateBinding CellSelectionBrush}"
                                SnapsToDevicePixels="True"
                                Visibility="{TemplateBinding SelectionBorderVisibility}" />
                        <Border x:Name="PART_FooterCellBorder"
                                Background="Transparent"
                                BorderBrush="{TemplateBinding BorderBrush}"/>
                        <Border x:Name="PART_FrozenCellBorder"
                                Background="Transparent"
                                BorderBrush="{TemplateBinding BorderBrush}"/>
                        <Border x:Name="PART_GridCellBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                SnapsToDevicePixels="True">
                            <ContentPresenter Margin="{TemplateBinding Padding}" Opacity="{TemplateBinding AnimationOpacity}" >
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>

                        <Border Margin="-1,0,0,0"
                                Background="Transparent"
                                BorderBrush="{TemplateBinding CurrentCellBorderBrush}"
                                BorderThickness="{TemplateBinding CurrentCellBorderThickness}"
                                IsHitTestVisible="False"
                                SnapsToDevicePixels="True"
                                Visibility="{TemplateBinding CurrentCellBorderVisibility}" >
                            <Border x:Name="PART_CurrentCellInnerBorder" BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" BorderBrush="{StaticResource BorderAlt3}" Visibility="Collapsed"/>
                        </Border>
                        <Border x:Name="PART_InValidCellBorder"
                                Width="10"
                                Height="10"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Top"
                                SnapsToDevicePixels="True"
                                Visibility="Collapsed">
                            <ToolTipService.ToolTip>
                                <ToolTip Placement="Right"
                                         PlacementRectangle="20,0,0,0"
                                         Tag="{TemplateBinding ErrorMessage}"
                                         Template="{StaticResource SyncfusionDataGridValidationToolTipTemplate}" />
                            </ToolTipService.ToolTip>
                            <Path Data="M0.5,0.5 L12.652698,0.5 12.652698,12.068006 z"
                                  Fill="{StaticResource ErrorBackground}"
                                  SnapsToDevicePixels="True"
                                  Stretch="Fill" />
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Resources>
            <Style TargetType="{x:Type TextBlock}"/>
        </Style.Resources>
    </Style>
    <Style TargetType="{x:Type grid:GridCell}" BasedOn="{StaticResource SyncfusionGridCellStyle}"/>

    <!--Grid UnBound Cell Style-->
    <Style TargetType="grid:GridUnBoundRowCell" x:Key="SyncfusionGridUnBoundRowCellStyle">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Padding" Value="0,0,0,0" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="IsTabStop" Value="False" />
    </Style>
    <Style TargetType="{x:Type grid:GridUnBoundRowCell}" BasedOn="{StaticResource SyncfusionGridUnBoundRowCellStyle}"/>
    
    <!--  Group Caption Row Style  -->
    <Style TargetType="grid:CaptionSummaryRowControl" x:Key="SyncfusionCaptionSummaryRowControlStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type grid:CaptionSummaryRowControl}">
                    <Grid SnapsToDevicePixels="True">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="BorderStates">
                                <VisualState x:Name="NormalRow" />
                                <VisualState x:Name="FixedCaption">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_CaptionSummaryRowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 1, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="FrozenRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 0, 0, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="FooterRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 1, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_RowBorder"
                                                                          Storyboard.TargetProperty="Margin">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, -1, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="PART_RowBorder"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}" />
                        <Rectangle x:Name="PART_CurrentFocusRow"
                                   Margin="{TemplateBinding CurrentFocusBorderMargin}"
                                   Stroke="{StaticResource BorderAlt1}"
                                   StrokeThickness="1"
                                   Visibility="{TemplateBinding CurrentFocusRowVisibility}" >
                            <Rectangle.StrokeDashArray>
                                <DoubleCollection>2,2</DoubleCollection>
                            </Rectangle.StrokeDashArray>
                        </Rectangle>
                        <Border Background="{TemplateBinding GroupRowSelectionBrush}"
                                Clip="{TemplateBinding SelectionBorderClipRect}"
                                SnapsToDevicePixels="True"
                                Visibility="{TemplateBinding SelectionBorderVisiblity}" />

                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                SnapsToDevicePixels="True">
                            <Grid SnapsToDevicePixels="True">
                                <ContentPresenter VerticalAlignment="Center"
                                                  Content="{TemplateBinding Content}"
                                                  SnapsToDevicePixels="True" />
                                <Grid x:Name="PART_CaptionSummaryRowGrid" IsHitTestVisible="False">
                                    <Border x:Name="PART_CaptionSummaryRowBorder"
                                            HorizontalAlignment="Left"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="0"
                                            SnapsToDevicePixels="True">
                                        <grid:GridExpanderCellControl x:Name="PART_ExpanderCell" Width="8"
                                                                       Height="8"
                                                                       HorizontalAlignment="Center"
                                                                       VerticalAlignment="Center"
                                                                       IsExpanded="{TemplateBinding IsExpanded}"
                                                                       SnapsToDevicePixels="True" />
                                    </Border>
                                </Grid>
                            </Grid>
                        </Border>
                        <Grid x:Name="PART_DragGrid" Height="5" VerticalAlignment="Bottom" Visibility="Collapsed" Margin="1,0,1.5,0">
                            <Line x:Name="PART_DragLeftVerticalLine" HorizontalAlignment="Left" VerticalAlignment="Center" StrokeThickness="1"
                          Y2="5" Stroke="{StaticResource BorderAlt3}"/>
                            <Border x:Name="PART_DragBorder" HorizontalAlignment="Stretch" VerticalAlignment="Center" BorderThickness="0,0,0,1.5"
                            BorderBrush="{StaticResource BorderAlt3}" />
                            <Line x:Name="PART_DragRightVerticalLine" HorizontalAlignment="Right" VerticalAlignment="Center" StrokeThickness="1"
                           Y2="5"  Stroke="{StaticResource BorderAlt3}"/>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:CaptionSummaryRowControl}" BasedOn="{StaticResource SyncfusionCaptionSummaryRowControlStyle}"/>

    <!--GridIndent style-->
    <Style TargetType="grid:GridIndentCell" x:Key="SyncfusionGridIndentCellStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,1,0" />
        <Setter Property="ForceCursor" Value="False" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:GridIndentCell">
                    <Border x:Name="PART_GridIndentCellBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="BorderStates">
                                <VisualState x:Name="DataRow" />
                                <VisualState x:Name="SummaryRow" />
                                <VisualState x:Name="UnBoundRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_GridIndentCellBorder" Storyboard.TargetProperty="(Border.BorderThickness)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,1,1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="FilterRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_GridIndentCellBorder" Storyboard.TargetProperty="(Border.BorderThickness)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,1,1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="AddNewRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_GridIndentCellBorder" Storyboard.TargetProperty="(Border.BorderThickness)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,1,1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="TableSummaryRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_GridIndentCellBorder" Storyboard.TargetProperty="(Border.BorderThickness)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Expander_Collapsed">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_GridIndentCellBorder" Storyboard.TargetProperty="(Border.BorderThickness)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="After_Expander">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_GridIndentCellBorder" Storyboard.TargetProperty="(Border.BorderThickness)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Before_Expander">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_GridIndentCellBorder" Storyboard.TargetProperty="(Border.BorderThickness)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,1,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Expander_Expanded">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_GridIndentCellBorder" Storyboard.TargetProperty="(Border.BorderThickness)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Last_GroupRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="PART_GridIndentCellBorder" Storyboard.TargetProperty="(Border.BorderThickness)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0,0,1,1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Fixed_NormalCell">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_GridIndentCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 1, 0, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="HorizontalBorder">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_GridIndentCellBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 0, 0, 1" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="VerticalBorder">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_GridIndentCellBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 0, 1, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="NoneBorder">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_GridIndentCellBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 0, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <ContentPresenter VerticalAlignment="Center" SnapsToDevicePixels="True" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:GridIndentCell}" BasedOn="{StaticResource SyncfusionGridIndentCellStyle}"/>
    
    <!--  Group Summary Row control  -->
    <Style TargetType="grid:GroupSummaryRowControl" x:Key="SyncfusionGroupSummaryRowControlStyle">
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.SubTitleTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type grid:GroupSummaryRowControl}">
                        <Grid>
                            <VisualStateManager.VisualStateGroups>
                                <VisualStateGroup x:Name="BorderStates">
                                    <VisualState x:Name="NormalRow" />
                                    <VisualState x:Name="FrozenRow">
                                        <Storyboard BeginTime="0">
                                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_GroupSummaryRowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                                <EasingThicknessKeyFrame KeyTime="0" Value="0, 0, 0, 1" />
                                            </ThicknessAnimationUsingKeyFrames>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="FooterRow">
                                        <Storyboard BeginTime="0">
                                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_GroupSummaryRowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                                <EasingThicknessKeyFrame KeyTime="0" Value="0, 1, 0, 0" />
                                            </ThicknessAnimationUsingKeyFrames>
                                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_GroupSummaryRowBorder"
                                                                          Storyboard.TargetProperty="Margin">
                                                <EasingThicknessKeyFrame KeyTime="0" Value="0, -1, 0, 0" />
                                            </ThicknessAnimationUsingKeyFrames>
                                        </Storyboard>
                                    </VisualState>
                                </VisualStateGroup>
                            </VisualStateManager.VisualStateGroups>
                        <Border x:Name="PART_GroupSummaryRowBorder"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}" />
                        <Border Background="{TemplateBinding GroupRowSelectionBrush}"
                                Clip="{TemplateBinding SelectionBorderClipRect}"
                                SnapsToDevicePixels="True"
                                Visibility="{TemplateBinding SelectionBorderVisiblity}" />
                            <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="{TemplateBinding Padding}"
                                SnapsToDevicePixels="True">
                            <ContentPresenter SnapsToDevicePixels="True">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                        <Grid x:Name="PART_DragGrid" Height="5" VerticalAlignment="Bottom" Visibility="Collapsed" Margin="1,0,1.5,0">
                            <Line x:Name="PART_DragLeftVerticalLine" HorizontalAlignment="Left" VerticalAlignment="Center" StrokeThickness="1"
                          Y2="5" Stroke="{StaticResource BorderAlt3}"/>
                            <Border x:Name="PART_DragBorder" HorizontalAlignment="Stretch" VerticalAlignment="Center" BorderThickness="0,0,0,1.5"
                            BorderBrush="{StaticResource BorderAlt3}" />
                            <Line x:Name="PART_DragRightVerticalLine" HorizontalAlignment="Right" VerticalAlignment="Center" StrokeThickness="1"
                           Y2="5"  Stroke="{StaticResource BorderAlt3}"/>
                        </Grid>
                        <Rectangle x:Name="PART_CurrentFocusRow"
                                   Margin="{TemplateBinding CurrentFocusBorderMargin}"
                                   Stroke="{StaticResource BorderAlt1}"
                                   StrokeThickness="1"
                                   Visibility="{TemplateBinding CurrentFocusRowVisibility}" >
                            <Rectangle.StrokeDashArray>
                                <DoubleCollection>2,2</DoubleCollection>
                            </Rectangle.StrokeDashArray>
                        </Rectangle>

                    </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
    </Style>
    <Style TargetType="{x:Type grid:GroupSummaryRowControl}" BasedOn="{StaticResource SyncfusionGroupSummaryRowControlStyle}"/>
    
    <!--  Grid Group Summary Cell Style  -->
    <ControlTemplate x:Key="SyncfusionGridGroupSummaryCellControlTemplate" TargetType="{x:Type grid:GridGroupSummaryCell}">
        <Border x:Name="PART_GridGroupSummaryCellBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
            <VisualStateManager.VisualStateGroups>
                <VisualStateGroup x:Name="BorderStates">
                    <VisualState x:Name="NormalCell">
                    </VisualState>
                    <VisualState x:Name="LastColumnCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_GridGroupSummaryCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0, 0, 1, 1" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                    <VisualState x:Name="HorizontalLastColumnCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_GridGroupSummaryCellBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,1" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                    <VisualState x:Name="VerticalLastColumnCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_GridGroupSummaryCellBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0,0,1,0" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                    <VisualState x:Name="NoneLastColumnCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_GridGroupSummaryCellBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,0" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateManager.VisualStateGroups>
            <ContentPresenter Margin="{TemplateBinding Padding}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="True">
                <ContentPresenter.Resources>
                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                </ContentPresenter.Resources>
            </ContentPresenter>
        </Border>
    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionGridGroupSummaryCellDrawingControlTemplate" TargetType="{x:Type grid:GridGroupSummaryCell}">
        <Border Background="Transparent">
            <ContentPresenter Margin="{TemplateBinding Padding}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="True">
                <ContentPresenter.Resources>
                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                </ContentPresenter.Resources>
            </ContentPresenter>
        </Border>
    </ControlTemplate>

    <Style TargetType="{x:Type grid:GridGroupSummaryCell}" x:Key="SyncfusionGridGroupSummaryCellStyle">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,0,1" />
        <Setter Property="Padding" Value="3,0,3,0" />
        <Setter Property="IsTabStop" Value="False" />
        <Style.Triggers>
            <Trigger Property="UseDrawing" Value="True">
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Template" Value="{StaticResource SyncfusionGridGroupSummaryCellDrawingControlTemplate}"/>
            </Trigger>
            <Trigger Property="UseDrawing" Value="False">
                <Setter Property="Template" Value="{StaticResource SyncfusionGridGroupSummaryCellControlTemplate}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style TargetType="{x:Type grid:GridGroupSummaryCell}" BasedOn="{StaticResource SyncfusionGridGroupSummaryCellStyle}"/>

    <!--  GridCaptionSummaryCell Style  -->
    <ControlTemplate x:Key="SyncfusionGridCaptionSummaryCellControlTemplate" TargetType="{x:Type grid:GridCaptionSummaryCell}">
        <Border x:Name="PART_GridCaptionSummaryCellBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
            <VisualStateManager.VisualStateGroups>
                <VisualStateGroup x:Name="BorderStates">
                    <VisualState x:Name="NormalCell"/>
                    <VisualState x:Name="LastColumnCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_GridCaptionSummaryCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0, 0, 1, 1" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                    <VisualState x:Name="HorizontalLastColumnCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_GridCaptionSummaryCellBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,1" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                    <VisualState x:Name="VerticalLastColumnCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_GridCaptionSummaryCellBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0,0,1,0" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                    <VisualState x:Name="NoneLastColumnCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_GridCaptionSummaryCellBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,0" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                    <VisualState x:Name="Fixed_NormalCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_GridCaptionSummaryCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0, 1, 0, 1" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                    <VisualState x:Name="Fixed_LastCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_GridCaptionSummaryCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0, 1, 1, 1" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateManager.VisualStateGroups>
            <ContentPresenter Margin="{TemplateBinding Padding}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="True">
                <ContentPresenter.Resources>
                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                </ContentPresenter.Resources>
            </ContentPresenter>
        </Border>
    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionGridCaptionSummaryCellDrawingControlTemplate" TargetType="{x:Type grid:GridCaptionSummaryCell}">
        <Border Background="Transparent">
            <ContentPresenter Margin="{TemplateBinding Padding}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="True">
                <ContentPresenter.Resources>
                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                </ContentPresenter.Resources>
            </ContentPresenter>
        </Border>
    </ControlTemplate>

    <Style TargetType="{x:Type grid:GridCaptionSummaryCell}" x:Key="SyncfusionGridCaptionSummaryCellStyle">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,0,1" />
        <Setter Property="Padding" Value="3,0,3,0" />
        <Setter Property="IsTabStop" Value="False" />
        <Style.Triggers>
            <Trigger Property="UseDrawing" Value="True">
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Template" Value="{StaticResource SyncfusionGridCaptionSummaryCellDrawingControlTemplate}"/>
            </Trigger>
            <Trigger Property="UseDrawing" Value="False">
                <Setter Property="Template" Value="{StaticResource SyncfusionGridCaptionSummaryCellControlTemplate}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style TargetType="{x:Type grid:GridCaptionSummaryCell}" BasedOn="{StaticResource SyncfusionGridCaptionSummaryCellStyle}"/>
    
    <!--  Table Summary Cell Style  -->
    <ControlTemplate x:Key="SyncfusionGridTableSummaryCellControlTemplate" TargetType="{x:Type grid:GridTableSummaryCell}">
        <Border x:Name="PART_GridTableSummaryCellBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
            <VisualStateManager.VisualStateGroups>
                <VisualStateGroup x:Name="BorderStates">
                    <VisualState x:Name="NormalCell"/>
                    <VisualState x:Name="LastColumnCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_GridTableSummaryCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0,0,1,0" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                    <VisualState x:Name="HorizontalLastColumnCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_GridTableSummaryCellBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,0" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                    <VisualState x:Name="VerticalLastColumnCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_GridTableSummaryCellBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0,0,1,0" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                    <VisualState x:Name="NoneLastColumnCell">
                        <Storyboard BeginTime="0">
                            <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                       Duration="1"
                                                                       Storyboard.TargetName="PART_GridTableSummaryCellBorder"
                                                                       Storyboard.TargetProperty="BorderThickness">
                                <EasingThicknessKeyFrame KeyTime="0" Value="0,0,0,0" />
                            </ThicknessAnimationUsingKeyFrames>
                        </Storyboard>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateManager.VisualStateGroups>
            <ContentPresenter Margin="{TemplateBinding Padding}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="True">
                <ContentPresenter.Resources>
                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                </ContentPresenter.Resources>
            </ContentPresenter>
        </Border>
    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionGridTableSummaryCellDrawingControlTemplate" TargetType="{x:Type grid:GridTableSummaryCell}">
        <ContentPresenter Margin="{TemplateBinding Padding}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="True">
            <ContentPresenter.Resources>
                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
            </ContentPresenter.Resources>
        </ContentPresenter>
    </ControlTemplate>

    <Style TargetType="{x:Type grid:GridTableSummaryCell}" x:Key="SyncfusionGridTableSummaryCellStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Padding" Value="3,0,3,0" />
        <Setter Property="IsTabStop" Value="False" />
        <Style.Triggers>
            <Trigger Property="UseDrawing" Value="True">
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Template" Value="{StaticResource SyncfusionGridTableSummaryCellDrawingControlTemplate}"/>
            </Trigger>
            <Trigger Property="UseDrawing" Value="False">
                <Setter Property="Template" Value="{StaticResource SyncfusionGridTableSummaryCellDrawingControlTemplate}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style TargetType="{x:Type grid:GridTableSummaryCell}" BasedOn="{StaticResource SyncfusionGridTableSummaryCellStyle}"/>

    <!--  Add new row -->
    <Style TargetType="grid:AddNewRowControl" x:Key="SyncfusionAddNewRowControlStyle">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.SubTitleTextStyle}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:AddNewRowControl">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="AddNewRowStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="Edit">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_AddNewRowTextBorder" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="BorderStates">
                                <VisualState x:Name="NormalRow" />
                                <VisualState x:Name="FooterRow">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_AddNewRowBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, 1, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_AddNewRowBorder"
                                                                          Storyboard.TargetProperty="Margin">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="0, -1, 0, 0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Rectangle x:Name="PART_CurrentFocusRow"
                                   Margin="{TemplateBinding CurrentFocusBorderMargin}"
                                   Stroke="{StaticResource BorderAlt1}"
                                   StrokeThickness="1"
                                   Visibility="{TemplateBinding CurrentFocusRowVisibility}" >
                            <Rectangle.StrokeDashArray>
                                <DoubleCollection>2,2</DoubleCollection>
                            </Rectangle.StrokeDashArray>
                        </Rectangle>
                        <Border Background="{TemplateBinding RowSelectionBrush}"
                                Clip="{TemplateBinding SelectionBorderClipRect}"
                                Visibility="{TemplateBinding SelectionBorderVisiblity}" />
                        <Border x:Name="PART_AddNewRowBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter >
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                        <Border x:Name="PART_AddNewRowTextBorder"
                                Background="{StaticResource ContentBackgroundAlt6}"
                                BorderBrush="Transparent"
                                BorderThickness="0,0,1,1"
                                Clip="{TemplateBinding TextBorderClip}"
                                IsHitTestVisible="False">
                            <ContentPresenter Margin="{TemplateBinding TextMargin}"
                                              HorizontalAlignment="Left"
                                              VerticalAlignment="Center"
                                              Content="{TemplateBinding AddNewRowText}" >
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                        <Grid x:Name="PART_DragGrid" Height="5" VerticalAlignment="Bottom" Visibility="Collapsed" Margin="1,0,1.5,0">
                            <Line x:Name="PART_DragLeftVerticalLine" HorizontalAlignment="Left" VerticalAlignment="Center" StrokeThickness="1"
                          Y2="5" Stroke="{StaticResource BorderAlt3}"/>
                            <Border x:Name="PART_DragBorder" HorizontalAlignment="Stretch" VerticalAlignment="Center" BorderThickness="0,0,0,1.5"
                            BorderBrush="{StaticResource BorderAlt3}" />
                            <Line x:Name="PART_DragRightVerticalLine" HorizontalAlignment="Right" VerticalAlignment="Center" StrokeThickness="1"
                           Y2="5"  Stroke="{StaticResource BorderAlt3}"/>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:AddNewRowControl}" BasedOn="{StaticResource SyncfusionAddNewRowControlStyle}"/>

    <Style x:Key="FilterOptionButtonStyle" TargetType="ToggleButton" BasedOn="{StaticResource WPFGlyphEditableDropdownExpanderStyle}">
        <Setter Property="ClickMode" Value="Release"/>
        <Setter Property="Focusable" Value="True"/>
    </Style>

    <!--  Grid FilterRow Cell Style  -->
    <Style TargetType="gridRowFilter:GridFilterRowCell" x:Key="SyncfusionGridFilterRowCellStyle">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="Padding">
            <Setter.Value>
                <Thickness>0</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="gridRowFilter:GridFilterRowCell">
                    <Grid SnapsToDevicePixels="True">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="BorderStates">
                                <VisualState x:Name="NormalCell"/>
                                <VisualState x:Name="FrozenColumnCell">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_FrozenCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0"
                                                                     Value="0,0,1,0"/>
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="FooterColumnCell">
                                    <Storyboard BeginTime="0">
                                        <ThicknessAnimationUsingKeyFrames BeginTime="0"
                                                                          Duration="1"
                                                                          Storyboard.TargetName="PART_FooterCellBorder"
                                                                          Storyboard.TargetProperty="BorderThickness">
                                            <EasingThicknessKeyFrame KeyTime="0"
                                                                     Value="1,0,0,0"/>
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border Background="{TemplateBinding CellSelectionBrush}"
                                SnapsToDevicePixels="True"
                                Visibility="{TemplateBinding SelectionBorderVisibility}" />
                        <Border x:Name="PART_FooterCellBorder"
                                Background="Transparent"
                                BorderBrush="{TemplateBinding BorderBrush}"/>
                        <Border x:Name="PART_FrozenCellBorder"
                                Background="Transparent"
                                BorderBrush="{TemplateBinding BorderBrush}"/>
                        <Border x:Name="PART_GridCellBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{StaticResource Windows11Dark.ThemeCornerRadiusVariant1}"
                                SnapsToDevicePixels="True">

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ContentPresenter Grid.Column="0"
                                                  x:Name="PART_ContentPresenter"
                                                  Margin="{TemplateBinding Padding}" >
                                    <ContentPresenter.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                                <ToggleButton x:Name ="PART_FilterOptionButton"
                                              Grid.Column="1"
                                              HorizontalAlignment="Stretch"
                                              VerticalAlignment="Stretch"                                              
                                              FocusVisualStyle="{x:Null}"
                                              Visibility="{TemplateBinding FilterOptionButtonVisibility}"
                                              IsTabStop="False"
                                              Width="19"
                                              Style="{StaticResource FilterOptionButtonStyle}">
                                    <ToggleButton.Margin>
                                        <Thickness>0,0,4,2</Thickness>
                                    </ToggleButton.Margin>
                                </ToggleButton>
                            </Grid>

                        </Border>
                       
                        <Border x:Name="PART_CurrentCellBorder" 
                                Background="Transparent" 
                                Margin="0,0,1,1"
                                BorderBrush="{TemplateBinding CurrentCellBorderBrush}"
                                BorderThickness="{TemplateBinding CurrentCellBorderThickness}"
                                IsHitTestVisible="False"
                                SnapsToDevicePixels="True"
                                Visibility="{TemplateBinding CurrentCellBorderVisibility}">
                            <Border x:Name="PART_FilterRowCellInnerBorder" BorderThickness="{StaticResource Windows11Dark.BorderThickness1}" BorderBrush="{StaticResource BorderAlt3}" Visibility="Collapsed"/>
                        </Border>
                        <Border x:Name="PART_PopupPresenter">
                            <Popup x:Name="PART_FilterOptionPopup"
                                  AllowsTransparency="True"
                                  Placement="Bottom"
                                  PlacementTarget="{Binding ElementName=PART_FilterOptionButton}"
                                  StaysOpen="False" 
                                  PopupAnimation="Slide">
                                <Border x:Name="PART_PopUpBorder"
                                            Background="{StaticResource PopupBackground}"
                                            Width="140"
                                            Margin="14,0,14,14"
                                            Effect="{StaticResource Default.ShadowDepth4}"
                                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"> 
                                    <ListBox x:Name="PART_FilterOptionsList"
                                             
                                             IsSynchronizedWithCurrentItem="True"
                                             FocusVisualStyle="{x:Null}"
                                             HorizontalAlignment="Stretch"
                                             VerticalAlignment="Stretch"
                                             HorizontalContentAlignment="Left"
                                             VerticalContentAlignment="Center">
                                        <ListBox.Resources>
                                            <Style TargetType="{x:Type ListBoxItem}" BasedOn="{StaticResource WPFListBoxItemStyle}">
                                                <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                                            </Style>
                                        </ListBox.Resources>
                                    </ListBox>
                                </Border>
                            </Popup>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Visibility" SourceName="PART_CurrentCellBorder" Value="Visible">
                            <Setter TargetName="PART_FilterOptionButton" Property="Foreground" Value="{StaticResource ContentForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type gridRowFilter:GridFilterRowCell}" BasedOn="{StaticResource SyncfusionGridFilterRowCellStyle}"/>

    <!--GroupDropArea style-->

    <Style x:Key="ToggleButtonStyle" TargetType="ToggleButton">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Border Name="PART_ExpanderIconBorder" CornerRadius="{StaticResource Windows11Dark.CornerRadius2}" Background="Transparent">
                        <Grid Background="{TemplateBinding Background}">
                            <Path Name="PART_Expanded"
                              Fill="{StaticResource IconColor}"
                              Width="6.5"
                              Height="4"
                              SnapsToDevicePixels="True"
                              HorizontalAlignment="Center"
                              Stretch="Fill"
                              Visibility="Collapsed"
                              UseLayoutRounding="False">
                                <Path.Data>
                                    <PathGeometry>M3.3479989,0 L6.7060001,3.3530057 5.9999988,4.0610001 3.3479989,1.4140052 0.7060012,4.0510056 0,3.3430112 z</PathGeometry>
                                </Path.Data>
                            </Path>
                            <Path Name="PART_Collapsed"
                              Fill="{StaticResource IconColor}"
                              Width="6.5"
                              Height="4"
                              SnapsToDevicePixels="True"
                              HorizontalAlignment="Center"
                              Stretch="Fill"
                              UseLayoutRounding="False">
                                <Path.Data>
                                    <PathGeometry>M0.70600799,0 L3.3579973,2.6480073 5.9999922,0.011001584 6.7060002,0.71900849 3.3579973,4.061 0,0.70800702 z</PathGeometry>
                                </Path.Data>
                            </Path>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="PART_Collapsed" Property="Visibility" Value="Collapsed"/>
                            <Setter TargetName="PART_Expanded" Property="Visibility" Value="Visible"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="PART_ExpanderIconBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter TargetName="PART_Collapsed" Property="Fill" Value="{StaticResource IconColorHovered}"/>
                            <Setter TargetName="PART_Expanded" Property="Fill" Value="{StaticResource IconColorHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="PART_ExpanderIconBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter TargetName="PART_Collapsed" Property="Fill" Value="{StaticResource IconColorSelected}"/>
                            <Setter TargetName="PART_Expanded" Property="Fill" Value="{StaticResource IconColorSelected}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="grid:GroupDropArea" x:Key="SyncfusionGroupDropAreaStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="1,1,1,0" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="Focusable" Value="False" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:GroupDropArea">
                    <Border BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="ExpansionStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0" />
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Collapsed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_GroupDropAreaBorder" Storyboard.TargetProperty="(FrameworkElement.Margin)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Thickness>0,0,0,0</Thickness>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_GroupDropAreaBorder" Storyboard.TargetProperty="(FrameworkElement.Height)">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Expanded">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_GroupDropAreaBorder" Storyboard.TargetProperty="(FrameworkElement.Margin)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Thickness>0,8,0,6</Thickness>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_GroupDropAreaBorder" Storyboard.TargetProperty="(FrameworkElement.Height)">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="28">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid Background="{TemplateBinding Background}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition x:Name="cd0" Width="*" />
                                <ColumnDefinition x:Name="cd1" Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <ToggleButton Grid.Column="1"
                                          Width="10"
                                          Height="12"
                                          Margin="2"
                                          Background="Transparent"
                                          VerticalAlignment="Top"
                                          IsChecked="{Binding Path=IsExpanded,
                                                              RelativeSource={RelativeSource TemplatedParent},
                                                              Mode=TwoWay}"
                                          IsTabStop="False"
                                          IsThreeState="False"
                                          SnapsToDevicePixels="True"
                                          Style="{StaticResource ToggleButtonStyle}" />
                            <Border Name="PART_GroupDropAreaBorder"
                                    Grid.Column="0"
                                    Height="0"
                                    SnapsToDevicePixels="True">
                                <Grid x:Name="PART_GroupDropAreaGrid"
                                      MaxHeight="36">
                                    <Grid HorizontalAlignment="Center"
                                          VerticalAlignment="Stretch"
                                          Visibility="{TemplateBinding WatermarkTextVisibility}">
                                        <TextBlock Margin="40,5"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"
                                                   Foreground="{TemplateBinding Foreground}"
                                                   Text="{TemplateBinding GroupDropAreaText}" />
                                    </Grid>
                                    <StackPanel Name="PART_StackPanel"
                                                MaxHeight="36"
                                                Orientation="Horizontal" />
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:GroupDropArea}" BasedOn="{StaticResource SyncfusionGroupDropAreaStyle}"/>

    <Style x:Key="ThumbStyle" TargetType="Thumb">
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Thumb">
                    <Grid Background="Transparent">
                        <Path Width="20"
                              Height="20"
                              Data="M64,32C64,49.673112 49.673112,64 32,64 14.326888,64 0,49.673112 0,32 0,14.326888 14.326888,0 32,0 49.673112,0 64,14.326888 64,32z"
                              Fill="Black"
                              Stretch="Uniform"
                              Stroke="White" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="grid:PopupContentControl" x:Key="SyncfusionPopupContentControlStyle">
        <Setter Property="BorderBrush" Value="{StaticResource ContentBackgroundAlt2}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="ThumbWidth" Value="18" />
        <Setter Property="IsManipulationEnabled" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:PopupContentControl">
                    <Grid Background="Transparent">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="DragStates">
                                <VisualState x:Name="Open">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_PopupPresenter" Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="1.05">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_PopupPresenter" Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="1.05">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>

                                <VisualState x:Name="Drag">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_PopupPresenter" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1.05" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="1.1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_PopupPresenter" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1.05" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="1.1">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_PopupPresenter" Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0.65">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CircleEase />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="DragIndicationStates">
                                <VisualState x:Name="InValid">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_DragIndicator" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Valid">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_DragIndicator" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>

                        <Grid Name="PART_PopupPresenter"
                              Margin="10,0,10,0"                            
                              Opacity="1"                   
                              Background="{StaticResource PopupBackground}" 
                              RenderTransformOrigin="0.5,0.5">
                            <Grid.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform />
                                    <SkewTransform />
                                    <RotateTransform />
                                    <TranslateTransform />
                                </TransformGroup>
                            </Grid.RenderTransform>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <Thumb x:Name="PART_LeftThumbGripper"
                                   Grid.Column="0"
                                   Margin="-10,0,0,0"                                  
                                   IsManipulationEnabled="True"                                 
                                   Style="{StaticResource ThumbStyle}"
                                   Visibility="{TemplateBinding LeftResizeThumbVisibility}" />
                            <Border Grid.Column="1"
                                    BorderThickness="2"
                                    CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                     SnapsToDevicePixels="True">
                                <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" >
                                    <ContentPresenter.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                            </Border>
                            <Thumb x:Name="PART_RightThumbGripper"
                                   Grid.Column="2"
                                   Margin="0,0,-10,0"
                                   IsManipulationEnabled="True"
                                   Style="{StaticResource ThumbStyle}"
                                   Visibility="{TemplateBinding RightResizeThumbVisibility}" />

                        </Grid>
                        <Grid x:Name="PART_DragIndicator"
                              Margin="-5,-7,0,0"
                              HorizontalAlignment="Left"
                              VerticalAlignment="Top"
                              Width="14"
                              Height="14"
                              Visibility="Collapsed">
                            <Grid Visibility="Visible">
                                <Ellipse Fill="#FFBC0000"
                                         SnapsToDevicePixels="True" />
                            </Grid>
                            <Path Width="8"
                                  Height="8"
                                  Data="F1M-1774.05,-6263.81L-1787.51,-6277.27 -1773.22,-6291.56C-1769.23,-6295.55 -1769.24,-6302.03 -1773.21,-6306.01 -1777.19,-6309.98 -1783.67,-6309.99 -1787.66,-6305.99L-1801.95,-6291.71 -1816.79,-6306.55C-1820.79,-6310.55 -1827.26,-6310.54 -1831.24,-6306.55 -1835.22,-6302.58 -1835.23,-6296.11 -1831.24,-6292.12L-1816.39,-6277.27 -1830.4,-6263.25C-1834.4,-6259.26 -1834.38,-6252.8 -1830.4,-6248.82 -1826.42,-6244.84 -1819.96,-6244.82 -1815.96,-6248.82L-1801.95,-6262.83 -1788.49,-6249.37C-1784.5,-6245.38 -1778.03,-6245.39 -1774.06,-6249.37 -1770.07,-6253.35 -1770.06,-6259.82 -1774.05,-6263.81"
                                  Fill="#FFFFFF"
                                  Stretch="Uniform">
                                <Path.RenderTransform>
                                    <TransformGroup>
                                        <TransformGroup.Children>
                                            <RotateTransform Angle="0" />
                                            <ScaleTransform ScaleX="1" ScaleY="1" />
                                        </TransformGroup.Children>
                                    </TransformGroup>
                                </Path.RenderTransform>
                            </Path>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Resources>
            <Style TargetType="{x:Type TextBlock}"/>
        </Style.Resources>
    </Style>
    <Style TargetType="grid:PopupContentControl" BasedOn="{StaticResource SyncfusionPopupContentControlStyle}"/>

    <Style x:Key="CloseButtonStyle" TargetType="Button">
        <Setter Property="Foreground" Value="{StaticResource IconColor}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" SnapsToDevicePixels="True" CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                        <Path Fill="{TemplateBinding Foreground}"
                                Width="9"
                                Height="9" 
                                Stretch="Fill">
                            <Path.Data>
                                <PathGeometry>M0.70403775,0 L5.9992591,5.2450725 11.294031,0 11.998011,0.71000715 6.709643,5.9487292 11.998,11.187002 11.293962,11.898 5.9989902,6.6527101 0.70399179,11.898 1.1442195E-05,11.188009 5.2886383,5.9490234 0,0.71000715 z</PathGeometry>
                            </Path.Data>
                        </Path>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!--  Group Drop Area Item Style  -->
    <Style TargetType="grid:GroupDropAreaItem" x:Key="SyncfusionGroupDropAreaItemStyle">
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt3}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="BorderBrush" Value="{StaticResource ContentBackgroundAlt2}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:GroupDropAreaItem">
                    <Border x:Name="PART_GroupDropAreaItemBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            SnapsToDevicePixels="True">
                        <VisualStateManager.VisualStateGroups>

                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_GroupDropAreaItemBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>

                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_GroupDropAreaItemBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>

                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CloseButton" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Visible</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_GroupDropAreaItemBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>

                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CloseButton" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Visible</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Normal">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_GroupDropAreaItemBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundAlt3}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_GroupDropAreaItemBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundAlt2}" />
                                        </ObjectAnimationUsingKeyFrames>

                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_CloseButton" Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0" Value="1" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:1" Value="0" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CloseButton" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <Visibility>Collapsed</Visibility>
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                        
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>

                        </VisualStateManager.VisualStateGroups>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid Grid.Column="0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ContentPresenter x:Name="contentPresenter" Margin="8 0"
                                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  Content="{Binding Path=GroupName,
                                                                    RelativeSource={RelativeSource TemplatedParent},
                                                                    Mode=TwoWay}">
                                    <ContentPresenter.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                                <Grid Grid.Column="1" Margin="8,0,8,0">
                                    <Path x:Name="AscendingSortDirection" Width="10"
                                          Height="12"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Fill="{StaticResource IconColor}"
                                          Stretch="Fill"
                                          Visibility="{Binding Path=SortDirection,
                                                               RelativeSource={RelativeSource TemplatedParent},
                                                               ConverterParameter=Ascending,
                                                               Converter={StaticResource sortDirectionToVisibilityConverter}}">
                                        <Path.Data>
                                                <PathGeometry>M5.9989966,0 L11.999,6.3080139 11.275,7 6.4990009,1.9796008 6.4990009,16.000992 5.4990009,16.000992 5.4990009,1.979692 0.72399958,7 0,6.3080139 z</PathGeometry>
                                            </Path.Data>
                                    </Path>
                                    <Path x:Name="DescendingSortDirection" Width="10"
                                          Height="12"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Fill="{StaticResource IconColor}"
                                          Stretch="Fill"
                                          Visibility="{Binding Path=SortDirection,
                                                               RelativeSource={RelativeSource TemplatedParent},
                                                               ConverterParameter=Decending,
                                                               Converter={StaticResource sortDirectionToVisibilityConverter}}">
                                        <Path.Data>
                                            <PathGeometry>M5.499006,0 L6.499006,0 6.499006,14.021328 11.275003,9.0000086 11.999,9.6930017 5.9990117,16.000009 0,9.6930017 0.72399775,9.0000086 5.499006,14.021219 z</PathGeometry>
                                        </Path.Data>
                                    </Path>
                                </Grid>
                            </Grid>
                            <Button Name="PART_CloseButton"
                                    Grid.Column="1"
                                    Width="20"
                                    Height="20"
                                    Margin="7 0 5 0"
                                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Style="{StaticResource CloseButtonStyle}"
                                    Visibility="Collapsed" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="Fill" TargetName="AscendingSortDirection" Value="{StaticResource IconColorHovered}"/>
                            <Setter Property="Fill" TargetName="DescendingSortDirection" Value="{StaticResource IconColorHovered}"/>
                            <Setter Property="Foreground" TargetName="PART_CloseButton" Value="{StaticResource IconColorHovered}"/>
                            <Setter Property="Background" TargetName="PART_CloseButton" Value="Transparent"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True" SourceName="PART_CloseButton">
                            <Setter Property="Foreground" TargetName="PART_CloseButton" Value="{StaticResource IconColorHovered}"/>
                            <Setter Property="Background" TargetName="PART_CloseButton" Value="{StaticResource ContentBackgroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True" SourceName="PART_CloseButton">
                            <Setter Property="Foreground" TargetName="PART_CloseButton" Value="{StaticResource IconColorSelected}"/>
                            <Setter Property="Background" TargetName="PART_CloseButton" Value="{StaticResource ContentBackgroundPressed}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:GroupDropAreaItem}" BasedOn="{StaticResource SyncfusionGroupDropAreaItemStyle}"/>

    <!--  Expander Cell Style  -->
    <Style TargetType="{x:Type grid:GridExpanderCellControl}" x:Key="SyncfusionGridExpanderCellControlStyle">
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Width" Value="8" />
        <Setter Property="Height" Value="4" />
        <Setter Property="Foreground" Value="{StaticResource IconColor}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type grid:GridExpanderCellControl}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="ExpansionStates">
                                <VisualState x:Name="Collapsed">
                                </VisualState>
                                <VisualState x:Name="Expanded">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames BeginTime="00:00:00"
                                                                       Duration="00:00:00"
                                                                       Storyboard.TargetName="PART_GridExpanderCellPath"
                                                                       Storyboard.TargetProperty="RenderTransform">
                                            <DiscreteObjectKeyFrame KeyTime="00:00:00">
                                                <DiscreteObjectKeyFrame.Value>
                                                            <RotateTransform Angle="0" />
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Path x:Name="PART_GridExpanderCellPath"
                             Width="10"
                              Height="5"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                            Fill="{TemplateBinding Foreground}"
                              SnapsToDevicePixels="True"
                              Stretch="Uniform">
                            <Path.Data>
                                <PathGeometry>M0.74499548,0 L5.0119957,4.7700001 9.2630047,0.017000169 10.008001,0.68400005 5.0119957,6.2700001 0,0.66699985 z</PathGeometry>
                            </Path.Data>
                            <Path.RenderTransform>
                                <TransformGroup>
                                    <TransformGroup.Children>
                                                <RotateTransform Angle="-90" CenterX="3" CenterY="3" />
                                    </TransformGroup.Children>
                                </TransformGroup>
                            </Path.RenderTransform>
                        </Path>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:GridExpanderCellControl}" BasedOn="{StaticResource SyncfusionGridExpanderCellControlStyle}"/>

    <!--Drag Arrow style-->

    <Style TargetType="grid:UpIndicatorContentControl" x:Key="SyncfusionDataGridUpIndicatorContentControlStyle" >
        <Setter Property="Height" Value="6" />
        <Setter Property="Width" Value="12" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:UpIndicatorContentControl">
                    <Border SnapsToDevicePixels="True">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="OpenStates">
                                <VisualState x:Name="Open">
                                    <Storyboard RepeatBehavior="Forever">
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_UpIndicator" Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="0.5" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="1" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:1" Value="0.5" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Path x:Name="PART_UpIndicator"
                              Fill="{StaticResource PrimaryBackgroundOpacity1}"
                              RenderTransformOrigin="0.5,0.5"
                              SnapsToDevicePixels="True"
                              Stretch="Fill"
                              Stroke="{StaticResource PrimaryBackgroundOpacity1}"
                              StrokeThickness="1"
                              UseLayoutRounding="False">
                            <Path.Data>
                                <PathGeometry>M5.85355 0.353554L10.1464 4.64645C10.4614 4.96143 10.2383 5.5 9.79289 5.5L1.20711 5.5C0.761655 5.5 0.538571 4.96143 0.853554 4.64645L5.14645 0.353554C5.34171 0.158292 5.65829 0.158292 5.85355 0.353554Z</PathGeometry>
                            </Path.Data>
                        </Path>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:UpIndicatorContentControl}" BasedOn="{StaticResource SyncfusionDataGridUpIndicatorContentControlStyle}"/>
    
    <Style TargetType="grid:DownIndicatorContentControl" x:Key="SyncfusionDataGridDownIndicatorContentControlStyle">
        <Setter Property="Height" Value="6" />
        <Setter Property="Width" Value="12" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:DownIndicatorContentControl">
                    <Border SnapsToDevicePixels="True">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="OpenStates">
                                <VisualState x:Name="Open">
                                    <Storyboard RepeatBehavior="Forever">
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_DownIndicator" Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="0.5" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="1" />
                                            <EasingDoubleKeyFrame KeyTime="0:0:1" Value="0.5" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Path x:Name="PART_DownIndicator"
                              Fill="{StaticResource PrimaryBackgroundOpacity1}"
                              RenderTransformOrigin="0.5,0.5"
                              SnapsToDevicePixels="True"
                              Stretch="Fill"
                              Stroke="{StaticResource PrimaryBackgroundOpacity1}"
                              StrokeThickness="1"
                              UseLayoutRounding="False">
                            <Path.Data>
                                <PathGeometry>M5.14645 5.14645L0.853553 0.853553C0.538571 0.538571 0.761654 0 1.20711 0H9.79289C10.2383 0 10.4614 0.538572 10.1464 0.853554L5.85355 5.14645C5.65829 5.34171 5.34171 5.34171 5.14645 5.14645Z</PathGeometry>
                            </Path.Data>
                        </Path>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:DownIndicatorContentControl}" BasedOn="{StaticResource SyncfusionDataGridDownIndicatorContentControlStyle}"/>

    <!--  DetailsView Style  -->
    <Style TargetType="grid:DetailsViewDataGrid" x:Key="SyncfusionDetailsViewDataGridStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="AllowRowHoverHighlighting" Value="True" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="HeaderRowHeight" Value="30" />
        <Setter Property="RowSelectionBrush" Value="{StaticResource TableSelectedBackground}" />
        <Setter Property="RowHoverHighlightingBrush" Value="{StaticResource TableHoveredBackground}"/>
        <Setter Property="SelectionForegroundBrush" Value="{StaticResource TableSelectedForeground}" />
        <Setter Property="GroupRowSelectionBrush" Value="{StaticResource TableSelectedBackground}" />
        <Setter Property="CurrentCellBorderThickness" Value="2" />
        <Setter Property="CurrentCellBorderBrush" Value="{StaticResource BorderAlt3}" />
		<Setter Property="GridLinesVisibility" Value="None"/>
        <Style.Triggers>
            <Trigger Property="skinManager:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="RowHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                <Setter Property="HeaderRowHeight" Value="36"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style TargetType="{x:Type grid:DetailsViewDataGrid}" BasedOn="{StaticResource SyncfusionDetailsViewDataGridStyle}"/>

    <Style TargetType="grid:GridDetailsViewExpanderCell" x:Key="SyncfusionGridDetailsViewExpanderCellStyle">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource IconColor}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type grid:GridDetailsViewExpanderCell}">
                    <Border x:Name="PART_ExpanderCellBorder" 
                            Background="Transparent"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="ExpansionStates">
                                <VisualState x:Name="Expanded">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CollapseCellPath" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame Value="{x:Static Visibility.Collapsed}" KeyTime="0:0:0"/>

                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ExpanderCellPath" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame Value="{x:Static Visibility.Visible}" KeyTime="0:0:0" />

                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Collapsed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CollapseCellPath" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame Value="{x:Static Visibility.Visible}" KeyTime="0:0:0"/>

                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ExpanderCellPath" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame Value="{x:Static Visibility.Collapsed}" KeyTime="0:0:0"/>
                                        </ObjectAnimationUsingKeyFrames>

                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid Background="{TemplateBinding Background}" Visibility="{TemplateBinding ExpanderIconVisibility}" Margin="{TemplateBinding Padding}">
                            <Path Margin="2" x:Name="PART_CollapseCellPath"
                                  Width="5"
                                  Height="10"
                                  Fill="{TemplateBinding Foreground}"
                                  Stretch="Uniform">
                                <Path.Data>
                                    <PathGeometry>M0.68398996,0 L6.2700001,4.9960007 0.66699173,10.007999 0,9.2629985 4.7700011,4.9960007 0.016998228,0.74499984 z</PathGeometry>
                                </Path.Data>

                            </Path>
                            <Path x:Name="PART_ExpanderCellPath"
                                  Margin="2"
                                   Width="10"
                                  Height="5"
                                  Visibility="Collapsed"
                                  Fill="{TemplateBinding Foreground}"
                                  Stretch="Uniform">
                                <Path.Data>
                                    <PathGeometry>M0.74499548,0 L5.0119957,4.7700001 9.2630047,0.017000169 10.008001,0.68400005 5.0119957,6.2700001 0,0.66699985 z</PathGeometry>
                                </Path.Data>
                            </Path>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:GridDetailsViewExpanderCell}" BasedOn="{StaticResource SyncfusionGridDetailsViewExpanderCellStyle}"/>
    
    <Style TargetType="grid:DetailsViewContentPresenter" x:Key="SyncfusionDetailsViewContentPresenterStyle">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
    </Style>
    <Style TargetType="{x:Type grid:DetailsViewContentPresenter}" BasedOn="{StaticResource SyncfusionDetailsViewContentPresenterStyle}"/>

    <Style TargetType="grid:GridDetailsViewIndentCell" x:Key="SyncfusionGridDetailsViewIndentCellStyle">
        <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
    </Style>
    <Style TargetType="{x:Type grid:GridDetailsViewIndentCell}" BasedOn="{StaticResource SyncfusionGridDetailsViewIndentCellStyle}"/>

    <Style x:Key="SyncfusionDataGridFilterDeleteButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="Foreground" Value="{StaticResource IconColor}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="PART_DeleteButtonPresenter" 
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            Background="Transparent"
                            BorderBrush="Transparent">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal">
                                </VisualState>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_DeleteButtonPresenter" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_DeleteButtonPresenter" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_DeleteButtonPresenter" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_DeleteButtonPresenter" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Path x:Name="PART_DeleteButtonPath"
                              Margin="1"
                              Fill="{TemplateBinding Foreground}"
                              Stretch="Uniform">
                            <Path.Data>
                                <PathGeometry>M5.15234 4.625L8.63867 8.11133C8.71289 8.18555 8.75 8.27344 8.75 8.375C8.75 8.47656 8.71289 8.56445 8.63867 8.63867C8.56445 8.71289 8.47656 8.75 8.375 8.75C8.27344 8.75 8.18555 8.71289 8.11133 8.63867L4.625 5.15234L1.13867 8.63867C1.06445 8.71289 0.976562 8.75 0.875 8.75C0.773438 8.75 0.685547 8.71289 0.611328 8.63867C0.537109 8.56445 0.5 8.47656 0.5 8.375C0.5 8.27344 0.537109 8.18555 0.611328 8.11133L4.09766 4.625L0.611328 1.13867C0.537109 1.06445 0.5 0.976562 0.5 0.875C0.5 0.773438 0.537109 0.685547 0.611328 0.611328C0.685547 0.537109 0.773438 0.5 0.875 0.5C0.976562 0.5 1.06445 0.537109 1.13867 0.611328L4.625 4.09766L8.11133 0.611328C8.18555 0.537109 8.27344 0.5 8.375 0.5C8.47656 0.5 8.56445 0.537109 8.63867 0.611328C8.71289 0.685547 8.75 0.773438 8.75 0.875C8.75 0.976562 8.71289 1.06445 8.63867 1.13867L5.15234 4.625Z</PathGeometry>
                            </Path.Data>

                            <Path.RenderTransform>
                                <TransformGroup>
                                    <RotateTransform Angle="0" />
                                    <ScaleTransform ScaleX="1" ScaleY="1" />
                                </TransformGroup>
                            </Path.RenderTransform>
                        </Path>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionDataGridClearFilterButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Grid x:Name="PART_ClearButtonPresenter" Background="Transparent">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ClearButtonPath" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource HoveredForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="clearButtonContentPresenter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource IconColorHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ClearButtonPath" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource SelectedForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="clearButtonContentPresenter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource SelectedForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ClearButtonPath" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource DisabledForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="clearButtonContentPresenter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource DisabledForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="FocusStates">
                                <VisualState x:Name="Focused">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ClearButtonPath" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource SelectedForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="clearButtonContentPresenter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource SelectedForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Unfocused" />
                                <VisualState x:Name="PointerFocused" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="PART_RootBorder" 
                                Background="Transparent" 
                                BorderBrush="Transparent" 
                                BorderThickness="1"
                                Height="24"
                                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                            <Border.Padding>
                                <Thickness>0</Thickness>
                            </Border.Padding>
                            <Border.Margin>
                                <Thickness>4,6,4,2</Thickness>
                            </Border.Margin>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <Path x:Name="PART_ClearButtonPath"
                                      Width="12"
                                      Height="12"
                                      Fill="{StaticResource IconColor}"
                                      Stretch="Uniform">
                                    <Path.Margin>
                                        <Thickness>4,4,3,4</Thickness>
                                    </Path.Margin>
                                    <Path.Data>
                                        <PathGeometry>M0 1.875C0 1.71875 0.0292969 1.57227 0.0878906 1.43555C0.146484 1.29883 0.226562 1.17969 0.328125 1.07812C0.429688 0.976562 0.548828 0.896484 0.685547 0.837891C0.822266 0.779297 0.96875 0.75 1.125 0.75H10.875C11.0312 0.75 11.1777 0.779297 11.3145 0.837891C11.4512 0.896484 11.5703 0.976562 11.6719 1.07812C11.7734 1.17969 11.8535 1.29883 11.9121 1.43555C11.9707 1.57227 12 1.71875 12 1.875C12 2.19531 11.8828 2.4668 11.6484 2.68945L7.85156 6.31055L7.81035 6.35352C7.77204 6.39648 7.73695 6.44043 7.70508 6.48633C7.66211 6.54883 7.625 6.61328 7.59375 6.67969C7.53125 6.81641 7.5 6.96484 7.5 7.125V11.625C7.5 11.7266 7.46289 11.8145 7.38867 11.8887C7.31445 11.9629 7.22656 12 7.125 12C7.05078 12 6.98242 11.9785 6.91992 11.9355L4.66992 10.4355C4.55664 10.3613 4.5 10.2578 4.5 10.125V7.125C4.5 6.96875 4.4707 6.82031 4.41211 6.67969C4.39347 6.6377 4.37205 6.59766 4.34785 6.55957C4.33148 6.53418 4.31384 6.50977 4.29492 6.48633C4.25195 6.42383 4.20312 6.36523 4.14844 6.31055L0.351562 2.68945C0.117188 2.4668 0 2.19531 0 1.875ZM11.25 1.86914C11.25 1.76758 11.2129 1.68164 11.1387 1.61133C11.0645 1.53711 10.9766 1.5 10.875 1.5H1.125C1.02344 1.5 0.935547 1.53711 0.861328 1.61133C0.787109 1.68164 0.75 1.76758 0.75 1.86914C0.75 1.98242 0.789062 2.07422 0.867188 2.14453L4.66992 5.76562C4.85352 5.94141 4.99609 6.14844 5.09766 6.38672C5.19922 6.625 5.25 6.87109 5.25 7.125V9.92578L6.75 10.9219V7.125C6.75 6.87109 6.80078 6.625 6.90234 6.38672C7.00391 6.14844 7.14648 5.94141 7.33008 5.76562L11.1328 2.14453C11.2109 2.07422 11.25 1.98242 11.25 1.86914ZM10.9849 8.11719C11.1413 7.96094 11.3947 7.96094 11.551 8.11719C11.7073 8.27344 11.7073 8.52637 11.551 8.68262L10.3997 9.83398L11.551 10.9854C11.7073 11.1416 11.7073 11.3945 11.551 11.5508C11.3947 11.707 11.1413 11.707 10.9849 11.5508L9.83398 10.3994L8.68302 11.5508C8.52671 11.707 8.27328 11.707 8.11698 11.5508C7.96068 11.3945 7.96068 11.1416 8.11698 10.9854L9.26827 9.83398L8.11698 8.68262C7.96068 8.52637 7.96068 8.27344 8.11698 8.11719C8.27328 7.96094 8.52671 7.96094 8.68302 8.11719L9.83398 9.26855L10.9849 8.11719Z</PathGeometry>
                                    </Path.Data>

                                    <Path.RenderTransform>
                                        <TransformGroup>
                                            <TransformGroup.Children>
                                                <RotateTransform Angle="0" />
                                                <ScaleTransform ScaleX="1" ScaleY="1" />
                                            </TransformGroup.Children>
                                        </TransformGroup>
                                    </Path.RenderTransform>
                                </Path>
                                <ContentPresenter x:Name="clearButtonContentPresenter"
                                                  Grid.Column="1"
                                                  Margin="2 0 8 0"
                                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  OpacityMask="{x:Null}"
                                                  RecognizesAccessKey="True"
                                                  SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" >
                                    <ContentPresenter.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                            </Grid>
                        </Border>
                        <Border Grid.ColumnSpan="2"
                                Width="202"
                                BorderBrush="{StaticResource BorderAlt}"
                                BorderThickness="0,1,0,0" >
                            <Border.Margin>
                                <Thickness>0,2,0,0</Thickness>
                            </Border.Margin>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="skinManager:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
            
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionDataGridAdvancedFilterButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Grid x:Name="PART_AdvancedFiltersButtonPresenter" Background="Transparent">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ExpanderCellPath" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource HoveredForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_AdvancedFilterToggleIndicator" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource HoveredForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_FilteredFromCheck" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource HoveredForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter_filter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource HoveredForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundSelected}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundSelected}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ExpanderCellPath" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource SelectedForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_AdvancedFilterToggleIndicator" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource SelectedForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_FilteredFromCheck" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource SelectedForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter_filter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_AdvancedFiltersButtonPresenter" Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="0.5" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="FocusStates">
                                <VisualState x:Name="Focused">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundSelected}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundSelected}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_ExpanderCellPath" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource SelectedForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter_filter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Unfocused" />
                                <VisualState x:Name="PointerFocused" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="ExpansionStates">
                                <VisualState x:Name="Expanded">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_ExpanderCellPath" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(RotateTransform.Angle)">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CubicEase EasingMode="EaseIn" />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
  
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Collapsed">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_ExpanderCellPath" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(RotateTransform.Angle)">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="-180">
                                                <EasingDoubleKeyFrame.EasingFunction>
                                                    <CubicEase EasingMode="EaseIn" />
                                                </EasingDoubleKeyFrame.EasingFunction>
                                            </EasingDoubleKeyFrame>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="PART_RootBorder" 
                                Background="Transparent" 
                                Height="24"
                                BorderBrush="Transparent" 
                                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                BorderThickness="1">
                            <Border.Padding>
                                <Thickness>0</Thickness>
                            </Border.Padding>
                            <Border.Margin>
                                <Thickness>4,2,4,2</Thickness>
                            </Border.Margin>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Border Grid.Column="0"
                                Width="19"
                                Height="19"
                                BorderThickness="0"
                                Visibility="{Binding FilteredFrom,
                                                     RelativeSource={RelativeSource FindAncestor,
                                                                                    AncestorType={x:Type grid:GridFilterControl}},
                                                     Converter={StaticResource filteredFromCheckVisibilityConverter},
                                                     ConverterParameter=reverse}" />
                                <Path x:Name="PART_AdvancedFilterToggleIndicator"
                                             Width="12"
                                             Height="12"
                                             Fill="{StaticResource IconColor}"
                                             Stretch="Uniform">
                                    <Path.Margin>
                                        <Thickness>4 4 3 4</Thickness>
                                    </Path.Margin>
                                    <Path.Data>
                                        <PathGeometry>M0 1.875C0 1.71875 0.0292969 1.57227 0.0878906 1.43555C0.146484 1.29883 0.226562 1.17969 0.328125 1.07812C0.429688 0.976562 0.548828 0.896484 0.685547 0.837891C0.822266 0.779297 0.96875 0.75 1.125 0.75H10.875C11.0312 0.75 11.1777 0.779297 11.3145 0.837891C11.4512 0.896484 11.5703 0.976562 11.6719 1.07812C11.7734 1.17969 11.8535 1.29883 11.9121 1.43555C11.9707 1.57227 12 1.71875 12 1.875C12 2.19531 11.8828 2.4668 11.6484 2.68945L7.85156 6.31055C7.79688 6.36523 7.74805 6.42383 7.70508 6.48633C7.66211 6.54883 7.625 6.61328 7.59375 6.67969C7.53125 6.81641 7.5 6.96484 7.5 7.125V11.625C7.5 11.7266 7.46289 11.8145 7.38867 11.8887C7.31445 11.9629 7.22656 12 7.125 12C7.05078 12 6.98242 11.9785 6.91992 11.9355L4.66992 10.4355C4.55664 10.3613 4.5 10.2578 4.5 10.125V7.125C4.5 6.96875 4.4707 6.82031 4.41211 6.67969C4.38086 6.60938 4.3418 6.54492 4.29492 6.48633C4.25195 6.42383 4.20312 6.36523 4.14844 6.31055L0.351562 2.68945C0.117188 2.4668 0 2.19531 0 1.875ZM11.25 1.86914C11.25 1.76758 11.2129 1.68164 11.1387 1.61133C11.0645 1.53711 10.9766 1.5 10.875 1.5H1.125C1.02344 1.5 0.935547 1.53711 0.861328 1.61133C0.787109 1.68164 0.75 1.76758 0.75 1.86914C0.75 1.98242 0.789062 2.07422 0.867188 2.14453L4.66992 5.76562C4.85352 5.94141 4.99609 6.14844 5.09766 6.38672C5.19922 6.625 5.25 6.87109 5.25 7.125V9.92578L6.75 10.9219V7.125C6.75 6.87109 6.80078 6.625 6.90234 6.38672C7.00391 6.14844 7.14648 5.94141 7.33008 5.76562L11.1328 2.14453C11.2109 2.07422 11.25 1.98242 11.25 1.86914Z</PathGeometry>
                                    </Path.Data>
                                    <Path.Visibility>
                                        <Binding Path="FilteredFrom"
                                           RelativeSource="{RelativeSource FindAncestor,
                                                                        AncestorType={x:Type grid:GridFilterControl}}"
                                           Converter="{StaticResource filteredFromCheckVisibilityConverter}"
                                           ConverterParameter="normal"></Binding>
                                    </Path.Visibility>
                                    <Path.RenderTransform>
                                        <TransformGroup>
                                            <TransformGroup.Children>
                                                <RotateTransform Angle="0" />
                                                <ScaleTransform ScaleX="1" ScaleY="1" />
                                            </TransformGroup.Children>
                                        </TransformGroup>
                                    </Path.RenderTransform>
                                </Path>
                                <Path x:Name="PART_FilteredFromCheck"
                              Width="12"
                              Height="12"     
                              Fill="{StaticResource IconColor}"
                              Stretch="Uniform"
                              Visibility="{Binding FilteredFrom,
                                                   RelativeSource={RelativeSource FindAncestor,
                                                                                  AncestorType={x:Type grid:GridFilterControl}},
                                                   Converter={StaticResource filteredFromCheckVisibilityConverter},
                                                   ConverterParameter=reverse}">
                                    <Path.Margin>
                                        <Thickness>0</Thickness>
                                    </Path.Margin>
                                    <Path.Data>
                                        <PathGeometry>M10 4C11.1046 4 12 3.10449 12 2C12 0.895508 11.1046 0 10 0C8.89543 0 8 0.895508 8 2C8 3.10449 8.89543 4 10 4ZM7.85156 6.31055L9.30946 4.91992C9.03473 4.85547 8.77458 4.75293 8.53478 4.61816L7.33008 5.76562C7.14648 5.94141 7.00391 6.14844 6.90234 6.38672C6.80078 6.625 6.75 6.87109 6.75 7.125V10.9219L5.25 9.92578V7.125C5.25 6.87109 5.19922 6.625 5.09766 6.38672C4.99609 6.14844 4.85352 5.94141 4.66992 5.76562L0.867188 2.14453C0.789062 2.07422 0.75 1.98242 0.75 1.86914C0.75 1.76758 0.787109 1.68164 0.861328 1.61133C0.935547 1.53711 1.02344 1.5 1.125 1.5H7.04148C7.08564 1.23633 7.16407 0.985352 7.27202 0.75H1.125C0.96875 0.75 0.822266 0.779297 0.685547 0.837891C0.548828 0.896484 0.429688 0.976562 0.328125 1.07812C0.226562 1.17969 0.146484 1.29883 0.0878906 1.43555C0.0292969 1.57227 0 1.71875 0 1.875C0 2.19531 0.117188 2.4668 0.351562 2.68945L4.14844 6.31055C4.20312 6.36523 4.25195 6.42383 4.29492 6.48633C4.3418 6.54492 4.38086 6.60938 4.41211 6.67969C4.4707 6.82031 4.5 6.96875 4.5 7.125V10.125C4.5 10.2578 4.55664 10.3613 4.66992 10.4355L6.91992 11.9355C6.98242 11.9785 7.05078 12 7.125 12C7.22656 12 7.31445 11.9629 7.38867 11.8887C7.46289 11.8145 7.5 11.7266 7.5 11.625V7.125C7.5 6.96484 7.53125 6.81641 7.59375 6.67969C7.625 6.61328 7.66211 6.54883 7.70508 6.48633C7.74805 6.42383 7.79688 6.36523 7.85156 6.31055Z</PathGeometry>
                                    </Path.Data>

                                    <Path.RenderTransform>
                                        <TransformGroup>
                                            <TransformGroup.Children>
                                                <RotateTransform Angle="0" />
                                                <ScaleTransform ScaleX="1" ScaleY="1" />
                                            </TransformGroup.Children>
                                        </TransformGroup>
                                    </Path.RenderTransform>
                                </Path>
                                <ContentPresenter Grid.Column="1" x:Name="ContentPresenter_filter"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          OpacityMask="{x:Null}"
                                          RecognizesAccessKey="True"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" >
                                    <ContentPresenter.Margin>
                                        <Thickness>2,2,0,2</Thickness>
                                    </ContentPresenter.Margin>
                                    <ContentPresenter.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                                <Grid Grid.Column="2"
                                      Width="16"
                                      Height="16">
                                    <Grid.Margin>
                                        <Thickness>2,2,2,2</Thickness>
                                    </Grid.Margin>
                                    <Path x:Name="PART_ExpanderCellPath"
                                  Width="9"
                                  Height="5"
                                  Fill="{StaticResource IconColor}"
                                  RenderTransformOrigin="0.5,0.5"
                                  Stretch="Fill">
                                        <Path.Margin>
                                            <Thickness>2,3.5,4,2</Thickness>
                                        </Path.Margin>
                                        <Path.Data>
                                            <PathGeometry>M0.855469 0.898438C0.757812 0.800782 0.640625 0.751953 0.503906 0.751953C0.367188 0.751953 0.248047 0.798828 0.146484 0.892578C0.0488281 0.990235 0 1.10938 0 1.25C0 1.38672 0.0488281 1.50391 0.146484 1.60156L3.65039 5.10547C3.74805 5.20312 3.86523 5.25195 4.00195 5.25195C4.13867 5.25195 4.25586 5.20312 4.35352 5.10547L7.85742 1.60156C7.95508 1.50391 8.00391 1.38672 8.00391 1.25C8.00391 1.10938 7.95312 0.990235 7.85156 0.892579C7.75391 0.798829 7.63672 0.751953 7.5 0.751953C7.36328 0.751953 7.24609 0.800782 7.14844 0.898438L4.00195 4.04492L0.855469 0.898438Z</PathGeometry>
                                        </Path.Data>

                                        <Path.RenderTransform>
                                            <TransformGroup>
                                                <TransformGroup.Children>
                                                    <RotateTransform Angle="0" />
                                                </TransformGroup.Children>
                                            </TransformGroup>
                                        </Path.RenderTransform>
                                    </Path>
                                </Grid>
                            </Grid>
                        </Border>
                        <Border BorderThickness="0,0,0,1" Margin="0,4,0,0" Width="202" Height="28"
                        BorderBrush="{StaticResource BorderAlt}"/>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="skinManager:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
           
        </Style.Triggers>
    </Style>

    <Style TargetType="grid:SortButton" x:Key="SyncfusionDataGridFilterControlSortButtonStyle">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Padding" Value="0"/>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:SortButton">
                    <Grid x:Name="PART_SortButtonPresenter" Background="Transparent"
                          Height="24">
                        <Grid.Margin>
                            <Thickness>4,2,4,2</Thickness>
                        </Grid.Margin>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SortButton_ContentPresenter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource HoveredForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PopupBackground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PopupBackground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SortButton_ContentPresenter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource DisabledForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SortButton_ContentPresenter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource HoveredForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="FocusStates">
                                <VisualState x:Name="Focused">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_RootBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SortButton_ContentPresenter" Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource HoveredForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Unfocused" />
                                <VisualState x:Name="PointerFocused" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="SortStates">
                                <VisualState x:Name="Sorted">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_SortedBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="UnSorted">
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="PART_SortedBorder" Height="24" 
                                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                Visibility="Visible" />
                        <Border x:Name="PART_RootBorder"                              
                                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                Background="{TemplateBinding Background}"
                                BorderBrush="Transparent"
                                BorderThickness="1">
                            <StackPanel Orientation="Horizontal">
                                <Border x:Name="PART_IconPresenter"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        BorderBrush="Transparent"
                                        BorderThickness="1" 
                                       CornerRadius="{StaticResource Windows11Dark.ThemeCornerRadiusVariant1}">
                                    <Border.Margin>
                                        <Thickness>0</Thickness>
                                    </Border.Margin>
                                </Border>
                                <ContentPresenter x:Name="SortButton_ContentPresenter" 
                                                  Margin="0 2 8 2"
                                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}" >
                                    <ContentPresenter.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                            </StackPanel>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="skinManager:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
            
        </Style.Triggers>
    </Style>

    <DataTemplate x:Key="CheckboxFilterControlItemTemplate">
        <CheckBox 
            HorizontalAlignment="Stretch"
            HorizontalContentAlignment="Stretch"
            Content="{Binding DisplayText,
                            Mode=OneWay}"
            Height="24"
           Focusable="True"                 
           grid:VisualContainer.WantsMouseInput="True" 
            IsChecked="{Binding IsSelected,
                                Mode=TwoWay}">
            <CheckBox.Margin>
                <Thickness>12,2,0,2</Thickness>
            </CheckBox.Margin>
        </CheckBox>
    </DataTemplate>

    <Style TargetType="grid:CheckboxFilterControl" x:Key="SyncfusionCheckboxFilterControlStyle">
        <Setter Property="ItemTemplate" Value="{StaticResource CheckboxFilterControlItemTemplate}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:CheckboxFilterControl">
                    <Grid Height="{TemplateBinding Height}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="0" />
                            <ColumnDefinition Width="188" />
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0"
                                Width="19"
                                Height="19"
                                Margin="4,39,4,4"
                                VerticalAlignment="Top"
                                BorderThickness="0"
                                Visibility="{Binding FilteredFrom,
                                                     RelativeSource={RelativeSource FindAncestor,
                                                                                    AncestorType={x:Type grid:GridFilterControl}},
                                                     Converter={StaticResource filteredFromCheckVisibilityConverter}}">
                            <Path x:Name="PART_FilteredFromCheck1"
                                  Width="11.4"
                                  Height="9"
                                  Fill="{StaticResource IconColor}"
                                  Stretch="Uniform"
                                  Visibility="{Binding FilteredFrom,
                                                       RelativeSource={RelativeSource FindAncestor,
                                                                                      AncestorType={x:Type grid:GridFilterControl}},
                                                       Converter={StaticResource filteredFromCheckVisibilityConverter}}">
                                <Path.Data>
                                    <PathGeometry>M15.288992,0 L15.997,0.70702884 5.7260096,11.001 0,5.8859662 0.66601519,5.1399969 5.6870082,9.6239904 z</PathGeometry>
                                </Path.Data>
                                <Path.RenderTransform>
                                    <TransformGroup>
                                        <TransformGroup.Children>
                                            <RotateTransform Angle="0" />
                                            <ScaleTransform ScaleX="1" ScaleY="1" />
                                        </TransformGroup.Children>
                                    </TransformGroup>
                                </Path.RenderTransform>
                            </Path>
                        </Border>

                        <Grid Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <Grid Visibility="{TemplateBinding SearchOptionVisibility}">

                                <TextBox x:Name="PART_SearchTextBox"
                                         Height="24"
                                         HorizontalAlignment="Stretch"
                                         VerticalAlignment="Center"
                                         VerticalContentAlignment="Center">
                                    <TextBox.Margin>
                                        <Thickness>8,8,4,4</Thickness>
                                    </TextBox.Margin>
                                </TextBox>

                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           IsHitTestVisible="False"
                                           Foreground="{StaticResource ContentForegroundAlt1}"
                                           FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                           FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                           FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                                           Text="{grid:GridLocalizationResourceExtension ResourceName=Search}"
                                           Visibility="{TemplateBinding SearchTextBlockVisibility}">
                                    <TextBlock.Margin>
                                        <Thickness>16,8,0,5</Thickness>
                                    </TextBlock.Margin>
                                </TextBlock>

                                <Border Width="12"
                                        Height="12"
                                        HorizontalAlignment="Right"
                                        Visibility="{Binding Text,
                                                             ElementName=PART_SearchTextBox,
                                                             ConverterParameter=searchIcon,
                                                             Converter={StaticResource textBlockVisibilityConverter}}">
                                    <Border.Margin>
                                        <Thickness>0,8,15,6</Thickness>
                                    </Border.Margin>
                                    <Path Fill="{StaticResource IconColor}"
                                          RenderTransformOrigin="0.5,0.5"
                                           
                                           
                                          Stretch="Uniform">
                                        <Path.Data>
                                            <PathGeometry>M9.67969 5.55664C9.89062 5.06836 9.99609 4.54883 9.99609 3.99805C9.99609 3.63086 9.94727 3.27734 9.84961 2.9375C9.75586 2.59766 9.62109 2.2793 9.44531 1.98242C9.27344 1.68555 9.06641 1.41602 8.82422 1.17383C8.58203 0.931641 8.3125 0.724609 8.01562 0.552734C7.71875 0.376953 7.40039 0.242188 7.06055 0.148438C6.7207 0.0507812 6.36719 0.00195312 6 0.00195312C5.44922 0.00195312 4.92969 0.107422 4.44141 0.318359C3.95703 0.525391 3.5332 0.810547 3.16992 1.17383C2.81055 1.53711 2.52539 1.96094 2.31445 2.44531C2.10352 2.92969 1.99805 3.44727 1.99805 3.99805C1.99805 4.44727 2.07031 4.88086 2.21484 5.29883C2.35938 5.71289 2.56836 6.09766 2.8418 6.45312L0.146484 9.14844C0.0488281 9.24609 0 9.36328 0 9.5C0 9.63672 0.0488281 9.75391 0.146484 9.85156C0.244141 9.94922 0.361328 9.99805 0.498047 9.99805C0.634766 9.99805 0.751953 9.94922 0.849609 9.85156L3.54492 7.15625C3.90039 7.42969 4.28516 7.63867 4.69922 7.7832C5.11719 7.92773 5.55078 8 6 8C6.55078 8 7.06836 7.89453 7.55273 7.68359C8.03711 7.47266 8.46094 7.1875 8.82422 6.82812C9.1875 6.46484 9.47266 6.04102 9.67969 5.55664ZM9 3.93945V3.99805C9 4.41211 8.92188 4.80273 8.76562 5.16992C8.60938 5.5332 8.39453 5.85156 8.12109 6.125C7.85156 6.39453 7.5332 6.60742 7.16602 6.76367C6.80273 6.91992 6.41406 6.99805 6 6.99805C5.58594 6.99805 5.19531 6.91992 4.82812 6.76367C4.46484 6.60742 4.14648 6.39453 3.87305 6.125C3.60352 5.85156 3.39062 5.5332 3.23438 5.16992C3.07812 4.80273 3 4.41211 3 3.99805C3 3.58398 3.07812 3.19531 3.23438 2.83203C3.39062 2.46484 3.60352 2.14648 3.87305 1.87695C4.14648 1.60352 4.46484 1.38867 4.82812 1.23242C5.19531 1.07617 5.58594 0.998047 6 0.998047C6.40234 0.998047 6.7832 1.07617 7.14258 1.23242C7.50586 1.38477 7.82422 1.59375 8.09766 1.85938C8.375 2.125 8.59375 2.4375 8.75391 2.79688C8.91797 3.15234 9 3.5332 9 3.93945Z</PathGeometry>
                                        </Path.Data>
                                        <Path.RenderTransform>
                                            <TransformGroup>
                                                <RotateTransform Angle="0" />
                                                <ScaleTransform ScaleX="1" ScaleY="1" />
                                            </TransformGroup>
                                        </Path.RenderTransform>
                                    </Path>
                                </Border>
                                <Button x:Name="PART_DeleteButton"
                                        Width="10"
                                        Height="10"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Style="{StaticResource SyncfusionDataGridFilterDeleteButtonStyle}"
                                        Visibility="{Binding Text,
                                                             ElementName=PART_SearchTextBox,
                                                             ConverterParameter=deletebtn,
                                                             Converter={StaticResource textBlockVisibilityConverter}}">
                                    <Button.Margin>
                                        <Thickness>0,6,15,4</Thickness>
                                    </Button.Margin>
                                </Button>
                            </Grid>
                            <Border Grid.Row="1"
                                    Margin="0,4,4,4"
                                    BorderThickness="0">
                                <Grid>
                                    <TextBlock Margin="0,25,0,0"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Top"
                                               Foreground="{TemplateBinding Foreground}"
                                               Text="{grid:GridLocalizationResourceExtension ResourceName=NoItems}"
                                               Visibility="{Binding HasItemsSource,
                                                                    RelativeSource={RelativeSource TemplatedParent},
                                                                    Converter={StaticResource ResourceKey=reverseVisibilityConverter}}" />
                                    <Grid Visibility="{Binding HasItemsSource, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ResourceKey=boolToVisiblityConverter}}">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="*" />
                                        </Grid.RowDefinitions>
                                        <Grid Grid.Row="1">
                                            <Grid.Resources>
                                                <Storyboard x:Key="LaoadingAnimation" RepeatBehavior="Forever">
                                                    <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Path" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[2].(RotateTransform.Angle)">
                                                        <EasingDoubleKeyFrame KeyTime="0" Value="0" />
                                                        <EasingDoubleKeyFrame KeyTime="0:0:5" Value="1170" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </Grid.Resources>
                                            <Path x:Name="Path"
                                                  Width="26"
                                                  Height="26"
                                                  Data="M33.091251,58.314999C35.398258,58.314999 37.268002,60.186188 37.268002,62.490997 37.268002,64.797111 35.398258,66.667 33.091251,66.667 30.786645,66.667 28.917001,64.797111 28.917001,62.490997 28.917001,60.186188 30.786645,58.314999 33.091251,58.314999z M47.2943,55.271999C49.601437,55.271999 51.471003,57.141811 51.471003,59.447948 51.471003,61.752788 49.601437,63.624 47.2943,63.624 44.989765,63.624 43.119999,61.752788 43.119999,59.447948 43.119999,57.141811 44.989765,55.271999 47.2943,55.271999z M18.6666,54.257999C21.252921,54.257999 23.352,56.354423 23.352,58.94035 23.352,61.526379 21.252921,63.624 18.6666,63.624 16.08058,63.624 13.984001,61.526379 13.984001,58.94035 13.984001,56.354423 16.08058,54.257999 18.6666,54.257999z M57.4405,45.199001C59.3416,45.199001 60.891001,46.743435 60.891001,48.651199 60.891001,50.557564 59.3416,52.102001 57.4405,52.102001 55.534201,52.102001 53.99,50.557564 53.99,48.651199 53.99,46.743435 55.534201,45.199001 57.4405,45.199001z M8.3045502,43.967003C10.890694,43.967003 12.987,46.064644 12.987,48.6507 12.987,51.236656 10.890694,53.333 8.3045502,53.333 5.7185383,53.333 3.6219997,51.236656 3.6219997,48.6507 3.6219997,46.064644 5.7185383,43.967003 8.3045502,43.967003z M61.643499,30.851999C63.544542,30.851999 65.093996,32.396133 65.093996,34.30365 65.093996,36.209869 63.544542,37.754002 61.643499,37.754002 59.737253,37.754002 58.193001,36.209869 58.193001,34.30365 58.193001,32.396133 59.737253,30.851999 61.643499,30.851999z M4.6824703,29.619999C7.268652,29.619999 9.3649998,31.717722 9.3649998,34.30365 9.3649998,36.88958 7.268652,38.986 4.6824703,38.986 2.0965385,38.986 0,36.88958 0,34.30365 0,31.717722 2.0965385,29.619999 4.6824703,29.619999z M57.440451,16.938999C59.101923,16.938999 60.455999,18.287865 60.455999,19.9543 60.455999,21.620834 59.101923,22.971001 57.440451,22.971001 55.773779,22.971001 54.425001,21.620834 54.425001,19.9543 54.425001,18.287865 55.773779,16.938999 57.440451,16.938999z M8.3045502,15.272C10.890694,15.272 12.987,17.368345 12.987,19.9543 12.987,22.540255 10.890694,24.637999 8.3045502,24.637999 5.7185383,24.637999 3.6219997,22.540255 3.6219997,19.9543 3.6219997,17.368345 5.7185383,15.272 8.3045502,15.272z M47.294703,7.0829992C48.875502,7.0829996 50.167002,8.3696136 50.167002,9.9543542 50.167002,11.540385 48.875502,12.827 47.294703,12.827 45.711302,12.827 44.425001,11.540385 44.425001,9.9543542 44.425001,8.3696136 45.711302,7.0829996 47.294703,7.0829992z M18.666401,4.0399989C21.61159,4.0399999 23.997,6.4307284 23.997001,9.3748798 23.997,12.319001 21.61159,14.710999 18.666401,14.710999 15.72391,14.710999 13.336,12.319001 13.335999,9.3748798 13.336,6.4307284 15.72391,4.0399999 18.666401,4.0399989z M33.091201,0C36.294464,-7.5211233E-08 38.891,2.59503 38.891,5.7968797 38.891,8.9987201 36.294464,11.595 33.091201,11.595 29.890533,11.595 27.294,8.9987201 27.294001,5.7968797 27.294,2.59503 29.890533,-7.5211233E-08 33.091201,0z"
                                                  Fill="#FFA0B2C9"
                                                  RenderTransformOrigin="0.5,0.5"
                                                  Stretch="Uniform"
                                                  Visibility="{Binding IsItemSourceLoaded,
                                                                       Mode=TwoWay,
                                                                       RelativeSource={RelativeSource TemplatedParent},
                                                                       ConverterParameter={StaticResource LaoadingAnimation},
                                                                       Converter={StaticResource ResourceKey=loadingVisiblityConverter}}">
                                                <Path.RenderTransform>
                                                    <TransformGroup>
                                                        <ScaleTransform />
                                                        <SkewTransform />
                                                        <RotateTransform />
                                                        <TranslateTransform />
                                                    </TransformGroup>
                                                </Path.RenderTransform>
                                            </Path>

                                            <ItemsControl x:Name="PART_ItemsControl"
                                                          Height="{TemplateBinding Height}"
                                                          Background="{TemplateBinding Background}"
                                                          HorizontalAlignment="Stretch"
                                                          VerticalAlignment="Stretch"
                                                          IsTabStop="False"
                                                          HorizontalContentAlignment="Stretch"
                                                          VerticalContentAlignment="Stretch"
                                                          ItemTemplate="{TemplateBinding ItemTemplate}"
                                                          ItemsSource="{TemplateBinding ItemsSource}"
                                                          KeyboardNavigation.TabNavigation="Continue"
                                                          Visibility="{Binding IsItemSourceLoaded,
                                                                               RelativeSource={RelativeSource TemplatedParent},
                                                                               Converter={StaticResource ResourceKey=boolToVisiblityConverter}}">
                                                <ItemsControl.Template>
                                                    <ControlTemplate TargetType="{x:Type ItemsControl}">
                                                        <Border Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                Padding="{TemplateBinding Padding}">
                                                            <Grid>
                                                                <ScrollViewer HorizontalAlignment="Stretch"
                                                                              CanContentScroll="True"
                                                                              PanningMode="Both"
                                                                              Background="{TemplateBinding Background}"
                                                                              HorizontalScrollBarVisibility="Auto"
                                                                              SnapsToDevicePixels="true"
                                                                              VerticalScrollBarVisibility="Auto">
                                                                    <ItemsPresenter x:Name="PART_ItemsPresenter"
                                                                                    Margin="{Binding FontSize,
                                                                                                     RelativeSource={RelativeSource TemplatedParent},
                                                                                                     Converter={StaticResource heightToMarginConverter}, ConverterParameter=1}"
                                                                                    ClipToBounds="True"
                                                                                    Focusable="False" />
                                                                </ScrollViewer>
                                                                <TextBlock Margin="{Binding ElementName=PART_ItemsPresenter,
                                                                                            Path=Margin}"
                                                                           HorizontalAlignment="Center"
                                                                           VerticalAlignment="Top"
                                                                           Foreground="{StaticResource ContentForegroundAlt1}"
                                                                           FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                                                           FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                                                                           FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                                           Text="{grid:GridLocalizationResourceExtension ResourceName=NoMatches}"
                                                                           Visibility="{Binding ItemsSource,
                                                                                                RelativeSource={RelativeSource TemplatedParent},
                                                                                                ConverterParameter=NoMatchText,
                                                                                                Converter={StaticResource ResourceKey=listItemsVisiblityConverter}}" />
                                                            </Grid>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="skinManager:SfSkinManager.SizeMode" Value="Touch">
                                                                <Setter TargetName="PART_ItemsPresenter" Property="Margin" Value="0,34,4,4"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </ItemsControl.Template>
                                                <ItemsControl.ItemsPanel>
                                                    <ItemsPanelTemplate>
                                                        <VirtualizingStackPanel HorizontalAlignment="Stretch" />
                                                    </ItemsPanelTemplate>
                                                </ItemsControl.ItemsPanel>

                                            </ItemsControl>
                                        </Grid>
                                        <Border Grid.Row="1"
                                                Margin="0,0,20,0"
                                                VerticalAlignment="Top"
                                                
                                                Visibility="{Binding ItemsSource,
                                                                     ElementName=PART_ItemsControl,
                                                                     ConverterParameter=ItemsControl,
                                                                     Converter={StaticResource ResourceKey=listItemsVisiblityConverter}}">
                                            <CheckBox x:Name="PART_CheckBox"
                                                      Height="24"
                                                      HorizontalAlignment="Stretch"
                                                      VerticalAlignment="Center"
                                                      Focusable="True"
                                                      Content="{grid:GridLocalizationResourceExtension ResourceName=SelectAll}"
                                                      IsThreeState="True"
                                                      Visibility="{Binding Visibility,
                                                                           ElementName=PART_ItemsControl}" >
                                                <CheckBox.Margin>
                                                    <Thickness>12,2,0,2</Thickness>
                                                </CheckBox.Margin>
                                            </CheckBox>
                                        </Border>
                                    </Grid>
                                </Grid>
                            </Border>
                        </Grid>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinManager:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                            <Setter TargetName="PART_ItemsControl" Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
                        </Trigger>
                       
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:CheckboxFilterControl}" BasedOn="{StaticResource SyncfusionCheckboxFilterControlStyle}"/>
    
    <Style TargetType="grid:GridFilterControl" x:Key="SyncfusionGridFilterControlStyle">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Background" Value="{StaticResource PopupBackground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:GridFilterControl">
                    <Border>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="FilterControlStates">
                                <VisualState x:Name="AdvancedFilter">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_AdvancedFilterControl" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Visible}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CheckboxFilterControl" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="CheckboxFilter">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_AdvancedFilterControl" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CheckboxFilterControl" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Visible}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Popup x:Name="PART_FilterPopup"
                               AllowsTransparency="True"
                               IsOpen="{Binding IsOpen,
                                                Mode=TwoWay,
                                                RelativeSource={RelativeSource TemplatedParent}}"
                               KeyboardNavigation.DirectionalNavigation="Cycle"
                               Placement="Bottom"
                               PlacementTarget="{Binding ElementName=PART_FilterToggleButton}"
                               StaysOpen="False"
                               grid:VisualContainer.WantsMouseInput="True"
                               PopupAnimation="Slide">
                            <ContentControl ContentTemplate="{TemplateBinding ContentTemplate}" IsTabStop="False">
                                <Border x:Name="PART_FilterPopUpBorder"
                                        Width="{Binding FilterPopupWidth,
                                                        Mode=TwoWay,
                                                        RelativeSource={RelativeSource TemplatedParent}}"
                                        Height="{Binding FilterPopupHeight,
                                                         Mode=TwoWay,
                                                         RelativeSource={RelativeSource TemplatedParent}}"
                                        MinWidth="202"
                                        MinHeight="384"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                        Margin="14,0,14,14"
                                        Effect="{StaticResource Default.ShadowDepth4}">
                                    <Border.Padding>
                                        <Thickness>0</Thickness>
                                    </Border.Padding>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="47" />
                                        </Grid.RowDefinitions>
                                        <StackPanel>
                                            <StackPanel.Margin>
                                                <Thickness>1,3,1,0</Thickness>
                                            </StackPanel.Margin>
                                            <grid:SortButton x:Name="PART_SortAscendingButton"
                                                              Content="{TemplateBinding AscendingSortString}"
                                                              Visibility="{TemplateBinding SortOptionVisibility}"
                                                              Style="{StaticResource SyncfusionDataGridFilterControlSortButtonStyle}">
                                                <grid:SortButton.Icon>
                                                    <Grid Width="20"
                                                          Height="20">
                                                        <Path x:Name="Ascending" 
                                                              Fill="{StaticResource IconColor}" 
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Height="12" 
                                                              Width="12" 
                                                              Stretch="Fill">
                                                            <Path.Margin>
                                                                <Thickness>4,4,3,4</Thickness>
                                                            </Path.Margin>
                                                            <Path.Data>
                                                                <PathGeometry>M8.6837 11.0117L8.68372 11.0117L8.68531 11.0108C8.73566 10.9818 8.78461 10.9415 8.83235 10.8912L10.9191 8.68847C11.006 8.59678 11.05 8.48685 11.05 8.36111C11.05 8.23538 11.006 8.12544 10.9191 8.03376C10.8322 7.94202 10.7268 7.89444 10.6053 7.89444C10.4837 7.89444 10.3783 7.94202 10.2914 8.03376L8.94474 9.44913V1.41667C8.94474 1.29093 8.90071 1.18099 8.81385 1.08931C8.72694 0.997576 8.62157 0.95 8.5 0.95C8.37843 0.95 8.27306 0.997576 8.18615 1.08931C8.09929 1.18099 8.05526 1.29093 8.05526 1.41667V9.44913L6.70858 8.03376C6.62168 7.94202 6.51631 7.89444 6.39474 7.89444C6.27317 7.89444 6.1678 7.94202 6.08089 8.03376C5.99403 8.12544 5.95 8.23538 5.95 8.36111C5.95 8.48685 5.99403 8.59678 6.08089 8.68847L8.16765 10.8912C8.2148 10.9409 8.26113 10.9808 8.30672 11.0097L8.30653 11.01L8.31184 11.0125C8.36558 11.0383 8.42896 11.05 8.5 11.05C8.57075 11.05 8.63303 11.0384 8.6837 11.0117ZM4.2234 5.51813L4.2358 5.55H4.27H4.828H4.90101L4.87462 5.48192L3.20662 1.17992L3.19424 1.148H3.16H2.674H2.6398L2.6274 1.17987L0.953403 5.48187L0.926892 5.55H1H1.546H1.58034L1.59267 5.51795L2.09034 4.224H3.7198L4.2234 5.51813ZM3.06518 2.41556L3.52016 3.644H2.30815L2.76882 2.41556L2.76885 2.41557L2.76943 2.41381C2.78558 2.36538 2.80364 2.3072 2.82359 2.23938C2.84793 2.17029 2.87015 2.09545 2.8903 2.01494C2.89715 1.99096 2.90384 1.96715 2.91036 1.94349C2.91337 1.9548 2.91646 1.96651 2.91962 1.97862L2.91955 1.97864L2.92035 1.98116C2.94842 2.06937 2.97649 2.15559 3.00457 2.23981C3.03252 2.32367 3.05277 2.38245 3.06514 2.41545C3.06516 2.41548 3.06517 2.41552 3.06518 2.41556ZM4.48367 10.834H4.53367V10.784V10.304V10.254H4.48367H2.26091L4.46532 6.93567L4.47367 6.92309V6.908V6.5V6.45H4.42367H1.58567H1.53567V6.5V6.98V7.03H1.58567H3.67643L1.47202 10.3483L1.46367 10.3609V10.376V10.784V10.834H1.51367H4.48367Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </grid:SortButton.Icon>
                                            </grid:SortButton>

                                            <grid:SortButton x:Name="PART_SortDescendingButton"
                                                              Content="{TemplateBinding DescendingSortString}"
                                                              Visibility="{TemplateBinding SortOptionVisibility}"
                                                              Style="{StaticResource SyncfusionDataGridFilterControlSortButtonStyle}">
                                                <grid:SortButton.Icon>
                                                    <Grid  Width="20"
                                                          Height="20">
                                                        <Path x:Name="Decending" 
                                                              Fill="{StaticResource IconColor}" 
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                               Height="12" 
                                                              Width="12"  
                                                              Stretch="Fill">
                                                            <Path.Margin>
                                                                <Thickness>4,4,3,4</Thickness>
                                                            </Path.Margin>
                                                            <Path.Data>
                                                                <PathGeometry>M8.6837 11.0117L8.68372 11.0117L8.68531 11.0108C8.73566 10.9818 8.78461 10.9415 8.83235 10.8912L10.9191 8.68847C11.006 8.59678 11.05 8.48685 11.05 8.36111C11.05 8.23538 11.006 8.12544 10.9191 8.03376C10.8322 7.94202 10.7268 7.89444 10.6053 7.89444C10.4837 7.89444 10.3783 7.94202 10.2914 8.03376L8.94474 9.44913V1.41667C8.94474 1.29093 8.90071 1.18099 8.81385 1.08931C8.72694 0.997576 8.62157 0.95 8.5 0.95C8.37843 0.95 8.27306 0.997576 8.18615 1.08931C8.09929 1.18099 8.05526 1.29093 8.05526 1.41667V9.44913L6.70858 8.03376C6.62168 7.94202 6.51631 7.89444 6.39474 7.89444C6.27317 7.89444 6.1678 7.94202 6.08089 8.03376C5.99403 8.12544 5.95 8.23538 5.95 8.36111C5.95 8.48685 5.99403 8.59678 6.08089 8.68847L8.16765 10.8912C8.2148 10.9409 8.26113 10.9808 8.30672 11.0097L8.30653 11.01L8.31184 11.0125C8.36558 11.0383 8.42896 11.05 8.5 11.05C8.57075 11.05 8.63303 11.0384 8.6837 11.0117ZM4.2234 10.8023L4.2358 10.8342H4.27H4.828H4.90101L4.87462 10.7661L3.20662 6.4641L3.19424 6.43218H3.16H2.674H2.6398L2.6274 6.46405L0.953403 10.766L0.926892 10.8342H1H1.546H1.58034L1.59267 10.8021L2.09034 9.50818H3.71981L4.2234 10.8023ZM2.91962 7.2628L2.91955 7.26282L3.112 7.68218L3.06511 7.69955L3.06514 7.69961C3.06515 7.69965 3.06517 7.69969 3.06518 7.69974L3.52016 8.92818H2.30815L2.76882 7.69974L2.76885 7.69975L2.76943 7.69799C2.78558 7.64956 2.80364 7.59138 2.82359 7.52356C2.84793 7.45447 2.87015 7.37963 2.8903 7.29912C2.89715 7.27514 2.90384 7.25133 2.91036 7.22767C2.91337 7.23898 2.91646 7.25069 2.91962 7.2628ZM1.46367 5.48224V5.53224H1.51367H4.48367H4.53367V5.48224V5.00224V4.95224H4.48367H2.26091L4.46532 1.63391L4.47367 1.62134V1.60624V1.19824V1.14824H4.42367H1.58567H1.53567V1.19824V1.67824V1.72824H1.58567H3.67643L1.47202 5.04658L1.46367 5.05915V5.07424V5.48224Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </Grid>
                                                </grid:SortButton.Icon>
                                            </grid:SortButton>

                                            <Button x:Name="PART_ClearFilterButton"
                                                    HorizontalAlignment="Stretch"
                                                    VerticalAlignment="Stretch"
                                                    HorizontalContentAlignment="Left"
                                                    Content="{grid:GridLocalizationResourceExtension ResourceName=ClearFilter}"
                                                    FontWeight="Normal"
                                                    IsEnabled="True"
                                                    Style="{StaticResource SyncfusionDataGridClearFilterButtonStyle}" />

                                            <TextBlock x:Name="AdvancedFiltersText"
                                                       HorizontalAlignment="Stretch"
                                                       VerticalAlignment="Center"
                                                       Text="{Binding FilterColumnType,
                                                                      RelativeSource={RelativeSource TemplatedParent}}"
                                                       Visibility="{Binding FilterMode,
                                                                            RelativeSource={RelativeSource TemplatedParent},
                                                                            Converter={StaticResource ResourceKey=advancedFilterButtonVisibilityConverter},
                                                                            ConverterParameter=reverse}"
                                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                                       FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}">
                                                <TextBlock.Margin>
                                                    <Thickness>26,5,4,2</Thickness>
                                                </TextBlock.Margin>
                                            </TextBlock>

                                            <Button x:Name="PART_AdvancedFilterButton"
                                                    HorizontalAlignment="Stretch"
                                                    VerticalAlignment="Stretch"
                                                    HorizontalContentAlignment="Left"
                                                    Content="{Binding FilterColumnType,
                                                                      RelativeSource={RelativeSource TemplatedParent}}"
                                                    Style="{StaticResource SyncfusionDataGridAdvancedFilterButtonStyle}"
                                                    Visibility="{Binding FilterMode,
                                                                         RelativeSource={RelativeSource TemplatedParent},
                                                                         Converter={StaticResource ResourceKey=advancedFilterButtonVisibilityConverter}}" />

                                        </StackPanel>
                                        <Grid Grid.Row="1">
                                            <grid:CheckboxFilterControl x:Name="PART_CheckboxFilterControl"
                                                                           FontFamily="{TemplateBinding FontFamily}"
                                                                           FontSize="{TemplateBinding FontSize}"
                                                                           FontStyle="{TemplateBinding FontStyle}"
                                                                           FontWeight="{TemplateBinding FontWeight}"
                                                                         Focusable="False"
                                                                         Visibility="{Binding IsAdvancedFilterVisible,
                                                                                              RelativeSource={RelativeSource TemplatedParent},
                                                                                              Converter={StaticResource reverseVisibilityConverter}}" />

                                            <grid:AdvancedFilterControl x:Name="PART_AdvancedFilterControl"
                                                                         Focusable="False"
                                                                         FontFamily="{TemplateBinding FontFamily}"
                                                                         FontSize="{TemplateBinding FontSize}"
                                                                         FontStyle="{TemplateBinding FontStyle}"
                                                                         Visibility="{Binding IsAdvancedFilterVisible,
                                                                                              RelativeSource={RelativeSource TemplatedParent},
                                                                                              Converter={StaticResource boolToVisiblityConverter}}" />
                                            <Grid.Margin>
                                                <Thickness>4,0,9,0</Thickness>
                                            </Grid.Margin>
                                        </Grid>
                                        <Border Grid.Row="2"
                                                BorderBrush="{StaticResource BorderAlt}"
                                                BorderThickness="0,0,0,1"
                                                />

                                        <StackPanel Grid.Row="3">
                                            <StackPanel HorizontalAlignment="Right"
                                                        Orientation="Horizontal" >
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                               
                                                    <Button x:Name="PART_OkButton"
                                                            Grid.Column="0"
                                                            Height="24"
                                                            Width="84"
                                                            Content="{grid:GridLocalizationResourceExtension ResourceName=OK}"
                                                            IsEnabled="False"
                                                            Visibility="{Binding ImmediateUpdateColumnFilter,
                                                                                    RelativeSource={RelativeSource TemplatedParent},
                                                                                    Converter={StaticResource ResourceKey=boolToVisiblityConverter},
                                                                                    ConverterParameter=InverseVisiblity}" 
                                                            Style="{StaticResource WPFPrimaryButtonStyle}">
                                                        <Button.Margin>
                                                            <Thickness>12,12,6,12</Thickness>
                                                        </Button.Margin>
                                                    </Button>
                                                    <Button x:Name="PART_CancelButton"
                                                            Grid.Column="1"
                                                            Height="24"
                                                            Width="84"
                                                            Content="{Binding ImmediateUpdateColumnFilter,
                                                                            RelativeSource={RelativeSource TemplatedParent},
                                                                            Converter={StaticResource resourceNameConverter}}"
                                                            >
                                                        <Button.Margin>
                                                            <Thickness>6,12,12,12</Thickness>
                                                        </Button.Margin>
                                                    </Button>
                                                </Grid>
                                            </StackPanel>
                                            <Border Height="10"
                                                    BorderThickness="0"
                                                    Visibility="Hidden">
                                                <Thumb x:Name="PART_ThumbGripper"
                                                       HorizontalAlignment="Right"
                                                       Cursor="SizeNWSE">
                                                    <Thumb.Margin>
                                                        <Thickness>0,0,1,0</Thickness>
                                                    </Thumb.Margin>
                                                    <Thumb.Template>
                                                        <ControlTemplate>
                                                            <Grid Background="Transparent">
                                                                <Path Width="8"
                                                                       Height="8"
                                                                       Data="M36.396,36.017 L47.901,36.017 47.901,47.521999 36.396,47.521999 z M18.198,36.017 L29.716,36.017 29.716,47.521999 18.198,47.521999 z M0,36.017 L11.511999,36.017 11.511999,47.521999 0,47.521999 z M36.396,18.191001 L47.901,18.191001 47.901,29.696 36.396,29.696 z M18.198,18.191 L29.716,18.191 29.716,29.696 18.198,29.696 z M36.396,0 L47.901,0 47.901,11.512 36.396,11.512 z"
                                                                       Fill="{StaticResource IconColorDisabled}"
                                                                       Stretch="Fill" />
                                                               
                                                            </Grid>
                                                        </ControlTemplate>
                                                    </Thumb.Template>
                                                </Thumb>
                                            </Border>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </ContentControl>
                        </Popup>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinManager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="Height" TargetName="PART_FilterPopUpBorder" Value="410"/>
                            <Setter Property="Margin" TargetName="PART_OkButton" Value="12,6,6,12"/>
                            <Setter Property="Margin" TargetName="PART_CancelButton" Value="6,6,12,12"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:GridFilterControl}" BasedOn="{StaticResource SyncfusionGridFilterControlStyle}"/>

    <!--Advanced Filter Style-->

    <Style x:Key="SyncfusionDataGridFilterCasingToggleButtonStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness">
            <Setter.Value>
                <Thickness>1</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Padding" Value="1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Grid x:Name="PART_CasingButtonPresenter" Background="Transparent">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CasingInnerBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CasingInnerBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_CasingInnerBorder" Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="0.5" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CasingInnerBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CasingInnerBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="FocusStates">
                                <VisualState x:Name="Focused">
                                </VisualState>
                                <VisualState x:Name="Unfocused" />
                                <VisualState x:Name="PointerFocused" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CaseSensitiveStates">
                                <VisualState x:Name="CaseSensitive">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CasingInnerBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CasingInnerBorder" Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="NotCaseSensitive">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CasingInnerBorder" Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                <DiscreteObjectKeyFrame.Value>
                                                    <SolidColorBrush Color="Transparent" />
                                                </DiscreteObjectKeyFrame.Value>
                                            </DiscreteObjectKeyFrame>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="PART_CasingInnerBorder"
                                Width="{TemplateBinding Width}"
                                Height="{TemplateBinding Height}"
                                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter Margin="{TemplateBinding Padding}"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              OpacityMask="{x:Null}"
                                              RecognizesAccessKey="True"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" >
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="PART_CasingInnerBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionDataGridFilterDatePickerStyle" TargetType="{x:Type DatePicker}" BasedOn="{StaticResource WPFDatePickerStyle}">
        <Setter Property="IsTodayHighlighted" Value="True" />
        <Setter Property="SelectedDateFormat" Value="Short" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="BorderThickness">
            <Setter.Value>
                <Thickness>1</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DatePicker}">
                    <Border x:Name="PART_Border"
                            Background="Transparent"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">

                        <Grid x:Name="PART_Root"
                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                            <Grid.Resources>
                                <ControlTemplate x:Key="DropDownButtonTemplate" TargetType="{x:Type Button}">
                                    <Grid>
                                        <Grid Width="24"
                                              Height="24"
                                              Margin="0"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Background="Transparent"
                                              FlowDirection="LeftToRight">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="20*" />
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="23*" />
                                            </Grid.RowDefinitions>
                                            <Path x:Name="date_filter"
                                                  Width="12"
                                                  Height="12"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"
                                                  Fill="{StaticResource IconColor}"
                                                  Stretch="Fill" >
                                                <Path.Data>
                                                    <PathGeometry>M11.4092 0.875C11.637 0.875 11.8535 0.922852 12.0586 1.01855C12.2682 1.1097 12.4505 1.23503 12.6055 1.39453C12.765 1.54948 12.8903 1.73177 12.9814 1.94141C13.0771 2.14648 13.125 2.36296 13.125 2.59082L13.125 11.4092C13.125 11.637 13.0771 11.8558 12.9814 12.0654C12.8903 12.2705 12.765 12.4528 12.6055 12.6123C12.4505 12.7673 12.2682 12.8926 12.0586 12.9883C11.8535 13.0794 11.637 13.125 11.4092 13.125L2.59082 13.125C2.36296 13.125 2.14421 13.0794 1.93457 12.9883C1.72949 12.8926 1.5472 12.7673 1.3877 12.6123C1.23275 12.4528 1.10742 12.2705 1.01172 12.0654C0.920573 11.8558 0.875 11.637 0.875 11.4092L0.875 2.59082C0.875 2.36296 0.920573 2.14648 1.01172 1.94141C1.10742 1.73177 1.23275 1.54948 1.3877 1.39453C1.5472 1.23503 1.72949 1.1097 1.93457 1.01855C2.14421 0.922852 2.36296 0.875 2.59082 0.875L11.4092 0.875ZM2.625 1.75C2.50195 1.75 2.38802 1.77279 2.2832 1.81836C2.17839 1.86393 2.08496 1.92773 2.00293 2.00977C1.92546 2.08724 1.86393 2.17839 1.81836 2.2832C1.77279 2.38802 1.75 2.50195 1.75 2.625L1.75 3.5L12.25 3.5V2.625C12.25 2.50651 12.2272 2.39486 12.1816 2.29004C12.1361 2.18066 12.0723 2.08724 11.9902 2.00977C11.9128 1.92773 11.8193 1.86393 11.71 1.81836C11.6051 1.77279 11.4935 1.75 11.375 1.75L2.625 1.75ZM11.375 12.25C11.498 12.25 11.612 12.2272 11.7168 12.1816C11.8216 12.1361 11.9128 12.0745 11.9902 11.9971C12.0723 11.915 12.1361 11.8216 12.1816 11.7168C12.2272 11.612 12.25 11.498 12.25 11.375L12.25 4.375L1.75 4.375L1.75 11.375C1.75 11.498 1.77279 11.6143 1.81836 11.7236C1.86393 11.8285 1.92546 11.9196 2.00293 11.9971C2.0804 12.0745 2.17155 12.1361 2.27637 12.1816C2.38574 12.2272 2.50195 12.25 2.625 12.25L11.375 12.25ZM3.5 7C3.5 6.87695 3.52279 6.76302 3.56836 6.6582C3.61393 6.55339 3.67546 6.46224 3.75293 6.38477C3.83496 6.30273 3.92839 6.23893 4.0332 6.19336C4.14258 6.14779 4.25879 6.125 4.38184 6.125C4.50488 6.125 4.61882 6.14779 4.72363 6.19336C4.82845 6.23893 4.9196 6.30046 4.99707 6.37793C5.07454 6.4554 5.13607 6.54655 5.18164 6.65137C5.22721 6.75618 5.25 6.87012 5.25 6.99316C5.25 7.11621 5.22721 7.23242 5.18164 7.3418C5.13607 7.44661 5.07227 7.54004 4.99023 7.62207C4.91276 7.69954 4.82161 7.76107 4.7168 7.80664C4.61198 7.85221 4.49805 7.875 4.375 7.875C4.25195 7.875 4.13574 7.85221 4.02637 7.80664C3.92155 7.76107 3.8304 7.69954 3.75293 7.62207C3.67546 7.5446 3.61393 7.45345 3.56836 7.34863C3.52279 7.23926 3.5 7.12305 3.5 7ZM6.125 7C6.125 6.87695 6.14779 6.76302 6.19336 6.6582C6.23893 6.55339 6.30046 6.46224 6.37793 6.38477C6.45996 6.30273 6.55339 6.23893 6.6582 6.19336C6.76758 6.14779 6.88379 6.125 7.00684 6.125C7.12988 6.125 7.24382 6.14779 7.34863 6.19336C7.45345 6.23893 7.5446 6.30046 7.62207 6.37793C7.69954 6.4554 7.76107 6.54655 7.80664 6.65137C7.85221 6.75618 7.875 6.87012 7.875 6.99316C7.875 7.11621 7.85221 7.23242 7.80664 7.3418C7.76107 7.44661 7.69727 7.54004 7.61523 7.62207C7.53776 7.69954 7.44661 7.76107 7.3418 7.80664C7.23698 7.85221 7.12305 7.875 7 7.875C6.87695 7.875 6.76074 7.85221 6.65137 7.80664C6.54655 7.76107 6.4554 7.69954 6.37793 7.62207C6.30046 7.5446 6.23893 7.45345 6.19336 7.34863C6.14779 7.23926 6.125 7.12305 6.125 7ZM10.5 6.99316C10.5 7.11621 10.4772 7.23242 10.4316 7.3418C10.3861 7.44661 10.3223 7.54004 10.2402 7.62207C10.1628 7.69954 10.0716 7.76107 9.9668 7.80664C9.86198 7.85221 9.74805 7.875 9.625 7.875C9.50195 7.875 9.38574 7.85221 9.27637 7.80664C9.17155 7.76107 9.0804 7.69954 9.00293 7.62207C8.92546 7.5446 8.86393 7.45345 8.81836 7.34863C8.77279 7.23926 8.75 7.12305 8.75 7C8.75 6.87695 8.77279 6.76302 8.81836 6.6582C8.86393 6.55339 8.92546 6.46224 9.00293 6.38477C9.08496 6.30273 9.17839 6.23893 9.2832 6.19336C9.39258 6.14779 9.50879 6.125 9.63184 6.125C9.75488 6.125 9.86882 6.14779 9.97363 6.19336C10.0785 6.23893 10.1696 6.30046 10.2471 6.37793C10.3245 6.4554 10.3861 6.54655 10.4316 6.65137C10.4772 6.75618 10.5 6.87012 10.5 6.99316ZM5.25 9.625C5.25 9.74805 5.22721 9.86198 5.18164 9.9668C5.13607 10.0716 5.07227 10.165 4.99023 10.2471C4.91276 10.3245 4.81934 10.3861 4.70996 10.4316C4.60514 10.4772 4.49121 10.5 4.36816 10.5C4.24512 10.5 4.13118 10.4772 4.02637 10.4316C3.92155 10.3861 3.8304 10.3245 3.75293 10.2471C3.67546 10.1696 3.61393 10.0785 3.56836 9.97363C3.52279 9.86882 3.5 9.75488 3.5 9.63184C3.5 9.50879 3.52279 9.39486 3.56836 9.29004C3.61393 9.18066 3.67546 9.08724 3.75293 9.00977C3.83496 8.92773 3.92839 8.86393 4.0332 8.81836C4.13802 8.77279 4.25195 8.75 4.375 8.75C4.49805 8.75 4.61198 8.77279 4.7168 8.81836C4.82617 8.86393 4.9196 8.92546 4.99707 9.00293C5.07454 9.0804 5.13607 9.17383 5.18164 9.2832C5.22721 9.38802 5.25 9.50195 5.25 9.625ZM7.875 9.625C7.875 9.74805 7.85221 9.86198 7.80664 9.9668C7.76107 10.0716 7.69727 10.165 7.61523 10.2471C7.53776 10.3245 7.44434 10.3861 7.33496 10.4316C7.23014 10.4772 7.11621 10.5 6.99316 10.5C6.87012 10.5 6.75618 10.4772 6.65137 10.4316C6.54655 10.3861 6.4554 10.3245 6.37793 10.2471C6.30046 10.1696 6.23893 10.0785 6.19336 9.97363C6.14779 9.86882 6.125 9.75488 6.125 9.63184C6.125 9.50879 6.14779 9.39486 6.19336 9.29004C6.23893 9.18066 6.30046 9.08724 6.37793 9.00977C6.45996 8.92773 6.55339 8.86393 6.6582 8.81836C6.76302 8.77279 6.87695 8.75 7 8.75C7.12305 8.75 7.23698 8.77279 7.3418 8.81836C7.45117 8.86393 7.5446 8.92546 7.62207 9.00293C7.69954 9.0804 7.76107 9.17383 7.80664 9.2832C7.85221 9.38802 7.875 9.50195 7.875 9.625Z</PathGeometry>
                                                </Path.Data>
                                            </Path>
                                        </Grid>
                                    </Grid>
                                </ControlTemplate>
                            </Grid.Resources>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Button x:Name="PART_Button"
                                    Grid.Row="0"
                                    Grid.Column="1"
                                    Margin="-1,0,0,1"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Top"
                                    Focusable="False"
                                    Foreground="{TemplateBinding Foreground}"
                                    Template="{StaticResource DropDownButtonTemplate}" />

                            <Grid x:Name="PART_DisabledVisual"
                                  Grid.Row="0"
                                  Grid.Column="0"
                                  Grid.ColumnSpan="2"
                                  IsHitTestVisible="False"
                                  Opacity="0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Rectangle Grid.Row="0"
                                           Grid.Column="0"
                                           RadiusX="1"
                                           RadiusY="1" />
                                <Rectangle Grid.Row="0"
                                           Grid.Column="1"
                                           Width="19"
                                           Height="18"
                                           Margin="3,0,3,0"
                                           RadiusX="1"
                                           RadiusY="1" />
                                <Popup x:Name="PART_Popup"
                                       AllowsTransparency="True"
                                       Placement="Bottom"
                                       PlacementTarget="{Binding ElementName=PART_Button}"
                                       StaysOpen="False" />
                            </Grid>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True" SourceName="PART_Button">
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="grid:AdvancedFilterControl" x:Key="SyncfusionAdvancedFilterControlStyle">
        <Setter Property="MaxHeight" Value="275" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="grid:AdvancedFilterControl">
                    <Grid Height="{TemplateBinding Height}">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="AdvancedFilterStates">
                                <VisualState x:Name="TextFilter">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_DatePicker1" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_DatePicker2" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="NumberFilter">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CasingButton1" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CasingButton2" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_DatePicker1" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_DatePicker2" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="DateFilter">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CasingButton1" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_CasingButton2" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_DatePicker1" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Visible}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_DatePicker2" Storyboard.TargetProperty="(FrameworkElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Visible}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="15" />
                            </Grid.RowDefinitions>
							<Grid.Margin>
                                <Thickness>0</Thickness>
                            </Grid.Margin>
                            <TextBlock Grid.Row="0"
                                       HorizontalAlignment="Stretch"
                                       VerticalAlignment="Center"
                                       Foreground="{StaticResource ContentForeground}"
                                       Height="14"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                       FontSize="{StaticResource Windows11Dark.CaptionText}"
                                       FontWeight="{StaticResource Windows11Dark.FontWeightMedium}"
                                       Text="{grid:GridLocalizationResourceExtension ResourceName=ShowRowsWhere}" >
                                <TextBlock.Margin>
                                    <Thickness>7,8,0,6</Thickness>
                                </TextBlock.Margin>
                            </TextBlock>
                            <ComboBox x:Name="PART_MenuComboBox1"
                                      Grid.Row="1"
                                      Margin="5,0,0,0"
                                      Height="24"
                                      ItemsSource="{Binding FilterTypeComboItems,
                                                            RelativeSource={RelativeSource TemplatedParent}}"
                                      SelectedItem="{Binding FilterType1,
                                                             RelativeSource={RelativeSource TemplatedParent},
                                                             Mode=TwoWay,
                                                             UpdateSourceTrigger=PropertyChanged}"/>

                            <Grid Grid.Row="2" Margin="5,0,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>

                                <ComboBox x:Name="PART_ComboBox1"
                                          Grid.Column="0"
                                          Height="24"
                                          DisplayMemberPath="DisplayText"
                                          IsEditable="True"
                                          IsEnabled="{Binding FilterType1,
                                                              RelativeSource={RelativeSource TemplatedParent},
                                                              Converter={StaticResource filterValueComboEnableConverter}}"
                                          IsTextSearchCaseSensitive="True"
                                          ItemsSource="{Binding ComboItemsSource,
                                                                RelativeSource={RelativeSource TemplatedParent},
                                                                Mode=TwoWay,
                                                                UpdateSourceTrigger=PropertyChanged}"
                                          SelectedItem="{Binding FilterSelectedItem1,
                                                                 RelativeSource={RelativeSource TemplatedParent}}"
                                          Text="{Binding FilterValue1,
                                                         RelativeSource={RelativeSource TemplatedParent},
                                                         Mode=TwoWay,
                                                         ValidatesOnDataErrors=true,
                                                         NotifyOnValidationError=true}"
                                          Visibility="{Binding CanGenerateUniqueItems,
                                                               RelativeSource={RelativeSource TemplatedParent},
                                                               Converter={StaticResource boolToVisiblityConverter}}" />

                                <TextBox x:Name="PART_TextBox1"
                                         Grid.Column="0"
                                         Height="24"
                                         VerticalContentAlignment="Center"
                                         IsEnabled="{Binding FilterType1,
                                                             RelativeSource={RelativeSource TemplatedParent},
                                                             Converter={StaticResource filterValueComboEnableConverter}}"
                                         Text="{Binding FilterValue1,
                                                        RelativeSource={RelativeSource TemplatedParent},
                                                        Mode=TwoWay,
                                                        UpdateSourceTrigger=PropertyChanged,
                                                        ValidatesOnDataErrors=true,
                                                        NotifyOnValidationError=true}"
                                         Visibility="{Binding CanGenerateUniqueItems,
                                                              RelativeSource={RelativeSource TemplatedParent},
                                                              Converter={StaticResource reverseVisibilityConverter}}" />

                                <DatePicker x:Name="PART_DatePicker1"
                                            Grid.Column="1"
                                            Height="24"
                                            Width="24"
                                            Margin="5 0 0 0"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Style="{StaticResource SyncfusionDataGridFilterDatePickerStyle}"
                                            IsEnabled="{Binding FilterType1,
                                                                RelativeSource={RelativeSource TemplatedParent},
                                                                Converter={StaticResource filterValueComboEnableConverter}}"
                                            SelectedDate="{Binding DateFilterValue1,
                                                                   RelativeSource={RelativeSource TemplatedParent},
                                                                   Mode=TwoWay,
                                                                   UpdateSourceTrigger=PropertyChanged}"/>

                                <ToggleButton x:Name="PART_CasingButton1"
                                              Grid.Column="1"
                                              Height="24"
                                              Width="24"
                                              Margin="5 0 0 0"
                                              Foreground="{TemplateBinding Foreground}"
                                              IsChecked="{Binding IsCaseSensitive1,
                                                                  RelativeSource={RelativeSource TemplatedParent},
                                                                  Mode=TwoWay}"
                                              IsEnabled="{Binding FilterType1,
                                                                  RelativeSource={RelativeSource TemplatedParent},
                                                                    Converter={StaticResource filterValueComboEnableConverter}}"
                                              Style="{StaticResource SyncfusionDataGridFilterCasingToggleButtonStyle}"
                                              Visibility="{Binding CasingButtonVisibility,
                                                                   RelativeSource={RelativeSource TemplatedParent},
                                                                   Converter={StaticResource reverseVisibilityConverter}}">
                                    <ToggleButton.Content>
                                        <Grid Width="20">
                                            <Path Width="14.98"
                                                  Height="9.97"
                                                  HorizontalAlignment="Left"
                                                  VerticalAlignment="Bottom"
                                                  Fill="{StaticResource IconColor}"
                                                  Stretch="Fill" >
                                                <Path.Margin>
                                                    <Thickness>2.5,0,2.5,0</Thickness>
                                                </Path.Margin>
                                                <Path.Data>
                                                    <PathGeometry>M11.0625 0.197266L7.33691 10H8.34863L9.37402 7.16309H13.626L14.6787 10H15.6973L11.9785 0.197266H11.0625ZM13.3184 6.33594H9.68848L11.2949 1.91309C11.3268 1.8265 11.3587 1.72624 11.3906 1.6123C11.4225 1.49382 11.4544 1.36165 11.4863 1.21582H11.5137C11.5456 1.35254 11.5752 1.48014 11.6025 1.59863C11.6299 1.71257 11.6618 1.81966 11.6982 1.91992L13.3184 6.33594ZM5.18359 10H6.05859V5.44727C6.05859 4.58594 5.85124 3.93424 5.43652 3.49219C5.02637 3.05013 4.41569 2.8291 3.60449 2.8291C3.0804 2.8291 2.60417 2.91569 2.17578 3.08887C1.7474 3.26204 1.34635 3.53092 0.972656 3.89551L1.49902 4.44238C1.7998 4.15072 2.12109 3.93652 2.46289 3.7998C2.80469 3.65853 3.17155 3.58789 3.56348 3.58789C4.06934 3.58789 4.46126 3.736 4.73926 4.03223C5.01725 4.32845 5.16536 4.79102 5.18359 5.41992V5.61133L3.16699 5.89844C2.32389 6.02148 1.7041 6.27441 1.30762 6.65723C0.911133 7.04004 0.712891 7.52995 0.712891 8.12695C0.712891 8.72852 0.911133 9.21842 1.30762 9.59668C1.7041 9.97493 2.23275 10.1641 2.89355 10.1641C3.3903 10.1641 3.83236 10.041 4.21973 9.79492C4.61165 9.54883 4.92383 9.2002 5.15625 8.74902H5.18359V10ZM5.18359 6.34277V7.0332C5.18359 7.73047 4.98079 8.30469 4.5752 8.75586C4.17415 9.20247 3.65462 9.42578 3.0166 9.42578C2.59277 9.42578 2.25098 9.30273 1.99121 9.05664C1.73145 8.81055 1.60156 8.48014 1.60156 8.06543C1.60156 7.62793 1.73828 7.29753 2.01172 7.07422C2.28516 6.85091 2.73861 6.69369 3.37207 6.60254L5.18359 6.34277Z</PathGeometry>
                                                </Path.Data>
                                            </Path>
                                        </Grid>
                                    </ToggleButton.Content>
                                </ToggleButton>
                            </Grid>

                            <StackPanel Grid.Row="3"
                                        Margin="5,0,0,0"
                                        VerticalAlignment="Center"
                                        Orientation="Horizontal">
                                <RadioButton x:Name="PART_RadioButton1"
                                             Content="{grid:GridLocalizationResourceExtension ResourceName=AND}"
                                             GroupName="R1" />
                                <RadioButton x:Name="PART_RadioButton2"
                                             Content="{grid:GridLocalizationResourceExtension ResourceName=OR}"
                                             GroupName="R1"
                                             IsChecked="{Binding IsORChecked,
                                                                 RelativeSource={RelativeSource TemplatedParent}}" >
                                    <RadioButton.Margin>
                                        <Thickness>60,0,0,0</Thickness>
                                    </RadioButton.Margin>
                                </RadioButton>
                            </StackPanel>

                            <ComboBox x:Name="PART_MenuComboBox2"
                                      Grid.Row="4"
                                      Height="24"
                                      Margin="5,0,0,0"
                                      ItemsSource="{Binding FilterTypeComboItems,
                                                            RelativeSource={RelativeSource TemplatedParent}}"
                                      SelectedItem="{Binding FilterType2,
                                                             RelativeSource={RelativeSource TemplatedParent},
                                                             Mode=TwoWay}"/>

                            <Grid Grid.Row="5" Margin="5,0,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ComboBox x:Name="PART_ComboBox2"
                                          Grid.Column="0"
                                          Height="24"
                                          DisplayMemberPath="DisplayText"
                                          IsEditable="True"
                                          IsEnabled="{Binding FilterType2,
                                                              RelativeSource={RelativeSource TemplatedParent},
                                                              Converter={StaticResource filterValueComboEnableConverter}}"
                                          IsTextSearchCaseSensitive="True"
                                          ItemsSource="{Binding ComboItemsSource,
                                                                RelativeSource={RelativeSource TemplatedParent}}"
                                          SelectedItem="{Binding FilterSelectedItem2,
                                                                 RelativeSource={RelativeSource TemplatedParent}}"
                                          Text="{Binding FilterValue2,
                                                         RelativeSource={RelativeSource TemplatedParent},
                                                         Mode=TwoWay,
                                                         ValidatesOnDataErrors=true,
                                                         NotifyOnValidationError=true}"
                                          Visibility="{Binding CanGenerateUniqueItems,
                                                               RelativeSource={RelativeSource TemplatedParent},
                                                               Converter={StaticResource boolToVisiblityConverter}}" />

                                <TextBox x:Name="PART_TextBox2"
                                         Grid.Column="0"
                                         Height="24"
                                         VerticalContentAlignment="Center"
                                         IsEnabled="{Binding FilterType2,
                                                             RelativeSource={RelativeSource TemplatedParent},
                                                             Converter={StaticResource filterValueComboEnableConverter}}"
                                         Text="{Binding FilterValue2,
                                                        RelativeSource={RelativeSource TemplatedParent},
                                                        UpdateSourceTrigger=PropertyChanged,
                                                        ValidatesOnDataErrors=true,
                                                        NotifyOnValidationError=true}"
                                         Visibility="{Binding CanGenerateUniqueItems,
                                                              RelativeSource={RelativeSource TemplatedParent},
                                                              Converter={StaticResource reverseVisibilityConverter}}" />

                                <DatePicker x:Name="PART_DatePicker2"
                                            Height="24"
                                            Width="24"
                                            Grid.Column="1"
                                            Margin="5 0 0 0"
                                            Style="{StaticResource SyncfusionDataGridFilterDatePickerStyle}"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            IsEnabled="{Binding FilterType2,
                                                                RelativeSource={RelativeSource TemplatedParent},
                                                                Converter={StaticResource filterValueComboEnableConverter}}"
                                            SelectedDate="{Binding DateFilterValue2,
                                                                   RelativeSource={RelativeSource TemplatedParent},
                                                                   Mode=TwoWay,
                                                                   UpdateSourceTrigger=PropertyChanged}"/>

                                <ToggleButton x:Name="PART_CasingButton2"
                                              Grid.Column="1"
                                              Height="24"
                                              Width="24"
                                              Margin="5 0 0 0"
                                              Foreground="{TemplateBinding Foreground}"
                                              IsChecked="{Binding IsCaseSensitive2,
                                                                  RelativeSource={RelativeSource TemplatedParent},
                                                                  Mode=TwoWay}"
                                              IsEnabled="{Binding FilterType2,
                                                                  RelativeSource={RelativeSource TemplatedParent},
                                                                  Converter={StaticResource filterValueComboEnableConverter}}"
                                              Style="{StaticResource SyncfusionDataGridFilterCasingToggleButtonStyle}"
                                              Visibility="{Binding CasingButtonVisibility,
                                                                   RelativeSource={RelativeSource TemplatedParent},
                                                                   Converter={StaticResource reverseVisibilityConverter}}">
                                    <ToggleButton.Content>
                                        <Grid Width="20">
                                            <Path 
                                                  Width="14.98"
                                                  Height="9.97"
                                                  HorizontalAlignment="Left"
                                                  VerticalAlignment="Bottom"
                                                  Fill="{StaticResource IconColor}"
                                                  Stretch="Fill" >
                                                <Path.Margin>
                                                    <Thickness>2.5,0,2.5,0</Thickness>
                                                </Path.Margin>
                                                <Path.Data>
                                                    <PathGeometry>M11.0625 0.197266L7.33691 10H8.34863L9.37402 7.16309H13.626L14.6787 10H15.6973L11.9785 0.197266H11.0625ZM13.3184 6.33594H9.68848L11.2949 1.91309C11.3268 1.8265 11.3587 1.72624 11.3906 1.6123C11.4225 1.49382 11.4544 1.36165 11.4863 1.21582H11.5137C11.5456 1.35254 11.5752 1.48014 11.6025 1.59863C11.6299 1.71257 11.6618 1.81966 11.6982 1.91992L13.3184 6.33594ZM5.18359 10H6.05859V5.44727C6.05859 4.58594 5.85124 3.93424 5.43652 3.49219C5.02637 3.05013 4.41569 2.8291 3.60449 2.8291C3.0804 2.8291 2.60417 2.91569 2.17578 3.08887C1.7474 3.26204 1.34635 3.53092 0.972656 3.89551L1.49902 4.44238C1.7998 4.15072 2.12109 3.93652 2.46289 3.7998C2.80469 3.65853 3.17155 3.58789 3.56348 3.58789C4.06934 3.58789 4.46126 3.736 4.73926 4.03223C5.01725 4.32845 5.16536 4.79102 5.18359 5.41992V5.61133L3.16699 5.89844C2.32389 6.02148 1.7041 6.27441 1.30762 6.65723C0.911133 7.04004 0.712891 7.52995 0.712891 8.12695C0.712891 8.72852 0.911133 9.21842 1.30762 9.59668C1.7041 9.97493 2.23275 10.1641 2.89355 10.1641C3.3903 10.1641 3.83236 10.041 4.21973 9.79492C4.61165 9.54883 4.92383 9.2002 5.15625 8.74902H5.18359V10ZM5.18359 6.34277V7.0332C5.18359 7.73047 4.98079 8.30469 4.5752 8.75586C4.17415 9.20247 3.65462 9.42578 3.0166 9.42578C2.59277 9.42578 2.25098 9.30273 1.99121 9.05664C1.73145 8.81055 1.60156 8.48014 1.60156 8.06543C1.60156 7.62793 1.73828 7.29753 2.01172 7.07422C2.28516 6.85091 2.73861 6.69369 3.37207 6.60254L5.18359 6.34277Z</PathGeometry>
                                                </Path.Data>
                                            </Path>
                                        </Grid>
                                    </ToggleButton.Content>
                                </ToggleButton>
                            </Grid>
                        </Grid>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinManager:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="PART_MenuComboBox1" Property="Margin" Value="5,0,0,5"/>
                            <Setter TargetName="PART_MenuComboBox2" Property="Margin" Value="5,0,0,5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="grid:AdvancedFilterControl" BasedOn="{StaticResource SyncfusionAdvancedFilterControlStyle}"/>

    <!-- ColumnChooser Style-->
    <Style TargetType="{x:Type grid:ColumnChooserItem}" x:Key="SyncfusionColumnChooserItemStyle">
        <Setter Property="Height" Value="30" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="BorderThickness" Value="0,0,0,0" />
        <Setter Property="Background" Value="{StaticResource PopupBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource PopupBackground}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type grid:ColumnChooserItem}">
                    <Border x:Name="PART_Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <ContentPresenter HorizontalAlignment="Left"
                                          VerticalAlignment="Center" Margin="12,0,0,0"
                                          Content="{TemplateBinding ColumnName}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" SourceName="PART_Border" Value="true">
                            <Setter Property="Background" TargetName="PART_Border" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="Foreground" Value="{StaticResource HoveredForeground}"/>
                            <Setter Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="{x:Type grid:ColumnChooserItem}" BasedOn="{StaticResource SyncfusionColumnChooserItemStyle}"/>

</ResourceDictionary>
