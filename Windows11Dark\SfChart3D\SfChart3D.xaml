<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF"
                    xmlns:System="clr-namespace:System;assembly=mscorlib"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="SyncfusionSfChart3DlineStyle"
           TargetType="Line">
        <Setter Property="Stroke"
                Value="{StaticResource Border}"></Setter>
        <Setter Property="StrokeThickness"
                Value="1"></Setter>
    </Style>

    <Style x:Key="3dLineStyle"
           TargetType="Line">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt4}"></Setter>
    </Style>

    <Style x:Key="3Dgridlinestyle"
           TargetType="Line">
        <Setter Property="Stroke"
                Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="StrokeThickness"
                Value="1"></Setter>
    </Style>

    <DataTemplate x:Key="Default3DTooltipTemplate">
        <Border Background="{StaticResource TooltipBackground}"
                BorderBrush="{StaticResource TooltipBorder}"
                BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                CornerRadius="3"
                Padding="4">
            <TextBlock Text="{Binding YData}"
                       Foreground="{StaticResource TooltipForeground}"
                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}" />
        </Border>
    </DataTemplate>

    <Style x:Key="SyncfusionBarSeries3DStyle" TargetType="local:BarSeries3D">
        <Setter Property="TooltipTemplate"
                Value="{StaticResource Default3DTooltipTemplate}"></Setter>
    </Style>

    <Style TargetType="local:BarSeries3D" BasedOn="{StaticResource SyncfusionBarSeries3DStyle}"></Style>

    <Style x:Key="SyncfusionStackingBarSeries3DStyle" TargetType="local:StackingBarSeries3D">
        <Setter Property="TooltipTemplate"
                Value="{StaticResource Default3DTooltipTemplate}"></Setter>
    </Style>

    <Style TargetType="local:StackingBarSeries3D" BasedOn="{StaticResource SyncfusionStackingBarSeries3DStyle}"></Style>

    <Style x:Key="SyncfusionStackingBar100Series3DStyle" TargetType="local:StackingBar100Series3D">
        <Setter Property="TooltipTemplate"
                Value="{StaticResource Default3DTooltipTemplate}"></Setter>
    </Style>

    <Style TargetType="local:StackingBar100Series3D" BasedOn="{StaticResource SyncfusionStackingBar100Series3DStyle}"></Style>
    
    <Style x:Key="SyncfusionStackingColumn100Series3DStyle" TargetType="local:StackingColumn100Series3D">
        <Setter Property="TooltipTemplate"
                Value="{StaticResource Default3DTooltipTemplate}"></Setter>
    </Style>

    <Style TargetType="local:StackingColumn100Series3D" BasedOn="{StaticResource SyncfusionStackingColumn100Series3DStyle}"></Style>

    <Style x:Key="SyncfusionStackingColumnSeries3DStyle" TargetType="local:StackingColumnSeries3D">
        <Setter Property="TooltipTemplate"
                Value="{StaticResource Default3DTooltipTemplate}"></Setter>
    </Style>

    <Style TargetType="local:StackingColumnSeries3D" BasedOn="{StaticResource SyncfusionStackingColumnSeries3DStyle}"></Style>
    
    <Style x:Key="SyncfusionScatterSeries3DStyle" TargetType="local:ScatterSeries3D">
        <Setter Property="TooltipTemplate"
                Value="{StaticResource Default3DTooltipTemplate}"></Setter>
    </Style>

    <Style TargetType="local:ScatterSeries3D" BasedOn="{StaticResource SyncfusionScatterSeries3DStyle}"></Style>

    <Style x:Key="SyncfusionAreaSeries3DStyle" TargetType="local:AreaSeries3D">
        <Setter Property="TooltipTemplate"
                Value="{StaticResource Default3DTooltipTemplate}"></Setter>
    </Style>

    <Style TargetType="local:AreaSeries3D" BasedOn="{StaticResource SyncfusionAreaSeries3DStyle}"></Style>

    <Style x:Key="SyncfusionDoughnutSeries3DStyle"  TargetType="local:DoughnutSeries3D">
        <Setter Property="TooltipTemplate"
                Value="{StaticResource Default3DTooltipTemplate}"></Setter>
    </Style>

    <Style TargetType="local:DoughnutSeries3D" BasedOn="{StaticResource SyncfusionDoughnutSeries3DStyle}"></Style>

    <Style x:Key="SyncfusionPieSeries3DStyle"  TargetType="local:PieSeries3D">
        <Setter Property="TooltipTemplate"
                Value="{StaticResource Default3DTooltipTemplate}"></Setter>
    </Style>

    <Style TargetType="local:PieSeries3D" BasedOn="{StaticResource SyncfusionPieSeries3DStyle}"></Style>

    <Style x:Key="SyncfusionColumnSeries3DStyle"  TargetType="local:ColumnSeries3D">
        <Setter Property="TooltipTemplate"
                Value="{StaticResource Default3DTooltipTemplate}"></Setter>
    </Style>

    <Style TargetType="local:ColumnSeries3D" BasedOn="{StaticResource SyncfusionColumnSeries3DStyle}"></Style>

    <Style x:Key="SyncfusionLineSeries3DStyle"  TargetType="local:LineSeries3D">
        <Setter Property="TooltipTemplate"
                Value="{StaticResource Default3DTooltipTemplate}"></Setter>
    </Style>

    <Style TargetType="local:LineSeries3D" BasedOn="{StaticResource SyncfusionLineSeries3DStyle}"></Style>

    <Style x:Key="SyncfusionChartAxisBase3DStyle" 
           TargetType="local:ChartAxisBase3D">
        <Setter Property="MajorTickLineStyle"
                Value="{StaticResource 3dLineStyle}"></Setter>
        <Setter Property="MinorTickLineStyle"
                Value="{StaticResource 3dLineStyle}"></Setter>
        <Setter Property="MajorGridLineStyle"
                Value="{StaticResource 3Dgridlinestyle}"></Setter>
        <Setter Property="MinorGridLineStyle"
                Value="{StaticResource 3Dgridlinestyle}"></Setter>
        <Setter Property="AxisLineStyle"
                Value="{StaticResource SyncfusionSfChart3DlineStyle}"></Setter>
    </Style>
  
    <Style BasedOn="{StaticResource SyncfusionChartAxisBase3DStyle}"
           TargetType="local:CategoryAxis3D"></Style>
    <Style TargetType="local:DateTimeAxis3D"
           BasedOn="{StaticResource SyncfusionChartAxisBase3DStyle}"></Style>
    <Style TargetType="local:LogarithmicAxis3D"
           BasedOn="{StaticResource SyncfusionChartAxisBase3DStyle}"></Style>
    <Style BasedOn="{StaticResource SyncfusionChartAxisBase3DStyle}"
           TargetType="local:NumericalAxis3D"></Style>
    <Style TargetType="local:TimeSpanAxis3D"
           BasedOn="{StaticResource SyncfusionChartAxisBase3DStyle}"></Style>

    <Style x:Key="SyncfusionSfChart3DStyle"  TargetType="local:SfChart3D">
        <Setter Property="Background"
                Value="{StaticResource ContentBackground}"></Setter>
        <Setter Property="BackWallBrush"
                Value="{StaticResource ContentBackground}"></Setter>
        <Setter Property="LeftWallBrush"
                Value="{StaticResource ContentBackgroundAlt2}"></Setter>
        <Setter Property="RightWallBrush"
                Value="{StaticResource ContentBackgroundAlt2}"></Setter>
        <Setter Property="BottomWallBrush"
                Value="{StaticResource ContentBackgroundAlt2}"></Setter>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"></Setter>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}"></Setter>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"></Setter>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}"></Setter>
        <Setter Property="ChartResourceDictionary">
            <Setter.Value>
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfChart/SfChartCommon.xaml" />
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="local:SfChart3D" BasedOn="{StaticResource SyncfusionSfChart3DStyle}"></Style>

</ResourceDictionary>
