<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:richtextboxadv="clr-namespace:Syncfusion.Windows.Controls.RichTextBoxAdv;assembly=Syncfusion.SfRichTextBoxAdv.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:tools_controls_shared="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.shared.WPF"
					xmlns:Syncfusion="http://schemas.syncfusion.com/wpf"
                    
                    xmlns:resources="clr-namespace:Syncfusion.Windows.Controls.RichTextBoxAdv;assembly=Syncfusion.SfRichTextBoxAdv.WPF">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfRichTextBoxAdv/SfRichTextBoxCommon.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/PrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatPrimaryButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="richtextboxadv:TableDialog">
        <Setter Property="Width" Value="500"/>
        <Setter Property="Height" Value="Auto"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="richtextboxadv:TableDialog">
                    <Grid Background="{StaticResource ContentBackground}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="16"/>
                            <RowDefinition Height="38"/>
                        </Grid.RowDefinitions>
                        <TabControl Margin="12" Height="380" Grid.Row="0" x:Name="PART_TablePropetiesTab" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}">
                            <TabControl.Items>
                                <TabItem Header="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=Table}" x:Name="PART_TableTab" Padding="14 3 14 3" >
                                    <Grid Margin="12 0 12 0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="16"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="20"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="20"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="*"/>
                                            <RowDefinition Height="16"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="12"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="8"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="8"/>
                                        </Grid.ColumnDefinitions>
                                        <Label Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="5" VerticalAlignment="Center" Foreground="{StaticResource ContentForeground}" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogSize}"/>
                                        <StackPanel Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="4" Orientation="Horizontal">
                                            <CheckBox x:Name="PART_TableWidthCheckBox" Margin="5" VerticalAlignment="Center" 
                                                      Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogPreferredWidth}" IsChecked="{Binding HasTableWidth, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" />
                                            <shared:UpDown x:Name="PART_TableWidthButton" Padding="4 1 1 2" Margin="5 5 5 13" Height="25" Width="80" Step="1" MinValue="0" MaxValue="2112" UseNullOption="True"
                                                               Value="{Binding TablePreferredWidth, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" IsEnabled="{Binding IsChecked, Mode=OneWay, ElementName=PART_TableWidthCheckBox}"/>
                                        </StackPanel>
                                        <StackPanel Grid.Row="2" Grid.Column="4" Grid.ColumnSpan="2" Orientation="Horizontal" HorizontalAlignment="Right">
                                            <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogPreferredWidthType}" IsEnabled="{Binding IsChecked, Mode=OneWay, ElementName=PART_TableWidthCheckBox}" VerticalAlignment="Center" Margin="5"/>
                                            <ComboBox x:Name="PART_TableWidthTypeBox" Width="80" Padding="4 1 1 2" Margin="5 5 5 13" Height="25" VerticalAlignment="Center"
                                                      ItemsSource="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName= WidthTypeCollection}" IsEnabled="{Binding IsChecked, Mode=OneWay, ElementName=PART_TableWidthCheckBox}"
                                                      SelectedValue="{Binding TablePreferredWidthType, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource WidthTypeStringConverter}}"/>
                                        </StackPanel>
                                        <Border Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="7" Height="1" Margin="5 12 5 5" Padding="4 2 2 2" BorderThickness="0 0 0 1" Opacity="0.5" VerticalAlignment="Center" Background="{TemplateBinding BorderBrush}"/>
                                        <Label Grid.Row="4" Grid.Column="1" Grid.ColumnSpan="5" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogAlignment}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                        <Border Grid.Row="5" 
                                                Grid.Column="1" 
                                                BorderThickness="1" 
                                                Margin="5" 
                                                BorderBrush="{StaticResource BorderAlt}" 
                                                Height="62" 
                                                Width="60">
                                            <ToggleButton 
                                                HorizontalAlignment="Center" 
                                                Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                Padding="1"
                                                Width="50"
                                                Height="50"
                                                          IsChecked="{Binding TableAlignment, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TableAlignmentToggleConverter},ConverterParameter= Left}">
                                                <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/TableleftAlignment_Icon.png"  
                                                       Height="50" 
                                                       Width="50"/>
                                            </ToggleButton>
                                        </Border>
                                        <Label Grid.Row="6" Grid.Column="1" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=LeftAlignment}" VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                        <Border Grid.Row="5" 
                                                Grid.Column="2"
                                                BorderThickness="1"
                                                Margin="5" 
                                                BorderBrush="{StaticResource BorderAlt}" 
                                                Height="62"
                                                Width="60">
                                            <ToggleButton HorizontalAlignment="Center" 
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                          Padding="1"
                                                          Width="50"
                                                Height="50"
                                                          IsChecked="{Binding TableAlignment, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TableAlignmentToggleConverter},ConverterParameter= Center}">
                                                <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/TableCenterAlignment_Icon.png"  
                                                       Height="50" 
                                                       Width="50"/>
                                            </ToggleButton>
                                        </Border>
                                        <Label Grid.Row="6" Grid.Column="2" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=CenteredAlignment}" VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                        <Border Grid.Row="5" 
                                                Grid.Column="3" 
                                                BorderThickness="1" 
                                                Margin="5" 
                                                BorderBrush="{StaticResource BorderAlt}" 
                                                Height="62" 
                                                Width="60">
                                            <ToggleButton HorizontalAlignment="Center" 
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}"
                                                          Padding="1" 
                                                          Width="50"
                                                Height="50"
                                                          IsChecked="{Binding TableAlignment, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TableAlignmentToggleConverter},ConverterParameter= Right}">
                                                <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/TableRightAlignment_Icon.png"  
                                                       Height="50" 
                                                       Width="50"/>
                                            </ToggleButton>
                                        </Border>
                                        <Label Grid.Row="6" Grid.Column="3" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=RightAlignment}" VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                        <StackPanel Grid.Row="5" Grid.Column="4" Grid.ColumnSpan="2" Orientation="Horizontal" HorizontalAlignment="Right">
                                            <Label x:Name="PART_IndentLabel" VerticalAlignment="Center" Margin="5" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogLeftIndent}" Foreground="{TemplateBinding Foreground}"/>
                                            <shared:UpDown x:Name="PART_LeftIndentButton" Padding="4 1 1 2" Margin="5 5 5 13" Height="25" Width="80" Step="1" MinValue="-1440" MaxValue="1440" UseNullOption="True"
                                                                Value="{Binding LeftIndent, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" />
                                        </StackPanel>
                                        <Border Grid.Row="7" Grid.Column="1" Grid.ColumnSpan="7" Height="1" Margin="5 12 5 5" Padding="4 2 2 2" BorderThickness="0 0 0 1" Opacity="0.5" VerticalAlignment="Center" Background="{TemplateBinding BorderBrush}"/>
                                        <Label Grid.Row="8" Grid.Column="1" Grid.ColumnSpan="5" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogTableDirection}"/>
                                        <StackPanel Grid.Row="9" Grid.Column="1" Grid.ColumnSpan="4" Orientation="Horizontal" HorizontalAlignment="Left">
                                            <RadioButton x:Name="PART_RtlRadioButton" GroupName="PART_TableBidi"  Margin="5" 
                                             Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DirectionRightToLeft}"  HorizontalAlignment="Center" VerticalAlignment="Center" 
                                             IsChecked="{Binding Bidi, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ToggleBooleanConverter}, ConverterParameter={StaticResource True}}" ></RadioButton>
                                            <RadioButton x:Name="PART_LtrRadioButton" GroupName="PART_TableBidi"  Margin="5" 
                                             Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DirectionLeftToRight}"  HorizontalAlignment="Center" VerticalAlignment="Center" 
                                             IsChecked="{Binding Bidi, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ToggleBooleanConverter}, ConverterParameter={StaticResource False}}" ></RadioButton>
                                        </StackPanel>
                                        <StackPanel Grid.Row="12" Grid.Column="1" Grid.ColumnSpan="5" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0 0 -8 0">
                                            <Button x:Name="PART_BordersShadingButton"
                                                    Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogTitle}" 
                                                    Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 0 6 0"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                                    Width="150"/>
                                            <Button x:Name="PART_TableOptionsButton" 
                                                    Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOptions}"
                                                    Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 0 6 0"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                                    Width="80"/>
                                        </StackPanel>
                                    </Grid>
                                </TabItem>
                                <TabItem Header="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=Row}" x:Name="PART_RowTab" Padding="14 3 14 3" TabIndex="1">
                                    <Grid Margin="12 0 12 0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="16"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="20"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="*"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="8"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="20"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="8"/>
                                        </Grid.ColumnDefinitions>
                                        <Label Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogSize}"/>
                                        <StackPanel Grid.Row="3" Grid.Column="1" Orientation="Horizontal">
                                            <CheckBox x:Name="PART_RowHeightCheckBox"  Margin="5" VerticalAlignment="Center" 
                                                      Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogRowHeight}" IsChecked="{Binding HasRowHeight, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                            <shared:UpDown x:Name="PART_RowHeightButton" Margin="5 5 5 13" Padding="4 1 1 2" Height="25" Width="80" Step="1" MinValue="0" MaxValue="2112" UseNullOption="True"
                                                                Value="{Binding RowHeight, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" IsEnabled="{Binding IsChecked, Mode=OneWay, ElementName=PART_RowHeightCheckBox}"/>
                                        </StackPanel>
                                        <StackPanel Grid.Row="3" Grid.Column="3" Orientation="Horizontal" HorizontalAlignment="Right">
                                            <Label  Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogRowHeightType}" IsEnabled="{Binding IsChecked, Mode=OneWay, ElementName=PART_RowHeightCheckBox}" VerticalAlignment="Center" Margin="5" />
                                            <ComboBox x:Name="PART_RowHeightBox" Width="80" Padding="4 1 1 2" Margin="5 5 5 13" Height="25" VerticalAlignment="Center"
                                                      ItemsSource="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName= HeightTypeCollection}" IsEnabled="{Binding IsChecked, Mode=OneWay, ElementName=PART_RowHeightCheckBox}"
                                                      SelectedValue="{Binding RowHeightType, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource HeightTypeStringConverter}}"/>
                                        </StackPanel>
                                        <Border Grid.Row="4" Grid.Column="1" Grid.ColumnSpan="5" Height="1" Margin="5 12 5 5" Padding="4 2 2 2" BorderThickness="0 0 0 1" Opacity="0.5" VerticalAlignment="Center" Background="{TemplateBinding BorderBrush}"/>
                                        <Label Grid.Row="5" Grid.Column="1" Grid.ColumnSpan="5" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOptions}"/>
                                        <CheckBox Grid.Row="6" Grid.Column="1" Grid.ColumnSpan="3" Margin="5"  Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogAllowRowBreak}" IsChecked="{Binding AllowBreakAcrossPages , Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                        <CheckBox Grid.Row="7" Grid.Column="1" Grid.ColumnSpan="3" Margin="5"  Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogRepeatHeader}" IsChecked="{Binding IsHeader, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                    </Grid>
                                </TabItem>
                                <TabItem Header="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=Cell}" x:Name="PART_CellTab" Padding="14 3 14 3" TabIndex="1">
                                    <Grid Margin="12 0 12 0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="16"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="20"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="*"/>
                                            <RowDefinition Height="16"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="12"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="8"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="8"/>
                                        </Grid.ColumnDefinitions>
                                        <Label Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="5" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogSize}"/>
                                        <StackPanel Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="4" Orientation="Horizontal">
                                            <CheckBox x:Name="PART_CellWidthCheckBox" Margin="5" VerticalAlignment="Center" 
                                                      Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogPreferredWidth}" IsChecked="{Binding HasCellWidth, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                            <shared:UpDown x:Name="PART_CellWidthButton" Padding="4 1 1 2" Margin="5 5 5 13" Height="25" Width="80" Step="1" MinValue="0" MaxValue="2112" UseNullOption="True"
                                                               Value="{Binding CellPreferredWidth, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" IsEnabled="{Binding IsChecked, Mode=OneWay, ElementName=PART_CellWidthCheckBox}"/>
                                        </StackPanel>
                                        <StackPanel Grid.Row="2" Grid.Column="4" Grid.ColumnSpan="2" Orientation="Horizontal" HorizontalAlignment="Right">
                                            <Label  Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogPreferredWidthType}" IsEnabled="{Binding IsChecked, Mode=OneWay, ElementName=PART_CellWidthCheckBox}" VerticalAlignment="Center" Margin="5"/>
                                            <ComboBox x:Name="PART_CellWidthTypeBox"  Width="80" Padding="4 1 1 2" Margin="5 5 5 13" Height="25" VerticalAlignment="Center"
                                                      ItemsSource="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName= WidthTypeCollection}" IsEnabled="{Binding IsChecked, Mode=OneWay, ElementName=PART_CellWidthCheckBox}"
                                                      SelectedValue="{Binding CellPreferredWidthType, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource WidthTypeStringConverter}}"/>
                                        </StackPanel>
                                        <Border Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="7" Height="1" Margin="5 12 5 5" Padding="4 2 2 2" BorderThickness="0 0 0 1" Opacity="0.5" VerticalAlignment="Center" Background="{TemplateBinding BorderBrush}"/>
                                        <Label Grid.Row="4" Grid.Column="1" Grid.ColumnSpan="5" VerticalAlignment="Center" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogVerticalAlignment}"/>
                                        <Border Grid.Row="5" 
                                                Grid.Column="1" 
                                                BorderThickness="1" 
                                                Margin="5" 
                                                BorderBrush="{StaticResource BorderAlt}" 
                                                Height="62" 
                                                Width="60">
                                            <ToggleButton HorizontalAlignment="Center"
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}"
                                                          Padding="1"
                                                          Height="50"
                                                          Width="50"
                                                          IsChecked="{Binding CellContentAlignment, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource VerticalAlignmentToggleConverter},ConverterParameter= Top}">
                                                <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/CellTopAlignment_Icon.png"  
                                                       Height="50" 
                                                       Width="50"/>
                                            </ToggleButton>
                                        </Border>
                                        <Label Grid.Row="6" Grid.Column="1" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TopAlignment}" VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                        <Border Grid.Row="5" 
                                                Grid.Column="2" 
                                                BorderThickness="1" 
                                                Margin="5" 
                                                BorderBrush="{StaticResource BorderAlt}" 
                                                Height="62" 
                                                Width="60">
                                            <ToggleButton HorizontalAlignment="Center" 
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                          Padding="1"
                                                          Height="50"
                                                          Width="50"
                                                          IsChecked="{Binding CellContentAlignment, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource VerticalAlignmentToggleConverter},ConverterParameter= Center}">
                                                <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/CellCenterAlignment_Icon.png"  
                                                       Height="50" 
                                                       Width="50"/>
                                            </ToggleButton>
                                        </Border>
                                        <Label Grid.Row="6" Grid.Column="2" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=CenteredAlignment}" VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                        <Border Grid.Row="5"
                                                Grid.Column="3"
                                                BorderThickness="1" 
                                                Margin="5" 
                                                BorderBrush="{StaticResource BorderAlt}"
                                                Height="62"
                                                Width="60">
                                            <ToggleButton HorizontalAlignment="Center" 
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                          Padding="1"
                                                          Height="50"
                                                          Width="50"
                                                          IsChecked="{Binding CellContentAlignment, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource VerticalAlignmentToggleConverter},ConverterParameter= Bottom}">
                                                <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/CellBottomAlignment_Icon.png"  
                                                       Height="50" 
                                                       Width="50"/>
                                            </ToggleButton>
                                        </Border>
                                        <Label Grid.Row="6" Grid.Column="3" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BottomAlignment}" VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                                        <StackPanel Grid.Row="9" Grid.Column="1" Grid.ColumnSpan="5" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0 0 -8 0">
                                            <Button x:Name="PART_CellOptionsButton" 
                                                    Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOptions}"
                                                    Height="24"
                                    Padding="12 2 12 2"
                                    Margin="0 0 6 0"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center" 
                                                    Width="80"/>
                                        </StackPanel>
                                    </Grid>
                                </TabItem>
                            </TabControl.Items>
                        </TabControl>
                        <Border Grid.Row="2" Grid.ColumnSpan="3" Width="Auto" BorderBrush="{StaticResource BorderAlt}" Background="{StaticResource PopupBackground}" BorderThickness="0 1 0 0">
                            <StackPanel Grid.Row="2" HorizontalAlignment="Right" Orientation="Horizontal">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyTablePropertiesButton"
                                    Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOk}"
                                        Grid.Column="0"
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 5 9 5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="52"
                                        IsDefault="True" 
                                        TabIndex="11"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        Style="{StaticResource WPFPrimaryButtonStyle}"/>
                                    <Button x:Name="PART_CancelButton" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxCancel}"
                                        Grid.Column="1"
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 5 9 5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="69" 
                                        TabIndex="13"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        />
                                </Grid>
                            </StackPanel>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="richtextboxadv:CellOptionsDialog">
        <Setter Property="Width" Value="340"/>
        <Setter Property="Height" Value="Auto"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="richtextboxadv:CellOptionsDialog">
                    <Grid Background="{StaticResource ContentBackground}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="12"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="38"/>
                        </Grid.RowDefinitions>
                        <Grid Grid.Row="1" Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="16"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="12"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="0.4*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Label Grid.Row="0" Grid.ColumnSpan="4" Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogCellMargin}" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"/>
                            <CheckBox Grid.Row="1" Grid.ColumnSpan="2" Margin="5" Padding="4 2 2 2"
                                      Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogMarginSameAsTable}" IsChecked="{Binding SameAsTable, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                            <StackPanel Grid.Row="2" Grid.Column="0">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TopMargin}" VerticalAlignment="Center" 
                                       IsEnabled="{Binding HasCellMargin, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                <shared:UpDown x:Name="PART_CellTopMarginButton" Margin="5" Padding="4 1 1 2" Height="25" Step="1" MinValue="0" MaxValue="2112" UseNullOption="True"
                                                   Value="{Binding TopMargin, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" IsEnabled="{Binding HasCellMargin, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                            </StackPanel>
                            <StackPanel Grid.Row="2" Grid.Column="3">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=LeftMargin}" VerticalAlignment="Center"
                                       IsEnabled="{Binding HasCellMargin, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                <shared:UpDown x:Name="PART_CellLeftMarginButton" Margin="5" Padding="4 1 1 2" Height="25"  Step="1" MinValue="0" MaxValue="2112" UseNullOption="True"
                                                   Value="{Binding LeftMargin, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" IsEnabled="{Binding HasCellMargin, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                            </StackPanel>
                            <StackPanel Grid.Row="3" Grid.Column="0">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BottomMargin}" VerticalAlignment="Center" 
                                       IsEnabled="{Binding HasCellMargin, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                <shared:UpDown x:Name="PART_CellBottomMarginButton" Margin="5" Padding="4 1 1 2" Height="25" Step="1" MinValue="0" MaxValue="2112" UseNullOption="True"
                                                   Value="{Binding BottomMargin, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" IsEnabled="{Binding HasCellMargin, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                            </StackPanel>
                            <StackPanel Grid.Row="3" Grid.Column="3">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=RightMargin}" VerticalAlignment="Center" 
                                       IsEnabled="{Binding HasCellMargin, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                <shared:UpDown x:Name="PART_CellRightMarginButton" Margin="5" Padding="4 1 1 2" Height="25" Step="1" MinValue="0" MaxValue="2112" UseNullOption="True"
                                                   Value="{Binding RightMargin, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" IsEnabled="{Binding HasCellMargin, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                            </StackPanel>
                        </Grid>
                        <Border Grid.Row="2" Grid.ColumnSpan="3" Width="Auto" BorderBrush="{StaticResource BorderAlt}" Background="{StaticResource PopupBackground}" BorderThickness="0 1 0 0">
                            <StackPanel Grid.Row="2" Grid.ColumnSpan="3" Orientation="Horizontal" HorizontalAlignment="Right">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyCellOptionsButton"
                                        Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOk}"
                                        Grid.Column="0"
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 5 9 5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="52"
                                        IsDefault="True" 
                                        TabIndex="10"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        Style="{StaticResource WPFPrimaryButtonStyle}"/>
                                    <Button x:Name="PART_CancelButton"
                                        Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxCancel}" 
                                        Grid.Column="1"
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 5 9 5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="69" 
                                        TabIndex="11"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        />
                                </Grid>
                            </StackPanel>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="richtextboxadv:TableOptionsDialog">
        <Setter Property="Width" Value="391"/>
        <Setter Property="Height" Value="341"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="richtextboxadv:TableOptionsDialog">
                    <Grid x:Name="PART_Root" Background="{StaticResource ContentBackground}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="12"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="12"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="12"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Border Grid.Row="1"/>
                        <Grid Grid.Row="1" Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="12"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="10"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="10"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="11"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="10"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="10"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="10"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="24"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="36"/>
                            </Grid.ColumnDefinitions>
                            <Label Grid.Row="0" 
                                   Grid.Column="0" 
                                   FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                   FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                   Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogDefaultCellMargin}"
                                   VerticalAlignment="Center"
                                   Padding="0"
                                   Foreground="{TemplateBinding Foreground}"/>
                            <StackPanel Grid.Row="2" Grid.Column="0">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TopMargin}"
                                               VerticalAlignment="Center"
                                               Margin="0"
                                               Padding="0 5 0 5"
                                               Foreground="{TemplateBinding Foreground}"
                                               FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                               FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <shared:UpDown x:Name="PART_CellTopMarginButton"
                                               Margin="0 0 5 5"
                                               Padding="0 1 1 2"
                                               Height="25"
                                               Width="159"
                                               Step="1"
                                               MinValue="0"
                                               MaxValue="2112"
                                               UseNullOption="True"
                                               Value="{Binding TopMargin, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                               FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                               FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}" />
                            </StackPanel>
                            <StackPanel Grid.Row="2" Grid.Column="2">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=LeftMargin}"
                                       VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}"
                                       Padding="0 5 0 5"
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <shared:UpDown x:Name="PART_CellLeftMarginButton"
                                               Margin="0 0 5 5"
                                               Padding="0 1 1 2"
                                               Height="25"
                                               Width="159"
                                               Step="1"
                                               MinValue="0"
                                               MaxValue="2112"
                                               UseNullOption="True"
                                               Value="{Binding LeftMargin, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                               FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                               FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                            </StackPanel>
                            <StackPanel Grid.Row="4" Grid.Column="0">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BottomMargin}"
                                       VerticalAlignment="Center"
                                       Padding="0 5 0 5"
                                       Foreground="{TemplateBinding Foreground}"
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <shared:UpDown x:Name="PART_CellBottomMarginButton"
                                               Margin="0 0 5 5" 
                                               Padding="0 1 1 2"
                                               Height="25"
                                               Width="159"
                                               Step="1"
                                               MinValue="0"
                                               MaxValue="2112"
                                               UseNullOption="True"
                                               Value="{Binding BottomMargin, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                               FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                               FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                            </StackPanel>
                            <StackPanel Grid.Row="4" Grid.Column="2" >
                                <Label  Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=RightMargin}" 
                                        VerticalAlignment="Center" 
                                        Padding="0 5 0 5" 
                                        Foreground="{TemplateBinding Foreground}" 
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <shared:UpDown  x:Name="PART_CellRightMarginButton"
                                                Margin="0 0 5 5" 
                                                Padding="0 1 1 2"
                                                Height="25"
                                                Width="159"
                                                Step="1"
                                                MinValue="0"
                                                MaxValue="2112"
                                                UseNullOption="True"
                                                Value="{Binding RightMargin, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                                FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}" />
                            </StackPanel>
                            <Label  Grid.Row="6"
                                    Grid.ColumnSpan="4" 
                                    Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogDefaultCellSpacing}"
                                    VerticalAlignment="Center" 
                                    Padding="1 5 1 5"
                                    Margin="0" 
                                    Foreground="{TemplateBinding Foreground}"
                                    FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                    FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                            <CheckBox  x:Name="PART_AllowCellSpacingCheckBox"
                                       Grid.Row="8"
                                       Grid.ColumnSpan="2"
                                       Margin="0 0 5 5"
                                       Padding="4 2 2 2"  
                                       Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogAllowCellSpacing}" 
                                       IsChecked="{Binding AllowCellSpacing, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                            <shared:UpDown  x:Name="PART_CellSpacingButton"
                                            Grid.Row="7"
                                            Grid.Column="2" 
                                            Grid.RowSpan="2"
                                            Margin="0 0 1 5" 
                                            Padding="0 0 2 2"
                                            Height="24" 
                                            Width="159"
                                            Step="1" 
                                            MinValue="0"
                                            MaxValue="264"
                                            UseNullOption="True"
                                            Value="{Binding CellSpacing, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                            IsEnabled="{Binding AllowCellSpacing, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" 
                                            FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                            FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}" />
                            <Border Grid.Row="9"
                                    Grid.ColumnSpan="4"
                                    Height="1"
                                    Margin="5 12 5 5"
                                    Padding="4 2 2 2"
                                    BorderThickness="0 0 0 1"
                                    Opacity="0.5"
                                    VerticalAlignment="Center"
                                    Background="{TemplateBinding BorderBrush}"/>
                            <Label Grid.Row="10" 
                                   Grid.ColumnSpan="4"
                                   Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableOptionsDialogOptions}" 
                                   Padding="1 0 5 5" 
                                   VerticalAlignment="Center"
                                   Foreground="{TemplateBinding Foreground}"  
                                   FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                   FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                            <CheckBox x:Name="PART_AllowAutoFit"
                                      Grid.Row="12" 
                                      Grid.ColumnSpan="7"
                                      Padding="4 2 2 2" 
                                      Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableOptionsDialogAllowAutoFit}" 
                                      IsChecked="{Binding AllowAutoFit, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" 
                                      FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                      FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                        </Grid>
                        <Border Grid.Row="2"
                                Grid.Column="0"
                                Grid.ColumnSpan="3"
                                Width="Auto"
                                BorderBrush="{StaticResource BorderAlt}" 
                                Background="{StaticResource PopupBackground}" 
                                BorderThickness="0 1 0 0" >
                            <StackPanel Grid.Row="2"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="3" 
                                        Orientation="Horizontal" 
                                        HorizontalAlignment="Right">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyTableOptionsButton" 
                                        Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOk}"
                                        Grid.Column="0"
                                        Height="24"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        Padding="12 2 12 2"
                                        Margin="0 0 9 1"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center" 
                                        Width="50"
                                        TabIndex="8"
                                        IsDefault="True"
                                        Style="{StaticResource WPFPrimaryButtonStyle}"/>
                                    <Button x:Name="PART_CancelButton"
                                        Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxCancel}"
                                        Grid.Column="1"
                                        Height="24"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        Padding="12 2 12 2"
                                        Margin="0 0 7 1"
                                        TabIndex="9"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="69"
                                        />
                                </Grid>
                            </StackPanel>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="richtextboxadv:BordersAndShadingDialog">
        <Setter Property="Width" Value="489"/>
        <Setter Property="Height" Value="407"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="richtextboxadv:BordersAndShadingDialog">
                    <Grid x:Name="PART_Root" Background="{StaticResource ContentBackground}">
                        <Grid.Resources>
                            <richtextboxadv:BrushOpacityConverter x:Key="BrushOpacityConverter"/>
                        </Grid.Resources>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="12"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="12"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="12"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="12"/>
                            <RowDefinition Height="2"/>
                            <RowDefinition Height="12"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="12"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="38"/>
                        </Grid.RowDefinitions>
                        <Grid Grid.Row="1" Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogBorders}"
                                   VerticalAlignment="Center" 
                                   Foreground="{StaticResource ContentForeground}"
                                   FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                    FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                            <Grid Grid.Row="1" Grid.ColumnSpan="3" >
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="24"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="26"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Grid >
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="8"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="8"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="8"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="8"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Label  HorizontalAlignment="Left"
                                            Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogSetting}" 
                                            FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                            FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                    <StackPanel  Orientation="Horizontal" Grid.Row="2">
                                        <Border BorderThickness="1" BorderBrush="{TemplateBinding BorderBrush}" Height="40" Width="40" >
                                            <ToggleButton HorizontalAlignment="Center" 
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                          Padding="9 8 9 8"
                                                          Height="36"
                                                          Width="36"                           
                                                          BorderThickness="1"
                                                          IsChecked="{Binding NoBorder, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                                <ToggleButton.Content>
                                                    <Image VerticalAlignment="Center"
                                                           HorizontalAlignment="Center"
                                                           Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/NoBorder_Icon.png"
                                                           Height="36"
                                                           Width="36"/>
                                                </ToggleButton.Content>
                                            </ToggleButton>
                                        </Border>
                                        <Label   Padding="0"
                                                 Margin="12 0 0 0"
                                                 Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogNoBorder}"
                                                 VerticalAlignment="Center" 
                                                 Foreground="{TemplateBinding Foreground}"
                                                 FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                 FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}" />
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Grid.Row="4">
                                        <Border BorderThickness="1"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                Height="40"
                                                Width="40" >
                                            <ToggleButton HorizontalAlignment="Center" 
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}"
                                                          Height="36"
                                                          Width="36"
                                                          Padding="1"                                                         
                                                          BorderThickness="1"
                                                          IsChecked="{Binding BoxBorder, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                                <ToggleButton.Content>
                                                    <Image VerticalAlignment="Center"
                                                           HorizontalAlignment="Center" 
                                                           Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/BoxBorder_Icon.png"
                                                           Height="36"
                                                           Width="36"/>
                                                </ToggleButton.Content>
                                            </ToggleButton>
                                        </Border>
                                        <Label   Padding="0"
                                                 Margin="12 0 0 0" 
                                                 Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogBoxBorder}" 
                                                 VerticalAlignment="Center"
                                                 Foreground="{TemplateBinding Foreground}" 
                                                 FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                 FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Grid.Row="6">
                                        <Border BorderThickness="1"
                                                BorderBrush="{TemplateBinding BorderBrush}" 
                                                Height="40" 
                                                Width="40" >
                                            <ToggleButton HorizontalAlignment="Center" 
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                          Padding="9 8 9 8"
                                                          Height="36"
                                                          Width="36"      
                                                          BorderThickness="1"
                                                          IsChecked="{Binding AllBorder, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                                <ToggleButton.Content>
                                                    <Image VerticalAlignment="Center"
                                                           HorizontalAlignment="Center" 
                                                           Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/AllBorder_Icon.png"
                                                           Height="36"
                                                           Width="36"/>
                                                </ToggleButton.Content>
                                            </ToggleButton>
                                        </Border>
                                        <Label   Padding="0"
                                                 Margin="12 0 0 0"
                                                 Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogAllBorder}" 
                                                 VerticalAlignment="Center"
                                                 Foreground="{TemplateBinding Foreground}"
                                                 FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                 FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Grid.Row="8">
                                        <Border BorderThickness="1"
                                                BorderBrush="{TemplateBinding BorderBrush}" 
                                                Height="40"
                                                Width="40" >
                                            <ToggleButton HorizontalAlignment="Center" 
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                          Padding="9 8 9 8"
                                                          Height="36"
                                                          Width="36"     
                                                          BorderThickness="1"
                                                          IsChecked="{Binding CustomBorder, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                                <ToggleButton.Content>
                                                    <Image VerticalAlignment="Center" 
                                                           HorizontalAlignment="Center"
                                                           Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/CustomBorder_Icon.png" 
                                                           Height="36"
                                                           Width="36"/>
                                                </ToggleButton.Content>
                                            </ToggleButton>
                                        </Border>
                                        <Label  Padding="0"
                                                Margin="12 0 0 0" 
                                                Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogCustomBorder}" 
                                                VerticalAlignment="Center" 
                                                Foreground="{TemplateBinding Foreground}" 
                                                FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                    </StackPanel>
                                </Grid>
                                <Grid Grid.Column="2" >
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="18"/>
                                        <RowDefinition Height="13"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="5"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="8"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="5"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="8"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="5"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Label  Grid.Row="2" 
                                            Padding="0" 
                                            Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogStyle}"
                                            FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                            FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                    <ComboBox  Grid.Row="4"
                                               Margin="0"
                                               Padding="2 2 2 2"
                                               Height="24"
                                               Width="129" 
                                               VerticalAlignment="Center"
                                               ItemsSource="{Binding LineStyleCollection, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"
                                               SelectedIndex="{Binding LineStyle, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                    <Label Grid.Row="6"
                                           Padding="0"
                                           Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogWidth}"
                                           FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                           FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                    <shared:UpDown  Grid.Row="8"
                                                    Margin="0"
                                                    Padding="2 1 1 2" 
                                                    Height="25"
                                                    Width="129" 
                                                    Step="0.25"
                                                    MinValue="0.25"
                                                    MaxValue="6"
                                                    Value="{Binding LineWidth, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" />
                                    <Label Grid.Row="10" 
                                           Padding="0" 
                                           Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogColor}"
                                           FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                           FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                    <tools_controls_shared:ColorPickerPalette  Grid.Row="12" 
                                                                               Margin="0"
                                                                               Padding="2 2 2 2"
                                                                               Height="24"
                                                                               Width="129" 
                                                                               x:Name="PART_BorderColorPicker" 
                                                                               BorderBrush="{StaticResource BorderAlt}" 
                                                                               HorizontalAlignment="Left"
                                                                               MoreColorOptionVisibility="Collapsed"
                                                                               BlackWhiteVisibility="Both"
                                                                               Color="{Binding BorderColor, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                </Grid>
                                <Grid Grid.Column="4">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="12"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogPreview}" 
                                           FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                           FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                    <Grid Grid.Row="2" >
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="8"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="8"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="45"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="12"/>
                                            <ColumnDefinition Width="17"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="8"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="8"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="17"/>
                                            <ColumnDefinition Width="12"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <ToggleButton HorizontalAlignment="Center" 
                                                      VerticalAlignment="Center"
                                                      Grid.Row="0" 
                                                      Grid.Column="0"
                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                      Padding="1"
                                                      Height="24"
                                                      Width="24"
                                                      IsChecked="{Binding TopBorder, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                            <Image VerticalAlignment="Center" 
                                                   HorizontalAlignment="Center" 
                                                   Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/TopBorder_Icon.png"  
                                                   Height="16" 
                                                   Width="16"/>
                                        </ToggleButton>
                                        <ToggleButton HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      Grid.Row="2" 
                                                      Grid.Column="0"
                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}"
                                                      Padding="1" 
                                                      Height="24"
                                                      Width="24"
                                                      IsChecked="{Binding HorizontalBorder, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                            <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/HorizontalBorder_Icon.png"  
                                                       Height="16" 
                                                       Width="16"/>
                                        </ToggleButton>
                                        <ToggleButton HorizontalAlignment="Center" 
                                                      VerticalAlignment="Center"
                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                      Padding="1"
                                                      Height="24"
                                                      Width="24"
                                                      IsChecked="{Binding BottomBorder, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                                      Grid.Row="4"
                                                      Grid.Column="0">
                                            <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/BottomBorder_Icon.png"  
                                                       Height="16" 
                                                       Width="16"/>
                                        </ToggleButton>
                                        <Grid  Grid.Row="0" Height="120" Width="120" Grid.RowSpan="6" Grid.Column="2" Grid.ColumnSpan="7" Background="{Binding ShadingFill, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ColorBrushConverter}}" >
                                            <Border Height="1" BorderBrush="{Binding BorderColor, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ColorBrushConverter}}" BorderThickness="1" VerticalAlignment="Top" 
                                                    Visibility="{Binding TopBorder, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ToggleVisibilityConverter}}" />
                                            <Border Width="1" BorderBrush="{Binding BorderColor, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ColorBrushConverter}}" BorderThickness="1" HorizontalAlignment="Left"  
                                                    Visibility="{Binding LeftBorder, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ToggleVisibilityConverter}}" />
                                            <Border Width="1" BorderBrush="{Binding BorderColor, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ColorBrushConverter}}" BorderThickness="1" HorizontalAlignment="Center" 
                                                    Visibility="{Binding VerticalBorder, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ToggleVisibilityConverter}}" />
                                            <Border Width="1" BorderBrush="{Binding BorderColor, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ColorBrushConverter}}" BorderThickness="1" HorizontalAlignment="Right" 
                                                    Visibility="{Binding RightBorder, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ToggleVisibilityConverter}}" />
                                            <Border Height="1" BorderBrush="{Binding BorderColor, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ColorBrushConverter}}" BorderThickness="1" VerticalAlignment="Bottom"  
                                                    Visibility="{Binding BottomBorder, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ToggleVisibilityConverter}}" />
                                            <Border Height="1" BorderBrush="{Binding BorderColor, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ColorBrushConverter}}" BorderThickness="1" VerticalAlignment="Center"  
                                                    Visibility="{Binding HorizontalBorder, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ToggleVisibilityConverter}}" />
                                            <Line X2="{Binding RelativeSource={RelativeSource AncestorType=Grid}, Path=ActualWidth}" Y2="{Binding RelativeSource={RelativeSource AncestorType=Grid, AncestorLevel=1}, Path=ActualHeight}"
                                                  Visibility="{Binding DiagonalDownBorder, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ToggleVisibilityConverter}}" Stroke="{Binding BorderColor, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ColorBrushConverter}}"/>
                                            <Line X1="{Binding RelativeSource={RelativeSource AncestorType=Grid}, Path=ActualWidth}" Y2="{Binding RelativeSource={RelativeSource AncestorType=Grid}, Path=ActualHeight}"
                                                  Visibility="{Binding DiagonalUpBorder, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ToggleVisibilityConverter}}" Stroke="{Binding BorderColor, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ColorBrushConverter}}"/>
                                        </Grid>
                                        <ToggleButton HorizontalAlignment="Center" 
                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                      Padding="1"
                                                      Height="24"
                                                      Width="24"
                                                      Grid.Row="6" 
                                                      Grid.Column="0"      
                                                      IsChecked="{Binding DiagonalUpBorder, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                            <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/DiagonalUpBorder_Icon.png"  
                                                       Height="16" 
                                                       Width="16"/>
                                        </ToggleButton>
                                        <ToggleButton HorizontalAlignment="Center"
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                          Padding="1"
                                                          Height="24"
                                                          Width="24"
                                                          Grid.Row="6" 
                                                          Grid.Column="3" 
                                                          IsChecked="{Binding LeftBorder, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                            <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/LeftBorder_Icon.png"  
                                                       Height="16" 
                                                       Width="16"/>
                                        </ToggleButton>
                                        <ToggleButton HorizontalAlignment="Center"
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}"
                                                          Padding="1"
                                                          Height="24"
                                                          Width="24"
                                                          Grid.Row="6" 
                                                          Grid.Column="5"    
                                                          IsChecked="{Binding VerticalBorder, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                            <ToggleButton.Content>
                                                <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/VerticalBorder_Icon.png"  
                                                       Height="16" 
                                                       Width="16"/>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                        <ToggleButton HorizontalAlignment="Center" 
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                          Padding="1"
                                                          Height="24"
                                                          Width="24"
                                                          Grid.Row="6"           
                                                          Grid.Column="7"
                                                          IsChecked="{Binding RightBorder, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                            <ToggleButton.Content>
                                                <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/RightBorder_Icon.png"  
                                                       Height="16" 
                                                       Width="16"/>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                        <ToggleButton HorizontalAlignment="Center" 
                                                          Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                          Padding="1"
                                                          Height="24"
                                                          Width="24"
                                                          Grid.Row="6" 
                                                          Grid.Column="10"      
                                                          IsChecked="{Binding DiagonalDownBorder, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                            <ToggleButton.Content>
                                                <Image VerticalAlignment="Center" 
                                                       HorizontalAlignment="Center" 
                                                       Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/DiagonalDownBorder_Icon.png"  
                                                       Height="16" 
                                                       Width="16"/>
                                            </ToggleButton.Content>
                                        </ToggleButton>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                        <Border Grid.Row="3" 
                                Grid.ColumnSpan="3" 
                                Height="1" 
                                BorderBrush="{StaticResource BorderAlt}" 
                                Background="{StaticResource BorderAlt}" 
                                BorderThickness="0 1 0 0" 
                                Opacity="0.5"
                                VerticalAlignment="Center"/>
                        <Label Grid.Row="5"
                               Grid.Column="1"
                               Grid.ColumnSpan="2"
                               Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogShading}"
                               VerticalAlignment="Center"
                               Foreground="{TemplateBinding Foreground}"
                               FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                               FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}" />
                        <Grid Grid.Row="7"  Grid.Column="1" Grid.ColumnSpan="2">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogFill}"
                                       VerticalAlignment="Center"
                                       Foreground="{TemplateBinding Foreground}" 
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <tools_controls_shared:ColorPickerPalette x:Name="PART_ShadingFillColorPicker" 
                                                                          Height="22" 
                                                                          Margin="5 0 5 5" 
                                                                          Width="127" 
                                                                          Padding="4 2 2 2"
                                                                          BorderThickness="1"
                                                                          BorderBrush="{StaticResource BorderAlt}"
                                                                          MoreColorOptionVisibility="Collapsed"
                                                                          BlackWhiteVisibility="Both"
                                                                          Color="{Binding ShadingFill, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                            </StackPanel>
                            <StackPanel Grid.Column="2"
                                        HorizontalAlignment="Right">
                                <Label Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=BordersShadingDialogApplyTo}"
                                       Margin="0" 
                                       VerticalAlignment="Center"
                                       Foreground="{TemplateBinding Foreground}" 
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"/>
                                <ComboBox Width="127" 
                                          Margin="4 0 0 0" 
                                          Height="22"
                                          VerticalAlignment="Center" 
                                          ItemsSource="{Binding ApplyToCollection, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"
                                          SelectedIndex="{Binding ApplyTo, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                            </StackPanel>
                        </Grid>
                        <Border Grid.Row="8"
                                Grid.ColumnSpan="3"
                                Width="Auto"
                                BorderBrush="{StaticResource BorderAlt}" 
                                Background="{StaticResource PopupBackground}"
                                BorderThickness="0 1 0 0">
                            <StackPanel Grid.Row="8"
                                            Grid.ColumnSpan="3"
                                            Orientation="Horizontal" 
                                            HorizontalAlignment="Right">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button x:Name="PART_ApplyBordersAndShadingButton" 
                                        Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOk}" 
                                        Grid.Column="0"
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 -5 6 0"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        TabIndex="9"
                                        Width="50" 
                                        IsDefault="True"
                                        Style="{StaticResource WPFPrimaryButtonStyle}"/>
                                    <Button x:Name="PART_CancelButton" 
                                        Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxCancel}"
                                        Grid.Column="1"
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 -5 6 0"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        TabIndex="10"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="67"
                                        />
                                </Grid>
                            </StackPanel>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="richtextboxadv:InsertTableDialog">
        <Setter Property="Width" Value="340"/>
        <Setter Property="Height" Value="Auto"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="richtextboxadv:InsertTableDialog">
                    <Grid Background="{StaticResource ContentBackground}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="12"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="38"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                        </Grid.ColumnDefinitions>
                        <Grid Grid.Row="1" Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="24"/>
                                <RowDefinition Height="12"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="3*" />
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogColumnSize}" Grid.Row="0" Grid.Column="0" 
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       Foreground="{TemplateBinding Foreground}" Margin="0 12 0 6" HorizontalAlignment="Stretch" VerticalAlignment="Center"/>
                            <shared:UpDown x:Name="PART_ColumnSizeBox"  Grid.Row="0" Grid.Column="2" Margin="5" Padding="0" Value="2" Step="1" NumberDecimalDigits="0" MinValue="1" MaxValue="63" TabIndex="1"/>
                            <TextBlock Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=TableDialogRowSize}" Grid.Row="1" Grid.Column="0" 
                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                       Foreground="{TemplateBinding Foreground}" Margin="0 12 0 6" HorizontalAlignment="Stretch" VerticalAlignment="Center"/>
                            <shared:UpDown x:Name="PART_RowSizeBox"  Grid.Row="1" Grid.Column="2" Margin="5" Value="2" Step="1" NumberDecimalDigits="0"  MinValue="1" MaxValue="32767" TabIndex="2"/>
                        </Grid>
                        <Border Grid.Row="2" Grid.ColumnSpan="3" Width="Auto" BorderBrush="{StaticResource BorderAlt}" Background="{StaticResource PopupBackground}" BorderThickness="0 1 0 0">
                            <StackPanel Grid.Row="2" Grid.ColumnSpan="3" Orientation="Horizontal" HorizontalAlignment="Right">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Button Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxOk}" 
                                        x:Name="PART_AddTableButton"
                                        Grid.Column="0"
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 5 9 5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="52"
                                        IsDefault="True" 
                                        TabIndex="3"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        Style="{StaticResource WPFPrimaryButtonStyle}"/>
                                    <Button Content="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=DialogBoxCancel}" 
                                        x:Name="PART_CancelButton"
                                        Grid.Column="1"
                                        Height="24"
                                        Padding="12 2 12 2"
                                        Margin="0 5 9 5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Width="69" 
                                        TabIndex="4"
                                        FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                        FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                        />
                                </Grid>
                            </StackPanel>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
