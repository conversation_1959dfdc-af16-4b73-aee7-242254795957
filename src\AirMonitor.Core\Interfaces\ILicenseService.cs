using AirMonitor.Core.Models;

namespace AirMonitor.Core.Interfaces;

/// <summary>
/// 许可证服务接口
/// 主程序和注册机共享的许可证服务定义
/// </summary>
public interface ILicenseService
{
    /// <summary>
    /// 当前许可证信息
    /// </summary>
    LicenseInfo? CurrentLicense { get; }

    /// <summary>
    /// 许可证是否有效
    /// </summary>
    bool IsLicenseValid { get; }

    /// <summary>
    /// 是否为试用版
    /// </summary>
    bool IsTrialVersion { get; }

    /// <summary>
    /// 加载许可证文件
    /// </summary>
    /// <param name="filePath">许可证文件路径</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> LoadLicenseAsync(string filePath);

    /// <summary>
    /// 验证许可证
    /// </summary>
    /// <param name="license">许可证信息</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateLicenseAsync(LicenseInfo license);

    /// <summary>
    /// 检查功能是否已授权
    /// </summary>
    /// <param name="featureCode">功能代码</param>
    /// <returns>true表示已授权，false表示未授权</returns>
    bool IsFeatureAuthorized(string featureCode);

    /// <summary>
    /// 获取已授权的功能列表
    /// </summary>
    /// <returns>功能代码列表</returns>
    List<string> GetAuthorizedFeatures();

    /// <summary>
    /// 获取许可证剩余天数
    /// </summary>
    /// <returns>剩余天数，-1表示永久</returns>
    int GetRemainingDays();

    /// <summary>
    /// 刷新许可证状态
    /// </summary>
    /// <returns>验证结果</returns>
    Task<ValidationResult> RefreshLicenseAsync();

    /// <summary>
    /// 清除当前许可证
    /// </summary>
    void ClearLicense();

    /// <summary>
    /// 许可证状态变更事件
    /// </summary>
    event EventHandler<LicenseStatusChangedEventArgs>? LicenseStatusChanged;
}

/// <summary>
/// 许可证状态变更事件参数
/// </summary>
public class LicenseStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 许可证信息
    /// </summary>
    public LicenseInfo? License { get; set; }

    /// <summary>
    /// 变更原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
}
