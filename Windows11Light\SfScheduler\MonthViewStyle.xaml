<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib"
                    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:scheduler="clr-namespace:Syncfusion.UI.Xaml.Scheduler;assembly=Syncfusion.SfScheduler.WPF">

    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="SfScheduler.TodayHighlight.Static.Background" Color="#0279FF" />

    <scheduler:TodayBorderSizeConverter x:Key="TodayBorderSizeConverter" FontSize="{StaticResource Windows11Light.BodyTextStyle}"/>

    <Style x:Key="SyncfusionMonthViewControlStyle"  TargetType="scheduler:MonthViewControl">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" >
            <Setter.Value>
                <Thickness>0,0,0,0.4</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:MonthViewControl">
                    <Border  BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <Grid
                        Background="{TemplateBinding Background}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="0" MinWidth="{Binding Path=WeekNumberColumnWidth, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <scheduler:WeekNumberPanel
                            x:Name="PART_WeekNumberPanel"
                            Grid.Column="0"
                            Visibility="Collapsed" />
                            <scheduler:MonthViewPanel x:Name="PART_MonthViewPanel" Grid.Column="1" />

                        </Grid>
                    </Border>

                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionMonthViewControlStyle}" TargetType="scheduler:MonthViewControl"/>

    <DataTemplate x:Key="SyncfusionMonthCellWindows11Template">
        <Grid>
            <Border x:Name="PART_Border" 
                    Background="Transparent">
                <Grid x:Name="PART_Grid"
                      Margin="7 ,3 ,0, 0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="0.1*" MinWidth="{Binding ElementName=PART_TextBlock,Path=ActualWidth}" />
                        <ColumnDefinition Width="0.9*" />
                    </Grid.ColumnDefinitions>
                    <TextBlock x:Name="PART_TextBlock"
                               TextAlignment="Center"
                               HorizontalAlignment="Stretch"
                               VerticalAlignment="Top"
                               Text="{Binding Path=DateText}" >
                        <TextBlock.Style>
                            <Style>
                                <Style.Triggers>
                                    <MultiDataTrigger>
                                        <MultiDataTrigger.Conditions>
                                            <Condition Binding="{Binding ElementName=PART_TextBlock,Path=IsMouseOver}" Value="true"/>
                                            <Condition Binding="{Binding Path=AllowViewNavigation}" Value="True" />
                                        </MultiDataTrigger.Conditions>
                                        <MultiDataTrigger.Setters>
                                            <Setter Property="TextBlock.TextDecorations" Value="Underline"/>
                                            <Setter Property="TextBlock.Cursor" Value="Hand"/>
                                        </MultiDataTrigger.Setters>
                                    </MultiDataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                    <Path x:Name="PART_BlackOutDate"
                          HorizontalAlignment="Left"
                          VerticalAlignment="Top" 
                          Opacity="0.7"
                          Width="{Binding ElementName=PART_TextBlock,Path=ActualWidth}"  
                          Height="{Binding ElementName=PART_TextBlock,Path=ActualHeight}"
                          Stroke="{Binding ElementName=PART_TextBlock,Path=Foreground}"
                          StrokeThickness="1"
                          Visibility="Collapsed"
                          Stretch="Fill">
                        <Path.Data>
                            <LineGeometry StartPoint="0,0" EndPoint="1,1"  />
                        </Path.Data>
                    </Path>
                </Grid>
            </Border>
        </Grid>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=DayType}" Value="Today">
                <Setter TargetName="PART_TextBlock" Property="Foreground" Value="{StaticResource PrimaryColorForeground}" />
                <Setter TargetName="PART_TextBlock" Property="FontWeight" Value="SemiBold" />
                <Setter TargetName="PART_Border" Property="BorderBrush" Value='{StaticResource PrimaryColorForeground}'/>
                <Setter TargetName="PART_Border" Property="Margin" Value="0,-1,0,0"/>
                <Setter TargetName="PART_Border" Property="BorderThickness" Value="0,2,0,0"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=DayType}" Value="NormalDay, BlackoutDay">
                <Setter TargetName="PART_BlackOutDate" Property="Visibility" Value="Visible"/>
                <Setter TargetName="PART_Grid" Property="Background" Value="{StaticResource ContentBackground}"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=DayType}" Value="Today, BlackoutDay">
                <Setter TargetName="PART_BlackOutDate" Property="Visibility" Value="Visible"/>
                <Setter TargetName="PART_TextBlock" Property="FontWeight" Value="SemiBold" />
                <Setter TargetName="PART_Grid" Property="Background" Value="{StaticResource ContentBackground}"/>
                <Setter TargetName="PART_TextBlock" Property="Foreground" Value="{StaticResource PrimaryColorForeground}" />
                <Setter TargetName="PART_Border" Property="BorderBrush" Value='{StaticResource PrimaryColorForeground}'/>
                <Setter TargetName="PART_Border" Property="Margin" Value="0,-1,0,0"/>
                <Setter TargetName="PART_Border" Property="BorderThickness" Value="0,2,0,0"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=DayType}" Value="LeadingDay, BlackoutDay">
                <Setter TargetName="PART_BlackOutDate" Property="Visibility" Value="Visible"/>
                <Setter TargetName="PART_Grid" Property="Background" Value="{StaticResource ContentBackground}"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=DayType}" Value="TrailingDay, BlackoutDay">
                <Setter TargetName="PART_BlackOutDate" Property="Visibility" Value="Visible"/>
                <Setter TargetName="PART_Grid" Property="Background" Value="{StaticResource ContentBackground}"/>
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <Style x:Key="SyncfusionMonthCellStyle" TargetType="scheduler:MonthCell">
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                <Setter Property="Background" Value="{StaticResource ContentBackgroundDisabled}"/>
            </Trigger>

            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="DayType" Value="Today" />
                    <Condition Property="IsEnabled" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter Property="Foreground" Value="{StaticResource PrimaryColorForeground}"/>
                    <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
                </MultiTrigger.Setters>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="DayType" Value="LeadingDay" />
                    <Condition Property="IsEnabled" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}"/>
                    <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
                    <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                    <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
                    <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
                    <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
                </MultiTrigger.Setters>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="DayType" Value="TrailingDay" />
                    <Condition Property="IsEnabled" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}"/>
                    <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
                    <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                    <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
                    <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
                    <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
                </MultiTrigger.Setters>
            </MultiTrigger>

            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="DayType" Value="Today,BlackoutDay" />
                    <Condition Property="IsEnabled" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter Property="Foreground" Value="{StaticResource PrimaryColorForeground}"/>
                    <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
                </MultiTrigger.Setters>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="DayType" Value="LeadingDay,BlackoutDay" />
                    <Condition Property="IsEnabled" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}"/>
                    <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
                </MultiTrigger.Setters>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="DayType" Value="TrailingDay,BlackoutDay" />
                    <Condition Property="IsEnabled" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}"/>
                    <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
                </MultiTrigger.Setters>
            </MultiTrigger>

            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="DayType" Value="Today,BlackoutDay" />
                    <Condition Property="IsEnabled" Value="False" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                    <Setter Property="Background" Value="{StaticResource ContentBackgroundDisabled}"/>
                </MultiTrigger.Setters>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="DayType" Value="LeadingDay,BlackoutDay" />
                    <Condition Property="IsEnabled" Value="False" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                    <Setter Property="Background" Value="{StaticResource ContentBackgroundDisabled}"/>
                </MultiTrigger.Setters>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="DayType" Value="TrailingDay,BlackoutDay" />
                    <Condition Property="IsEnabled" Value="False" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                    <Setter Property="Background" Value="{StaticResource ContentBackgroundDisabled}"/>
                </MultiTrigger.Setters>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="DayType" Value="NormalDay" />
                    <Condition Property="IsSelected" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter Property="Foreground" Value="{StaticResource SelectedForeground}" />
                </MultiTrigger.Setters>
            </MultiTrigger>
        </Style.Triggers>
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="UseLayoutRounding" Value="False" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="ContentTemplate" Value="{StaticResource SyncfusionMonthCellWindows11Template}" />
        <Setter Property="AllowDrop" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:MonthCell">
                    <Border 
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="SelectionStates">
                                <VisualState x:Name="Unselected" />
                                <VisualState x:Name="Selected">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames BeginTime="00:00:00"
                                                                       Storyboard.TargetName="PART_SelectionBorder"
                                                                       Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="00:00:00" Value="{x:Static Visibility.Visible}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames BeginTime="0" Storyboard.TargetName="PART_MonthCellGrid" 
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="NormalDayMouseOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames BeginTime="0" Storyboard.TargetName="PART_MonthCellContentPresenter" 
                                                                       Storyboard.TargetProperty="(TextBlock.Foreground)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource HoveredForeground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames BeginTime="0" Storyboard.TargetName="PART_MonthCellGrid" 
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>

                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid
                        x:Name="PART_MonthCellGrid"
                        Background="{TemplateBinding Background}">
                            <Border x:Name="PART_SelectionBorder"
                                    Background="{StaticResource ContentBackgroundSelected}"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    BorderThickness="0"
                                    CornerRadius="0"
                                    Visibility="Collapsed" />
                            <ContentPresenter
                            Content="{TemplateBinding DataContext}"
                            x:Name="PART_MonthCellContentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalAlignment}"
                            ContentTemplate="{TemplateBinding ContentTemplate}"
                            DataContext="{TemplateBinding DataContext}" 
                            TextBlock.FontFamily="{TemplateBinding FontFamily}"
                            TextBlock.FontSize="{TemplateBinding FontSize}"
                            TextBlock.FontWeight="{TemplateBinding FontWeight}"
                            TextBlock.Foreground="{TemplateBinding Foreground}"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                            <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionMonthCellStyle}" TargetType="scheduler:MonthCell" />

    <Style x:Key="SyncfusionSchedulerWeekNumberCellStyle" TargetType="scheduler:WeekNumberCell">
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="Padding" Value="0,4,0,0" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="UseLayoutRounding" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:WeekNumberCell">
                    <Border 
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid
                        Background="{TemplateBinding Background}">
                            <ContentPresenter
                            TextBlock.FontFamily="{TemplateBinding FontFamily}"
                            TextBlock.FontSize="{TemplateBinding FontSize}"
                            TextBlock.FontWeight="{TemplateBinding FontWeight}"
                            TextBlock.Foreground="{TemplateBinding Foreground}"
                            Content="{TemplateBinding Content}"
                            ContentTemplate="{TemplateBinding ContentTemplate}"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="Center" />
                        </Grid>
                    </Border>

                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSchedulerWeekNumberCellStyle}" TargetType="scheduler:WeekNumberCell" />
    
</ResourceDictionary>
