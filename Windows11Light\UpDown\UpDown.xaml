<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/DoubleTextBox/DoubleTextBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphRepeatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/RepeatButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="UpDown.Static.Border" Color="#9E9E9E" />
    <SolidColorBrush x:Key="UpDown.Static.NegativeBorder" Color="#333333" />

    <SolidColorBrush x:Key="UpDown.MouseOver.Border" Color="#d1eaf5" />

    <SolidColorBrush x:Key="UpDown.Focused.Border" Color="#d1eaf5" />

    <sys:Double x:Key="UpDown.Border.Static.Opacity">1</sys:Double>

    <LinearGradientBrush x:Key="UpDown.Static.BorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="UpDown.Static.BorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="UpDown.Static.BorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <shared:NullToVisibilityConverter x:Key="UpDownNullToVisibilityConverter" />
    <shared:ClipConverter x:Key="UpDownclipConverter" />
    <shared:RadiusConverter x:Key="UpDownradiusConverter" />
    <shared:ProgressConverter x:Key="ProgressConverter" />

    <DataTemplate x:Key="WatermarkContentTemplate">
        <TextBlock Style="{x:Null}" Text="{Binding}" />
    </DataTemplate>
    
    <Style x:Key="SyncfusionUpDownDoubleTextBoxStyle" BasedOn="{StaticResource SyncfusionDoubleTextBoxStyle}" TargetType="{x:Type shared:DoubleTextBox}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:DoubleTextBox}">
                    <Grid x:Name="ContentHost">
                        <Border
                            x:Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            SnapsToDevicePixels="True">
                            <Border.Clip>
                                <RectangleGeometry RadiusX="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Border}}, Path=CornerRadius, Converter={StaticResource CornerRadiusConverter}}" RadiusY="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Border}}, Path=CornerRadius, Converter={StaticResource CornerRadiusConverter}}">
                                    <RectangleGeometry.Rect>
                                        <MultiBinding Converter="{StaticResource ClipConverter}">
                                            <Binding Path="ActualWidth" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type Border}}" />
                                            <Binding Path="ActualHeight" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type Border}}" />
                                        </MultiBinding>
                                    </RectangleGeometry.Rect>
                                </RectangleGeometry>
                            </Border.Clip>
                            <Grid x:Name="InnerContentHost">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Border
                                    x:Name="RangeAdornerBorder"
                                    Background="{TemplateBinding RangeAdornerBackground}"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                    IsHitTestVisible="False"
                                    Visibility="Collapsed"
                                    Opacity="{StaticResource UpDown.Border.Static.Opacity}">
                                    <Border.RenderTransform>
                                        <ScaleTransform>
                                            <ScaleTransform.ScaleX>
                                                <MultiBinding Converter="{StaticResource ProgressConverter}">
                                                    <Binding Path="Value" RelativeSource="{RelativeSource TemplatedParent}" />
                                                    <Binding Path="ActualWidth" RelativeSource="{RelativeSource TemplatedParent}" />
                                                    <Binding Path="MaxValue" RelativeSource="{RelativeSource TemplatedParent}" />
                                                    <Binding Path="MinValue" RelativeSource="{RelativeSource TemplatedParent}" />
                                                </MultiBinding>
                                            </ScaleTransform.ScaleX>
                                        </ScaleTransform>
                                    </Border.RenderTransform>
                                </Border>

                                <ScrollViewer
                                    x:Name="PART_ContentHost"
                                    VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Background="{TemplateBinding Background}"
                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                    Visibility="{TemplateBinding ContentElementVisibility}" 
                                    Margin="2,0,0,0">
                                </ScrollViewer>

                                <ContentControl
                                    x:Name="PART_Watermark"
                                    Grid.Row="0"
                                    Padding="{TemplateBinding Padding}"
                                    Margin="{TemplateBinding Padding}"
                                    VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Background="{TemplateBinding WatermarkBackground}"
                                    Content="{TemplateBinding WatermarkText}"
                                    ContentTemplate="{TemplateBinding WatermarkTemplate}"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                    FontStretch="{TemplateBinding FontStretch}"
                                    FontStyle="{TemplateBinding FontStyle}"
                                    FontWeight="{StaticResource Windows11Light.FontWeightNormal}"
                                    Foreground="{TemplateBinding WatermarkTextForeground}"
                                    IsHitTestVisible="False"
                                    IsTabStop="False"
                                    Opacity="{TemplateBinding WatermarkOpacity}"
                                    Visibility="{TemplateBinding WatermarkVisibility}" />
                                
                            </Grid>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="EnableRangeAdorner" Value="False">
                            <Setter TargetName="Border" Property="Clip" Value="{x:Null}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Border" Property="Background" Value="transparent" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="EnableRangeAdorner" Value="True" />
                                <Condition Property="IsFocused" Value="False" />
                                <Condition Property="IsKeyboardFocused" Value="False" />
                                <Condition Property="IsKeyboardFocusWithin" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="RangeAdornerBorder" Property="Visibility" Value="Visible" />
                            <Setter TargetName="RangeAdornerBorder" Property="CornerRadius" Value="{StaticResource Windows11Light.CornerRadius4}"/>
                            <Setter TargetName="RangeAdornerBorder" Property="Margin" Value="-2,-1,-1,-1"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
   
    <Style x:Key="SyncfusionUpDownStyle" TargetType="{x:Type shared:UpDown}">
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="NegativeBackground" Value="{StaticResource ContentBackground}" />
        <Setter Property="NegativeBorderBrush" Value="{StaticResource UpDown.Static.BorderBrush}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="NegativeForeground" Value="{StaticResource ErrorForeground}" />
        <Setter Property="RangeAdornerBackground" Value="{StaticResource BorderAlt4}" />
        <Setter Property="BorderBrush" Value="{StaticResource UpDown.Static.BorderBrush}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FocusedBackground" Value="{StaticResource ContentBackground}" />
        <Setter Property="FocusedBorderBrush" Value="{StaticResource UpDown.Static.BorderBrushFocused}" />
        <Setter Property="FocusedForeground" Value="{StaticResource ContentForeground}" />
        <Setter Property="ZeroColor" Value="{StaticResource WarningForeground}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type shared:UpDown}">
                    <Grid>
                        <Border
                            x:Name="Border"
                            
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            SnapsToDevicePixels="True">
                            <Border.Clip>
                                <RectangleGeometry RadiusX="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Border}}, Path=CornerRadius, Converter={StaticResource UpDownradiusConverter}}" RadiusY="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Border}}, Path=CornerRadius, Converter={StaticResource UpDownradiusConverter}}">
                                    <RectangleGeometry.Rect>
                                        <MultiBinding Converter="{StaticResource UpDownclipConverter}">
                                            <Binding Path="ActualWidth" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type Border}}" />
                                            <Binding Path="ActualHeight" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type Border}}" />
                                        </MultiBinding>
                                    </RectangleGeometry.Rect>
                                </RectangleGeometry>
                            </Border.Clip>

                            <Grid ClipToBounds="True">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition />
                                    <RowDefinition />
                                </Grid.RowDefinitions>

                                <shared:DoubleTextBox
                                    x:Name="textBox"
                                    Grid.RowSpan="2"
                                    Grid.Column="0"
                                    HorizontalContentAlignment="Center"
                                    VerticalContentAlignment="Center"
                                    ApplyNegativeForeground="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=EnableNegativeColors, Mode=TwoWay}"
                                    ApplyZeroColor="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=ApplyZeroColor, Mode=TwoWay}"
                                    Background="Transparent"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                    ContextMenu="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=ContextMenu}"
                                    Culture="{TemplateBinding Culture}"
                                    EnableExtendedScrolling="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=EnableExtendedScrolling, Mode=TwoWay}"
                                    EnableRangeAdorner="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=EnableRangeAdorner, Mode=TwoWay}"
                                    EnableTouch="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=EnableTouch, Mode=TwoWay}"
                                    EnterToMoveNext="False"
                                    Focusable="{TemplateBinding Focusable}"
                                    MinHeight="{TemplateBinding MinHeight}"
                                    GroupSeperatorEnabled="{TemplateBinding GroupSeperatorEnabled}"
                                    IsTabStop="{TemplateBinding IsTabStop}"
                                    NumberFormat="{TemplateBinding NumberFormatInfo}"
                                    PositiveForeground="{TemplateBinding Foreground}"
                                    RangeAdornerBackground="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=RangeAdornerBackground, Mode=TwoWay}"
                                    Style="{StaticResource SyncfusionUpDownDoubleTextBoxStyle}"
                                    TabIndex="{TemplateBinding TabIndex}"
                                    TextAlignment="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TextAlignment, Mode=TwoWay}"
                                    Visibility="Collapsed"
                                    ZeroColor="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=ZeroColor, Mode=TwoWay}"
                                    FontSize="{TemplateBinding FontSize}"
                                    FontWeight="{TemplateBinding FontWeight}"
                                    FontFamily="{TemplateBinding FontFamily}"/>
                                <shared:DoubleTextBox
                                    x:Name="DoubleTextBox"
                                    Grid.RowSpan="2"
                                    Grid.Column="0"
                                    Padding="1 0 0 0"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    HorizontalContentAlignment="Center"
                                    VerticalContentAlignment="Center"
                                    ApplyNegativeForeground="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=EnableNegativeColors, Mode=TwoWay}"
                                    ApplyZeroColor="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=ApplyZeroColor, Mode=TwoWay}"
                                    Background="Transparent"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                    ContextMenu="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=ContextMenu}"
                                    CornerRadius="{TemplateBinding CornerRadius}"
                                    Culture="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Culture, Mode=TwoWay}"
                                    EnableExtendedScrolling="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=EnableExtendedScrolling, Mode=TwoWay}"
                                    EnableRangeAdorner="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=EnableRangeAdorner, Mode=TwoWay}"
                                    EnableTouch="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=EnableTouch, Mode=TwoWay}"
                                    EnterToMoveNext="False"
                                    Focusable="{TemplateBinding Focusable}"
                                    GroupSeperatorEnabled="{TemplateBinding GroupSeperatorEnabled}"
                                    IsReadOnly="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IsReadOnly, Mode=TwoWay}"
                                    IsScrollingOnCircle="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IsScrollingOnCircle, Mode=TwoWay}"
                                    IsTabStop="{TemplateBinding IsTabStop}"
                                    MinHeight="{TemplateBinding MinHeight}"
                                    MaxValidation="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=MaxValidation, Mode=TwoWay}"
                                    MaxValue="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=MaxValue, Mode=TwoWay}"
                                    MaxValueOnExceedMaxDigit="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=MaxValueOnExceedMaxDigit, Mode=TwoWay}"
                                    MinValidation="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=MinValidation, Mode=TwoWay}"
                                    MinValue="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=MinValue, Mode=TwoWay}"
                                    MinValueOnExceedMinDigit="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=MinValueOnExceedMinDigit, Mode=TwoWay}"
                                    NullValue="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=NullValue, Mode=TwoWay}"
                                    NumberDecimalDigits="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=NumberDecimalDigits, Mode=TwoWay}"
                                    NumberFormat="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=NumberFormatInfo, Mode=TwoWay}"
                                    PositiveForeground="{TemplateBinding Foreground}"
                                    RangeAdornerBackground="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=RangeAdornerBackground, Mode=TwoWay}"
                                    Step="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Step, Mode=TwoWay}"
                                    Style="{StaticResource SyncfusionUpDownDoubleTextBoxStyle}"
                                    TabIndex="{TemplateBinding TabIndex}"
                                    TextAlignment="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TextAlignment, Mode=TwoWay}"
                                    UseNullOption="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=UseNullOption, Mode=TwoWay}"
                                    WatermarkText="{TemplateBinding NullValueText}"
                                    WatermarkTextForeground="{TemplateBinding Foreground}"
                                    WatermarkTextIsVisible="True"
                                    ZeroColor="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=ZeroColor, Mode=TwoWay}"
                                    Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Value, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
					                FontSize="{TemplateBinding FontSize}"
                                    FontWeight="{TemplateBinding FontWeight}"
                                    FontFamily="{TemplateBinding FontFamily}"/>

                                <RepeatButton
                                    x:Name="upbutton"
                                    Grid.Row="0"
                                    Grid.Column="1"
                                    Width="NaN"
                                    MinHeight="0"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                    Background="Transparent"
                                    Command="shared:UpDown.m_upValue"
                                    Focusable="{TemplateBinding Focusable}"
                                    IsTabStop="False"
                                    SnapsToDevicePixels="True"
                                    Style="{StaticResource WPFRepeatButtonStyle}"
                                    Margin="4,2,4,1"
                                    Padding="5,0,5,0">
                                    <RepeatButton.Content>
                                        <Path x:Name="upbuttonpath"
                                                    Width="10"
                                                    Height="6"
                                                    HorizontalAlignment="Stretch"
                                                    VerticalAlignment="Stretch"
                                                    StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}"
                                                    SnapsToDevicePixels="True"
                                                    Stretch="Uniform">
                                            <Path.Data>
                                                <PathGeometry>M0.5 4.875C0.5 4.77344 0.537109 4.68555 0.611328 4.61133L4.73633 0.486328C4.81055 0.412109 4.89844 0.375 5 0.375C5.10156 0.375 5.18945 0.412109 5.26367 0.486328L9.38867 4.61133C9.46289 4.68555 9.5 4.77344 9.5 4.875C9.5 4.97656 9.46289 5.06445 9.38867 5.13867C9.31445 5.21289 9.22656 5.25 9.125 5.25C9.02344 5.25 8.93555 5.21289 8.86133 5.13867L5 1.2832L1.13867 5.13867C1.06445 5.21289 0.976562 5.25 0.875 5.25C0.773438 5.25 0.685547 5.21289 0.611328 5.13867C0.537109 5.06445 0.5 4.97656 0.5 4.875Z</PathGeometry>
                                            </Path.Data>
                                            <Path.Style>
                                                <Style TargetType="Path">
                                                    <Setter Property="Fill" Value="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"/>
                                                </Style>
                                            </Path.Style>
                                        </Path>
                                    </RepeatButton.Content>
                                </RepeatButton>

                                <RepeatButton
                                    x:Name="downbutton"
                                    Grid.Row="2"
                                    Grid.Column="1"
                                    Width="NaN"
                                    MinHeight="0"
                                    Margin="4,1,4,2"
                                    Padding="5,0,5,0"
                                    Background="Transparent"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                    Command="shared:UpDown.m_downValue"
                                    Focusable="{TemplateBinding Focusable}"
                                    IsTabStop="False"
                                    SnapsToDevicePixels="True"
                                    Style="{StaticResource WPFRepeatButtonStyle}">
                                    <RepeatButton.Content>
                                        <Path x:Name="downbuttonpath"
                                                    Width="10"
                                                    Height="6"
                                                    HorizontalAlignment="Stretch"
                                                    VerticalAlignment="Stretch"
                                                    StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}"
                                                    SnapsToDevicePixels="True"
                                                    Stretch="Uniform">
                                            <Path.Data>
                                                <PathGeometry>M0.5 1.125C0.5 1.02344 0.537109 0.935547 0.611328 0.861328C0.685547 0.787109 0.773438 0.75 0.875 0.75C0.976562 0.75 1.06445 0.787109 1.13867 0.861328L5 4.7168L8.86133 0.861328C8.93555 0.787109 9.02344 0.75 9.125 0.75C9.22656 0.75 9.31445 0.787109 9.38867 0.861328C9.46289 0.935547 9.5 1.02344 9.5 1.125C9.5 1.22656 9.46289 1.31445 9.38867 1.38867L5.26367 5.51367C5.18945 5.58789 5.10156 5.625 5 5.625C4.89844 5.625 4.81055 5.58789 4.73633 5.51367L0.611328 1.38867C0.537109 1.31445 0.5 1.22656 0.5 1.125Z</PathGeometry>
                                            </Path.Data>
                                            <Path.Style>
                                                <Style TargetType="Path">
                                                    <Setter Property="Fill" Value="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"/>
                                                </Style>
                                            </Path.Style>
                                        </Path>
                                    </RepeatButton.Content>
                                </RepeatButton>
                            </Grid>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter TargetName="DoubleTextBox" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="downbuttonpath" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="upbuttonpath" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                        <Trigger SourceName="Border" Property="CornerRadius" Value="0">
                            <Setter TargetName="Border" Property="Clip" Value="{x:Null}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource UpDown.Static.BorderBrushHovered}" />
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt5}" />
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>
                        <Trigger Property="IsUpdownFocused" Value="true">
                            <Setter TargetName="Border" Property="Background" Value="{Binding Path=FocusedBackground, RelativeSource={RelativeSource TemplatedParent}}" />
                            <Setter TargetName="Border" Property="BorderBrush" Value="{Binding Path=FocusedBorderBrush, RelativeSource={RelativeSource TemplatedParent}}" />
                            <Setter TargetName="DoubleTextBox" Property="Foreground" Value="{Binding Path=FocusedForeground, RelativeSource={RelativeSource TemplatedParent}}" />
                            <Setter TargetName="textBox" Property="Foreground" Value="{Binding Path=FocusedForeground, RelativeSource={RelativeSource TemplatedParent}}" />
                        </Trigger>
                        <Trigger Property="IsValueNegative" Value="true">
                            <Setter TargetName="Border" Property="Background" Value="{Binding Path=NegativeBackground, RelativeSource={RelativeSource TemplatedParent}}" />
                            <Setter TargetName="Border" Property="BorderBrush" Value="{Binding Path=NegativeBorderBrush, RelativeSource={RelativeSource TemplatedParent}}" />
                            <Setter Property="Foreground" Value="{Binding Path=NegativeForeground, RelativeSource={RelativeSource TemplatedParent}}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsKeyboardFocusWithin" Value="true" />
                                <Condition Property="EnableFocusedColors" Value="true" />
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" Value="{Binding Path=FocusedBackground, RelativeSource={RelativeSource TemplatedParent}}" />
                            <Setter Property="BorderBrush" Value="{Binding Path=FocusedBorderBrush, RelativeSource={RelativeSource TemplatedParent}}" />
                            <Setter TargetName="DoubleTextBox" Property="Foreground" Value="{Binding Path=FocusedForeground, RelativeSource={RelativeSource TemplatedParent}}" />
                            <Setter TargetName="textBox" Property="Foreground" Value="{Binding Path=FocusedForeground, RelativeSource={RelativeSource TemplatedParent}}" />
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}" />
                            <Setter TargetName="upbutton" Property="Padding" Value="5 0 5 -1"/>
                            <Setter TargetName="downbutton" Property="Padding" Value="5 0 5 -1"/>
                            <Setter Property="Padding" Value="0,0,0,-1"/>
                        </MultiTrigger>

                        <Trigger SourceName="upbutton" Property="IsMouseOver" Value="True">
                            <Setter TargetName="upbuttonpath" Property="Fill" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger SourceName="upbutton" Property="IsPressed" Value="True">
                            <Setter TargetName="upbuttonpath" Property="Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger SourceName="downbutton" Property="IsMouseOver" Value="True">
                            <Setter TargetName="downbuttonpath" Property="Fill" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger SourceName="downbutton" Property="IsPressed" Value="True">
                            <Setter TargetName="downbuttonpath" Property="Fill" Value="{StaticResource IconColorSelected}" />
                        </Trigger>                        
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionUpDownStyle}" TargetType="{x:Type shared:UpDown}" />
</ResourceDictionary>
