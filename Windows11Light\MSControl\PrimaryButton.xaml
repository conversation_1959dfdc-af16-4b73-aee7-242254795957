<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                                      
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                     xmlns:po="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="PrimaryButton.Static.Border" Color="#ececec"/>

    <SolidColorBrush x:Key="PrimaryButton.MouseOver.Border" Color="Transparent"/>

    <LinearGradientBrush x:Key="PrimaryButtonBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource PrimaryButtonBorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource PrimaryButtonBorder.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="PrimaryButtonBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource PrimaryButtonBorderHoveredGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource PrimaryButtonBorderHovered.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="WPFPrimaryButtonStyle"
           TargetType="{x:Type Button}">
        <Setter Property="FocusVisualStyle"
                Value="{x:Null}"/>
        <Setter Property="Background"                                
                Value="{StaticResource PrimaryBackground}"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource PrimaryButtonBorderBrush}"/>
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="Foreground"
                Value="{StaticResource PrimaryForeground}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="HorizontalContentAlignment"
                Value="Center"/>
        <Setter Property="VerticalContentAlignment"
                Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="border"                       
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"                            
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            
                            SnapsToDevicePixels="true" 
                            >
                        <ContentPresenter Margin="{TemplateBinding Padding}" 
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"  
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"                                          
                                          RecognizesAccessKey="True" 
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default"/>
                                <Condition Property="IsFocused" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource PrimaryBackground}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt3}"/>
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource PrimaryButtonBorderBrushHovered}"/>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource PrimaryBorderSelected}"/>
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForegroundOpacity1}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt4}"/>
                            <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}"/>
                            <Setter Property="Opacity" Value="1"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary>
