using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using AirMonitor.Core.Interfaces;
using AirMonitor.Core.Models;
using Microsoft.Extensions.Logging;

namespace AirMonitor.LicenseGenerator.ViewModels;

/// <summary>
/// 许可证验证器视图模型
/// 管理许可证验证相关的界面逻辑
/// </summary>
public partial class LicenseValidatorViewModel : ViewModelBase
{
    private readonly ILicenseGeneratorService _licenseGeneratorService;
    private readonly ILogger<LicenseValidatorViewModel> _logger;

    private string _licenseFilePath = string.Empty;
    private string _licenseContent = string.Empty;
    private LicenseInfo? _licenseInfo;
    private ValidationResult? _validationResult;
    private bool _isValid = false;

    public LicenseValidatorViewModel(
        ILicenseGeneratorService licenseGeneratorService,
        ILogger<LicenseValidatorViewModel> logger)
    {
        _licenseGeneratorService = licenseGeneratorService;
        _logger = logger;

        InitializeCommands();

        Title = "许可证验证器";
        StatusMessage = "请选择要验证的许可证文件";
    }

    #region 属性

    /// <summary>
    /// 许可证文件路径
    /// </summary>
    public string LicenseFilePath
    {
        get => _licenseFilePath;
        set => SetProperty(ref _licenseFilePath, value);
    }

    /// <summary>
    /// 许可证内容
    /// </summary>
    public string LicenseContent
    {
        get => _licenseContent;
        set => SetProperty(ref _licenseContent, value);
    }

    /// <summary>
    /// 许可证信息
    /// </summary>
    public LicenseInfo? LicenseInfo
    {
        get => _licenseInfo;
        set => SetProperty(ref _licenseInfo, value);
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public ValidationResult? ValidationResult
    {
        get => _validationResult;
        set => SetProperty(ref _validationResult, value);
    }

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid
    {
        get => _isValid;
        set => SetProperty(ref _isValid, value);
    }

    #endregion

    #region 命令

    /// <summary>
    /// 选择文件命令
    /// </summary>
    public ICommand SelectFileCommand { get; private set; } = null!;

    /// <summary>
    /// 验证许可证命令
    /// </summary>
    public ICommand ValidateLicenseCommand { get; private set; } = null!;

    /// <summary>
    /// 清除结果命令
    /// </summary>
    public ICommand ClearResultCommand { get; private set; } = null!;

    #endregion

    /// <summary>
    /// 初始化命令
    /// </summary>
    private void InitializeCommands()
    {
        SelectFileCommand = new AsyncRelayCommand(SelectFileAsync);
        ValidateLicenseCommand = new AsyncRelayCommand(ValidateLicenseAsync, CanValidateLicense);
        ClearResultCommand = new RelayCommand(ClearResult);
    }

    /// <summary>
    /// 选择文件
    /// </summary>
    private async Task SelectFileAsync()
    {
        // TODO: 实现文件选择对话框
        await Task.Delay(100);
        SetInfoStatus("文件选择功能待实现");
    }

    /// <summary>
    /// 验证许可证
    /// </summary>
    private async Task ValidateLicenseAsync()
    {
        await ExecuteAsync(async () =>
        {
            // TODO: 获取公钥（从配置或密钥文件）
            var publicKey = "临时公钥"; // 这里需要实际的公钥
            
            var result = await _licenseGeneratorService.ValidateLicenseFileAsync(LicenseFilePath, publicKey);
            ValidationResult = result;
            IsValid = result.IsSuccess;
            
            if (result.IsSuccess && result.Data is LicenseInfo license)
            {
                LicenseInfo = license;
                _logger.LogInformation("许可证验证成功: {LicenseId}", license.LicenseId);
            }
            else
            {
                LicenseInfo = null;
                _logger.LogWarning("许可证验证失败: {Error}", result.ErrorMessage);
            }
        }, "正在验证许可证...", "许可证验证完成");
    }

    /// <summary>
    /// 清除结果
    /// </summary>
    private void ClearResult()
    {
        LicenseFilePath = string.Empty;
        LicenseContent = string.Empty;
        LicenseInfo = null;
        ValidationResult = null;
        IsValid = false;
        
        SetInfoStatus("结果已清除");
    }

    /// <summary>
    /// 检查是否可以验证许可证
    /// </summary>
    private bool CanValidateLicense()
    {
        return !string.IsNullOrWhiteSpace(LicenseFilePath);
    }

    #region 公共方法

    /// <summary>
    /// 激活时调用
    /// </summary>
    public void OnActivated()
    {
        SetInfoStatus("许可证验证器已激活");
    }

    /// <summary>
    /// 打开许可证
    /// </summary>
    public async void OpenLicense()
    {
        await SelectFileAsync();
    }

    #endregion
}
