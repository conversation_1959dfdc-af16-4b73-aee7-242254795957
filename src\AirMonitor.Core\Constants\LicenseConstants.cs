namespace AirMonitor.Core.Constants;

/// <summary>
/// 许可证相关常量定义
/// </summary>
public static class LicenseConstants
{
    /// <summary>
    /// 产品名称
    /// </summary>
    public const string ProductName = "AirMonitor";

    /// <summary>
    /// 产品版本
    /// </summary>
    public const string ProductVersion = "1.0.0";

    /// <summary>
    /// 许可证文件扩展名
    /// </summary>
    public const string LicenseFileExtension = ".lic";

    /// <summary>
    /// 默认许可证文件名
    /// </summary>
    public const string DefaultLicenseFileName = "license.lic";

    /// <summary>
    /// 许可证ID前缀
    /// </summary>
    public const string LicenseIdPrefix = "LIC";

    /// <summary>
    /// 默认有效期天数（1年）
    /// </summary>
    public const int DefaultValidityDays = 365;

    /// <summary>
    /// 永久许可证标识
    /// </summary>
    public const int PermanentLicense = -1;

    /// <summary>
    /// 无设备数量限制标识
    /// </summary>
    public const int UnlimitedDevices = -1;

    /// <summary>
    /// 许可证验证间隔（分钟）
    /// </summary>
    public const int ValidationIntervalMinutes = 30;

    /// <summary>
    /// 硬件指纹长度
    /// </summary>
    public const int HardwareFingerprintLength = 16;

    /// <summary>
    /// 许可证模板文件夹
    /// </summary>
    public const string TemplateFolder = "templates";

    /// <summary>
    /// Excel批量模板文件名
    /// </summary>
    public const string ExcelTemplateName = "batch_template.xlsx";

    /// <summary>
    /// 密钥文件夹
    /// </summary>
    public const string KeysFolder = "keys";

    /// <summary>
    /// 私钥文件名
    /// </summary>
    public const string PrivateKeyFileName = "private.key";

    /// <summary>
    /// 公钥文件名
    /// </summary>
    public const string PublicKeyFileName = "public.key";
}
