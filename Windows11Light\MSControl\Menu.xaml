<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:po="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Separator.xaml"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <MenuScrollingVisibilityConverter x:Key="MenuScrollingVisibilityConverter"/>
    
    <Style x:Key="MenuItemFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="{StaticResource Windows11Light.FocusMargin}"
                               Stroke="{StaticResource BorderAlt}" 
                               StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Light.StrokeDashArray}" 
                               SnapsToDevicePixels="true" 
                               />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="{ComponentResourceKey TypeInTargetAssembly={x:Type FrameworkElement}, ResourceId=SubmenuContent}"
                     TargetType="{x:Type ContentControl}">
        <Border Background="{StaticResource PopupBackground}"
                BorderBrush="{StaticResource BorderAlt}"
                BorderThickness="{StaticResource Windows11Light.BorderThickness}">
            <Grid>
                <Rectangle Fill="{StaticResource PopupBackground}"
                           HorizontalAlignment="Left"
                           Width="28"/>
                <Rectangle HorizontalAlignment="Left"
                           Width="1"
                           Fill="{StaticResource BorderAlt}"/>
                <Rectangle HorizontalAlignment="Left"
                           Width="1"
                           Fill="{StaticResource BorderAlt}"/>
                <ContentPresenter Grid.ColumnSpan="2"/>
            </Grid>
        </Border>
    </ControlTemplate>

    <Style x:Key="MenuScrollButtonFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="{StaticResource Windows11Light.FocusMargin}"
                               SnapsToDevicePixels="true" 
                               Stroke="{StaticResource BorderAlt3}"
                               StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Light.StrokeDashArray}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style x:Key="WPFMenuScrollButton"
           TargetType="{x:Type RepeatButton}"
           BasedOn="{x:Null}">
        <Setter Property="ClickMode"
                Value="Hover"/>
        <Setter Property="MinWidth"
                Value="0"/>
        <Setter Property="MinHeight"
                Value="0"/>
        <Setter Property="Height"
                Value="16"/>
        <Setter Property="FocusVisualStyle" Value="{StaticResource MenuScrollButtonFocusVisual}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <DockPanel x:Name="bg" 
                               Background="Transparent"
                               SnapsToDevicePixels="true">
                        <Rectangle DockPanel.Dock="Right"
                                   x:Name="R1"
                                   Width="0"
                                   Stroke="Transparent"
                                   Fill="Transparent"/>
                        <Rectangle DockPanel.Dock="Bottom"
                                   x:Name="B1"
                                   Height="0" 
                                   Stroke="Transparent"
                                   Fill="Transparent"/>
                        <Rectangle DockPanel.Dock="Left"
                                   x:Name="L1"
                                   Width="0" 
                                   Stroke="Transparent"
                                   Fill="Transparent"/>
                        <Rectangle DockPanel.Dock="Top"
                                   x:Name="T1"
                                   Height="0" 
                                   Stroke="Transparent"
                                   Fill="Transparent"/>
                        <ContentPresenter x:Name="ContentContainer"
                                          Margin="2,2,2,2"
                                          VerticalAlignment="Center"
                                          HorizontalAlignment="Center"/>
                    </DockPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsPressed"
                                 Value="true">
                            <Setter TargetName="R1"
                                    Property="Fill"
                                    Value="{StaticResource PopupPressedBackground}"/>
                            <Setter TargetName="R1"
                                    Property="Stroke"
                                    Value="{StaticResource PopupPressedBackground}"/>
                            <Setter TargetName="B1"
                                    Property="Fill"
                                    Value="{StaticResource PopupPressedBackground}"/>
                            <Setter TargetName="B1"
                                    Property="Stroke"
                                    Value="{StaticResource PopupPressedBackground}"/>
                            <Setter TargetName="L1"
                                    Property="Fill"
                                    Value="{StaticResource PopupPressedBackground}"/>
                            <Setter TargetName="L1"
                                    Property="Stroke"
                                    Value="{StaticResource PopupPressedBackground}"/>
                            <Setter TargetName="T1"
                                    Property="Fill"
                                    Value="{StaticResource PopupPressedBackground}"/>
                            <Setter TargetName="T1"
                                    Property="Stroke"
                                    Value="{StaticResource PopupPressedBackground}"/>
                            <Setter TargetName="ContentContainer"
                                    Property="Margin"
                                    Value="3,3,1,1"/>
                            <Setter TargetName="bg" Property="Background" Value="{StaticResource PopupPressedBackground}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver"
                                 Value="true">
                            <Setter TargetName="R1"
                                    Property="Fill"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter TargetName="R1"
                                    Property="Stroke"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter TargetName="B1"
                                    Property="Fill"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter TargetName="B1"
                                    Property="Stroke"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter TargetName="L1"
                                    Property="Fill"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter TargetName="L1"
                                    Property="Stroke"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter TargetName="T1"
                                    Property="Fill"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter TargetName="T1"
                                    Property="Stroke"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter TargetName="ContentContainer"
                                    Property="Margin"
                                    Value="3,3,1,1"/>
                            <Setter TargetName="bg" Property="Background" Value="{StaticResource PopupHoveredBackground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="False">
                            <Setter TargetName="R1"
                                    Property="Fill"
                                    Value="{StaticResource PopupDisabledBackground}"/>
                            <Setter TargetName="R1"
                                    Property="Stroke"
                                    Value="{StaticResource PopupDisabledBackground}"/>
                            <Setter TargetName="B1"
                                    Property="Fill"
                                    Value="{StaticResource PopupDisabledBackground}"/>
                            <Setter TargetName="B1"
                                    Property="Stroke"
                                    Value="{StaticResource PopupDisabledBackground}"/>
                            <Setter TargetName="L1"
                                    Property="Fill"
                                    Value="{StaticResource PopupDisabledBackground}"/>
                            <Setter TargetName="L1"
                                    Property="Stroke"
                                    Value="{StaticResource PopupDisabledBackground}"/>
                            <Setter TargetName="T1"
                                    Property="Fill"
                                    Value="{StaticResource PopupDisabledBackground}"/>
                            <Setter TargetName="T1"
                                    Property="Stroke"
                                    Value="{StaticResource PopupDisabledBackground}"/>
                            <Setter TargetName="ContentContainer"
                                    Property="Margin"
                                    Value="3,3,1,1"/>
                            <Setter TargetName="bg" Property="Background" Value="{StaticResource PopupDisabledBackground}"/>
                        </Trigger>                        
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFMenuScrollViewer"
           TargetType="{x:Type ScrollViewer}"
           BasedOn="{x:Null}">
        <Setter Property="HorizontalScrollBarVisibility"
                Value="Hidden"/>
        <Setter Property="VerticalScrollBarVisibility"
                Value="Auto"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollViewer}">
                    <Grid SnapsToDevicePixels="true">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Border x:Name="border" Grid.Row="1" 
                                Grid.Column="0" 
                                CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                Background="{StaticResource PopupBackground}">
                            <ScrollContentPresenter Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <RepeatButton x:Name="upButton"
                                      Style="{StaticResource WPFMenuScrollButton}"
                                      Grid.Row="0"
                                      Grid.Column="0"
                                      Command="{x:Static ScrollBar.LineUpCommand}"
                                      CommandTarget="{Binding RelativeSource={RelativeSource TemplatedParent}}"
                                      Focusable="false">
                            <RepeatButton.Visibility>
                                <MultiBinding FallbackValue="Visibility.Collapsed"
                                              Converter="{StaticResource MenuScrollingVisibilityConverter}"
                                              ConverterParameter="0">
                                    <Binding RelativeSource="{RelativeSource TemplatedParent}"
                                             Path="ComputedVerticalScrollBarVisibility"/>
                                    <Binding RelativeSource="{RelativeSource TemplatedParent}"
                                             Path="VerticalOffset"/>
                                    <Binding RelativeSource="{RelativeSource TemplatedParent}"
                                             Path="ExtentHeight"/>
                                    <Binding RelativeSource="{RelativeSource TemplatedParent}"
                                             Path="ViewportHeight"/>
                                </MultiBinding>
                            </RepeatButton.Visibility>
                            <TextBlock x:Name="upPath"
                                       Text="&#xe709;"
                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                       Foreground="{StaticResource IconColor}" />
                        </RepeatButton>
                        <RepeatButton x:Name="downButton"
                                      Style="{StaticResource WPFMenuScrollButton}"
                                      Grid.Row="2"
                                      Grid.Column="0"
                                      Command="{x:Static ScrollBar.LineDownCommand}"
                                      CommandTarget="{Binding RelativeSource={RelativeSource TemplatedParent}}"
                                      Focusable="false">
                            <RepeatButton.Visibility>
                                <MultiBinding FallbackValue="Visibility.Collapsed"
                                              Converter="{StaticResource MenuScrollingVisibilityConverter}"
                                              ConverterParameter="100">
                                    <Binding RelativeSource="{RelativeSource TemplatedParent}"
                                             Path="ComputedVerticalScrollBarVisibility"/>
                                    <Binding RelativeSource="{RelativeSource TemplatedParent}"
                                             Path="VerticalOffset"/>
                                    <Binding RelativeSource="{RelativeSource TemplatedParent}"
                                             Path="ExtentHeight"/>
                                    <Binding RelativeSource="{RelativeSource TemplatedParent}"
                                             Path="ViewportHeight"/>
                                </MultiBinding>
                            </RepeatButton.Visibility>
                            <TextBlock x:Name="downPath"
                                       Text="&#xe708;"
                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                       Foreground="{StaticResource IconColor}" />
                        </RepeatButton>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsPressed" SourceName="upButton"
                                 Value="true">
                            <Setter TargetName="upPath"
                                    Property="Foreground"
                                    Value="{StaticResource IconColorSelected}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" SourceName="upButton"
                                 Value="true">
                            <Setter TargetName="upPath"
                                    Property="Foreground"
                                    Value="{StaticResource IconColorHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" SourceName="downButton"
                                 Value="true">
                            <Setter TargetName="downPath"
                                    Property="Foreground"
                                    Value="{StaticResource IconColorSelected}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" SourceName="downButton"
                                 Value="true">
                            <Setter TargetName="downPath"
                                    Property="Foreground"
                                    Value="{StaticResource IconColorHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" SourceName="upButton"
                                 Value="true">
                            <Setter TargetName="downPath"
                                    Property="Foreground"
                                    Value="{StaticResource IconColorDisabled}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" SourceName="downButton"
                                 Value="false">
                            <Setter TargetName="downPath"
                                    Property="Foreground"
                                    Value="{StaticResource IconColorDisabled}"/>
                        </Trigger>
						<Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter TargetName="border"
                                    Property="Background"
                                    Value="{StaticResource PopupBackground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="{x:Static MenuItem.SeparatorStyleKey}" TargetType="{x:Type Separator}">
        <Setter Property="Background"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="Margin"
                Value="0,2,0,2"/>
        <Setter Property="Focusable"
                Value="false"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Separator}">
                    <Grid SnapsToDevicePixels="true">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition MinWidth="24" Width="Auto" SharedSizeGroup="MenuItemIconColumnGroup" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="1" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFMenuStyle" TargetType="{x:Type Menu}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Menu}">
                    <Border x:Name="border" BorderBrush="{TemplateBinding BorderBrush}" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            Background="{TemplateBinding Background}" 
                            Padding="{TemplateBinding Padding}" 
                            SnapsToDevicePixels="true">
                        <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                         <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" TargetName="border" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background"  Value="Transparent"/>
                <Setter Property="BorderBrush"  Value="{StaticResource PopupBackground}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    
    <Style BasedOn="{StaticResource WPFMenuStyle}" TargetType="{x:Type Menu}"/>

    <Style x:Key="WPFMenuItemStyle"
           TargetType="{x:Type MenuItem}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource MenuItemFocusVisual}"/>
        <Setter Property="Background"
                Value="{StaticResource PopupBackground}"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="Foreground"
                Value="{StaticResource PopupForeground}"/>
        <Setter Property="HorizontalContentAlignment"
                Value="Left"/>
        <Setter Property="VerticalContentAlignment"
                Value="Center"/>
        <Setter Property="ScrollViewer.PanningMode" Value="Both"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type MenuItem}">
                    <Border Name="Bg"
                            Background="{TemplateBinding Background}"
                            BorderBrush="Transparent"
                            BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                            Margin="4,2">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition MinWidth="24"
                                      Width="Auto"
                                      SharedSizeGroup="MenuItemIconColumnGroup"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"
                                      SharedSizeGroup="MenuItemIGTColumnGroup"/>
                                <ColumnDefinition Width="17"/>
                            </Grid.ColumnDefinitions>
                            <ContentPresenter x:Name="Icon"
                                              VerticalAlignment="Center"
                                              ContentSource="Icon"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                            <Border x:Name="GlyphPanel"
                                    Background="{StaticResource PopupBackground}"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                    CornerRadius="3"
                                    Visibility="Hidden"
                                    Width="22" 
                                    Height="22">
                                <TextBlock x:Name="Glyph"
                                           Text="&#xe70c;"
                                           FontSize="14"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                           Foreground="{StaticResource IconColor}"
                                           Margin="0,5,0,0"
                                           FlowDirection="LeftToRight"/>
                            </Border>
                            <ContentPresenter Grid.Column="1"
                                              ContentSource="Header"
                                              Margin="{TemplateBinding MenuItem.Padding}"
                                              RecognizesAccessKey="True" 
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                            <TextBlock Grid.Column="2"
                                       VerticalAlignment="Center"
                                       Text="{TemplateBinding MenuItem.InputGestureText}"
                                       Margin="{TemplateBinding MenuItem.Padding}"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                            <Trigger Property="Icon"
                     Value="{x:Null}">
                                <Setter TargetName="Icon"
                                    Property="Visibility"
                                    Value="Collapsed"/>
                            </Trigger>
                            <Trigger Property="IsChecked"
                     Value="true">
                                <Setter TargetName="GlyphPanel"
                                    Property="Visibility"
                                    Value="Visible"/>
                                <Setter TargetName="Icon"
                                    Property="Visibility"
                                    Value="Collapsed"/>
                            </Trigger>
                            <Trigger Property="IsHighlighted"
                     Value="true">
                                <Setter TargetName="Bg"
                                    Property="Background"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                                <Setter TargetName="Bg"
                                    Property="BorderBrush"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                                <Setter TargetName="GlyphPanel"
                                    Property="Background"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                                <Setter TargetName="GlyphPanel"
                                    Property="BorderBrush"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                                <Setter TargetName="Glyph"
                                    Property="Foreground"
                                    Value="{StaticResource IconColorHovered}"/>
                                <Setter Property="Foreground"
                                    Value="{StaticResource PopupHoveredForeground}"/>
                            </Trigger>
                            <Trigger Property="IsEnabled"
                     Value="false">
                                <Setter TargetName="GlyphPanel"
                                    Property="Opacity"
                                    Value="0.5"/>
                                <Setter TargetName="Bg"
                                    Property="Background"
                                    Value="{StaticResource PopupDisabledBackground}"/>
                                <Setter TargetName="Bg"
                                    Property="BorderBrush"
                                    Value="{StaticResource PopupDisabledBackground}"/>
                                <Setter TargetName="Glyph"
                                    Property="Foreground"
                                    Value="{StaticResource IconColorDisabled}"/>
                                <Setter Property="Foreground"
                                    Value="{StaticResource PopupDisabledForeground}"/>
                                <Setter Property="Opacity" 
                                        Value="1"/>
                        </Trigger>
                            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            </Trigger>
                            <Trigger Property="IsEnabled"
                     Value="true">
                                <Setter TargetName="GlyphPanel"
                                    Property="Opacity"
                                    Value="1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Role"
                     Value="TopLevelHeader">
                <Setter Property="Padding"
                        Value="8,4,8,4"/>
                <Setter Property="Background"
                Value="Transparent"/>
                <Setter Property="BorderBrush"
                Value="{StaticResource PopupBorder}"/>
                <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
                <Setter Property="VerticalContentAlignment"
                Value="Center"/>
                <Setter Property="Margin"
                        Value="4,2"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate 
                     TargetType="{x:Type MenuItem}">
                            <Grid SnapsToDevicePixels="true">
                                <Rectangle Name="Bg"
                                           Fill="{TemplateBinding Background}"
                                           Stroke="Transparent"
                                           StrokeThickness="0"
                                           Margin="0"
                                           RadiusX="4"
                                           RadiusY="4" />
                                <DockPanel>
                                    <ContentPresenter x:Name="Icon"
                                                      Margin="4,0,6,0"
                                                      VerticalAlignment="Center"
                                                      ContentSource="Icon"
                                                      SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                    <TextBlock x:Name="Glyph"
                                               Visibility="Collapsed"
                                               VerticalAlignment="Center"
                                               Text="&#xe70c;"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               Foreground="{StaticResource IconColor}"
                                               Margin="7,5,0,0"
                                               FlowDirection="LeftToRight"/>
                                    <ContentPresenter x:Name="content"
                                                      ContentSource="Header"
                                                      Margin="{TemplateBinding MenuItem.Padding}"
                                                      RecognizesAccessKey="True" 
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                                        <ContentPresenter.Resources>
                                            <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                        </ContentPresenter.Resources>
                                    </ContentPresenter>
                                </DockPanel>
                                <Popup x:Name="PART_Popup"
                                       HorizontalOffset="1"
                                       VerticalOffset="-1"
                                       AllowsTransparency="true"
                                       Placement="Bottom"
                                       IsOpen="{Binding Path=IsSubmenuOpen,RelativeSource={RelativeSource TemplatedParent}}"
                                       Focusable="false"
                                       PopupAnimation="{StaticResource {x:Static SystemParameters.MenuPopupAnimationKey}}">
                                    <Border x:Name="Shdw" 
                                            BorderBrush="{StaticResource BorderAlt}"
                                            CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                                            BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                            Effect="{StaticResource Default.ShadowDepth4}"
                                            Padding="0,4,0,4">
                                        <ScrollViewer CanContentScroll="true"
                                                          Style="{StaticResource WPFMenuScrollViewer}">
                                            <StackPanel IsItemsHost="True"
                                                            KeyboardNavigation.DirectionalNavigation="Cycle" />
                                        </ScrollViewer>

                                        <Border.RenderTransform>
                                            <TransformGroup>
                                                <ScaleTransform />
                                            </TransformGroup>
                                        </Border.RenderTransform>

                                        <Border.Triggers>
                                            <EventTrigger RoutedEvent="Border.Loaded">
                                                <BeginStoryboard>
                                                    <Storyboard>                                                                
                                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(Effect).Opacity"
                                                                                       Storyboard.TargetName="Shdw">
                                                            <SplineDoubleKeyFrame KeyTime="00:00:00"
                                                                                  Value="0" />
                                                            <SplineDoubleKeyFrame KeyTime="00:00:0.4"
                                                                                  Value="0" />
                                                            <SplineDoubleKeyFrame KeyTime="00:00:0.5"
                                                                                  Value="0.17" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger>
                                        </Border.Triggers>
                                    </Border>
                                </Popup>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsSuspendingPopupAnimation"
                     Value="true">
                                    <Setter TargetName="PART_Popup"
                                            Property="PopupAnimation"
                                            Value="None"/>
                                </Trigger>
                                <Trigger Property="Icon"
                     Value="{x:Null}">
                                    <Setter TargetName="Icon"
                                            Property="Visibility"
                                            Value="Collapsed"/>
                                </Trigger>
                                <Trigger Property="IsChecked"
                     Value="true">
                                    <Setter TargetName="Glyph"
                                            Property="Visibility"
                                            Value="Visible"/>
                                    <Setter TargetName="Icon"
                                            Property="Visibility"
                                            Value="Collapsed"/>
                                </Trigger>
                                <Trigger SourceName="PART_Popup"
                                        Property="Popup.HasDropShadow"
                                        Value="true">
                                    <Setter TargetName="Shdw"
                                            Property="Margin"
                                            Value="14,0,14,14"/>
                                    <Setter TargetName="Shdw"
                                            Property="Background"
                                            Value="{StaticResource PopupBackground}"/>
                                </Trigger>
                                <Trigger Property="IsHighlighted" Value="true">
                                    <Setter TargetName="Bg"
                                            Property="Fill"
                                            Value="{StaticResource ContentBackgroundHovered}"/>
                                    <Setter TargetName="Bg"
                                            Property="Stroke"
                                            Value="Transparent"/>
									<Setter TargetName="Glyph"
                                            Property="Foreground"
                                            Value="{StaticResource IconColorHovered}"/>
                                    <Setter TargetName="content" 
                                            Property="TextBlock.Foreground"
                                            Value="{StaticResource ContentForeground}"/>
                                </Trigger>
                                <Trigger Property="IsKeyboardFocused" Value="true">
                                    <Setter TargetName="Bg"
                                            Property="Stroke"
                                            Value="Transparent"/>
                                    <Setter TargetName="Bg"
                                            Property="Fill"
                                            Value="{StaticResource ContentBackgroundSelected}"/>
                                    <Setter TargetName="Glyph"
                                            Property="Foreground"
                                            Value="{StaticResource IconColorSelected}"/>
                                    <Setter TargetName="content" 
                                            Property="TextBlock.Foreground"
                                            Value="{StaticResource ContentForeground}"/>
                                </Trigger>
                                <Trigger Property="IsSubmenuOpen" Value="true">
                                    <Setter TargetName="Bg"
                                            Property="Stroke"
                                            Value="Transparent"/>
                                    <Setter TargetName="Bg" 
                                            Property="Fill" 
                                            Value="{StaticResource ContentBackgroundSelected}"/>
                                    <Setter TargetName="Glyph"
                                            Property="Foreground"
                                            Value="{StaticResource IconColorSelected}"/>
                                    <Setter TargetName="content" 
                                            Property="TextBlock.Foreground"
                                            Value="{StaticResource ContentForeground}"/>
                                </Trigger>
                                <Trigger Property="IsEnabled" 
                                         Value="false">
                                    <Setter TargetName="Bg"
                                            Property="Fill"
                                            Value="Transparent"/>
                                    <Setter TargetName="Bg"
                                            Property="Stroke"
                                            Value="Transparent"/>
                                    <Setter TargetName="Glyph"
                                            Property="Foreground"
                                            Value="{StaticResource IconColorDisabled}"/>
                                    <Setter TargetName="content" 
                                            Property="TextBlock.Foreground"
                                            Value="{StaticResource DisabledForeground}"/>
                                    <Setter Property="Opacity" Value="1"/>
                                </Trigger>
                                <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                                    <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                                </Trigger>
                                <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                                    <Setter Property="FocusVisualStyle" Value="{StaticResource CurveKeyboardFocusVisualStyle}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="Role"
                     Value="TopLevelItem">
                <Setter Property="Padding"
                        Value="8,4,8,4"/>
                <Setter Property="Background"
                Value="Transparent"/>
                <Setter Property="BorderBrush"
                Value="{StaticResource PopupBorder}"/>
                <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"/>
                <Setter Property="Margin"
                        Value="4,2"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate 
                     TargetType="{x:Type MenuItem}">
                            <Grid SnapsToDevicePixels="true">
                                <Rectangle Name="Bg"
                                           Fill="{TemplateBinding Background}"
                                           Stroke="Transparent"
                                           StrokeThickness="0"
                                           Margin="0"
                                           RadiusX="4"
                                           RadiusY="4" />
                                <DockPanel>
                                    <ContentPresenter x:Name="Icon"
                                                      Margin="4,0,6,0"
                                                      VerticalAlignment="Center"
                                                      ContentSource="Icon"
                                                      SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                    <TextBlock x:Name="Glyph"
                                               Visibility="Collapsed"
                                               VerticalAlignment="Center"
                                               Text="&#xe70c;"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               Foreground="{StaticResource IconColor}"
                                               Margin="7,5,0,0"
                                               FlowDirection="LeftToRight"/>
                                    <ContentPresenter x:Name="ContentElement"
                                                      ContentSource="Header"
                                                      Margin="{TemplateBinding MenuItem.Padding}"
                                                      RecognizesAccessKey="True" 
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                </DockPanel>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="Icon"
                     Value="{x:Null}">
                                    <Setter TargetName="Icon"
                                            Property="Visibility"
                                            Value="Collapsed"/>
                                </Trigger>
                                <Trigger Property="IsChecked"
                     Value="true">
                                    <Setter TargetName="Glyph"
                                            Property="Visibility"
                                            Value="Visible"/>
                                    <Setter TargetName="Icon"
                                            Property="Visibility"
                                            Value="Collapsed"/>
                                </Trigger>
                                <Trigger Property="IsHighlighted"
                     Value="true">
                                    <Setter TargetName="Bg"
                                            Property="Fill"
                                            Value="{StaticResource ContentBackgroundHovered}"/>
                                    <Setter TargetName="Bg"
                                            Property="Stroke"
                                            Value="Transparent"/>
                                    <Setter Property="Foreground"
                                            Value="{StaticResource ContentForeground}"/>
                                </Trigger>
                                <Trigger Property="IsKeyboardFocused"
                     Value="true">
                                    <Setter TargetName="Bg"
                                            Property="Stroke"
                                            Value="Transparent"/>
                                    <Setter TargetName="Bg"
                                            Property="Fill"
                                            Value="{StaticResource ContentBackgroundSelected}"/>
                                </Trigger>
                                <Trigger Property="IsEnabled"
                     Value="false">
                                    <Setter TargetName="Bg"
                                            Property="Fill"
                                            Value="Transparent"/>
                                    <Setter TargetName="Bg"
                                            Property="Stroke"
                                            Value="Transparent"/>
                                    <Setter TargetName="Glyph"
                                            Property="Foreground"
                                            Value="{StaticResource IconColorDisabled}"/>
                                    <Setter Property="Foreground"
                                            Value="{StaticResource DisabledForeground}"/>
                                    <Setter Property="Opacity" Value="1"/>
                                    <Setter TargetName="ContentElement" Property="TextBlock.Foreground" Value="{StaticResource IconColorDisabled}"/>
                                </Trigger>
                                 <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                                    <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                                </Trigger>
                                <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                                    <Setter Property="FocusVisualStyle" Value="{StaticResource CurveKeyboardFocusVisualStyle}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="Role"
                     Value="SubmenuHeader">
                <Setter Property="Padding"
                        Value="8,3,8,3"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate 
                     TargetType="{x:Type MenuItem}">
                            <Border Name="Bg"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="Transparent"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                    CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                    Margin="4,2">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition MinWidth="24"
                                      Width="Auto"
                                      SharedSizeGroup="MenuItemIconColumnGroup"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"
                                      SharedSizeGroup="MenuItemIGTColumnGroup"/>
                                        <ColumnDefinition Width="17"/>
                                    </Grid.ColumnDefinitions>
                                    <ContentPresenter x:Name="Icon"
                                                      Margin="1"
                                                      VerticalAlignment="Center"
                                                      ContentSource="Icon"
                                                      SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                    <Border x:Name="GlyphPanel"
                                            Background="{StaticResource PopupBackground}"
                                            BorderBrush="{StaticResource BorderAlt}"
                                            BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                            CornerRadius="3"
                                            Margin="1"
                                            Visibility="Hidden"
                                            Width="22" 
                                            Height="22">
                                        <TextBlock x:Name="Glyph"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"
                                                   Text="&#xe70c;"
                                                   FontSize="14"
                                                   FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                                   Foreground="{StaticResource IconColor}"
                                                   FlowDirection="LeftToRight"
                                                   Margin="0,5,0,0"/>
                                    </Border>
                                    <ContentPresenter Grid.Column="1"
                                                      ContentSource="Header"
                                                      Margin="{TemplateBinding MenuItem.Padding}"
                                                      RecognizesAccessKey="True" 
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                    <TextBlock Grid.Column="2"
                                            Text="{TemplateBinding MenuItem.InputGestureText}"
                                            Margin="{TemplateBinding MenuItem.Padding}"
                                            VerticalAlignment="Center"/>
                                    <TextBlock x:Name="Glyph1"  
                                               Grid.Column="3"
                                               VerticalAlignment="Center"
                                               Margin="4,0,0,0"
                                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                               Foreground="{StaticResource IconColor}"
                                               Text="&#xe704;"/>
                                    <Popup x:Name="PART_Popup"
                                       AllowsTransparency="true"
                                       Placement="Right"
                                       VerticalOffset="-3"
                                       HorizontalOffset="-2"
                                       IsOpen="{Binding Path=IsSubmenuOpen,RelativeSource={RelativeSource TemplatedParent}}"
                                       Focusable="false"
                                       PopupAnimation="{StaticResource {x:Static SystemParameters.MenuPopupAnimationKey}}">
                                        <Border Name="Shdw"
                                            Background="Transparent"
                                            BorderBrush="{StaticResource BorderAlt}" 
                                            CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                                            BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                            Effect="{StaticResource Default.ShadowDepth4}"
											Grid.IsSharedSizeScope="true"
                                            Padding="0,4,0,4">
                                            <ScrollViewer CanContentScroll="true" 
                                                              Style="{StaticResource WPFMenuScrollViewer}">
                                                <StackPanel IsItemsHost="True"
                                                            KeyboardNavigation.DirectionalNavigation="Cycle" />
                                                </ScrollViewer>
                                        </Border>
                                    </Popup>
                                </Grid>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsSuspendingPopupAnimation"
                     Value="true">
                                    <Setter TargetName="PART_Popup"
                        Property="PopupAnimation"
                        Value="None"/>
                                </Trigger>
                                <Trigger Property="Icon"
                     Value="{x:Null}">
                                    <Setter TargetName="Icon"
                                            Property="Visibility"
                                            Value="Collapsed"/>
                                </Trigger>
                                <Trigger Property="IsChecked"
                     Value="true">
                                    <Setter TargetName="GlyphPanel"
                                            Property="Visibility"
                                            Value="Visible"/>
                                    <Setter TargetName="Icon"
                                            Property="Visibility"
                                            Value="Collapsed"/>
                                </Trigger>
                                <Trigger SourceName="PART_Popup"
                                         Property="Popup.HasDropShadow"
                                         Value="true">
                                    <Setter TargetName="Shdw"
                                            Property="Margin"
                                            Value="14,0,14,14"/>
                                    <Setter TargetName="Shdw"
                                            Property="Background"
                                            Value="{StaticResource PopupBackground}"/>
                                </Trigger>
                                <Trigger Property="IsHighlighted"
                     Value="true">
                                    <Setter TargetName="Bg"
                                            Property="Background"
                                            Value="{StaticResource PopupHoveredBackground}"/>
                                    <Setter TargetName="Bg"
                                            Property="BorderBrush"
                                            Value="{StaticResource PopupHoveredBackground}"/>
                                    <Setter TargetName="GlyphPanel"
                                            Property="Background"
                                            Value="{StaticResource PopupHoveredBackground}"/>
                                    <Setter TargetName="GlyphPanel"
                                            Property="BorderBrush"
                                            Value="{StaticResource PopupHoveredBackground}"/>
                                    <Setter TargetName="Glyph"
                                            Property="Foreground"
                                            Value="{StaticResource IconColorHovered}"/>
                                    <Setter TargetName="Glyph1"
                                            Property="Foreground"
                                            Value="{StaticResource IconColorHovered}"/>
                                    <Setter Property="Foreground"
                                            Value="{StaticResource PopupHoveredForeground}"/>
                                </Trigger>
                                <Trigger Property="IsEnabled"
                     Value="false">
                                    <Setter TargetName="GlyphPanel"
                                            Property="Opacity"
                                            Value="0.5"/>
                                    <Setter TargetName="Bg"
                                            Property="Background"
                                            Value="{StaticResource PopupDisabledBackground}"/>
                                    <Setter TargetName="Bg"
                                            Property="BorderBrush"
                                            Value="{StaticResource PopupDisabledBackground}"/>
                                    <Setter TargetName="Glyph"
                                            Property="Foreground"
                                             Value="{StaticResource IconColorDisabled}"/>
                                    <Setter Property="Foreground"
                                            Value="{StaticResource PopupDisabledForeground}"/>
                                    <Setter Property="Opacity" Value="1"/>
                                    <Setter TargetName="Glyph1" Property="Foreground" Value="{StaticResource IconColorDisabled}"/>
                                </Trigger>
                                <Trigger Property="IsEnabled"
                     Value="true">
                                    <Setter TargetName="GlyphPanel"
                                            Property="Opacity"
                                            Value="1"/>
                                </Trigger>
                                <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                                    <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                                </Trigger>
                                <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                                    <Setter Property="FocusVisualStyle" Value="{StaticResource CurveKeyboardFocusVisualStyle}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="Role"
                     Value="SubmenuItem">
                <Setter Property="Padding"
                        Value="8,3,8,3"/>
			    <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate 
                     TargetType="{x:Type MenuItem}">
                            <Border Name="Bg"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="Transparent"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                    CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                                    Margin="4,2">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition MinWidth="24"
                                      Width="Auto"
                                      SharedSizeGroup="MenuItemIconColumnGroup"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"
                                      SharedSizeGroup="MenuItemIGTColumnGroup"/>
                                    </Grid.ColumnDefinitions>
                                    <ContentPresenter x:Name="Icon"
                                                      Margin="1"
                                                      VerticalAlignment="Center"
                                                      ContentSource="Icon"
                                                      SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                    <Border x:Name="GlyphPanel"
                                            Background="{StaticResource PopupBackground}"
                                            BorderBrush="{StaticResource BorderAlt}"
                                            BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                            CornerRadius="3"
                                            Margin="1"
                                            Visibility="Hidden"
                                            Width="22" 
                                            Height="22">
                                        <TextBlock x:Name="Glyph"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"
                                                   Text="&#xe70c;" 
                                                   FontSize="14"
                                                   FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                                   Foreground="{StaticResource IconColor}"
                                                   Margin="0,5,0,0"
                                                   FlowDirection="LeftToRight"/>
                                    </Border>
                                    <ContentPresenter Grid.Column="1"
                                                      ContentSource="Header"
                                                      Margin="{TemplateBinding MenuItem.Padding}"
                                                      RecognizesAccessKey="True" 
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                    <TextBlock Grid.Column="2"
                                            Text="{TemplateBinding MenuItem.InputGestureText}"
                                            Margin="{TemplateBinding MenuItem.Padding}"
                                            VerticalAlignment="Center"/>
                                </Grid>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsSuspendingPopupAnimation"
                     Value="true">
                                </Trigger>
                                <Trigger Property="Icon"
                     Value="{x:Null}">
                                    <Setter TargetName="Icon"
                                            Property="Visibility"
                                            Value="Collapsed"/>
                                </Trigger>
                                <Trigger Property="IsChecked"
                     Value="true">
                                    <Setter TargetName="GlyphPanel"
                                            Property="Visibility"
                                            Value="Visible"/>
                                    <Setter TargetName="Icon"
                                            Property="Visibility"
                                            Value="Collapsed"/>
                                </Trigger>
                                <Trigger Property="IsHighlighted"
                     Value="true">
                                    <Setter TargetName="Bg"
                                            Property="Background"
                                            Value="{StaticResource PopupHoveredBackground}"/>
                                    <Setter TargetName="Bg"
                                            Property="BorderBrush"
                                            Value="{StaticResource PopupHoveredBackground}"/>
                                    <Setter TargetName="GlyphPanel"
                                            Property="Background"
                                            Value="Transparent"/>
                                    <Setter TargetName="GlyphPanel"
                                            Property="BorderBrush"
                                            Value="{StaticResource PopupHoveredBackground}"/>
                                    <Setter TargetName="Glyph"
                                            Property="Foreground"
                                            Value="{StaticResource IconColorHovered}"/>
                                    <Setter Property="Foreground"
                                            Value="{StaticResource PopupHoveredForeground}"/>
                                </Trigger>
                                <Trigger Property="IsEnabled"
                     Value="false">
                                    <Setter TargetName="GlyphPanel"
                                            Property="Opacity"
                                            Value="0.5"/>
                                    <Setter TargetName="Bg"
                                            Property="Background"
                                            Value="{StaticResource PopupDisabledBackground}"/>
                                    <Setter TargetName="Bg"
                                            Property="BorderBrush"
                                            Value="{StaticResource PopupDisabledBackground}"/>
                                    <Setter TargetName="Glyph"
                                            Property="Foreground"
                                             Value="{StaticResource IconColorDisabled}"/>
                                    <Setter Property="Foreground"
                                            Value="{StaticResource PopupDisabledForeground}"/>
                                    <Setter Property="Opacity" Value="1"/>
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="true">
                                    <Setter TargetName="GlyphPanel"
                                            Property="Opacity"
                                            Value="1"/>
                                </Trigger>
                                <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                                    <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                                </Trigger>
                                <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                                    <Setter Property="FocusVisualStyle" Value="{StaticResource CurveKeyboardFocusVisualStyle}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelHeader" />
                    <Condition Property="IsHighlighted" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelHeader" />
                    <Condition Property="IsKeyboardFocused" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelItem" />
                    <Condition Property="IsHighlighted" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Role" Value="TopLevelItem" />
                    <Condition Property="IsKeyboardFocused" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
            </MultiTrigger>
        </Style.Triggers>        
    </Style>
    <Style BasedOn="{StaticResource WPFMenuItemStyle}" TargetType="{x:Type MenuItem}"/>

    <Style x:Key="WPFContextMenuStyle"
           TargetType="{x:Type ContextMenu}">
        <Setter Property="Background"
                Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="Foreground" Value="{StaticResource Menu.Static.Foreground}"/>
        <Setter Property="MenuItem.Foreground"
                Value="{StaticResource PopupForeground}"/>
        <Setter Property="VerticalContentAlignment"
                Value="Center"/>
        <Setter Property="Padding"
                Value="2"/>
        <Setter Property="Grid.IsSharedSizeScope"
                Value="true"/>
        <Setter Property="HasDropShadow"
                Value="{StaticResource {x:Static SystemParameters.DropShadowKey}}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ContextMenu}">                    
                    <Border Name="Shdw"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                            BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                            Padding="0"
                            Effect="{StaticResource Default.ShadowDepth4}"
                            SnapsToDevicePixels="true">
                        <Grid>
                            <Rectangle Fill="{TemplateBinding Background}"
                                        HorizontalAlignment="Left"
                                        Width="28"
                                        Margin="2"
                                        RadiusX="2"
                                        RadiusY="2"/>
                            <Rectangle HorizontalAlignment="Left"
                                        Width="1"
                                        Margin="30,2,0,2"
                                        Fill="{TemplateBinding BorderBrush}"/>
                            <Rectangle HorizontalAlignment="Left"
                                        Width="1"
                                        Margin="31,2,0,2"
                                        Fill="{TemplateBinding BorderBrush}"/>
                            <ScrollViewer CanContentScroll="true"
                                            Grid.ColumnSpan="2" Margin="1,0"
                                            Style="{StaticResource WPFMenuScrollViewer}">
                                <ItemsPresenter Margin="{TemplateBinding Padding}"
                                                KeyboardNavigation.DirectionalNavigation="Cycle"
                                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                            </ScrollViewer>
                        </Grid>
                    </Border>                    
                    <ControlTemplate.Triggers>
                        <Trigger Property="HasDropShadow"
                                 Value="true">
                            <Setter TargetName="Shdw"
                                    Property="Margin"
                                    Value="14,0,14,14"/>
                            <Setter TargetName="Shdw"
                                    Property="Background"
                                    Value="Transparent"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background"  Value="Transparent"/>
                <Setter Property="BorderBrush"  Value="{StaticResource PopupBackground}"/>
                <Setter Property="Foreground" Value="{StaticResource Menu.Disabled.Foreground}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource WPFContextMenuStyle}" TargetType="{x:Type ContextMenu}"/>
    
</ResourceDictionary>
