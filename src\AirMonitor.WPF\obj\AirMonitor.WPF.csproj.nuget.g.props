﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;E:\DevExpress 23.2\Components\Offline Packages;e:\DevExpress 24.2\Components\Offline Packages;E:\Microsoft Visual Studio\Shared\NuGetPackages;D:\Syncfusion\Essential Studio\WPF\29.1.33\ToolboxNuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="E:\DevExpress 23.2\Components\Offline Packages\" />
    <SourceRoot Include="e:\DevExpress 24.2\Components\Offline Packages\" />
    <SourceRoot Include="E:\Microsoft Visual Studio\Shared\NuGetPackages\" />
    <SourceRoot Include="D:\Syncfusion\Essential Studio\WPF\29.1.33\ToolboxNuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\9.0.5\buildTransitive\net8.0\Microsoft.Extensions.Configuration.UserSecrets.props" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\9.0.5\buildTransitive\net8.0\Microsoft.Extensions.Configuration.UserSecrets.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore\9.0.5\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore\9.0.5\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgSyncfusion_Shared_WPF Condition=" '$(PkgSyncfusion_Shared_WPF)' == '' ">C:\Users\<USER>\.nuget\packages\syncfusion.shared.wpf\29.2.9</PkgSyncfusion_Shared_WPF>
    <PkgSyncfusion_SfInput_WPF Condition=" '$(PkgSyncfusion_SfInput_WPF)' == '' ">C:\Users\<USER>\.nuget\packages\syncfusion.sfinput.wpf\29.2.9</PkgSyncfusion_SfInput_WPF>
    <PkgSyncfusion_SfBusyIndicator_WPF Condition=" '$(PkgSyncfusion_SfBusyIndicator_WPF)' == '' ">C:\Users\<USER>\.nuget\packages\syncfusion.sfbusyindicator.wpf\29.2.9</PkgSyncfusion_SfBusyIndicator_WPF>
    <PkgSyncfusion_SfScheduler_WPF Condition=" '$(PkgSyncfusion_SfScheduler_WPF)' == '' ">C:\Users\<USER>\.nuget\packages\syncfusion.sfscheduler.wpf\29.2.9</PkgSyncfusion_SfScheduler_WPF>
    <PkgSyncfusion_SfGrid_WPF Condition=" '$(PkgSyncfusion_SfGrid_WPF)' == '' ">C:\Users\<USER>\.nuget\packages\syncfusion.sfgrid.wpf\29.2.9</PkgSyncfusion_SfGrid_WPF>
    <PkgSyncfusion_SfGauge_WPF Condition=" '$(PkgSyncfusion_SfGauge_WPF)' == '' ">C:\Users\<USER>\.nuget\packages\syncfusion.sfgauge.wpf\29.2.9</PkgSyncfusion_SfGauge_WPF>
    <PkgSyncfusion_SfChart_WPF Condition=" '$(PkgSyncfusion_SfChart_WPF)' == '' ">C:\Users\<USER>\.nuget\packages\syncfusion.sfchart.wpf\29.2.9</PkgSyncfusion_SfChart_WPF>
  </PropertyGroup>
</Project>