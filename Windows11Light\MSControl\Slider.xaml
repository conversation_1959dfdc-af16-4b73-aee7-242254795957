<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/RepeatButton.xaml"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <LinearGradientBrush x:Key="ThumbBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderAlt5Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt5.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ThumbBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderAlt5Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt5.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ThumbBorderBrushPressed" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderAlt5Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt5.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>
    
     <LinearGradientBrush x:Key="ThumbBorderBrushDisabled" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderAlt5Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt5.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="SliderFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="{StaticResource Windows11Light.FocusMargin}"
                               Stroke="{StaticResource BorderAlt3}" 
                               StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Light.StrokeDashArray}" 
                               SnapsToDevicePixels="true" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="SliderThumbHorizontalTop" TargetType="{x:Type Thumb}">
        <Grid UseLayoutRounding="True" 
              HorizontalAlignment="Center" 
              VerticalAlignment="Center">
            <Path x:Name="gripBorder" 
                  Visibility="Visible"
                  Width="20" 
                  Height="20"
                  Data="M362,462.135928 L367.166007,457 L372,462.135928 L372,475 C372,476.104569 371.104569,477 370,477 L367.068498,477 L367.068498,477 L364,477 C362.895431,477 362,476.104569 362,475 L362,462.135928 L362,462.135928 Z" 
                  Fill="Transparent" 
                  Stretch="Fill" 
                  Stroke="{StaticResource ThumbBorderBrush}" 
                  StrokeThickness="1"
                  UseLayoutRounding="True" 
                  VerticalAlignment="Center"
                  SnapsToDevicePixels="True" />
            
            <Path x:Name="grip" 
                  Width="18" 
                  Height="18"
                  Data="M362,462.135928 L367.166007,457 L372,462.135928 L372,475 C372,476.104569 371.104569,477 370,477 L367.068498,477 L367.068498,477 L364,477 C362.895431,477 362,476.104569 362,475 L362,462.135928 L362,462.135928 Z" 
                  Fill="{StaticResource PrimaryBackground}" 
                  Stretch="Fill" 
                  Stroke="{StaticResource ContentBackground}" 
                  StrokeThickness="3"
                  UseLayoutRounding="True" 
                  VerticalAlignment="Center"
                  SnapsToDevicePixels="True" />
            <Path x:Name="OverlayCircle" 
                  Width="40" 
                  Height="40"
                  Data="M362,462.135928 L367.166007,457 L372,462.135928 L372,475 C372,476.104569 371.104569,477 370,477 L367.068498,477 L367.068498,477 L364,477 C362.895431,477 362,476.104569 362,475 L362,462.135928 L362,462.135928 Z" 
                  Stretch="Fill" 
                  UseLayoutRounding="True" 
                  VerticalAlignment="Center"
                  SnapsToDevicePixels="True"
                  Visibility="Collapsed"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="StrokeThickness" TargetName="grip" Value="{StaticResource Windows11Light.StrokeThickness1}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushHovered}"/>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="StrokeThickness" TargetName="grip" Value="5"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushPressed}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushDisabled}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    
    <ControlTemplate x:Key="SliderThumbHorizontalBottom" TargetType="{x:Type Thumb}">
        <Grid HorizontalAlignment="Center" 
              UseLayoutRounding="True" 
              VerticalAlignment="Center">
            <Path x:Name="gripBorder" 
                  Width="20" 
                  Height="20"
                  Visibility="Visible"
                  Data="M203,457 L209,457 C210.104569,457 211,457.895431 211,459 L211,471.666667 L211,471.666667 L206.068498,477 L201,471.666667 L201,459 C201,457.895431 201.895431,457 203,457 Z" 
                  Fill="Transparent"
                  Stretch="Fill"  
                  Stroke="{StaticResource ThumbBorderBrush}" 
                  StrokeThickness="1"
                  UseLayoutRounding="True" 
                  VerticalAlignment="Center"
                  SnapsToDevicePixels="True"/>
            
            <Path x:Name="grip" 
                  Width="18" 
                  Height="18"
                  Data="M203,457 L209,457 C210.104569,457 211,457.895431 211,459 L211,471.666667 L211,471.666667 L206.068498,477 L201,471.666667 L201,459 C201,457.895431 201.895431,457 203,457 Z" 
                  Fill="{StaticResource PrimaryBackground}"
                  Stretch="Fill"  
                  Stroke="{StaticResource ContentBackground}" 
                  StrokeThickness="3"
                  UseLayoutRounding="True" 
                  VerticalAlignment="Center"
                  SnapsToDevicePixels="True"/>
            <Path x:Name="OverlayCircle" 
                  Width="40" 
                  Height="40"
                  Data="M203,457 L209,457 C210.104569,457 211,457.895431 211,459 L211,471.666667 L211,471.666667 L206.068498,477 L201,471.666667 L201,459 C201,457.895431 201.895431,457 203,457 Z" 
                  Stretch="Fill"  
                  UseLayoutRounding="True" 
                  VerticalAlignment="Center"
                  SnapsToDevicePixels="True"
                  Visibility="Collapsed"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="StrokeThickness" TargetName="grip" Value="{StaticResource Windows11Light.StrokeThickness1}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushHovered}"/>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="StrokeThickness" TargetName="grip" Value="5"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushPressed}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushDisabled}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="RepeatButtonTransparent" TargetType="{x:Type RepeatButton}" BasedOn="{StaticResource WPFRepeatButtonStyle}">
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Focusable" Value="false"/>
        <Setter Property="IsTabStop" Value="false"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <Rectangle Fill="{TemplateBinding Background}" 
                               Height="{TemplateBinding Height}" 
                               Width="{TemplateBinding Width}"
                               RadiusX="4"
                               RadiusY="4"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <ControlTemplate x:Key="SliderThumbHorizontalDefault" TargetType="{x:Type Thumb}">
        <Grid HorizontalAlignment="Center" UseLayoutRounding="True" VerticalAlignment="Center">
            <Ellipse x:Name="gripBorder" 
                     Width="20" 
                     Height="20"
                     Stroke="{StaticResource ThumbBorderBrush}" 
                     StrokeThickness="1"
                     Fill="Transparent"
                     Visibility="Visible"/>
            <Ellipse x:Name="grip" 
                     Width="18"
                     Height="18"
                     Fill="{StaticResource PrimaryBackground}"
                     Stroke="{StaticResource ContentBackground}"
                     StrokeThickness="3"/>
            <Ellipse x:Name="OverlayCircle"
                     Height="40"
                     Width="40"
                     Visibility="Collapsed"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" TargetName="grip" Value="{StaticResource TouchMode.MinSize}" />
                <Setter Property="MinWidth" TargetName="grip" Value="{StaticResource TouchMode.MinSize}" />
            </Trigger>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="Transparent"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushHovered}"/>
                <Trigger.EnterActions>
                    <BeginStoryboard x:Name="MouseEnterAnimation">
                        <Storyboard>
                            <DoubleAnimation Duration="0:0:0:0.167" Storyboard.TargetProperty="StrokeThickness" Storyboard.TargetName="grip" To="2"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <RemoveStoryboard BeginStoryboardName="MouseEnterAnimation"/>
                </Trigger.ExitActions>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="Transparent"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushPressed}"/>
                <Trigger.EnterActions>
                    <BeginStoryboard x:Name="DragAnimation">
                        <Storyboard>
                            <DoubleAnimation Duration="0:0:0:0.167" Storyboard.TargetProperty="StrokeThickness" Storyboard.TargetName="grip" To="5"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <RemoveStoryboard BeginStoryboardName="DragAnimation"/>
                </Trigger.ExitActions>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushDisabled}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    
    <ControlTemplate x:Key="SliderHorizontal" TargetType="{x:Type Slider}">
        <Border x:Name="border" 
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}"
                Background="{TemplateBinding Background}" 
                SnapsToDevicePixels="True">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto" MinHeight="{TemplateBinding MinHeight}"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <TickBar x:Name="TopTick"
                         Grid.Row="0" 
                         Height="5" 
                         Placement="Top" 
                         Visibility="Collapsed"
                         Fill="{StaticResource BorderAlt1}"
                         Margin="0,0,0,2"/>
                <TickBar x:Name="BottomTick" 
                         Grid.Row="2" 
                         Height="5" 
                         Placement="Bottom" 
                         Visibility="Collapsed"
                         Fill="{StaticResource BorderAlt1}"
                         Margin="0,2,0,0"/>
                <Border x:Name="TrackBackground" 
                        Grid.Row="1" 
                        Height="4"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                        Margin="5,0,5,0"
                        BorderBrush="{StaticResource BorderAlt1}" 
                        Background="{StaticResource BorderAlt1}"
                        VerticalAlignment="center">
                    <Canvas  Margin="-6,0">
                        <Rectangle x:Name="PART_SelectionRange" 
                                   Fill="{StaticResource PrimaryBackground}" 
                                   Height="2.0" 
                                   Visibility="Hidden"/>
                    </Canvas>
                </Border>
                <Track x:Name="PART_Track" 
                       Grid.Row="1">
                    <Track.DecreaseRepeatButton>
                        <RepeatButton Command="{x:Static Slider.DecreaseLarge}" 
                                      Style="{StaticResource RepeatButtonTransparent}"
                                      Height="4"
                                      Background="{StaticResource PrimaryBackground}"/>
                    </Track.DecreaseRepeatButton>
                    <Track.IncreaseRepeatButton>
                        <RepeatButton Command="{x:Static Slider.IncreaseLarge}" 
                                      Style="{StaticResource RepeatButtonTransparent}"/>
                    </Track.IncreaseRepeatButton>
                    <Track.Thumb>
                        <Thumb x:Name="Thumb"
                               Width="20" 
                               Height="20"
                               Focusable="False" 
                               Margin="0"
                               OverridesDefaultStyle="True" 
                               Template="{StaticResource SliderThumbHorizontalDefault}" 
                               VerticalAlignment="Center" />
                    </Track.Thumb>
                </Track>
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                <Setter Property="MinHeight" TargetName="Thumb" Value="{StaticResource TouchMode.MinSize}" />
                <Setter Property="MinWidth" TargetName="Thumb" Value="{StaticResource TouchMode.MinHeight}" />                
            </Trigger>            
            <Trigger Property="TickPlacement" Value="TopLeft">
                <Setter Property="Visibility" TargetName="TopTick" Value="Visible"/>
                <Setter Property="Template" TargetName="Thumb" Value="{StaticResource SliderThumbHorizontalTop}"/>
            </Trigger>
            <Trigger Property="TickPlacement" Value="BottomRight">
                <Setter Property="Visibility" TargetName="BottomTick" Value="Visible"/>
                <Setter Property="Template" TargetName="Thumb" Value="{StaticResource SliderThumbHorizontalBottom}"/>
            </Trigger>
            <Trigger Property="TickPlacement" Value="Both">
                <Setter Property="Visibility" TargetName="TopTick" Value="Visible"/>
                <Setter Property="Visibility" TargetName="BottomTick" Value="Visible"/>
            </Trigger>
            <Trigger Property="IsSelectionRangeEnabled" Value="true">
                <Setter Property="Visibility" TargetName="PART_SelectionRange" Value="Visible"/>
            </Trigger>
            <Trigger Property="IsKeyboardFocused" Value="true">
                <Setter Property="Foreground" TargetName="Thumb" Value="Blue"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" TargetName="TrackBackground" Value="{StaticResource BorderAlt}"/>
                <Setter Property="BorderBrush" TargetName="TrackBackground" Value="{StaticResource BorderAlt}"/>
                <Setter Property="Fill" TargetName="PART_SelectionRange" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Fill" TargetName="TopTick" Value="{StaticResource BorderAlt}"/>
                <Setter Property="Fill" TargetName="BottomTick" Value="{StaticResource BorderAlt}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SliderThumbVerticalLeft" TargetType="{x:Type Thumb}">
        <Grid HorizontalAlignment="Center" 
              UseLayoutRounding="True" 
              VerticalAlignment="Center">
            <Path x:Name="gripBorder"
                  Width="20" 
                  Height="20"
                  Visibility="Visible"
                  Data="M412,457 L425,457 C426.104569,457 427,457.895431 427,459 L427,465 C427,466.104569 426.104569,467 425,467 L412,467 L412,467 L407,462.111033 L412,457 Z" 
                  Fill="Transparent" 
                  Stretch="Fill" 
                  StrokeThickness="1"
                  Stroke="{StaticResource ThumbBorderBrush}" />

            <Path x:Name="grip"
                  Width="18" 
                  Height="18"
                  Data="M412,457 L425,457 C426.104569,457 427,457.895431 427,459 L427,465 C427,466.104569 426.104569,467 425,467 L412,467 L412,467 L407,462.111033 L412,457 Z" 
                  Fill="{StaticResource PrimaryBackground}" 
                  Stretch="Fill" 
                  StrokeThickness="3"
                  Stroke="{StaticResource ContentBackground}"/>
            <Path x:Name="OverlayCircle"
                  Width="40" 
                  Height="40"
                  Data="M412,457 L425,457 C426.104569,457 427,457.895431 427,459 L427,465 C427,466.104569 426.104569,467 425,467 L412,467 L412,467 L407,462.111033 L412,457 Z" 
                  Visibility="Collapsed"
                  Stretch="Fill"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="StrokeThickness" TargetName="grip" Value="{StaticResource Windows11Light.StrokeThickness1}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushHovered}"/>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="StrokeThickness" TargetName="grip" Value="5"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushPressed}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushDisabled}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    <ControlTemplate x:Key="SliderThumbVerticalRight" TargetType="{x:Type Thumb}">
        <Grid HorizontalAlignment="Center"
              UseLayoutRounding="True"
              VerticalAlignment="Center">
            <Path x:Name="gripBorder" 
                  Width="20" 
                  Height="20"
                  Visibility="Visible"
                  Data="M387,457 L400,457 L400,457 L405,462.082328 L400,467 L387,467 C385.895431,467 385,466.104569 385,465 L385,459 C385,457.895431 385.895431,457 387,457 Z"
                  Fill="Transparent"
                  Stretch="Fill" 
                  Stroke="{StaticResource ThumbBorderBrush}" 
                  StrokeThickness="1"/>
            
            <Path x:Name="grip" 
                  Width="18" 
                  Height="18"
                  Data="M387,457 L400,457 L400,457 L405,462.082328 L400,467 L387,467 C385.895431,467 385,466.104569 385,465 L385,459 C385,457.895431 385.895431,457 387,457 Z"
                  Fill="{StaticResource PrimaryBackground}"
                  Stretch="Fill" 
                  Stroke="{StaticResource ContentBackground}" 
                  StrokeThickness="3"/>
            <Path x:Name="OverlayCircle" 
                  Width="40" 
                  Height="40"
                  Data="M387,457 L400,457 L400,457 L405,462.082328 L400,467 L387,467 C385.895431,467 385,466.104569 385,465 L385,459 C385,457.895431 385.895431,457 387,457 Z"
                  Stretch="Fill" 
                  Visibility="Collapsed"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="StrokeThickness" TargetName="grip" Value="{StaticResource Windows11Light.StrokeThickness1}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushHovered}"/>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="StrokeThickness" TargetName="grip" Value="5"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushPressed}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushDisabled}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    <ControlTemplate x:Key="SliderThumbVerticalDefault" TargetType="{x:Type Thumb}">
        <Grid UseLayoutRounding="True" 
              HorizontalAlignment="Center"
              VerticalAlignment="Center">
            <Ellipse x:Name="gripBorder" 
                     Width="20" 
                     Height="20"
                     Stroke="{StaticResource ThumbBorderBrush}" 
                     StrokeThickness="1"
                     Fill="Transparent" 
                     Visibility="Visible" />
            <Ellipse x:Name="grip" 
                     Width="18" 
                     Height="18"
                     Fill="{StaticResource PrimaryBackground}" 
                     Stretch="Fill"
                     StrokeThickness="3"
                     Stroke="{StaticResource ContentBackground}"/>
            <Ellipse x:Name="OverlayCircle"
                     Height="40"
                     Width="40"
                     Visibility="Collapsed"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" TargetName="grip" Value="{StaticResource TouchMode.MinSize}" />
                <Setter Property="MinWidth" TargetName="grip" Value="{StaticResource TouchMode.MinSize}" />
            </Trigger>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource PrimaryBackgroundOpacity1}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="Transparent"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushHovered}"/>
                <Trigger.EnterActions>
                    <BeginStoryboard x:Name="MouseEnterAnimation">
                        <Storyboard>
                            <DoubleAnimation Duration="0:0:0:0.167" Storyboard.TargetProperty="StrokeThickness" Storyboard.TargetName="grip" To="2"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <RemoveStoryboard BeginStoryboardName="MouseEnterAnimation"/>
                </Trigger.ExitActions>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource PrimaryBackgroundOpacity2}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="Transparent"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushPressed}"/>
                <Trigger.EnterActions>
                    <BeginStoryboard x:Name="DragAnimation">
                        <Storyboard>
                            <DoubleAnimation Duration="0:0:0:0.167" Storyboard.TargetProperty="StrokeThickness" Storyboard.TargetName="grip" To="5"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <RemoveStoryboard BeginStoryboardName="DragAnimation"/>
                </Trigger.ExitActions>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource ContentBackground}"/>
                <Setter Property="Fill" TargetName="gripBorder" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Stroke" TargetName="gripBorder" Value="{StaticResource ThumbBorderBrushDisabled}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    <ControlTemplate x:Key="SliderVertical" TargetType="{x:Type Slider}">
        <Border x:Name="border" 
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}" 
                Background="{TemplateBinding Background}" SnapsToDevicePixels="True">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition MinWidth="{TemplateBinding MinWidth}" Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TickBar x:Name="TopTick" 
                         Grid.Column="0"
                         Fill="{StaticResource BorderAlt1}"
                         Placement="Left"
                         Visibility="Collapsed"
                         Width="4"
                         Margin="0,0,2,0"/>
                <TickBar x:Name="BottomTick" 
                         Grid.Column="2" 
                         Fill="{StaticResource BorderAlt1}"
                         Placement="Right"
                         Visibility="Collapsed" 
                         Width="4"
                         Margin="2,0,0,0"/>
                <Border x:Name="TrackBackground"
                        Grid.Column="1" 
                        BorderBrush="{StaticResource BorderAlt1}" 
                        BorderThickness="1" 
                        Background="{StaticResource BorderAlt1}" 
                        HorizontalAlignment="center" 
                        Margin="0,5,0,5"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                        Width="4">
                    <Canvas  Margin="-1,-6">
                        <Rectangle x:Name="PART_SelectionRange" 
                                   Fill="{StaticResource PrimaryBackground}" 
                                   Visibility="Hidden" 
                                   Width="4.0"/>
                    </Canvas>
                </Border>
                <Track x:Name="PART_Track" Grid.Column="1">
                    <Track.DecreaseRepeatButton>
                        <RepeatButton Command="{x:Static Slider.DecreaseLarge}" 
                                      Style="{StaticResource RepeatButtonTransparent}"
                                      Width="4"
                                      Height="{TemplateBinding Height}"
                                      Background="{StaticResource PrimaryBackground}"/>
                    </Track.DecreaseRepeatButton>
                    <Track.IncreaseRepeatButton>
                        <RepeatButton Command="{x:Static Slider.IncreaseLarge}" 
                                      Style="{StaticResource RepeatButtonTransparent}"/>
                    </Track.IncreaseRepeatButton>
                    <Track.Thumb>
                        <Thumb x:Name="Thumb" 
                               Focusable="False"                           
                               Width="20" 
                               Height="20"
                               Margin="0"
                               OverridesDefaultStyle="True" 
                               Template="{StaticResource SliderThumbVerticalDefault}" 
                               VerticalAlignment="Top"/>
                    </Track.Thumb>
                </Track>
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                <Setter Property="MinHeight" TargetName="Thumb" Value="{StaticResource TouchMode.MinHeight}"/>
                <Setter Property="MinWidth" TargetName="Thumb" Value="{StaticResource TouchMode.MinSize}"/>
            </Trigger>          
            <Trigger Property="TickPlacement" Value="TopLeft">
                <Setter Property="Visibility" TargetName="TopTick" Value="Visible"/>
                <Setter Property="Template" TargetName="Thumb" Value="{StaticResource SliderThumbVerticalLeft}"/>
            </Trigger>
            <Trigger Property="TickPlacement" Value="BottomRight">
                <Setter Property="Visibility" TargetName="BottomTick" Value="Visible"/>
                <Setter Property="Template" TargetName="Thumb" Value="{StaticResource SliderThumbVerticalRight}"/>
            </Trigger>
            <Trigger Property="TickPlacement" Value="Both">
                <Setter Property="Visibility" TargetName="TopTick" Value="Visible"/>
                <Setter Property="Visibility" TargetName="BottomTick" Value="Visible"/>
            </Trigger>
            <Trigger Property="IsSelectionRangeEnabled" Value="true">
                <Setter Property="Visibility" TargetName="PART_SelectionRange" Value="Visible"/>
            </Trigger>
            <Trigger Property="IsKeyboardFocused" Value="true">
                <Setter Property="Foreground" TargetName="Thumb" Value="Blue"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" TargetName="TrackBackground" Value="{StaticResource BorderAlt}"/>
                <Setter Property="BorderBrush" TargetName="TrackBackground" Value="{StaticResource BorderAlt}"/>
                <Setter Property="Fill" TargetName="PART_SelectionRange" Value="{StaticResource BorderAlt4}"/>
                <Setter Property="Fill" TargetName="TopTick" Value="{StaticResource BorderAlt}"/>
                <Setter Property="Fill" TargetName="BottomTick" Value="{StaticResource BorderAlt}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="WPFSliderStyle" TargetType="{x:Type Slider}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource SliderFocusVisual}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}"/>
        <Setter Property="Stylus.IsPressAndHoldEnabled" Value="false"/>
        <Setter Property="Template" Value="{StaticResource SliderHorizontal}"/>
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                <Setter Property="MinWidth" Value="{StaticResource TouchMode.MinWidth}"/>
            </Trigger>
            <Trigger Property="Orientation" Value="Vertical">
                <Setter Property="Template" Value="{StaticResource SliderVertical}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="Transparent"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource WPFSliderStyle}" TargetType="{x:Type Slider}"/>
</ResourceDictionary>
