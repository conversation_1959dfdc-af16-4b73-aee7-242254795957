<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
    xmlns:converters="clr-namespace:Syncfusion.Windows.Tools;assembly=Syncfusion.Tools.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:utilsOuter="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ScrollViewer.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="TaskBarItem.Header.Selected.Border" Color="#E0E0E0" />

    <converters:SpeedToDurationConverter x:Key="SpeedToDurationConverter" />
    <converters:DoubleToNegativeDoubleConverter x:Key="DoubleToNegativeDoubleConverter" />

    <Style x:Key="SyncfusionToggleButtonExtStyle" TargetType="{x:Type local:ToggleButtonExt}">
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <local:CheckableBorder
                        x:Name="checkBorder"
                        MinHeight="19"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{StaticResource Windows11Light.BorderThickness}">
                        <DockPanel x:Name="headerPanel"
                               Background="{TemplateBinding  Background}">
                            <Border
                                Name="arrowBorder"
                                Margin="1,1,3,1"
                                Width="{Binding Path=(local:TaskBar.ButtonSize), RelativeSource={RelativeSource AncestorType={x:Type local:TaskBarItem}}}"
                                Height="{Binding Path=(local:TaskBar.ButtonSize), RelativeSource={RelativeSource AncestorType={x:Type local:TaskBarItem}}}"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                ClipToBounds="True"
                                DockPanel.Dock="Right"
                                RenderTransformOrigin="0.5, 0.5">
                                <Border.RenderTransform>
                                    <RotateTransform Angle="180" />
                                </Border.RenderTransform>
                                <Path
                                    x:Name="arrowPath"
                                    Width=" 10"
                                    Height=" 6"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Fill="{StaticResource IconColor}"
                                    RenderTransformOrigin="0.5,0.5"
                                    Stretch="Fill">
                                    <Path.Data>
                                        <PathGeometry>M0.998047 1.5C0.998047 1.42969 1.01172 1.36523 1.03906 1.30664C1.06641 1.24414 1.10156 1.19141 1.14453 1.14844C1.19141 1.10156 1.24414 1.06445 1.30273 1.03711C1.36523 1.00977 1.43164 0.996094 1.50195 0.996094C1.57617 0.996094 1.64062 1.00977 1.69531 1.03711C1.75 1.06055 1.80273 1.09766 1.85352 1.14844L5 4.29492L8.14648 1.14844C8.24805 1.04688 8.36523 0.996094 8.49805 0.996094C8.56836 0.996094 8.63281 1.00977 8.69141 1.03711C8.75391 1.06445 8.80664 1.10156 8.84961 1.14844C8.89648 1.19141 8.93359 1.24414 8.96094 1.30664C8.98828 1.36523 9.00195 1.42969 9.00195 1.5C9.00195 1.63672 8.95312 1.75391 8.85547 1.85156L5.35156 5.35547C5.25391 5.45312 5.13672 5.50195 5 5.50195C4.86328 5.50195 4.74609 5.45312 4.64844 5.35547L1.14453 1.85156C1.04688 1.75391 0.998047 1.63672 0.998047 1.5Z</PathGeometry>
                                    </Path.Data>
                                    <Path.RenderTransform>
                                        <TransformGroup>
                                            <ScaleTransform />
                                            <SkewTransform />
                                            <RotateTransform Angle="360" />
                                            <TranslateTransform />
                                        </TransformGroup>
                                    </Path.RenderTransform>
                                </Path>
                            </Border>
                            <ContentPresenter
                                Name="ContentSite"
                                Margin="12,0,0,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Content="{TemplateBinding ContentControl.Content}"
                                ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}"
                                RecognizesAccessKey="True"
                                TextElement.FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                TextElement.FontWeight="{StaticResource Windows11Light.FontWeightNormal}"
                                TextElement.Foreground="{StaticResource ContentForeground}">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="TextBlock" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </DockPanel>
                        <local:CheckableBorder.Triggers>
                            <EventTrigger RoutedEvent="local:CheckableBorder.Checked">
                                <EventTrigger.Actions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="arrowBorder" Storyboard.TargetProperty="(Border.RenderTransform).(RotateTransform.Angle)">
                                                <DoubleAnimation.Duration>
                                                    <MultiBinding Converter="{StaticResource SpeedToDurationConverter}" ConverterParameter="Expanded">
                                                        <Binding RelativeSource="{RelativeSource TemplatedParent}" />
                                                        <Binding ElementName="ItemsPresenter" Path="DesiredSize.Height" />
                                                        <Binding ElementName="PanelPresenter" Path="SafeHeight" />
                                                    </MultiBinding>
                                                </DoubleAnimation.Duration>
                                            </DoubleAnimation>
                                            <DoubleAnimation
                                                Storyboard.TargetName="arrowPath"
                                                Storyboard.TargetProperty="(Path.RenderTransform).(RotateTransform.Angle)"
                                                To="90" />
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger.Actions>
                            </EventTrigger>
                            <EventTrigger RoutedEvent="local:CheckableBorder.Unchecked">
                                <EventTrigger.Actions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation
                                                Storyboard.TargetName="arrowBorder"
                                                Storyboard.TargetProperty="(Border.RenderTransform).(RotateTransform.Angle)"
                                                To="0">
                                                <DoubleAnimation.Duration>
                                                    <MultiBinding Converter="{StaticResource SpeedToDurationConverter}" ConverterParameter="Collapsed">
                                                        <Binding RelativeSource="{RelativeSource TemplatedParent}" />
                                                        <Binding ElementName="PanelPresenter" Path="SafeHeight" />
                                                    </MultiBinding>
                                                </DoubleAnimation.Duration>
                                            </DoubleAnimation>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger.Actions>
                            </EventTrigger>
                        </local:CheckableBorder.Triggers>
                    </local:CheckableBorder>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ContentSite" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                            <Setter TargetName="arrowPath" Property="Path.Fill" Value="{StaticResource IconColorSelected}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="checkBorder" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="headerPanel" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="checkBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="arrowBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="arrowBorder" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="arrowPath" Property="Fill" Value="{StaticResource IconColorHovered}" />
                            <Setter TargetName="ContentSite" Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="checkBorder" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="headerPanel" Property="Background" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter TargetName="checkBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="arrowBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="arrowBorder" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="arrowPath" Property="Fill" Value="{StaticResource IconColorSelected}" />
                            <Setter TargetName="ContentSite" Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsChecked" Value="True"/>
                                <Condition Property="IsMouseOver" Value="False"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="checkBorder" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="headerPanel" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="checkBorder" Property="BorderBrush" Value="{StaticResource TaskBarItem.Header.Selected.Border}" />
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="checkBorder" Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                            <Setter TargetName="headerPanel" Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                            <Setter TargetName="checkBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="arrowBorder" Property="Background" Value="Transparent" />
                            <Setter TargetName="arrowBorder" Property="BorderBrush" Value="Transparent" />
                            <Setter TargetName="arrowPath" Property="Fill" Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="ContentSite" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>                       
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="checkBorder" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionToggleButtonExtStyle}" TargetType="{x:Type local:ToggleButtonExt}" />

    <Style x:Key="SyncfusionExpanderExtStyle" TargetType="{x:Type local:ExpanderExt}">
        <Setter Property="Border.BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}" />
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Expander}">
                    <Border
                        Name="GeneralBorder"
                        Padding="{TemplateBinding Control.Padding}"
                        Background="{Binding Path=Background, RelativeSource={RelativeSource AncestorType={x:Type local:TaskBarItem}}}"
                        BorderBrush="{Binding Path=BorderBrush, RelativeSource={RelativeSource AncestorType={x:Type local:TaskBarItem}}}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        SnapsToDevicePixels="True">
                        <DockPanel>
                            <local:ToggleButtonExt
                                x:Name="HeaderSite1"
                                MinHeight="32"
                                Padding="{TemplateBinding Control.Padding}"
                                HorizontalContentAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                                VerticalContentAlignment="{TemplateBinding Control.VerticalContentAlignment}"
                                Content="{TemplateBinding HeaderedContentControl.Header}"
                                ContentTemplate="{TemplateBinding HeaderedContentControl.HeaderTemplate}"
                                DockPanel.Dock="Top"
                                FontFamily="{TemplateBinding FontFamily}"
                                FontSize="{TemplateBinding FontSize}"
                                FontStyle="{TemplateBinding FontStyle}"
                                FontWeight="{TemplateBinding FontWeight}"
                                Foreground="{TemplateBinding Foreground}"
								Background="{Binding Path=HeaderBackground, RelativeSource={RelativeSource AncestorType={x:Type local:TaskBarItem}}}"
                                Style="{Binding Path=(local:TaskBar.HeaderStyle), RelativeSource={RelativeSource AncestorType={x:Type local:TaskBarItem}}}"
                                TextElement.Foreground="{TemplateBinding Foreground}">
                                <ToggleButton.IsChecked>
                                    <Binding
                                        BindsDirectlyToSource="False"
                                        IsAsync="False"
                                        Mode="TwoWay"
                                        NotifyOnSourceUpdated="False"
                                        NotifyOnTargetUpdated="False"
                                        NotifyOnValidationError="False"
                                        Path="IsExpanded"
                                        RelativeSource="{RelativeSource TemplatedParent}" />
                                </ToggleButton.IsChecked>
                            </local:ToggleButtonExt>
                            <ContentPresenter
                                x:Name="ExpandSite"
                                Margin="1,0,0,0"
                                HorizontalAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding Control.VerticalContentAlignment}"
                                Content="{TemplateBinding ContentControl.Content}"
                                ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}"
                                DockPanel.Dock="Bottom"
                                Focusable="False" />
                        </DockPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsExpanded" Value="true">
                            <Setter TargetName="ExpandSite" Property="Visibility" Value="Visible" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionExpanderExtStyle}" TargetType="{x:Type local:ExpanderExt}" />

    <Style x:Key="SyncfusionItemsPresenterExtStyle" TargetType="{x:Type local:ItemsPresenterExt}">
        <Style.Triggers>
            <Trigger Property="Opacity" Value="0">
                <Setter Property="UIElement.Visibility" Value="Hidden" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionTaskBarItemStyle" TargetType="{x:Type local:TaskBarItem}">
        <Setter Property="Width" Value="{Binding Path=GroupWidth, RelativeSource={RelativeSource AncestorType={x:Type local:TaskBar}}}" />
        <Setter Property="Margin" Value="{Binding Path=GroupMargin, RelativeSource={RelativeSource AncestorType={x:Type local:TaskBar}}}" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="HeaderBackground" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:TaskBarItem}">
                    <StackPanel Name="StackName">
                        <local:ExpanderExt
                            x:Name="ExpandTemplate"
                            ClipToBounds="True"
                            ExpandDirection="Down"
                            Focusable="False"
                            Header="{TemplateBinding Header}"
                            HeaderTemplate="{Binding Path=ItemTemplate, RelativeSource={RelativeSource AncestorType={x:Type local:TaskBar}}}"
                            IsExpanded="{Binding Path=(local:TaskBar.IsOpened), RelativeSource={RelativeSource AncestorType={x:Type local:TaskBarItem}}}"
                            Style="{StaticResource SyncfusionExpanderExtStyle}">
                            <ScrollViewer
                                Background="{TemplateBinding Background}"
                                BorderBrush="Transparent"
                                Focusable="False"
                                VerticalScrollBarVisibility="Disabled">
                                <local:TaskBarStackPanel x:Name="PanelPresenter" ClipToBounds="True">
                                    <StackPanel Name="SimplePanel" Margin="12">
                                        <StackPanel.RenderTransform>
                                            <TranslateTransform />
                                        </StackPanel.RenderTransform>
                                        <local:ItemsPresenterExt
                                            x:Name="ItemsPresenter"
                                            Margin="{Binding Path=(local:TaskBar.GroupPadding), RelativeSource={RelativeSource AncestorType={x:Type local:TaskBarItem}}}"
                                            VerticalAlignment="Bottom"
                                            Style="{StaticResource SyncfusionItemsPresenterExtStyle}" />
                                    </StackPanel>
                                </local:TaskBarStackPanel>
                            </ScrollViewer>
                            <Expander.Triggers>
                                <EventTrigger RoutedEvent="Expander.Expanded">
                                    <EventTrigger.Actions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation
                                                    AccelerationRatio="0.8"
                                                    DecelerationRatio="0.2"
                                                    Storyboard.Target="{Binding ElementName=ItemsPresenter}"
                                                    Storyboard.TargetProperty="Opacity"
                                                    To="1">
                                                    <DoubleAnimation.Duration>
                                                        <Duration>0:0:0.3</Duration>
                                                    </DoubleAnimation.Duration>
                                                </DoubleAnimation>
                                                <DoubleAnimation
                                                    Storyboard.Target="{Binding ElementName=PanelPresenter}"
                                                    Storyboard.TargetName="PanelPresenter"
                                                    Storyboard.TargetProperty="(SafeHeight)">
                                                    <DoubleAnimation.Duration>
                                                        <Duration>0:0:0.3</Duration>
                                                    </DoubleAnimation.Duration>
                                                </DoubleAnimation>
                                                <DoubleAnimation
                                                    Storyboard.Target="{Binding ElementName=SimplePanel}"
                                                    Storyboard.TargetProperty="(StackPanel.RenderTransform).(TranslateTransform.Y)"
                                                    To="0">
                                                    <DoubleAnimation.Duration>
                                                        <Duration>0:0:0.3</Duration>
                                                    </DoubleAnimation.Duration>
                                                </DoubleAnimation>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger.Actions>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="Expander.Collapsed">
                                    <EventTrigger.Actions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation
                                                    AccelerationRatio="0.8"
                                                    DecelerationRatio="0.2"
                                                    Storyboard.Target="{Binding ElementName=ItemsPresenter}"
                                                    Storyboard.TargetProperty="Opacity"
                                                    To="0">
                                                    <DoubleAnimation.Duration>
                                                        <Duration>0:0:0.3</Duration>
                                                    </DoubleAnimation.Duration>
                                                </DoubleAnimation>
                                                <DoubleAnimation
                                                    Storyboard.Target="{Binding ElementName=PanelPresenter}"
                                                    Storyboard.TargetName="PanelPresenter"
                                                    Storyboard.TargetProperty="(SafeHeight)"
                                                    To="0">
                                                    <DoubleAnimation.Duration>
                                                        <Duration>0:0:0.3</Duration>
                                                    </DoubleAnimation.Duration>
                                                </DoubleAnimation>
                                                <DoubleAnimation Storyboard.Target="{Binding ElementName=SimplePanel}" Storyboard.TargetProperty="(StackPanel.RenderTransform).(TranslateTransform.Y)">
                                                    <DoubleAnimation.To>
                                                        <MultiBinding Converter="{StaticResource DoubleToNegativeDoubleConverter}">
                                                            <Binding ElementName="ItemsPresenter" Path="DesiredSize.Height" />
                                                            <Binding Path="(local:TaskBar.GroupPadding)" RelativeSource="{RelativeSource AncestorType={x:Type local:TaskBarItem}}" />
                                                        </MultiBinding>
                                                    </DoubleAnimation.To>
                                                    <DoubleAnimation.Duration>
                                                        <Duration>0:0:0.3</Duration>
                                                    </DoubleAnimation.Duration>
                                                </DoubleAnimation>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger.Actions>
                                </EventTrigger>
                            </Expander.Triggers>
                        </local:ExpanderExt>
                    </StackPanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTaskBarItemStyle}" TargetType="{x:Type local:TaskBarItem}" />

    <Style x:Key="SyncfusionTaskBarStyle" TargetType="{x:Type local:TaskBar}">
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="HeaderStyle" Value="{StaticResource SyncfusionToggleButtonExtStyle}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}" />
        <Setter Property="GroupMargin">
            <Setter.Value>
                <Thickness>0,0,0,0</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="local:TaskBar.ButtonSize" Value="20" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:TaskBar}">
                    <Border
                        x:Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="8"
                        TextBlock.Foreground="{TemplateBinding Foreground}">
                        <ScrollViewer
                            VerticalScrollBarVisibility="Auto">
                            <ItemsPresenter Name="TaskBarPresent" Margin="0,2,0,2" />
                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        
        <Style.Triggers>
            <Trigger Property="local:TaskBar.GroupOrientation" Value="Horizontal">
                <Setter Property="ItemsPanel">
                    <Setter.Value>
                        <ItemsPanelTemplate>
                            <VirtualizingStackPanel Orientation="Horizontal" />
                        </ItemsPanelTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>

        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTaskBarStyle}" TargetType="{x:Type local:TaskBar}" />

</ResourceDictionary>
