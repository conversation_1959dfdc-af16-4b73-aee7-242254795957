<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Microsoft_Windows_Aero="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:Microsoft_Windows_Luna="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Luna"
    xmlns:componentModel="clr-namespace:System.ComponentModel;assembly=PresentationFramework"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:tools_controls="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
    xmlns:tools_resources="clr-namespace:Syncfusion.Windows.Tools.Controls.Resources;assembly=Syncfusion.Tools.WPF"
    xmlns:windows_shared="clr-namespace:Syncfusion.Windows;assembly=Syncfusion.Shared.Wpf">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphTreeExpander.xaml" />

    </ResourceDictionary.MergedDictionaries>

    <DataTemplate x:Key="draggingContainerTemplate">
        <Grid x:Name="draggingStack">
            <Border
                Width="50"
                Height="50"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Background="{StaticResource ContentBackgroundSelected}"
                BorderBrush="{StaticResource ContentBackgroundSelected}"
                 />
            <Border
                Width="50"
                Height="50"
                Margin="5,5,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Background="{StaticResource ContentBackgroundSelected}"
                BorderBrush="{StaticResource ContentBackgroundSelected}"
                />
            <Border
                Width="50"
                Height="50"
                Margin="10,10,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Background="{StaticResource ContentBackgroundSelected}"
                BorderBrush="{StaticResource ContentBackgroundSelected}"
                >
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Background="Transparent"
                    Foreground="{StaticResource ContentForeground}"
                    TextAlignment="Center" />
            </Border>
        </Grid>
    </DataTemplate>

    <Style x:Key="TreeViewFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle SnapsToDevicePixels="true" Stroke="{StaticResource BorderAlt}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Default SortingArrowStyle for SortingArrows on column header  -->
    <Style x:Key="SyncfusionSortingArrowStyle" TargetType="{x:Type Path}">
        <Setter Property="Fill" Value="{StaticResource IconColor}" />
        <Setter Property="Data" Value="M0,0 L0,6 L6,0 z" />
        <Setter Property="StrokeThickness" Value="0" />
        <Setter Property="Stroke" Value="{StaticResource IconColor}" />
    </Style>
    <!--  Style for Expander  -->
    <Style x:Key="TreeViewAdvExpanderBaseStyle" TargetType="{x:Type Expander}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Expander}">
                    <ToggleButton
                        Name="Expander"
                        ClickMode="Press"
                        IsChecked="{Binding Path=IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                        Style="{StaticResource WPFGlyphTreeExpanderToggleStyle}">
                    </ToggleButton>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
	
	<!--  Style for DragMarker  -->
    <Style x:Key="SyncfusionTreeViewAdvDragMarkerAdornerrInternalControlStyle" TargetType="{x:Type shared:TemplatedAdornerInternalControl}">
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="SnapsToDevicePixels" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Column="0"
                            Height="2"
                            Fill="{StaticResource BorderAlt3}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!--  Style for TreeViewAdv  -->
    <Style x:Key="SyncfusionTreeViewAdvStyle" TargetType="{x:Type tools_controls:TreeViewAdv}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource TreeViewFocusVisual}" />
        <Setter Property="SortingDirectionArrowStyle" Value="{StaticResource SyncfusionSortingArrowStyle}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="SelectedBackground" Value="{StaticResource ContentBackgroundSelected}" />
        <Setter Property="SelectionUnfocussedBackcolor" Value="{StaticResource ContentBackgroundSelected}" />
        <Setter Property="SelectedForeground" Value="{StaticResource SelectedForeground}" />
        <Setter Property="SelectedBorderBrush" Value="Transparent" />
        <Setter Property="MouseOverBackground" Value="{StaticResource ContentBackgroundHovered}" />
        <Setter Property="MouseOverForeground" Value="{StaticResource HoveredForeground}" />
        <Setter Property="MouseOverBorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
        <Setter Property="LineBrush" Value="{StaticResource BorderAlt1}" />
        <Setter Property="FakeItemForeground" Value="Transparent" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1}" />
        <Setter Property="Padding" Value="1" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="AllowDrop" Value="True" />
        <Setter Property="LineStrokeDashArray" Value="2" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="{x:Static ScrollBarVisibility.Auto}" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="{x:Static ScrollBarVisibility.Auto}" />
        <Setter Property="ScrollViewer.Focusable" Value="False" />
        <Setter Property="Control.VerticalContentAlignment" Value="{x:Static VerticalAlignment.Center}" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="Continue" />
        <Setter Property="KeyboardNavigation.DirectionalNavigation" Value="Contained" />
        <Setter Property="ExpanderStyle" Value="{StaticResource TreeViewAdvExpanderBaseStyle}" />
        <Setter Property="DragIndicatorStyle" Value="{StaticResource SyncfusionTreeViewAdvDragMarkerAdornerrInternalControlStyle}" />
        <Setter Property="EditedItemTemplate">
            <Setter.Value>
                <DataTemplate>
                    <TextBox Name="TreeViewItemAdvText" KeyboardNavigation.IsTabStop="True" />
                </DataTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <tools_controls:TreeViewAdvVirtualizingPanel />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:TreeViewAdv}">
                    <Border
                        Name="PART_Border"
                        Margin="{TemplateBinding Border.BorderThickness}"
                        Padding="{TemplateBinding Border.Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding Border.BorderThickness}"
                        CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                        <ScrollViewer
                            Name="PART_ScrollViwer"
                            Padding="{TemplateBinding Control.Padding}"
                            Background="{TemplateBinding Panel.Background}"
                            CanContentScroll="True"
                            Focusable="{TemplateBinding ScrollViewer.Focusable}"
                            HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                            VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}">
                            <ScrollViewer.Style>
                                <Style TargetType="ScrollViewer">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="ScrollViewer">
                                                <Grid Background="{TemplateBinding Panel.Background}">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*" />
                                                        <ColumnDefinition Width="Auto" />
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto" />
                                                        <RowDefinition Height="*" />
                                                        <RowDefinition Height="Auto" />
                                                    </Grid.RowDefinitions>
                                                    <ScrollViewer HorizontalScrollBarVisibility="Hidden" VerticalScrollBarVisibility="Disabled">
                                                        <tools_controls:TreeViewHeaderRowPresenter
                                                            Grid.Row="0"
                                                            Grid.ColumnSpan="2"
                                                            Height="0"
                                                            Columns="{Binding Path=Columns, UpdateSourceTrigger=PropertyChanged, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                                                    </ScrollViewer>
                                                    <ScrollContentPresenter
                                                        Name="PART_ScrollContentPresenter"
                                                        Grid.Row="1"
                                                        Grid.Column="0"
                                                        Margin="{TemplateBinding Control.Padding}"
                                                        CanContentScroll="True"
                                                        Content="{TemplateBinding ContentControl.Content}"
                                                        ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}"
                                                        KeyboardNavigation.DirectionalNavigation="Local"
                                                        SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}" />
                                                    <ScrollBar
                                                        Name="PART_VerticalScrollBar"
                                                        Grid.Row="0"
                                                        Grid.RowSpan="2"
                                                        Grid.Column="1"
                                                        AutomationProperties.AutomationId="VerticalScrollBar"
                                                        Cursor="Arrow"
                                                        Maximum="{TemplateBinding ScrollableHeight}"
                                                        Minimum="0"
                                                        Orientation="Vertical"
                                                        ViewportSize="{TemplateBinding ViewportHeight}"
                                                        Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                                        Value="{TemplateBinding VerticalOffset}" />
                                                    <ScrollBar
                                                        Name="PART_HorizontalScrollBar"
                                                        Grid.Row="2"
                                                        Grid.Column="0"
                                                        AutomationProperties.AutomationId="HorizontalScrollBar"
                                                        Cursor="Arrow"
                                                        Maximum="{TemplateBinding ScrollableWidth}"
                                                        Minimum="0"
                                                        Orientation="Horizontal"
                                                        ViewportSize="{TemplateBinding ViewportWidth}"
                                                        Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                                        Value="{TemplateBinding HorizontalOffset}" />
                                                </Grid>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </ScrollViewer.Style>
                            <ItemsPresenter Name="PART_ItemPresenter" VerticalAlignment="Top" />
                        </ScrollViewer>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="PART_ScrollViwer" Property="Padding" Value="0" />
                            <Setter Property="ShowRootLines" Value="True" />
                            <Setter Property="AnimationType" Value="None" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="ItemsControl.HasItems" Value="False" />
                                <Condition Property="FrameworkElement.Width" Value="NaN" />
                            </MultiTrigger.Conditions>
                            <Setter Property="FrameworkElement.MinWidth" Value="120" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="ItemsControl.HasItems" Value="False" />
                                <Condition Property="FrameworkElement.Height" Value="NaN" />
                            </MultiTrigger.Conditions>
                            <Setter Property="FrameworkElement.MinHeight" Value="95" />
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
          
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="MultiColumnEnable" Value="True">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:TreeViewAdv}">
                            <Border
                                Name="PART_Border"
                                Margin="{TemplateBinding Border.BorderThickness}"
                                Padding="{TemplateBinding Border.Padding}"
                                Background="{TemplateBinding Border.Background}"
                                BorderBrush="{TemplateBinding Border.BorderBrush}"
                                BorderThickness="{TemplateBinding Border.BorderThickness}"
                                CornerRadius="{TemplateBinding Border.CornerRadius}">
                                <ScrollViewer
                                    Name="PART_ScrollViwer"
                                    Padding="{TemplateBinding Control.Padding}"
                                    Background="{TemplateBinding Panel.Background}"
                                    CanContentScroll="True"
                                    Focusable="False"
                                    HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                    VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}">
                                    <ScrollViewer.Style>
                                        <Style TargetType="ScrollViewer">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="ScrollViewer">
                                                        <Grid Background="{TemplateBinding Panel.Background}">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*" />
                                                                <ColumnDefinition Width="Auto" />
                                                            </Grid.ColumnDefinitions>
                                                            <Grid.RowDefinitions>
                                                                <RowDefinition Height="Auto" />
                                                                <RowDefinition Height="*" />
                                                                <RowDefinition Height="Auto" />
                                                            </Grid.RowDefinitions>
                                                            <ScrollViewer HorizontalScrollBarVisibility="Hidden" VerticalScrollBarVisibility="Disabled">
                                                                <tools_controls:TreeViewHeaderRowPresenter
                                                                    Name="PART_HeaderRowPresenter"
                                                                    Grid.Row="0"
                                                                    Grid.ColumnSpan="2"
                                                                    Columns="{Binding Path=Columns, UpdateSourceTrigger=PropertyChanged, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                                                            </ScrollViewer>
                                                            <ScrollContentPresenter
                                                                Name="PART_ScrollContentPresenter"
                                                                Grid.Row="1"
                                                                Grid.Column="0"
                                                                Margin="{TemplateBinding Control.Padding}"
                                                                CanContentScroll="True"
                                                                Content="{TemplateBinding ContentControl.Content}"
                                                                ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}"
                                                                KeyboardNavigation.DirectionalNavigation="Local"
                                                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}" />
                                                            <ScrollBar
                                                                Name="PART_VerticalScrollBar"
                                                                Grid.Row="0"
                                                                Grid.RowSpan="2"
                                                                Grid.Column="1"
                                                                AutomationProperties.AutomationId="VerticalScrollBar"
                                                                Cursor="Arrow"
                                                                Maximum="{TemplateBinding ScrollableHeight}"
                                                                Minimum="0"
                                                                ViewportSize="{TemplateBinding ViewportHeight}"
                                                                Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                                                Value="{TemplateBinding VerticalOffset}" />
                                                            <ScrollBar
                                                                Name="PART_HorizontalScrollBar"
                                                                Grid.Row="2"
                                                                Grid.Column="0"
                                                                AutomationProperties.AutomationId="HorizontalScrollBar"
                                                                Cursor="Arrow"
                                                                Maximum="{TemplateBinding ScrollableWidth}"
                                                                Minimum="0"
                                                                Orientation="Horizontal"
                                                                ViewportSize="{TemplateBinding ViewportWidth}"
                                                                Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                                                Value="{TemplateBinding HorizontalOffset}" />
                                                        </Grid>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </ScrollViewer.Style>
                                    <ItemsPresenter Name="PART_ItemPresenter" VerticalAlignment="Top" />
                                </ScrollViewer>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                                    <Setter TargetName="PART_ScrollViwer" Property="Padding" Value="0" />
                                    <Setter Property="ShowRootLines" Value="True" />
                                    <Setter Property="AnimationType" Value="None" />
                                </Trigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="ItemsControl.HasItems" Value="False" />
                                        <Condition Property="FrameworkElement.Width" Value="NaN" />
                                    </MultiTrigger.Conditions>
                                    <Setter Property="FrameworkElement.MinWidth" Value="120" />
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="ItemsControl.HasItems" Value="False" />
                                        <Condition Property="FrameworkElement.Height" Value="NaN" />
                                    </MultiTrigger.Conditions>
                                    <Setter Property="FrameworkElement.MinHeight" Value="95" />
                                </MultiTrigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTreeViewAdvStyle}" TargetType="{x:Type tools_controls:TreeViewAdv}" />

    <!--  Style for TreeViewColumnHeader  -->
    <Style x:Key="SyncfusionTreeViewColumnHeaderStyle" TargetType="{x:Type tools_controls:TreeViewColumnHeader}">
        <Setter Property="Focusable" Value="False" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Control.HorizontalContentAlignment" Value="Center" />
        <Setter Property="Control.VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:TreeViewColumnHeader}">
                    <Grid Background="{TemplateBinding Panel.Background}" SnapsToDevicePixels="True">
                        <Border
                            x:Name="border"
                            Margin="1,0,1,0"
                            Background="Transparent"
                            BorderThickness="{StaticResource Windows11Dark.BorderThickness}">
                            <Grid Margin="{TemplateBinding Padding}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ContentPresenter
                                        Name="HeaderContent"
                                        Margin="0,0,0,1"
                                        HorizontalAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding Control.VerticalContentAlignment}"
                                        Content="{TemplateBinding ContentControl.Content}"
                                        ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}"
                                        RecognizesAccessKey="True"
                                        SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                                        TextElement.FontFamily="{TemplateBinding FontFamily}"
                                        TextElement.FontWeight="{TemplateBinding FontWeight}"
                                        TextElement.Foreground="{TemplateBinding Foreground}">
                                    <ContentPresenter.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                                <Path
                                        x:Name="arrow"
                                        Grid.Column="1"
                                        Margin="0,0,7,6"
                                        VerticalAlignment="Center"
                                        Style="{Binding Path=SortingDirectionArrowStyle, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                        Visibility="Collapsed" />
                            </Grid>
                        </Border>

                        <Canvas>
                            <Thumb Name="PART_HeaderGripper">
                                <Thumb.Style>
                                    <Style TargetType="Thumb">
                                        <Setter Property="Canvas.Right" Value="-9" />
                                        <Setter Property="FrameworkElement.Width" Value="10" />
                                        <Setter Property="FrameworkElement.Height">
                                            <Setter.Value>
                                                <Binding Path="ActualHeight" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="Control.Padding" Value="0,3,0,4" />
                                        <Setter Property="Panel.Background" Value="Transparent" />
                                        <Setter Property="Control.Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Thumb">
                                                    <Border Padding="{TemplateBinding Control.Padding}" Background="{TemplateBinding Panel.Background}">
                                                        <DockPanel HorizontalAlignment="Center">
                                                            <Rectangle Width="1" Fill="{StaticResource BorderAlt}" />
                                                        </DockPanel>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Thumb.Style>
                            </Thumb>
                        </Canvas>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="Padding" Value="2,9,2,9" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsPressedHeader" Value="True" />
                                <Condition Property="tools_controls:TreeViewHeaderRowPresenter.AllowsColumnReorder" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="HeaderContent" Property="TextBlock.Foreground" Value="{StaticResource SelectedForeground}" />
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="HeaderContent" Property="TextBlock.Foreground" Value="{StaticResource SelectedForeground}" />
                            <Setter TargetName="arrow" Property="Fill" Value="{StaticResource IconColor}" />
                        </Trigger>
                        <Trigger Property="FrameworkElement.Height" Value="NaN">
                            <Setter Property="FrameworkElement.MinHeight" Value="{StaticResource Windows11Dark.MinHeight}" />
                        </Trigger>
                        <Trigger Property="UIElement.IsEnabled" Value="False">
                            <Setter Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="SortDirection" Value="Ascending" />
                                <Condition Property="sfskin:SfSkinManager.SizeMode" Value="Touch" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="arrow" Property="Visibility" Value="Visible" />
                            <Setter TargetName="arrow" Property="Data" Value="M 0,10 L 15,10 L 8,1 L 0,10" />
                        </MultiTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="SortDirection" Value="Ascending" />
                                <Condition Property="sfskin:SfSkinManager.SizeMode" Value="Default" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="arrow" Property="Visibility" Value="Visible" />
                            <Setter TargetName="arrow" Property="Data" Value="M 5,10 L 15,10 L 10,5 L 5,10" />
                        </MultiTrigger>

                        <Trigger Property="SortDirection" Value="None">
                            <Setter TargetName="arrow" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="SortDirection" Value="Descending" />
                                <Condition Property="sfskin:SfSkinManager.SizeMode" Value="Default" />
                            </MultiTrigger.Conditions>

                            <Setter TargetName="arrow" Property="Visibility" Value="Visible" />
                            <Setter TargetName="arrow" Property="Data" Value="M 5,5 L 10,10 L 15,5 L 5,5" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="SortDirection" Value="Descending" />
                                <Condition Property="sfskin:SfSkinManager.SizeMode" Value="Touch" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="arrow" Property="Data" Value="M 8,8 L 16,16 L 24,8 L 8,8" />
                            <Setter TargetName="arrow" Property="Visibility" Value="Visible" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTreeViewColumnHeaderStyle}" TargetType="{x:Type tools_controls:TreeViewColumnHeader}" />
    <!--  Style for TreeViewItemAdv  -->

    <Style x:Key="SyncfusionTreeViewItemAdvStyle" TargetType="{x:Type tools_controls:TreeViewItemAdv}">
        <Setter Property="MinHeight" Value="{StaticResource Windows11Dark.MinHeight}" />
        <Setter Property="ExpandAnimation">
            <Setter.Value>
                <DoubleAnimation Duration="0:0:0.5" />
            </Setter.Value>
        </Setter>
        <Setter Property="FadeAnimation">
            <Setter.Value>
                <DoubleAnimation Duration="0:0:0.5" />
            </Setter.Value>
        </Setter>
        <Setter Property="Panel.Background" Value="Transparent" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Control.Padding" Value="2,0,2,0" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness}" />
        <Setter Property="KeyboardNavigation.DirectionalNavigation" Value="Local" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="KeyboardNavigation.IsTabStop" Value="False" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" /> 
        <Setter Property="FrameworkElement.FocusVisualStyle">
            <Setter.Value>
                <Style TargetType="IFrameworkInputElement">
                    <Setter Property="Control.Template">
                        <Setter.Value>
                            <ControlTemplate>
                                <Border>
                                    <Rectangle
                                        Opacity="0"
                                        Stroke="{StaticResource BorderAlt}"
                                        StrokeDashArray="{StaticResource Windows11Dark.StrokeDashArray}"
                                        StrokeThickness="5" />
                                </Border>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <tools_controls:TreeViewAdvVirtualizingPanel Name="PART_ItemsPanel" IsShowLine="{Binding Path=ShowRootLines, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="tools_controls:TreeViewItemAdv">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Border
                            Name="Part_TopDragLine"
                            Grid.Row="0"
                            Height="2"
                            Background="Transparent"
                            Visibility="Collapsed" />
                        <Grid
                            Grid.Row="1"
                            Background="Transparent"
                            Focusable="False"
                            TextElement.Foreground="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <Grid
                                Name="VertLineGrid"
                                Grid.Row="0"
                                Grid.RowSpan="2">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>

                                <tools_controls:TreeRootLine
                                    Name="PART_VerticalLinePartOne"
                                    Grid.Row="0"
                                    Width="{Binding Path=LineStrokeThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                    Panel.ZIndex="0"
                                    Focusable="False"
                                    IsVerticalLine="True"
                                    LineBrush="{Binding Path=LineBrush, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                    LineStrokeThickness="{Binding Path=LineStrokeThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                    SnapsToDevicePixels="True"
                                    Visibility="Visible" />
                                <tools_controls:TreeRootLine
                                    Name="PART_VerticalLinePartTwo"
                                    Grid.Row="1"
                                    Width="{Binding Path=LineStrokeThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                    Panel.ZIndex="0"
                                    Focusable="False"
                                    IsVerticalLine="True"
                                    LineBrush="{Binding Path=LineBrush, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                    LineStrokeThickness="{Binding Path=LineStrokeThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                    SnapsToDevicePixels="True"
                                    Visibility="Visible" />
                            </Grid>
                            <Grid Name="HorLineGrid">
                                <tools_controls:TreeRootLine
                                    Name="PART_HorizontalLine"
                                    Grid.Row="0"
                                    Width="13"
                                    Height="{Binding Path=LineStrokeThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                    Margin="9,0,-3,0"
                                    HorizontalAlignment="Right"
                                    Panel.ZIndex="0"
                                    Focusable="False"
                                    IsVerticalLine="False"
                                    LineBrush="{Binding Path=LineBrush, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                    LineStrokeThickness="{Binding Path=LineStrokeThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                    SnapsToDevicePixels="True" />
                            </Grid>
                            <tools_controls:BottomLine
                                Name="BottomBorder"
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="0,0,0,1"
                                Fill="Transparent"
                                SnapsToDevicePixels="True"
                                TargetItem="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewItemAdv}}}" />

                            <tools_controls:SelectRectangle
                                Name="PART_SelectRectangle"
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="0,0,0,1"
                                Fill="Transparent"
                                SnapsToDevicePixels="True"
                                TargetItem="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewItemAdv}}}" />
                            <Expander
                                Name="PART_Expander"
                                Grid.Row="0"
                                Grid.Column="0"
                                Height="13"
                                Margin="1,0,0,0"
                                HorizontalAlignment="Center"
                                Panel.ZIndex="100"
                                Background="{Binding Path=Background, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                Focusable="False"
                                IsExpanded="{Binding Path=IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                                Style="{TemplateBinding tools_controls:TreeViewAdv.ExpanderStyle}" />

                            <Ellipse
                                x:Name="path"
                                Grid.Row="0"
                                Grid.Column="0"
                                Width="12"
                                Height="12"
                                Margin="1,0,0,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Panel.ZIndex="100"
                                Fill="White"
                                RenderTransformOrigin="0.5,0.5"
                                Stretch="Fill"
                                StrokeThickness="2"
                                Visibility="Collapsed">
                                <Ellipse.RenderTransform>
                                    <TransformGroup>
                                        <ScaleTransform />
                                        <SkewTransform />
                                        <RotateTransform Angle="-180" />
                                        <TranslateTransform />
                                    </TransformGroup>
                                </Ellipse.RenderTransform>
                                <Ellipse.Stroke>
                                    <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                        <GradientStop Offset="0.378" Color="#FFEBF0F4" />
                                        <GradientStop Offset="1" Color="#FF119EDA" />
                                    </LinearGradientBrush>
                                </Ellipse.Stroke>
                            </Ellipse>

                            <Grid
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="3,0,0,0"
                                Panel.ZIndex="20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>

                                <StackPanel Name="PART_CompleteHeader" Orientation="Horizontal">
                                    <StackPanel Name="PART_ImagePanel" Orientation="Horizontal">
                                        <Image
                                            Name="ExpanderImage"
                                            Width="{TemplateBinding ImageWidth}"
                                            Height="{TemplateBinding ImageHeight}"
                                            Focusable="False"
                                            Source="{TemplateBinding ExpandedImageSource}"
                                            Stretch="{TemplateBinding ImageStretch}"
                                            Visibility="Collapsed" />

                                        <Image
                                            Name="CollapsedImage"
                                            Width="{TemplateBinding ImageWidth}"
                                            Height="{TemplateBinding ImageHeight}"
                                            Focusable="False"
                                            Source="{TemplateBinding CollapsedImageSource}"
                                            Stretch="{TemplateBinding ImageStretch}" />
                                        <Image
                                            Name="LeftImage"
                                            Width="{TemplateBinding ImageWidth}"
                                            Height="{TemplateBinding ImageHeight}"
                                            Margin="2,0,0,0"
                                            Focusable="False"
                                            Source="{TemplateBinding LeftImageSource}"
                                            Stretch="{TemplateBinding ImageStretch}" />
                                    </StackPanel>

                                    <StackPanel
                                        Name="PART_ItemContent"
                                        VerticalAlignment="Center"
                                        Focusable="True"
                                        KeyboardNavigation.TabNavigation="Cycle">
                                        <Border
                                            Name="border"
                                            MinHeight="19"
                                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                            Margin="0,1,0,1"
                                            Padding="{TemplateBinding Padding}"
                                            Background="{TemplateBinding Panel.Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            SnapsToDevicePixels="True"
                                            TextElement.Foreground="{TemplateBinding Foreground}"
                                            ToolTip="{TemplateBinding tools_controls:TreeViewItemAdv.Header}"
                                            ToolTipService.HorizontalOffset="4"
                                            ToolTipService.IsEnabled="False"
                                            ToolTipService.Placement="Center"
                                            ToolTipService.VerticalOffset="2" >
                                            <Grid>
                                                <ContentPresenter
                                                    Name="PART_Header"
                                                    Margin=" 5 0 4 0"
                                                    HorizontalAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                                                    VerticalAlignment="Center"
                                                    Content="{TemplateBinding tools_controls:TreeViewItemAdv.Header}"
                                                    ContentSource="Header"
                                                    ContentTemplate="{TemplateBinding tools_controls:TreeViewItemAdv.HeaderTemplate}"
                                                    ContentTemplateSelector="{TemplateBinding tools_controls:TreeViewItemAdv.HeaderTemplateSelector}"
                                                    Visibility="Visible">
                                                </ContentPresenter>
                                                <ContentPresenter
                                                    x:Name="LoadingContentPresenter"
                                                    HorizontalAlignment="Left"
                                                    Content="{TemplateBinding LoadingHeader}"
                                                    ContentTemplate="{TemplateBinding LoadingHeaderTemplate}"
                                                    Visibility="Collapsed" />
                                                <Rectangle x:Name="PART_SelectionIndicator" Visibility="Collapsed"
                                            Width="2" Height="10" Margin="-1,0,0,0"
                                            RadiusX="2" RadiusY="2"
                                            Fill="{StaticResource PrimaryBackground}"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Center"/>
                                            </Grid>
                                        </Border>

                                        <ContentPresenter
                                            Name="PART_EditHeader"
                                            HorizontalAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                                            VerticalAlignment="Center"
                                            Content="{Binding Path=Header, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewItemAdv}}}"
                                            ContentSource="Header"
                                            ContentTemplate="{Binding Path=EditedItemTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                            ContentTemplateSelector="{Binding Path=EditedItemTemplateSelector, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                            Visibility="Collapsed" />
                                    </StackPanel>

                                    <Image
                                        Name="RightImage"
                                        Width="{TemplateBinding ImageWidth}"
                                        Height="{TemplateBinding ImageHeight}"
                                        Margin="3,0,3,0"
                                        Focusable="False"
                                        Source="{TemplateBinding RightImageSource}"
                                        Stretch="{TemplateBinding ImageStretch}" />
                                </StackPanel>
                            </Grid>

                            <Grid Grid.Row="1" Grid.Column="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <ItemsPresenter Name="PART_ItemsHost" Visibility="Collapsed" />
                            </Grid>
                        </Grid>
                        <Border
                            Name="Part_BottomDragLine"
                            Grid.Row="2"
                            Height="2"
                            Background="Transparent"
                            Visibility="Collapsed" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="FontSize" Value="18" />
                            <Setter Property="Padding" Value="5,5,12,5" />
                            <Setter TargetName="PART_HorizontalLine" Property="Width" Value="15"/>
                            <Setter TargetName="PART_Expander" Property="Margin" Value="1,0,0,0" />
                        </Trigger>
                        <Trigger Property="tools_controls:TreeViewItemAdv.IsShowToolTip" Value="True">
                            <Setter TargetName="border" Property="ToolTipService.IsEnabled" Value="True" />
                        </Trigger>
                        <Trigger Property="tools_controls:TreeViewItemAdv.LeftImageSource" Value="{x:Null}">
                            <Setter TargetName="LeftImage" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="tools_controls:TreeViewItemAdv.RightImageSource" Value="{x:Null}">
                            <Setter TargetName="RightImage" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="tools_controls:TreeViewItemAdv.IsExpanded" Value="True">
                            <Setter TargetName="ExpanderImage" Property="Source" Value="{Binding Path=ExpandedImageSource, RelativeSource={RelativeSource TemplatedParent}}" />
                            <Setter TargetName="CollapsedImage" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="ExpanderImage" Property="Visibility" Value="Visible" />
                            <Setter TargetName="PART_ItemsHost" Property="Visibility" Value="Visible" />
                        </Trigger>
                        <Trigger Property="tools_controls:TreeViewItemAdv.IsExpanded" Value="False">
                            <Setter TargetName="CollapsedImage" Property="Source" Value="{Binding Path=CollapsedImageSource, RelativeSource={RelativeSource TemplatedParent}}" />
                            <Setter TargetName="ExpanderImage" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="CollapsedImage" Property="Visibility" Value="Visible" />
                            <Setter TargetName="PART_ItemsHost" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="tools_controls:TreeViewItemAdv.IsExpanded" Value="True" />
                                <Condition Property="tools_controls:TreeViewItemAdv.ExpandedImageSource" Value="{x:Null}" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="ExpanderImage" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="LeftImage" Property="Margin" Value="0,0,2,0" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="tools_controls:TreeViewItemAdv.IsExpanded" Value="False" />
                                <Condition Property="tools_controls:TreeViewItemAdv.CollapsedImageSource" Value="{x:Null}" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="CollapsedImage" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="LeftImage" Property="Margin" Value="0,0,2,0" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="tools_controls:TreeViewItemAdv.IsExpanded" Value="True" />
                                <Condition Property="tools_controls:TreeViewItemAdv.ExpandedImageSource" Value="{x:Null}" />
                                <Condition Property="tools_controls:TreeViewItemAdv.LeftImageSource" Value="{x:Null}" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_ImagePanel" Property="Visibility" Value="Collapsed" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="tools_controls:TreeViewItemAdv.IsExpanded" Value="False" />
                                <Condition Property="tools_controls:TreeViewItemAdv.CollapsedImageSource" Value="{x:Null}" />
                                <Condition Property="tools_controls:TreeViewItemAdv.LeftImageSource" Value="{x:Null}" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_ImagePanel" Property="Visibility" Value="Collapsed" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="tools_controls:TreeViewItemAdv.IsEditable" Value="True" />
                                <Condition Property="tools_controls:TreeViewItemAdv.IsInEditMode" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Header" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="border" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="PART_EditHeader" Property="Visibility" Value="Visible" />
                        </MultiTrigger>
                        <Trigger Property="tools_controls:TreeViewAdv.ShowRootLines" Value="False">
                            <Setter TargetName="HorLineGrid" Property="Visibility" Value="Hidden" />
                            <Setter TargetName="VertLineGrid" Property="Visibility" Value="Hidden" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="tools_controls:TreeViewItemAdv.HasItems" Value="False" />
                                <Condition Property="tools_controls:TreeViewItemAdv.IsLoadOnDemand" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Expander" Property="Visibility" Value="Hidden" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="HeaderedContentControl.HasHeader" Value="False" />
                                <Condition Property="FrameworkElement.Width" Value="NaN" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Header" Property="FrameworkElement.MinWidth" Value="75" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="HeaderedContentControl.HasHeader" Value="False" />
                                <Condition Property="FrameworkElement.Height" Value="NaN" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Header" Property="FrameworkElement.MinHeight" Value="19" />
                        </MultiTrigger>

                        <Trigger SourceName="PART_CompleteHeader" Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Panel.Background" Value="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                            <Setter TargetName="border" Property="TextElement.Foreground" Value="{Binding Path=MouseOverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{Binding Path=MouseOverBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                        </Trigger>

                        <Trigger Property="tools_controls:TreeViewItemAdv.IsSelected" Value="True">
                            <Setter TargetName="border" Property="Panel.Background" Value="{Binding Path=SelectedBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                            <Setter TargetName="border" Property="TextElement.Foreground" Value="{Binding Path=SelectedForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{Binding Path=SelectedBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                        </Trigger>

                        <Trigger Property="tools_controls:TreeViewItemAdv.IsSelectedFalse" Value="True">
                            <Setter TargetName="border" Property="Panel.Background" Value="{Binding Path=SelectionUnfocussedBackcolor, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                            <Setter TargetName="border" Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                        </Trigger>

                        <Trigger Property="tools_controls:TreeViewItemAdv.IsDragOver" Value="True">
                            <Setter TargetName="border" Property="Panel.Background" Value="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                            <Setter TargetName="border" Property="TextElement.Foreground" Value="{Binding Path=MouseOverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                            <Setter TargetName="PART_SelectRectangle" Property="Fill" Value="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="tools_controls:TreeViewItemAdv.IsSelected" Value="True" />
                                <Condition Property="tools_controls:TreeViewItemAdv.IsSelectionActive" Value="False" />
                                <Condition Property="Selector.IsSelectionActive" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="border" Property="Panel.Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="border" Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
                        </MultiTrigger>
                        <Trigger Property="tools_controls:TreeViewItemAdv.IsDraging" Value="True">
                            <Setter TargetName="border" Property="Panel.Background" Value="{StaticResource ContentBackgroundSelected}"/>
                            <Setter TargetName="border" Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
                        </Trigger>
                       
                        <Trigger Property="UIElement.IsEnabled" Value="False">
                            <Setter TargetName="border" Property="TextElement.Background" Value="Transparent" />
                            <Setter TargetName="border" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="RightImage" Property="Opacity" Value="0.7" />
                            <Setter TargetName="LeftImage" Property="Opacity" Value="0.7" />
                        </Trigger>
                        <Trigger Property="tools_controls:TreeViewItemAdv.IsLoadOnDemand" Value="True">
                            <Setter TargetName="PART_Expander" Property="Visibility" Value="Visible" />
                        </Trigger>

                        <Trigger Property="tools_controls:TreeViewItemAdv.IsLoading" Value="True">
                            <Setter TargetName="PART_Expander" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="LoadingContentPresenter" Property="Visibility" Value="Visible" />
                            <Setter TargetName="PART_Header" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="path" Property="Visibility" Value="Visible" />
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="beginLoadOnDemandStoryBoard">
                                    <BeginStoryboard.Storyboard>
                                        <Storyboard RepeatBehavior="Forever">
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="path" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[2].(RotateTransform.Angle)">
                                                <LinearDoubleKeyFrame KeyTime="0" Value="0" />
                                                <LinearDoubleKeyFrame KeyTime="0:0:1" Value="360" />
                                            </DoubleAnimationUsingKeyFrames>
                                        </Storyboard>
                                    </BeginStoryboard.Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="tools_controls:TreeViewItemAdv.IsLoading" Value="False">
                            <Trigger.ExitActions>
                                <StopStoryboard BeginStoryboardName="beginLoadOnDemandStoryBoard" />
                            </Trigger.ExitActions>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>

                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="MultiColumnEnable" Value="True">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type tools_controls:TreeViewItemAdv}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Border
                                    Name="Part_TopDragLine"
                                    Grid.Row="0"
                                    Height="2"
                                    Background="Transparent"
                                    Visibility="Collapsed" />
                                <Grid
                                    Grid.Row="1"
                                    Background="Transparent"
                                    Focusable="False"
                                    TextElement.Foreground="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>

                                    <Grid
                                        Name="VertLineGrid"
                                        Grid.Row="0"
                                        Grid.RowSpan="2">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="*" />
                                        </Grid.RowDefinitions>

                                        <tools_controls:TreeRootLine
                                            Name="PART_VerticalLinePartOne"
                                            Grid.Row="0"
                                            Width="{Binding Path=LineStrokeThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                            Panel.ZIndex="0"
                                            Focusable="False"
                                            IsVerticalLine="True"
                                            LineBrush="{Binding Path=LineBrush, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                            LineStrokeThickness="{Binding Path=LineStrokeThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                            SnapsToDevicePixels="True"
                                            Visibility="Visible" />
                                        <tools_controls:TreeRootLine
                                            Name="PART_VerticalLinePartTwo"
                                            Grid.Row="1"
                                            Width="{Binding Path=LineStrokeThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                            Panel.ZIndex="0"
                                            Focusable="False"
                                            IsVerticalLine="True"
                                            LineBrush="{Binding Path=LineBrush, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                            LineStrokeThickness="{Binding Path=LineStrokeThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                            SnapsToDevicePixels="True"
                                            Visibility="Visible" />
                                    </Grid>
                                    <Grid Name="HorLineGrid">

                                        <tools_controls:TreeRootLine
                                            Name="PART_HorizontalLine"
                                            Grid.Row="0"
                                            Width="13"
                                            Height="{Binding Path=LineStrokeThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                            Margin="9,0,-3,0"
                                            HorizontalAlignment="Right"
                                            Panel.ZIndex="0"
                                            Focusable="False"
                                            IsVerticalLine="False"
                                            LineBrush="{Binding Path=LineBrush, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                            LineStrokeThickness="{Binding Path=LineStrokeThickness, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                            SnapsToDevicePixels="True"
                                            Visibility="Visible" />
                                    </Grid>
                                    <tools_controls:BottomLine
                                        Name="BottomBorder"
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        Margin="0,0,0,1"
                                        Fill="Transparent"
                                        SnapsToDevicePixels="True"
                                        TargetItem="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewItemAdv}}}" />

                                    <tools_controls:SelectRectangle
                                        Name="PART_SelectRectangle"
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        Margin="0,0,0,1"
                                        Fill="Transparent"
                                        SnapsToDevicePixels="True"
                                        TargetItem="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewItemAdv}}}" />
                                    <Expander
                                        Name="PART_Expander"
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        HorizontalAlignment="Center"
                                        Panel.ZIndex="100"
                                        Background="{Binding Path=Background, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                        Focusable="False"
                                        IsExpanded="{Binding Path=IsExpanded, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewItemAdv}}}"
                                        Style="{TemplateBinding tools_controls:TreeViewAdv.ExpanderStyle}" />

                                    <Grid
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        Panel.ZIndex="10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Name="PART_CompleteHeader" Orientation="Horizontal">
                                            <StackPanel
                                                Name="PART_ImagePanel"
                                                Margin="5,0,1,0"
                                                Orientation="Horizontal">
                                                <Image
                                                    Name="ExpanderImage"
                                                    Width="{TemplateBinding ImageWidth}"
                                                    Height="{TemplateBinding ImageHeight}"
                                                    Focusable="False"
                                                    Source="{TemplateBinding ExpandedImageSource}"
                                                    Stretch="{TemplateBinding ImageStretch}"
                                                    Visibility="Collapsed" />

                                                <Image
                                                    Name="CollapsedImage"
                                                    Width="{TemplateBinding ImageWidth}"
                                                    Height="{TemplateBinding ImageHeight}"
                                                    Focusable="False"
                                                    Source="{TemplateBinding CollapsedImageSource}"
                                                    Stretch="{TemplateBinding ImageStretch}" />

                                                <Image
                                                    Name="LeftImage"
                                                    Width="{TemplateBinding ImageWidth}"
                                                    Height="{TemplateBinding ImageHeight}"
                                                    Margin="2,0,0,0"
                                                    Focusable="False"
                                                    Source="{TemplateBinding LeftImageSource}"
                                                    Stretch="{TemplateBinding ImageStretch}" />
                                            </StackPanel>

                                            <Border
                                                Name="border"
                                                MinHeight="19"
                                                Padding="{Binding Path=Padding, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewItemAdv}}}"
                                                Background="{Binding Path=Background, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewItemAdv}}}"
                                                BorderBrush="{Binding Path=BorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewItemAdv}}}"
                                                ClipToBounds="True"
                                                SnapsToDevicePixels="True"
                                                TextElement.Foreground="{TemplateBinding Foreground}"
                                                ToolTip="{TemplateBinding tools_controls:TreeViewItemAdv.Header}"
                                                ToolTipService.HorizontalOffset="4"
                                                ToolTipService.IsEnabled="False"
                                                ToolTipService.Placement="Center"
                                                ToolTipService.VerticalOffset="2">
                                                <StackPanel
                                                    Margin="1.5,0,-5,0"
                                                    VerticalAlignment="Center"
                                                    Focusable="True"
                                                    KeyboardNavigation.TabNavigation="Cycle">
                                                    <ContentControl
                                                        Name="PART_Header"
                                                        ContentTemplate="{Binding Path=HeaderTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewItemAdv}}}"
                                                        ContentTemplateSelector="{Binding Path=HeaderTemplateSelector, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewItemAdv}}}">
                                                        <ContentControl.Content>
                                                            <tools_controls:TreeViewRowPresenter
                                                                Name="PART_RowPresenter"
                                                                Margin="-1,1,1,1"
                                                                Columns="{Binding Path=WrappedColumns, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                                                Content="{TemplateBinding tools_controls:TreeViewItemAdv.Header}" />
                                                        </ContentControl.Content>
                                                    </ContentControl>

                                                    <ContentPresenter
                                                        Name="PART_EditHeader"
                                                        HorizontalAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                                                        VerticalAlignment="Center"
                                                        Content="{Binding Path=Header, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewItemAdv}}}"
                                                        ContentSource="Header"
                                                        ContentTemplate="{Binding Path=EditedItemTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                                        ContentTemplateSelector="{Binding Path=EditedItemTemplateSelector, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}"
                                                        Visibility="Collapsed" />
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </Grid>

                                    <Grid Grid.Row="1" Grid.Column="1">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <ItemsPresenter Name="PART_ItemsHost" Visibility="Collapsed" />
                                    </Grid>
                                </Grid>
                                <Border
                                    Name="Part_BottomDragLine"
                                    Grid.Row="2"
                                    Height="2"
                                    Background="Transparent"
                                    Visibility="Collapsed" />
                            </Grid>
                            <ControlTemplate.Triggers>

                                <Trigger Property="tools_controls:TreeViewItemAdv.LeftImageSource" Value="{x:Null}">
                                    <Setter TargetName="LeftImage" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <Trigger Property="tools_controls:TreeViewItemAdv.IsExpanded" Value="True">
                                    <Setter TargetName="ExpanderImage" Property="Source" Value="{Binding Path=ExpandedImageSource, RelativeSource={RelativeSource TemplatedParent}}" />
                                    <Setter TargetName="CollapsedImage" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="ExpanderImage" Property="Visibility" Value="Visible" />
                                    <Setter TargetName="PART_ItemsHost" Property="Visibility" Value="Visible" />
                                </Trigger>
                                <Trigger Property="tools_controls:TreeViewItemAdv.IsExpanded" Value="False">
                                    <Setter TargetName="CollapsedImage" Property="Source" Value="{Binding Path=CollapsedImageSource, RelativeSource={RelativeSource TemplatedParent}}" />
                                    <Setter TargetName="ExpanderImage" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="CollapsedImage" Property="Visibility" Value="Visible" />
                                    <Setter TargetName="PART_ItemsHost" Property="Visibility" Value="Collapsed" />
                                </Trigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="tools_controls:TreeViewItemAdv.IsExpanded" Value="True" />
                                        <Condition Property="tools_controls:TreeViewItemAdv.ExpandedImageSource" Value="{x:Null}" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="ExpanderImage" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="LeftImage" Property="Margin" Value="0,0,2,0" />
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="tools_controls:TreeViewItemAdv.IsExpanded" Value="False" />
                                        <Condition Property="tools_controls:TreeViewItemAdv.CollapsedImageSource" Value="{x:Null}" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="CollapsedImage" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="LeftImage" Property="Margin" Value="0,0,2,0" />
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="tools_controls:TreeViewItemAdv.IsExpanded" Value="True" />
                                        <Condition Property="tools_controls:TreeViewItemAdv.ExpandedImageSource" Value="{x:Null}" />
                                        <Condition Property="tools_controls:TreeViewItemAdv.LeftImageSource" Value="{x:Null}" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="PART_ImagePanel" Property="Visibility" Value="Collapsed" />
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="tools_controls:TreeViewItemAdv.IsExpanded" Value="False" />
                                        <Condition Property="tools_controls:TreeViewItemAdv.CollapsedImageSource" Value="{x:Null}" />
                                        <Condition Property="tools_controls:TreeViewItemAdv.LeftImageSource" Value="{x:Null}" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="PART_ImagePanel" Property="Visibility" Value="Collapsed" />
                                </MultiTrigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="tools_controls:TreeViewItemAdv.IsEditable" Value="True" />
                                        <Condition Property="tools_controls:TreeViewItemAdv.IsInEditMode" Value="True" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="PART_Header" Property="Visibility" Value="Collapsed" />
                                    <Setter TargetName="PART_EditHeader" Property="Visibility" Value="Visible" />
                                </MultiTrigger>
                                <Trigger Property="tools_controls:TreeViewItemAdv.IsShowToolTip" Value="True">
                                    <Setter TargetName="border" Property="ToolTipService.IsEnabled" Value="True" />
                                </Trigger>
                                <Trigger Property="tools_controls:TreeViewAdv.ShowRootLines" Value="False">
                                    <Setter TargetName="HorLineGrid" Property="Visibility" Value="Hidden" />
                                    <Setter TargetName="VertLineGrid" Property="Visibility" Value="Hidden" />
                                </Trigger>
                                <Trigger Property="tools_controls:TreeViewItemAdv.HasItems" Value="False">
                                    <Setter TargetName="PART_Expander" Property="Visibility" Value="Hidden" />
                                </Trigger>

                                <Trigger SourceName="PART_Header" Property="IsMouseOver" Value="True">
                                    <Setter TargetName="border" Property="Panel.Background" Value="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                                    <Setter TargetName="border" Property="TextElement.Foreground" Value="{Binding Path=MouseOverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                                    <Setter TargetName="border" Property="BorderBrush" Value="{Binding Path=MouseOverBorderBrush, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                                </Trigger>

                                <Trigger Property="tools_controls:TreeViewItemAdv.IsSelected" Value="True">
                                    <Setter TargetName="border" Property="Panel.Background" Value="{Binding Path=SelectedBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                                    <Setter TargetName="border" Property="TextElement.Foreground" Value="{Binding Path=SelectedForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                                </Trigger>

                                <Trigger Property="tools_controls:TreeViewItemAdv.IsSelectedFalse" Value="True">
                                    <Setter TargetName="border" Property="Panel.Background" Value="{Binding Path=SelectionUnfocussedBackcolor, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                                    <Setter TargetName="border" Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
                                </Trigger>

                                <Trigger Property="tools_controls:TreeViewItemAdv.IsDragOver" Value="True">
                                    <Setter TargetName="border" Property="Panel.Background" Value="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                                    <Setter TargetName="border" Property="TextElement.Foreground" Value="{Binding Path=MouseOverForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />

                                </Trigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="tools_controls:TreeViewItemAdv.IsSelected" Value="True" />
                                        <Condition Property="tools_controls:TreeViewItemAdv.IsSelectionActive" Value="False" />
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="border" Property="Panel.Background" Value="{Binding Path=SelectedBackground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                                    <Setter TargetName="border" Property="TextElement.Foreground" Value="{Binding Path=SelectedForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />

                                </MultiTrigger>
                                <Trigger Property="tools_controls:TreeViewItemAdv.IsDraging" Value="True">
                                    <Setter TargetName="border" Property="TextElement.Foreground" Value="{Binding Path=SelectedForeground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewAdv}}}" />
                                </Trigger>
                                <Trigger Property="UIElement.IsEnabled" Value="False">
                                    <Setter TargetName="border" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTreeViewItemAdvStyle}" TargetType="{x:Type tools_controls:TreeViewItemAdv}" />
    
    <!--  Style for ColumnHeader drag marker  -->
    <Style x:Key="SyncfusionTreeViewRowDragMarkerTemplatedAdornerInternalControlStyle" TargetType="{x:Type tools_controls:TreeViewRowDragMarkerTemplatedAdornerInternalControl}">
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="SnapsToDevicePixels" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:TreeViewRowDragMarkerTemplatedAdornerInternalControl}">
                    <Grid>
                        <Rectangle
                            Grid.Column="0"
                            Width="2"
                            Fill="{StaticResource BorderAlt3}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTreeViewRowDragMarkerTemplatedAdornerInternalControlStyle}" TargetType="{x:Type tools_controls:TreeViewRowDragMarkerTemplatedAdornerInternalControl}" />
    <!--  Style for ColumnHeader header marker  -->
    <Style x:Key="SyncfusionTreeViewColumnHeaderTemplatedAdornerInternalControlStyle" TargetType="{x:Type tools_controls:TreeViewColumnHeaderTemplatedAdornerInternalControl}">
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="SnapsToDevicePixels" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:TreeViewColumnHeaderTemplatedAdornerInternalControl}">
                    <Grid>
                        <Border
                            Grid.Column="0"
                            Background="Transparent"
                            BorderBrush="{StaticResource ContentBackground}"
                            BorderThickness="{StaticResource Windows11Dark.BorderThickness1}">
                            <TextBlock HorizontalAlignment="Center" Text="{Binding Path=Header, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:TreeViewColumn}}}" />
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionTreeViewColumnHeaderTemplatedAdornerInternalControlStyle}" TargetType="{x:Type tools_controls:TreeViewColumnHeaderTemplatedAdornerInternalControl}" />
</ResourceDictionary>
