# AirMonitor License Generator

AirMonitor许可证生成器是一个专门用于生成和管理AirMonitor空调监控软件许可证的工具。

## 功能特性

### 1. 许可证生成
- 支持4种许可证类型：普通版、售后版、研发版、管理版
- 灵活的有效期配置（支持永久许可证）
- 硬件指纹绑定，确保许可证安全性
- 功能模块化授权，精确控制软件功能
- RSA+AES混合加密，保证许可证安全

### 2. 许可证验证
- 数字签名验证，防止许可证被篡改
- 硬件指纹匹配验证
- 有效期检查
- 详细的验证结果显示

### 3. 模板管理
- 预设许可证模板，提高生成效率
- 自定义模板创建和编辑
- 模板导入导出功能
- 批量许可证生成支持

## 技术架构

### 核心技术栈
- **.NET 8**: 现代化的.NET平台
- **WPF**: Windows桌面应用程序框架
- **Syncfusion**: 现代化UI控件库
- **MVVM**: 清晰的架构模式
- **依赖注入**: 松耦合的组件设计

### 安全特性
- **RSA 2048位加密**: 非对称加密保护许可证
- **AES 256位加密**: 对称加密提高性能
- **SHA256数字签名**: 防止许可证被篡改
- **硬件指纹绑定**: 防止许可证被复制

### 项目结构
```
AirMonitor.LicenseGenerator/
├── Services/           # 业务服务层
│   ├── CryptoService.cs
│   ├── HardwareFingerprintService.cs
│   └── LicenseGeneratorService.cs
├── ViewModels/         # 视图模型层
│   ├── MainViewModel.cs
│   ├── LicenseGeneratorViewModel.cs
│   ├── LicenseValidatorViewModel.cs
│   └── TemplateManagerViewModel.cs
├── Views/              # 视图层
│   ├── LicenseGeneratorView.xaml
│   ├── LicenseValidatorView.xaml
│   └── TemplateManagerView.xaml
├── Converters/         # 数据转换器
├── Resources/          # 资源文件
│   └── Styles/         # 样式文件
└── App.xaml           # 应用程序入口
```

## 使用说明

### 1. 生成许可证
1. 选择"许可证生成"选项卡
2. 填写基本信息（许可证ID、客户信息等）
3. 选择许可证类型和有效期
4. 获取或导入硬件指纹
5. 选择授权功能
6. 点击"生成许可证"按钮
7. 保存生成的许可证文件

### 2. 验证许可证
1. 选择"许可证验证"选项卡
2. 选择要验证的许可证文件
3. 点击"验证"按钮
4. 查看验证结果和许可证详细信息

### 3. 管理模板
1. 选择"模板管理"选项卡
2. 创建、编辑或删除许可证模板
3. 导入导出模板文件
4. 使用模板快速生成许可证

## 许可证类型说明

### 普通版
- 基础监控功能
- 1年有效期
- 适用于一般用户

### 售后版
- 售后服务专用功能
- 1年有效期
- 包含高级分析和数据导出功能

### 研发版
- 所有功能
- 永久有效期
- 适用于研发团队

### 管理版
- 所有功能+管理权限
- 永久有效期
- 适用于管理人员

## 安全注意事项

1. **私钥保护**: 请妥善保管RSA私钥文件，避免泄露
2. **许可证分发**: 通过安全渠道分发许可证文件
3. **硬件绑定**: 确保硬件指纹的准确性
4. **定期更新**: 定期更新加密算法和密钥

## 系统要求

- **操作系统**: Windows 10/11 (x64)
- **.NET Runtime**: .NET 8.0 或更高版本
- **内存**: 最少 4GB RAM
- **存储**: 最少 100MB 可用空间

## 开发团队

AirMonitor Technologies - 专注于空调监控解决方案

## 版权信息

Copyright © 2024 AirMonitor Technologies. All rights reserved.

本软件仅供内部使用，不得用于商业销售。
