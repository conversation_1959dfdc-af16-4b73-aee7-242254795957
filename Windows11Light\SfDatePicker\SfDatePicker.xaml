<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Input="clr-namespace:Syncfusion.Windows.Controls.Input;assembly=Syncfusion.SfInput.WPF"
    xmlns:Microsoft_Windows_Themes="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:Sync_Resource="clr-namespace:Syncfusion.Windows.Controls.Input.Resources;assembly=Syncfusion.SfInput.Wpf"
    xmlns:input_controls="clr-namespace:Syncfusion.Windows.Controls;assembly=Syncfusion.SfInput.WPF"
    xmlns:converter="clr-namespace:Syncfusion.Windows.Converters;assembly=Syncfusion.SfInput.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:Syncfusion.Windows.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:shared_Brushconverter="clr-namespace:Syncfusion.Windows.Converters;assembly=Syncfusion.Shared.WPF"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Primitives;assembly=Syncfusion.SfInput.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/RepeatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphRepeatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/SfDateSelector/SfDateSelector.xaml" />
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Menu.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBox.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="DatePicker.MouseOver.Border" Color="DarkGray" />

    <SolidColorBrush x:Key="DatePicker.Focused.Border" Color="DarkGray" />

    <LinearGradientBrush x:Key="DatePickerBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DatePickerBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DatePickerBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Input:NameToScopeConverter x:Key="NameToScope" />

    <BooleanToVisibilityConverter x:Key="BooleanVisibilityConverter" />

    <converter:InverseBooleanToVisibilityConverter x:Key="InverseBooleanVisibilityConverter" />

    <shared_Brushconverter:BrushToColorConverter x:Key="BrushConverter" />

    <Style
        x:Key="SyncfusionSfDatePickerDropDownButtonStyle"
        BasedOn="{StaticResource WPFGlyphRepeatButtonStyle}"
        TargetType="{x:Type RepeatButton}">
        <Setter Property="ContentTemplate">
            <Setter.Value>
                <DataTemplate>
                    <Path
                        x:Name="path"
                        Width="12"
                        Height="12"
                        Margin="0,0,0,0"
                        Stroke="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                        RenderTransformOrigin="0.5,0.5"
                        Stretch="Uniform" 
                        Data="M1 5.5H13M10.5 0V3M3.5 0V3M1 2H13V12H1V2ZM10 9H11V10H10V9Z"/>
                </DataTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfDatePickerStyle" TargetType="{x:Type Input:SfDatePicker}">
        <Setter Property="AccentBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="BorderBrush" Value="{StaticResource DatePickerBorderBrush}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.ThemeBorderThicknessVariant1}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="SelectorStyle" Value="{StaticResource SyncfusionSfDateSelectorStyle}" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="DropDownHeight" Value="320" />
        <Setter Property="Height" Value="24" />
        <Setter Property="FormatString" Value="d" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="SelectorItemHeight" Value="32"/>
        <Setter Property="SelectorItemWidth" Value="64"/>

        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Input:SfDatePicker">
                    <Grid x:Name="DatePickerGrid" Background="Transparent">
                        <Border
                            x:Name="DatePickerBorder"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            SnapsToDevicePixels="True">
                            <Grid x:Name="DatePickerInnerGrid" >
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition x:Name="DropDownColumnDefinition" Width="auto" />
                                </Grid.ColumnDefinitions>
                                <Input:SfTextBoxExt
                                    x:Name="PART_TextBlock"
                                    Grid.Column="0"
                                    MinWidth="{TemplateBinding MinWidth}"
                                    MinHeight="{TemplateBinding MinHeight}"
                                    Margin="1,0"
                                    Padding="-7, 0, 0, 0"
                                    HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    AllowPointerEvents="True"
                                    Background="Transparent"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="0"
                                    CaretBrush="{StaticResource IconColor}"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    FontSize="{TemplateBinding FontSize}"
                                    FontStretch="{TemplateBinding FontStretch}"
                                    FontStyle="{TemplateBinding FontStyle}"
                                    FontWeight="{TemplateBinding FontWeight}"
                                    Foreground="{TemplateBinding Foreground}"
                                    VerticalAlignment="Center"
                                    IsReadOnly="True"
                                    IsTabStop="True"
                                    SelectionBrush="{StaticResource PrimaryBackground}"
                                    Text="{Binding Value, Mode=OneWay, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                    Visibility="{Binding AllowInlineEditing, Converter={StaticResource InverseBooleanVisibilityConverter}, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                    Watermark="{Binding Watermark, RelativeSource={RelativeSource TemplatedParent}}"
                                    WatermarkTemplate="{Binding WatermarkTemplate, RelativeSource={RelativeSource TemplatedParent}}" />

                                <Input:SfTextBoxExt
                                    x:Name="PART_TextBoxExt"
                                    Grid.Column="0"
                                    MinWidth="{TemplateBinding MinWidth}"
                                    MinHeight="{TemplateBinding MinHeight}"
                                    Margin="1,0"
                                    Padding="-7, 0, 0, 0"
                                    HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    VerticalAlignment="Center"
                                    AllowPointerEvents="True"
                                    Background="Transparent"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="0"
                                    CaretBrush="{StaticResource IconColor}"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    FontSize="{TemplateBinding FontSize}"
                                    FontStretch="{TemplateBinding FontStretch}"
                                    FontStyle="{TemplateBinding FontStyle}"
                                    FontWeight="{TemplateBinding FontWeight}"
                                    Foreground="{TemplateBinding Foreground}"
                                    InputScope="{Binding InputScope, RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource NameToScope}}"
                                    IsReadOnly="true"
                                    IsTabStop="{Binding AllowInlineEditing, RelativeSource={RelativeSource TemplatedParent}}"
                                    SelectionBrush="{StaticResource PrimaryBackground}"
                                    Text="{Binding Value, Mode=OneWay, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                    Visibility="{Binding AllowInlineEditing, Converter={StaticResource BooleanVisibilityConverter}, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                    Watermark="{Binding Watermark, RelativeSource={RelativeSource TemplatedParent}}"
                                    WatermarkTemplate="{Binding WatermarkTemplate, RelativeSource={RelativeSource TemplatedParent}}" />
                                <RepeatButton
                                    x:Name="PART_DropDownButton"
                                    Grid.Column="1"
                                    Width="NaN"
                                    Padding="5 0 5 0"
                                    Margin="4, 2, 4, 2"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    Background="Transparent"
                                    IsTabStop="False"
                                    Style="{StaticResource WPFRepeatButtonStyle}"
                                    Visibility="{Binding ShowDropDownButton, Converter={StaticResource BooleanVisibilityConverter}, RelativeSource={RelativeSource Mode=TemplatedParent}}" >
                                    <RepeatButton.Content>
                                        <Path
                        x:Name="path"
                        Width="12"
                        Height="12"
                        Margin="0,0,0,0"
                        Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                        RenderTransformOrigin="0.5,0.5"
                        Stretch="Uniform" 
                        Data="M12.4031 0.0373535C12.2847 0.0124512 12.1633 0 12.0391 0H1.96094C1.70056 0 1.45056 0.0546875 1.21094 0.164062C0.976562 0.268311 0.768188 0.411377 0.585938 0.59375C0.408813 0.770752 0.265625 0.979248 0.15625 1.21875C0.052124 1.45312 0 1.70044 0 1.96094V12.0391C0 12.1633 0.0118408 12.2852 0.0355225 12.4045C0.0615234 12.5356 0.101807 12.6638 0.15625 12.7891C0.265625 13.0234 0.408813 13.2317 0.585938 13.4141C0.768188 13.5911 0.976562 13.7344 1.21094 13.8438C1.45056 13.948 1.70056 14 1.96094 14H12.0391C12.2994 14 12.5469 13.948 12.7812 13.8438C13.0209 13.7344 13.2291 13.5911 13.4062 13.4141C13.5885 13.2317 13.7318 13.0234 13.8359 12.7891C13.9453 12.5496 14 12.2996 14 12.0391V1.96094C14 1.70044 13.9453 1.45312 13.8359 1.21875C13.7318 0.979248 13.5885 0.770752 13.4062 0.59375C13.2291 0.411377 13.0209 0.268311 12.7812 0.164062C12.6588 0.106934 12.5327 0.0646973 12.4031 0.0373535ZM1.60938 1.07812C1.72913 1.02612 1.85938 1 2 1H12C12.1354 1 12.2631 1.02612 12.3828 1.07812C12.5078 1.13013 12.6146 1.20312 12.7031 1.29688C12.7969 1.3855 12.8698 1.49219 12.9219 1.61719C12.974 1.73706 13 1.8645 13 2V3H7H6H1V2C1 1.85938 1.026 1.72925 1.07812 1.60938C1.11084 1.53418 1.15063 1.46533 1.19763 1.40234C1.22559 1.36499 1.2561 1.32983 1.28906 1.29688C1.38281 1.20312 1.48962 1.13013 1.60938 1.07812ZM6 4H1V12C1 12.0476 1.00293 12.0942 1.00891 12.1399C1.02051 12.2295 1.04358 12.3157 1.07812 12.3984C1.13025 12.5183 1.20056 12.6223 1.28906 12.7109C1.37756 12.7996 1.48181 12.8699 1.60156 12.9219C1.72656 12.9739 1.85938 13 2 13H12C12.1406 13 12.2709 12.9739 12.3906 12.9219C12.4536 12.8945 12.5123 12.8623 12.5667 12.8247C12.6156 12.791 12.6611 12.7529 12.7031 12.7109C12.7474 12.6667 12.787 12.6196 12.822 12.5696C12.8611 12.5134 12.8944 12.4539 12.9219 12.3906C12.974 12.2708 13 12.1406 13 12V4H7H6ZM3.07812 6.60938C3.026 6.72925 3 6.85938 3 7C3 7.14062 3.026 7.27344 3.07812 7.39844C3.13025 7.51831 3.20056 7.62231 3.28906 7.71094C3.33252 7.75439 3.37976 7.79346 3.43079 7.82812C3.48364 7.86426 3.54065 7.89551 3.60156 7.92188C3.72656 7.97388 3.85938 8 4 8C4.08459 8 4.16528 7.99072 4.24231 7.97168C4.29346 7.95923 4.3429 7.94263 4.39062 7.92188C4.51038 7.86987 4.61462 7.79956 4.70312 7.71094C4.79688 7.61719 4.86975 7.5105 4.92188 7.39062C4.974 7.26562 5 7.13281 5 6.99219C5 6.85156 4.974 6.72144 4.92188 6.60156C4.86975 6.48169 4.79944 6.37769 4.71094 6.28906C4.62244 6.20044 4.51819 6.13013 4.39844 6.07812C4.27869 6.02612 4.14844 6 4.00781 6C3.86719 6 3.73438 6.02612 3.60938 6.07812C3.48962 6.13013 3.38281 6.20312 3.28906 6.29688C3.20056 6.3855 3.13025 6.4895 3.07812 6.60938ZM6.00879 6.8623C6.00293 6.90698 6 6.95288 6 7C6 7.14062 6.026 7.27344 6.07812 7.39844C6.13025 7.51831 6.20056 7.62231 6.28906 7.71094C6.33032 7.7522 6.375 7.78955 6.4231 7.823C6.47815 7.86108 6.53772 7.89404 6.60156 7.92188C6.72656 7.97388 6.85938 8 7 8C7.07935 8 7.1554 7.9917 7.22815 7.9751C7.2843 7.9624 7.3385 7.94458 7.39062 7.92188C7.51038 7.86987 7.61462 7.79956 7.70312 7.71094C7.79688 7.61719 7.86975 7.5105 7.92188 7.39062C7.974 7.26562 8 7.13281 8 6.99219C8 6.85156 7.974 6.72144 7.92188 6.60156C7.86975 6.48169 7.79944 6.37769 7.71094 6.28906C7.62244 6.20044 7.51819 6.13013 7.39844 6.07812C7.27869 6.02612 7.14844 6 7.00781 6C6.86719 6 6.73438 6.02612 6.60938 6.07812C6.48962 6.13013 6.38281 6.20312 6.28906 6.29688C6.20056 6.3855 6.13025 6.4895 6.07812 6.60938C6.04346 6.68921 6.02026 6.77344 6.00879 6.8623ZM10.9219 7.39062C10.974 7.26562 11 7.13281 11 6.99219C11 6.90967 10.9911 6.83057 10.9731 6.75513C10.9604 6.70215 10.9434 6.65088 10.9219 6.60156C10.8698 6.48169 10.7994 6.37769 10.7109 6.28906C10.6224 6.20044 10.5182 6.13013 10.3984 6.07812C10.2787 6.02612 10.1484 6 10.0078 6C9.86719 6 9.73438 6.02612 9.60938 6.07812C9.48962 6.13013 9.38281 6.20312 9.28906 6.29688C9.20056 6.3855 9.13025 6.4895 9.07812 6.60938C9.026 6.72925 9 6.85938 9 7C9 7.14062 9.026 7.27344 9.07812 7.39844C9.13025 7.51831 9.20056 7.62231 9.28906 7.71094C9.37756 7.79956 9.48181 7.86987 9.60156 7.92188C9.72656 7.97388 9.85938 8 10 8C10.1406 8 10.2709 7.97388 10.3906 7.92188C10.5104 7.86987 10.6146 7.79956 10.7031 7.71094C10.7969 7.61719 10.8698 7.5105 10.9219 7.39062ZM4.97803 10.2146C4.99268 10.146 5 10.0745 5 10C5 9.85938 4.974 9.72925 4.92188 9.60938C4.86975 9.48438 4.79944 9.37769 4.71094 9.28906C4.62244 9.20044 4.51562 9.13013 4.39062 9.07812C4.27087 9.02612 4.14062 9 4 9C3.85938 9 3.72913 9.02612 3.60938 9.07812C3.48962 9.13013 3.38281 9.20312 3.28906 9.29688C3.20056 9.3855 3.13025 9.49219 3.07812 9.61719C3.026 9.73706 3 9.86719 3 10.0078C3 10.1484 3.026 10.2786 3.07812 10.3984C3.11499 10.4832 3.16089 10.5601 3.21594 10.6292C3.23877 10.6577 3.26318 10.6851 3.28906 10.7109C3.37756 10.7996 3.48181 10.8699 3.60156 10.9219C3.72131 10.9739 3.85156 11 3.99219 11C4.13281 11 4.26306 10.9739 4.38281 10.9219C4.50781 10.8699 4.61462 10.7996 4.70312 10.7109C4.79688 10.6172 4.86975 10.5105 4.92188 10.3906C4.94641 10.3342 4.96509 10.2756 4.97803 10.2146ZM7.92188 10.3906C7.974 10.2708 8 10.1406 8 10C8 9.94702 7.99634 9.89551 7.98889 9.84546C7.97668 9.7627 7.95435 9.68408 7.92188 9.60938C7.88953 9.53174 7.8501 9.46094 7.80371 9.39746C7.77539 9.35864 7.74451 9.32251 7.71094 9.28906C7.62244 9.20044 7.51562 9.13013 7.39062 9.07812C7.27087 9.02612 7.14062 9 7 9C6.85938 9 6.72913 9.02612 6.60938 9.07812C6.53406 9.11084 6.46387 9.15186 6.3988 9.20093C6.36047 9.22998 6.32385 9.26196 6.28906 9.29688C6.20056 9.3855 6.13025 9.49219 6.07812 9.61719C6.026 9.73706 6 9.86719 6 10.0078C6 10.1484 6.026 10.2786 6.07812 10.3984C6.13025 10.5183 6.20056 10.6223 6.28906 10.7109C6.37756 10.7996 6.48181 10.8699 6.60156 10.9219C6.72131 10.9739 6.85156 11 6.99219 11C7.13281 11 7.26306 10.9739 7.38281 10.9219C7.50781 10.8699 7.61462 10.7996 7.70312 10.7109C7.73474 10.6794 7.76392 10.6465 7.79077 10.6121C7.84363 10.5439 7.88733 10.4702 7.92188 10.3906Z"/>
                                    </RepeatButton.Content>
                                </RepeatButton>
                            </Grid>
                        </Border>
                        <Popup
                            x:Name="PART_DropDown"
                            AllowsTransparency="True"
                            FlowDirection="LeftToRight"
                            Placement="Bottom"
                            SnapsToDevicePixels="True"
                            StaysOpen="False">
                            <Border
                                Margin="16, 1, 16, 16"
                                BorderBrush="{StaticResource Border}"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                                Effect="{StaticResource Default.ShadowDepth4}"
                                CornerRadius="4">
                                <Border.RenderTransform>
                                    <TranslateTransform/>
                                </Border.RenderTransform>
                                <Border.Triggers>
                                    <EventTrigger RoutedEvent="Border.Loaded">
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimationUsingKeyFrames
                                                    BeginTime="00:00:00"
                                                    Storyboard.TargetProperty="(RenderTransform).(TranslateTransform.Y)">
                                                    <SplineDoubleKeyFrame
                                                       KeyTime="00:00:00.01"
                                                       Value="-800" />
                                                    <SplineDoubleKeyFrame
                                                       KeyTime="00:00:0.2"
                                                       Value="0"
                                                       KeySpline="0.0, 0.1, 0.0, 1.0" />
                                                </DoubleAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger>
                                </Border.Triggers>
                                <Grid x:Name="PART_DatePickerPage" Height="{Binding DropDownHeight, RelativeSource={RelativeSource Mode=TemplatedParent}}">
                                    <Input:SfDateSelector
                                        x:Name="PART_DateSelector"
                                        Height="{Binding DropDownHeight, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        AccentBrush="{Binding AccentBrush, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Background="{StaticResource PopupBackground}"
                                        BorderBrush="{StaticResource Border}"
                                        BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                        FlowDirection="{TemplateBinding FlowDirection}"
                                        Header="{x:Null}"
                                        IsEnabled="{Binding IsEnabled, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        IsTabStop="{Binding IsTabStop, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        SelectedDateTime="{Binding Value, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Style="{Binding SelectorStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" />
                                </Grid>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="Width" TargetName="DropDownColumnDefinition" Value="Auto" />
                            <Setter TargetName="PART_DropDownButton" Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="MinWidth" TargetName="PART_DropDownButton" Value="{StaticResource TouchMode.MinSize}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="PART_TextBlock" Property="Background" Value="Transparent" />
                            <Setter TargetName="DatePickerBorder" Property="BorderBrush" Value="{StaticResource DatePickerBorderBrushHovered}" />
                            <Setter TargetName="PART_TextBlock" Property="Foreground" Value="{StaticResource ContentForeground}" />
                            <Setter TargetName="DatePickerBorder" Property="Background" Value="{StaticResource ContentBackgroundAlt5}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="DatePickerBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="PART_TextBlock" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="DatePickerBorder" Property="Background" Value="{StaticResource ContentBackgroundAlt6}"/>
                            <Setter TargetName="PART_TextBlock" Property="Background" Value="Transparent"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="PART_TextBlock" Property="Background" Value="Transparent" />                            
                            <Setter TargetName="DatePickerBorder" Property="BorderBrush" Value="{StaticResource DatePickerBorderBrushFocused}" />
                            <Setter TargetName="PART_TextBlock" Property="Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocusWithin" Value="True">
                            <Setter TargetName="DatePickerBorder" Property="BorderBrush" Value="{StaticResource DatePickerBorderBrushFocused}" />
                            <Setter TargetName="DatePickerBorder" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="DatePickerBorder" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1112}" />
                            <Setter TargetName="PART_DropDownButton" Property="Padding" Value="5 0 5 -1"/>
                            <Setter TargetName="PART_TextBlock" Property="Background" Value="Transparent"/>
                            <Setter Property="Padding" Value="0,0,0,-1"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfDatePickerStyle}" TargetType="{x:Type Input:SfDatePicker}" />
</ResourceDictionary>
