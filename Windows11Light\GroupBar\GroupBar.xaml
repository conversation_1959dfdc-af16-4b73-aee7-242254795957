<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	
    xmlns:Sync_Resources="clr-namespace:Syncfusion.Windows.Tools.Controls.Resources"
    xmlns:componentModel="clr-namespace:System.ComponentModel;assembly=PresentationFramework"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:syncfusion="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
    xmlns:tools_resources="clr-namespace:Syncfusion.Windows.Tools.Controls.Resources;assembly=Syncfusion.Tools.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MsControl/ToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ListBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphPrimaryToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Menu.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ScrollViewer.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Separator.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/CheckBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GridSplitter.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphTreeExpander.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Label.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <syncfusion:HeaderTrimmingTemplate x:Key="SyncfusionHeaderTrimmingTemplate" />
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
    <syncfusion:WidthConverter x:Key="WidthConverter" />
    <syncfusion:AngleConverter x:Key="AngleConverter" />
    <syncfusion:IntToFontWeightConverter x:Key="IntToFontWeightConverter" />
    <syncfusion:CollapseToOrientationConverter x:Key="CollapseToOrientationConverter" />

    <DoubleAnimation x:Key="SyncfusionGroupBarSelectedAnimation"
                    Storyboard.TargetName="ContentHost"
                    From="0"
                    Duration="0:0:0.1" />
    
    <DoubleAnimation x:Key="SyncfusionGroupBarUnSelectedAnimation"
                    Storyboard.TargetName="ContentHost"
                    To="0"
                    Duration="0:0:0.1" />

    <Storyboard x:Key="SyncfusionOrientationChangedStoryboard" Storyboard.TargetName="ContentHost">
        <DoubleAnimation
                        BeginTime="0:0:0.0"
                        Storyboard.TargetProperty="(UIElement.Opacity)"
                        From="0"
                        To="1"
                        Duration="0:0:1" />
    </Storyboard>

    <!--  Overflow Button Style  -->
    <Style
        x:Key="OverFlowButtonStyle"
        BasedOn="{StaticResource WPFGlyphToggleButtonStyle}"
        TargetType="{x:Type ToggleButton}">
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Margin" Value="0,0,2,0"/>
        <Setter Property="Padding" Value="0,0,0,4"/>
        <Setter Property="Width" Value="30"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
    </Style>

    <!--  Context Menu for GroupBar  -->
    <ContextMenu
        x:Key="SyncfusionGroupBarContextMenu"
        ItemContainerStyle="{StaticResource WPFMenuItemStyle}"
        Style="{StaticResource WPFContextMenuStyle}">
        <MenuItem Command="syncfusion:GroupBar.CutItemCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemCut}" />
        <MenuItem Command="syncfusion:GroupBar.CopyItemCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemCopy}" />
        <MenuItem Command="syncfusion:GroupBar.PasteItemCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemPaste}" />
        <Separator Style="{StaticResource WPFSeparatorStyle}"/>
        <MenuItem
            Command="syncfusion:GroupBar.ChangeListViewModeCommand"
            Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemListView}"
            IsCheckable="True"
            IsChecked="{Binding Path=(PlacementTarget).(syncfusion:GroupView.IsListViewMode), RelativeSource={RelativeSource AncestorType={x:Type ContextMenu}}}" />
        <MenuItem Command="syncfusion:GroupBar.SortAscCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemSortAsc}" />
        <MenuItem Command="syncfusion:GroupBar.SortDesCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemSortDec}" />
        <Separator Style="{StaticResource WPFSeparatorStyle}"/>
        <MenuItem Command="syncfusion:GroupBar.AddTabCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemAddTab}" />
        <MenuItem Command="syncfusion:GroupBar.DeleteTabCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemDeleteTab}" />
        <MenuItem Command="syncfusion:GroupBar.RenameTabCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemRenameTab}" />
        <MenuItem Command="syncfusion:GroupBar.AddItemCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemAddItem}" />
        <MenuItem Command="syncfusion:GroupBar.RenameItemCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemRenameItem}" />
        <MenuItem Command="syncfusion:GroupBar.DeleteItemCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemDeleteItem}" />
        <Separator Style="{StaticResource WPFSeparatorStyle}"/>
        <MenuItem Command="syncfusion:GroupBar.MoveUpCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemMoveUp}" />
        <MenuItem Command="syncfusion:GroupBar.MoveDownCommand" Header="{syncfusion:ToolsLocalizationResourceExtension ResourceName=GroupbarMItemMoveDown}" />
    </ContextMenu>

    <!--  Collapse button Template  -->
    <ControlTemplate x:Key="CollapseToggleButtonTemplate" TargetType="{x:Type ToggleButton}">
        <Border
            x:Name="PART_Border"
            Width="{TemplateBinding Width}"
            Height="{TemplateBinding Height}"
            Padding="0"
            CornerRadius="{StaticResource Windows11Light.CornerRadius2}"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Background="Transparent"
            SnapsToDevicePixels="True"
            ToolTip="{Binding Path=CollapseButtonToolTip, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}">
            <Path
                x:Name="ExpandPath"
                Width="10"
                Height="7"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"                 
                Stroke="{StaticResource ContentForeground}"
                StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}"
                RenderTransformOrigin="0.5,0.5"
                Stretch="Fill">
                <Path.Data>
                    <Geometry>M8 48 L32 16 L56 48</Geometry>
                </Path.Data>
                <Path.RenderTransform>
                    <TransformGroup>
                        <RotateTransform Angle="270" />
                    </TransformGroup>
                </Path.RenderTransform>
            </Path>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="IsPressed" Value="true">
                <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource SecondaryBackgroundSelected}" />
            </Trigger>
            <DataTrigger Binding="{Binding Path=(syncfusion:GroupBar.IsCollapsed), RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}" Value="True">
                <Setter TargetName="PART_Border" Property="ToolTip" Value="{Binding Path=ExpandButtonToolTip, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}" />
            </DataTrigger>
            <Trigger Property="ToggleButton.IsChecked" Value="True">
                <Setter TargetName="ExpandPath" Property="RenderTransform">
                    <Setter.Value>
                        <TransformGroup>
                            <RotateTransform Angle="90" />
                        </TransformGroup>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter Property="Stroke" TargetName="ExpandPath" Value="{StaticResource IconColorHovered}"/>
            </Trigger>

            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource SecondaryBackgroundDisabled}" />
                <Setter Property="Stroke" TargetName="ExpandPath" Value="{StaticResource IconColorDisabled}"/>
            </Trigger>
            <DataTrigger Binding="{Binding Path=(syncfusion:GroupBar.IsCollapsed), RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}" Value="True">                        
                <Setter TargetName="PART_Border" Property="Margin" Value="4,4,6,4" />
                <Setter TargetName="PART_Border" Property="CornerRadius" Value="4,4,4,4" />
            </DataTrigger>                    
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  Header Style  -->
    <Style x:Key="HeaderStyle" TargetType="{x:Type Border}">
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="TextBlock.FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="TextBlock.TextAlignment" Value="Center" />
        <Setter Property="Height" Value="{Binding Path=HeaderHeight, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type syncfusion:GroupBar}}}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness0001}" />
        <Setter Property="CornerRadius" Value="{Binding Path=HeaderTopCornerRadius, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type syncfusion:GroupBar}}}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="TextElement.FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="TextElement.FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
    </Style>

    <DataTemplate x:Key="OverFlowItemTemplate">
        <DockPanel
            Name="MainGrid"
            Margin="5,0,0,0"
            SnapsToDevicePixels="True">
            <Border
                Name="ImageHost"
                Width="{Binding ImageWidth}"
                Height="{Binding ImageHeight}"
                Margin="{Binding ImageMargin}"
                HorizontalAlignment="{Binding ImageHorizontalAlignment}"
                VerticalAlignment="{Binding ImageVerticalAlignment}"
                DockPanel.Dock="Left">
                <Image
                    Name="Image"
                    Source="{Binding ImageSource}"
                    Stretch="Fill"
                    Visibility="Visible" />
            </Border>
            <Border
                Name="TextHost"
                Margin="{Binding TextMargin}"
                HorizontalAlignment="{Binding TextHorizontalAlignment}"
                VerticalAlignment="{Binding TextVerticalAlignment}"
                DockPanel.Dock="Top"
                TextBlock.Foreground="{Binding Foreground}">
                <StackPanel>
                    <TextBlock
                        Name="Text"
                        Margin="1"
                        HorizontalAlignment="{Binding TextHorizontalAlignment}"
                        VerticalAlignment="{Binding TextVerticalAlignment}"
                        Text="{Binding Path=Text}"
                        TextTrimming="CharacterEllipsis" />
                    <TextBox
                        Name="TextEditor"
                        Width="{Binding ActualWidth, ElementName=TextHost}"
                        Height="14"
                        Margin="1,0,0,0"
                        BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                        Text="{Binding Path=Text}"
                        Visibility="Collapsed" />
                </StackPanel>
            </Border>
        </DockPanel>
    </DataTemplate>

    <!--  Close Button Style  -->
    <Style
        x:Key="SyncfusionGroupBarCloseButtonStyle"
        BasedOn="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
        TargetType="{x:Type ToggleButton}">
        <Setter Property="Content">
            <Setter.Value>
                <Path
                    Name="pathButton"
                    Width="9"
                    Height="8"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Data="M109,51 L216,142 M215,52 L109,142"
                    SnapsToDevicePixels="False"
                    Stretch="Fill"
                    Stroke="{StaticResource ContentForeground}"
                    StrokeThickness="1.5" />
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
    
    <Style x:Key="CollapsedContentToggleButtonStyle" BasedOn="{StaticResource WPFToggleButtonStyle}" TargetType="ToggleButton">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Border Name="CollapsedContentHostBorder"
                            Background="{StaticResource ContentBackgroundAlt1}"
                            BorderBrush="{StaticResource BorderAlt}"
                            BorderThickness="{StaticResource Windows11Light.BorderThickness}">
                            <TextBlock Name="ToggleButtonText"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       FontSize="14"
                                       FontWeight="SemiBold"
                                       Foreground="{StaticResource ContentForeground}"
                                       Text="{Binding Path=NavigationPaneText, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type syncfusion:GroupBar}}}">
                                <TextBlock.LayoutTransform>
                                    <TransformGroup>
                                        <RotateTransform Angle="270" />
                                    </TransformGroup>
                                </TextBlock.LayoutTransform>
                            </TextBlock>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger SourceName="CollapsedContentHostBorder" Property="IsMouseOver" Value="True">
                            <Setter TargetName="CollapsedContentHostBorder" Property="ToolTip" Value="Click to expand groupbar item header" />
                            <Setter TargetName="CollapsedContentHostBorder" Property="Cursor" Value="Hand" />
                        </Trigger>
                        <DataTrigger Binding="{Binding Path=(syncfusion:GroupBar.Orientation), RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}" Value="Horizontal">
                            <Setter TargetName="ToggleButtonText" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="90" />
                                </Setter.Value>
                            </Setter>
                        </DataTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="CollapsedContentHostBorder" Property="Background" Value="{StaticResource SecondaryBackgroundDisabled}" />
                            <Setter TargetName="CollapsedContentHostBorder" Property="BorderBrush" Value="{StaticResource SecondaryBorderDisabled}" />
                            <Setter TargetName="ToggleButtonText" Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CircleKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  GroupBar StackedMode Template  -->
    <ControlTemplate x:Key="SyncfusionGroupBarStackedControlTemplate" TargetType="{x:Type syncfusion:GroupBar}">
        <Border
            Height="{TemplateBinding Height}"
            Background="{StaticResource ContentBackgroundAlt1}"
            CornerRadius="4"
            BorderBrush="{TemplateBinding BorderBrush}"
            BorderThickness="{TemplateBinding BorderThickness}">
            <Grid
                x:Name="MainHost"
                Background="{StaticResource ContentBackgroundAlt1}"
                SnapsToDevicePixels="True">
                <Grid.LayoutTransform>
                    <TransformGroup>
                        <RotateTransform Angle="{Binding Path=RotationAngle, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource AngleConverter}}" />
                    </TransformGroup>
                </Grid.LayoutTransform>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition />
                    <RowDefinition Height="Auto" />
                    <RowDefinition
                        Height="Auto"
                        MinHeight="{Binding MinDragHeight, RelativeSource={RelativeSource TemplatedParent}}"
                        MaxHeight="{Binding MaxDragHeight, RelativeSource={RelativeSource TemplatedParent}}" />

                </Grid.RowDefinitions>
                <Border
                    x:Name="HeaderHost"
                    Grid.Row="0"
                    Background="{StaticResource ContentBackgroundAlt1}"
                    BorderBrush="{StaticResource BorderAlt}"
                    BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                    CornerRadius="4"
                    KeyboardNavigation.IsTabStop="False"
                    Style="{Binding Path=GroupBarHeaderStyle, RelativeSource={RelativeSource TemplatedParent}}">                    
                    <DockPanel Name="HeaderDockPanel" Margin="{Binding Path=HeaderMarginRight, RelativeSource={RelativeSource TemplatedParent}}">
                        <ToggleButton
                            Name="CloseButton"
                            Grid.Column="1"
                            Width="22"
                            Height="22"
                            Margin="2,2,4,2"
                            ClickMode="Press"
                            Command="syncfusion:GroupBar.m_closeButtonCommand"
                            DockPanel.Dock="Right"
                            Style="{StaticResource SyncfusionGroupBarCloseButtonStyle}"
                            Visibility="Collapsed" />
                        <ToggleButton
                            Name="CollapseButton"
                            Grid.Column="1"
                            Margin="2,2,4,2"
                            ClickMode="Press"
                            DockPanel.Dock="Right"
                            IsChecked="{Binding Path=IsCollapsed, RelativeSource={RelativeSource TemplatedParent}}"
                            Template="{Binding Path=CollapseButtonTemplate, RelativeSource={RelativeSource TemplatedParent}}"
                            Width="22"
                            Height="22">
                            <ToggleButton.Style>
                                <Style BasedOn="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" TargetType="ToggleButton">
                                    <Setter Property="FrameworkElement.Focusable" Value="False" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Path=(syncfusion:GroupBar.AllowCollapse), RelativeSource={RelativeSource TemplatedParent}}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ToggleButton.Style>
                        </ToggleButton>
                        <ContentPresenter
                            Name="HeaderContent"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding SelectedHeader}"
                            ContentSource="SelectedHeader"
                            ContentTemplateSelector="{StaticResource SyncfusionHeaderTrimmingTemplate}" />
                    </DockPanel>
                </Border>
                <StackPanel
                    Name="ItemContentPanel"
                    Grid.Row="1"
                    Background="{StaticResource ContentBackgroundAlt1}">
                    <Border
                        Name="ContentHostBorder"
                        Height="{TemplateBinding ItemContentLength}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                        Focusable="True">
                        <ScrollViewer
                            Name="ScrollViewer"
                            Margin="{Binding Path=ScrollViewerMargin, RelativeSource={RelativeSource TemplatedParent}}"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            CanContentScroll="false"
                            Focusable="false"
                            HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                            VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}">
                            <ContentPresenter Name="ContentHost" ContentSource="SelectedContent">
                                <ContentPresenter.LayoutTransform>
                                    <TransformGroup>
                                        <RotateTransform Angle="{Binding Path=ContentRotationAngle, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource AngleConverter}}" />
                                    </TransformGroup>
                                </ContentPresenter.LayoutTransform>
                            </ContentPresenter>
                        </ScrollViewer>
                    </Border>
                    <ToggleButton
                        Name="CollapsedContentToggleButton"
                        Height="{TemplateBinding ItemContentLength}"
                        Command="syncfusion:GroupBar.m_popupCommand"
                        Visibility="{Binding IsCollapsed, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Style="{StaticResource CollapsedContentToggleButtonStyle}">
                    </ToggleButton>
                </StackPanel>
                <GridSplitter
                    x:Name="Splitter"
                    Grid.Row="2"
                    Height="4"
                    HorizontalAlignment="Stretch"
                    BorderBrush="{StaticResource BorderAlt}"
                    BorderThickness="2" />
                <Border
                    Grid.Row="3"
                    Background="{StaticResource ContentBackgroundAlt2}"
                    BorderBrush="{StaticResource BorderAlt}"
                    BorderThickness="{StaticResource Windows11Light.BorderThickness}">
                    <WrapPanel
                        Grid.Row="3"
                        Width="{Binding Width, RelativeSource={RelativeSource TemplatedParent}}"
                        Orientation="{Binding IsCollapsed, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource CollapseToOrientationConverter}}">

                        <ItemsPresenter Grid.Column="0" Width="{Binding ActualWidth, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource WidthConverter}}" />
                        <ToggleButton
                            x:Name="PART_OverFlowButton"
                            Grid.Column="1"
                            Content="{TemplateBinding OverFlowIndicatorContent}"
                            FlowDirection="RightToLeft"
                            Height="{Binding HeaderHeight, RelativeSource={RelativeSource TemplatedParent}}"
                            FontWeight="{Binding SelectedItem, ElementName=PART_OverFlow, Converter={StaticResource IntToFontWeightConverter}}"
                            Style="{TemplateBinding OverFlowButtonStyle}" />
                        <Popup
                            x:Name="PopUp"
                            Width="Auto"
                            Height="Auto"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Stretch"
                            AllowsTransparency="True"
                            FocusVisualStyle="{x:Null}"
                            Focusable="False"
                            IsOpen="{Binding IsChecked, ElementName=PART_OverFlowButton, Mode=TwoWay}"
                            Placement="Top"
                            PlacementTarget="{Binding ElementName=PART_OverFlowButton}"
                            StaysOpen="false">

                            <Border
                                HorizontalAlignment="Left"
                                VerticalAlignment="Stretch"
                                Background="{StaticResource ContentBackgroundAlt1}"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                BorderBrush="{StaticResource BorderAlt}"
                                CornerRadius="4"
                                Effect="{StaticResource Default.ShadowDepth4}">
                                <Border.Margin>
                                    <Thickness>8,6,8,16</Thickness>
                                </Border.Margin>
                                <StackPanel>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Grid.Margin>
                                            <Thickness>0,4,0,4</Thickness>
                                        </Grid.Margin>
                                        <ListBoxItem x:Name="PART_NavigationOption"  Grid.Row="0" Content="Navigation Option..." VerticalContentAlignment="Center" MinHeight="20">
                                            <ListBoxItem.Margin>
                                                <Thickness>4,2,4,2</Thickness>
                                            </ListBoxItem.Margin>
                                            <ListBoxItem.Padding>
                                                <Thickness>20,0,10,0</Thickness>
                                            </ListBoxItem.Padding>
                                        </ListBoxItem>
                                        <Separator x:Name="PART_OverflowSeparator" Grid.Row="1" BorderThickness="1" Height="1" MinHeight="1" MaxHeight="2" Visibility="Collapsed" IsHitTestVisible="False" Padding="0" Margin="30, 1, 0, 1" Width="123" Focusable="False" ></Separator>
                                        <ListBox Grid.Row="2"
                                        x:Name="PART_OverFlow"
                                        BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                        FocusVisualStyle="{x:Null}"
                                        ItemTemplate="{TemplateBinding OverFlowItemTemplate}"
                                        Style="{StaticResource WPFListBoxStyle}">
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <StackPanel IsItemsHost="True" />
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                            <ItemsControl.ItemContainerStyle>
                                                <Style BasedOn="{StaticResource WPFListBoxItemStyle}" TargetType="{x:Type ListBoxItem}">
                                                    <Setter Property="Margin" Value="2" />
                                                    <Setter Property="FocusVisualStyle" Value="{x:Null}" />
                                                </Style>
                                            </ItemsControl.ItemContainerStyle>
                                        </ListBox>
                                    </Grid>
                                </StackPanel>
                            </Border>
                        </Popup>
                    </WrapPanel>
                </Border>
                <Popup
                    x:Name="PART_Popup"
                    Grid.Row="4"
                    AllowsTransparency="True"
                    Focusable="False"
                    IsOpen="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                    PopupAnimation="Fade"
                    StaysOpen="False">
                    <Border
                        Name="PopupResizePart"
                        Background="{StaticResource ContentBackgroundAlt1}"
                        BorderBrush="{StaticResource BorderAlt}"
                        CornerRadius="4"
                        BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                        Effect="{StaticResource Default.ShadowDepth4}">
                        <Border.Margin>
                            <Thickness>8,0,8,16</Thickness>
                        </Border.Margin>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Name="PopupGripperRow" Height="0" />
                            </Grid.RowDefinitions>
                            <Border
                                Name="PopupInnerBorder"
                                Grid.Row="0"
                                Margin="2"
                                Background="{StaticResource ContentBackgroundAlt1}"
                                BorderBrush="{StaticResource BorderAlt}"
                                BorderThickness="{StaticResource Windows11Light.BorderThickness}">
                                <Grid>
                                    <ScrollViewer
                                        Name="popup_ScrollViewer"
                                        Margin="{Binding Path=ScrollViewerMargin, RelativeSource={RelativeSource TemplatedParent}}"
                                        Padding="{TemplateBinding Padding}"
                                        Background="{TemplateBinding Background}"
                                        CanContentScroll="false"
                                        Focusable="false"
                                        HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                        VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}">
                                        <ContentPresenter Name="PopupContentHost" Focusable="True" />
                                    </ScrollViewer>

                                    <Canvas
                                        Name="PopupGripper"
                                        Grid.Row="1"
                                        Height="10"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Bottom"
                                        RenderTransformOrigin="0.5,0.5"
                                        SnapsToDevicePixels="True"
                                        Visibility="{Binding Path=ShowGripper, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BooleanToVisibilityConverter}}">
                                        <Canvas.RenderTransform>
                                            <RotateTransform Angle="180" />
                                        </Canvas.RenderTransform>
                                        <Line
                                            SnapsToDevicePixels="True"
                                            Stroke="{StaticResource BorderAlt}"
                                            StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}"
                                            X1="1"
                                            X2="3"
                                            Y1="3"
                                            Y2="1" />
                                        <Line
                                            SnapsToDevicePixels="True"
                                            Stroke="{StaticResource BorderAlt}"
                                            StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}"
                                            X1="1"
                                            X2="6"
                                            Y1="6"
                                            Y2="1" />
                                        <Line
                                            SnapsToDevicePixels="True"
                                            Stroke="{StaticResource BorderAlt}"
                                            StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}"
                                            X1="1"
                                            X2="9"
                                            Y1="9"
                                            Y2="1" />
                                    </Canvas>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                </Popup>
            </Grid>
        </Border>

        <ControlTemplate.Triggers>
            <Trigger Property="ShowGripper" Value="False">
                <Setter TargetName="PopupGripperRow" Property="Height" Value="5" />
            </Trigger>
            <Trigger Property="Orientation" Value="Horizontal">
                <Setter TargetName="Splitter" Property="Cursor" Value="SizeWE" />
            </Trigger>
            <Trigger Property="IsCollapsed" Value="False">
                <Setter TargetName="CollapsedContentToggleButton" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="ContentHostBorder" Property="Visibility" Value="Visible" />
                <Setter TargetName="Splitter" Property="Cursor" Value="Arrow" />
                <Setter TargetName="Splitter" Property="Height" Value="0" />
                <Setter TargetName="ScrollViewer" Property="Margin" Value="0"/>
                <Setter TargetName="HeaderContent" Property="TextBlock.FontWeight" Value="SemiBold" />
            </Trigger>
            <Trigger Property="IsCollapsed" Value="True">
                <Setter TargetName="HeaderDockPanel" Property="Margin" Value="0" />
                <Setter TargetName="HeaderContent" Property="Visibility" Value="Hidden" />
                <Setter TargetName="CollapsedContentToggleButton" Property="Visibility" Value="Visible" />
                <Setter TargetName="ContentHostBorder" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="Splitter" Property="Cursor" Value="SizeNS" />
                <Setter TargetName="Splitter" Property="Height" Value="2" />
                <Setter TargetName="CollapseButton" Property="Margin" Value="0"/>
                <Setter TargetName="PART_OverFlowButton" Property="Margin" Value="0"/>
                <Setter TargetName="PART_OverFlowButton" Property="Width" Value="{Binding Path=CollapsedWidth, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}"/>
                <Setter TargetName="CollapseButton" Property="Height" Value="{Binding Path=HeaderHeight, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}"/>
                <Setter TargetName="CollapseButton" Property="Width" Value="{Binding Path=CollapsedWidth, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}"/>
                <Setter TargetName="ItemContentPanel" Property="Background" Value="{StaticResource ContentBackgroundAlt1}"/>
                <Setter TargetName="ItemContentPanel" Property="Height" Value="{Binding Path=ItemContentLength, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}" />
            </Trigger>
            <Trigger Property="IsToolBarEnabled" Value="True">
                <Setter TargetName="Splitter" Property="Visibility" Value="Visible" />

            </Trigger>
            <Trigger Property="IsToolBarEnabled" Value="False">
                <Setter TargetName="Splitter" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="HeaderHost" Property="TextBlock.TextAlignment" Value="Left" />
                <Setter TargetName="HeaderHost" Property="Padding" Value="5,1,1,1" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsCloseButtonEnabled" Value="True" />
                    <Condition Property="IsToolBarEnabled" Value="False" />
                    <Condition Property="IsCollapsed" Value="False" />
                </MultiTrigger.Conditions>
                <Setter TargetName="CloseButton" Property="Visibility" Value="Visible" />
                <Setter TargetName="CollapseButton" Property="Margin" Value="0"/>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsCloseButtonEnabled" Value="True" />
                    <Condition Property="IsToolBarEnabled" Value="False" />
                    <Condition Property="IsCollapsed" Value="True" />
                </MultiTrigger.Conditions>
                <Setter TargetName="CloseButton" Property="Visibility" Value="Collapsed" />
            </MultiTrigger>
            <Trigger SourceName="CloseButton" Property="IsChecked" Value="True">
                <Setter Property="Visibility" Value="Collapsed" />
            </Trigger>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter TargetName="HeaderContent" Property="TextBlock.FontSize" Value="18" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="HeaderContent" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  GroupBar MultipleExpansion Template  -->
    <ControlTemplate x:Key="SyncfusionGroupBarMultipleExpansionControlTemplate" TargetType="{x:Type syncfusion:GroupBar}">
        <Grid
            Name="MainHost"
            Background="{TemplateBinding Background}"
            ClipToBounds="True"
            SnapsToDevicePixels="True">
            <Grid.LayoutTransform>
                <TransformGroup>
                    <RotateTransform Angle="{Binding Path=RotationAngle, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource AngleConverter}}" />
                </TransformGroup>
            </Grid.LayoutTransform>
            <Border
                Name="ItemsHost"
                Background="{TemplateBinding Background}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}"
                CornerRadius="{TemplateBinding CornerRadius}">
                <ScrollViewer
                    Name="ScrollViewer"
                    Margin="{Binding Path=ScrollViewerMargin, RelativeSource={RelativeSource TemplatedParent}}"
                    Focusable="False"
                    VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}">
                    <ItemsPresenter />
                </ScrollViewer>
            </Border>
        </Grid>
    </ControlTemplate>

    <!--  GroupBar NonStackedMode Template  -->
    <ControlTemplate x:Key="SyncfusionGroupBarNonStackedControlTemplate" TargetType="{x:Type syncfusion:GroupBar}">
        <Grid
            Name="MainHost"
            Background="{StaticResource ContentBackgroundAlt1}"
            ClipToBounds="True"
            SnapsToDevicePixels="True">
            <Grid.LayoutTransform>
                <TransformGroup>
                    <RotateTransform Angle="{Binding Path=RotationAngle, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource AngleConverter}}" />
                </TransformGroup>
            </Grid.LayoutTransform>
            <Border
                Name="ItemsHost"
                Background="{TemplateBinding Background}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}"
                CornerRadius="{TemplateBinding CornerRadius}">
                <ScrollViewer
                    Name="ScrollViewer"
                    Margin="{Binding Path=ScrollViewerMargin, RelativeSource={RelativeSource TemplatedParent}}"
                    Focusable="False"
                    VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}">
                    <ItemsPresenter Margin="{Binding Path=ScrollViewerMargin, RelativeSource={RelativeSource TemplatedParent}}" />
                </ScrollViewer>
            </Border>
        </Grid>
    </ControlTemplate>

    <!--  GroupBar Style  -->

    <Style x:Key="SyncfusionGroupBarStyle" TargetType="{x:Type syncfusion:GroupBar}">
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="ItemHeaderHeight" Value="32" />
        <Setter Property="HeaderHeight" Value="32" />
        <Setter Property="OverFlowItemTemplate" Value="{StaticResource OverFlowItemTemplate}" />
        <Setter Property="OverFlowButtonStyle" Value="{StaticResource OverFlowButtonStyle}" />
        <Setter Property="GroupBarHeaderStyle" Value="{StaticResource HeaderStyle}" />
        <Setter Property="StackItemHostHeight" Value="32" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="FocusManager.IsFocusScope" Value="True" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="Local" />
        <Setter Property="KeyboardNavigation.ControlTabNavigation" Value="None" />
        <Setter Property="KeyboardNavigation.DirectionalNavigation" Value="Cycle" />
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <VirtualizingStackPanel
                        KeyboardNavigation.ControlTabNavigation="None"
                        KeyboardNavigation.DirectionalNavigation="Cycle"
                        KeyboardNavigation.TabNavigation="Local" />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Focusable" Value="False" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="DragMarkerBrush" Value="{StaticResource BorderAlt3}" />
        <Setter Property="TextElement.Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}" />
        <Setter Property="VerticalOrientationStoryboard">
            <Setter.Value>
                <Storyboard>
                    <DoubleAnimation
                        Storyboard.TargetProperty="(LayoutTransform).(Children)[0].(Angle)"
                        To="0"
                        Duration="0:0:0.6" />
                </Storyboard>
            </Setter.Value>
        </Setter>
        <Setter Property="HorizontalOrientationStoryboard">
            <Setter.Value>
                <Storyboard>
                    <DoubleAnimation
                        Storyboard.TargetProperty="(LayoutTransform).(Children)[0].(Angle)"
                        To="-90"
                        Duration="0:0:0.6" />
                </Storyboard>
            </Setter.Value>
        </Setter>
        <Setter Property="OrientationChangedContentStoryboard" Value="{StaticResource SyncfusionOrientationChangedStoryboard}">
        </Setter>
        <Setter Property="CollapseButtonBackground" Value="Transparent" />
        <Setter Property="CollapseButtonMouseOverBackground" Value="{StaticResource SecondaryBackgroundHovered}" />
        <Setter Property="CollapseButtonTemplate" Value="{StaticResource CollapseToggleButtonTemplate}" />

        <Style.Triggers>
            <Trigger Property="componentModel:DesignerProperties.IsInDesignMode" Value="False">
                <Setter Property="ContextMenu" Value="{StaticResource SyncfusionGroupBarContextMenu}" />
            </Trigger>

            <Trigger Property="syncfusion:GroupBar.VisualMode" Value="Default">
                <Setter Property="Template" Value="{StaticResource SyncfusionGroupBarNonStackedControlTemplate}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="syncfusion:GroupBar.VisualMode" Value="StackMode" />
                    <Condition Property="IsCollapsed" Value="False" />
                </MultiTrigger.Conditions>
                <Setter Property="ItemsPanel">
                    <Setter.Value>
                        <ItemsPanelTemplate>
                            <syncfusion:OverflowPanel x:Name="PART_OverFlowPanel" Orientation="Horizontal" />
                        </ItemsPanelTemplate>
                    </Setter.Value>
                </Setter>
                <Setter Property="Template" Value="{StaticResource SyncfusionGroupBarStackedControlTemplate}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="syncfusion:GroupBar.VisualMode" Value="StackMode" />
                    <Condition Property="IsCollapsed" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="ItemsPanel">
                    <Setter.Value>
                        <ItemsPanelTemplate>
                            <syncfusion:OverflowPanel x:Name="PART_OverFlowPanel" Orientation="Vertical" />
                        </ItemsPanelTemplate>
                    </Setter.Value>
                </Setter>
                <Setter Property="Template" Value="{StaticResource SyncfusionGroupBarStackedControlTemplate}" />
            </MultiTrigger>
            <Trigger Property="syncfusion:GroupBar.VisualMode" Value="MultipleExpansion">
                <Setter Property="ItemHeaderHeight" Value="26" />
                <Setter Property="Template" Value="{StaticResource SyncfusionGroupBarMultipleExpansionControlTemplate}" />
            </Trigger>

            <Trigger Property="syncfusion:GroupBar.IsEnabledContextMenu" Value="False">
                <Setter Property="ContextMenuService.IsEnabled" Value="False" />
            </Trigger>

            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="HeaderHeight" Value="45" />
                <Setter Property="ItemHeaderHeight" Value="45" />
                <Setter Property="StackItemHostHeight" Value="45" />
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionGroupBarStyle}" TargetType="{x:Type syncfusion:GroupBar}" />

    <!--  GroupBarItem NonStackedMode Template  -->
    <ControlTemplate x:Key="SyncfusionGroupBarItemNonStackedControlTemplate" TargetType="{x:Type syncfusion:GroupBarItem}">
        <Grid
            Name="MainGrid"
            ClipToBounds="True"
            SnapsToDevicePixels="True">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Border
                Name="HeaderHost"
                Grid.Row="0"
                Height="{Binding Path=ItemHeaderHeight, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}"
                Background="{TemplateBinding Background}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                Cursor="{Binding Path=GroupBarItemCursor, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}"
                >
                <Border.Margin>
                    <Thickness>5,0,5,0</Thickness>
                </Border.Margin>
                <ContentPresenter
                    x:Name="contentText"                    
                    VerticalAlignment="Center"
                    Content="{TemplateBinding Header}"
                    ContentTemplate="{TemplateBinding HeaderTemplate}"
                    ContentTemplateSelector="{TemplateBinding HeaderTemplateSelector}"
                    TextElement.FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                    TextElement.FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                    TextElement.Foreground="{StaticResource ContentForeground}">
                    <ContentPresenter.Margin>
                        <Thickness>6,2,4,2</Thickness>
                    </ContentPresenter.Margin>
                    <ContentPresenter.Resources>
                        <Style BasedOn="{x:Null}" TargetType="TextBlock" />
                    </ContentPresenter.Resources>
                </ContentPresenter>
            </Border>
            <Border
                Name="ContentHostBorder"
                Grid.Row="1"
                Background="{StaticResource ContentBackgroundAlt1}"
                BorderBrush="{StaticResource BorderAlt}"
                BorderThickness="{StaticResource Windows11Light.BorderThickness}">
                <Border.LayoutTransform>
                    <TransformGroup>
                        <RotateTransform Angle="{Binding Path=ContentRotationAngle, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}, Converter={StaticResource AngleConverter}}" />
                    </TransformGroup>
                </Border.LayoutTransform>
                <Border.Margin>
                    <Thickness>0,2,0,0</Thickness>
                </Border.Margin>
                <ContentPresenter
                    Name="ContentHost"
                    Margin="0,0,0,0"
                    Content="{TemplateBinding Content}"
                    ContentTemplate="{TemplateBinding ContentTemplate}"
                    ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}" />
            </Border>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter TargetName="HeaderHost" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="HeaderHost" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="contentText" Property="TextElement.Foreground" Value="{StaticResource HoveredForeground}" />
            </Trigger>

            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsSelected" Value="True" />
                    <Condition Property="IsEnabled" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.EnterActions>
                    <BeginStoryboard>
                        <BeginStoryboard.Storyboard>
                            <Storyboard Storyboard.TargetName="ContentHost">
                                <DoubleAnimation
                                    Storyboard.TargetProperty="(UIElement.Opacity)"
                                    From="0"
                                    To="1"
                                    Duration="0:0:0.5" />
                            </Storyboard>
                        </BeginStoryboard.Storyboard>
                    </BeginStoryboard>
                </MultiTrigger.EnterActions>
                <Setter TargetName="HeaderHost" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                <Setter TargetName="HeaderHost" Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                <Setter TargetName="contentText" Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
            </MultiTrigger>

            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="HeaderHost" Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                <Setter TargetName="HeaderHost" Property="BorderBrush" Value="{StaticResource ContentBackgroundDisabled}" />
                <Setter TargetName="contentText" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  GroupBarItem MultipleExpansion Template  -->
    <ControlTemplate x:Key="SyncfusionGroupBarItemMultipleExpansionControlTemplate" TargetType="{x:Type syncfusion:GroupBarItem}">
        <Grid
            Name="MainGrid"
            ClipToBounds="True"
            SnapsToDevicePixels="True">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <StackPanel>
                <Border
                    Name="HeaderHost"
                    Height="{Binding Path=ItemHeaderHeight, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}"                   
                    BorderThickness="0.5"
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    ClipToBounds="True"
                    Cursor="{Binding Path=GroupBarItemCursor, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}"
                    >
                    <Border.Margin>
                        <Thickness>5,0,5,0</Thickness>
                    </Border.Margin>
                    <Grid>
                        <Border x:Name="SelectionIndicator"
                                HorizontalAlignment="Left"
                                CornerRadius="1.5"
                                Height="12"
                                Width="2"
                                Visibility="Collapsed"
                                Background="{StaticResource PrimaryBackground}" />
                        <DockPanel>
                            <DockPanel.Margin>
                                <Thickness>0,0,0,0</Thickness>
                            </DockPanel.Margin>
                            <ToggleButton Name="Expander"
                                          ClickMode="Press"
                                          DockPanel.Dock="Left"
                                          IsChecked="{Binding Path=IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                                          Style="{StaticResource WPFGlyphTreeExpanderToggleStyle}">
                                <ToggleButton.Margin>
                                    <Thickness>8,4,4,4</Thickness>
                                </ToggleButton.Margin>
                            </ToggleButton>
                            <ContentPresenter x:Name="contentText"
                                              VerticalAlignment="Center"
                                              Content="{TemplateBinding Header}"
                                              ContentTemplate="{TemplateBinding HeaderTemplate}"
                                              ContentTemplateSelector="{TemplateBinding HeaderTemplateSelector}"
                                              DockPanel.Dock="Top"
                                              TextElement.FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                              TextElement.FontSize="{StaticResource Windows11Light.BodyTextStyle}"
                                              TextElement.Foreground="{StaticResource ContentForeground}">
                                <ContentPresenter.Margin>
                                    <Thickness>4,2,0,2</Thickness>
                                </ContentPresenter.Margin>
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}"
                                           TargetType="TextBlock" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </DockPanel>
                    </Grid>                    
                </Border>
            </StackPanel>

            <Border
                Name="ContentHostBorder"
                Grid.Row="1"
                Width="0"
                Height="0"                
                Background="{StaticResource ContentBackgroundAlt1}"
                BorderBrush="{StaticResource BorderAlt}"
                BorderThickness="{StaticResource Windows11Light.BorderThickness}">
                <Border.Margin>
                    <Thickness>0,2,0,0</Thickness>
                </Border.Margin>
                <Border.LayoutTransform>
                    <TransformGroup>
                        <RotateTransform Angle="{Binding Path=ContentRotationAngle, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}, Converter={StaticResource AngleConverter}}" />
                    </TransformGroup>
                </Border.LayoutTransform>
                <ContentPresenter
                    Name="ContentHost"
                    Width="{Binding Width, ElementName=ContentHostBorder, UpdateSourceTrigger=PropertyChanged}"
                    Height="{Binding Height, ElementName=ContentHostBorder, UpdateSourceTrigger=PropertyChanged}"
                    Content="{TemplateBinding Content}"
                    ContentTemplate="{TemplateBinding ContentTemplate}"
                    ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}"
                    KeyboardNavigation.ControlTabNavigation="Continue"
                    KeyboardNavigation.DirectionalNavigation="Continue"
                    KeyboardNavigation.IsTabStop="False"
                    KeyboardNavigation.TabNavigation="Continue" />
            </Border>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsLastItem" Value="True">
                <Setter TargetName="ContentHostBorder" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}" />
            </Trigger>
            <Trigger Property="IsMouseOver" SourceName="HeaderHost" Value="True">
                <Setter TargetName="HeaderHost" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="HeaderHost" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="contentText" Property="TextElement.Foreground" Value="{StaticResource HoveredForeground}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsSelected" Value="True" />
                    <Condition Property="IsEnabled" Value="True" />
                </MultiTrigger.Conditions>
                <Setter TargetName="HeaderHost" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                <Setter TargetName="HeaderHost" Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                <Setter TargetName="contentText" Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
                <Setter TargetName="contentText" Property="TextBlock.Foreground" Value="{StaticResource SelectedForeground}" />
            </MultiTrigger>
            <Trigger Property="IsExpanded" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <BeginStoryboard.Storyboard>
                            <Storyboard Storyboard.TargetName="ContentHost">
                                <DoubleAnimation
                                    Storyboard.TargetProperty="(UIElement.Opacity)"
                                    From="0"
                                    To="1"
                                    Duration="0:0:0.5" />
                            </Storyboard>
                        </BeginStoryboard.Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="HeaderHost" Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                <Setter TargetName="HeaderHost" Property="BorderBrush" Value="{StaticResource ContentBackgroundDisabled}" />
                <Setter TargetName="contentText" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>                            
            <Trigger Property="IsSelected" Value="True">
                <Setter TargetName="SelectionIndicator" Property="Visibility" Value="Visible"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  GroupBarItem StackedMode Template  -->

    <ControlTemplate x:Key="SyncfusionGroupBarItemStackedControlTemplate" TargetType="{x:Type syncfusion:GroupBarItem}">
        <Grid
            Name="HeaderGrid"
            Grid.Row="0"
            Height="{Binding Path=ItemHeaderHeight, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}"
            ClipToBounds="True"
            Cursor="{Binding Path=GroupBarItemCursor, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}">
            <Grid.RowDefinitions>
                <RowDefinition Height="0.5*" />
                <RowDefinition Height="0.5*" />
            </Grid.RowDefinitions>
            <Border
                Name="HeaderHost"
                Grid.Row="0"
                Grid.RowSpan="2"
                Padding="5,5,6,5"
                Background="{StaticResource ContentBackgroundAlt1}"
                BorderBrush="{StaticResource BorderAlt}"
                BorderThickness="{StaticResource Windows11Light.BorderThickness}">
                <ContentPresenter
                    x:Name="contentText"
                    Content="{TemplateBinding Header}"
                    ContentTemplate="{TemplateBinding HeaderTemplate}"
                    ContentTemplateSelector="{TemplateBinding HeaderTemplateSelector}"
                    KeyboardNavigation.ControlTabNavigation="Continue"
                    KeyboardNavigation.DirectionalNavigation="Continue"
                    KeyboardNavigation.IsTabStop="False"
                    KeyboardNavigation.TabNavigation="Continue" />
            </Border>
        </Grid>

        <ControlTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=(syncfusion:GroupBar.IsCollapsed), RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}" Value="True">
                <Setter TargetName="HeaderHost" Property="Padding" Value="0" />                                      
            </DataTrigger>
            
            <Trigger Property="IsMouseOver" Value="True">
                <Setter TargetName="HeaderHost" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="HeaderHost" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="contentText" Property="TextElement.Foreground" Value="{StaticResource HoveredForeground}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsSelected" Value="True" />
                    <Condition Property="IsEnabled" Value="True" />
                </MultiTrigger.Conditions>
                <Setter TargetName="HeaderHost" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                <Setter TargetName="HeaderHost" Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                <Setter TargetName="contentText" Property="TextElement.Foreground" Value="{StaticResource SelectedForeground}" />
            </MultiTrigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="HeaderHost" Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                <Setter TargetName="HeaderHost" Property="BorderBrush" Value="{StaticResource ContentBackgroundDisabled}" />
                <Setter TargetName="contentText" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  GroupBarItem Style  -->

    <Style x:Key="SyncfusionGroupBarItemStyle" TargetType="{x:Type syncfusion:GroupBarItem}">
        <Setter Property="GroupBarItemCornerRadius" Value="4" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="AllowDrop" Value="True" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="SelectedAnimation" Value="{StaticResource SyncfusionGroupBarSelectedAnimation}"/>
        <Setter Property="UnselectedAnimation" Value="{StaticResource SyncfusionGroupBarUnSelectedAnimation}"/>
        <Setter Property="Margin">
            <Setter.Value>
                <Thickness>0,2,0,2</Thickness>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Path=(syncfusion:GroupBar.IsCollapsed), RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}" Value="True">
                <Setter Property="GroupBarItemCornerRadius" Value="4" />
                <Setter Property="Margin" Value="2,1,2,1" />                        
            </DataTrigger>
            
            <Trigger Property="componentModel:DesignerProperties.IsInDesignMode" Value="False">
                <Setter Property="ContextMenu" Value="{StaticResource SyncfusionGroupBarContextMenu}" />
            </Trigger>

            <Trigger Property="syncfusion:GroupBar.VisualMode" Value="Default">
                <Setter Property="Template" Value="{StaticResource SyncfusionGroupBarItemNonStackedControlTemplate}" />
            </Trigger>

            <Trigger Property="syncfusion:GroupBar.VisualMode" Value="StackMode">
                <Setter Property="Template" Value="{StaticResource SyncfusionGroupBarItemStackedControlTemplate}" />
            </Trigger>
            <Trigger Property="syncfusion:GroupBar.VisualMode" Value="MultipleExpansion">
                <Setter Property="Template" Value="{StaticResource SyncfusionGroupBarItemMultipleExpansionControlTemplate}" />
            </Trigger>

            <Trigger Property="syncfusion:GroupBar.IsEnabledContextMenu" Value="False">
                <Setter Property="ContextMenuService.IsEnabled" Value="False" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionGroupBarItemStyle}" TargetType="{x:Type syncfusion:GroupBarItem}" />

    <!--  GroupBarItemHeader style  -->

    <Style x:Key="SyncfusionGroupBarItemHeaderStyle" TargetType="{x:Type syncfusion:GroupBarItemHeader}">
        <Setter Property="KeyboardNavigation.IsTabStop" Value="True" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="ImageMargin" Value="0,0,5,0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type syncfusion:GroupBarItemHeader}">
                    <DockPanel
                        Name="MainGrid"                       
                        SnapsToDevicePixels="True">
                        <DockPanel.Margin>
                            <Thickness>0,0,0,0</Thickness>
                        </DockPanel.Margin>
                        <Border
                            Name="ImageHost"
                            Width="{TemplateBinding ImageWidth}"
                            Height="{TemplateBinding ImageHeight}"
                            Margin="{TemplateBinding ImageMargin}"
                            HorizontalAlignment="{TemplateBinding ImageHorizontalAlignment}"
                            VerticalAlignment="{TemplateBinding ImageVerticalAlignment}"
                            DockPanel.Dock="Left">
                            <Border>
                                <Image
                                    Name="Image"
                                    Source="{TemplateBinding ImageSource}"
                                    Stretch="Fill" />
                            </Border>
                        </Border>
                        <Border
                            Name="TextHost"
                            Margin="{TemplateBinding TextMargin}"
                            HorizontalAlignment="{TemplateBinding TextHorizontalAlignment}"
                            VerticalAlignment="{TemplateBinding TextVerticalAlignment}"
                            DockPanel.Dock="Top"
                            TextBlock.Foreground="{Binding Path=Foreground, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type syncfusion:GroupBarItem}}}">
                            <StackPanel>
                                <TextBlock
                                    Name="Text"
                                    Margin="1"
                                    HorizontalAlignment="{TemplateBinding TextHorizontalAlignment}"
                                    VerticalAlignment="{TemplateBinding TextVerticalAlignment}"
                                    Text="{Binding Path=Text, RelativeSource={RelativeSource TemplatedParent}}"
                                    TextTrimming="CharacterEllipsis" />
                                <TextBox
                                    Name="TextEditor"
                                    Height="14"
                                    Margin="1,0,0,0"
                                    HorizontalAlignment="Center"
                                    BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                                    Text="{Binding Path=Text, RelativeSource={RelativeSource TemplatedParent}}"
                                    Visibility="Collapsed" />
                            </StackPanel>
                        </Border>
                    </DockPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsInEditMode" Value="True">
                            <Setter TargetName="TextEditor" Property="Visibility" Value="Visible" />
                            <Setter TargetName="Text" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="ImageHost" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="TextHost" Property="HorizontalAlignment" Value="Stretch" />
                            <Setter TargetName="MainGrid" Property="Margin" Value="0,0,0,0" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Text" Property="TextElement.Foreground" Value="{StaticResource HoveredForeground}" />
                            <Setter TargetName="TextEditor" Property="TextElement.Foreground" Value="{StaticResource HoveredForeground}" />
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBarItem}}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="Text" Property="Foreground" Value="{StaticResource SelectedForeground}" />
                            <Setter TargetName="TextEditor" Property="Foreground" Value="{StaticResource SelectedForeground}" />
                        </MultiDataTrigger>

                        <DataTrigger Binding="{Binding Path=(syncfusion:GroupBar.IsCollapsed), RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBar}}}" Value="True">
                            <Setter TargetName="ImageHost"
                                    Property="Margin">
                                <Setter.Value>
                                    <Thickness>4,0,4,0</Thickness>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="Text" Property="Visibility" Value="Collapsed" />                             
                        </DataTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Text" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="TextEditor" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionGroupBarItemHeaderStyle}" TargetType="{x:Type syncfusion:GroupBarItemHeader}" />

    <!--  GroupView MultipleExpansion Template  -->
    <ControlTemplate x:Key="SyncfusionGroupViewMultipleExpansionControlTemplate" TargetType="{x:Type syncfusion:GroupView}">
        <StackPanel
            Name="MainHost"
            Background="{StaticResource ContentBackgroundAlt1}"
            TextBlock.Foreground="{TemplateBinding Foreground}">
            <ItemsPresenter Margin="0,0,0,0" />
        </StackPanel>
    </ControlTemplate>

    <!--  GroupView Default Template  -->

    <ControlTemplate x:Key="SyncfusionGroupViewDefaultControlTemplate" TargetType="{x:Type syncfusion:GroupView}">
        <ScrollViewer
            Name="MainHost"
            Background="{TemplateBinding Background}"
            Focusable="False"
            Foreground="{TemplateBinding Foreground}"
            Style="{StaticResource WPFScrollViewerStyle}"
            VerticalScrollBarVisibility="Auto">
            <Border Name="ItemsHost">
                <ItemsPresenter ClipToBounds="True" />
            </Border>
        </ScrollViewer>
        <ControlTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=(syncfusion:GroupBarItem.IsAnimating), RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupBarItem}}}" Value="True">
                <Setter TargetName="MainHost" Property="VerticalScrollBarVisibility" Value="Hidden" />
            </DataTrigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="MainHost" Property="Background" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SyncfusionGroupViewStackModeControlTemplate" TargetType="{x:Type syncfusion:GroupView}">
        <Border Name="ItemsHost" Padding="0,0,0,0">
            <ItemsPresenter ClipToBounds="True" />
        </Border>
    </ControlTemplate>

    <Style x:Key="SyncfusionGroupViewStyle" TargetType="{x:Type syncfusion:GroupView}">
        <Setter Property="KeyboardNavigation.IsTabStop" Value="False" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="VerticalContentAlignment" Value="Stretch" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <WrapPanel
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch"
                        KeyboardNavigation.ControlTabNavigation="None"
                        KeyboardNavigation.DirectionalNavigation="Continue"
                        KeyboardNavigation.TabNavigation="Cycle"
                        Orientation="{Binding Path=Orientation, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupView}}}" />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>

        <Style.Triggers>
            <Trigger Property="componentModel:DesignerProperties.IsInDesignMode" Value="False">
                <Setter Property="ContextMenu" Value="{StaticResource SyncfusionGroupBarContextMenu}" />
            </Trigger>

            <Trigger Property="syncfusion:GroupBar.VisualMode" Value="Default">
                <Setter Property="Template" Value="{StaticResource SyncfusionGroupViewDefaultControlTemplate}" />
            </Trigger>

            <Trigger Property="syncfusion:GroupBar.VisualMode" Value="StackMode">
                <Setter Property="Template" Value="{StaticResource SyncfusionGroupViewStackModeControlTemplate}" />
            </Trigger>

            <Trigger Property="syncfusion:GroupBar.VisualMode" Value="MultipleExpansion">
                <Setter Property="Template" Value="{StaticResource SyncfusionGroupViewMultipleExpansionControlTemplate}" />
            </Trigger>

            <Trigger Property="syncfusion:GroupBar.IsEnabledContextMenu" Value="False">
                <Setter Property="ContextMenuService.IsEnabled" Value="False" />
            </Trigger>
            <Trigger Property="syncfusion:GroupView.IsListViewMode" Value="False">
                <Setter Property="IsShowText" Value="False" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionGroupViewStyle}" TargetType="{x:Type syncfusion:GroupView}" />

    <!--  GroupViewIem Template  -->

    <ControlTemplate x:Key="SyncfusionGroupViewItemControlTemplate" TargetType="{x:Type syncfusion:GroupViewItem}">
        <Border
            Name="MainGrid"
            Background="{TemplateBinding Background}"
            BorderBrush="Transparent"
            BorderThickness="{TemplateBinding BorderThickness}"
            ClipToBounds="True"
            Cursor="{Binding Path=GroupViewItemCursor, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupView}}}"
            SnapsToDevicePixels="True"
            CornerRadius="4">
            <Grid>
                <Grid.Resources>
                    <syncfusion:ContentToVisibilityConverter x:Key="ContentToVisibility" />
                </Grid.Resources>
                <Border x:Name="SelectionIndicator"
                        HorizontalAlignment="Left"
                        CornerRadius="1.5"
                        Height="12"
                        Width="2"
                        Visibility="Collapsed"
                        Margin="1,0,0,0"
                        Background="{StaticResource PrimaryBackground}" />
                <DockPanel
                    Name="ContentHost"
                    Width="{Binding Path=InternalContentLength, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupView}}}"
                    Margin="0"
                    HorizontalAlignment="{Binding Path=HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupView}}}"
                    Visibility="{Binding Path=Content, ElementName=contentpresenter, Converter={StaticResource ContentToVisibility}}">
                    <Border
                        Name="ImageHost"
                        Width="{TemplateBinding ImageWidth}"
                        Height="{TemplateBinding ImageHeight}"
                        Margin="8,0,0,0"
                        HorizontalAlignment="Left"
                        Background="Transparent"
                        DockPanel.Dock="Left">
                        <Image
                            Name="Image"
                            Source="{TemplateBinding ImageSource}"
                            Stretch="Fill" />
                    </Border>
                    <Border
                        Name="TextHost"
                        Padding="8,0,8,0"
                        HorizontalAlignment="{Binding Path=TextAlignment, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupView}}}"
                        DockPanel.Dock="Top"
                        TextBlock.FontFamily="{TemplateBinding FontFamily}"
                        TextBlock.FontSize="{TemplateBinding FontSize}"
                        TextBlock.FontWeight="{TemplateBinding FontWeight}"
                        TextBlock.Foreground="{TemplateBinding Foreground}">
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock
                                Name="Text"
                                Text="{Binding Path=Text, RelativeSource={RelativeSource TemplatedParent}}"
                                TextTrimming="CharacterEllipsis"
                                Visibility="Visible" />
                            <TextBox
                                Name="TextEditor"
                                Background="{TemplateBinding Background}"
                                Text="{Binding Path=Text, RelativeSource={RelativeSource TemplatedParent}}"
                                Visibility="Collapsed" />
                        </StackPanel>
                    </Border>
                </DockPanel>
                <Border>
                    <DockPanel>
                        <ContentControl
                            x:Name="contentpresenter"
                            Width="{Binding Path=InternalContentLength, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupView}}}"
                            HorizontalContentAlignment="{Binding Path=HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupView}}}"
                            Content="{TemplateBinding Content}"
                            ContentTemplate="{Binding Path=ItemTemplate, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupView}}}"
                            ContentTemplateSelector="{Binding Path=ItemTemplateSelector, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupView}}}"
                            FocusVisualStyle="{x:Null}" />
                    </DockPanel>
                </Border>
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=IsShowText, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupView}}}" Value="False">
                <Setter TargetName="TextHost" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <Trigger Property="IsInEditMode" Value="True">
                <Setter TargetName="TextEditor" Property="Visibility" Value="Visible" />
                <Setter TargetName="Text" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="TextHost" Property="HorizontalAlignment" Value="Stretch" />
                <Setter TargetName="TextHost" Property="Visibility" Value="Visible" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition SourceName="MainGrid" Property="IsMouseOver" Value="True" />
                    <Condition Property="IsSelected" Value="False" />
                </MultiTrigger.Conditions>
                <Setter TargetName="MainGrid" Property="Background" Value="{Binding Path=MouseHoverColor, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupViewItem}}}" />
                <Setter TargetName="MainGrid" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter TargetName="Text" Property="Foreground" Value="{StaticResource HoveredForeground}" />
            </MultiTrigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter TargetName="MainGrid" Property="Background" Value="{Binding Path=MouseDownColor, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupViewItem}}}" />
                <Setter TargetName="MainGrid" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter TargetName="Text" Property="Foreground" Value="{StaticResource HoveredForeground}" />
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter TargetName="MainGrid" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="MainGrid" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter TargetName="Text" Property="Foreground" Value="{StaticResource HoveredForeground}" />
                <Setter Property="Visibility" TargetName="SelectionIndicator" Value="Visible"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter TargetName="MainGrid" Property="Background" Value="{Binding Path=SelectedItemColor, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupViewItem}}}" />
                <Setter TargetName="MainGrid" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter TargetName="Text" Property="Foreground" Value="{StaticResource SelectedForeground}" /> 
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition SourceName="MainGrid" Property="IsMouseOver" Value="True" />
                    <Condition Property="IsSelected" Value="True" />
                </MultiTrigger.Conditions>
                <Setter TargetName="MainGrid" Property="Background" Value="{Binding Path=SelectedItemMouseHoverColor, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupViewItem}}}" />
                <Setter TargetName="MainGrid" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                <Setter TargetName="Text" Property="Foreground" Value="{StaticResource SelectedForeground}" />
            </MultiTrigger>
            <Trigger Property="IsDragging" Value="True">
                <Setter TargetName="MainGrid" Property="Background" Value="{StaticResource TooltipBackground}" />
                <Setter TargetName="MainGrid" Property="BorderBrush" Value="{StaticResource TooltipBorder}" />
                <Setter TargetName="Text" Property="Foreground" Value="{StaticResource TooltipForeground}" />
                <Setter TargetName="MainGrid" Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}"/>
            </Trigger>
            <DataTrigger Binding="{Binding Path=(syncfusion:GroupView.IsListViewMode), RelativeSource={RelativeSource AncestorType={x:Type syncfusion:GroupView}}}" Value="False">
                <Setter TargetName="ContentHost" Property="Width" Value="{Binding Path=Width}" />
            </DataTrigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="MainGrid" Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                <Setter TargetName="MainGrid" Property="BorderBrush" Value="{StaticResource ContentBackgroundDisabled}" />
                <Setter TargetName="Text" Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SyncfusionGroupViewItemStyle" TargetType="{x:Type syncfusion:GroupViewItem}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="SelectedItemColor" Value="{StaticResource ContentBackgroundSelected}" />
        <Setter Property="MouseDownColor" Value="{StaticResource ContentBackgroundHovered}" />
        <Setter Property="SelectedItemMouseHoverColor" Value="{StaticResource ContentBackgroundHovered}" />
        <Setter Property="Margin">
            <Setter.Value>
                <Thickness>5,2,5,2</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}" />
        <Setter Property="Template" Value="{StaticResource SyncfusionGroupViewItemControlTemplate}" />
        <Setter Property="MinHeight" Value="24" />
        <Setter Property="ToolTipService.IsEnabled" Value="True" />
        <Setter Property="ToolTipService.Placement" Value="Mouse" />

        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="componentModel:DesignerProperties.IsInDesignMode" Value="False">
                <Setter Property="MouseHoverColor" Value="{StaticResource ContentBackgroundHovered}" />
            </Trigger>

            <Trigger Property="syncfusion:GroupBar.IsEnabledContextMenu" Value="False">
                <Setter Property="ContextMenuService.IsEnabled" Value="False" />
            </Trigger>
            <Trigger Property="ShowToolTip" Value="False">
                <Setter Property="ToolTipService.IsEnabled" Value="False" />
            </Trigger>
        </Style.Triggers>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionGroupViewItemStyle}" TargetType="{x:Type syncfusion:GroupViewItem}" />
</ResourceDictionary>
