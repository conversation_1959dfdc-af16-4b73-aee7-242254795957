<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:Syncfusion.Windows.Controls.Navigation;assembly=Syncfusion.SfRadialMenu.WPF"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ToolTip.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <BooleanToVisibilityConverter x:Key="VisibilityConverter" />
    <local:IconConverter x:Key="IconConverter" />
    <local:MenuItemIconConverter x:Key="MenuItemIconConverter" />
    <local:StrokeThicknessToMarginConverter x:Key="StrokeThicknessToMarginConverter" />
	<local:RadialMenuRadiusToWidthConverter x:Key="RadiusConverter"/>
    <local:RadialMenuBrushOpacityModifier x:Key="BrushModifier" />
    <local:RadialMenuColorToBrushConverter x:Key="ColorToBrushConverter" />

    <Style x:Key="SyncfusionSfRadialMenuSfRadialColorItemStyle" TargetType="local:SfRadialColorItem">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Color" Value="{StaticResource RadialColorItem.Static.Background}" />
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfRadialMenuSfRadialColorItemStyle}" TargetType="local:SfRadialColorItem" />

    <Style x:Key="SyncfusionSfRadialMenuPointerOverOuterRimItemStyle" TargetType="local:OuterRimItem">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="IsCheckable" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:OuterRimItem">
                    <Grid Margin="0,0,-1366,-1366">
                        <Path
                            x:Name="PART_PointerOverRim"
                            Opacity="1"
                            Stroke="{Binding Path=RimBackground, RelativeSource={RelativeSource TemplatedParent}}"
                            StrokeThickness="{StaticResource Windows11Light.StrokeThickness2}"
                            Visibility="Collapsed">
                            <Path.Data>
                                <PathGeometry>
                                    <PathFigure
                                        x:Name="PointerOverRim_Path"
                                        IsClosed="False"
                                        IsFilled="False"
                                        StartPoint="{Binding StartPoint, RelativeSource={RelativeSource TemplatedParent}}">
                                        <ArcSegment
                                            Point="{Binding RimPoint, RelativeSource={RelativeSource TemplatedParent}}"
                                            RotationAngle="0"
                                            Size="{Binding RimSize, RelativeSource={RelativeSource TemplatedParent}}"
                                            SweepDirection="Clockwise" />
                                    </PathFigure>
                                </PathGeometry>
                            </Path.Data>
                        </Path>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="PointerOver">

                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_PointerOverRim" Storyboard.TargetProperty="Visibility">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>

                                </VisualState>

                                <VisualState x:Name="Checked">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_PointerOverRim" Storyboard.TargetProperty="Visibility">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>

                                <VisualState x:Name="UnChecked">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_PointerOverRim" Storyboard.TargetProperty="Visibility">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>

                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfRadialMenuSliceOuterRimItemStyle" TargetType="local:OuterRimItem">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:OuterRimItem">
                    <Grid
                        x:Name="PART_BackRimGrid"
                        Width="{Binding ElementName=PART_BackRim, Path=ActualWidth}"
                        Height="{Binding ElementName=PART_BackRim, Path=ActualHeight}"
                        Margin="{Binding MouseOverRimStrokeThickness, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource StrokeThicknessToMarginConverter}, ConverterParameter=0}">
                        <Path
                            x:Name="PART_BackRim"
                            Stroke="{StaticResource ContentBackgroundHovered}"
                            StrokeThickness="{Binding MouseOverRimStrokeThickness, RelativeSource={RelativeSource TemplatedParent}}"
                            Visibility="Collapsed">
                            <Path.Data>
                                <PathGeometry>
                                    <PathFigure
                                        x:Name="SliceRim_Path"
                                        IsClosed="False"
                                        IsFilled="False"
                                        StartPoint="{Binding StartPoint, RelativeSource={RelativeSource TemplatedParent}}">
                                        <ArcSegment
                                            Point="{Binding RimPoint, RelativeSource={RelativeSource TemplatedParent}}"
                                            RotationAngle="0"
                                            Size="{Binding RimSize, RelativeSource={RelativeSource TemplatedParent}}"
                                            SweepDirection="Clockwise" />
                                    </PathFigure>
                                </PathGeometry>
                            </Path.Data>
                        </Path>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="PointerOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PART_BackRim" Storyboard.TargetProperty="Visibility">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfRadialMenuDefaultOuterRimItemStyle" TargetType="local:OuterRimItem">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:OuterRimItem">
                    <Grid
                        x:Name="PART_BackRimGrid"
                        Width="{Binding ElementName=PART_BackRim, Path=ActualWidth}"
                        Height="{Binding ElementName=PART_BackRim, Path=ActualHeight}"
                        Margin="0,0,-1366,-1366">
                        <Path
                            x:Name="PART_BackRim"
                            Stroke="{StaticResource ContentBackground}"
                            StrokeThickness="{Binding MouseOverRimStrokeThickness, RelativeSource={RelativeSource TemplatedParent}}"
                            Visibility="Visible">
                            <Path.Data>
                                <PathGeometry>
                                    <PathFigure
                                        x:Name="DefaultRim_Path"
                                        IsClosed="False"
                                        IsFilled="False"
                                        StartPoint="{Binding StartPoint, RelativeSource={RelativeSource TemplatedParent}}">
                                        <ArcSegment
                                            Point="{Binding RimPoint, RelativeSource={RelativeSource TemplatedParent}}"
                                            RotationAngle="0"
                                            Size="{Binding RimSize, RelativeSource={RelativeSource TemplatedParent}}"
                                            SweepDirection="Clockwise" />
                                    </PathFigure>
                                </PathGeometry>
                            </Path.Data>
                        </Path>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="PointerOver" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True" />
                                <Condition Property="IsColorItem" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_BackRim" Property="Stroke" Value="{StaticResource ContentBackgroundHovered}" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>

            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfRadialMenuOuterRimItemExpanderArrowStyle" TargetType="local:OuterRimItem">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:OuterRimItem">
                    <Grid
                        Width="{Binding ElementName=PART_rim, Path=ActualWidth}"
                        Height="{Binding ElementName=PART_rim, Path=ActualHeight}"
                        Margin="0,0,-50,-50">
                        <Path
                            x:Name="PART_rim"
                            Stroke="{Binding RimActiveBrush, RelativeSource={RelativeSource TemplatedParent}}"
                            StrokeThickness="{Binding StrokeThickness, RelativeSource={RelativeSource TemplatedParent}}">
                            <Path.Data>
                                <PathGeometry>
                                    <PathFigure
                                        x:Name="ExpanderRim_Path"
                                        IsClosed="False"
                                        IsFilled="False"
                                        StartPoint="{Binding StartPoint, RelativeSource={RelativeSource TemplatedParent}}">
                                        <ArcSegment
                                            Point="{Binding RimPoint, RelativeSource={RelativeSource TemplatedParent}}"
                                            RotationAngle="0"
                                            Size="{Binding RimSize, RelativeSource={RelativeSource TemplatedParent}}"
                                            SweepDirection="Clockwise" />
                                    </PathFigure>
                                </PathGeometry>
                            </Path.Data>
                        </Path>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_rim" Storyboard.TargetProperty="Opacity">
                                            <DiscreteDoubleKeyFrame KeyTime="0" Value="0.3" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsColorItem" Value="True" />
                                <Condition Property="IsMouseOver" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_rim" Property="Opacity" Value="0.8" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsColorItem" Value="False" />
                                <Condition Property="IsMouseOver" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_rim" Property="Stroke" Value="{StaticResource PrimaryColorLight1}" />
                        </MultiTrigger>
                        <Trigger Property="HasItems" Value="False">
                            <Setter TargetName="PART_rim" Property="Stroke" Value="{StaticResource PrimaryBackgroundOpacity3}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsColorItem" Value="False" />
                                <Condition Property="IsMouseOver" Value="True" />
                                <Condition Property="HasItems" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_rim" Property="Stroke" Value="{StaticResource PrimaryBackgroundOpacity3}" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfRadialMenuExpanderArrowOuterRimItemStyle" TargetType="local:OuterRimItem">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:OuterRimItem">
                    <Grid IsHitTestVisible="False">
                        <Path
                            x:Name="PART_Arrow"
                            Width="7"
                            Height="12"
                            Margin="-5"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Fill="{StaticResource PrimaryForeground}"
                            RenderTransformOrigin="0.5, 0.5"
                            Stretch="Fill"
                            Stroke="{StaticResource PrimaryForeground}"
                            Visibility="{Binding Path=HasItems, Converter={StaticResource VisibilityConverter}, Mode=TwoWay}">
                            <Path.RenderTransform>
                                <RotateTransform x:Name="PART_RotateTransform" Angle="{Binding Angle, RelativeSource={RelativeSource TemplatedParent}}" />
                            </Path.RenderTransform>
                            <Path.Data>
                                <PathGeometry>M640,160 L640,200 L650,180 z</PathGeometry>
                            </Path.Data>
                        </Path>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_Arrow" Storyboard.TargetProperty="Opacity">
                                            <DiscreteDoubleKeyFrame KeyTime="0" Value="0.3" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="HasItems" Value="False">
                            <Setter TargetName="PART_Arrow" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="HasItems" Value="True" />
                                <Condition Property="IsExpanderVisible" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Arrow" Property="Visibility" Value="Visible" />
                        </MultiTrigger>
                        <Trigger Property="IsExpanderVisible" Value="False">
                            <Setter TargetName="PART_Arrow" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="PART_Arrow" Property="Shape.Fill" Value="Red" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfRadialMenuOuterRimStyle" TargetType="local:OuterRim">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <local:OuterRimPanel />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:OuterRim">
                    <ItemsPresenter />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfRadialMenuOuterRimStyle}" TargetType="local:OuterRim" />

    <Style x:Key="SyncfusionSfRadialMenuItemStyle" TargetType="local:SfRadialMenuItem">
        <Setter Property="RimActiveBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="RimInactiveBrush" Value="{StaticResource PrimaryBackgroundOpacity3}" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="Foreground" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="MenuBackgroundColor" Value="{StaticResource ContentBackground}" />
        <Setter Property="MenuMouseOverBackgroundColor" Value="{StaticResource ContentBackgroundHovered}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:SfRadialMenuItem">
                    <Grid Background="{TemplateBinding Background}">
                        <Border Padding="5">
                            <Grid>

                                <ContentPresenter
                                    Margin="0,-10,0,0"
                                    VerticalAlignment="Top"
                                    Content="{Binding Converter={StaticResource MenuItemIconConverter}, RelativeSource={RelativeSource TemplatedParent}}" />
                                <ContentPresenter
                                    Name="PART_MenuItem"
                                    Grid.Row="1"
                                    Content="{TemplateBinding Header}"
                                    ContentTemplate="{TemplateBinding HeaderTemplate}"
                                    ContentTemplateSelector="{TemplateBinding HeaderTemplateSelector}" />
                            </Grid>
                        </Border>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PART_MenuItem" Storyboard.TargetProperty="Opacity">
                                            <DiscreteDoubleKeyFrame KeyTime="0" Value="0.3" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfRadialMenuItemStyle}" TargetType="local:SfRadialMenuItem" />

    <Style x:Key="NavigationButtonStyle" TargetType="Button">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="FontFamily" Value="Segoe UI Symbol" />
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid Margin="-5" Background="Transparent">
                        <Ellipse x:Name="BackgroundEllipse" Fill="{StaticResource ContentBackground}" />
                        <Ellipse
                            x:Name="OuterEllipse"
                            Fill="Transparent"
                            Stroke="{TemplateBinding BorderBrush}"
                            StrokeThickness="{StaticResource Windows11Light.StrokeThickness2}" />
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="PointerOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="BackgroundEllipse" Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorLight3}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse" Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource PrimaryColorDark1}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="OuterEllipse" Property="Opacity" Value="0.3" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfRadialMenuStyle" TargetType="local:SfRadialMenu">
        <Setter Property="CenterBackButtonForeground" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="SeparatorBackground" Value="{StaticResource ContentBackground}" />
        <Setter Property="RimBackground" Value="{StaticResource PrimaryBackgroundOpacity3}" />
        <Setter Property="NavigationButtonStyle" Value="{StaticResource NavigationButtonStyle}" />
        <Setter Property="RimActiveBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="RimHoverBrush" Value="{StaticResource PrimaryColorLight1}" />
        <Setter Property="MouseOverRimStyle" Value="{StaticResource SyncfusionSfRadialMenuSliceOuterRimItemStyle}" />
        <Setter Property="RadiusX" Value="130" />
        <Setter Property="RadiusY" Value="130" />
        <Setter Property="Padding" Value="5" />
        <!--<Setter Property="ManipulationMode" Value="All"/>-->
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:SfRadialMenu">
                    <Grid
                        Width="{Binding Path=RadiusX, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource RadiusConverter}}"
                        Height="{Binding Path=RadiusY, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource RadiusConverter}}"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Background="Transparent">

                        <Popup x:Name="PART_ToolTipPopup">
                            <Border
                                MinWidth="70"
                                MinHeight="35"
                                Background="White"
                                BorderBrush="Black"
                                BorderThickness="2">
                                <Grid HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <ContentPresenter x:Name="PART_ToolTipContent" />
                                </Grid>
                            </Border>
                        </Popup>

                        <Grid x:Name="PART_Root">

                            <Grid
                                x:Name="PART_Rim"
                                Background="Transparent"
                                Visibility="Collapsed">

                                <Ellipse Fill="Transparent" />

                                <Ellipse Fill="{Binding RimBackground, RelativeSource={RelativeSource TemplatedParent}}" />

                                <local:OuterRim
                                    x:Name="PART_ExpanderRim"
                                    IsTabStop="False"
                                    Margin="{Binding Path=StrokeThickness, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource StrokeThicknessToMarginConverter}}"
                                    ItemContainerStyle="{StaticResource SyncfusionSfRadialMenuOuterRimItemExpanderArrowStyle}"
                                    ItemsSource="{Binding DrillDownItem.Items, RelativeSource={RelativeSource TemplatedParent}}" />

                                <local:OuterRim
                                    x:Name="PART_ExpanderArrowRim"
                                    IsTabStop="False"
                                    Margin="{Binding Path=StrokeThickness, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource StrokeThicknessToMarginConverter}}"
                                    ItemContainerStyle="{StaticResource SyncfusionSfRadialMenuExpanderArrowOuterRimItemStyle}"
                                    ItemsSource="{Binding DrillDownItem.Items, RelativeSource={RelativeSource TemplatedParent}}">
                                    <local:OuterRim.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <local:CircularPanel Margin="-4" />
                                        </ItemsPanelTemplate>
                                    </local:OuterRim.ItemsPanel>
                                </local:OuterRim>
                            </Grid>

                            <Grid
                                x:Name="PART_Items"
                                Background="{x:Null}"
                                Visibility="Collapsed">

                                <Ellipse Fill="{Binding Background, RelativeSource={RelativeSource TemplatedParent}}" />

                                <Ellipse Fill="{StaticResource ContentBackground}" />

                                <local:OuterRim
                                    x:Name="PART_DefaultRim"
                                    IsTabStop="False"
                                    Margin="{Binding MouseOverRimStrokeThickness, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource StrokeThicknessToMarginConverter}}"
                                    ItemContainerStyle="{StaticResource SyncfusionSfRadialMenuDefaultOuterRimItemStyle}"
                                    ItemsSource="{Binding DrillDownItem.Items, RelativeSource={RelativeSource TemplatedParent}}" />

                                <local:OuterRim
                                    x:Name="PART_SliceRim"
                                    IsTabStop="False"
                                    Margin="{Binding MouseOverRimStrokeThickness, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource StrokeThicknessToMarginConverter}}"
                                    ItemContainerStyle="{Binding MouseOverRimStyle, RelativeSource={RelativeSource TemplatedParent}}"
                                    ItemsSource="{Binding DrillDownItem.Items, RelativeSource={RelativeSource TemplatedParent}}" />
                                <local:OuterRim
                                    x:Name="PART_SelectionRim"
                                    IsTabStop="False"
                                    Margin="5"
                                    ItemContainerStyle="{StaticResource SyncfusionSfRadialMenuPointerOverOuterRimItemStyle}"
                                    ItemsSource="{Binding DrillDownItem.Items, RelativeSource={RelativeSource TemplatedParent}}" />

                                <Grid Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}}">

                                    <local:InnerRim
                                        x:Name="PART_InnerColorItemRim"
                                        IsTabStop="False"
                                        ItemContainerStyle="{TemplateBinding ItemContainerStyle}"
                                        ItemTemplate="{TemplateBinding ItemTemplate}"
                                        ItemsPanel="{TemplateBinding ItemsPanel}"
                                        Visibility="Collapsed" />

                                    <local:InnerRim
                                        x:Name="PART_InnerRim"
                                        IsTabStop="False"
                                        ItemContainerStyle="{TemplateBinding ItemContainerStyle}"
                                        ItemTemplate="{TemplateBinding ItemTemplate}"
                                        ItemsPanel="{TemplateBinding ItemsPanel}"
                                        ItemsSource="{Binding DrillDownItem.Items, RelativeSource={RelativeSource TemplatedParent}}" />
                                </Grid>
                            </Grid>

                        </Grid>

                        <Button
                            x:Name="PART_NavigationButton"
                            IsTabStop="False"
                            Width="1"
                            Height="1"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Content="{Binding DrillDownItem, Converter={StaticResource IconConverter}, RelativeSource={RelativeSource TemplatedParent}}"
                            Foreground="{StaticResource PrimaryBackground}"
                            Style="{TemplateBinding NavigationButtonStyle}" />
                        
                        <Path x:Name="backButtonPath" 
                              Visibility="Collapsed" 
                              Data="M5.9351995,1.7166138E-05 C6.191056,-0.0014829636 6.4475377,0.094773293 6.6440237,0.28928566 6.8420093,0.48429823 6.9410022,0.74231482 6.9410022,1.0003314 6.9410022,1.2543476 6.8450091,1.508364 6.6520231,1.7033765 L3.3947313,5.0000117 15.000005,5.0000117 C15.551995,5.0000117 15.999982,5.4470137 15.999982,5.9999681 15.999982,6.5529836 15.551995,6.9999245 15.000005,6.9999247 L3.3942476,6.9999247 6.6531771,10.296778 C7.041169,10.689766 7.0361641,11.322734 6.6451815,11.710718 6.2511781,12.099739 5.6181968,12.095711 5.2302049,11.702723 L0.30932209,6.723164 0.29647896,6.711699 C0.12499117,6.5423756 0.027763774,6.3257717 0.005131483,6.1027555 L0.0021214279,6.0465301 0,6.0069036 0.0012931195,5.9759599 0.0012681484,5.9738314 0.0016320664,5.9678491 0.0040085315,5.9109814 C0.0068687879,5.8790751 0.011253239,5.8472761 0.017163038,5.8157039 L0.025305054,5.7814223 0.030626058,5.7560618 0.034627981,5.7421685 0.039469003,5.7217855 0.045940916,5.7028938 0.051231145,5.6845279 0.064685996,5.6481761 0.070932622,5.6299419 0.073595236,5.6241055 0.077336069,5.6139987 C0.087086182,5.5907491 0.097648721,5.5680073 0.10898232,5.5458007 L0.11082071,5.5425071 0.11155963,5.5408874 0.11558152,5.5339777 0.14525579,5.4808138 0.15603949,5.4644702 0.16135572,5.4553368 0.16924511,5.444456 0.18590831,5.4192016 0.21347794,5.3834515 0.22032713,5.3740053 0.22354037,5.3704034 0.23069285,5.3611286 C0.24628233,5.342379 0.26251909,5.3242466 0.27936194,5.3067587 L0.28624425,5.3001137 0.28847954,5.2976079 5.2301252,0.29728603 C5.4241112,0.10077333 5.6793425,0.001516819 5.9351995,1.7166138E-05 z" 
                              Fill="Black" 
                              HorizontalAlignment="Left" 
                              Height="12" 
                              Margin="338.409,0,0,556.595" 
                              Stretch="Fill" 
                              VerticalAlignment="Bottom" 
                              Width="16"/>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <local:CircularPanel />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfRadialMenuStyle}" TargetType="local:SfRadialMenu" />

</ResourceDictionary>
