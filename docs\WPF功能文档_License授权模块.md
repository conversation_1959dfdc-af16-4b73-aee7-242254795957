# WPF功能文档 - License授权模块

## 文档信息
- **模块名称**: License授权模块
- **文档版本**: 1.0
- **创建日期**: 2024-12-09
- **最后更新**: 2024-12-09
- **负责人**: AI Assistant

## 1. 模块概述

### 1.1 功能描述
License授权模块是商用空调监控调试软件的核心安全组件，专为公司内部使用场景设计，负责软件的授权验证和功能控制。该模块采用基于license文件的本地授权验证机制，无需网络连接，确保软件在离线环境下的正常运行。

### 1.2 核心特性
- **离线授权**: 基于本地license文件，无需网络连接
- **硬件绑定**: 通过CPU和主板序列号进行硬件绑定
- **功能控制**: 支持按功能模块进行精细化授权控制
- **内部使用**: 专为公司内部使用设计，无试用版概念
- **设备无限制**: 授权与监控设备数量无关联，支持总线上所有设备
- **安全加密**: 采用RSA+AES混合加密确保license文件安全
- **数字签名**: 防止license文件被篡改

### 1.3 技术架构
```
┌─────────────────────────────────────────────────────────────┐
│                    License授权模块                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ License     │  │ Hardware    │  │ Crypto      │         │
│  │ Validator   │  │ Fingerprint │  │ Service     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ License     │  │ Feature     │  │ Trial       │         │
│  │ Manager     │  │ Controller  │  │ Manager     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 2. 授权机制设计

### 2.1 License文件结构
License文件采用JSON格式，包含以下信息：
```json
{
  "licenseId": "LIC-2024-001-XXXXXX",
  "productName": "AirMonitor",
  "productVersion": "1.0.0",
  "customerName": "技术部门",
  "customerEmail": "<EMAIL>",
  "licenseType": "Internal",
  "authorizedFeatures": [
    "SerialCommunication",
    "DataCollection",
    "RealTimeMonitoring",
    "HistoryData",
    "AlarmManagement"
  ],
  "hardwareFingerprint": "CPU-XXXX-MB-YYYY",
  "issuedDate": "2024-12-09T00:00:00Z",
  "expiryDate": "2025-12-09T23:59:59Z",
  "maxDeviceCount": -1,
  "isTrial": false,
  "trialDays": 0,
  "signature": "RSA签名数据"
}
```

### 2.2 硬件指纹生成
硬件指纹由以下组件组成：
- **CPU序列号**: 处理器唯一标识
- **主板序列号**: 主板唯一标识
- **MAC地址**: 网卡物理地址（备用）

生成算法：
```csharp
string fingerprint = $"CPU-{cpuId}-MB-{motherboardId}";
string hash = SHA256.ComputeHash(fingerprint);
return hash.Substring(0, 16); // 取前16位作为指纹
```

### 2.3 加密算法
采用RSA+AES混合加密方案：
1. **AES-256**: 加密license内容
2. **RSA-2048**: 加密AES密钥
3. **SHA-256**: 生成数字签名

## 3. 功能模块定义

### 3.1 可授权功能列表
| 功能代码 | 功能名称 | 描述 |
|---------|---------|------|
| SerialCommunication | 串口通信 | RS485串口通信功能 |
| DataCollection | 数据采集 | 实时数据采集功能 |
| RealTimeMonitoring | 实时监控 | 实时数据显示和监控 |
| HistoryData | 历史数据 | 历史数据查询和分析 |
| DataPlayback | 数据回放 | 历史数据回放功能 |
| AlarmManagement | 报警管理 | 故障报警和管理功能 |
| DataExport | 数据导出 | 数据导出功能 |
| AdvancedAnalysis | 高级分析 | 高级数据分析功能 |
| MultiDevice | 多设备支持 | 支持多设备同时监控 |
| CustomProtocol | 自定义协议 | 自定义协议解析功能 |

### 3.2 许可证类型（内部使用）
| 类型 | 名称 | 包含功能 | 设备数量 | 有效期 | 适用场景 |
|------|------|----------|----------|--------|----------|
| Standard | 普通版 | 基础监控功能 | 无限制 | 1年 | 一般用户使用 |
| AfterSales | 售后版 | 售后服务专用功能 | 无限制 | 1年 | 售后服务部门 |
| Development | 研发版 | 所有功能 | 无限制 | 永久 | 研发部门专用 |
| Management | 管理版 | 所有功能+管理权限 | 无限制 | 永久 | 管理层专用 |

### 3.3 功能权限分配
| 功能模块 | 普通版 | 售后版 | 研发版 | 管理版 |
|---------|--------|--------|--------|--------|
| 串口通信 | ✓ | ✓ | ✓ | ✓ |
| 数据采集 | ✓ | ✓ | ✓ | ✓ |
| 实时监控 | ✓ | ✓ | ✓ | ✓ |
| 历史数据查询 | ✓ | ✓ | ✓ | ✓ |
| 数据导出 | ✗ | ✓ | ✓ | ✓ |
| 高级分析 | ✗ | ✓ | ✓ | ✓ |
| 数据回放 | ✗ | ✓ | ✓ | ✓ |
| 报警管理 | ✓ | ✓ | ✓ | ✓ |
| 自定义协议 | ✗ | ✗ | ✓ | ✓ |
| 系统配置 | ✗ | ✗ | ✓ | ✓ |
| 用户管理 | ✗ | ✗ | ✗ | ✓ |
| 许可证管理 | ✗ | ✗ | ✗ | ✓ |

## 4. 实现细节

### 4.1 LicenseService实现
```csharp
public class LicenseService : ILicenseService
{
    private LicenseInfo? _currentLicense;
    private readonly ICryptoService _cryptoService;
    private readonly IHardwareFingerprintService _hardwareService;

    public bool IsLicenseValid => _currentLicense?.IsValid() ?? false;
    public bool IsTrialVersion => _currentLicense?.IsTrial ?? false;

    public async Task<LicenseValidationResult> LoadLicenseAsync(string filePath)
    {
        try
        {
            // 1. 读取license文件
            var encryptedContent = await File.ReadAllTextAsync(filePath);
            
            // 2. 解密license内容
            var licenseJson = await _cryptoService.DecryptAsync(encryptedContent);
            
            // 3. 反序列化license对象
            var license = JsonSerializer.Deserialize<LicenseInfo>(licenseJson);
            
            // 4. 验证license
            return await ValidateLicenseAsync(license);
        }
        catch (Exception ex)
        {
            return LicenseValidationResult.Failure(
                LicenseErrorCode.Unknown, ex.Message);
        }
    }

    public async Task<LicenseValidationResult> ValidateLicenseAsync(LicenseInfo license)
    {
        // 1. 验证数字签名
        if (!await _cryptoService.VerifySignatureAsync(license))
        {
            return LicenseValidationResult.Failure(
                LicenseErrorCode.InvalidSignature, "数字签名验证失败");
        }

        // 2. 验证硬件指纹
        var currentFingerprint = _hardwareService.GetFingerprint();
        if (license.HardwareFingerprint != currentFingerprint)
        {
            return LicenseValidationResult.Failure(
                LicenseErrorCode.HardwareMismatch, "硬件指纹不匹配");
        }

        // 3. 验证有效期
        if (!license.IsValid())
        {
            var errorCode = license.IsTrial ? 
                LicenseErrorCode.TrialExpired : LicenseErrorCode.Expired;
            return LicenseValidationResult.Failure(errorCode, "许可证已过期");
        }

        _currentLicense = license;
        return LicenseValidationResult.Success(license);
    }
}
```

### 4.2 硬件指纹服务
```csharp
public class HardwareFingerprintService : IHardwareFingerprintService
{
    public string GetFingerprint()
    {
        var cpuId = GetCpuId();
        var motherboardId = GetMotherboardId();
        var fingerprint = $"CPU-{cpuId}-MB-{motherboardId}";
        
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(fingerprint));
        return Convert.ToHexString(hash)[..16];
    }

    private string GetCpuId()
    {
        using var searcher = new ManagementObjectSearcher(
            "SELECT ProcessorId FROM Win32_Processor");
        
        foreach (ManagementObject obj in searcher.Get())
        {
            return obj["ProcessorId"]?.ToString() ?? "UNKNOWN";
        }
        return "UNKNOWN";
    }

    private string GetMotherboardId()
    {
        using var searcher = new ManagementObjectSearcher(
            "SELECT SerialNumber FROM Win32_BaseBoard");
        
        foreach (ManagementObject obj in searcher.Get())
        {
            return obj["SerialNumber"]?.ToString() ?? "UNKNOWN";
        }
        return "UNKNOWN";
    }
}
```

### 4.3 功能控制器
```csharp
public class FeatureController : IFeatureController
{
    private readonly ILicenseService _licenseService;

    public bool IsFeatureEnabled(string featureCode)
    {
        if (!_licenseService.IsLicenseValid)
            return false;

        return _licenseService.IsFeatureAuthorized(featureCode);
    }

    public void EnforceFeature(string featureCode)
    {
        if (!IsFeatureEnabled(featureCode))
        {
            throw new UnauthorizedAccessException(
                $"功能 '{featureCode}' 未授权，请检查许可证");
        }
    }

    public List<string> GetAuthorizedFeatures()
    {
        return _licenseService.CurrentLicense?.AuthorizedFeatures ?? new();
    }
}
```

## 5. 用户界面设计

### 5.1 许可证状态显示
在主窗口状态栏显示许可证信息：
- **许可证类型**: 普通版/售后版/研发版/管理版
- **剩余天数**: 显示剩余有效天数（永久许可证显示"永久"）
- **授权状态**: 有效/过期/未授权
- **部门信息**: 显示授权部门名称

### 5.2 许可证管理界面
提供许可证管理对话框：
- **导入许可证**: 选择并导入license文件
- **查看详情**: 显示当前许可证详细信息
- **硬件信息**: 显示当前硬件指纹
- **授权功能**: 显示已授权的功能模块列表

### 5.3 功能限制提示
当用户访问未授权功能时：
- **弹出提示**: 显示功能未授权提示
- **功能说明**: 说明该功能的用途和重要性
- **联系方式**: 提供内部技术支持联系方式

## 6. 安全考虑

### 6.1 防篡改措施
- **代码混淆**: 对关键代码进行混淆
- **完整性检查**: 启动时检查程序文件完整性
- **反调试**: 检测调试器和反汇编工具
- **时间检查**: 防止系统时间篡改

### 6.2 密钥管理
- **公钥内嵌**: RSA公钥内嵌在程序中
- **私钥保护**: RSA私钥由授权服务器保管
- **密钥轮换**: 定期更换加密密钥

### 6.3 日志记录
记录所有授权相关操作：
- **许可证加载**: 记录加载成功/失败
- **验证结果**: 记录验证过程和结果
- **功能访问**: 记录功能访问尝试
- **异常情况**: 记录所有异常和错误

## 7. 部署和维护

### 7.1 许可证生成工具
开发独立的许可证生成工具：
- **部门信息录入**: 录入使用部门基本信息
- **功能选择**: 选择授权功能模块
- **有效期设置**: 设置许可证有效期
- **硬件绑定**: 绑定目标机器硬件信息
- **文件生成**: 生成加密的license文件

### 7.2 内部支持流程
1. **部门申请**: 使用部门提供硬件信息和功能需求
2. **需求评估**: 评估功能需求的合理性
3. **许可证生成**: 使用工具生成对应许可证
4. **文件交付**: 安全交付license文件给申请部门
5. **安装指导**: 指导部门人员安装和激活

### 7.3 故障排除
常见问题和解决方案：
- **硬件指纹变化**: 重新生成许可证
- **文件损坏**: 重新下载许可证文件
- **时间同步**: 检查系统时间设置
- **权限问题**: 检查文件读写权限

## 8. 测试策略

### 8.1 单元测试
- **加密解密**: 测试加密解密功能
- **签名验证**: 测试数字签名验证
- **硬件指纹**: 测试硬件指纹生成
- **有效期检查**: 测试各种有效期场景

### 8.2 集成测试
- **完整流程**: 测试从加载到验证的完整流程
- **异常处理**: 测试各种异常情况处理
- **功能控制**: 测试功能授权控制
- **界面交互**: 测试用户界面交互

### 8.3 安全测试
- **破解测试**: 测试防破解能力
- **篡改测试**: 测试防篡改能力
- **时间攻击**: 测试时间相关攻击
- **内存分析**: 测试内存数据保护

## 9. 性能要求

### 9.1 响应时间
- **许可证加载**: < 1秒
- **验证检查**: < 100ms
- **功能检查**: < 10ms
- **界面显示**: < 200ms

### 9.2 资源使用
- **内存占用**: < 10MB
- **CPU使用**: < 1%
- **磁盘空间**: < 1MB
- **启动时间**: < 500ms

## 10. 版本历史

| 版本 | 日期 | 变更内容 | 负责人 |
|------|------|----------|--------|
| 1.0 | 2024-12-09 | 初始版本，完整的授权机制设计 | AI Assistant |
