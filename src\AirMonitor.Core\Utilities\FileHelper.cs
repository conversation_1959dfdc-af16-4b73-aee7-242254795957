using System.Security.Cryptography;
using System.Text;

namespace AirMonitor.Core.Utilities;

/// <summary>
/// 文件操作工具类
/// 提供文件和目录操作的辅助功能
/// </summary>
public static class FileHelper
{
    /// <summary>
    /// 确保目录存在，如果不存在则创建
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>目录路径</returns>
    public static string EnsureDirectoryExists(string directoryPath)
    {
        if (!Directory.Exists(directoryPath))
        {
            Directory.CreateDirectory(directoryPath);
        }
        return directoryPath;
    }

    /// <summary>
    /// 安全删除文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否删除成功</returns>
    public static bool SafeDeleteFile(string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
                return true;
            }
            return false;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 安全删除目录
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <param name="recursive">是否递归删除</param>
    /// <returns>是否删除成功</returns>
    public static bool SafeDeleteDirectory(string directoryPath, bool recursive = true)
    {
        try
        {
            if (Directory.Exists(directoryPath))
            {
                Directory.Delete(directoryPath, recursive);
                return true;
            }
            return false;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 复制文件
    /// </summary>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="destinationFilePath">目标文件路径</param>
    /// <param name="overwrite">是否覆盖已存在的文件</param>
    /// <returns>是否复制成功</returns>
    public static async Task<bool> CopyFileAsync(string sourceFilePath, string destinationFilePath, bool overwrite = true)
    {
        try
        {
            if (!File.Exists(sourceFilePath))
            {
                return false;
            }

            var destinationDirectory = Path.GetDirectoryName(destinationFilePath);
            if (!string.IsNullOrEmpty(destinationDirectory))
            {
                EnsureDirectoryExists(destinationDirectory);
            }

            using var sourceStream = new FileStream(sourceFilePath, FileMode.Open, FileAccess.Read);
            using var destinationStream = new FileStream(destinationFilePath, 
                overwrite ? FileMode.Create : FileMode.CreateNew, FileAccess.Write);
            
            await sourceStream.CopyToAsync(destinationStream);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 移动文件
    /// </summary>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="destinationFilePath">目标文件路径</param>
    /// <param name="overwrite">是否覆盖已存在的文件</param>
    /// <returns>是否移动成功</returns>
    public static bool MoveFile(string sourceFilePath, string destinationFilePath, bool overwrite = true)
    {
        try
        {
            if (!File.Exists(sourceFilePath))
            {
                return false;
            }

            var destinationDirectory = Path.GetDirectoryName(destinationFilePath);
            if (!string.IsNullOrEmpty(destinationDirectory))
            {
                EnsureDirectoryExists(destinationDirectory);
            }

            if (overwrite && File.Exists(destinationFilePath))
            {
                File.Delete(destinationFilePath);
            }

            File.Move(sourceFilePath, destinationFilePath);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取文件大小（字节）
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件大小，-1表示文件不存在</returns>
    public static long GetFileSize(string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                var fileInfo = new FileInfo(filePath);
                return fileInfo.Length;
            }
            return -1;
        }
        catch
        {
            return -1;
        }
    }

    /// <summary>
    /// 获取格式化的文件大小字符串
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>格式化的文件大小</returns>
    public static string GetFormattedFileSize(string filePath)
    {
        var size = GetFileSize(filePath);
        return FormatFileSize(size);
    }

    /// <summary>
    /// 格式化文件大小
    /// </summary>
    /// <param name="bytes">字节数</param>
    /// <returns>格式化的文件大小</returns>
    public static string FormatFileSize(long bytes)
    {
        if (bytes < 0)
        {
            return "未知";
        }

        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;

        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len /= 1024;
        }

        return $"{len:0.##} {sizes[order]}";
    }

    /// <summary>
    /// 计算文件MD5哈希值
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>MD5哈希值</returns>
    public static async Task<string> CalculateFileMD5Async(string filePath)
    {
        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"文件不存在: {filePath}");
        }

        using var md5 = MD5.Create();
        using var stream = File.OpenRead(filePath);
        var hash = await Task.Run(() => md5.ComputeHash(stream));
        return Convert.ToHexString(hash).ToLowerInvariant();
    }

    /// <summary>
    /// 计算文件SHA256哈希值
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>SHA256哈希值</returns>
    public static async Task<string> CalculateFileSHA256Async(string filePath)
    {
        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"文件不存在: {filePath}");
        }

        using var sha256 = SHA256.Create();
        using var stream = File.OpenRead(filePath);
        var hash = await Task.Run(() => sha256.ComputeHash(stream));
        return Convert.ToHexString(hash).ToLowerInvariant();
    }

    /// <summary>
    /// 生成唯一文件名
    /// </summary>
    /// <param name="directory">目录路径</param>
    /// <param name="fileName">原始文件名</param>
    /// <returns>唯一文件名</returns>
    public static string GenerateUniqueFileName(string directory, string fileName)
    {
        var fullPath = Path.Combine(directory, fileName);
        if (!File.Exists(fullPath))
        {
            return fileName;
        }

        var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
        var extension = Path.GetExtension(fileName);
        var counter = 1;

        do
        {
            var newFileName = $"{nameWithoutExtension}_{counter}{extension}";
            fullPath = Path.Combine(directory, newFileName);
            counter++;
        }
        while (File.Exists(fullPath));

        return Path.GetFileName(fullPath);
    }

    /// <summary>
    /// 备份文件
    /// </summary>
    /// <param name="filePath">原文件路径</param>
    /// <param name="backupDirectory">备份目录</param>
    /// <returns>备份文件路径</returns>
    public static async Task<string?> BackupFileAsync(string filePath, string? backupDirectory = null)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                return null;
            }

            backupDirectory ??= Path.Combine(Path.GetDirectoryName(filePath) ?? "", "Backup");
            EnsureDirectoryExists(backupDirectory);

            var fileName = Path.GetFileName(filePath);
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            var extension = Path.GetExtension(fileName);
            var backupFileName = $"{nameWithoutExtension}_{timestamp}{extension}";
            var backupFilePath = Path.Combine(backupDirectory, backupFileName);

            var success = await CopyFileAsync(filePath, backupFilePath);
            return success ? backupFilePath : null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 获取临时文件路径
    /// </summary>
    /// <param name="extension">文件扩展名</param>
    /// <returns>临时文件路径</returns>
    public static string GetTempFilePath(string extension = ".tmp")
    {
        var tempDirectory = Path.GetTempPath();
        var fileName = $"{Guid.NewGuid()}{extension}";
        return Path.Combine(tempDirectory, fileName);
    }

    /// <summary>
    /// 安全写入文件内容
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="content">文件内容</param>
    /// <param name="encoding">编码格式</param>
    /// <returns>是否写入成功</returns>
    public static async Task<bool> SafeWriteAllTextAsync(string filePath, string content, Encoding? encoding = null)
    {
        try
        {
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory))
            {
                EnsureDirectoryExists(directory);
            }

            // 先写入临时文件，然后原子性地替换原文件
            var tempFilePath = $"{filePath}.tmp";
            await File.WriteAllTextAsync(tempFilePath, content, encoding ?? Encoding.UTF8);
            
            if (File.Exists(filePath))
            {
                File.Replace(tempFilePath, filePath, null);
            }
            else
            {
                File.Move(tempFilePath, filePath);
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 检查文件是否被占用
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>true表示被占用，false表示未被占用</returns>
    public static bool IsFileInUse(string filePath)
    {
        if (!File.Exists(filePath))
        {
            return false;
        }

        try
        {
            using var stream = File.Open(filePath, FileMode.Open, FileAccess.ReadWrite, FileShare.None);
            return false;
        }
        catch (IOException)
        {
            return true;
        }
        catch
        {
            return false;
        }
    }
}
