<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:syncfusion="clr-namespace:Syncfusion.Windows.Controls.PivotGrid;assembly=Syncfusion.PivotAnalysis.WPF"
                    xmlns:schemaDesigner="clr-namespace:Syncfusion.Windows.Controls.PivotSchemaDesigner;assembly=Syncfusion.PivotAnalysis.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphToggleButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Button.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/CheckBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Menu.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ListBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatPrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Window.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Label.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBlock.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Separator.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ScrollViewer.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/ChromelessWindow/ChromelessWindow.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/PivotGridControl/PivotGridResources.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <schemaDesigner:VisibilityConverter x:Key="VisibilityConverter"/>

    <DrawingImage x:Key="SyncfusionPivotSchemaDesignerFilterImage">
        <DrawingImage.Drawing>
            <DrawingGroup>
                <DrawingGroup.Children>
                    <GeometryDrawing Brush="{StaticResource IconColor}"
                                     Geometry="M0,0 L14,0 14,1 8.5,7.0000002 8.5,16 5.5,13 5.5,7.0000002 0,1 z">
                    </GeometryDrawing>
                </DrawingGroup.Children>
            </DrawingGroup>
        </DrawingImage.Drawing>
    </DrawingImage>

    <DrawingImage x:Key="SyncfusionPivotSchemaDesignerRowHeaderImage">
        <DrawingImage.Drawing>
            <DrawingGroup>
                <DrawingGroup.Children>
                    <GeometryDrawing Brush="{StaticResource IconColor}"
                                     Geometry="M10,11 L10,15 14,15 14,11 z M5,11 L5,15 9,15 9,11 z M10,6 L10,10 14,10 14,6 z M5,6 L5,10 9,10 9,6 z M10,1 L10,5 14,5 14,1 z M5,1 L5,5 9,5 9,1 z M1,0 L14,0 C14.552002,0 15,0.44799805 15,1 L15,15 C15,15.552994 14.552002,16 14,16 L1,16 C0.44702148,16 0,15.552994 0,15 L0,1 C0,0.44799805 0.44702148,0 1,0 z">
                    </GeometryDrawing>
                </DrawingGroup.Children>
            </DrawingGroup>
        </DrawingImage.Drawing>
    </DrawingImage>

    <DrawingImage x:Key="SyncfusionPivotSchemaDesignerColumnHeaderImage">
        <DrawingImage.Drawing>
            <DrawingGroup>
                <DrawingGroup.Children>
                    <GeometryDrawing Brush="{StaticResource IconColor}"
                                     Geometry="M11,10 L11,14 15,14 15,10 z M6,10 L6,14 10,14 10,10 z M1,10 L1,14 5,14 5,10 z M11,5 L11,9 15,9 15,5 z M6,5 L6,9 10,9 10,5 z M1,5 L1,9 5,9 5,5 z M1,0 L15,0 C15.552002,0 16,0.44799805 16,1 L16,14 C16,14.552994 15.552002,15 15,15 L1,15 C0.44702148,15 0,14.552994 0,14 L0,1 C0,0.44799805 0.44702148,0 1,0 z">
                    </GeometryDrawing>
                </DrawingGroup.Children>
            </DrawingGroup>
        </DrawingImage.Drawing>
    </DrawingImage>

    <DrawingImage x:Key="SyncfusionPivotSchemaDesignerSigmaImage">
        <DrawingImage.Drawing>
            <DrawingGroup>
                <DrawingGroup.Children>
                    <GeometryDrawing Brush="{StaticResource IconColor}"
                                     Geometry="F1 M 0,0.329224L 7.62939e-006,0L 4.64537,0.0530396L 15.924,0.0908813L 15.874,3.24121C 15.8364,3.46619 14.9597,3.60864 14.2471,3.34613C 13.5346,3.08356 12.7887,1.94598 12.4136,1.62927L 4.97965,1.56262L 12.3469,8.0174L 12.3344,8.49243L 5.5797,15.2305L 12.4362,15.1533C 12.8112,14.8366 13.2846,13.9595 13.9971,13.697C 14.6972,13.2636 15.8739,13.3413 15.9115,13.5663L 15.9615,16.7166L 5.09764,16.7531L 5.09168,16.759L 3.31583,16.759L 1.06017,16.7666L 0.433388,16.7515L 0.412514,16.4848L 8.95908,8.37158L 8.98409,7.89655L 0,0.329224 Z ">
                    </GeometryDrawing>
                </DrawingGroup.Children>
            </DrawingGroup>
        </DrawingImage.Drawing>
    </DrawingImage>

    <DrawingImage x:Key="SyncfusionPivotSchemaDesignerDownPathImage">
        <DrawingImage.Drawing>
            <DrawingGroup>
                <DrawingGroup.Children>
                    <GeometryDrawing Geometry="M 2.40021,2.9129L 15.0308,14.5004" Brush="{StaticResource IconColor}">
                        <GeometryDrawing.Pen>
                            <Pen Brush="{StaticResource IconColor}"
                                 MiterLimit="2.75"
                                 Thickness="4.80042" />
                        </GeometryDrawing.Pen>
                    </GeometryDrawing>
                    <GeometryDrawing Geometry="M 23.86,2.40021L 11.4931,14.5204" Brush="{StaticResource IconColor}">
                        <GeometryDrawing.Pen>
                            <Pen Brush="{StaticResource IconColor}"
                                 MiterLimit="2.75"
                                 Thickness="4.80042" />
                        </GeometryDrawing.Pen>
                    </GeometryDrawing>
                </DrawingGroup.Children>
            </DrawingGroup>
        </DrawingImage.Drawing>
    </DrawingImage>

    <Style x:Key="PivotSchemaDesignerButtonStyle" 
           BasedOn="{StaticResource WPFGlyphButtonStyle}" 
           TargetType="{x:Type Button}">
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="24" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="PivotSchemaDesignerToggleButtonStyle" 
           TargetType="{x:Type ToggleButton}"
           BasedOn="{StaticResource WPFGlyphToggleButtonStyle}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border x:Name="brdItem"
                            Margin="2,1"
                            Background="Transparent"
                            BorderBrush="Transparent"
                            BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius2}">
                        <Grid HorizontalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBlock x:Name="contentTxt"
                                       Margin="6,0"
                                       Foreground="{StaticResource TextFillColorSecondary}" 
                                       Text="{Binding FieldCaption}" />
                            <ContentPresenter Content="{TemplateBinding Content}"
                                              Grid.Column="1"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger SourceName="contentTxt" 
                                 Property="IsMouseOver" 
                                 Value="True">
                            <Setter Property="Cursor" 
                                    Value="SizeAll" />
                        </Trigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="contentTxt" 
                                    Property="Margin" 
                                    Value="6,3" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="PivotSchemaItemContainerStyle" 
           TargetType="{x:Type ListBoxItem}">
        <Setter Property="OverridesDefaultStyle" Value="True"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListBoxItem}">
                    <Border x:Name="Bd">
                        <ContentPresenter />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" 
                                 Value="True">
                            <Setter Property="Background" 
                                    TargetName="Bd"
                                    Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" 
                                    TargetName="Bd"
                                    Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground"
                                    Value="{StaticResource HoveredForeground}"/>
                        </Trigger>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="Height" 
                                    Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ContextMenu x:Key="SyncfusionPivotSchemaDesignerContextMenu" 
                 Style="{StaticResource WPFContextMenuStyle}">
        <MenuItem Command="schemaDesigner:PivotCommands.MoveUp"
                  CommandParameter="Move Up"
                  Header="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ContextMenu_Up}" />
        <MenuItem Command="schemaDesigner:PivotCommands.MoveDown"
                  CommandParameter="Move Down"
                  Header="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ContextMenu_Down}" />
        <MenuItem Command="schemaDesigner:PivotCommands.MovetoBeginning"
                  CommandParameter="Move to Beginning"
                  Header="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ContextMenu_Beginning}" />
        <MenuItem Command="schemaDesigner:PivotCommands.MovetoEnd"
                  CommandParameter="Move to End"
                  Header="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ContextMenu_End}" />
        <Separator />
        <MenuItem Command="schemaDesigner:PivotCommands.MovetoFilter"
                  CommandParameter="Move to Filter"
                  Header="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ContextMenu_ToFilter}" />
        <MenuItem Command="schemaDesigner:PivotCommands.MovetoColumn"
                  CommandParameter="Move to Column"
                  Header="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ContextMenu_ToColumn}" />
        <MenuItem Command="schemaDesigner:PivotCommands.MovetoRow"
                  CommandParameter="Move to Row"
                  Header="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ContextMenu_ToRow}" />
        <MenuItem Command="schemaDesigner:PivotCommands.MovetoValues"
                  CommandParameter="Move to Values"
                  Header="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ContextMenu_ToValues}" />
        <Separator />
        <MenuItem Command="schemaDesigner:PivotCommands.RemoveField"
                  CommandParameter="Remove Field"
                  Header="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ContextMenu_RemoveField}" />
        <MenuItem Command="schemaDesigner:PivotCommands.Exit"
                  CommandParameter="Exit"
                  Header="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ContextMenu_Exit}" />
    </ContextMenu>

    <DataTemplate x:Key="PivotSchemaItemTemplate">
        <StackPanel HorizontalAlignment="Stretch">
            <Rectangle x:Name="Part_Indicator"
                       Height="2"
                       Fill="Transparent" />
            <ToggleButton x:Name="toggleButtonItem"
                          Style="{StaticResource PivotSchemaDesignerToggleButtonStyle}"
                          Tag="{Binding}"
                          ContextMenu="{StaticResource SyncfusionPivotSchemaDesignerContextMenu}">
                <ToggleButton.Content>
                    <Button x:Name="btnDelete"
                            Width="16"
                            Height="16"
                            Style="{StaticResource SyncfusionPivotGridDeleteButtonStyle}"
                            Command="schemaDesigner:PivotCommands.DeletePivotItem"
                            Tag="{Binding}"/>
                </ToggleButton.Content>
            </ToggleButton>
            <StackPanel.Triggers>
                <EventTrigger RoutedEvent="DragDrop.DragOver">
                    <BeginStoryboard>
                        <Storyboard Storyboard.TargetName="Part_Indicator" 
                                    Storyboard.TargetProperty="Fill.Color">
                            <ColorAnimation Duration="0:0:0"
                                            From="Transparent"
                                            To="{StaticResource PrimaryBackground.Color}" />
                        </Storyboard>
                    </BeginStoryboard>
                </EventTrigger>
                <EventTrigger RoutedEvent="DragDrop.DragLeave">
                    <BeginStoryboard>
                        <Storyboard Storyboard.TargetName="Part_Indicator" 
                                    Storyboard.TargetProperty="Fill.Color">
                            <ColorAnimation Duration="0:0:0"
                                            From="{StaticResource PrimaryBackground.Color}"
                                            To="Transparent" />
                        </Storyboard>
                    </BeginStoryboard>
                </EventTrigger>

                <EventTrigger RoutedEvent="DragDrop.Drop">
                    <BeginStoryboard>
                        <Storyboard Storyboard.TargetName="Part_Indicator" 
                                    Storyboard.TargetProperty="Fill.Color">
                            <ColorAnimation Duration="0:0:0"
                                            From="{StaticResource PrimaryBackground.Color}"
                                            To="Transparent" />
                        </Storyboard>
                    </BeginStoryboard>
                </EventTrigger>
            </StackPanel.Triggers>
        </StackPanel>
    </DataTemplate>

    <DataTemplate x:Key="PivotSchemaFilterTemplate">
        <StackPanel HorizontalAlignment="Stretch">
            <Rectangle x:Name="Part_Indicator"
                       Height="2"
                       Fill="Transparent" />
            <ToggleButton x:Name="toggleButtonItem"
                          Style="{StaticResource PivotSchemaDesignerToggleButtonStyle}"
                          Tag="{Binding}"
                          ContextMenu="{StaticResource SyncfusionPivotSchemaDesignerContextMenu}">
                <ToggleButton.Content>
                    <Grid HorizontalAlignment="Stretch">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Button Width="16"
                                Height="16"
                                Style="{StaticResource SyncfusionPivotGridDeleteButtonStyle}"
                                Command="schemaDesigner:PivotCommands.DeleteFilter"
                                Tag="{Binding}"/>
                        <Button Grid.Column="1"
                                Width="16"
                                Height="16"
                                Style="{StaticResource PivotSchemaDesignerButtonStyle}"
                                Margin="2,0"
                                Command="schemaDesigner:PivotCommands.ShowFilter" 
                                Tag="{Binding}" >
                            <Image Width="8"
                                    Height="8"
                                    Margin="2,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Source="{StaticResource SyncfusionPivotSchemaDesignerDownPathImage}" />
                        </Button>
                    </Grid>
                </ToggleButton.Content>
            </ToggleButton>
            <StackPanel.Triggers>
                <EventTrigger RoutedEvent="DragDrop.DragOver">
                    <BeginStoryboard>
                        <Storyboard Storyboard.TargetName="Part_Indicator" 
                                    Storyboard.TargetProperty="Fill.Color">
                            <ColorAnimation Duration="0:0:0"
                                            From="Transparent"
                                            To="{StaticResource PrimaryBackground.Color}" />
                        </Storyboard>
                    </BeginStoryboard>
                </EventTrigger>
                <EventTrigger RoutedEvent="DragDrop.DragLeave">
                    <BeginStoryboard>
                        <Storyboard Storyboard.TargetName="Part_Indicator" 
                                    Storyboard.TargetProperty="Fill.Color">
                            <ColorAnimation Duration="0:0:0"
                                            From="{StaticResource PrimaryBackground.Color}"
                                            To="Transparent" />
                        </Storyboard>
                    </BeginStoryboard>
                </EventTrigger>
                <EventTrigger RoutedEvent="DragDrop.Drop">
                    <BeginStoryboard>
                        <Storyboard Storyboard.TargetName="Part_Indicator" 
                                    Storyboard.TargetProperty="Fill.Color">
                            <ColorAnimation Duration="0:0:0"
                                            From="{StaticResource PrimaryBackground.Color}"
                                            To="Transparent" />
                        </Storyboard>
                    </BeginStoryboard>
                </EventTrigger>
            </StackPanel.Triggers>
        </StackPanel>
    </DataTemplate>

    <Style x:Key="SyncfusionPivotSchemaDesignerStyle"
           TargetType="{x:Type schemaDesigner:PivotSchemaDesigner}">
        <Style.Setters>
            <Setter Property="Foreground" 
                    Value="{StaticResource ContentForegroundAlt1}" />
            <Setter Property="FontSize" 
                    Value="{StaticResource Windows11Dark.BodyTextStyle}" />
            <Setter Property="sfskin:SkinManagerHelper.ScrollBarMode" Value="Default" />           
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type schemaDesigner:PivotSchemaDesigner}">
                        <Border Background="{StaticResource PopupBackground}"
                                BorderBrush="{StaticResource BorderAlt}"
                                BorderThickness="{StaticResource Windows11Dark.BorderThickness1}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height=".5*" 
                                                   MinHeight="150" />
                                    <RowDefinition Height="11" />
                                    <RowDefinition Height=".5*" 
                                                   MinHeight="150" />
                                    <RowDefinition x:Name="PART_ShowCalculationsAsColumnRowDefinition" 
                                                   Height="28" />
                                    <RowDefinition x:Name="PART_DeferLayoutUpdateRowDefinition" 
                                                   Height="28" />
                                </Grid.RowDefinitions>
                                <Grid Grid.Row="0" 
                                      HorizontalAlignment="Stretch">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="25" />
                                        <RowDefinition Height="25" />
                                        <RowDefinition />
                                    </Grid.RowDefinitions>
                                    <Border x:Name="titleBorder"
                                            Grid.Row="0" 
                                            Background="{StaticResource ContentBackgroundAlt1}">
                                        <TextBlock x:Name="textBlock"
                                                   Margin="4,0"
                                                   VerticalAlignment="Center" 
                                                   Foreground="{StaticResource ContentForeground}"
                                                   Text="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_Header_PivotTableFieldList}" />
                                    </Border>
                                    <TextBlock Grid.Row="1"
                                               Margin="8,0,8,3"
                                               VerticalAlignment="Bottom"
                                               Text="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_Header_AddReport}" />
                                    <ListBox x:Name="PART_PivotTableList"
                                               HorizontalContentAlignment="Left"
                                               VerticalContentAlignment="Center"
                                               Grid.Row="2"
                                               Margin="6,0"
                                               AllowDrop="True"
                                               ItemsSource="{Binding Path=PivotTableFields}"
                                               ItemContainerStyle="{StaticResource PivotSchemaItemContainerStyle}">
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <Grid x:Name="grid"
                                                      HorizontalAlignment="Stretch">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="18"/>
                                                        <ColumnDefinition />
                                                        <ColumnDefinition Width="22"/>
                                                    </Grid.ColumnDefinitions>
                                                    <CheckBox x:Name="checkbox"
                                                              IsChecked="{Binding IsSelected}" 
                                                              VerticalAlignment="Center"  />
                                                    <TextBlock x:Name="contentTxt" 
                                                             Grid.Column="1" 
                                                             Margin="4,0,0,0"
                                                             VerticalAlignment="Center"
                                                             Text="{Binding FieldCaption}"/>
                                                    <Button x:Name="filterbutton"
                                                            Grid.Column="2"
                                                            Width="{TemplateBinding Width}"
                                                            Height="{TemplateBinding Height}"
                                                            Style="{StaticResource PivotSchemaDesignerButtonStyle}"
                                                            Margin="2,0"
                                                            Command="schemaDesigner:PivotCommands.ShowFilter"
                                                            Tag="{Binding}">
                                                        <Image Width="8"
                                                            Height="8"
                                                            Margin="2,0"
                                                            HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            Source="{StaticResource SyncfusionPivotSchemaDesignerDownPathImage}" />
                                                        <Button.Visibility>
                                                            <MultiBinding Converter="{StaticResource VisibilityConverter}" >
                                                                <Binding ElementName="checkbox" 
                                                                         Path="IsChecked" />
                                                                <Binding></Binding>
                                                                <Binding RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType=schemaDesigner:PivotSchemaDesigner}"/>
                                                            </MultiBinding>
                                                        </Button.Visibility>
                                                    </Button>
                                                </Grid>
                                                <DataTemplate.Triggers>
                                                    <Trigger SourceName="contentTxt"
                                                             Property="IsMouseOver" 
                                                             Value="True">
                                                        <Setter Property="Cursor" 
                                                                Value="SizeAll" />
                                                    </Trigger>
                                                </DataTemplate.Triggers>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>
                                    </ListBox>
                                </Grid>

                                <GridSplitter Grid.Row="1"
                                              Width="Auto"
                                              Height="1"
                                              HorizontalAlignment="Stretch"
                                              VerticalAlignment="Stretch"
                                              ResizeDirection="Rows" />

                                <Grid Grid.Row="2"
                                      Margin="6,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height=".5*" />
                                        <RowDefinition Height=".5*" />
                                    </Grid.RowDefinitions>
                                    <Grid Grid.Row="0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="23" />
                                            <RowDefinition Height="20" />
                                            <RowDefinition />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Row="0"
                                                   Grid.ColumnSpan="2"
                                                   Margin="0,0,0,5"
                                                   VerticalAlignment="Center"
                                                   Text="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_Header_DragArea}" />
                                        <StackPanel Grid.Row="1"
                                                    Grid.Column="0"
                                                    Height="24"
                                                    VerticalAlignment="Center"
                                                    Orientation="Horizontal">
                                            <Image Width="16"
                                                   Height="16"
                                                   Margin="4,0"
                                                   Source="{StaticResource SyncfusionPivotSchemaDesignerFilterImage}" />
                                            <TextBlock Margin="4,0"
                                                       VerticalAlignment="Center"
                                                      Text="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ListBox_ReportFilter}" />
                                        </StackPanel>
                                        <ListBox x:Name="PART_FilterList"
                                                 HorizontalContentAlignment="Left"
                                                 VerticalContentAlignment="Center"
                                                 Grid.Row="2"
                                                 Grid.Column="0"
                                                 Margin="0,0,2,0"
                                                 AllowDrop="True"
                                                 BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                                 ItemContainerStyle="{StaticResource PivotSchemaItemContainerStyle}"
                                                 ItemTemplate="{StaticResource PivotSchemaFilterTemplate}"
                                                 ItemsSource="{Binding Path=Filters}" />
                                        <StackPanel Grid.Row="1"
                                                    Grid.Column="1"
                                                    Height="24"
                                                    VerticalAlignment="Center"
                                                    Orientation="Horizontal">
                                            <Image Width="16"
                                                   Height="16"
                                                   Margin="4,0"
                                                   Source="{StaticResource SyncfusionPivotSchemaDesignerColumnHeaderImage}" />
                                            <TextBlock Margin="4,0"
                                                       VerticalAlignment="Center"
                                                       Text="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ListBox_ColumnLabel}" />
                                        </StackPanel>
                                        <ListBox x:Name="PART_PivotColumnList"
                                                 HorizontalContentAlignment="Left"
                                                 VerticalContentAlignment="Center"
                                                 Grid.Row="2"
                                                 Grid.Column="1"
                                                 Margin="2,0,0,0"
                                                 AllowDrop="True"
                                                 BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                                 ItemContainerStyle="{StaticResource PivotSchemaItemContainerStyle}"
                                                 ItemTemplate="{StaticResource PivotSchemaItemTemplate}"
                                                 ItemsSource="{Binding Path=PivotControl.PivotColumns}" />
                                    </Grid>

                                    <Grid Grid.Row="1" Margin="0,5,0,0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="23" />
                                            <RowDefinition />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition />
                                        </Grid.ColumnDefinitions>
                                        <StackPanel Grid.Row="0"
                                                    Grid.Column="0"
                                                    Height="24"
                                                    VerticalAlignment="Center"
                                                    Orientation="Horizontal">
                                            <Image Width="16"
                                                   Height="16"
                                                   Margin="4,0"
                                                   Source="{StaticResource SyncfusionPivotSchemaDesignerRowHeaderImage}" />
                                            <TextBlock Margin="4,0"
                                                       VerticalAlignment="Center"
                                                       Text="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ListBox_RowLabel}" />
                                        </StackPanel>

                                        <ListBox x:Name="PART_PivotRowList"
                                                 HorizontalContentAlignment="Left"
                                                 VerticalContentAlignment="Center"
                                                 Grid.Row="1"
                                                 Grid.Column="0"
                                                 Margin="0,0,2,0"
                                                 AllowDrop="True"
                                                 BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                                 ItemContainerStyle="{StaticResource PivotSchemaItemContainerStyle}"
                                                 ItemTemplate="{StaticResource PivotSchemaItemTemplate}"
                                                 ItemsSource="{Binding Path=PivotControl.PivotRows}" />

                                        <StackPanel Grid.Row="0"
                                                    Grid.Column="1"
                                                    Height="24"
                                                    VerticalAlignment="Center"
                                                    Orientation="Horizontal">
                                            <Image Width="16"
                                                   Height="16"
                                                   Margin="4,0"
                                                   Source="{StaticResource SyncfusionPivotSchemaDesignerSigmaImage}" />
                                            <TextBlock Margin="4,0"
                                                       VerticalAlignment="Center"
                                                       Text="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_ListBox_Values}" />
                                        </StackPanel>

                                        <ListBox x:Name="PART_PivotCalculationList"
                                                 HorizontalContentAlignment="Left"
                                                 VerticalContentAlignment="Center"
                                                 Grid.Row="1"
                                                 Grid.Column="1"
                                                 Margin="2,0,0,0"
                                                 AllowDrop="True"
                                                 BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                                 ItemContainerStyle="{StaticResource PivotSchemaItemContainerStyle}"
                                                 ItemTemplate="{StaticResource PivotSchemaItemTemplate}"
                                                 ItemsSource="{Binding Path=PivotControl.PivotCalculations}"/>
                                    </Grid>
                                </Grid>
                                <CheckBox x:Name="PART_ShowCalculationsAsColumn"
                                          Grid.Row="3"
                                          Margin="6,0"
                                          Height="20"
                                          VerticalAlignment="Center"
                                          Content="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_CheckBox_ShowCalulation}"
                                          IsChecked="{Binding Path=PivotControl.ShowCalculationsAsColumns,
                                                              Mode=TwoWay}" />

                                <Grid x:Name="PART_DeferLayoutUpdateGrid" Grid.Row="4" Margin="6,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <CheckBox x:Name="PART_DeferLayoutUpdate" 
                                              Margin="0,0,10,0"
                                              Height="20"
                                              VerticalAlignment="Center"
                                              Content="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_CheckBox_DeferUpdate}"
                                              IsChecked="{Binding Path=PivotControl.DeferLayoutUpdate, Mode=TwoWay}" />
                                    <Button x:Name="PART_DeferLayoutUpdateButton"
                                            Grid.Column="1"
                                            Style="{StaticResource WPFButtonStyle}"
                                            Height="20"
                                            Width="75"
                                            HorizontalAlignment="Right"
                                            VerticalAlignment="Center"
                                            Content="{syncfusion:PivotAnalysisLocalizationResourceExtension ResourceName=PivotSchema_Button_Update}"
                                            IsEnabled="{Binding ElementName=PART_DeferLayoutUpdate,
                                                                        Path=IsChecked}" />
                                </Grid>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsEnabled" 
                                     Value="False">
                                <Setter Property="Background" 
                                        Value="{StaticResource PopupBackground}"/>
                                <Setter Property="BorderBrush" 
                                        Value="{StaticResource BorderAlt}"/>
                                <Setter Property="Foreground"
                                        Value="{StaticResource ContentForegroundAlt1}"/>
                                <Setter Property="Background" TargetName="titleBorder" 
                                        Value="{StaticResource ContentBackgroundAlt1}"/>
                                <Setter Property="Foreground" TargetName="textBlock" 
                                        Value="{StaticResource ContentBackground}"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" 
                                     Value="True" >
                                <Setter Property="Background" 
                                        Value="{StaticResource ContentBackgroundHovered}" />
                            </Trigger>
                            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                                <Setter TargetName="PART_ShowCalculationsAsColumn" 
                                        Property="MinHeight" 
                                        Value="{StaticResource TouchMode.MinHeight}" />
                                <Setter TargetName="PART_DeferLayoutUpdate" 
                                        Property="MinHeight" 
                                        Value="{StaticResource TouchMode.MinHeight}" />
                                <Setter TargetName="PART_DeferLayoutUpdateButton" 
                                        Property="MinHeight" 
                                        Value="{StaticResource TouchMode.MinHeight}" />
                                <Setter TargetName="PART_ShowCalculationsAsColumnRowDefinition" 
                                        Property="MinHeight" 
                                        Value="{StaticResource TouchMode.MinHeight}" />
                                <Setter TargetName="PART_DeferLayoutUpdateRowDefinition" 
                                        Property="MinHeight" 
                                        Value="{StaticResource TouchMode.MinHeight}" />
                                <Setter TargetName="PART_DeferLayoutUpdateGrid" 
                                        Property="Margin"
                                        Value="6,0,6,2" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style.Setters>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionPivotSchemaDesignerStyle}" 
           TargetType="{x:Type schemaDesigner:PivotSchemaDesigner}" />

</ResourceDictionary>
