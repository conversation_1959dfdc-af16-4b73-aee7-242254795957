<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:richtextboxadv="clr-namespace:Syncfusion.Windows.Controls.RichTextBoxAdv;assembly=Syncfusion.SfRichTextBoxAdv.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:tools_controls_shared="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.shared.WPF"
					xmlns:Syncfusion="http://schemas.syncfusion.com/wpf"
                    xmlns:resources="clr-namespace:Syncfusion.Windows.Controls.RichTextBoxAdv;assembly=Syncfusion.SfRichTextBoxAdv.WPF">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SfRichTextBoxAdv/SfRichTextBoxCommon.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/FlatToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/ColorPickerPalette/ColorPickerPalette.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphPrimaryToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/SplitButtonAdv/SplitButtonAdv.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <richtextboxadv:FontColorIconColorConverter x:Key="FontColorIconColorConverter"/>

    <DataTemplate x:Key="fontColorIconTemplate">
        <Grid Width="13" Height="17">
            <Path Data="M0,0 L16,0 16,4 0,4 z"
                  Fill="#FFFE0000" 
                  Height="4" 
                  Stretch="Fill" 
                  VerticalAlignment="Bottom" />
            <Path Data="M4.6480023,0.95898432 C4.6079937,1.0870056 4.5689923,1.2149963 4.533012,1.34198 4.4980089,1.4909973 4.4510118,1.6419983 4.394005,1.7949829 L2.5330156,6.8809814 6.787006,6.8809814 4.9330055,1.7799988 C4.8510047,1.5699768 4.7659832,1.2969971 4.6790081,0.95898432 z M4.0779959,0 L5.209006,0 9.304,11 8.3170024,11 7.2039977,8.0019836 2.1150171,8.0029907 1.0100081,11 0,11 z" 
                  Fill="{StaticResource IconColor}"
                  Margin="3.344,0,3.352,5" 
                  Stretch="Fill" />
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="highLightColorIconTemplate">
        <Grid Width="13" Height="17">
            <Path
                  Height="4"
                  Margin="1"
                  VerticalAlignment="Bottom"
                  Data="M0,0 L16,0 16,4 0,4 z"
                  Fill="Yellow"
                  Stretch="Fill" />
            <Path
                  Margin="6.5,0.502,1.934,8.048"
                  Data="M6.4365052,0.00010436084 C6.6881317,-0.0035195307 6.9411348,0.087229683 7.1381453,0.27322931 L8.254165,1.3292008 C8.6621631,1.7171909 8.6711658,2.3651685 8.2731775,2.763153 L2.5470487,8.4889981 0,6.0360758 5.7431277,0.29321776 C5.9346298,0.10172514 6.1848787,0.0037282004 6.4365052,0.00010436084 z"
                  Fill="Transparent"
                  Stretch="Fill" />
            <Path
                  Margin="2.061,0,1.434,5"
                  Data="M10.881433,1.0010477 C10.756186,1.0027986 10.631936,1.052303 10.535929,1.1488092 L5.1529995,6.5319232 6.9789655,8.2909641 12.358904,2.9118485 C12.454911,2.815838 12.506913,2.6878447 12.504899,2.55184 12.502915,2.4158355 12.446916,2.2888338 12.347918,2.1958293 L11.232911,1.1398066 C11.132922,1.0452991 11.00668,0.99929665 10.881433,1.0010477 z M10.868932,0.0001521778 C11.245179,-0.005222887 11.623424,0.13178115 11.920921,0.41278561 L13.035898,1.4688085 C13.332891,1.7498205 13.498905,2.1298184 13.504886,2.5378322 13.509891,2.9468378 13.354894,3.329857 13.065897,3.6188647 L6.9929731,9.6919965 6.6261415,9.3388461 5.5570025,10.219989 C4.9130021,10.748988 4.1050018,11.038988 3.2720014,11.038988 L0,11.038988 0.22000027,10.953988 C2.0155009,10.255738 3.3807202,8.7842073 3.9559099,6.9755153 L4.0042109,6.8147053 3.725008,6.5459155 9.8289366,0.44179319 C10.118438,0.15328126 10.492686,0.0055271609 10.868932,0.0001521778 z"
                  Fill="{StaticResource IconColor}"
                  Stretch="Fill" />
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="bulletedListIconTemplate">
        <Grid Width="12" Height="16">
            <Path
                   Width="3"
                   HorizontalAlignment="Left"
                   Data="M0,12 L2.9999999,12 2.9999999,15 0,15 z M0,6 L2.9999999,6 2.9999999,9 0,9 z M0,0 L2.9999999,0 2.9999999,3 0,3 z"
                   Fill="{StaticResource PrimaryBackground}"
                   Stretch="Fill" />
            <Path
                   Margin="5,1,0,1"
                   Data="M0,12 L10,12 10,13 1.937151E-07,13 z M1.937151E-07,6 L10,6 10,7 1.937151E-07,7 z M0,0 L10,0 10,1 1.937151E-07,1 z"
                   Fill="{StaticResource IconColor}"
                   Stretch="Fill" />
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="numberedListIconTemplate">
        <Grid Width="12" Height="16">
            <Path
                  Width="14"
                  Height="15"
                  HorizontalAlignment="Right"
                  VerticalAlignment="Top"
                  Margin="5,1,0,1"
                  Data="M0,12.003 L10,12.003 10,13.003 0,13.003 z M0,6.0009933 L10,6.0009933 10,7.0009933 0,7.0009933 z M0,0 L10,0 10,1 0,1 z"
                  Fill="{StaticResource IconColor}"
                  Stretch="Fill" />
            <Path
                  Data="M0,10.997995 L2.9999999,10.997995 2.9999999,14.998988 0,14.998988 0,13.99899 1.9999999,13.99899 0.9999999,13.998988 0.9999999,12.998988 1.9999999,12.998988 1.9999999,11.997993 0,11.997993 z M0,4.9999973 L2.9999999,4.9999973 2.9999999,6.7080019 1.7070006,8.000999 2.9999999,8.000999 2.9999999,9.0009971 0,8.9979761 0,8.2939978 1.9999999,6.2929943 1.9999999,6.0010023 0,6.0010023 z M2.0079874,0 L2.0079874,3.9999998 1.0079922,3.9999998 1.0079922,1.8599999 0.56199415,2.1549997 0.010996761,1.3209999 z"
                  Fill="{StaticResource PrimaryBackground}"
                  Width="3"
                  HorizontalAlignment="Left"
                  Stretch="Fill" />
        </Grid>
    </DataTemplate>

    <Style TargetType="richtextboxadv:MiniToolBar">
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="richtextboxadv:MiniToolBar">
                    <Border BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            
                            Background="{StaticResource ContentBackground}"
                            SnapsToDevicePixels="True">
                        <ContentControl x:Name="content" 
                                        HorizontalAlignment="Stretch" 
                                        VerticalAlignment="Stretch"
                                        Margin="2">
                            <Grid>
                                <Grid x:Name="PART_ToolBarGrid" Background="{StaticResource ContentBackground}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid Grid.Column="0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid Grid.Row="0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <ComboBox Grid.Column="0" 
                                                      x:Name="PART_FontFamilyBox"  
                                                      Text="{Binding Selection.CharacterFormat.FontFamily, Mode=OneWay,Converter={StaticResource FontFamilyStringConverter}}"
                                                      SelectedValue="{Binding Selection.CharacterFormat.FontFamily,Mode=TwoWay,Converter={StaticResource FontFamilyStringConverter}}" 
                                                      Margin="3,3,0,3"
                                                      Focusable="False"
                                                      IsEditable="True"
                                                      Width="130" Height="23"
                                                      ToolTipService.ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarFontToolTip}"/>
                                            <ComboBox Grid.Column="1" x:Name="PART_FontSizeBox" 
                                                      Text="{Binding Selection.CharacterFormat.FontSize, Mode=OneWay,Converter={StaticResource DoubleStringConverter}}"
                                                      SelectedValue="{Binding Selection.CharacterFormat.FontSize}"  
                                                      Margin="3,3,0,3" Width="50"
                                                      Focusable="False"
                                                      IsEditable="True"
                                                      Height="23"
                                                      VerticalAlignment="Top" 
                                                      ToolTipService.ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarFontSizeToolTip}"/>
                                            <Button x:Name="PART_IncreaseFontSizeButton" 
                                                    Grid.Column="2" 
                                                    Focusable="False"
                                                    Margin="3,0,0,3"
                                                    Height="24"
                                                    Width="24"
                                                    Style="{StaticResource WPFGlyphButtonStyle}"
                                                    ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarIncreaseFontToolTip}">
                                                <Grid>
                                                    <Path Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M6.5 3C6.36458 3 6.2474 2.95052 6.14844 2.85156C6.04948 2.7526 6 2.63542 6 2.5C6 2.41146 6.02344 2.32552 6.07031 2.24219C6.1224 2.15885 6.1901 2.09635 6.27344 2.05469L8.27344 0.0546875C8.34115 0.0182292 8.41667 0 8.5 0C8.58333 0 8.65885 0.0182292 8.72656 0.0546875L10.7266 2.05469C10.8099 2.09635 10.875 2.15885 10.9219 2.24219C10.974 2.32552 11 2.41146 11 2.5C11 2.63542 10.9505 2.7526 10.8516 2.85156C10.7526 2.95052 10.6354 3 10.5 3C10.4167 3 10.3411 2.98177 10.2734 2.94531L8.5 1.0625L6.72656 2.94531C6.65885 2.98177 6.58333 3 6.5 3ZM0 11.4922C0 11.4245 0.0104167 11.3672 0.03125 11.3203L3.53125 2.32031C3.57292 2.22135 3.63542 2.14323 3.71875 2.08594C3.80208 2.02865 3.89583 2 4 2C4.10417 2 4.19792 2.02865 4.28125 2.08594C4.36458 2.14323 4.42708 2.22135 4.46875 2.32031L7.96875 11.3203C7.98958 11.3672 8 11.4245 8 11.4922C8 11.6276 7.95312 11.7474 7.85938 11.8516C7.76562 11.9505 7.64844 12 7.50781 12C7.40365 12 7.30729 11.9714 7.21875 11.9141C7.13542 11.8568 7.07292 11.7786 7.03125 11.6797L5.99219 9H2.00781L0.96875 11.6797C0.927083 11.7786 0.861979 11.8568 0.773438 11.9141C0.690104 11.9714 0.596354 12 0.492188 12C0.351562 12 0.234375 11.9505 0.140625 11.8516C0.046875 11.7474 0 11.6276 0 11.4922ZM4 3.88281L2.39844 8H5.60156L4 3.88281Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </Button>
                                            <Button x:Name="PART_DecreaseFontSizeButton"
                                                    Grid.Column="3"
                                                    Focusable="False"
                                                    Margin="3,0,0,3"
                                                    Height="24"
                                                    Width="24"
                                                    Style="{StaticResource WPFGlyphButtonStyle}"
                                                    ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarDecreaseFontToolTip}">
                                                <Grid>
                                                    <Path Fill="{StaticResource IconColor}">
                                                        <Path.Data>
                                                            <PathGeometry>M6 0.5C6 0.364583 6.04948 0.247396 6.14844 0.148438C6.2474 0.0494792 6.36458 0 6.5 0C6.58333 0 6.65885 0.0182292 6.72656 0.0546875L8.5 1.9375L10.2734 0.0546875C10.3411 0.0182292 10.4167 0 10.5 0C10.6354 0 10.7526 0.0494792 10.8516 0.148438C10.9505 0.247396 11 0.364583 11 0.5C11 0.588542 10.974 0.674479 10.9219 0.757812C10.875 0.841146 10.8099 0.903646 10.7266 0.945312L8.72656 2.94531C8.64323 2.98177 8.56771 3 8.5 3C8.43229 3 8.35677 2.98177 8.27344 2.94531L6.27344 0.945312C6.1901 0.903646 6.1224 0.841146 6.07031 0.757812C6.02344 0.674479 6 0.588542 6 0.5ZM0 11.4922C0 11.4245 0.0104167 11.3672 0.03125 11.3203L3.53125 2.32031C3.57292 2.22135 3.63542 2.14323 3.71875 2.08594C3.80208 2.02865 3.89583 2 4 2C4.10417 2 4.19792 2.02865 4.28125 2.08594C4.36458 2.14323 4.42708 2.22135 4.46875 2.32031L7.96875 11.3203C7.98958 11.3672 8 11.4245 8 11.4922C8 11.6276 7.95312 11.7474 7.85938 11.8516C7.76562 11.9505 7.64844 12 7.50781 12C7.40365 12 7.30729 11.9714 7.21875 11.9141C7.13542 11.8568 7.07292 11.7786 7.03125 11.6797L5.99219 9H2.00781L0.96875 11.6797C0.927083 11.7786 0.861979 11.8568 0.773438 11.9141C0.690104 11.9714 0.596354 12 0.492188 12C0.351562 12 0.234375 11.9505 0.140625 11.8516C0.046875 11.7474 0 11.6276 0 11.4922ZM4 3.88281L2.39844 8H5.60156L4 3.88281Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </Grid>
                                            </Button>
                                        </Grid>
                                        <Grid Grid.Row="1">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <ToggleButton x:Name="PART_BoldToggleButton" 
                                                          Grid.Column="0" 
                                                          Focusable="False"  
                                                          IsChecked="{Binding Selection.CharacterFormat.Bold, Mode=TwoWay}"  
                                                          Margin="3,0,0,3" 
                                                          Height="23" 
                                                          Width="23" 
                                                          Style="{StaticResource WPFFlatToggleButtonStyle}"
                                                          ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarBoldToolTip}" 
                                                          HorizontalAlignment="Left">
                                                <ToggleButton.Content>
                                                    <Path x:Name="BoldButtonPath"
                                                          Width="7"
                                                          Height="11"
                                                          Fill="{StaticResource IconColor}"
                                                          Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M1.22656 13.9062C1.0599 13.9062 0.901042 13.875 0.75 13.8125C0.604167 13.75 0.473958 13.6641 0.359375 13.5547C0.25 13.4453 0.161458 13.3177 0.09375 13.1719C0.03125 13.026 0 12.8698 0 12.7031V1.29688C0 1.125 0.0338542 0.960938 0.101562 0.804688C0.174479 0.648438 0.270833 0.510417 0.390625 0.390625C0.510417 0.270833 0.648438 0.177083 0.804688 0.109375C0.960938 0.0364583 1.125 0 1.29688 0H4.79688C5.34896 0 5.86719 0.106771 6.35156 0.320312C6.83594 0.528646 7.25781 0.815104 7.61719 1.17969C7.98177 1.53906 8.26823 1.96094 8.47656 2.44531C8.6901 2.92969 8.79688 3.44792 8.79688 4C8.79688 4.44271 8.72656 4.86458 8.58594 5.26562C8.45052 5.66146 8.25521 6.03906 8 6.39844C8.52083 6.80469 8.91667 7.29167 9.1875 7.85938C9.46354 8.42708 9.60156 9.04167 9.60156 9.70312C9.60156 10.1146 9.55208 10.5182 9.45312 10.9141C9.35417 11.3047 9.1875 11.6719 8.95312 12.0156C8.75521 12.3073 8.52344 12.5703 8.25781 12.8047C7.9974 13.0339 7.71354 13.2318 7.40625 13.3984C7.09896 13.5599 6.77344 13.6849 6.42969 13.7734C6.09115 13.8568 5.7474 13.8984 5.39844 13.8984C4.70052 13.8984 4.00521 13.901 3.3125 13.9062C2.61979 13.9062 1.92448 13.9062 1.22656 13.9062ZM4.70312 5.5C4.91146 5.5 5.10677 5.46094 5.28906 5.38281C5.47135 5.30469 5.63021 5.19792 5.76562 5.0625C5.90104 4.92708 6.00781 4.76823 6.08594 4.58594C6.16406 4.40365 6.20312 4.20833 6.20312 4C6.20312 3.79167 6.16406 3.59635 6.08594 3.41406C6.00781 3.23177 5.90104 3.07292 5.76562 2.9375C5.63021 2.80208 5.47135 2.69531 5.28906 2.61719C5.10677 2.53906 4.91146 2.5 4.70312 2.5H2.5V5.5H4.70312ZM5.29688 11.5C5.54167 11.5 5.76823 11.4557 5.97656 11.3672C6.1849 11.2734 6.36458 11.1484 6.51562 10.9922C6.66667 10.8359 6.78385 10.6536 6.86719 10.4453C6.95573 10.2318 7 10.0026 7 9.75781C7 9.52865 6.95312 9.3099 6.85938 9.10156C6.77083 8.88802 6.64844 8.70052 6.49219 8.53906C6.34115 8.3724 6.16146 8.24219 5.95312 8.14844C5.75 8.04948 5.53125 8 5.29688 8H2.5V11.5H5.29688Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </ToggleButton.Content>
                                            </ToggleButton>
                                            <ToggleButton x:Name="PART_ItalicToggleButton" 
                                                          Grid.Column="1" 
                                                          Focusable="False" 
                                                          IsChecked="{Binding Selection.CharacterFormat.Italic, Mode=TwoWay}"
                                                          Margin="0,0,0,3" 
                                                          Height="23" 
                                                          Width="23" 
                                                          Style="{StaticResource WPFFlatToggleButtonStyle}"
                                                          ToolTipService.ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarItalicToolTip}">
                                                <ToggleButton.Content>
                                                    <Path x:Name="italicButtonPath"
                                                          Width="7"
                                                          Height="11"
                                                          Fill="{StaticResource IconColor}"
                                                          Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M12.5 0C12.6354 0 12.7526 0.0494792 12.8516 0.148438C12.9505 0.247396 13 0.364583 13 0.5C13 0.635417 12.9505 0.752604 12.8516 0.851562C12.7526 0.950521 12.6354 1 12.5 1H9.34375L4.72656 13H8C8.13542 13 8.2526 13.0495 8.35156 13.1484C8.45052 13.2474 8.5 13.3646 8.5 13.5C8.5 13.6354 8.45052 13.7526 8.35156 13.8516C8.2526 13.9505 8.13542 14 8 14H0.5C0.364583 14 0.247396 13.9505 0.148438 13.8516C0.0494792 13.7526 0 13.6354 0 13.5C0 13.3646 0.0494792 13.2474 0.148438 13.1484C0.247396 13.0495 0.364583 13 0.5 13H3.65625L8.27344 1H5C4.86458 1 4.7474 0.950521 4.64844 0.851562C4.54948 0.752604 4.5 0.635417 4.5 0.5C4.5 0.364583 4.54948 0.247396 4.64844 0.148438C4.7474 0.0494792 4.86458 0 5 0H12.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </ToggleButton.Content>
                                            </ToggleButton>
                                            <ToggleButton x:Name="PART_UnderlineToggleButton" 
                                                          Grid.Column="2" 
                                                          Focusable="False"  
                                                          IsChecked="{Binding Selection.CharacterFormat.Underline, Mode=TwoWay, Converter={StaticResource UnderlineToggleconverter}}" 
                                                          Margin="0,0,3,3" 
                                                          Height="23" 
                                                          Width="23" 
                                                          Style="{StaticResource WPFFlatToggleButtonStyle}"
                                                          ToolTipService.ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarUnderlineToolTip}" 
                                                          HorizontalAlignment="Left">
                                                <ToggleButton.Content>
                                                    <Path x:Name="UnderlineButtonPath"
                                                          Width="7"
                                                          Height="12"
                                                          Fill="{StaticResource IconColor}"
                                                          Stretch="Fill">
                                                        <Path.Data>
                                                            <PathGeometry>M0 6.95312V0.5C0 0.364583 0.0494792 0.247396 0.148438 0.148438C0.247396 0.0494792 0.364583 0 0.5 0C0.635417 0 0.752604 0.0494792 0.851562 0.148438C0.950521 0.247396 1 0.364583 1 0.5V7.03125C1 7.57292 1.10677 8.08594 1.32031 8.57031C1.53385 9.04948 1.82292 9.46875 2.1875 9.82812C2.55208 10.1875 2.97656 10.474 3.46094 10.6875C3.94531 10.8958 4.45833 11 5 11C5.55729 11 6.07812 10.8932 6.5625 10.6797C7.05208 10.4661 7.47656 10.1771 7.83594 9.8125C8.19531 9.44271 8.47917 9.01302 8.6875 8.52344C8.89583 8.03385 9 7.51042 9 6.95312V0.5C9 0.364583 9.04948 0.247396 9.14844 0.148438C9.2474 0.0494792 9.36458 0 9.5 0C9.63542 0 9.7526 0.0494792 9.85156 0.148438C9.95052 0.247396 10 0.364583 10 0.5V6.95312C10 7.41667 9.9401 7.86198 9.82031 8.28906C9.70573 8.71615 9.53906 9.11719 9.32031 9.49219C9.10677 9.86719 8.84635 10.2083 8.53906 10.5156C8.23698 10.8229 7.90104 11.0859 7.53125 11.3047C7.16146 11.5234 6.76302 11.6953 6.33594 11.8203C5.90885 11.9401 5.46354 12 5 12C4.53646 12 4.09115 11.9401 3.66406 11.8203C3.23698 11.6953 2.83854 11.5234 2.46875 11.3047C2.09896 11.0859 1.76042 10.8229 1.45312 10.5156C1.15104 10.2083 0.890625 9.86719 0.671875 9.49219C0.458333 9.11719 0.291667 8.71615 0.171875 8.28906C0.0572917 7.85677 0 7.41146 0 6.95312ZM0.5 14C0.364583 14 0.247396 13.9505 0.148438 13.8516C0.0494792 13.7526 0 13.6354 0 13.5C0 13.3646 0.0494792 13.2474 0.148438 13.1484C0.247396 13.0495 0.364583 13 0.5 13H9.5C9.63542 13 9.7526 13.0495 9.85156 13.1484C9.95052 13.2474 10 13.3646 10 13.5C10 13.6354 9.95052 13.7526 9.85156 13.8516C9.7526 13.9505 9.63542 14 9.5 14H0.5Z</PathGeometry>
                                                        </Path.Data>
                                                    </Path>
                                                </ToggleButton.Content>
                                            </ToggleButton>
                                            <tools_controls_shared:ColorPickerPalette x:Name="PART_ColorPicker" 
                                                                                          Grid.Column="3" 
                                                                                          Margin="0,0,4,0" 
                                                                                          Height="24" 
                                                                                          Focusable="False"
                                                                                          BlackWhiteVisibility="Both"
                                                                                          RecentlyUsedPanelVisibility="Collapsed" 
                                                                                          MoreColorOptionVisibility="Collapsed"
                                                                                          Mode="Split"
                                                                                          Style="{StaticResource SyncfusionColorPickerPaletteStyle}">
                                                <tools_controls_shared:ColorPickerPalette.HeaderTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M3.53906 7.78125C3.51302 7.84896 3.5 7.91406 3.5 7.97656C3.5 8.11719 3.54948 8.23438 3.64844 8.32812C3.7526 8.42188 3.8724 8.46875 4.00781 8.46875C4.11198 8.46875 4.20312 8.4401 4.28125 8.38281C4.35938 8.32552 4.41927 8.25 4.46094 8.15625L5.33594 5.96875H8.66406L9.53906 8.15625C9.58073 8.25 9.64062 8.32552 9.71875 8.38281C9.80208 8.4401 9.89583 8.46875 10 8.46875C10.1354 8.46875 10.2526 8.42188 10.3516 8.32812C10.4505 8.22917 10.5 8.11198 10.5 7.97656C10.5 7.91406 10.487 7.84896 10.4609 7.78125L9.44531 5.25L7.46094 0.28125C7.41927 0.182292 7.35677 0.111979 7.27344 0.0703125C7.19531 0.0234375 7.10417 0 7 0C6.90625 0 6.8151 0.0234375 6.72656 0.0703125C6.63802 0.117188 6.57552 0.1875 6.53906 0.28125L4.55469 5.25L3.53906 7.78125ZM7 1.8125L8.26562 4.96875H5.73438L7 1.8125Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                            <Path  Fill="{Binding ElementName=PART_ColorPicker,Path=Color,Mode=OneWay, Converter={StaticResource FontColorIconColorConverter}}">
                                                                <Path.Data>
                                                                    <PathGeometry>M12.5 9.96875C12.6979 9.96875 12.8854 10.0104 13.0625 10.0938C13.2448 10.1771 13.4036 10.2891 13.5391 10.4297C13.6797 10.5651 13.7917 10.724 13.875 10.9062C13.9583 11.0833 14 11.2708 14 11.4688V14.4688C14 14.6667 13.9583 14.8568 13.875 15.0391C13.7917 15.2161 13.6797 15.375 13.5391 15.5156C13.4036 15.651 13.2448 15.7604 13.0625 15.8438C12.8854 15.9271 12.6979 15.9688 12.5 15.9688H1.5C1.30208 15.9688 1.11198 15.9271 0.929688 15.8438C0.752604 15.7604 0.59375 15.651 0.453125 15.5156C0.317708 15.375 0.208333 15.2161 0.125 15.0391C0.0416667 14.8568 0 14.6667 0 14.4688V11.4688C0 11.2708 0.0416667 11.0833 0.125 10.9062C0.208333 10.724 0.317708 10.5651 0.453125 10.4297C0.59375 10.2891 0.752604 10.1771 0.929688 10.0938C1.11198 10.0104 1.30208 9.96875 1.5 9.96875H12.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </tools_controls_shared:ColorPickerPalette.HeaderTemplate>
                                            </tools_controls_shared:ColorPickerPalette>
                                            <tools_controls_shared:SplitButtonAdv x:Name="PART_HighlightColorSplitButton" 
                                                                                  Style="{StaticResource SyncfusionToolbarSplitButtonAdvStyle}"
                                                                                  Focusable="False" 
                                                                                  Grid.Column="4" 
                                                                                  SizeMode="Small"
                                                                                  Margin="0,0,4,0" 
                                                                                  Height="24"
                                                                                  ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarHighlightColorToolTip}">
                                                <tools_controls_shared:SplitButtonAdv.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Fill="#FFFF00">
                                                                <Path.Data>
                                                                    <PathGeometry>M12.5 9.96875C12.6979 9.96875 12.8854 10.0104 13.0625 10.0938C13.2448 10.1771 13.4036 10.2891 13.5391 10.4297C13.6797 10.5651 13.7917 10.724 13.875 10.9062C13.9583 11.0833 14 11.2708 14 11.4688V14.4688C14 14.6667 13.9583 14.8568 13.875 15.0391C13.7917 15.2161 13.6797 15.375 13.5391 15.5156C13.4036 15.651 13.2448 15.7604 13.0625 15.8438C12.8854 15.9271 12.6979 15.9688 12.5 15.9688H1.5C1.30208 15.9688 1.11198 15.9271 0.929688 15.8438C0.752604 15.7604 0.59375 15.651 0.453125 15.5156C0.317708 15.375 0.208333 15.2161 0.125 15.0391C0.0416667 14.8568 0 14.6667 0 14.4688V11.4688C0 11.2708 0.0416667 11.0833 0.125 10.9062C0.208333 10.724 0.317708 10.5651 0.453125 10.4297C0.59375 10.2891 0.752604 10.1771 0.929688 10.0938C1.11198 10.0104 1.30208 9.96875 1.5 9.96875H12.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                            <Path Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M5.01685 6.79538C4.82312 6.11923 4.99254 5.36101 5.52513 4.82843L9.76777 0.585786C10.5488 -0.195262 11.8151 -0.195262 12.5962 0.585786C13.3772 1.36683 13.3772 2.63316 12.5962 3.41421L8.35355 7.65685C7.82097 8.18944 7.06275 8.35886 6.3866 8.16513C6.34467 8.23609 6.29321 8.30298 6.23223 8.36396L4.81802 9.77817C4.42749 10.1687 5 10 3 10C-1.4038 10.7218 3.01328 8.75449 3.40381 8.36396L4.81802 6.94975C4.879 6.88877 4.94589 6.83731 5.01685 6.79538ZM10.4749 1.29289L6.23223 5.53553C5.84171 5.92606 5.84171 6.55922 6.23223 6.94975C6.62276 7.34027 7.25592 7.34027 7.64645 6.94975L11.8891 2.70711C12.2796 2.31658 12.2796 1.68342 11.8891 1.29289C11.4986 0.902369 10.8654 0.902369 10.4749 1.29289Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </tools_controls_shared:SplitButtonAdv.IconTemplate>
                                                <Border BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Dark.ThemeCornerRadiusVariant1}"
                            
                            SnapsToDevicePixels="True">
                                                <Grid>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>
                                                    <Grid Grid.Row="0">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        <ToggleButton x:Name="PART_YellowHighlightButton"
                                                                      Grid.Column="0"
                                                                      Focusable="False"
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 0}" 
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="Yellow" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarYellowHighlightToolTip}"/>
                                                        <ToggleButton x:Name="PART_BrightGreenHighlightButton" 
                                                                      Grid.Column="1" 
                                                                      Focusable="False" 
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 1}" 
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="Green" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarBrightGreenHighlightToolTip}"/>
                                                        <ToggleButton x:Name="PART_TurquoiseHighlightButton" 
                                                                      Grid.Column="2" 
                                                                      Focusable="False" 
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 2}" 
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24"  
                                                                      Background="Turquoise"
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarTurquoiseHighlightToolTip}"/>
                                                        <ToggleButton x:Name="PART_PinkHighlightButton" 
                                                                      Grid.Column="3" 
                                                                      Focusable="False" 
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}"
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 3}" 
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="Pink"
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarPinkHighlightToolTip}" />
                                                        <ToggleButton x:Name="PART_BlueHighlightButton" 
                                                                      Grid.Column="4" 
                                                                      Focusable="False" 
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 4}"
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="Blue" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarBlueHighlightToolTip}"/>
                                                    </Grid>
                                                    <Grid Grid.Row="1">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        <ToggleButton x:Name="PART_RedHighlightButton" 
                                                                      Grid.Column="0"
                                                                      Focusable="False"
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 5}" 
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="Red" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarRedHighlightToolTip}"/>
                                                        <ToggleButton x:Name="PART_DarkBlueHighlightButton" 
                                                                      Grid.Column="1"
                                                                      Focusable="False" 
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}"
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 6}" 
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="DarkBlue"
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarDarkBlueHighlightToolTip}"/>
                                                        <ToggleButton x:Name="PART_TealHighlightButton" 
                                                                      Grid.Column="2" 
                                                                      Focusable="False" 
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 7}" 
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="Teal" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarTealHighlightToolTip}"/>
                                                        <ToggleButton x:Name="PART_GreenHighlightButton" 
                                                                      Grid.Column="3" 
                                                                      Focusable="False" 
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 8}" 
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24"
                                                                      Background="Green"
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarGreenHighlightToolTip}"/>
                                                        <ToggleButton x:Name="PART_VioletHighlightButton" 
                                                                      Grid.Column="4"
                                                                      Focusable="False" 
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 9}"
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="Violet" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarVioletHighlightToolTip}"/>
                                                    </Grid>
                                                    <Grid Grid.Row="2">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        <ToggleButton x:Name="PART_DarkRedHighlightButton" Focusable="False" Grid.Column="0" Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 10}"
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="DarkRed" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarDarkRedHighlightToolTip}"/>
                                                        <ToggleButton x:Name="PART_DarkYellowHighlightButton" 
                                                                      Focusable="False" 
                                                                      Grid.Column="1" 
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 11}"
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="DarkOrange" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarDarkYelowHighlightToolTip}"/>
                                                        <ToggleButton x:Name="PART_DarkGrayHighlightButton" 
                                                                      Focusable="False" 
                                                                      Grid.Column="2" 
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 12}" 
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="DarkGray" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarGray50HighlightToolTip}"/>
                                                        <ToggleButton x:Name="PART_LightGrayHighlightButton" 
                                                                      Focusable="False" 
                                                                      Grid.Column="3" 
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 13}" 
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="LightGray" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarGray25HighlightToolTip}"/>
                                                        <ToggleButton x:Name="PART_BlackHighlightButton" 
                                                                      Focusable="False" 
                                                                      Grid.Column="4" 
                                                                      Style="{StaticResource WPFHighlightBorderToggleButtonStyle}" 
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 14}"
                                                                      Margin="3" 
                                                                      Height="24" 
                                                                      Width="24" 
                                                                      Background="Black" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarBlackHighlightToolTip}"/>
                                                    </Grid>
                                                    <Grid Grid.Row="3">
                                                        <ToggleButton x:Name="PART_NoHighlightButton" 
                                                                      Focusable="False" 
                                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                                      IsChecked="{Binding Selection.CharacterFormat.HighlightColor, Mode=TwoWay, Converter={StaticResource HighlightColorConverter},ConverterParameter= 15}" 
                                                                      Margin="2" 
                                                                      Height="24" 
                                                                      Width="Auto" 
                                                                      BorderThickness="0" 
                                                                      HorizontalAlignment="Stretch" 
                                                                      HorizontalContentAlignment="Left" 
                                                                      Background="Transparent" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNoHighlightToolTip}">
                                                            <ToggleButton.Content>
                                                                <StackPanel Orientation="Horizontal">
                                                                        <Border Height="16" 
                                                                                Width="16" 
                                                                                Margin="8 0 8 0"
                                                                            HorizontalAlignment="Left" 
                                                                            Background="Transparent" 
                                                                            BorderBrush="{Binding RelativeSource={RelativeSource AncestorType=ToggleButton}, Path=Foreground}"
                                                                            BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"/>
                                                                        <TextBlock VerticalAlignment="Center"
                                                                               HorizontalAlignment="Stretch" 
                                                                               Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNoColorHighlight}" />
                                                                </StackPanel>
                                                            </ToggleButton.Content>
                                                        </ToggleButton>
                                                    </Grid>
                                                </Grid>
                                                </Border>
                                            </tools_controls_shared:SplitButtonAdv>
                                            <Button x:Name="PART_AlignmentButton" 
                                                    Grid.Column="5"
                                                    Focusable="False" 
                                                    Margin="0,0,4,0" 
                                                    Height="23" 
                                                    Width="29" 
                                                    Style="{StaticResource WPFGlyphButtonStyle}" 
                                                    ToolTipService.ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarAlignmentToolTip}">
                                                <Button.Content>
                                                    <Image Source="/Syncfusion.SfRichTextBoxAdv.WPF;component/Images/LeftAlignment_IconMiniToolBar.png" Height="16" Width="26"/>
                                                </Button.Content>
                                            </Button>
                                            <tools_controls_shared:SplitButtonAdv 
                                                x:Name="PART_BulletedListSplitButton" 
                                                Style="{StaticResource SyncfusionFlatSplitButtonAdvStyle}"
                                                Focusable="False" 
                                                Grid.Column="6" 
                                                SizeMode="Small" 
                                                Margin="0,0,4,0"
                                                ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarBulletToolTip}">
                                                <tools_controls_shared:SplitButtonAdv.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Fill="{StaticResource PrimaryBackground}">
                                                                <Path.Data>
                                                                    <PathGeometry>M0 1C0 0.859375 0.0260417 0.729167 0.078125 0.609375C0.130208 0.489583 0.200521 0.385417 0.289062 0.296875C0.382812 0.203125 0.489583 0.130208 0.609375 0.078125C0.729167 0.0260417 0.859375 0 1 0C1.13542 0 1.26302 0.0260417 1.38281 0.078125C1.50781 0.130208 1.61458 0.203125 1.70312 0.296875C1.79688 0.385417 1.86979 0.492188 1.92188 0.617188C1.97396 0.736979 2 0.864583 2 1C2 1.14062 1.97396 1.27083 1.92188 1.39062C1.86979 1.51042 1.79688 1.61719 1.70312 1.71094C1.61458 1.79948 1.51042 1.86979 1.39062 1.92188C1.27083 1.97396 1.14062 2 1 2C0.859375 2 0.726562 1.97396 0.601562 1.92188C0.481771 1.86979 0.377604 1.79948 0.289062 1.71094C0.200521 1.6224 0.130208 1.51823 0.078125 1.39844C0.0260417 1.27344 0 1.14062 0 1Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                            <Path Fill="{StaticResource PrimaryBackground}">
                                                                <Path.Data>
                                                                    <PathGeometry>M0 6C0 5.85938 0.0260417 5.72917 0.078125 5.60938C0.130208 5.48958 0.200521 5.38542 0.289062 5.29688C0.382812 5.20312 0.489583 5.13021 0.609375 5.07812C0.729167 5.02604 0.859375 5 1 5C1.13542 5 1.26302 5.02604 1.38281 5.07812C1.50781 5.13021 1.61458 5.20312 1.70312 5.29688C1.79688 5.38542 1.86979 5.49219 1.92188 5.61719C1.97396 5.73698 2 5.86458 2 6C2 6.14062 1.97396 6.27083 1.92188 6.39062C1.86979 6.51042 1.79688 6.61719 1.70312 6.71094C1.61458 6.79948 1.51042 6.86979 1.39062 6.92188C1.27083 6.97396 1.14062 7 1 7C0.859375 7 0.726562 6.97396 0.601562 6.92188C0.481771 6.86979 0.377604 6.79948 0.289062 6.71094C0.200521 6.6224 0.130208 6.51823 0.078125 6.39844C0.0260417 6.27344 0 6.14062 0 6Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                            <Path Fill="{StaticResource IconColor}">
                                                                <Path.Data>M4.14844 1.35156C4.2474 1.45052 4.36458 1.5 4.5 1.5H15.5C15.6354 1.5 15.7526 1.45052 15.8516 1.35156C15.9505 1.2526 16 1.13542 16 1C16 0.864583 15.9505 0.747396 15.8516 0.648438C15.7526 0.549479 15.6354 0.5 15.5 0.5H4.5C4.36458 0.5 4.2474 0.549479 4.14844 0.648438C4.04948 0.747396 4 0.864583 4 1C4 1.13542 4.04948 1.2526 4.14844 1.35156ZM4.14844 6.35156C4.2474 6.45052 4.36458 6.5 4.5 6.5H15.5C15.6354 6.5 15.7526 6.45052 15.8516 6.35156C15.9505 6.2526 16 6.13542 16 6C16 5.86458 15.9505 5.7474 15.8516 5.64844C15.7526 5.54948 15.6354 5.5 15.5 5.5H4.5C4.36458 5.5 4.2474 5.54948 4.14844 5.64844C4.04948 5.7474 4 5.86458 4 6C4 6.13542 4.04948 6.2526 4.14844 6.35156ZM4.14844 11.3516C4.2474 11.4505 4.36458 11.5 4.5 11.5H15.5C15.6354 11.5 15.7526 11.4505 15.8516 11.3516C15.9505 11.2526 16 11.1354 16 11C16 10.8646 15.9505 10.7474 15.8516 10.6484C15.7526 10.5495 15.6354 10.5 15.5 10.5H4.5C4.36458 10.5 4.2474 10.5495 4.14844 10.6484C4.04948 10.7474 4 10.8646 4 11C4 11.1354 4.04948 11.2526 4.14844 11.3516Z</Path.Data>
                                                            </Path>
                                                            <Path Fill="{StaticResource PrimaryBackground}">
                                                                <Path.Data>
                                                                    <PathGeometry>M0 11C0 10.8594 0.0260417 10.7292 0.078125 10.6094C0.130208 10.4896 0.200521 10.3854 0.289062 10.2969C0.382812 10.2031 0.489583 10.1302 0.609375 10.0781C0.729167 10.026 0.859375 10 1 10C1.13542 10 1.26302 10.026 1.38281 10.0781C1.50781 10.1302 1.61458 10.2031 1.70312 10.2969C1.79688 10.3854 1.86979 10.4922 1.92188 10.6172C1.97396 10.737 2 10.8646 2 11C2 11.1406 1.97396 11.2708 1.92188 11.3906C1.86979 11.5104 1.79688 11.6172 1.70312 11.7109C1.61458 11.7995 1.51042 11.8698 1.39062 11.9219C1.27083 11.974 1.14062 12 1 12C0.859375 12 0.726562 11.974 0.601562 11.9219C0.481771 11.8698 0.377604 11.7995 0.289062 11.7109C0.200521 11.6224 0.130208 11.5182 0.078125 11.3984C0.0260417 11.2734 0 11.1406 0 11Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </tools_controls_shared:SplitButtonAdv.IconTemplate>
                                                <Border BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="{StaticResource Windows11Dark.ThemeCornerRadiusVariant1}"
                                                SnapsToDevicePixels="True">
                                                <Grid>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="12"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="12"/>
                                                    </Grid.RowDefinitions>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="12"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="12"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarBulletsHeader}" FontWeight="Bold" Padding="4 0 0 0" Margin="2 6" VerticalAlignment="Top" SnapsToDevicePixels="True" />
                                                    <Grid Grid.Row="2" Grid.Column="1">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        <ToggleButton  
                                                                      x:Name="PART_NoBulletButton" 
                                                                      Focusable="False"
                                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                                      Grid.Column="0" 
                                                                      BorderThickness="0" 
                                                                      Background="Transparent" 
                                                                      Padding="4" 
                                                                      HorizontalContentAlignment="Left" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNoListToolTip}">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Bullets_None"
                                                                      Background="Transparent" 
                                                                      HorizontalAlignment="Left" 
                                                                      Height="40" 
                                                                      VerticalAlignment="Top" 
                                                                      Width="40">
                                                                        <Path
                                                                        Fill="Transparent" 
                                                                        Margin="0.5" 
                                                                        Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L39,0 39,39 0,39 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                        Fill="{StaticResource IconColor}" 
                                                                        Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path Fill="{StaticResource IconColor}"
                                                                          Margin="7.449,16.465,7.849,15.607"
                                                                          Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M22.333007,2.7070129 C21.957,2.707013 21.640006,2.8580138 21.380004,3.160992 21.120002,3.4640006 20.954001,3.8559983 20.883002,4.3400065 L23.708008,4.3400065 23.708008,4.1999923 C23.708008,3.7670091 23.594002,3.410015 23.364006,3.1290095 23.135002,2.848004 22.791,2.707013 22.333007,2.7070129 z M9.9689951,2.7069989 C9.4389954,2.7069988 9.0409956,2.9089988 8.7759953,3.3139987 8.5119953,3.7179987 8.3789959,4.2319986 8.3789959,4.8549985 L8.3789959,4.9839984 C8.3789959,5.6179985 8.5119953,6.1329983 8.7759953,6.5309981 9.0409956,6.9279979 9.4419956,7.1269983 9.9799957,7.1269983 10.509995,7.1269983 10.908996,6.926998 11.176995,6.5279983 11.445995,6.1289982 11.579995,5.6139983 11.579995,4.9839984 L11.579995,4.8549985 C11.579995,4.2319986 11.444995,3.7179987 11.174995,3.3139987 10.903996,2.9089988 10.501995,2.7069988 9.9689951,2.7069989 z M16.590996,1.9020022 C17.225996,1.9020023 17.709996,2.0800021 18.046996,2.4390017 18.383996,2.7970013 18.551996,3.3670007 18.551996,4.1529998 L18.551996,7.8209957 17.552996,7.8209957 17.552996,4.1739999 C17.552996,3.6400003 17.451996,3.2660007 17.249996,3.0510012 17.046996,2.8360013 16.729996,2.7290013 16.295996,2.7290014 15.969996,2.7290013 15.688997,2.8060013 15.450997,2.9590012 15.211997,3.113001 15.019997,3.3280007 14.872997,3.6040006 L14.872997,7.8209957 13.874998,7.8209957 13.874998,2.0090022 14.770997,2.0090022 14.840997,2.8790012 14.856997,2.885001 C15.045997,2.5720015 15.287997,2.3310018 15.579997,2.1590019 15.871997,1.9880022 16.207996,1.9020023 16.590996,1.9020022 z M22.333007,1.9010142 C23.121002,1.9010143 23.713005,2.1330086 24.108002,2.5940064 24.504005,3.0570186 24.702004,3.6879992 24.702004,4.4899999 L24.702004,5.1410002 20.851005,5.1410002 C20.865004,5.734017 21.012999,6.2150038 21.294005,6.5799936 21.575004,6.9450139 21.975005,7.1269902 22.494003,7.1269902 22.855003,7.1269902 23.168007,7.0770024 23.432006,6.9769963 23.694008,6.8769907 23.932007,6.7349924 24.144005,6.5530161 L24.535004,7.2129887 C24.324005,7.4209961 24.053001,7.5919861 23.722008,7.7269953 23.391006,7.859991 22.981003,7.9280146 22.494003,7.9280146 21.678001,7.9280146 21.033004,7.6640071 20.561003,7.1359929 20.088004,6.6070017 19.852004,5.9060138 19.852003,5.0329987 L19.852003,4.7909948 C19.852004,3.9459945 20.094,3.2530023 20.579001,2.7129943 21.064002,2.1720101 21.649001,1.9010143 22.333007,1.9010142 z M9.9689951,1.9009991 C10.784995,1.900999 11.423995,2.1739989 11.883995,2.7199987 12.343995,3.2659987 12.572995,3.9779986 12.572995,4.8549985 L12.572995,4.9839984 C12.572995,5.8649982 12.344995,6.5759982 11.885995,7.1169981 11.427995,7.656998 10.791995,7.926998 9.9799957,7.926998 9.1629953,7.926998 8.5249958,7.656998 8.0669956,7.1169981 7.6089954,6.5759982 7.3799953,5.8649982 7.3799953,4.9839984 L7.3799953,4.8549985 C7.3799953,3.9779986 7.6089954,3.2659987 8.0669956,2.7199987 8.5249958,2.1739989 9.1589956,1.900999 9.9689951,1.9009991 z M0,0 L0.99299812,0 4.8769979,6.1550182 4.8939981,6.1550182 4.8939981,0 5.8869982,0 5.8869982,7.8200229 4.8939981,7.8200229 1.0099983,1.6600049 0.99299812,1.6600049 0.99299812,7.8200229 0,7.8200229 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                        <ToggleButton 
                                                            Focusable="False"
                                                            x:Name="PART_DotBulletButton" 
                                                            Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                            Grid.Column="1" 
                                                            BorderThickness="0" 
                                                            Background="Transparent" 
                                                            Padding="4" 
                                                            HorizontalContentAlignment="Left">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Bullets_Dot" 
                                                                  Background="Transparent" 
                                                                  HorizontalAlignment="Left" 
                                                                  Height="40" 
                                                                  VerticalAlignment="Top" 
                                                                  Width="40">
                                                                        <Path 
                                                                 Fill="Transparent" 
                                                                 Margin="0.5" 
                                                                 Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L39,0 39,39 0,39 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                 Fill="{StaticResource IconColor}" 
                                                                 Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path
                                                                 Fill="{StaticResource IconColor}"
                                                                 Margin="14,16,15,11"
                                                                 Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M5.5,0 C8.5370026,0 11,2.4630127 11,5.5 11,8.5369873 8.5370026,11 5.5,11 2.4629974,11 0,8.5369873 0,5.5 0,2.4630127 2.4629974,0 5.5,0 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                        <ToggleButton 
                                                            Focusable="False" 
                                                            x:Name="PART_CircleBulletButton" 
                                                            Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                            Grid.Column="2" 
                                                            BorderThickness="0" 
                                                            Background="Transparent" 
                                                            Padding="4" 
                                                            HorizontalContentAlignment="Left">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Bullets_Circle" 
                                                                  Background="Transparent" 
                                                                  HorizontalAlignment="Left" 
                                                                  Height="40" 
                                                                  VerticalAlignment="Top" 
                                                                  Width="40">
                                                                        <Path Data="M0,0 L39,0 39,39 0,39 z" 
                                                                      Fill="Transparent" 
                                                                      Margin="0.5" 
                                                                      Stretch="Fill" />
                                                                        <Path 
                                                                     Fill="{StaticResource IconColor}" 
                                                                     Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path
                                                                     Fill="{StaticResource IconColor}"
                                                                     Margin="14,16,15,11"
                                                                     Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M7.5,0.99999991 C3.9160004,1 1,3.6910096 0.99999994,7 1,10.308991 3.9160004,13 7.5,13 11.084,13 14,10.308991 14,7 14,3.6910096 11.084,1 7.5,0.99999991 z M7.5,0 C11.636002,2.9802322E-08 15,3.1409912 15,7 15,10.859009 11.636002,14 7.5,14 3.3639984,14 0,10.859009 0,7 0,3.1409912 3.3639984,2.9802322E-08 7.5,0 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                        <ToggleButton 
                                                            Focusable="False" 
                                                            x:Name="PART_SquareBulletButton" 
                                                            Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                            Grid.Column="3" 
                                                            BorderThickness="0" 
                                                            Background="Transparent" 
                                                            Padding="4" 
                                                            HorizontalContentAlignment="Left">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Bullets_Square" 
                                                                  Background="Transparent" 
                                                                  HorizontalAlignment="Left" 
                                                                  Height="40" 
                                                                  VerticalAlignment="Top" 
                                                                  Width="40">
                                                                        <Path Data="M0,0 L39,0 39,39 0,39 z" 
                                                                      Fill="Transparent" 
                                                                      Margin="0.5" 
                                                                      Stretch="Fill" />
                                                                        <Path 
                                                                    Fill="{StaticResource IconColor}" 
                                                                    Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path
                                                                     Fill="{StaticResource IconColor}"
                                                                     Margin="16,15,15,16"
                                                                     Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L9,0 9,9 0,9 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                    </Grid>
                                                    <Grid Grid.Row="3" Grid.Column="1">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        <ToggleButton 
                                                            Focusable="False" 
                                                            x:Name="PART_FlowerBulletButton"
                                                            Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
                                                            Grid.Column="0" 
                                                            BorderThickness="0" 
                                                            Background="Transparent" 
                                                            Padding="4" 
                                                            HorizontalContentAlignment="Left">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Bullets_Flower" 
                                                                  Background="Transparent" 
                                                                  HorizontalAlignment="Left" 
                                                                  Height="40" 
                                                                  VerticalAlignment="Top" 
                                                                  Width="40">
                                                                        <Path Data="M0,0 L39,0 39,39 0,39 z" 
                                                                      Fill="Transparent" 
                                                                      Margin="0.5" 
                                                                      Stretch="Fill" />
                                                                        <Path 
                                                                    Fill="{StaticResource IconColor}" 
                                                                    Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path
                                                                    Fill="{StaticResource IconColor}"
                                                                    Margin="9,8,8,9"
                                                                    Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M11.5,14.328001 L15.835999,18.664002 11.5,23.000001 7.1639996,18.664002 z M18.664,7.1640015 L23,11.500002 18.664,15.836002 14.328,11.500002 z M4.3360004,7.1640015 L8.6720003,11.500002 4.3360004,15.836002 0,11.500002 z M11.5,0 L15.836,4.3360004 11.5,8.6720014 7.1640003,4.3360004 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                        <ToggleButton Focusable="False" 
                                                                      x:Name="PART_ArrowBulletButton" 
                                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                                      Grid.Column="1" 
                                                                      BorderThickness="0" 
                                                                      Background="Transparent" 
                                                                      Padding="4" 
                                                                      HorizontalContentAlignment="Left">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Bullets_Arrow" 
                                                                  Background="Transparent" 
                                                                  HorizontalAlignment="Left" 
                                                                  Height="40" 
                                                                  VerticalAlignment="Top" 
                                                                  Width="40">
                                                                        <Path 
                                                                  Fill="Transparent" 
                                                                  Margin="0.5" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L39,0 39,39 0,39 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                  Fill="{StaticResource IconColor}" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path
                                                                    Fill="{StaticResource IconColor}"
                                                                    Margin="9.181,7.2,9.502,8.146"
                                                                    Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M2.6369915,2.600007 L7.8879943,11.789005 7.8822666,11.799983 19.284758,11.799983 z M0,0 L21.316999,11.78101 0.049011266,24.654001 6.7489877,11.811009 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                        <ToggleButton Focusable="False" 
                                                                      x:Name="PART_TickBulletButton" 
                                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                                      Grid.Column="2" 
                                                                      BorderThickness="0" 
                                                                      Background="Transparent" 
                                                                      Padding="4" 
                                                                      HorizontalContentAlignment="Left">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Bullets_Tick" 
                                                                  Background="Transparent" 
                                                                  HorizontalAlignment="Left" 
                                                                  Height="40" 
                                                                  VerticalAlignment="Top" 
                                                                  Width="40">
                                                                        <Path 
                                                                    Fill="Transparent" 
                                                                    Margin="0.5" 
                                                                    Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L39,0 39,39 0,39 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                    Fill="{StaticResource IconColor}" 
                                                                    Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M1.0000002,1.0000002 L1.0000002,39 39,39 39,1.0000002 z M0,0 L40,0 40,40 0,40 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                    Fill="{StaticResource IconColor}"
                                                                    Margin="10.512,6.54,8.468,13.165"
                                                                    Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M21.019999,0 C21.019999,-1.0224994E-07 16.818007,4.3180208 13.771014,8.1249947 10.075002,12.741996 7.2249994,16.943012 5.0140028,20.295 5.0140028,20.295 2.0540142,12.986991 0,12.020988 L2.4770179,10.33001 C2.4770179,10.33001 4.2520099,12.261009 6.0039921,14.797994 6.0039921,14.797994 11.145009,5.3760032 21.019999,0 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                    </Grid>
                                                </Grid>
                                                </Border>
                                            </tools_controls_shared:SplitButtonAdv>
                                            <tools_controls_shared:SplitButtonAdv x:Name="PART_NumberedListSplitButton" 
                                                                                  Style="{StaticResource SyncfusionFlatSplitButtonAdvStyle}"
                                                                                  Focusable="False"
                                                                                  SizeMode="Small"
                                                                                  Grid.Column="7" 
                                                                                  Margin="0,0,4,0"
                                                                                  ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNumberingToolTip}">
                                                <tools_controls_shared:SplitButtonAdv.IconTemplate>
                                                    <DataTemplate>
                                                        <Grid>
                                                            <Path Fill="{StaticResource PrimaryBackground}">
                                                                <Path.Data>
                                                                    <PathGeometry>M0.615234 0.847656V2.86328H1.44727V0H0.957031C0.798157 0.111977 0.642578 0.200523 0.490234 0.265625C0.339172 0.32943 0.175781 0.376305 0 0.40625V1.03125C0.123718 1.01823 0.234375 0.998047 0.332031 0.970703C0.429688 0.942055 0.524109 0.901039 0.615234 0.847656Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                            <Path Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M4.5 2C4.36456 2 4.24738 1.95052 4.14844 1.85156C4.0495 1.7526 4 1.63541 4 1.5C4 1.36459 4.0495 1.2474 4.14844 1.14844C4.24738 1.04948 4.36456 1 4.5 1H15.5C15.6354 1 15.7526 1.04948 15.8516 1.14844C15.9505 1.2474 16 1.36459 16 1.5C16 1.63541 15.9505 1.7526 15.8516 1.85156C15.7526 1.95052 15.6354 2 15.5 2H4.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                            <Path Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M4.5 7C4.36456 7 4.24738 6.95052 4.14844 6.85156C4.0495 6.7526 4 6.63541 4 6.5C4 6.36459 4.0495 6.2474 4.14844 6.14844C4.24738 6.04948 4.36456 6 4.5 6H15.5C15.6354 6 15.7526 6.04948 15.8516 6.14844C15.9505 6.2474 16 6.36459 16 6.5C16 6.63541 15.9505 6.7526 15.8516 6.85156C15.7526 6.95052 15.6354 7 15.5 7H4.5Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                            <Path Fill="{StaticResource IconColor}">
                                                                <Path.Data>
                                                                    <PathGeometry>M4.14844 11.8516C4.24738 11.9505 4.36456 12 4.5 12H15.5C15.6354 12 15.7526 11.9505 15.8516 11.8516C15.9505 11.7526 16 11.6354 16 11.5C16 11.3646 15.9505 11.2474 15.8516 11.1484C15.7526 11.0495 15.6354 11 15.5 11H4.5C4.36456 11 4.24738 11.0495 4.14844 11.1484C4.0495 11.2474 4 11.3646 4 11.5C4 11.6354 4.0495 11.7526 4.14844 11.8516Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                            <Path Fill="{StaticResource PrimaryBackground}">
                                                                <Path.Data>
                                                                    <PathGeometry>M0 7.62305V7.84766H1.97266V7.17969H0.878906C0.880188 7.14193 0.910156 7.09635 0.96875 7.04297C1.02863 6.98828 1.14911 6.90234 1.33008 6.78516C1.56708 6.63281 1.72919 6.48633 1.81641 6.3457C1.90363 6.20508 1.94727 6.03385 1.94727 5.83203C1.94727 5.56901 1.86395 5.36459 1.69727 5.21875C1.53058 5.07291 1.29492 5 0.990234 5C0.848328 5 0.709656 5.01562 0.574219 5.04688C0.440125 5.07812 0.295593 5.13281 0.140625 5.21094V5.89258C0.34375 5.72721 0.559265 5.64453 0.787109 5.64453C1.01105 5.64453 1.12305 5.73763 1.12305 5.92383C1.12305 5.97982 1.11066 6.0319 1.08594 6.08008C1.0625 6.12826 1.02606 6.17838 0.976562 6.23047C0.928406 6.28125 0.830078 6.36523 0.681641 6.48242C0.473328 6.64388 0.326843 6.77604 0.242188 6.87891C0.158875 6.98177 0.0976562 7.0918 0.0585938 7.20898C0.0195312 7.32617 0 7.4642 0 7.62305Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                            <Path Fill="{StaticResource PrimaryBackground}">
                                                                <Path.Data>
                                                                    <PathGeometry>M1.58203 12.6738C1.77734 12.5254 1.875 12.3164 1.875 12.0469C1.875 11.875 1.82031 11.7305 1.71094 11.6133C1.60156 11.4948 1.45245 11.4225 1.26367 11.3965V11.3867C1.43427 11.3503 1.56641 11.2728 1.66016 11.1543C1.75519 11.0358 1.80273 10.8893 1.80273 10.7148C1.80273 10.4909 1.71484 10.3158 1.53906 10.1895C1.36328 10.0631 1.12042 10 0.810547 10C0.560547 10 0.322266 10.0469 0.0957031 10.1406V10.7637C0.276672 10.6595 0.457703 10.6074 0.638672 10.6074C0.755859 10.6074 0.842468 10.6289 0.898438 10.6719C0.95575 10.7148 0.984375 10.7734 0.984375 10.8477C0.984375 10.9258 0.946594 10.9896 0.871094 11.0391C0.796875 11.0872 0.680969 11.1113 0.523438 11.1113H0.285156V11.7246H0.554688C0.710938 11.7246 0.832703 11.75 0.919922 11.8008C1.00714 11.8516 1.05078 11.9219 1.05078 12.0117C1.05078 12.1003 1.01495 12.1686 0.943359 12.2168C0.873047 12.2637 0.771484 12.2871 0.638672 12.2871C0.524109 12.2871 0.410828 12.2721 0.298828 12.2422C0.186829 12.2122 0.0872192 12.1712 0 12.1191V12.7715C0.0950317 12.8092 0.208984 12.8392 0.341797 12.8613C0.474609 12.8848 0.608704 12.8965 0.744141 12.8965C1.10742 12.8965 1.38672 12.8223 1.58203 12.6738Z</PathGeometry>
                                                                </Path.Data>
                                                            </Path>
                                                        </Grid>
                                                    </DataTemplate>
                                                </tools_controls_shared:SplitButtonAdv.IconTemplate>
                                                <Border BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="{StaticResource Windows11Dark.ThemeCornerRadiusVariant1}"
                                                
                                                Background="{StaticResource ContentBackground}"
                                                SnapsToDevicePixels="True">
                                                <Grid Background="{StaticResource ContentBackground}">
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="12"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="12"/>
                                                    </Grid.RowDefinitions>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="12"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="12"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNumberingHeader}" FontWeight="Bold" Padding="4 0 0 0" Margin="2 6" VerticalAlignment="Top" SnapsToDevicePixels="True" />
                                                    <Grid Grid.Row="2" Grid.Column="1">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        <ToggleButton Focusable="False" 
                                                                      Grid.Column="0" 
                                                                      x:Name="PART_NoNumberingButton" 
                                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                                      BorderThickness="0" 
                                                                      Background="Transparent" 
                                                                      Padding="4" 
                                                                      HorizontalContentAlignment="Left" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNoListToolTip}">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Numbering_None" 
                                                                  HorizontalAlignment="Left" 
                                                                  Height="76" 
                                                                  VerticalAlignment="Top" 
                                                                  Width="76">
                                                                        <Path
                                                                  Fill="Transparent" 
                                                                  Margin="0.5" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path  
                                                                  Fill="{StaticResource IconColor}"
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L76,0 76,76 0,76 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path   
                                                                  Fill="{StaticResource IconColor}"
                                                                  Margin="24.436,33.863,23.809,33.228" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M11.200999,3.0420207 C10.604999,3.0420207 10.159999,3.2690196 9.8609991,3.7240174 9.5639982,4.1790152 9.4159985,4.7560122 9.4159985,5.4560087 L9.4159985,5.6010079 C9.4159985,6.3130043 9.5639982,6.8920014 9.8609991,7.3389995 10.159999,7.7859972 10.608999,8.0089962 11.212999,8.0089962 11.808999,8.0089962 12.257999,7.7849972 12.560999,7.3359993 12.860999,6.8870018 13.011999,6.3090045 13.011999,5.6010079 L13.011999,5.4560087 C13.011999,4.7560122 12.860999,4.1790152 12.556999,3.7240174 12.251999,3.2690196 11.801,3.0420207 11.200999,3.0420207 z M25.095003,3.0420025 C24.671,3.0420024 24.315996,3.2120464 24.022997,3.5520124 23.731996,3.8920395 23.545999,4.3330188 23.463998,4.8760488 L26.640002,4.8760488 26.640002,4.7200429 C26.640002,4.2320056 26.511004,3.8310043 26.253001,3.5160019 25.994998,3.1990459 25.609004,3.0420024 25.095003,3.0420025 z M25.095003,2.1370339 C25.979998,2.1370336 26.644,2.3960059 27.089,2.9150494 27.534,3.4340314 27.755001,4.1440539 27.755001,5.0450559 L27.755001,5.7750366 23.429002,5.7750366 C23.445001,6.4430053 23.611001,6.9820073 23.926996,7.393018 24.240999,7.8040292 24.693003,8.0090463 25.275004,8.0090463 25.681002,8.0090463 26.032,7.9520395 26.328998,7.84004 26.624003,7.7270029 26.890002,7.5680063 27.128002,7.3620121 L27.570003,8.1050546 C27.331005,8.3390028 27.027003,8.5300429 26.654002,8.6820204 26.282,8.8320448 25.824,8.9080336 25.275004,8.9080336 24.359003,8.9080336 23.633996,8.6110365 23.102998,8.0180185 22.572,7.4240239 22.306001,6.6370361 22.306,5.6550412 L22.306,5.3830075 C22.306001,4.434032 22.578996,3.6550399 23.124002,3.0480449 23.669,2.4400123 24.324999,2.1370336 25.095003,2.1370339 z M11.200999,2.1370253 C12.118999,2.1370253 12.835999,2.4440237 13.353999,3.0570209 13.870999,3.6700176 14.129,4.4700136 14.129,5.4560087 L14.129,5.6010079 C14.129,6.5900033 13.870999,7.3889992 13.355,7.9969962 12.841999,8.6049931 12.126999,8.9089916 11.212999,8.9089916 10.296999,8.9089916 9.579999,8.6049931 9.0639982,7.9969962 8.5509987,7.3889992 8.2929983,6.5900033 8.2929983,5.6010079 L8.2929983,5.4560087 C8.2929983,4.4700136 8.5509987,3.6700176 9.0639982,3.0570209 9.579999,2.4440237 10.292999,2.1370253 11.200999,2.1370253 z M18.642,2.136981 C19.355,2.1369811 19.900001,2.3379822 20.279001,2.7399844 20.656001,3.1429867 20.845001,3.7839901 20.845001,4.6649947 L20.845001,8.7870176 19.722,8.7870176 19.722,4.6899955 C19.722001,4.0899919 19.609,3.6689895 19.382,3.4279879 19.156,3.1869869 18.798,3.0659862 18.31,3.0659861 17.944999,3.0659862 17.627999,3.1529868 17.360999,3.3249875 17.092999,3.4979886 16.875999,3.7389899 16.711998,4.0499917 L16.711998,8.7870176 15.588998,8.7870176 15.588998,2.2569817 16.596999,2.2569817 16.674999,3.2349871 16.692999,3.239987 C16.905999,2.8909853 17.176999,2.6189838 17.504999,2.4259828 17.832999,2.2329816 18.211999,2.1369811 18.642,2.136981 z M0,0 L1.1169987,0 5.4800014,6.9160097 5.4980011,6.9160097 5.4980011,0 6.6150017,0 6.6150017,8.7870123 5.4980011,8.7870123 1.1350002,1.8640026 1.1169987,1.8640026 1.1169987,8.7870123 0,8.7870123 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                        <ToggleButton Focusable="False" 
                                                                          Grid.Column="1" 
                                                                          x:Name="PART_NumberDotNumberingButton" 
                                                                          Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                                          BorderThickness="0" 
                                                                          Background="Transparent" 
                                                                          Padding="4" 
                                                                          HorizontalContentAlignment="Left" 
                                                                          ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNumberingAlignLeftToolTip}">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Numbering_Dot" 
                                                                  HorizontalAlignment="Left" 
                                                                  Height="76" 
                                                                  VerticalAlignment="Top" 
                                                                  Width="76">
                                                                        <Path 
                                                                  Fill="Transparent" 
                                                                  Margin="0.5" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                  Fill="{StaticResource IconColor}" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L76,0 76,76 0,76 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path Fill="{StaticResource IconColor}"
                                                                      HorizontalAlignment="Left" 
                                                                      Margin="6.925,12.339,0,17.007" 
                                                                      Stretch="Fill" 
                                                                      Width="8.244">
                                                                            <Path.Data>
                                                                                <PathGeometry>M7.1420118,45.376981 L8.2440114,45.376981 8.2440114,46.530981 7.1420118,46.530981 z M2.7070026,37.877029 C3.5110023,37.877029 4.1420021,38.083028 4.5990024,38.495026 5.0560019,38.907024 5.2850019,39.508022 5.285002,40.297019 5.2850019,40.660017 5.1780019,41.014015 4.963002,41.357013 4.7480021,41.701012 4.4220021,41.967011 3.9840024,42.15401 4.5110021,42.32201 4.883002,42.584008 5.0970023,42.939007 5.3120018,43.295005 5.4200019,43.701003 5.420002,44.158001 5.4200019,44.950998 5.1700019,45.564995 4.6700022,46.000993 4.1700022,46.436991 3.5170023,46.65399&#xd;&#xa;2.7130027,46.65399 1.9240026,46.65399 1.275003,46.445991 0.76700258,46.029993 0.26000309,45.613995 0.0060033798,45.028998 0.0060033798,44.275001 L1.0950031,44.275001 C1.0950031,44.739999 1.2380028,45.106997 1.5230026,45.376996 1.8080029,45.645995 2.2050028,45.780994 2.7130027,45.780994 3.2320025,45.780994 3.6330023,45.648995 3.9140022,45.385996 4.1950021,45.121997 4.3360021,44.720999 4.3360023,44.182001 4.3360021,43.639004 4.2070022,43.242005 3.949002,42.992007 3.6910024,42.742008 3.2810025,42.617008 2.7190032,42.617008 L1.7400026,42.617008 1.7400026,41.750012 2.7190032,41.750012 C3.2580025,41.750012 3.6390023,41.621012 3.8640022,41.363014 4.0890021,41.105015 4.2010021,40.742016 4.2010024,40.273019 4.2010021,39.77002 4.0780022,39.391022 3.8320022,39.137024 3.5860023,38.883025 3.2110023,38.756025 2.7070026,38.756025 2.2340026,38.756025 1.8590026,38.889025 1.5820031,39.154024 1.3040028,39.420022 1.1660032,39.779021 1.1660028,40.232018 L0.082003593,40.232018 C0.082003117,39.557022 0.32200336,38.995024 0.80300379,38.548026 1.2830029,38.101028 1.9180026,37.877029 2.7070026,37.877029 z M7.1420013,26.377007 L8.2439969,26.377007 8.2439969,27.531015 7.1420013,27.531015 z M2.8469973,18.876986 C3.6209977,18.876986 4.2309973,19.094986 4.6789968,19.532986 5.1259966,19.969986 5.3489965,20.542986 5.3489965,21.249987 5.3489965,21.721987 5.2129966,22.185987 4.9389961,22.640989 4.665997,23.095989 4.2729971,23.60499 3.761997,24.16799 L1.4709992,26.657991 5.6599964,26.657991 5.6599964,27.530993 0.16400003,27.530993 0.16400003,26.762993 2.9589972,23.651989 C3.4739978,23.081989 3.8199975,22.633989 3.9959974,22.306988 4.1719973,21.980988 4.2599974,21.641987 4.2599976,21.290987 4.2599974,20.844986 4.1339974,20.477987 3.8819973,20.188986 3.6299977,19.899986 3.2849979,19.755985 2.8469973,19.755985 2.2379985,19.755985 1.7919989,19.905987 1.5089989,20.206985 1.2249994,20.507986 1.0839992,20.940987 1.0839996,21.507988 L0,21.507988 C0,20.745987 0.24799967,20.116985 0.74399948,19.620985 1.2399993,19.124985 1.9409986,18.876986 2.8469973,18.876986 z M7.142002,7.3770065 L8.2439969,7.3770065 8.2439969,8.5310135 7.142002,8.5310135 z M3.539005,0 L3.539005,8.5309944 2.4550076,8.5309944 2.4550076,1.2419968 0.71501112,1.2889977 0.71501112,0.59199905 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                     Fill="{StaticResource IconColor}"
                                                                     Margin="18,16,6,20" 
                                                                     Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,38 L52,38 52,40 0,40 z M0,19 L52,19 52,21 0,21 z M0,0 L52,0 52,2 0,2 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                        <ToggleButton Focusable="False" 
                                                                      Grid.Column="2" 
                                                                      x:Name="PART_NumberBraceNumberingButton" 
                                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                                      BorderThickness="0" 
                                                                      Background="Transparent" 
                                                                      Padding="4" 
                                                                      HorizontalContentAlignment="Left" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNumberingAlignLeftToolTip}">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Numbering_NumberingBrace" 
                                                                  Height="76" 
                                                                  VerticalAlignment="Top">
                                                                        <Path 
                                                                  Fill="Transparent"
                                                                  Margin="0.5" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path  
                                                                  Fill="{StaticResource IconColor}"
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L76,0 76,76 0,76 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path     
                                                                  Fill="{StaticResource IconColor}" 
                                                                  HorizontalAlignment="Left"
                                                                  Margin="6.925,11.249,0,14.376" 
                                                                  Stretch="Fill"
                                                                  Width="9.334">
                                                                            <Path.Data>
                                                                                <PathGeometry>M2.7069993,38.966997 C3.5109989,38.966998 4.1419956,39.17299 4.5989964,39.585037 5.0559966,39.997022 5.2850005,40.598033 5.2849991,41.387031 5.2850005,41.750005 5.1779906,42.104007 4.9629938,42.447024 4.747998,42.791016 4.4219944,43.057006 3.9839913,43.244017 4.5109984,43.411985 4.8829925,43.674008 5.0969975,44.028987 5.3119933,44.385004 5.419995,44.791008 5.4199936,45.247976 5.419995,46.041002 5.169995,46.654952 4.6699945,47.090985 4.1699955,47.526958 3.5169956,47.743998&#xd;&#xa;2.7129952,47.743998 1.9239938,47.743998 1.2749919,47.535991 0.76699658,47.119977 0.25999285,46.703963 0.0059951316,46.119005 0.0059954098,45.36498 L1.0950003,45.36498 C1.0949993,45.830004 1.2379894,46.197007 1.5229932,46.466964 1.8079966,46.736006 2.2049996,46.870955 2.7129952,46.870954 3.2319922,46.870955 3.6329929,46.738997 3.9139998,46.475998 4.1949894,46.211961 4.3359959,45.810961 4.3359954,45.271963 4.3359959,44.728996 4.2069981,44.331964 3.9489872,44.081965 3.6909916,43.831966 3.2809879,43.706967 2.7189922,43.706967 L1.7399884,43.706967 1.7399884,42.840027 2.7189922,42.840027 C3.2579932,42.840027 3.6389896,42.710999 3.8639962,42.453005 4.0890019,42.195011 4.2010014,41.831975 4.2010009,41.362983 4.2010014,40.859995 4.0780003,40.481029 3.8319983,40.227003 3.5859959,39.973036 3.2109961,39.846022 2.7069993,39.846022 2.2339913,39.846022 1.8589915,39.979017 1.5819984,40.24403 1.3039989,40.51002 1.1659984,40.869028 1.1659988,41.322028 L0.081999072,41.322028 C0.081999139,40.646983 0.32198931,40.085035 0.8029923,39.638015 1.2829876,39.190996 1.9179971,38.966998 2.7069993,38.966997 z M6.4690072,38.000001 C7.1720049,38.399001 7.8250029,39.136001 8.429001,40.212001 9.0319988,41.288001 9.3339979,42.604001 9.3339979,44.158 L9.3339979,44.217 C9.3339979,45.783 9.0319988,47.102 8.429001,48.172001 7.8250029,49.242001 7.1720049,49.977 6.4690072,50.375001 L6.2400076,49.725 C6.7830059,49.307001 7.2540047,48.632001 7.6520035,47.700001 8.0510017,46.769001 8.2500011,45.612 8.2500011,44.229001 L8.2500011,44.147001 C8.2500011,42.787001 8.0450022,41.640001 7.6350028,40.704001 7.2250049,39.769001 6.7600061,39.084001 6.2400076,38.651001 z M2.8469977,19.966955 C3.6209971,19.966955 4.2309968,20.184956 4.6789963,20.622955 5.1259963,21.059956 5.3489959,21.632956 5.3489964,22.339956 5.3489959,22.811956 5.2129962,23.275958 4.9389965,23.730958 4.6659967,24.185959 4.2729966,24.694959 3.7619977,25.257959 L1.470999,27.747962 5.6599962,27.747962 5.6599962,28.620963 0.16399963,28.620963 0.16399963,27.852962 2.9589977,24.741959 C3.4739973,24.171959 3.819997,23.723958 3.9959971,23.396957 4.1719968,23.070957 4.2599966,22.731956 4.2599971,22.380957 4.2599966,21.934956 4.1339967,21.567957 3.8819973,21.278955 3.6299972,20.989956 3.2849974,20.845955 2.8469977,20.845955 2.2379982,20.845955 1.7919985,20.995956 1.5089989,21.296955 1.2249988,21.597956 1.0839989,22.030956 1.0839989,22.597958 L0,22.597958 C-4.3027103E-07,21.835957 0.24799942,21.206955 0.74399902,20.710955 1.2399988,20.214954 1.9409983,19.966955 2.8469977,19.966955 z M6.4690072,19 C7.1720049,19.399 7.8250029,20.136002 8.429001,21.212 9.0319988,22.288 9.3339979,23.604001 9.3339979,25.158001 L9.3339979,25.217001 C9.3339979,26.783001 9.0319988,28.102 8.429001,29.172001 7.8250029,30.242001 7.1720049,30.977001 6.4690072,31.375001 L6.2400076,30.725 C6.7830059,30.307001 7.2540047,29.632001 7.6520035,28.700001 8.0510017,27.769001 8.2500011,26.612 8.2500011,25.229001 L8.2500011,25.147 C8.2500011,23.787001 8.0450022,22.640001 7.6350028,21.704 7.2250049,20.769001 6.7600061,20.084002 6.2400076,19.651001 z M3.5390055,1.0899696 L3.5390055,9.6209621 2.4550073,9.6209621 2.4550073,2.3319664 0.7150106,2.3789673 0.7150106,1.6819687 z M6.4690072,0 C7.1720049,0.39900208 7.8250029,1.1360016 8.429001,2.2120018 9.0319988,3.288002 9.3339979,4.6040001 9.3339979,6.1580009 L9.3339979,6.2169991 C9.3339979,7.7830009 9.0319988,9.1020012 8.429001,10.172001 7.8250029,11.242001 7.1720049,11.977001 6.4690072,12.375 L6.2400076,11.725 C6.7830059,11.307001 7.2540047,10.632 7.6520035,9.7000008 8.0510017,8.769001 8.2500011,7.6120014 8.2500011,6.2290001 L8.2500011,6.1469994 C8.2500011,4.7870026 8.0450022,3.6399994 7.6350028,2.7040024 7.2250049,1.769001 6.7600061,1.0839996 6.2400076,0.65100098 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                  Fill="{StaticResource IconColor}" 
                                                                  Margin="18,16,6,20" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,37.999999 L52,37.999999 52,39.999999 0,39.999999 z M0,18.999999 L52,18.999999 52,20.999999 0,20.999999 z M0,0 L52,0 52,1.9999991 0,1.9999991 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                    </Grid>
                                                    <Grid Grid.Row="3" Grid.Column="1">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        <ToggleButton Focusable="False" 
                                                                      Grid.Column="0" 
                                                                      x:Name="PART_UpRomanNumberingButton" 
                                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                                      BorderThickness="0" 
                                                                      Background="Transparent" 
                                                                      Padding="4" 
                                                                      HorizontalContentAlignment="Left" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNumberingAlignRightToolTip}">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Numbering_Uproman" 
                                                                  HorizontalAlignment="Right" 
                                                                  Height="76" 
                                                                  VerticalAlignment="Top"
                                                                  Width="76">
                                                                        <Path  
                                                                  Fill="Transparent" 
                                                                  Margin="0.5" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                  Fill="{StaticResource IconColor}"
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L76,0 76,76 0,76 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                  Fill="{StaticResource IconColor}"
                                                                  Margin="14,16,7,20" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M6,38 L55,38 55,40 6,40 z M2.9999995,19 L55,19 55,21 2.9999995,21 z M0,0 L55,0 55,1.9999997 0,1.9999997 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path     
                                                                  Fill="{StaticResource IconColor}"
                                                                  HorizontalAlignment="Left" 
                                                                  Margin="7,12,0,17" 
                                                                  Stretch="Fill" 
                                                                  Width="10">
                                                                            <Path.Data>
                                                                                <PathGeometry>M9.0000003,46 L10,46 10,47 9.0000003,47 z M6,38 L6.9999998,38 6.9999998,40 6.9999998,41 6.9999998,47 6,47 6,41 6,40 z M3.0000005,38 L4,38 4,40 4,41 4,47 3.0000005,47 3.0000005,41 3.0000005,40 z M0,38 L1,38 1,40 1,41 1,47 0,47 0,41 0,40 z M6,27 L6.9999998,27 6.9999998,28 6,28 z M3.0000005,19 L4,19 4,21 4,22 4,28 3.0000005,28 3.0000005,22 3.0000005,21 z M0,19 L1,19 1,21 1,22 1,28 0,28 0,22 0,21 z M3.0000005,8 L4,8 4,9 3.0000005,9 z M0,0 L1,0 1,2 1,3 1,9 0,9 0,3 0,2 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                        <ToggleButton Focusable="False" 
                                                                      Grid.Column="1" 
                                                                      x:Name="PART_UpLetterNumberingButton" 
                                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                                      BorderThickness="0" 
                                                                      Background="Transparent" 
                                                                      Padding="4" 
                                                                      HorizontalContentAlignment="Left" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNumberingAlignLeftToolTip}">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Numbering_Upletter" 
                                                                  HorizontalAlignment="Right" 
                                                                  Height="76"
                                                                  VerticalAlignment="Top" 
                                                                  Width="76">
                                                                        <Path 
                                                                  Fill="Transparent"
                                                                  Margin="0.5" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path
                                                                  Fill="{StaticResource IconColor}"
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L4.2189999,0 4.8439999,0 76,0 76,76 0,76 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path     
                                                                  Fill="{StaticResource IconColor}"
                                                                  HorizontalAlignment="Left" 
                                                                  Margin="5.608,12.339,0,17.007" 
                                                                  Stretch="Fill" 
                                                                  Width="9.756">
                                                                            <Path.Data>
                                                                                <PathGeometry>M8.396025,45.377015 L9.4980357,45.377015 9.4980357,46.531025 8.396025,46.531025 z M3.798023,37.877038 C4.7550271,37.877038 5.4960306,38.124035 6.0210326,38.618035 6.547035,39.112034 6.856036,39.812031 6.9500363,40.719025 L5.8660315,40.719025 C5.7680314,40.070027 5.5600308,39.581032 5.2420295,39.25103 4.9240282,38.921032 4.442026,38.756035 3.798023,38.756035 3.1260205,38.756035 2.5960184,39.015034 2.2070163,39.532033 1.818015,40.05003 1.6240142,40.707024 1.6240142,41.504021 L1.6240142,43.021016 C1.6240142,43.826012 1.818015,44.487008 2.2070163,45.005005 2.5960184,45.522004 3.1260205,45.781003 3.798023,45.781003 4.4460261,45.781003 4.9290279,45.621003 5.2450293,45.301007 5.5610307,44.980008 5.7680314,44.49001 5.8660315,43.830014 L6.9500363,43.830014 C6.856036,44.689007 6.5430348,45.375004 6.0100325,45.887002 5.4760302,46.397999 4.7390273,46.654 3.798023,46.654 2.8130191,46.654 2.024016,46.318001 1.4300135,45.646004 0.83701092,44.975007 0.54000967,44.10001 0.54000955,43.021016 L0.54000955,41.516022 C0.54000967,40.441025 0.83701092,39.566029 1.4300135,38.891034 2.024016,38.215035 2.8130191,37.877038 3.798023,37.877038 z M8.3550103,26.377017 L9.4570211,26.377017 9.4570211,27.531024 8.3550103,27.531024 z M1.9220321,23.517018 L1.9220321,26.658001 3.9550276,26.658001 C4.506027,26.658001 4.9310263,26.528002 5.2300256,26.268004 5.5280254,26.008006 5.6780251,25.632006 5.6780251,25.140009 5.6780251,24.628012 5.5520251,24.230013 5.3000258,23.945015 5.0480259,23.660018 4.6540268,23.517018 4.1190279,23.517018 z M1.9220321,19.878037 L1.9220321,22.644022 3.8670285,22.644022 C4.3170274,22.632021 4.6730269,22.510024 4.9370263,22.278023 5.2000259,22.045025 5.3320258,21.712027 5.3320258,21.279028 5.3320258,20.802031 5.185026,20.450034 4.8900259,20.221034 4.5950268,19.993035 4.1560276,19.878037 3.5740295,19.878037 z M0.83803361,18.999041 L3.5740295,18.999041 C4.4810274,18.999041 5.1810258,19.18804 5.6750253,19.565037 6.1690238,19.942036 6.4160235,20.521033 6.4160235,21.302029 6.4160235,21.693026 6.298024,22.037026 6.0620239,22.333024 5.8250249,22.630023 5.514025,22.849021 5.1270254,22.990021 L5.1270254,23.00702 C5.639025,23.11302 6.0390242,23.360018 6.3280237,23.748017 6.6170233,24.137015 6.7620228,24.597012 6.7620228,25.12801 6.7620228,25.921006 6.5140231,26.520003 6.0180243,26.924 5.5220254,27.328999 4.8340266,27.530997 3.9550276,27.530997 L0.83803361,27.530997 z M8.6540301,7.3770161 L9.7560094,7.3770161 9.7560094,8.5310245 8.6540301,8.5310245 z M3.7560075,1.3419791 L2.2680033,5.3849449 5.2210024,5.3849449 z M3.292996,0 L4.2309831,0 7.471,8.5309997 6.3639763,8.5309997 5.537988,6.2579918 1.9459817,6.2579918 1.1080008,8.5309997 0,8.5309997 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path     
                                                                  Fill="{StaticResource IconColor}"
                                                                  Margin="18,16,7,20" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M1.9999999,38 L51,38 51,40 1.9999999,40 z M1,19 L51,19 51,21 1,21 z M0,0 L51,0 51,2 0,2 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                        <ToggleButton Focusable="False" 
                                                                      Grid.Column="2" 
                                                                      x:Name="PART_LowLetterBraceNumberingButton" 
                                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                                      BorderThickness="0" 
                                                                      Background="Transparent" 
                                                                      Padding="4" 
                                                                      HorizontalContentAlignment="Left" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNumberingAlignLeftToolTip}">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Numbering_LowLetterBrace"
                                                                  HorizontalAlignment="Right" 
                                                                  Height="76"
                                                                  VerticalAlignment="Top" 
                                                                  Width="76">
                                                                        <Path 
                                                                  Fill="Transparent" 
                                                                  Margin="0.5" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                  Fill="{StaticResource IconColor}"
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L76,0 76,76 0,76 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path   
                                                                  Fill="{StaticResource IconColor}"
                                                                  HorizontalAlignment="Left"
                                                                  Margin="6.948,11.249,0,14.376" 
                                                                  Stretch="Fill" 
                                                                  Width="9.369">
                                                                            <Path.Data>
                                                                                <PathGeometry>M2.8240006,41.163951 C3.5700009,41.163951 4.177001,41.378952 4.6430013,41.807953 5.1100011,42.237955 5.3450012,42.788957 5.3490009,43.46096 L4.318001,43.46096 C4.314001,43.053958 4.174001,42.716957 3.8960007,42.446956 3.6190009,42.177955 3.2610009,42.042954 2.8240006,42.042954 2.1950006,42.042954 1.7490005,42.260955 1.4850001,42.695956 1.2210002,43.131958 1.0900006,43.675961 1.0900006,44.327963 L1.0900006,44.573964 C1.0900006,45.241967 1.2200003,45.790969 1.4820008,46.219971 1.7440004,46.649972 2.1910007,46.864973 2.8240006,46.864973 3.2260008,46.864973 3.5750008,46.747972 3.8700006,46.512972 4.1650009,46.278971 4.314001,45.981969 4.318001,45.622968 L5.3490009,45.622968 C5.3450012,46.200971 5.0950012,46.697972 4.5960011,47.113974 4.098001,47.529976 3.5080009,47.737976 2.8240006,47.737976 1.8940005,47.737976 1.1910005,47.441975 0.71500015,46.849973 0.23800039,46.25897 0,45.499968 0,44.573964 L0,44.327963 C0,43.40596 0.23800039,42.647956 0.71500015,42.053954 1.1910005,41.460952 1.8940005,41.163951 2.8240006,41.163951 z M6.052991,37.999981 C6.7559884,38.399029 7.4089983,39.136028 8.0129974,40.212017 8.6159894,41.288006 8.9179888,42.603985 8.9179888,44.158001 L8.9179888,44.217021 C8.9179888,45.783001 8.6159894,47.102032 8.0129974,48.171978 7.4089983,49.241985 6.7559884,49.977032 6.052991,50.374981 L5.8239892,49.725018 C6.3669839,49.306988 6.8379884,48.632 7.2359951,47.699993 7.6349791,46.769024 7.8339827,45.61198 7.8339827,44.228985 L7.8339827,44.147014 C7.8339827,42.787029 7.6289977,41.639996 7.2189975,40.704021 6.808997,39.769024 6.3440043,39.084026 5.8239892,38.650982 z M2.9069784,23.066991 C2.5279789,23.066991 2.2139792,23.15499 1.9659796,23.330991 1.7179794,23.505991 1.5199795,23.748991 1.3719802,24.056991 L1.3719802,26.839993 C1.5239797,27.152992 1.7239795,27.396993 1.9719794,27.572992 2.219979,27.748993 2.5359788,27.835993 2.9189782,27.835993 3.4849782,27.835993 3.9009777,27.642993 4.1669779,27.255993 4.4319773,26.869992 4.5649772,26.345992 4.5649769,25.685991 L4.5649769,25.562992 C4.5649772,24.812991 4.4299773,24.208991 4.160978,23.75199 3.8909777,23.294991 3.4729781,23.066991 2.9069784,23.066991 z M0.28198004,19.480989 L1.3719802,19.480989 1.3719802,23.042991 1.3889799,23.04899 C1.5849795,22.759991 1.8289795,22.54099 2.1219795,22.38999 2.414979,22.23999 2.7659786,22.16499 3.1759784,22.16499 3.9689777,22.16499 4.5799772,22.47099 5.0069767,23.08399 5.4349765,23.69799 5.6489763,24.523992 5.6489762,25.562992 L5.6489762,25.685991 C5.6489763,26.623992 5.4349765,27.366993 5.0069767,27.915993 4.5799772,28.463993 3.9729776,28.738993 3.1879783,28.738993 2.7619786,28.738993 2.3969791,28.658993 2.0919793,28.498993 1.7879796,28.337993 1.5319796,28.097993 1.3249798,27.777992 L1.2189798,28.621993 0.28198004,28.621993 z M6.5039962,19.000002 C7.2069905,19.399002 7.8599851,20.136002 8.4639802,21.212002 9.0669756,22.288002 9.3689733,23.604002 9.3689728,25.158002 L9.3689728,25.217002 C9.3689733,26.783002 9.0669756,28.102001 8.4639802,29.172002 7.8599851,30.242002 7.2069905,30.977002 6.5039962,31.375001 L6.2749974,30.725002 C6.8179935,30.307002 7.2889898,29.632002 7.6869868,28.700002 8.0859835,27.769002 8.2849817,26.612001 8.284982,25.229002 L8.284982,25.147001 C8.2849817,23.787002 8.0799835,22.640001 7.669987,21.704002 7.25999,20.769001 6.7949937,20.084002 6.2749974,19.651001 z M2.8069894,6.6679878 C2.302989,6.6679878 1.9069889,6.7859879 1.6179886,7.0229893 1.3289886,7.2589912 1.1839881,7.5429935 1.1839886,7.8749962 1.1839881,8.1679993 1.2749882,8.3969994 1.4569883,8.5610008 1.6379886,8.7250023 1.9109888,8.807003 2.273989,8.807003 2.6799893,8.807003 3.0449896,8.7130032 3.3669899,8.526001 3.6889901,8.3379993 3.9219903,8.1079979 4.0669904,7.8339958 L4.0669904,6.6679878 z M2.7489893,3.1639595 C3.4789898,3.1639595 4.0619904,3.3439636 4.4979901,3.7029648 4.932991,4.0629692 5.1509911,4.5899734 5.150991,5.2859764 L5.150991,8.3439999 C5.1509911,8.5710011 5.1629911,8.7870026 5.1859914,8.9940052 5.2099912,9.2020054 5.2529912,9.4110069 5.3149914,9.6210098 L4.1959903,9.6210098 C4.1569904,9.4220085 4.1279904,9.258007 4.1079899,9.1290054 4.0879904,9.0000038 4.0749904,8.866003 4.0669904,8.7250023 3.8399901,9.0220051 3.55899,9.265007 3.2229898,9.4540081 2.8869896,9.6440086 2.5179892,9.7390099 2.1159892,9.7390099 1.4479885,9.7390099 0.94498825,9.5740089 0.60698795,9.2440071 0.26898766,8.9130039 0.099987507,8.4500008 0.099987507,7.8519955 0.099987507,7.2349911 0.34198761,6.757988 0.82698822,6.4219856 1.3109884,6.0859833 1.9829888,5.9179802&#xd;&#xa;2.8419898,5.9179802 L4.0669904,5.9179802 4.0669904,5.2739754 C4.0669904,4.8869743 3.9479903,4.5849724 3.70999,4.368969 3.4709899,4.1519699 3.1309898,4.0429688 2.6899893,4.0429688 2.279989,4.0429688 1.9489889,4.1399689 1.6969886,4.3329697 1.4449887,4.5269699 1.3189883,4.7619743 1.3189888,5.0389748 L0.23498821,5.0389748 C0.23498774,4.550972 0.46698761,4.1169701 0.93198824,3.7359657 1.3969884,3.3549614 2.0029888,3.1639595 2.7489893,3.1639595 z M6.2819852,0 C6.9849864,0.39900208 7.6379876,1.1360016 8.2419887,2.2120018 8.84499,3.288002 9.1469905,4.6040001 9.1469905,6.1580009 L9.1469905,6.217001 C9.1469905,7.7830009 8.84499,9.1020012 8.2419887,10.172001 7.6379876,11.242002 6.9849864,11.977001 6.2819852,12.375002 L6.052985,11.725002 C6.5959857,11.307001 7.0669866,10.632002 7.464987,9.7000008 7.8639882,8.769001 8.0629885,7.6120014 8.0629883,6.229002 L8.0629883,6.1470013 C8.0629885,4.7870026 7.8579881,3.6400032 7.4479872,2.7040024 7.0379865,1.769001 6.5729856,1.0840034 6.052985,0.65100098 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path     
                                                                  Fill="{StaticResource IconColor}"
                                                                  Margin="18,16,7,20" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,37.999999 L51,37.999999 51,39.999999 0,39.999999 z M0.99999994,18.999999 L51,18.999999 51,20.999999 0.99999994,20.999999 z M0.99999994,0 L51,0 51,1.9999992 0.99999994,1.9999992 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                    </Grid>
                                                    <Grid Grid.Row="4" Grid.Column="1">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        <ToggleButton Focusable="False" Grid.Column="0" 
                                                                      x:Name="PART_LowLetterDotNumberingButton" 
                                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                                      BorderThickness="0" 
                                                                      Background="Transparent" 
                                                                      Padding="4" 
                                                                      HorizontalContentAlignment="Left" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNumberingAlignLeftToolTip}">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Numbering_Lowletter_Dot" 
                                                                  HorizontalAlignment="Right"
                                                                  Height="76" 
                                                                  VerticalAlignment="Top" 
                                                                  Width="76">
                                                                        <Path
                                                                  Fill="Transparent"
                                                                  Margin="0.5" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                  Fill="{StaticResource IconColor}" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L76,0 76,76 0,76 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path    
                                                                  Fill="{StaticResource IconColor}"
                                                                  HorizontalAlignment="Left" 
                                                                  Margin="6.948,14.413,0,17.013" 
                                                                  Stretch="Fill" 
                                                                  Width="8.28">
                                                                            <Path.Data>
                                                                                <PathGeometry>M6.7260109,43.302991 L7.8280107,43.302991 7.8280107,44.456992 6.7260109,44.456992 z M2.8240008,37.99999 C3.5700009,37.99999 4.177001,38.214991 4.6430008,38.643993 5.1100011,39.073995 5.3450011,39.624997 5.3490012,40.296999 L4.318001,40.296999 C4.3140008,39.889998 4.174001,39.552996 3.8960011,39.282995 3.6190009,39.013994 3.2610006,38.878994 2.8240008,38.878994 2.1950006,38.878994 1.7490005,39.096994 1.4850001,39.531996 1.2210002,39.967998 1.0900002,40.512 1.0900006,41.164003 L1.0900006,41.410003 C1.0900002,42.078006 1.2200003,42.627008 1.4819999,43.05601 1.7440004,43.486012 2.1910005,43.701012 2.8240008,43.701012 3.2260008,43.701012 3.5750008,43.584012 3.8700008,43.349011 4.1650009,43.11501 4.3140008,42.818009 4.318001,42.459008 L5.3490012,42.459008 C5.3450011,43.03701 5.0950011,43.534012 4.5960011,43.950013 4.098001,44.366015 3.5080009,44.574016 2.8240008,44.574016 1.8940005,44.574016 1.1910005,44.278015 0.71499968,43.686012 0.23800039,43.09501 0,42.336007 0,41.410003 L0,41.164003 C0,40.241999 0.23800039,39.483996 0.71499968,38.889994 1.1910005,38.296991 1.8940005,37.99999 2.8240008,37.99999 z M7.1780095,24.303017 L8.2799587,24.303017 8.2799587,25.457026 7.1780095,25.457026 z M2.9070091,19.90303 C2.5280094,19.90303 2.2140098,19.99103 1.9660091,20.16703 1.7180099,20.34203 1.52001,20.58503 1.3720102,20.89303 L1.3720102,23.676031 C1.5240102,23.989033 1.72401,24.233032 1.9720106,24.409033 2.2200098,24.585032 2.5360093,24.672031 2.9190087,24.672031 3.4850085,24.672031 3.9010081,24.479033 4.1670079,24.092031 4.4320078,23.706032 4.5650077,23.182032 4.5650079,22.522032 L4.5650079,22.399031 C4.5650077,21.649031 4.4300077,21.045031 4.1610081,20.58803 3.8910081,20.131031 3.4730086,19.90303 2.9070091,19.90303 z M0.28201103,16.317028 L1.3720102,16.317028 1.3720102,19.87903 1.38901,19.885031 C1.5850101,19.596029 1.82901,19.377029 2.1220098,19.22603 2.4150095,19.076029 2.7660091,19.00103 3.1760087,19.00103 3.9690082,19.00103 4.5800076,19.30703 5.0070069,19.920031 5.435007,20.534031 5.6490068,21.360031 5.6490072,22.399031 L5.6490072,22.522032 C5.6490068,23.460032 5.435007,24.203032 5.0070069,24.752033 4.5800076,25.300032 3.9730082,25.575033 3.188009,25.575033 2.7620091,25.575033 2.3970094,25.495033 2.0920095,25.335033 1.7880101,25.174032 1.5320101,24.934032 1.3250098,24.614033 L1.2190108,25.458033 0.28201103,25.458033 z M6.9549709,5.3030167 L8.0569813,5.3030167 8.0569813,6.4570236 6.9549709,6.4570236 z M2.8069746,3.5040283 C2.3029714,3.5040283 1.9069686,3.6220284 1.6179676,3.8590279 1.3289652,4.0950317 1.1839643,4.3790321 1.1839643,4.7110367 1.1839643,5.0040398 1.2749648,5.2330399 1.4569664,5.3970413 1.6379671,5.5610428 1.9109688,5.6430435 2.2739706,5.6430435 2.6799741,5.6430435 3.0449762,5.5490417 3.3669782,5.3620415 3.6889806,5.1740379 3.921982,4.9440384 4.0669835,4.6700363 L4.0669835,3.5040283 z M2.7489743,0 C3.4789791,0 4.0619829,0.18000031 4.4979858,0.53900528 4.9329886,0.89900589 5.1509901,1.4260101 5.1509905,2.1220169 L5.1509905,5.1800385 C5.1509901,5.4070396 5.1629902,5.6230431 5.1859901,5.8300438 5.2099905,6.038044 5.2529907,6.2470474 5.3149912,6.4570503 L4.1959839,6.4570503 C4.1569836,6.258049 4.1279833,6.0940475 4.1079834,5.9650459 4.0879831,5.8360443 4.0749831,5.7020416 4.0669835,5.5610428 3.8399816,5.8580437 3.5589797,6.1010475 3.2229779,6.2900467 2.8869753,6.4800491 2.5179729,6.5750504 2.1159701,6.5750504 1.4479656,6.5750504 0.9449625,6.4100494 0.6069603,6.0800476 0.26895809,5.7490425 0.099956989,5.2860413 0.099956512,4.6880341 0.099956989,4.0710297 0.34195852,3.5940285 0.82696152,3.2580261 1.3109651,2.9220238 1.9829693,2.7540207&#xd;&#xa;2.841975,2.7540207 L4.0669835,2.7540207 4.0669835,2.1100159 C4.066983,1.7230148 3.9479823,1.4210129 3.7099807,1.2050095 3.470979,0.98800659 3.1309769,0.87900543 2.6899738,0.87900543 2.2799711,0.87900543 1.9489689,0.97600937 1.6969681,1.1690102 1.4449658,1.3630104 1.318965,1.598011 1.3189645,1.8750153 L0.23495817,1.8750153 C0.2349577,1.3870125 0.46695948,0.95300674 0.93196249,0.57200623 1.3969655,0.19100189 2.0029693,0 2.7489743,0 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path 
                                                                  Fill="{StaticResource IconColor}"
                                                                  Margin="17,16,6,20" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,38 L53,38 53,40 0,40 z M1,19 L53,19 53,21 1,21 z M1,0 L53,0 53,2 1,2 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                        <ToggleButton Focusable="False" 
                                                                      Grid.Column="1" 
                                                                      x:Name="PART_LowRomanNumberingButton" 
                                                                      Style="{StaticResource WPFGlyphPrimaryToggleButtonStyle}" 
                                                                      BorderThickness="0" 
                                                                      Background="Transparent" 
                                                                      Padding="4" 
                                                                      HorizontalContentAlignment="Left" 
                                                                      ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarNumberingAlignRightToolTip}">
                                                                <ToggleButton.Content>
                                                                    <Grid x:Name="Numbering_LowRoman" 
                                                                  HorizontalAlignment="Left"
                                                                  Width="76">
                                                                        <Path  
                                                                  Fill="Transparent" 
                                                                  Margin="0.5" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0,0 L75,0 75,75 0,75 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path    
                                                                  Fill="{StaticResource IconColor}" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M0.99999958,0.99999958 L0.99999958,75 75,75 75,0.99999958 z M0,0 L76,0 76,76 0,76 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path     
                                                                  Fill="{StaticResource IconColor}" 
                                                                  Margin="14,16,7,20" 
                                                                  Stretch="Fill">
                                                                            <Path.Data>
                                                                                <PathGeometry>M6,38 L55,38 55,40 6,40 z M2.9999995,19 L55,19 55,21 2.9999995,21 z M0,0 L55,0 55,2 0,2 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                        <Path     
                                                                  Fill="{StaticResource IconColor}" 
                                                                  HorizontalAlignment="Left"
                                                                  Margin="7,12,0,17" 
                                                                  Stretch="Fill" 
                                                                  Width="10">
                                                                            <Path.Data>
                                                                                <PathGeometry>M9.0000002,46 L10,46 10,47 9.0000002,47 z M6.0000002,40 L7.0000006,40 7.0000006,47 6.0000002,47 z M3.0000004,40 L4.0000002,40 4.0000002,47 3.0000004,47 z M0,40 L0.99999985,40 0.99999985,47 0,47 z M6.0000002,38 L6.9999997,38 6.9999997,39 6.0000002,39 z M3.0000004,38 L4.0000002,38 4.0000002,39 3.0000004,39 z M0,38 L0.99999985,38 0.99999985,39 0,39 z M6.0000002,27 L7.0000006,27 7.0000006,28 6.0000002,28 z M3.0000004,21 L4.0000002,21 4.0000002,28 3.0000004,28 z M0,21 L1.0000006,21 1.0000006,28 0,28 z M3.0000004,19 L4.0000002,19 4.0000002,20 3.0000004,20 z M0,19 L1.0000006,19 1.0000006,20 0,20 z M3.0000004,7.9999995 L4.0000002,7.9999995 4.0000002,9 3.0000004,9 z M0,1.9999998 L1.0000006,1.9999998 1.0000006,9 0,9 z M0,0 L1.0000006,0 1.0000006,0.99999988 0,0.99999988 z</PathGeometry>
                                                                            </Path.Data>
                                                                        </Path>
                                                                    </Grid>
                                                                </ToggleButton.Content>
                                                            </ToggleButton>
                                                    </Grid>
                                                </Grid>
                                                </Border>
                                            </tools_controls_shared:SplitButtonAdv>
                                        </Grid>
                                    </Grid>
                                    <Separator x:Name="PART_Separator"
                                               Visibility="Collapsed" 
                                               HorizontalAlignment="Left"
                                               Grid.Column="1">
                                        <Separator.LayoutTransform>
                                            <RotateTransform Angle="90"/>
                                        </Separator.LayoutTransform>
                                    </Separator>
                                    <Button x:Name="PART_InsertTableButton" 
                                            Focusable="False" 
                                            Visibility="Collapsed" 
                                            Grid.Column="2" 
                                            Margin="5" 
                                            Style="{StaticResource WPFGlyphButtonStyle}" 
                                            HorizontalAlignment="Left" 
                                            VerticalAlignment="Center" 
                                            Width="32" 
                                            ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarInsertTableToolTip}">
                                    </Button>
                                    <Button x:Name="PART_DeleteTableButton" 
                                            Focusable="False" 
                                            Visibility="Collapsed"
                                            Grid.Column="3"
                                            Margin="0,0,5,0"
                                            Style="{StaticResource WPFGlyphButtonStyle}" 
                                            HorizontalAlignment="Left" 
                                            VerticalAlignment="Center" 
                                            Width="32" 
                                            ToolTip="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarDeleteTableToolTip}"/>
                                </Grid>
                                <Popup x:Name="PART_AlignmentPopup" 
                                       IsOpen="False"
                                       PlacementTarget="{Binding ElementName=PART_ToolBarGrid}">
                                    <Border BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            CornerRadius="{StaticResource Windows11Dark.ThemeCornerRadiusVariant1}"
                                            
                                            Background="{StaticResource ContentBackground}"
                                            SnapsToDevicePixels="True">
                                        <Border.Resources>
                                            <richtextboxadv:LeftAlignmentToggleConverter x:Key="LeftAlignmentToggleConverter"/>
                                            <richtextboxadv:CenterAlignmentToggleConverter x:Key="CenterAlignmentToggleConverter"/>
                                            <richtextboxadv:RightAlignmentToggleConverter x:Key="RightAlignmentToggleConverter"/>
                                            <richtextboxadv:JustifyAlignmentToggleConverter x:Key="JustifyAlignmentToggleConverter"/>
                                        </Border.Resources>
                                        <ListBox x:Name="PART_AlignmentListBox">
                                            <ListBoxItem IsSelected="{Binding Selection.ParagraphFormat.TextAlignment, Mode=TwoWay,Converter={StaticResource LeftAlignmentToggleConverter}}">
                                                <Grid Width="110" 
                                                      Height="26"
                                                      VerticalAlignment="Center">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid x:Name="LeftAlignment" 
                                                          HorizontalAlignment="Left" 
                                                          Height="16" 
                                                          Grid.Column="0" 
                                                          VerticalAlignment="Center" 
                                                          Width="16">
                                                        <Path Data="M1,1 L1,15 15,15 15,9 15,7 15,1 z M0,0 L16,0 16,16 0,16 z" 
                                                              Fill="{StaticResource ContentForeground}" 
                                                              Stretch="Fill" />
                                                        <Path Data="M0,6 L7,6 7,7 0,7 z M0,4 L10,4 10,5 0,5 z M0,1.9999998 L7,1.9999998 7,3 0,3 z M0,0 L10,0 10,0.99999996 0,0.99999996 z" 
                                                              Fill="{StaticResource IconColor}"
                                                              Margin="3,4,3,5" 
                                                              Stretch="Fill" />
                                                    </Grid>
                                                    <TextBlock Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=LeftAlignment}" 
                                                               Margin="15,0,0,0" 
                                                               HorizontalAlignment="Center" 
                                                               VerticalAlignment="Center"
                                                               Grid.Column="1">
                                                    </TextBlock>
                                                </Grid>
                                            </ListBoxItem>
                                            <ListBoxItem IsSelected="{Binding Selection.ParagraphFormat.TextAlignment, Mode=TwoWay,Converter={StaticResource CenterAlignmentToggleConverter}}">
                                                <Grid Width="110" 
                                                      Height="26"
                                                      VerticalAlignment="Center">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid x:Name="CenterAlignment" 
                                                          HorizontalAlignment="Right"
                                                          VerticalAlignment="Center" 
                                                          Height="16"
                                                          Width="16">
                                                        <Path Data="M1,1 L1,15 15,15 15,1 z M0,0 L16,0 16,16 0,16 z"
                                                              Fill="{StaticResource IconColor}"
                                                              Stretch="Fill" />
                                                        <Path Data="M0.99999994,6 L9,6 9,7 0.99999994,7 z M0,4 L10,4 10,5 0,5 z M0.99999994,1.9999998 L9,1.9999998 9,3 0.99999994,3 z M0,0 L10,0 10,0.99999996 0,0.99999996 z" 
                                                              Fill="{StaticResource IconColor}"
                                                              Margin="3,4,3,5" 
                                                              Stretch="Fill" />
                                                    </Grid>
                                                    <TextBlock Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=CenteredAlignment}" 
                                                               Margin="15,0,0,0" 
                                                               HorizontalAlignment="Center" 
                                                               VerticalAlignment="Center"
                                                               Grid.Column="1">
                                                    </TextBlock>
                                                </Grid>
                                            </ListBoxItem>
                                            <ListBoxItem IsSelected="{Binding Selection.ParagraphFormat.TextAlignment, Mode=TwoWay,Converter={StaticResource RightAlignmentToggleConverter}}">
                                                <Grid Width="110" 
                                                      Height="26"
                                                      VerticalAlignment="Center">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid x:Name="RightAlignment" 
                                                          HorizontalAlignment="Right" 
                                                          Height="16"
                                                          VerticalAlignment="Center" 
                                                          Width="16">
                                                        <Path Data="M1,1 L1,15 15,15 15,1 z M0,0 L16,0 16,16 0,16 z" 
                                                              Fill="{StaticResource IconColor}" 
                                                              Stretch="Fill" />
                                                        <Path Data="M3,5.9999999 L10,5.9999999 10,6.9999999 3,6.9999999 z M0,4 L10,4 10,5 0,5 z M3,2 L10,2 10,3 3,3 z M0,0 L10,0 10,1 0,1 z" 
                                                              Fill="{StaticResource IconColor}"
                                                              Margin="3,4,3,5" 
                                                              Stretch="Fill"/>
                                                    </Grid>
                                                    <TextBlock Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=RightAlignment}" 
                                                               Margin="15,0,0,0" 
                                                               HorizontalAlignment="Center" 
                                                               VerticalAlignment="Center"
                                                               Grid.Column="1">
                                                    </TextBlock>
                                                </Grid>
                                            </ListBoxItem>
                                            <ListBoxItem IsSelected="{Binding Selection.ParagraphFormat.TextAlignment, Mode=TwoWay,Converter={StaticResource JustifyAlignmentToggleConverter}}">
                                                <Grid Width="110" 
                                                      Height="26"
                                                      VerticalAlignment="Center">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid x:Name="Justify"
                                                          HorizontalAlignment="Right" 
                                                          Height="16"
                                                          VerticalAlignment="Center" 
                                                          Width="16">
                                                        <Path Data="M1,1 L1,15 15,15 15,1 z M0,0 L16,0 16,16 0,16 z" 
                                                              Fill="{StaticResource IconColor}"
                                                              Stretch="Fill" />
                                                        <Path Data="M0,5.9999999 L10,5.9999999 10,6.9999999 0,6.9999999 z M0,4 L10,4 10,5 0,5 z M0,2 L10,2 10,3 0,3 z M0,0 L10,0 10,1 0,1 z" 
                                                              Fill="{StaticResource IconColor}"
                                                              Margin="3,4,3,5" 
                                                              Stretch="Fill" />
                                                    </Grid>
                                                    <TextBlock Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=JustifiedAlignment}" 
                                                               Margin="15,0,0,0" 
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"
                                                               Grid.Column="1">
                                                    </TextBlock>
                                                </Grid>
                                            </ListBoxItem>
                                        </ListBox>
                                    </Border>
                                </Popup>
                                <Popup x:Name="PART_InsertPopup" IsOpen="False" PlacementTarget="{Binding ElementName=PART_ToolBarGrid}">
                                    <Border BorderBrush="{StaticResource BorderAlt}" 
                                            Background="{StaticResource ContentBackground}" 
                                            BorderThickness="1">
                                        <StackPanel Orientation="Horizontal">
                                            <Grid Background="{TemplateBinding Background}">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>
                                                <Button x:Name="PART_InsertRowAboveButton" 
                                                        Focusable="False" 
                                                        Grid.Row="0"
                                                        Style="{StaticResource WPFGlyphButtonStyle}" 
                                                        Height="26"
                                                        Width="110">
                                                    <Button.Content>
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="auto"/>
                                                                <ColumnDefinition Width="auto"/>
                                                            </Grid.ColumnDefinitions>

                                                            <Grid x:Name="InsertRowAbove" Grid.Column="0"
                                                                   HorizontalAlignment="Left"
                                                                   VerticalAlignment="Top" 
                                                                   Width="16">
                                                                <Path Data="M0.0029982862,0 L3.4990006,0 3.5000077,0 3.5000077,3.5 5.4990006,3.5 5.4990006,7.5 8.5000077,7.5 8.5000077,5.5 8.5000077,3.5 10.500008,3.5 10.500008,0 15.000008,0 15.000008,13 0.0029982862,13 C-0.00099942874,13 -0.00099942874,1.2912665E-08 0.0029982862,0 z" 
                                                                      Fill="Transparent"
                                                                      Margin="0.5,1.501,0.5,0.499"
                                                                      Stretch="Fill" />
                                                                <Path Data="M4.9989929,0 L5.9989929,0 5.9989929,3 4.9989929,3 z M0,0 L1,0 1,3 0,3 z"
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Height="3" 
                                                                      Margin="5.002,0,4.999,1" 
                                                                      Stretch="Fill" 
                                                                      VerticalAlignment="Bottom"/>
                                                                <Path Data="M0,0 L4.9989929,0 4.9989929,3.0000001 8,3.0000001 8,0 14,0 14,4.0000001 0,4.0000001 z" 
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Margin="1,6,1,5"
                                                                      Stretch="Fill" />
                                                                <Path Data="M0,0.99999996 L3.9999999,0.99999996 3.9999999,1.9999999 1,1.9999999 1,14 15,14 15,1.9999999 11,1.9999999 11,0.99999996 16,0.99999996 16,15 0,15 z M7.5029621,0 L10.973974,3.2749939 10.285985,4.0010071 7.9989648,1.842987 7.9989648,8 6.9989638,8 6.9989638,1.8309935 4.6539603,4.0039978 3.974974,3.2720032 z"
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Stretch="Fill"/>
                                                                <Path Data="M0,4.9999999 L14.000993,4.9999999 14.000993,5.9999999 0,5.9999999 z M8.0009928,0 L14.000993,0 14.000993,0.99999994 8.0009928,0.99999994 z M0,0 L4.9999998,0 4.9999998,0.99999994 0,0.99999994 z"
                                                                      Fill="{StaticResource PrimaryBackground}"
                                                                      Margin="0.999,5,1,4" 
                                                                      Stretch="Fill"/>
                                                            </Grid>
                                                            <TextBlock Grid.Column="1" 
                                                                       Margin="15,0,0,0" 
                                                                       VerticalAlignment="Center"
                                                                       FontWeight="Light"
                                                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}" 
                                                                       Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarInsertTableAbove}"/>
                                                        </Grid>
                                                    </Button.Content>
                                                </Button>
                                                <Button x:Name="PART_InsertRowBelowButton" 
                                                        Focusable="False"
                                                        Grid.Row="1" 
                                                        Style="{StaticResource WPFGlyphButtonStyle}"
                                                        Height="26"
                                                        Width="110">
                                                    <Button.Content>
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="auto"/>
                                                                <ColumnDefinition Width="auto"/>
                                                            </Grid.ColumnDefinitions>

                                                            <Grid x:Name="Insertrowbelow" Grid.Column="0"
                                                                  HorizontalAlignment="Left" 
                                                                  Height="15"
                                                                  VerticalAlignment="Top" 
                                                                  Width="16">
                                                                <Path Data="M0.0029982862,0 L15.000008,0 15.000008,13 10.500008,13 10.500008,9.5 8.5000077,9.5 8.5000077,5.5 5.4990006,5.5 5.4990006,9.5 3.5000077,9.5 3.5000077,13 3.4990006,13 0.0029982862,13 C-0.00099942874,13 -0.00099942874,1.2912665E-08 0.0029982862,0 z" 
                                                                      Fill="Transparent"
                                                                      Margin="0.5,0.5,0.5,1.5" 
                                                                      Stretch="Fill" />
                                                                <Path Data="M4.9989929,0 L5.9989929,0 5.9989929,3 4.9989929,3 z M0,0 L1,0 1,3 0,3 z" 
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Height="3"
                                                                      Margin="5.002,1,4.999,0"
                                                                      Stretch="Fill" 
                                                                      VerticalAlignment="Top"/>
                                                                <Path Data="M8.0010004,4.9990071 L14.001,4.9990071 14.001,5.9990071 8.0010004,5.9990071 z M0,4.999 L5,4.999 5,5.9990001 0,5.9990001 z M7.0934598E-06,0 L14.001,0 14.001,1 7.0934598E-06,1 z" 
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Margin="0.999,4,1,5.001"
                                                                      Stretch="Fill"/>
                                                                <Path Data="M0,0 L14,0 14,4.0000001 8,4.0000001 8,1.0000001 4.9989929,1.0000001 4.9989929,4.0000001 0,4.0000001 z" 
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Margin="1,5,1,6"
                                                                      Stretch="Fill" />
                                                                <Path Data="M6.9989967,7 L7.9989982,7 7.9989982,13.157 10.286,10.998 10.974001,11.725 7.5029974,15 3.9749939,11.728 4.6539946,10.995 6.9989967,13.169 z M0,0 L16,0 16,14 11,14 11,13 15,13 15,1 1,1 1,13 4,13 4,14 0,14 z"
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Stretch="Fill" />
                                                            </Grid>
                                                            <TextBlock Grid.Column="1" 
                                                                       Margin="15,0,0,0" 
                                                                       VerticalAlignment="Center"
                                                                       FontWeight="Light"
                                                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}" 
                                                                       Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarInsertTableBelow}"/>
                                                        </Grid>
                                                    </Button.Content>
                                                </Button>
                                                <Button x:Name="PART_InsertColumnLeftButton" 
                                                        Focusable="False"
                                                        Grid.Row="2" 
                                                        Style="{StaticResource WPFGlyphButtonStyle}"
                                                        Height="26"
                                                        Width="110">
                                                    <Button.Content>
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="auto"/>
                                                                <ColumnDefinition Width="auto"/>
                                                            </Grid.ColumnDefinitions>

                                                            <Grid x:Name="InsertColumnBefore" Grid.Column="0"
                                                                  HorizontalAlignment="Left"
                                                                  Height="14"
                                                                  VerticalAlignment="Top" 
                                                                  Width="16">
                                                                <Path Data="M7.4995,0 C11.24925,1.0372787E-08 14.999,0.00099946106 14.999,0.0029982863 L14.999,13.000008 0,13.000008 0,9.5000076 1.4239814,9.5000076 2.7339795,10.888008 4.2009909,9.5000076 7.4989965,9.5000076 7.4989965,7.5000076 8.498997,7.5000076 8.498997,4.5000076 8.498997,4.4990006 7.4989965,4.4990006 7.4989965,2.5000076 4.2689841,2.5000076 2.7189953,1.0610123 1.3849801,2.5000076 0,2.5000076 0,2.4990006 0,0.0029982863 C0,0.00099946106 3.7497499,1.0372787E-08 7.4995,0 z" 
                                                                      Fill="Transparent"
                                                                      Margin="0.501,0.5,0.5,0.5"
                                                                      Stretch="Fill" />
                                                                <Path Data="M0,5 L4,5 4,6 0,6 z M0,0 L4,0 4,1 0,1 z"
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      HorizontalAlignment="Right" 
                                                                      Margin="0,4,1,4"
                                                                      Stretch="Fill"
                                                                      Width="4" />
                                                                <Path Data="M0,7 L1.000001,7 1.000001,12 0,12 z M4.9999986,0 L6,0 6,12 4.9999986,12 z M0,0 L0.99999994,0 0.99999994,4 0,4 z"
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Margin="5.001,1,4.999,1"
                                                                      Stretch="Fill"/>
                                                                <Path Data="M0,0 L4.0000001,0 4.0000001,12 0,12 0,7 2.998993,7 2.998993,4 0,4 z"
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Margin="6.001,1,5.999,1" 
                                                                      Stretch="Fill" />
                                                                <Path Data="M3.2720032,2.9749703 L4.0050049,3.6549637 1.8299866,5.9999671 8,5.9999671 8,6.9999681 1.8429871,6.9999681 4.0020142,9.2859812 3.2749939,9.9739699 0,6.5039654 z M0,0 L16,0 16,14 0,14 0,10 1,10 1,13 15,13 15,0.99999976 1,0.99999976 1,3 0,3 z" 
                                                                     Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Stretch="Fill"/>
                                                            </Grid>
                                                            <TextBlock Grid.Column="1" 
                                                                       Margin="15,0,0,0" 
                                                                       VerticalAlignment="Center"
                                                                       FontWeight="Light"
                                                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}" 
                                                                       Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarInsertTableLeft}"/>
                                                        </Grid>
                                                    </Button.Content>
                                                </Button>
                                                <Button x:Name="PART_InsertColumRightButton"
                                                        Focusable="False" 
                                                        Grid.Row="3" 
                                                        Style="{StaticResource WPFGlyphButtonStyle}"
                                                        Height="26"
                                                        Width="110">
                                                    <Button.Content>
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="auto"/>
                                                                <ColumnDefinition Width="auto"/>
                                                            </Grid.ColumnDefinitions>

                                                            <Grid x:Name="InsertColumnAfter" Grid.Column="0"
                                                                  HorizontalAlignment="Left" 
                                                                  Height="14"
                                                                  VerticalAlignment="Top"
                                                                  Width="16">
                                                                <Path Data="M7.4995,0 C11.24925,1.0581061E-08 14.999,0.00099946126 14.999,0.0029982863 L14.999,2.4990006 14.999,2.5000076 13.614021,2.5000076 12.280005,1.0610123 10.730016,2.5000076 7.5000036,2.5000076 7.5000036,4.4990006 6.5000031,4.4990006 6.5000031,4.5000076 6.5000031,7.5000076 7.5000036,7.5000076 7.5000036,9.5000076 10.798009,9.5000076 12.265021,10.888008 13.575019,9.5000076 14.999,9.5000076 14.999,13.000008 0,13.000008 0,0.0029982863 C0,0.00099946126 3.7497499,1.0581061E-08 7.4995,0 z" 
                                                                      Fill="Transparent"
                                                                      Margin="0.5,0.5,0.501,0.5" 
                                                                      Stretch="Fill" />
                                                                <Path Data="M0,5 L4,5 4,6 0,6 z M0,0 L4,0 4,1 0,1 z" 
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      HorizontalAlignment="Left" 
                                                                      Margin="1,4,0,4" 
                                                                      Stretch="Fill"
                                                                      Width="4" />
                                                                <Path Data="M4.9999999,7 L5.9999998,7 5.9999998,12 4.9999999,12 z M4.999993,0 L5.9999928,0 5.9999928,4 4.999993,4 z M0,0 L1,0 1,12 0,12 z" 
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Margin="4.999,1,5.001,1" 
                                                                      Stretch="Fill"/>
                                                                <Path Data="M0,0 L4.0000001,0 4.0000001,4 1.0010071,4 1.0010071,7 4.0000001,7 4.0000001,12 0,12 z"
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Margin="5.999,1,6.001,1" 
                                                                      Stretch="Fill" />
                                                                <Path Data="M12.728003,2.9749939 L16,6.5039973 12.725003,9.9740009 11.998004,9.2860003 14.157001,6.9999981 7.9990072,6.9999981 7.9990072,5.9999971 14.170002,5.9999971 11.995004,3.6549945 z M0,0 L16,0 16,3 15,3 15,1 1,1 1,13 15,13 15,10 16,10 16,14 0,14 z"
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}" 
                                                                      Stretch="Fill" />
                                                            </Grid>
                                                            <TextBlock Grid.Column="1" 
                                                                       Margin="15,0,0,0" 
                                                                       VerticalAlignment="Center"
                                                                       FontWeight="Light"
                                                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}" 
                                                                       Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarInsertTableRight}"/>
                                                        </Grid>
                                                    </Button.Content>
                                                </Button>
                                            </Grid>
                                        </StackPanel>
                                    </Border>
                                </Popup>
                                <Popup x:Name="PART_DeletePopup" IsOpen="False" PlacementTarget="{Binding ElementName=PART_ToolBarGrid}">
                                    <Border BorderBrush="LightGray" Background="{StaticResource ContentBackground}" BorderThickness="1">
                                        <StackPanel Orientation="Horizontal">
                                            <Grid Background="{TemplateBinding Background}">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>
                                                <Button x:Name="PART_DeleteColumnButton" 
                                                        Focusable="False" Style="{StaticResource WPFGlyphButtonStyle}"
                                                        Grid.Row="0"
                                                        Height="26"
                                                        Width="130" >
                                                    <Button.Content>
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="auto"/>
                                                                <ColumnDefinition Width="auto"/>
                                                            </Grid.ColumnDefinitions>

                                                            <Grid x:Name="Delete_column" Grid.Column="0"
                                                                  HorizontalAlignment="Left" 
                                                                  Height="15"
                                                                  VerticalAlignment="Top"
                                                                  Width="15">
                                                                <Path Data="M0.003023059,3.9960142 L9.001023,3.9960142 8.002032,4.9960141 0.003023059,4.9960141 z M0,0 L7.9990127,0 8.9980142,1 0,1 z" 
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Margin="0,5.002,5.999,5.002"
                                                                      Stretch="Fill"/>
                                                                <Path Data="M0,0 L8.997978,0 10.415,1.5 8.997978,3 0,3 z"
                                                                      Fill="{StaticResource PrimaryBackground}"
                                                                      Margin="0,6.002,4.585,5.998" 
                                                                      Stretch="Fill" />
                                                                <Path Data="M5.9989987,11.000012 L6.9989997,11.000012 6.9989997,15.000011 0,15.000011 0,14.000011 5.9989987,14.000011 z M0.0030380511,0 L7.0020147,0 7.0020147,3.9999995 6.0020181,3.9999995 6.0020181,1 0.0030380511,1 z" 
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      HorizontalAlignment="Left"
                                                                      Stretch="Fill" 
                                                                      Width="7.002"/>
                                                                <Path Data="M0,9.9969912 L5.9990001,9.9969912 5.9990001,12.996991 0,12.996991 z M5.2290971E-06,0 L5.9990125,0 5.9990125,2.9989772 5.2290971E-06,2.9989772 z" 
                                                                      Fill="Transparent"
                                                                      HorizontalAlignment="Left" 
                                                                      Margin="0,1.003,0,1" 
                                                                      Stretch="Fill"
                                                                      Width="5.999" />
                                                                <Path Data="M0.70700001,0 L2.5025001,1.7929902 4.2979999,0 5.0050001,0.70800779 3.2104995,2.5 5.0050001,4.2919922 4.2979999,5 2.5025001,3.2070098 0.70700001,5 0,4.2919922 1.7945005,2.5 0,0.70800779 z"
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      HorizontalAlignment="Right"
                                                                      Margin="0,5.002,0,4.998" 
                                                                      Stretch="Fill" 
                                                                      Width="5.005" />
                                                            </Grid>
                                                            <TextBlock Grid.Column="1" 
                                                                       Margin="15,0,0,0" 
                                                                       VerticalAlignment="Center"
                                                                       FontWeight="Light"
                                                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}" 
                                                                       Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarDeleteTableColumns}"/>
                                                        </Grid>
                                                    </Button.Content>
                                                </Button>
                                                <Button x:Name="PART_DeleteRowButton"
                                                        Focusable="False" Style="{StaticResource WPFGlyphButtonStyle}"
                                                        Grid.Row="1" 
                                                        Height="26"
                                                        Width="130">
                                                    <Button.Content>
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="auto"/>
                                                                <ColumnDefinition Width="auto"/>
                                                            </Grid.ColumnDefinitions>

                                                            <Grid x:Name="Delete_ROw" Grid.Column="0"
                                                                  HorizontalAlignment="Left"
                                                                  Height="15"
                                                                  VerticalAlignment="Top"
                                                                  Width="15">
                                                                <Path Data="M4,0 L5,0 5,8 4,9 z M0,0 L0.99999993,0 0.99999993,9 0,8 z" 
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Margin="5,0,5,6" 
                                                                      Stretch="Fill"/>
                                                                <Path Data="M0,0 L2.9999999,0 2.9999999,9.0000067 1.4999999,10.417 0,9.0000067 z"
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Margin="6,0,6,4.583"
                                                                      Stretch="Fill" />
                                                                <Path Data="M14,0 L15,0 15,7 11,7 11,6 14,6 z M0,0 L0.99999994,0 0.99999994,6 4,6 4,7 0,7 z" 
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Height="7" 
                                                                      Stretch="Fill" 
                                                                      VerticalAlignment="Top"/>
                                                                <Path Data="M10,0 L13,0 13,6 10,6 z M0,0 L3,0 3,6 0,6 z"
                                                                      Fill="Transparent" 
                                                                      Height="6" 
                                                                      Margin="1,0" 
                                                                      Stretch="Fill" 
                                                                      VerticalAlignment="Top"/>
                                                                <Path Data="M0.70699906,0 L2.503,1.793073 4.2990012,0 5.006,0.70800783 3.2110815,2.5 5.006,4.2919922 4.2990012,5 2.503,3.2069271 0.70699906,5 0,4.2919922 1.7949185,2.5 0,0.70800783 z" 
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Height="5"
                                                                      Margin="4.997,0" 
                                                                      Stretch="Fill" 
                                                                      VerticalAlignment="Bottom" />
                                                            </Grid>
                                                            <TextBlock Grid.Column="1" 
                                                                       Margin="15,0,0,0" 
                                                                       VerticalAlignment="Center"
                                                                       FontWeight="Light"
                                                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                                       Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarDeleteTableRows}"/>
                                                        </Grid>
                                                    </Button.Content>
                                                </Button>
                                                <Button x:Name="PART_DeleteEntireRowButton"
                                                        Focusable="False" Style="{StaticResource WPFGlyphButtonStyle}"  
                                                        Grid.Row="2"
                                                        Height="26"
                                                        Width="130">
                                                    <Button.Content>
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="auto"/>
                                                                <ColumnDefinition Width="auto"/>
                                                            </Grid.ColumnDefinitions>

                                                            <Grid x:Name="Delete1" 
                                                                  HorizontalAlignment="Left" 
                                                                  Height="15" 
                                                                  VerticalAlignment="Top"
                                                                  Width="16">
                                                                <Path Data="M0,0 L14,0 14,6.0000001 7,6.0000001 7,10 5,12 0,12 z" 
                                                                      Fill="Transparent" 
                                                                      Margin="1,1,1,2" 
                                                                      Stretch="Fill" />
                                                                <Path Data="M4,0 L4.9999999,0 4.9999999,3.0000001 9,3.0000001 9,0 10,0 10,3.0000001 14,3.0000001 14,4.0000001 10,4.0000001 10,6.0000001 14,6.0000001 12.96875,7.0000001 10,7.0000001 10,6.9919907 9,6.0215212 9,4.0000001 4.9999999,4.0000001 4.9999999,6.0000001 8.0319991,6.0000001 7.2194445,7.0000001 4.9999999,7.0000001 4.9999999,12 4,12&#xd;&#xa;4,7.0000001 0,7.0000001 0,6.0000001 4,6.0000001 4,4.0000001 0,4.0000001 0,3.0000001 4,3.0000001 z"
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Margin="1,1,1,2" 
                                                                      Stretch="Fill"/>
                                                                <Path Data="M0,0 L16,0 16,1 16,7.512002 15,6.6366973 15,1 1,1 1,11 10,11 10,12 1,12 1,13 6.0159979,13 6.0159979,14 0,14 0,13 0,1 z"
                                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      Margin="0,0,0,1" 
                                                                      Stretch="Fill"/>
                                                                <Path Data="M0.7069726,0 L3.5005109,2.793082 6.2940249,0 7.0010524,0.70700071 4.207583,3.5000384 7.0009999,6.2929992 6.2940273,7 3.5005415,4.2069702 0.70707989,7 5.2474876E-05,6.2929992 2.7934697,3.500014 0,0.70700071 z" 
                                                                     Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(Button.Foreground)}"
                                                                      HorizontalAlignment="Right" 
                                                                      Height="7"
                                                                      Stretch="Fill" 
                                                                      VerticalAlignment="Bottom" 
                                                                      Width="7.001" />
                                                            </Grid>
                                                            <TextBlock Grid.Column="1" 
                                                                       Margin="15,0,0,0" 
                                                                       VerticalAlignment="Center"
                                                                       FontWeight="Light"
                                                                       FontSize="{StaticResource Windows11Dark.BodyTextStyle}" 
                                                                       Text="{resources:RichTextBoxAdvLocalizationResourceExtension ResourceName=MiniToolBarDeleteTable}"/>
                                                        </Grid>
                                                    </Button.Content>
                                                </Button>
                                            </Grid>
                                        </StackPanel>
                                    </Border>
                                </Popup>
                            </Grid>
                        </ContentControl>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
