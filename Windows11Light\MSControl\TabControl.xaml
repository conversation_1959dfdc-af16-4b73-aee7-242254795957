<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
					
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="TabItem.Selection.Active.Background" Color="#ffffff"/>

    <Style x:Key="WPFTabControlStyle" TargetType="{x:Type TabControl}">
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness0100}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize"  Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Padding" Value="2,2,2,2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TabControl}">
                    <Grid x:Name="templateRoot" 
                          ClipToBounds="true" 
                          SnapsToDevicePixels="true" 
                          KeyboardNavigation.TabNavigation="Local">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition x:Name="ColumnDefinition0"/>
                            <ColumnDefinition x:Name="ColumnDefinition1" Width="0"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition x:Name="RowDefinition0" Height="Auto"/>
                            <RowDefinition x:Name="RowDefinition1" Height="*"/>
                        </Grid.RowDefinitions>
                        <TabPanel x:Name="headerPanel" 
                                  Grid.Column="0" 
                                  Grid.Row="0" 
                                  Margin="2,2,2,0" 
                                  Background="Transparent" 
                                  IsItemsHost="true" 
                                  KeyboardNavigation.TabIndex="1" 
                                  Panel.ZIndex="1"/>
                        <Border x:Name="contentPanel" 
                                Grid.Column="0" 
                                Grid.Row="1" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Background="{TemplateBinding Background}" 
                                KeyboardNavigation.DirectionalNavigation="Contained"
                                KeyboardNavigation.TabIndex="2" 
                                KeyboardNavigation.TabNavigation="Local">
                            <ContentPresenter x:Name="PART_SelectedContentHost" 
                                              ContentSource="SelectedContent"
                                              Margin="{TemplateBinding Padding}"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="TabStripPlacement" Value="Bottom">
                            <Setter Property="Grid.Row" TargetName="headerPanel" Value="1"/>
                            <Setter Property="Grid.Row" TargetName="contentPanel" Value="0"/>
                            <Setter Property="Height" TargetName="RowDefinition0" Value="*"/>
                            <Setter Property="Height" TargetName="RowDefinition1" Value="Auto"/>
                            <Setter Property="Margin" TargetName="headerPanel" Value="2,0,2,2"/>
                        </Trigger>
                        <Trigger Property="TabStripPlacement" Value="Left">
                            <Setter Property="Grid.Row" TargetName="headerPanel" Value="0"/>
                            <Setter Property="Grid.Row" TargetName="contentPanel" Value="0"/>
                            <Setter Property="Grid.Column" TargetName="headerPanel" Value="0"/>
                            <Setter Property="Grid.Column" TargetName="contentPanel" Value="1"/>
                            <Setter Property="Width" TargetName="ColumnDefinition0" Value="Auto"/>
                            <Setter Property="Width" TargetName="ColumnDefinition1" Value="*"/>
                            <Setter Property="Height" TargetName="RowDefinition0" Value="*"/>
                            <Setter Property="Height" TargetName="RowDefinition1" Value="0"/>
                            <Setter Property="Margin" TargetName="headerPanel" Value="2,2,0,2"/>
                        </Trigger>
                        <Trigger Property="TabStripPlacement" Value="Right">
                            <Setter Property="Grid.Row" TargetName="headerPanel" Value="0"/>
                            <Setter Property="Grid.Row" TargetName="contentPanel" Value="0"/>
                            <Setter Property="Grid.Column" TargetName="headerPanel" Value="1"/>
                            <Setter Property="Grid.Column" TargetName="contentPanel" Value="0"/>
                            <Setter Property="Width" TargetName="ColumnDefinition0" Value="*"/>
                            <Setter Property="Width" TargetName="ColumnDefinition1" Value="Auto"/>
                            <Setter Property="Height" TargetName="RowDefinition0" Value="*"/>
                            <Setter Property="Height" TargetName="RowDefinition1" Value="0"/>
                            <Setter Property="Margin" TargetName="headerPanel" Value="0,2,2,2"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="TextElement.Foreground" TargetName="templateRoot" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
	
    <Style x:Key="TabItemFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="{StaticResource Windows11Light.FocusMargin}" 
                               SnapsToDevicePixels="true" 
                               Stroke="{StaticResource BorderAlt3}" 
                               StrokeThickness="{StaticResource Windows11Light.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Light.StrokeDashArray}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFTabItemStyle" TargetType="{x:Type TabItem}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource TabItemFocusVisual}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="Padding" Value="6,4,6,4" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
        <Setter Property="VerticalContentAlignment" Value="Stretch"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TabItem}">
                    <Grid x:Name="templateRoot" 
                          SnapsToDevicePixels="true">
                        <Rectangle x:Name="TabSeparator"
                                   HorizontalAlignment="Right"
                                   Width="1"
                                   Height="15"
                                   SnapsToDevicePixels="True"
                                   Visibility="Visible"
                                   Stroke="{StaticResource BorderAlt}" 
                                   StrokeThickness="1"
                                   Margin="0,4,0,4"/>
                        
                        <Border x:Name="mainBorder" 
                                SnapsToDevicePixels="True"
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                Background="{TemplateBinding Background}"
                                CornerRadius="7,7,0,0"
                                Margin="-2,0,0,0">
                            <Border x:Name="TabStrip" 
                                    Margin="16,0" 
                                    CornerRadius="4,4,0,0" 
                                    Height="2"
                                    VerticalAlignment="Bottom"
                                    Background="{StaticResource BorderAlt}"
                                    Visibility="Collapsed"/>
                        </Border>
                        <ContentPresenter x:Name="contentPresenter" 
                                          ContentSource="Header"
                                          Focusable="False"
                                          
		                                  HorizontalAlignment="{Binding HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}" 
                                          VerticalAlignment="{Binding VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"                                          
                                          RecognizesAccessKey="True" 
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" 
                                          Margin="{TemplateBinding Padding}"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="mainBorder" Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="mainBorder" Value="Transparent"/>
                            <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}"/>
                            <Setter Property="Opacity" TargetName="TabSeparator" Value="0"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="true">
                            <Setter Property="BorderThickness" TargetName="mainBorder" Value="1,1,1,0" />
                            <Setter Property="Background" TargetName="mainBorder" Value="{StaticResource ContentBackgroundAlt1}"/>
                            <Setter Property="BorderBrush" TargetName="mainBorder" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="Opacity" TargetName="TabSeparator" Value="0"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" TargetName="mainBorder" Value="Transparent"/>
                            <Setter Property="BorderBrush" TargetName="mainBorder" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsEnabled, RelativeSource={RelativeSource Self}}" Value="false"/>
                                <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource Self}}" Value="true"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="BorderThickness" TargetName="mainBorder" Value="1,1,1,0"/>
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource WPFTabControlStyle}" TargetType="{x:Type TabControl}"/>
    <Style BasedOn="{StaticResource WPFTabItemStyle}" TargetType="{x:Type TabItem}"/>
    
</ResourceDictionary>
