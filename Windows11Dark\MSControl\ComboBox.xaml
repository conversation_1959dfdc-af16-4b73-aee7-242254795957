<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"    
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:po="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphDropdownExpander.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphEditableDropdownExpander.xaml"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="ComboBox.Static.Border" Color="#c8c8c8"/>

    <SolidColorBrush x:Key="ComboBox.MouseOver.Border" Color="#179bd7"/>

    <SolidColorBrush x:Key="ComboBox.Focused.Border" Color="#686969"/>

    <LinearGradientBrush x:Key="ComboBoxBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ComboBoxBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ComboBoxBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="ComboBoxItemFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="{StaticResource Windows11Dark.FocusMargin}" 
                               Stroke="{StaticResource BorderAlt3}" 
                               StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Dark.StrokeDashArray}"
                               SnapsToDevicePixels="true"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <ControlTemplate x:Key="ComboBoxEditableTemplate" TargetType="{x:Type ComboBox}">
        <Grid x:Name="Placement" SnapsToDevicePixels="true">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition MinWidth="{StaticResource {x:Static SystemParameters.VerticalScrollBarWidthKey}}" Width="0" />
            </Grid.ColumnDefinitions>
            <Popup x:Name="PART_Popup"  
                   Grid.ColumnSpan="2" 
                   AllowsTransparency="true" 
                   IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}"
                   PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" 
                   Placement="Bottom"
                   SnapsToDevicePixels="True">
                <Border x:Name="DropDownBorder" 
                        MaxHeight="{TemplateBinding MaxDropDownHeight}" 
                        MinWidth="{Binding ActualWidth, ElementName=Placement}"
                        BorderBrush="{StaticResource BorderAlt}" 
                        BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                        Background="{StaticResource PopupBackground}" 
                        Effect="{StaticResource Default.ShadowDepth4}"
                        Padding="0,2,0,2"
                        CornerRadius="0,0,4,4">
                    <ScrollViewer x:Name="DropDownScrollViewer" ClipToBounds="True">
                        <Grid>
                            <Canvas HorizontalAlignment="Left" 
                                    VerticalAlignment="Top" 
                                    Height="0" 
                                    Width="0">
                                <Rectangle x:Name="OpaqueRect" 
                                           Fill="{Binding Background, ElementName=DropDownBorder}" 
                                           Height="{Binding ActualHeight, ElementName=DropDownBorder}" 
                                           Width="{Binding ActualWidth, ElementName=DropDownBorder}"/>
                            </Canvas>
                            <ItemsPresenter x:Name="ItemsPresenter" 
                                            KeyboardNavigation.DirectionalNavigation="Contained" 
                                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                            Margin="4,0,6,0"/>
                            </Grid>
                    </ScrollViewer>
                </Border>
            </Popup>
            <Border x:Name="Border"
                    Grid.ColumnSpan="2"
                    Background="{TemplateBinding Background}" 
                    BorderBrush="{TemplateBinding BorderBrush}" 
                    BorderThickness="{TemplateBinding BorderThickness}"                    
                    CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"/>
            <ToggleButton x:Name="PART_ToggleButton" 
                          Grid.ColumnSpan="2"
                          Width="20"
                          IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" 
                          Style="{StaticResource WPFGlyphEditableDropdownExpanderStyle}"
                          Height="NaN"
                          HorizontalAlignment="Right"
                          Padding="0,0,0,2"
                          Margin="3,3,3,3"/>
            <TextBox x:Name="PART_EditableTextBox" 
                     IsReadOnly="{Binding IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}" 
                     Margin="2,2,0,2" 
                     Background="Transparent"
                     TextElement.Foreground="{TemplateBinding Foreground}"
                     TextElement.FontSize="{TemplateBinding FontSize}"
                     HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                     VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                     Style="{StaticResource WPFBorderlessTextBoxStyle}"/>
        </Grid>
        <ControlTemplate.Triggers>
            <EventTrigger SourceName="DropDownBorder" RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(Effect).Opacity"
                                                       Storyboard.TargetName="DropDownBorder">
                            <SplineDoubleKeyFrame KeyTime="00:00:00"
                                                  Value="0" />
                            <SplineDoubleKeyFrame KeyTime="00:00:0.4"
                                                  Value="0" />
                            <SplineDoubleKeyFrame KeyTime="00:00:0.5"
                                                  Value="0.17" />
                        </DoubleAnimationUsingKeyFrames>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
            </Trigger>
            <Trigger Property="IsKeyboardFocusWithin" Value="true">
                <Setter Property="TextElement.Foreground" TargetName="PART_EditableTextBox" Value="{StaticResource ContentForeground}"/>
            </Trigger>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="DropDownBorder" Value="95"/>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Foreground" TargetName="PART_EditableTextBox"  Value="{StaticResource ContentForeground}" />
                <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt5}" />
                <Setter Property="BorderBrush" TargetName="Border" Value="{StaticResource ComboBoxBorderBrushHovered}"/> 
            </Trigger>
            <Trigger Property="IsFocused" SourceName="PART_EditableTextBox" Value="True">
                <Setter Property="Foreground" TargetName="PART_EditableTextBox" Value="{StaticResource ContentForeground}" />
                <Setter Property="Background" TargetName="Border" Value="{StaticResource ContentBackground}" />
                <Setter Property="BorderBrush" TargetName="Border" Value="{StaticResource ComboBoxBorderBrushFocused}"/>
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}"/>
            </Trigger>
            <Trigger Property="IsDropDownOpen" Value="True">
                <Setter Property="Foreground" TargetName="PART_EditableTextBox" Value="{StaticResource ContentForeground}" />
                <Setter Property="Background" TargetName="Border" Value="{StaticResource ContentBackground}" />
                <Setter Property="BorderBrush" TargetName="Border" Value="{StaticResource ComboBoxBorderBrushFocused}"/>
                <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}"/>
                <Setter Property="CornerRadius" TargetName="Border" Value="4,4,0,0"/>
            </Trigger>
            <Trigger Property="IsGrouping" Value="true">
                <Setter Property="ScrollViewer.CanContentScroll" Value="false"/>
            </Trigger>
            <Trigger Property="HasDropShadow" SourceName="PART_Popup" Value="true">
                <Setter Property="Margin" TargetName="DropDownBorder" Value="14,0,14,14"/>
            </Trigger>
            <Trigger Property="ScrollViewer.CanContentScroll" SourceName="DropDownScrollViewer" Value="false">
                <Setter Property="Canvas.Top" TargetName="OpaqueRect" Value="{Binding VerticalOffset, ElementName=DropDownScrollViewer}"/>
                <Setter Property="Canvas.Left" TargetName="OpaqueRect" Value="{Binding HorizontalOffset, ElementName=DropDownScrollViewer}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Background" TargetName="Border" Value="{StaticResource ContentBackgroundAlt6}"/>
                <Setter Property="BorderBrush" TargetName="Border" Value="{StaticResource BorderAlt}"/>
                <Setter Property="Foreground" TargetName="PART_EditableTextBox" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    
    <Style x:Key="WPFComboBoxStyle" TargetType="{x:Type ComboBox}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="BorderBrush"  Value="{StaticResource ComboBoxBorderBrush}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant1}"/>
        <Setter Property="Padding" Value="4,2,2,2"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.CanContentScroll" Value="true" />
        <Setter Property="ScrollViewer.PanningMode" Value="Both"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ComboBox}">
                    <Grid x:Name="templateRoot" SnapsToDevicePixels="True">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition MinWidth="{StaticResource {x:Static SystemParameters.VerticalScrollBarWidthKey}}" Width="0" />
                        </Grid.ColumnDefinitions>
                        <Popup x:Name="PART_Popup" 
                               Grid.ColumnSpan="2"
                               StaysOpen="False"
                               IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}"                            
                               AllowsTransparency="true"
                               PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" 
                               Placement="Bottom"
                               SnapsToDevicePixels="True">
                            <Border x:Name="DropDownBorder" 
                                    MaxHeight="{TemplateBinding MaxDropDownHeight}" 
                                    MinWidth="{Binding ActualWidth, ElementName=templateRoot}"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    SnapsToDevicePixels="True"
                                    Background="{StaticResource PopupBackground}" 
                                    BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                    Effect="{StaticResource Default.ShadowDepth4}"
                                    Padding="0,2,0,2"
                                    CornerRadius="0,0,4,4">
                                <ScrollViewer x:Name="DropDownScrollViewer" ClipToBounds="True">
                                    <Grid>
                                        <Canvas HorizontalAlignment="Left"
                                                VerticalAlignment="Top" 
                                                Height="0" 
                                                Width="0">
                                            <Rectangle x:Name="OpaqueRect" 
                                                        Fill="{Binding Background, ElementName=DropDownBorder}" 
                                                        Height="{Binding ActualHeight, ElementName=DropDownBorder}" 
                                                        Width="{Binding ActualWidth, ElementName=DropDownBorder}" />
                                        </Canvas>
                                        <ItemsPresenter x:Name="ItemsPresenter" 
                                                        KeyboardNavigation.DirectionalNavigation="Contained"
                                                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                                        Margin="4,0,6,0"/>
                                    </Grid>
                                </ScrollViewer>
                            </Border>
                        </Popup>
                        <Border x:Name="Border" 
                                Grid.ColumnSpan="2" 
                                Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"/>
                        <ToggleButton x:Name="PART_ToggleButton" 
                                      Grid.ColumnSpan="2" 
                                      Background="Transparent"
                                      BorderBrush="Transparent"
                                      IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" 
                                      Style="{StaticResource WPFGlyphDropdownExpanderStyle}"
                                      HorizontalContentAlignment="Right"
                                      Margin="0,0,5,0"/>
                        <ContentPresenter x:Name="contentPresenter" Margin="{TemplateBinding Padding}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"                     
                                          Content="{TemplateBinding SelectionBoxItem}"
                                          ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}"
                                          ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                          ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                          IsHitTestVisible="false"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" >
								<ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
					    </ContentPresenter>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <EventTrigger SourceName="DropDownBorder" RoutedEvent="Loaded">
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(Effect).Opacity"
                                                                   Storyboard.TargetName="DropDownBorder">
                                        <SplineDoubleKeyFrame KeyTime="00:00:00"
                                                              Value="0" />
                                        <SplineDoubleKeyFrame KeyTime="00:00:0.4"
                                                              Value="0" />
                                        <SplineDoubleKeyFrame KeyTime="00:00:0.5"
                                                              Value="0.17" />
                                    </DoubleAnimationUsingKeyFrames>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                        <Trigger Property="HasItems" Value="false">
                            <Setter Property="Height" TargetName="DropDownBorder" Value="95" />
                        </Trigger>
                        <Trigger Property="HasDropShadow" SourceName="PART_Popup" Value="true">
                            <Setter Property="Margin" TargetName="DropDownBorder" Value="14,0,14,14"/>
                        </Trigger>                      
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="Margin" TargetName="PART_ToggleButton" Value="0,0,5,0"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}" />
                            <Setter Property="Background" TargetName="Border" Value="{StaticResource ContentBackgroundAlt5}" />
                            <Setter Property="BorderBrush" TargetName="Border" Value="{StaticResource ComboBoxBorderBrushHovered}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}" />
                            <Setter Property="Background" TargetName="Border" Value="{StaticResource ContentBackground}" />
                            <Setter Property="BorderBrush" TargetName="Border" Value="{StaticResource ComboBoxBorderBrushHovered}"/>
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant2}"/>
                        </Trigger>
                        <Trigger SourceName="PART_ToggleButton" Property="IsPressed" Value="True">
                            <Setter Property="BorderBrush" TargetName="Border" Value="{StaticResource ComboBoxBorderBrush}"/>
                        </Trigger>
                        <Trigger Property="IsDropDownOpen" Value="True">
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource ContentForeground}" />
                            <Setter Property="Background" TargetName="Border" Value="{StaticResource ContentBackground}" />
                            <Setter Property="BorderBrush" TargetName="Border" Value="{StaticResource ComboBoxBorderBrush}"/>
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant2}"/>
                            <Setter Property="CornerRadius" TargetName="Border" Value="4,4,0,0"/>
                        </Trigger>
                        <Trigger Property="IsGrouping" Value="true">
                            <Setter Property="ScrollViewer.CanContentScroll" Value="false" />
                        </Trigger>
                        <Trigger Property="ScrollViewer.CanContentScroll" SourceName="DropDownScrollViewer" Value="false">
                            <Setter Property="Canvas.Top" TargetName="OpaqueRect" Value="{Binding VerticalOffset, ElementName=DropDownScrollViewer}"/>
                            <Setter Property="Canvas.Left" TargetName="OpaqueRect" Value="{Binding HorizontalOffset, ElementName=DropDownScrollViewer}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource DisabledForeground}" />
                            <Setter Property="Background" TargetName="Border" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter Property="BorderBrush" TargetName="Border" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>

            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default">
                <Setter Property="FocusVisualStyle" Value="{StaticResource DottedKeyboardFocusVisualStyle}"/>
            </Trigger>
            <Trigger Property="IsEditable" Value="true">
                <Setter Property="BorderBrush" Value="{StaticResource ComboBoxBorderBrush}"/>
                <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
                <Setter Property="IsTabStop" Value="false" />
                <Setter Property="Template" Value="{StaticResource ComboBoxEditableTemplate}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource WPFComboBoxStyle}" TargetType="{x:Type ComboBox}"/>

    <Style x:Key="WPFComboBoxItemStyle"
           TargetType="{x:Type ComboBoxItem}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource ComboBoxItemFocusVisual}"/>
        <Setter Property="Padding" Value="7,2,7,2"/>
        <Setter Property="Margin" Value="0,2,0,2"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness}"/>
        <Setter Property="Background"
                Value="{StaticResource PopupBackground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="Foreground" Value="{StaticResource PopupForeground}"/>
        <Setter Property="MinHeight" Value="{StaticResource Windows11Dark.MinHeight}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ComboBoxItem}">
                    <Grid>
                        <Border x:Name="SelectionIndicator"
                                        HorizontalAlignment="Left"            
                                        CornerRadius="1.5"
                                        Height="12"
                                        Width="2"
                                        Visibility="Collapsed"     
                                        Background="{StaticResource PrimaryBackground}" />

                        <Border Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            SnapsToDevicePixels="true">
                            <ContentPresenter x:Name="contentPresenter" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                            <Border.Triggers>
                                <!-- Animates the SelectionIndicator -->
                                <EventTrigger RoutedEvent="Border.MouseDown">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="SelectionIndicator" 
                                                     Storyboard.TargetProperty="Height"
                                                     From="12" 
                                                     To="7" 
                                                     Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="Border.MouseUp">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="SelectionIndicator" 
                                                     Storyboard.TargetProperty="Height"
                                                     From="7" 
                                                     To="12" 
                                                     Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </Border.Triggers>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsHighlighted"
                                 Value="true">
                            <Setter TargetName="border"
                                    Property="Background"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter TargetName="border"
                                    Property="BorderBrush"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter Property="TextElement.Foreground"
                                    TargetName="contentPresenter"
                                    Value="{StaticResource PopupHoveredForeground}"/>
                        </Trigger>
                        <Trigger Property="IsSelected"
                                 Value="true">
                            <Setter TargetName="border"
                                    Property="Background"
                                    Value="{StaticResource PopupSelectedBackground}"/>
                            <Setter TargetName="border"
                                    Property="BorderBrush"
                                    Value="{StaticResource PopupSelectedBackground}"/>
                            <Setter Property="TextElement.Foreground"
                                    TargetName="contentPresenter"
                                    Value="{StaticResource PopupSelectedForeground}"/>
                            <Setter Property="Visibility" TargetName="SelectionIndicator" Value="Visible"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="border"
                                    Property="Background"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter TargetName="border"
                                    Property="BorderBrush"
                                    Value="{StaticResource PopupHoveredBackground}"/>
                            <Setter Property="TextElement.Foreground"
                                    TargetName="contentPresenter"
                                    Value="{StaticResource PopupHoveredForeground}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter TargetName="border"
                                    Property="Background"
                                    Value="{StaticResource PopupBackground}"/>
                            <Setter TargetName="border"
                                    Property="BorderBrush"
                                    Value="{StaticResource BorderAlt}"/>
                            <Setter Property="TextElement.Foreground"
                                    TargetName="contentPresenter"
                                    Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource CheckKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource WPFComboBoxItemStyle}" TargetType="{x:Type ComboBoxItem}"/>

</ResourceDictionary>
