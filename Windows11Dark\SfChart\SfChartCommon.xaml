<ResourceDictionary 
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF" 
    xmlns:local="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF">

<ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <DataTemplate x:Key="SegmentDragInfo">
        <StackPanel Orientation="Vertical" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Rectangle Grid.ColumnSpan="3" VerticalAlignment="Stretch" Opacity="0.6" Fill="{Binding Brush}"></Rectangle>
                <ContentControl  Grid.Column="0" ContentTemplate="{Binding PrefixLabelTemplate}" VerticalAlignment="Bottom" VerticalContentAlignment="Bottom"/>
                <TextBlock Grid.Column="1" Margin="3" Foreground="White" FontSize="20"  Text="{Binding NewValue, StringFormat='.###'}"></TextBlock>
                <ContentControl Grid.Column="2" ContentTemplate="{Binding PostfixLabelTemplate}" VerticalContentAlignment="Bottom"/>
            </Grid>
            <TextBlock Text="&#xe709;"
                       FontSize="14"
                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons" 
                       Foreground="{Binding Brush}"
                       Opacity="0.6"
                       Margin="0,-3,0,0" 
                       RenderTransformOrigin="0.5,0.5">
                <TextBlock.RenderTransform>
                    <RotateTransform Angle="180"></RotateTransform>
                </TextBlock.RenderTransform>
            </TextBlock>
        </StackPanel>
    </DataTemplate>

    <Style x:Key="pathStyle" TargetType="Path">
        <Setter Property="Stroke" Value="Black"></Setter>
        <Setter Property="StrokeThickness" Value="1"></Setter>
    </Style>

    <DataTemplate x:Key="AdornmentLabelTemplate">
        <Border Background="{Binding Converter={StaticResource labelBackgroundConverter}}" VerticalAlignment="Stretch" HorizontalAlignment="Stretch" BorderBrush="{Binding BorderBrush}" BorderThickness="{Binding BorderThickness}">
            <TextBlock Text="{Binding Converter={StaticResource labelContentPathConverter}}" IsHitTestVisible="False" FontStyle="{Binding FontStyle}" Margin="{Binding Margin}" FontSize="{Binding FontSize}" FontFamily="{Binding FontFamily}" Foreground="{Binding Converter={StaticResource labelForegroundConverter}, ConverterParameter=true}"  />
       </Border>
    </DataTemplate>

    <DataTemplate x:Key="AdornmentDefaultLabelTemplate">
        <Border Background="{Binding Converter={StaticResource labelBackgroundConverter}}" VerticalAlignment="Stretch" HorizontalAlignment="Stretch" BorderBrush="{Binding BorderBrush}" BorderThickness="{Binding BorderThickness}">
            <TextBlock Foreground="{Binding Converter={StaticResource labelForegroundConverter},ConverterParameter=false}" IsHitTestVisible="False" FontFamily="{Binding FontFamily}" FontStyle="{Binding FontStyle}" Margin="{Binding Margin}" FontSize="{Binding FontSize}" Text="{Binding Converter={StaticResource labelContentPathConverter}}" />
         </Border>
    </DataTemplate>

    <DataTemplate x:Key="SegmentDragInfoOppLeft">
        <StackPanel Orientation="Horizontal" Margin="0,0,20,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Rectangle Grid.ColumnSpan="3" VerticalAlignment="Stretch" Opacity="0.6" Fill="{Binding Brush}"></Rectangle>
                <ContentControl  Grid.Column="0" ContentTemplate="{Binding PrefixLabelTemplate}" VerticalAlignment="Bottom" VerticalContentAlignment="Bottom"/>
                <TextBlock Grid.Column="1" Margin="3" Foreground="White" FontSize="20"  Text="{Binding NewValue, StringFormat='.###'}"></TextBlock>
                <ContentControl Grid.Column="2" ContentTemplate="{Binding PostfixLabelTemplate}" VerticalContentAlignment="Bottom"/>
            </Grid>
            <TextBlock Text="&#xe709;"
                       FontSize="14"
                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons" 
                       Foreground="{Binding Brush}"
                       Opacity="0.6"
                       Margin="-4,0,0,0" 
                       RenderTransformOrigin="0.5,0.5">
                <TextBlock.RenderTransform>
                    <RotateTransform Angle="90"></RotateTransform>
                </TextBlock.RenderTransform>
            </TextBlock>
        </StackPanel>
    </DataTemplate>

    <DataTemplate x:Key="SegmentDragInfoOppRight">
        <StackPanel Orientation="Horizontal" Margin="20,0,0,0">
            <TextBlock Text="&#xe709;"
                       FontSize="14"
                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons" 
                       Foreground="{Binding Brush}"
                       Opacity="0.6"
                       Margin="0,0,-4,0" 
                       RenderTransformOrigin="0.5,0.5">
                <TextBlock.RenderTransform>
                    <RotateTransform Angle="270"></RotateTransform>
                </TextBlock.RenderTransform>
            </TextBlock>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Rectangle Grid.ColumnSpan="3" VerticalAlignment="Stretch" Opacity="0.6" Fill="{Binding Brush}"></Rectangle>
                <ContentControl  Grid.Column="0" ContentTemplate="{Binding PrefixLabelTemplate}" VerticalAlignment="Bottom" VerticalContentAlignment="Bottom"/>
                <TextBlock Grid.Column="1" Margin="3" Foreground="White" FontSize="20"  Text="{Binding NewValue, StringFormat='.###'}"></TextBlock>
                <ContentControl Grid.Column="2" ContentTemplate="{Binding PostfixLabelTemplate}" VerticalContentAlignment="Bottom"/>
            </Grid>
        </StackPanel>
    </DataTemplate>

    <DataTemplate x:Key="SegmentDragInfoOppBottom">
        <StackPanel Orientation="Vertical"
                    Margin="0,20,0,0">
            <TextBlock Text="&#xe709;"
                       FontSize="14"
                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons" 
                       Foreground="{Binding Brush}"
                       Opacity="0.6"
                       Margin="0,0,0,-4" 
                       RenderTransformOrigin="0.5,0.5">
            </TextBlock>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Rectangle Grid.ColumnSpan="3"
                           VerticalAlignment="Stretch"
                           Opacity="0.6"
                           Fill="{Binding Brush}"></Rectangle>
                <ContentControl  Grid.Column="0"
                                 ContentTemplate="{Binding PrefixLabelTemplate}"
                                 VerticalAlignment="Bottom"
                                 VerticalContentAlignment="Bottom" />
                <TextBlock Grid.Column="1"
                           Margin="3"
                           Foreground="White"
                           FontSize="20"
                           Text="{Binding NewValue, StringFormat='.###'}"></TextBlock>
                <ContentControl Grid.Column="2"
                                ContentTemplate="{Binding PostfixLabelTemplate}"
                                VerticalContentAlignment="Bottom" />
            </Grid>
        </StackPanel>
    </DataTemplate>

    <DataTemplate x:Key="SeriesDragInfoVertical">
        <Grid>
            <StackPanel Height="{Binding OffsetY}" Orientation="Horizontal">
                <Grid RenderTransformOrigin="0.5,0.5">
                    <Grid.RenderTransform>
                        <RotateTransform Angle="{Binding IsNegative, Converter={StaticResource dragElementRotateConverter}}"/>
                    </Grid.RenderTransform>
                    <TextBlock Text="&#xe702;"
                        FontSize="16"
                        FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons" 
                        Foreground="{Binding Brush}"
                        VerticalAlignment="Top"
                        Margin="0,0,0,0" 
                        RenderTransformOrigin="0.5,0.5">
                        <TextBlock.RenderTransform>
                            <TransformGroup>
                                <TransformGroup.Children>
                                    <RotateTransform Angle="180" />
                                    <ScaleTransform ScaleX="1" ScaleY="-1" />
                                </TransformGroup.Children>
                            </TransformGroup>
                        </TextBlock.RenderTransform>
                    </TextBlock>
                    <Rectangle VerticalAlignment="Stretch" Width="3" Fill="{Binding Brush}"/>
                </Grid>
                <TextBlock Foreground="{Binding Brush}" VerticalAlignment="Center" FontSize="17" HorizontalAlignment="Center"  Text="{Binding
                 Delta , StringFormat='.###'}"></TextBlock>
            </StackPanel>
        </Grid>
    </DataTemplate>
    <DataTemplate x:Key="SeriesDragInfoHorizontal">
        <Grid>
            <StackPanel Width="{Binding OffsetY}" >
                <Grid RenderTransformOrigin="0.5,0.5">
                    <Grid.RenderTransform>
                        <RotateTransform Angle="{Binding IsNegative, Converter={StaticResource dragElementRotateConverter}}"/>
                    </Grid.RenderTransform>
                    <Rectangle HorizontalAlignment="Stretch" Height="3" Fill="{Binding Brush}"/>
                    <TextBlock Text="&#xe702;"
                               FontSize="16"
                               FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons" 
                               Foreground="{Binding Brush}"
                               HorizontalAlignment="Left"
                               Margin="0,0,0,0" 
                               RenderTransformOrigin="0.5,0.5">
                        <TextBlock.RenderTransform>
                            <TransformGroup>
                                <TransformGroup.Children>
                                    <RotateTransform Angle="270" />
                                    <ScaleTransform ScaleX="1" ScaleY="-1" />
                                </TransformGroup.Children>
                            </TransformGroup>
                        </TextBlock.RenderTransform>
                    </TextBlock>
                </Grid>
                <TextBlock Foreground="{Binding Brush}" VerticalAlignment="Center" FontSize="17" HorizontalAlignment="Center"  Text="{Binding
                 Delta , StringFormat='.###'}"></TextBlock>
            </StackPanel>
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="AxisLabelsCustomTemplate">
        <Grid >
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <ContentControl  Grid.Column="0" x:Name="PrefixLabel" ContentTemplate="{Binding PrefixLabelTemplate}" VerticalAlignment="Bottom" VerticalContentAlignment="Bottom"/>
            <TextBlock x:Name="textBlock" Grid.Column="1" Text="{Binding LabelContent}"/>
            <ContentControl Grid.Column="2" x:Name="PostfixLabel" ContentTemplate="{Binding PostfixLabelTemplate}" VerticalContentAlignment="Bottom"/>
        </Grid>
    </DataTemplate>

    <Style x:Key="lineStyle" TargetType="Line">
        <Setter Property="Stroke" Value="Black"></Setter>
    </Style>

    <DataTemplate x:Key="trackerSymbolTemplate">
        <Ellipse Fill="{Binding Fill}" Stroke="{Binding Stroke}" StrokeThickness="{Binding StrokeThickness}" Height="{Binding Height}" Width="{Binding Width}">
        </Ellipse>
    </DataTemplate>
    
    <Style x:Key="trackBallLineStyle" TargetType="Line">
        <Setter Property="Stroke" Value="{StaticResource Series10}"></Setter>
        <Setter Property="StrokeThickness" Value="1"></Setter>
    </Style>

    <LinearGradientBrush x:Key="backgroundbrush"  EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFEBFFFF" Offset="0.388"/>
        <GradientStop Color="#FFCAF5F8" Offset="0.398"/>
        <GradientStop Color="#FFD1F8FA" Offset="0.791"/>
        <GradientStop Color="#FFEAFFFF" Offset="0.801"/>
        <GradientStop Color="#FFEAFFFF" Offset="0.99"/>
        <GradientStop Color="#FFA3B9CC" Offset="1"/>
    </LinearGradientBrush>

    <Style x:Key="roundthumbstyle" TargetType="Thumb">
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Width" Value="10"/>
        <Setter Property="Height" Value="10"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Thumb">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ColorAnimation Duration="0" To="#F2FFFFFF" Storyboard.TargetProperty="(Shape.Fill).(GradientBrush.GradientStops)[1].(GradientStop.Color)" Storyboard.TargetName="BackgroundGradient"/>
                                        <ColorAnimation Duration="0" To="#CCFFFFFF" Storyboard.TargetProperty="(Shape.Fill).(GradientBrush.GradientStops)[2].(GradientStop.Color)" Storyboard.TargetName="BackgroundGradient"/>
                                        <ColorAnimation Duration="0" To="#7FFFFFFF" Storyboard.TargetProperty="(Shape.Fill).(GradientBrush.GradientStops)[3].(GradientStop.Color)" Storyboard.TargetName="BackgroundGradient"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimation Duration="0" To="#D8FFFFFF" Storyboard.TargetProperty="(Shape.Fill).(GradientBrush.GradientStops)[0].(GradientStop.Color)" Storyboard.TargetName="BackgroundGradient"/>
                                        <ColorAnimation Duration="0" To="#C6FFFFFF" Storyboard.TargetProperty="(Shape.Fill).(GradientBrush.GradientStops)[1].(GradientStop.Color)" Storyboard.TargetName="BackgroundGradient"/>
                                        <ColorAnimation Duration="0" To="#8CFFFFFF" Storyboard.TargetProperty="(Shape.Fill).(GradientBrush.GradientStops)[2].(GradientStop.Color)" Storyboard.TargetName="BackgroundGradient"/>
                                        <ColorAnimation Duration="0" To="#3FFFFFFF" Storyboard.TargetProperty="(Shape.Fill).(GradientBrush.GradientStops)[3].(GradientStop.Color)" Storyboard.TargetName="BackgroundGradient"/>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid>
                            <Ellipse x:Name="BackgroundGradient" Fill="{StaticResource backgroundbrush}" Stroke="#FF6E8DAC" Width="{TemplateBinding Width}" Height="{TemplateBinding Height}"/>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

  <DataTemplate x:Key="trackBallLabel">
    <Grid>
            <Polygon StrokeThickness="0.3"
                     Stroke="{StaticResource TooltipBorder}"
                     Fill="{StaticResource TooltipBackground}"
                     HorizontalAlignment="Stretch"
                     VerticalAlignment="Stretch"
                     Points="{Binding PolygonPoints}" />
            <TextBlock  FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                        Text="{Binding ValueY}"
                        Foreground="{StaticResource TooltipForeground}"
                        Padding="{StaticResource TrackBallLabelPadding}" />
        </Grid>
  </DataTemplate>
    <DataTemplate x:Key="axisTrackBallLabel">
        <Grid>
            <Polygon StrokeThickness="1"
                     Stroke="{StaticResource TooltipBorder}"
                     Fill="{StaticResource TooltipBackground}"
                     Points="{Binding PolygonPoints}" />
            <TextBlock  FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                        Text="{Binding ValueX}"
                        Padding="4,0,4,3"
                        Foreground="{StaticResource TooltipForeground}"></TextBlock>
            <TextBlock  FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                        Text="{Binding ValueY}"
                        Padding="4,0,4,3"
                        Foreground="{StaticResource TooltipForeground}"></TextBlock>
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="axisCrosshairLabel">
    <Border BorderThickness="1" BorderBrush="{StaticResource TooltipBorder}" Background="{StaticResource TooltipBackground}">
      <Grid>
        <TextBlock  FontSize="{StaticResource Windows11Dark.BodyTextStyle}" Text="{Binding ValueX}" Foreground="{StaticResource TooltipForeground}"></TextBlock>
                <TextBlock FontSize="{StaticResource Windows11Dark.BodyTextStyle}" Text="{Binding ValueY}" Foreground="{StaticResource TooltipForeground}"></TextBlock>
      </Grid>
    </Border>
  </DataTemplate>

    <DataTemplate x:Key="DefaultTooltipTemplate">
        <Border Background="Transparent" 
                BorderBrush="Transparent" 
                CornerRadius="3">
            <TextBlock Text="{Binding YData}" Style="{Binding Series.Area.Tooltip.LabelStyle}" />
        </Border>
    </DataTemplate>

    <DataTemplate x:Key="AnnotationTooltipTemplate">
        <Border Background="{StaticResource TooltipBackground}"
                BorderThickness="1"
                Padding="4">
            <TextBlock Text="{Binding}"
                       Foreground="{StaticResource TooltipForeground}"
                       FontSize="{StaticResource Windows11Dark.CaptionText}" />
        </Border>
    </DataTemplate>
    <DataTemplate x:Key="AxisLabel">
        <Border   CornerRadius="2">
            <Grid Background="#1E90FF" MinWidth="28"  MinHeight="20">
                <TextBlock Text="{Binding}" Padding="2" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White"></TextBlock>
            </Grid>
        </Border>
    </DataTemplate>

  <DataTemplate x:Key="FinancialTooltipTemplate">
        <Border Background="Transparent" BorderBrush="Transparent" CornerRadius="3">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <TextBlock Grid.Row="0" Grid.Column="0" Text="{Binding Source={local:ChartLocalizationResourceExtension ResourceName=Open}}" Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="0" Grid.Column="1" Text=" : " Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Margin="2,0,0,0" Grid.Row="0" Grid.Column="2" Text="{Binding Open}" Style="{Binding Series.Area.Tooltip.LabelStyle}" />
                <TextBlock Text="{Binding  Source={local:ChartLocalizationResourceExtension ResourceName=High}}" Grid.Row="1" Grid.Column="0" Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="1" Grid.Column="1" Text=" : " Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Margin="2,0,0,0" Grid.Row="1" Grid.Column="2" Text="{Binding High}" Style="{Binding Series.Area.Tooltip.LabelStyle}" />
                <TextBlock Grid.Row="2" Text="{Binding  Source={local:ChartLocalizationResourceExtension ResourceName=Low}}" Grid.Column="0" Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="2" Grid.Column="1" Text=" : " Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Margin="2,0,0,0" Grid.Row="2" Grid.Column="2" Text="{Binding Low}" Style="{Binding Series.Area.Tooltip.LabelStyle}" />
                <TextBlock Text="{Binding  Source={local:ChartLocalizationResourceExtension ResourceName=Close}}" Grid.Row="3" Grid.Column="0" Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="3" Grid.Column="1" Text=" : " Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Margin="2,0,0,0" Grid.Row="3" Grid.Column="2" Text="{Binding Close}" Style="{Binding Series.Area.Tooltip.LabelStyle}" />
            </Grid>
        </Border>
    </DataTemplate>
    
    <DataTemplate x:Key="RangeTooltipTemplate">
        <Border Background="Transparent" BorderBrush="Transparent" CornerRadius="3">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <TextBlock Grid.Row="0" Grid.Column="0" Text="{Binding  Source={local:ChartLocalizationResourceExtension ResourceName=High}}" Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="0" Grid.Column="1" Text=" : " Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="0" Grid.Column="2" Margin="2,0,0,0" Text="{Binding High}" Style="{Binding Series.Area.Tooltip.LabelStyle}" />
                <TextBlock Grid.Row="1" Grid.Column="0" Text="{Binding  Source={local:ChartLocalizationResourceExtension ResourceName=Low}}"  Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="1" Grid.Column="1" Text=" : " Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="1" Grid.Column="2" Margin="2,0,0,0" Text="{Binding Low}" Style="{Binding Series.Area.Tooltip.LabelStyle}" />
            </Grid>
        </Border>
    </DataTemplate>

    <DataTemplate x:Key="BoxWhiskerTooltipTemplate">
        <Border Background="Transparent" BorderBrush="Transparent" CornerRadius="3">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="4.5*"></ColumnDefinition>
                    <ColumnDefinition Width="0.5*"></ColumnDefinition>
                    <ColumnDefinition Width="4.5*"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <TextBlock Grid.Row="0" Grid.Column="0" Text="{Binding Source={local:ChartLocalizationResourceExtension ResourceName=Maximum}}" Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="0" Grid.Column="1" Text=" : " Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Margin="2,0,0,0" Grid.Row="0" Grid.Column="2" Text="{Binding Maximum}" Style="{Binding Series.Area.Tooltip.LabelStyle}" />
                <TextBlock Grid.Row="1" Grid.Column="0"  Text="{Binding Source={local:ChartLocalizationResourceExtension ResourceName=Q3}}" Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="1" Grid.Column="1" Text=" : " Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Margin="2,0,0,0" Grid.Row="1" Grid.Column="2" Text="{Binding UppperQuartile}" Style="{Binding Series.Area.Tooltip.LabelStyle}" />
                <TextBlock Grid.Row="2" Grid.Column="0"  Text="{Binding Source={local:ChartLocalizationResourceExtension ResourceName=Median}}" Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="2" Grid.Column="1" Text=" : " Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Margin="2,0,0,0" Grid.Row="2" Grid.Column="2" Text="{Binding Median}" Style="{Binding Series.Area.Tooltip.LabelStyle}" />
                <TextBlock Grid.Row="3" Grid.Column="0" Text="{Binding Source={local:ChartLocalizationResourceExtension ResourceName=Q1}}" Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="3" Grid.Column="1" Text=" : " Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Margin="2,0,0,0" Grid.Row="3" Grid.Column="2" Text="{Binding LowerQuartile}" Style="{Binding Series.Area.Tooltip.LabelStyle}" />
                <TextBlock Grid.Row="4" Grid.Column="0" Text="{Binding Source={local:ChartLocalizationResourceExtension ResourceName=Minimum}}" Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Grid.Row="4" Grid.Column="1" Text=" : " Style="{Binding Series.Area.Tooltip.LabelStyle}"/>
                <TextBlock Margin="2,0,0,0" Grid.Row="4" Grid.Column="2" Text="{Binding Minimum}" Style="{Binding Series.Area.Tooltip.LabelStyle}" />
            </Grid>
        </Border>
    </DataTemplate>

    <DataTemplate x:Key="LineTooltipTemplate">
        <Border Background="{StaticResource ContentBackgroundBrush}" BorderBrush="{StaticResource ContentBorderBrush}" BorderThickness="{StaticResource TooltipBorderThickness}" CornerRadius="{StaticResource TooltipCornerRadius}" Padding="{StaticResource TooltipPadding}">
            <TextBlock Text="{Binding YData}" Foreground="{StaticResource ContentForegroundBrush}" FontSize="{StaticResource TooltipFontSize}" />
        </Border>
    </DataTemplate>

    <DataTemplate x:Key="AreaTooltipTemplate">
        <Border Background="#FFFFFFFF"
                BorderBrush="#FF808080"
                BorderThickness="1"
                CornerRadius="3"
                Padding="4">
            <TextBlock Text="{Binding YData}"
                       Foreground="#FF666666"
                       FontSize="{StaticResource TooltipFontSize}" />
        </Border>
    </DataTemplate>

    <DataTemplate x:Key="textBlockAnnotation">
        <TextBlock/>
    </DataTemplate>

    <DataTemplate x:Key="textBoxAnnotation">
        <TextBox AcceptsReturn="True" AcceptsTab="True"/>
    </DataTemplate>

  <Style TargetType="Path" x:Key="tooltipPathStyle">
        <Setter Property="Fill" Value="{StaticResource TooltipBackground}"></Setter>
        <Setter Property="Stroke" Value="{StaticResource TooltipBorder}"></Setter>
  </Style>

  <Style TargetType="TextBlock" x:Key="tooltipLabelStyle">
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="Foreground" Value="{StaticResource TooltipForeground}"/>
  </Style>
  
  <Style x:Key="printDialogBorderStyle" TargetType="Border">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="Background" Value="{StaticResource PopupBackground}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1}"/>
  </Style>

  <Style x:Key="printDialogDashedLineStyle" TargetType="Rectangle">
        <Setter Property="Stroke" Value="{StaticResource BorderAlt}"/>
        <Setter Property="StrokeThickness" Value="{StaticResource Windows11Dark.StrokeThickness1}"/>
  </Style>

</ResourceDictionary>
