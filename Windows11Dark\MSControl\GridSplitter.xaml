<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="DefaultGridSplitterPreviewStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Fill="{StaticResource PrimaryBackground}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="GridSplitterFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="{StaticResource Windows11Dark.FocusMargin}" 
                               Stroke="{StaticResource BorderAlt1}" 
                               StrokeThickness="{StaticResource Windows11Dark.StrokeThickness1}" 
                               StrokeDashArray="{StaticResource Windows11Dark.StrokeDashArray}"
                               SnapsToDevicePixels="true" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFGridSplitterStyle" TargetType="{x:Type GridSplitter}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource GridSplitterFocusVisual}"/>
        <Setter Property="Background" Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="PreviewStyle" Value="{StaticResource DefaultGridSplitterPreviewStyle}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type GridSplitter}">
                    <Border x:Name="border" 
                            BorderBrush="{TemplateBinding BorderBrush}" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            Background="{TemplateBinding Background}"/>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt1}"/>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource BorderAlt1}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt}"/>
                        </Trigger>
                        <Trigger Property="IsDragging" Value="true">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsDragging" Value="true"></Condition>
                                <Condition Property="ShowsPreview" Value="false"></Condition>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource PrimaryBackground}"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource WPFGridSplitterStyle}" TargetType="{x:Type GridSplitter}"/>
</ResourceDictionary>
