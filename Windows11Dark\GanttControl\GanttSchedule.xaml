<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" 
                    xmlns:system="clr-namespace:System;assembly=mscorlib"
                    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:ganttcontrol="clr-namespace:Syncfusion.Windows.Controls.Gantt;assembly=Syncfusion.Gantt.Wpf">

    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ToolTip.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!-- Gantt Schedule -->

    <!--Gantt Schedule Styles-->
    <Style TargetType="{x:Type ganttcontrol:GanttSchedule}">
        <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="ClipToBounds" Value="True"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ganttcontrol:GanttSchedule}">
                    <Border SnapsToDevicePixels="True" 
                            ClipToBounds="True" 
                            Background="{TemplateBinding Background}" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            BorderBrush="{TemplateBinding BorderBrush}">
                        <ItemsPresenter x:Name="PART_ItemsPresenter" 
                                        Margin="0" 
                                        Width="{TemplateBinding Width}"
                                        HorizontalAlignment="Left" 
                                        VerticalAlignment="Top" 
                                        Height="Auto"/>
                    </Border>                   
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type ganttcontrol:GanttScheduleRow}">
        <Setter Property="MinHeight" Value="20"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ganttcontrol:GanttScheduleRow}">
                    <ganttcontrol:GanttScheduleRowPanel x:Name="PART_RowItemsPresenter" 
                                                        MinHeight="{TemplateBinding MinHeight}" 
                                                        Width="Auto" 
                                                        HorizontalAlignment="Left"
                                                        SnapsToDevicePixels="True" 
                                                        Background="{TemplateBinding Background}">
                    </ganttcontrol:GanttScheduleRowPanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type ganttcontrol:GanttScheduleCell}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ganttcontrol:GanttScheduleCell}">
                    <Border BorderThickness="1,1,0,0"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}" 
                            SnapsToDevicePixels="True" >
                       <ToolTipService.ToolTip>
                            <ToolTip Style="{StaticResource WPFToolTipStyle}" Content="{TemplateBinding CellToolTip}"/>
                        </ToolTipService.ToolTip>
                           <ContentPresenter  Margin="1,0,1,0"
                                              SnapsToDevicePixels="True"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
   
</ResourceDictionary>
