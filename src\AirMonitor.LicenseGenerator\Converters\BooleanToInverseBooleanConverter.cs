using System.Globalization;
using System.Windows.Data;

namespace AirMonitor.LicenseGenerator.Views;

/// <summary>
/// 布尔值反转转换器
/// </summary>
public class BooleanToInverseBooleanConverter : IValueConverter
{
    public static readonly BooleanToInverseBooleanConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return !boolValue;
        }
        return false;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return !boolValue;
        }
        return true;
    }
}
