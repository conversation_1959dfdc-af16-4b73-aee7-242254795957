<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:input="clr-namespace:Syncfusion.Windows.Controls.Input;assembly=Syncfusion.SfInput.WPF"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="MaskedEdit.Static.Border" Color="#333333" />

    <SolidColorBrush x:Key="MaskedEdit.MouseOver.Border" Color="#757575" />

    <SolidColorBrush x:Key="MaskedEdit.Focused.Border" Color="#0279FF" />

    <LinearGradientBrush x:Key="MaskedEdit.Static.BorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="MaskedEdit.Static.BorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="MaskedEdit.Static.BorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <DataTemplate x:Key="WatermarkContentTemplate">
        <TextBlock Style="{x:Null}" Text="{Binding}" />
    </DataTemplate>

    <Style x:Key="SyncfusionSfMaskedEditStyle" TargetType="input:SfMaskedEdit">
        <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}" />
        <Setter Property="SelectionBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="BorderBrush" Value="{StaticResource MaskedEdit.Static.BorderBrush}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant1}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="WatermarkTemplate" Value="{StaticResource WatermarkContentTemplate}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type input:SfMaskedEdit}">
                    <Grid>
                        <Border
                            x:Name="Border"
                            Padding="4 0 0 0"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            SnapsToDevicePixels="true">
                            <Grid>
                                <ScrollViewer
                                    x:Name="PART_ContentHost"
                                    VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Background="Transparent"
                                    Foreground="{TemplateBinding Foreground}"
                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                <ContentControl
                                    x:Name="PART_Watermark"
                                    Grid.Row="0"
                                    Padding="{TemplateBinding Padding}"
                                    Margin="3,0,0,0"
                                    VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Background="{StaticResource ContentBackgroundAlt4}"
                                    Content="{TemplateBinding Watermark}"
                                    ContentTemplate="{TemplateBinding WatermarkTemplate}"
                                    ContentTemplateSelector="{TemplateBinding WatermarkTemplateSelector}"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                    FontStretch="{TemplateBinding FontStretch}"
                                    FontStyle="{TemplateBinding FontStyle}"
                                    FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"
                                    Foreground="{StaticResource PlaceholderForeground}"
                                    IsHitTestVisible="False"
                                    IsTabStop="False"
                                    Visibility="{TemplateBinding Visibility}" />
                            </Grid>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger Property="HasError" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource ErrorBorder}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource MaskedEdit.Static.BorderBrushHovered}" />
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt5}" />
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource MaskedEdit.Static.BorderBrushFocused}" />
                            <Setter Property="Background" TargetName="Border" Value="{StaticResource ContentBackground}"/>
                        </Trigger>
                        <Trigger Property="IsReadOnly" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource MaskedEdit.Static.BorderBrushFocused}" />
                            <Setter Property="Background" TargetName="Border" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}"/>
                            <Setter Property="Padding" Value="0,0,0,-1"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="PART_ContentHost" Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>

                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfMaskedEditStyle}" TargetType="input:SfMaskedEdit" />

</ResourceDictionary>
