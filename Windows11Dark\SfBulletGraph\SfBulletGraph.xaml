<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:bulletGraph="clr-namespace:Syncfusion.UI.Xaml.BulletGraph;assembly=Syncfusion.SfBulletGraph.WPF"
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">
    
    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <DataTemplate x:Key="DefaultToolTipTemplate">
        <Border  x:Name="ToolTipBorder"
                 BorderBrush="{StaticResource TooltipBorder}"
                 BorderThickness="1"
                 Background="{StaticResource TooltipBackground}"
                 Effect="{StaticResource Default.ShadowDepth3}"
                 CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
            <Border.Margin>
                <Thickness>4,0,4,4</Thickness>
            </Border.Margin>
            <TextBlock x:Name="ToolTipText"
                       Text="{Binding}"
                       FontSize="{StaticResource Windows11Dark.CaptionText}"
                       Foreground="{StaticResource TooltipForeground}"
                       Margin="12 8" />
        </Border>
    </DataTemplate>

    <DataTemplate x:Key="DefaultQualitativeRangeToolTipTemplate">
        <Border x:Name="QualitativeRangeToolTipBorder"
                BorderBrush="{StaticResource Series10}"
                BorderThickness="{StaticResource Windows11Dark.BorderThickness}"
                Effect="{StaticResource Default.ShadowDepth3}"
                CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                Background="{Binding RangeStroke}">
            <Border.Margin>
                <Thickness>6,0,6,6</Thickness>
            </Border.Margin>
            <StackPanel Orientation="Horizontal"
                            Margin="12 8">
                    <TextBlock Text="{Binding Caption}"
                               FontSize="{StaticResource Windows11Dark.CaptionText}"
                               Foreground="{StaticResource TooltipForeground}" />
                    <TextBlock Text="{Binding RangeStart}"
                               FontSize="{StaticResource Windows11Dark.CaptionText}"
                               Foreground="{StaticResource TooltipForeground}" />
                    <TextBlock Text="-"
                               FontSize="{StaticResource Windows11Dark.CaptionText}"
                               Foreground="{StaticResource TooltipForeground}"
                               Width="10"
                               TextAlignment="Center" />
                    <TextBlock Text="{Binding RangeEnd}"
                               FontSize="{StaticResource Windows11Dark.CaptionText}"
                               Foreground="{StaticResource TooltipForeground}" />
                </StackPanel>
        </Border>
    </DataTemplate>

    <Style x:Key="SyncfusionSfBulletGraphStyle"
           TargetType="bulletGraph:SfBulletGraph">
        <Setter Property="Background"
                Value="Transparent"></Setter>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"></Setter>
        <Setter Property="BorderThickness"
                Value="0"></Setter>
        <Setter Property="Foreground"
                Value="{StaticResource ContentForeground}"></Setter>
        <Setter  Property="ComparativeMeasureSymbolStroke"
                 Value="{StaticResource Series10}"></Setter>
        <Setter  Property="FeaturedMeasureBarStroke"
                 Value="{StaticResource Series10}"></Setter>
        <Setter  Property="MajorTickStroke"
                 Value="{StaticResource BorderAlt1}"></Setter>
        <Setter  Property="MinorTickStroke"
                 Value="{StaticResource BorderAlt1}"></Setter>
        <Setter x:Name="LabelStroke"
                Property="LabelStroke"
                Value="{StaticResource ContentForeground}"></Setter>
        <Setter Property="FeaturedMeasureToolTipTemplate"
                Value="{StaticResource DefaultToolTipTemplate}" />
        <Setter Property="ComparativeMeasureToolTipTemplate"
                Value="{StaticResource DefaultToolTipTemplate}" />
        <Setter Property="QualitativeRangeToolTipTemplate"
                Value="{StaticResource DefaultQualitativeRangeToolTipTemplate}" />
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"></Setter>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}"></Setter>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.CaptionText}"></Setter>
        <Setter Property="LabelSize"
                Value="{StaticResource Windows11Dark.CaptionText}"></Setter>
    </Style>

    <Style x:Key="SyncfusionSfBulletGraphQualitativeRangeStyle"
           TargetType="bulletGraph:QualitativeRange">
        <Setter Property="RangeStroke"
                Value="{StaticResource Series6}" />
        <Setter Property="IsTabStop" Value="False"/>
    </Style>

    <Style TargetType="bulletGraph:SfBulletGraph"
           BasedOn="{StaticResource SyncfusionSfBulletGraphStyle}" />

    <Style TargetType="bulletGraph:QualitativeRange"
           BasedOn="{StaticResource SyncfusionSfBulletGraphQualitativeRangeStyle}" />

</ResourceDictionary>
