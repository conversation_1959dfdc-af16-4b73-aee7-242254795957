<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib"
					xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:scheduler="clr-namespace:Syncfusion.UI.Xaml.Scheduler;assembly=Syncfusion.SfScheduler.WPF">

    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="SyncfusionAllDayExpanderButtonStyle" TargetType="ToggleButton" >
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Cursor" Value="Hand"/>
            </Trigger>
        </Style.Triggers>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Border
            x:Name="PART_Root"
            Background="Transparent"
            BorderBrush="{StaticResource BorderAlt}"
            BorderThickness="0">
                        <Path x:Name="PART_Expander" 
                  Fill="Transparent" 
                  Stroke="{StaticResource IconColor}" 
                  Height="4.5"
                  Width="8"   
                  Stretch="Fill" 
                  HorizontalAlignment="Center"
                  VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="PART_Expander" Property="Data" Value="M8 4.5L4.5 1L1 4.5" />
                        </Trigger>
                        <Trigger Property="IsChecked" Value="False">
                            <Setter TargetName="PART_Expander" Property="Data" Value="M1 0.5L4.5 4L8 0.5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionAllDayExpanderButtonStyle}" TargetType="ToggleButton"/>

    <Style x:Key="SyncfusionAllDayAppointmentViewControlStyle" TargetType="scheduler:AllDayAppointmentViewControl">
        <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,0,1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:AllDayAppointmentViewControl">
                    <Border BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid
                        Background="{TemplateBinding Background}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Border BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="0, 0, 1, 0" >
                                <ToggleButton
                                          x:Name="PART_ExpanderCell"
                                          Style="{StaticResource SyncfusionAllDayExpanderButtonStyle}"
                                          Width="15"
                                          Height="10"
                                          Margin="0,0,0,5"
                                          VerticalAlignment="Bottom" 
                                          IsChecked="{Binding RelativeSource={RelativeSource TemplatedParent},
                                                     Path=IsExpanded,
                                                     Mode=TwoWay,
                                                     UpdateSourceTrigger=PropertyChanged}"
                                          Visibility="{Binding RelativeSource={RelativeSource TemplatedParent},
                                                     Path=ExpanderVisibility,
                                                     Mode=TwoWay,
                                                     UpdateSourceTrigger=PropertyChanged}" >

                                </ToggleButton>
                            </Border>
                            <ScrollViewer
                            x:Name="PART_ScrollViewer"  AllowDrop="True"
                            Grid.Column="1" 
                            HorizontalScrollBarVisibility="Hidden"
                            IsDeferredScrollingEnabled="False"
                            VerticalScrollBarVisibility="Hidden" >

                                <ScrollViewer.Resources>
                                    <Style TargetType="{x:Type ScrollBar}">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="{x:Type ScrollBar}">
                                                    <Border Background="Transparent" />
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </ScrollViewer.Resources>

                                <scheduler:AllDayAppointmentPanel
                                x:Name="PART_AllDayAppointmentPanel"
                                Background="Transparent"  />
                            </ScrollViewer>

                        </Grid>
                    </Border>

                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionAllDayAppointmentViewControlStyle}" TargetType="scheduler:AllDayAppointmentViewControl"/>
</ResourceDictionary>
