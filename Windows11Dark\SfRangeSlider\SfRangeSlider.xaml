<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Input="clr-namespace:Syncfusion.Windows.Controls.Input;assembly=Syncfusion.SfInput.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark" />
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBlock.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ToolTip.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!--  Theme Keys for SfRangeSlider  -->

    <!--  Style for Thumb of SfRangeSlider  -->
    <Style x:Key="SyncfusionSfRangeSliderThumbStyle" TargetType="Thumb">
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1}" />
        <Setter Property="BorderBrush" Value="{StaticResource ContentBackground}" />
        <Setter Property="Background" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Thumb">
                    <Grid>
                        <Border
                            x:Name="ThumbOuterBorder"
                            
                            
                            BorderBrush="{StaticResource BorderAlt5}"
                            CornerRadius="10"
                            BorderThickness="1">
                            <Border
                                x:Name="ThumbBorder"
                                Width="20"
                                Height="20"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="4"
                                CornerRadius="10"
                                />
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="ThumbBorder" Property="MinHeight" Value="20" />
                            <Setter TargetName="ThumbBorder" Property="MinWidth" Value="20" />
                        </Trigger>

                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="ThumbBorder" Property="Background" Value="{StaticResource BorderAlt4}" />
                            <Setter TargetName="ThumbBorder" Property="BorderBrush" Value="{StaticResource ContentBackground}" />
                        </Trigger>

                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ThumbBorder" Property="Background" Value="{StaticResource PrimaryBackground}" />
                            <Setter TargetName="ThumbBorder" Property="BorderBrush" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="ThumbOuterBorder" Property="Background" Value="Transparent"/>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="MouseEnterAnimation">
                                    <Storyboard>
                                        <ThicknessAnimation Duration="0:0:0:0.167" Storyboard.TargetProperty="BorderThickness" Storyboard.TargetName="ThumbBorder" To="2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <RemoveStoryboard BeginStoryboardName="MouseEnterAnimation"/>
                            </Trigger.ExitActions>
                        </Trigger>

                        <Trigger Property="IsDragging" Value="True">
                            <Setter TargetName="ThumbBorder" Property="Background" Value="{StaticResource PrimaryBackground}" />
                            <Setter TargetName="ThumbBorder" Property="BorderBrush" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="ThumbOuterBorder" Property="Background" Value="Transparent"/>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="DraggingAnimation">
                                    <Storyboard>
                                        <ThicknessAnimation Duration="0:0:0:0.167" Storyboard.TargetProperty="BorderThickness" Storyboard.TargetName="ThumbBorder" To="6"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <RemoveStoryboard BeginStoryboardName="DraggingAnimation"/>
                            </Trigger.ExitActions>
                        </Trigger>

                        <Trigger Property="IsMouseOver" Value="False">
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="MouseLeaveAnimation">
                                    <Storyboard>
                                        <ThicknessAnimation Duration="0:0:0:0.167" Storyboard.TargetProperty="BorderThickness" Storyboard.TargetName="ThumbBorder" To="4"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <RemoveStoryboard BeginStoryboardName="MouseLeaveAnimation"/>
                            </Trigger.ExitActions>
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsDragging" Value="false"/>
                                <Condition Property="IsMouseOver" Value="True"/>
                            </MultiTrigger.Conditions>
                            <MultiTrigger.EnterActions>
                                <BeginStoryboard x:Name="MouseOverAfterDraggingAnimation">
                                    <Storyboard>
                                        <ThicknessAnimation Duration="0:0:0:0.167" Storyboard.TargetProperty="BorderThickness" Storyboard.TargetName="ThumbBorder" To="2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </MultiTrigger.EnterActions>
                            <MultiTrigger.ExitActions>
                                <RemoveStoryboard BeginStoryboardName="MouseOverAfterDraggingAnimation"/>
                            </MultiTrigger.ExitActions>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="SyncfusionSfRangeSliderHorizontalThumbStyle"
        BasedOn="{StaticResource SyncfusionSfRangeSliderThumbStyle}"
        TargetType="Thumb">
        <Setter Property="MinWidth" Value="20" />
        <Setter Property="MinHeight" Value="20" />
    </Style>

    <Style
        x:Key="SyncfusionSfRangeSliderVerticalThumbStyle"
        BasedOn="{StaticResource SyncfusionSfRangeSliderThumbStyle}"
        TargetType="Thumb">
        <Setter Property="MinWidth" Value="20" />
        <Setter Property="MinHeight" Value="20" />
    </Style>

    <!--  Style for TooltTip of Thumb  -->
    <Style x:Key="ThumbToolTipStyle" TargetType="ToolTip">
        <Setter Property="Background" Value="{StaticResource TooltipBackground}" />
        <Setter Property="Foreground" Value="{StaticResource TooltipForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.SubTitleTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="BorderBrush" Value="{StaticResource TooltipBorder}" />
    </Style>

    <Style
        x:Key="TopToolTipStyle"
        BasedOn="{StaticResource ThumbToolTipStyle}"
        TargetType="ToolTip">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ContentControl">
                    <Grid Effect="{StaticResource Default.ShadowDepth3}"
                          Margin="14,0,14,0">
                        <Border
                            x:Name="ToolTipBorder"
                            Background="{TemplateBinding Background}"
                            Height="28"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Center"
                            Padding="8,5,8,7"
                            SnapsToDevicePixels="True"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" >
                            <TextBlock
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Center"
                                FontFamily="{TemplateBinding FontFamily}"
                                FontSize="{TemplateBinding FontSize}"
                                FontWeight="{TemplateBinding FontWeight}"
                                Foreground="{TemplateBinding Foreground}"
                                Text="{TemplateBinding Content}" /> 
                        </Border>
                        <TextBlock
                            Margin="10 0 10 5"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Center"
                            FontFamily="{TemplateBinding FontFamily}"
                            FontSize="{TemplateBinding FontSize}"
                            FontWeight="{TemplateBinding FontWeight}"
                            Foreground="{TemplateBinding Foreground}"
                            Visibility="Hidden"
                            Text="{TemplateBinding Content}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style
        x:Key="BottomToolTipStyle"
        BasedOn="{StaticResource ThumbToolTipStyle}"
        TargetType="ToolTip">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ContentControl">
                    <Grid 
                        Effect="{StaticResource Default.ShadowDepth3}"
                        RenderTransformOrigin="0.5,0.5"
                        Margin="14,0,14,0">
                        <Border
                            x:Name="ToolTipBorder"
                            Background="{TemplateBinding Background}"
                            Height="28"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Center"
                            Padding="{TemplateBinding Padding}"
                            SnapsToDevicePixels="True"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" >
                            <TextBlock
                                Margin="0 5 1 7"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Center"
                                FontFamily="{TemplateBinding FontFamily}"
                                FontSize="{TemplateBinding FontSize}"
                                FontWeight="{TemplateBinding FontWeight}"
                                Foreground="{TemplateBinding Foreground}"
                                Text="{TemplateBinding Content}" />
                        </Border>
                        <TextBlock
                            Margin="10,0,10,0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Center"
                            FontFamily="{TemplateBinding FontFamily}"
                            FontSize="{TemplateBinding FontSize}"
                            FontWeight="{TemplateBinding FontWeight}"
                            Foreground="{TemplateBinding Foreground}"
                            Visibility="Hidden"
                            Text="{TemplateBinding Content}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="RightToolTipStyle"
        BasedOn="{StaticResource ThumbToolTipStyle}"
        TargetType="ToolTip">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ContentControl">
                    <Grid  Effect="{StaticResource Default.ShadowDepth3}" 
                           Margin="0,4,4,4">
                        <Border
                            x:Name="ToolTipBorder"
                            Background="{TemplateBinding Background}"
                            Height="28"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Center"
                            Padding="8,5,8,7"
                            SnapsToDevicePixels="True"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" >
                            <TextBlock
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Center"
                                FontFamily="{TemplateBinding FontFamily}"
                                FontSize="{TemplateBinding FontSize}"
                                FontWeight="{TemplateBinding FontWeight}"
                                Foreground="{TemplateBinding Foreground}"
                                Text="{TemplateBinding Content}" />
                        </Border>
                        <TextBlock
                                    Padding="16,5,8,7"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Center"
                                    Background="Transparent"
                                    Visibility="Hidden"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    FontSize="{TemplateBinding FontSize}"
                                    FontWeight="{TemplateBinding FontWeight}"
                                    Foreground="{TemplateBinding Foreground}"
                                    Text="{TemplateBinding Content}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="LeftToolTipStyle"
        BasedOn="{StaticResource ThumbToolTipStyle}"
        TargetType="ToolTip">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ContentControl">
                    <Grid Effect="{StaticResource Default.ShadowDepth3}"
                          Margin="4,4,0,4">
                        <Border
                            x:Name="ToolTipBorder"
                            Background="{TemplateBinding Background}"
                            Height="28"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Center"
                            Padding="8,5,8,7"
                            SnapsToDevicePixels="True"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}" >
                            <TextBlock
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Center"
                                FontFamily="{TemplateBinding FontFamily}"
                                FontSize="{TemplateBinding FontSize}"
                                FontWeight="{TemplateBinding FontWeight}"
                                Foreground="{TemplateBinding Foreground}"
                                Text="{TemplateBinding Content}" />
                        </Border>
                        <TextBlock
                                    Padding="8,5,16,7"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Center"
                                    Background="Transparent"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    FontSize="{TemplateBinding FontSize}"
                                    FontWeight="{TemplateBinding FontWeight}"
                                    Foreground="{TemplateBinding Foreground}"
                                    Visibility="Hidden"
                                    Text="{TemplateBinding Content}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <!--  Style for inactive track of SfRangeSlider  -->
    <Style x:Key="SyncfusionSfRangeSliderInactiveTrackStyle" TargetType="Rectangle">
        <Setter Property="Height" Value="4" />
        <Setter Property="Fill" Value="{StaticResource BorderAlt1}" />
    </Style>
    <Style x:Key="SyncfusionSfRangeSliderVerticalInactiveTrackStyle" TargetType="Rectangle">
        <Setter Property="Width" Value="4" />
        <Setter Property="Fill" Value="{StaticResource BorderAlt1}" />
    </Style>
    <!--  Style for active track of SfRangeSlider  -->
    <Style x:Key="SyncfusionSfRangeSliderActiveTrackStyle" TargetType="Rectangle">
        <Setter Property="Height" Value="4" />
        <Setter Property="Fill" Value="{StaticResource PrimaryBackground}" />
    </Style>

    <Style x:Key="SyncfusionSfRangeSliderVerticalActiveTrackStyle" TargetType="Rectangle">
        <Setter Property="Width" Value="4" />
        <Setter Property="Fill" Value="{StaticResource PrimaryBackground}" />
    </Style>

    <!--  Style for TickBar of SfRangeSlider  -->
    <Style x:Key="SyncfusionSfRangeSliderTickBarStyle" TargetType="Input:TickBar">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Input:TickBar">
                    <Grid x:Name="PART_Layout" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfRangeSliderTickBarStyle}" TargetType="Input:TickBar" />

    <!--  Style for TickBarItem of SfRangeSlider  -->
    <Style x:Key="SyncfusionSfRangeSliderTickBarItemStyle" TargetType="Input:TickBarItem">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Input:TickBarItem">
                    <Grid x:Name="PART_TickBarItem">
                        <Grid x:Name="TopLeftPanel" Visibility="Collapsed">
                            <Grid.RowDefinitions>
                                <RowDefinition />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Line
                                x:Name="PART_TopLeftLine"
                                Opacity="{TemplateBinding LineOpacity}"
                                X1="{TemplateBinding StartPointX}"
                                X2="{TemplateBinding EndPointX}"
                                Y1="{TemplateBinding StartPointY}"
                                Y2="{TemplateBinding EndPointY}" />
                            <TextBlock
                                x:Name="PART_TopLeftText"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Top"
                                Background="{TemplateBinding Background}"
                                FontFamily="{TemplateBinding FontFamily}"
                                FontSize="{TemplateBinding FontSize}"
                                FontWeight="{TemplateBinding FontWeight}"
                                Foreground="{TemplateBinding Foreground}"
                                Text="{TemplateBinding Tick}"
                                TextTrimming="WordEllipsis"
                                Visibility="Collapsed">
                                <TextBlock.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </TextBlock.Resources>
                            </TextBlock>
                        </Grid>
                        <Grid x:Name="BottomRightPanel" Visibility="Collapsed">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <Line
                                x:Name="PART_BottomRightLine"
                                Opacity="{TemplateBinding LineOpacity}"
                                X1="{TemplateBinding StartPointX}"
                                X2="{TemplateBinding EndPointX}"
                                Y1="{TemplateBinding StartPointY}"
                                Y2="{TemplateBinding EndPointY}" />
                            <TextBlock
                                x:Name="PART_BottomRightText"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Top"
                                Background="{TemplateBinding Background}"
                                FontFamily="{TemplateBinding FontFamily}"
                                FontSize="{TemplateBinding FontSize}"
                                FontWeight="{TemplateBinding FontWeight}"
                                Foreground="{TemplateBinding Foreground}"
                                Text="{TemplateBinding Tick}"
                                TextTrimming="WordEllipsis"
                                Visibility="Collapsed">
                                <TextBlock.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </TextBlock.Resources>
                            </TextBlock>
                        </Grid>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="PART_TopLeftText" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="PART_TopLeftText" Property="Background" Value="{StaticResource ContentBackground}" />
                            <Setter TargetName="PART_BottomRightText" Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter TargetName="PART_BottomRightText" Property="Background" Value="{StaticResource ContentBackground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfRangeSliderTickBarItemStyle}" TargetType="Input:TickBarItem" />    <!--  Template for HorizontalRangeSlider  -->
    <ControlTemplate x:Key="SyncfusionSfRangeSliderHorizontalControlTemplate" TargetType="Input:SfRangeSlider">
        <Border
            x:Name="border"
            Background="Transparent"
            BorderBrush="Transparent"
            SnapsToDevicePixels="True">
            <Grid
                x:Name="HorizontalTemplate"
                Background="Transparent">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <Rectangle
                    x:Name="HorizontalTrackRectangle"
                    Grid.Row="1"
                    Grid.ColumnSpan="3"
                    Height="4"
                    Width="{TemplateBinding ActualWidth}"
                    RadiusX="2"
                    RadiusY="4"
                    Style="{TemplateBinding InactiveTrackStyle}" />
                <Rectangle
                    x:Name="HorizontalDecreaseRectangle"
                    Grid.Row="1"
                    Height="4"
                    Grid.ColumnSpan="3"
                    Width="{TemplateBinding ActualWidth}"
                    RadiusX="2"
                    RadiusY="4"
                    Style="{TemplateBinding ActiveTrackStyle}" />
                <Input:TickBar
                    x:Name="TopTickBar"
                    Grid.ColumnSpan="3"
                    Margin="0,0,-1,2"
                    IsTabStop="False"
                    VerticalAlignment="Bottom"
                    CustomLabels="{TemplateBinding CustomLabels}"
                    IsDirectionReversed="{TemplateBinding IsDirectionReversed}"
                    IsHitTestVisible="False"
                    LabelOrientation="{TemplateBinding LabelOrientation}"
                    LabelPlacement="{TemplateBinding LabelPlacement}"
                    Maximum="{TemplateBinding Maximum}"
                    Minimum="{TemplateBinding Minimum}"
                    Orientation="{TemplateBinding Orientation}"
                    ShowCustomLabels="{TemplateBinding ShowCustomLabels}"
                    ShowValueLabels="{TemplateBinding ShowValueLabels}"
                    TickFrequency="{TemplateBinding TickFrequency}"
                    TickPlacement="{TemplateBinding TickPlacement}"
                    ValuePlacement="{TemplateBinding ValuePlacement}"
                    Visibility="Collapsed" />
                <Input:TickBar
                    x:Name="HorizontalInlineTickBar"
                    Grid.Row="1"
                    Grid.ColumnSpan="3"
                    IsTabStop="False"
                    CustomLabels="{TemplateBinding CustomLabels}"
                    IsDirectionReversed="{TemplateBinding IsDirectionReversed}"
                    IsHitTestVisible="False"
                    LabelOrientation="{TemplateBinding LabelOrientation}"
                    LabelPlacement="{TemplateBinding LabelPlacement}"
                    Maximum="{TemplateBinding Maximum}"
                    Minimum="{TemplateBinding Minimum}"
                    Orientation="{TemplateBinding Orientation}"
                    ShowCustomLabels="{TemplateBinding ShowCustomLabels}"
                    ShowValueLabels="{TemplateBinding ShowValueLabels}"
                    TickFrequency="{TemplateBinding TickFrequency}"
                    TickPlacement="{TemplateBinding TickPlacement}"
                    ValuePlacement="{TemplateBinding ValuePlacement}"
                    Visibility="Collapsed" />
                <Input:TickBar
                    x:Name="BottomTickBar"
                    Grid.Row="2"
                    Grid.ColumnSpan="3"
                    Margin="0,2,-1,0"
                    IsTabStop="False"
                    VerticalAlignment="Top"
                    CustomLabels="{TemplateBinding CustomLabels}"
                    IsDirectionReversed="{TemplateBinding IsDirectionReversed}"
                    IsHitTestVisible="False"
                    LabelOrientation="{TemplateBinding LabelOrientation}"
                    LabelPlacement="{TemplateBinding LabelPlacement}"
                    Maximum="{TemplateBinding Maximum}"
                    Minimum="{TemplateBinding Minimum}"
                    Orientation="{TemplateBinding Orientation}"
                    ShowCustomLabels="{TemplateBinding ShowCustomLabels}"
                    ShowValueLabels="{TemplateBinding ShowValueLabels}"
                    TickFrequency="{TemplateBinding TickFrequency}"
                    TickPlacement="{TemplateBinding TickPlacement}"
                    ValuePlacement="{TemplateBinding ValuePlacement}"
                    Visibility="Collapsed" />
                <Rectangle
                    x:Name="HorizontalBorder"
                    Grid.Row="1"
                    Grid.ColumnSpan="3"
                    Stroke="{TemplateBinding BorderBrush}"
                    StrokeThickness="{TemplateBinding BorderThickness}" />
                <Thumb
                    x:Name="HorizontalRangeStartThumb"
                    Grid.Row="1"
                    Grid.Column="1"
                    DataContext="{TemplateBinding Value}"
                    Style="{TemplateBinding ThumbStyle}"
                    Visibility="Collapsed" />
                <Thumb
                    x:Name="HorizontalRangeEndThumb"
                    Grid.Row="1"
                    Grid.Column="1"
                    DataContext="{TemplateBinding Value}"
                    Style="{TemplateBinding ThumbStyle}" />
                <Rectangle
                    x:Name="FocusVisualWhiteHorizontal"
                    Grid.RowSpan="3"
                    Grid.ColumnSpan="3"
                    IsHitTestVisible="False"
                    Opacity="0"
                    StrokeDashArray="1,1"
                    StrokeDashOffset="1.5"
                    StrokeEndLineCap="Square" />
                <Rectangle
                    x:Name="FocusVisualBlackHorizontal"
                    Grid.RowSpan="3"
                    Grid.ColumnSpan="3"
                    IsHitTestVisible="False"
                    Opacity="0"
                    StrokeDashArray="1,1"
                    StrokeDashOffset="0.5"
                    StrokeEndLineCap="Square" />
                <VisualStateManager.VisualStateGroups>
                    <VisualStateGroup x:Name="CommonStates">
                        <VisualState x:Name="Normal" />
                        <VisualState x:Name="Pressed">
                            <Storyboard>
                                <ColorAnimation
                                    Storyboard.TargetName="HorizontalDecreaseRectangle"
                                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                    To="{StaticResource PrimaryBackgroundOpacity2.Color}"
                                    Duration="0" />
                                <ColorAnimation
                                    Storyboard.TargetName="HorizontalTrackRectangle"
                                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                    To="{StaticResource BorderAlt1.Color}"
                                    Duration="0" />
                            </Storyboard>
                        </VisualState>
                        <VisualState x:Name="Disabled">
                            <Storyboard>
                                <ColorAnimation
                                    Storyboard.TargetName="HorizontalDecreaseRectangle"
                                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                    To="{StaticResource BorderAlt4.Color}"
                                    Duration="0" />
                                <ColorAnimation
                                    Storyboard.TargetName="HorizontalTrackRectangle"
                                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                    To="{StaticResource BorderAlt.Color}"
                                    Duration="0" />
                                <ColorAnimation
                                    Storyboard.TargetName="bottomTickBarColor"
                                    Storyboard.TargetProperty="Color"
                                    To="{StaticResource BorderAlt.Color}"
                                    Duration="0" />
                                <ColorAnimation
                                    Storyboard.TargetName="horizontalInlineTickBarColor"
                                    Storyboard.TargetProperty="Color"
                                    To="{StaticResource BorderAlt.Color}"
                                    Duration="0" />
                                <ColorAnimation
                                    Storyboard.TargetName="topTickBarColor"
                                    Storyboard.TargetProperty="Color"
                                    To="{StaticResource BorderAlt.Color}"
                                    Duration="0" />
                            </Storyboard>
                        </VisualState>
                        <VisualState x:Name="PointerOver">
                            <Storyboard>
                                <ColorAnimation
                                    Storyboard.TargetName="HorizontalDecreaseRectangle"
                                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                    To="{StaticResource PrimaryBackgroundOpacity1.Color}"
                                    Duration="0" />
                                <ColorAnimation
                                    Storyboard.TargetName="HorizontalTrackRectangle"
                                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                    To="{StaticResource PrimaryColorLight1.Color}"
                                    Duration="0" />
                            </Storyboard>
                        </VisualState>
                    </VisualStateGroup>
                    <VisualStateGroup x:Name="FocusStates">
                        <VisualState x:Name="Focused" />
                        <VisualState x:Name="Unfocused" />
                    </VisualStateGroup>
                </VisualStateManager.VisualStateGroups>
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter TargetName="HorizontalTrackRectangle" Property="Height" Value="4" />
                <Setter TargetName="HorizontalDecreaseRectangle" Property="Height" Value="4" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Fill" TargetName="HorizontalDecreaseRectangle" Value="{StaticResource BorderAlt}"/>

                <Setter Property="Fill" TargetName="HorizontalTrackRectangle" Value="{StaticResource BorderAlt}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  Template for VerticalRangeSlider  -->
    <ControlTemplate x:Key="SyncfusionSfRangeSliderVerticalControlTemplate" TargetType="Input:SfRangeSlider">
        <Border
            x:Name="border"
            Background="Transparent"
            BorderBrush="Transparent"
            SnapsToDevicePixels="True">
            <Grid
                x:Name="VerticalTemplate"
                Margin="{TemplateBinding Padding}"
                Background="Transparent">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Rectangle
                    x:Name="VerticalTrackRectangle"
                    Grid.RowSpan="3"
                    Grid.Column="1"
                    Height="{TemplateBinding ActualHeight}"
                    RadiusX="4"
                    RadiusY="2"
                    Style="{TemplateBinding InactiveTrackStyle}" />
                <Rectangle
                    x:Name="VerticalDecreaseRectangle"
                    Grid.RowSpan="3"
                    Grid.Column="1"
                    Height="{TemplateBinding ActualHeight}"
                    RadiusX="4"
                    RadiusY="2"
                    Style="{TemplateBinding ActiveTrackStyle}" />
                <Input:TickBar
                    x:Name="LeftTickBar"
                    Grid.RowSpan="3"
                    Margin="0,0,2,0"
                    IsTabStop="False"
                    HorizontalAlignment="Right"
                    CustomLabels="{TemplateBinding CustomLabels}"
                    IsDirectionReversed="{TemplateBinding IsDirectionReversed}"
                    IsHitTestVisible="False"
                    LabelOrientation="{TemplateBinding LabelOrientation}"
                    LabelPlacement="{TemplateBinding LabelPlacement}"
                    Maximum="{TemplateBinding Maximum}"
                    Minimum="{TemplateBinding Minimum}"
                    Orientation="{TemplateBinding Orientation}"
                    ShowCustomLabels="{TemplateBinding ShowCustomLabels}"
                    ShowValueLabels="{TemplateBinding ShowValueLabels}"
                    TickFrequency="{TemplateBinding TickFrequency}"
                    TickPlacement="{TemplateBinding TickPlacement}"
                    ValuePlacement="{TemplateBinding ValuePlacement}"
                    Visibility="Collapsed" />
                <Input:TickBar
                    x:Name="VerticalInlineTickBar"
                    Grid.RowSpan="3"
                    Grid.Column="1"
                    IsTabStop="False"
                    CustomLabels="{TemplateBinding CustomLabels}"
                    IsDirectionReversed="{TemplateBinding IsDirectionReversed}"
                    IsHitTestVisible="False"
                    LabelOrientation="{TemplateBinding LabelOrientation}"
                    LabelPlacement="{TemplateBinding LabelPlacement}"
                    Maximum="{TemplateBinding Maximum}"
                    Minimum="{TemplateBinding Minimum}"
                    Orientation="{TemplateBinding Orientation}"
                    ShowCustomLabels="{TemplateBinding ShowCustomLabels}"
                    ShowValueLabels="{TemplateBinding ShowValueLabels}"
                    TickFrequency="{TemplateBinding TickFrequency}"
                    TickPlacement="{TemplateBinding TickPlacement}"
                    ValuePlacement="{TemplateBinding ValuePlacement}"
                    Visibility="Collapsed" />
                <Input:TickBar
                    x:Name="RightTickBar"
                    Grid.RowSpan="3"
                    Grid.Column="2"
                    Margin="2,0,0,0"
                    IsTabStop="False"
                    HorizontalAlignment="Left"
                    CustomLabels="{TemplateBinding CustomLabels}"
                    IsDirectionReversed="{TemplateBinding IsDirectionReversed}"
                    IsHitTestVisible="False"
                    LabelOrientation="{TemplateBinding LabelOrientation}"
                    LabelPlacement="{TemplateBinding LabelPlacement}"
                    Maximum="{TemplateBinding Maximum}"
                    Minimum="{TemplateBinding Minimum}"
                    Orientation="{TemplateBinding Orientation}"
                    ShowCustomLabels="{TemplateBinding ShowCustomLabels}"
                    ShowValueLabels="{TemplateBinding ShowValueLabels}"
                    TickFrequency="{TemplateBinding TickFrequency}"
                    TickPlacement="{TemplateBinding TickPlacement}"
                    ValuePlacement="{TemplateBinding ValuePlacement}"
                    Visibility="Collapsed" />
                <Rectangle
                    x:Name="VerticalBorder"
                    Grid.RowSpan="3"
                    Grid.Column="1"
                    Stroke="{TemplateBinding BorderBrush}"
                    StrokeThickness="{TemplateBinding BorderThickness}" />
                <Thumb
                    x:Name="VerticalRangeStartThumb"
                    Grid.Row="1"
                    Grid.Column="1"
                    DataContext="{TemplateBinding Value}"
                    Style="{TemplateBinding ThumbStyle}"
                    Visibility="Collapsed" />
                <Thumb
                    x:Name="VerticalRangeEndThumb"
                    Grid.Row="1"
                    Grid.Column="1"
                    DataContext="{TemplateBinding Value}"
                    Style="{TemplateBinding ThumbStyle}" />
                <Rectangle
                    x:Name="FocusVisualWhiteVertical"
                    Grid.RowSpan="3"
                    Grid.ColumnSpan="3"
                    IsHitTestVisible="False"
                    Opacity="0"
                    Stroke="Black"
                    StrokeDashArray="1,1"
                    StrokeDashOffset="1.5"
                    StrokeEndLineCap="Square" />
                <Rectangle
                    x:Name="FocusVisualBlackVertical"
                    Grid.RowSpan="3"
                    Grid.ColumnSpan="3"
                    IsHitTestVisible="False"
                    Opacity="0"
                    Stroke="Black"
                    StrokeDashArray="1,1"
                    StrokeDashOffset="0.5"
                    StrokeEndLineCap="Square" />
                <VisualStateManager.VisualStateGroups>
                    <VisualStateGroup x:Name="CommonStates">
                        <VisualState x:Name="Normal" />
                        <VisualState x:Name="Pressed">
                            <Storyboard>
                                <ColorAnimation
                                    Storyboard.TargetName="VerticalDecreaseRectangle"
                                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                    To="{StaticResource PrimaryBackgroundOpacity2.Color}"
                                    Duration="0" />
                                <ColorAnimation
                                    Storyboard.TargetName="VerticalTrackRectangle"
                                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                    To="{StaticResource BorderAlt1.Color}"
                                    Duration="0" />
                            </Storyboard>
                        </VisualState>
                        <VisualState x:Name="Disabled">
                            <Storyboard>
                                <ColorAnimation
                                    Storyboard.TargetName="VerticalDecreaseRectangle"
                                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                    To="{StaticResource BorderAlt4.Color}"
                                    Duration="0" />
                                <ColorAnimation
                                    Storyboard.TargetName="VerticalTrackRectangle"
                                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                    To="{StaticResource BorderAlt.Color}"
                                    Duration="0" />
                                <ColorAnimation
                                    Storyboard.TargetName="leftTickBarColor"
                                    Storyboard.TargetProperty="Color"
                                    To="{StaticResource BorderAlt.Color}"
                                    Duration="0" />
                                <ColorAnimation
                                    Storyboard.TargetName="verticalInlineTickBarColor"
                                    Storyboard.TargetProperty="Color"
                                    To="{StaticResource BorderAlt.Color}"
                                    Duration="0" />
                                <ColorAnimation
                                    Storyboard.TargetName="rightTickBarColor"
                                    Storyboard.TargetProperty="Color"
                                    To="{StaticResource BorderAlt.Color}"
                                    Duration="0" />
                            </Storyboard>
                        </VisualState>
                        <VisualState x:Name="PointerOver">
                            <Storyboard>
                                <ColorAnimation
                                    Storyboard.TargetName="VerticalDecreaseRectangle"
                                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                    To="{StaticResource PrimaryBackgroundOpacity1.Color}"
                                    Duration="0" />
                                <ColorAnimation
                                    Storyboard.TargetName="VerticalTrackRectangle"
                                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                    To="{StaticResource PrimaryColorLight1.Color}"
                                    Duration="0" />
                            </Storyboard>
                        </VisualState>
                    </VisualStateGroup>
                    <VisualStateGroup x:Name="FocusStates">
                        <VisualState x:Name="Focused" />
                        <VisualState x:Name="Unfocused" />
                    </VisualStateGroup>
                </VisualStateManager.VisualStateGroups>
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter TargetName="VerticalTrackRectangle" Property="Height" Value="4" />
                <Setter TargetName="VerticalDecreaseRectangle" Property="Height" Value="4" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Fill" TargetName="VerticalDecreaseRectangle" Value="{StaticResource BorderAlt}"/>
                <Setter Property="Fill" TargetName="VerticalTrackRectangle" Value="{StaticResource BorderAlt}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  Style for SfRangeSlider  -->
    <Style x:Key="SyncfusionSfRangeSliderStyle" TargetType="Input:SfRangeSlider">
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="TickStroke" Value="{StaticResource BorderAlt1}" />
        <Setter Property="MinorTickStroke" Value="{StaticResource BorderAlt1}" />
        <Setter Property="ActiveMinorTickStroke" Value="{StaticResource BorderAlt1}" />
        <Setter Property="ActiveTickStroke" Value="{StaticResource BorderAlt1}" />
        <Setter Property="TickStrokeThickness" Value="{StaticResource Windows11Dark.StrokeThickness1}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness2}" />
        <Setter Property="TickLength" Value="5" />
        <Setter Property="MinorTickLength" Value="2.5" />
        <Setter Property="Maximum" Value="100" />
        <Setter Property="SmallChange" Value="1" />
        <Style.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="TickLength" Value="10" />
                <Setter Property="MinorTickLength" Value="5" />
            </Trigger>
            <Trigger Property="Orientation" Value="Horizontal">
                <Setter Property="InactiveTrackStyle" Value="{StaticResource SyncfusionSfRangeSliderInactiveTrackStyle}" />
                <Setter Property="ActiveTrackStyle" Value="{StaticResource SyncfusionSfRangeSliderActiveTrackStyle}" />
                <Setter Property="ThumbStyle" Value="{StaticResource SyncfusionSfRangeSliderHorizontalThumbStyle}" />
                <Setter Property="Template" Value="{StaticResource SyncfusionSfRangeSliderHorizontalControlTemplate}" />
            </Trigger>
            <Trigger Property="Orientation" Value="Vertical">
                <Setter Property="InactiveTrackStyle" Value="{StaticResource SyncfusionSfRangeSliderVerticalInactiveTrackStyle}" />
                <Setter Property="ActiveTrackStyle" Value="{StaticResource SyncfusionSfRangeSliderVerticalActiveTrackStyle}" />
                <Setter Property="ThumbStyle" Value="{StaticResource SyncfusionSfRangeSliderVerticalThumbStyle}" />
                <Setter Property="Template" Value="{StaticResource SyncfusionSfRangeSliderVerticalControlTemplate}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Orientation" Value="Horizontal" />
                    <Condition Property="ThumbToolTipPlacement" Value="TopLeft" />
                </MultiTrigger.Conditions>
                <Setter Property="ToolTipStyle" Value="{StaticResource TopToolTipStyle}" />
            </MultiTrigger>

            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Orientation" Value="Horizontal" />
                    <Condition Property="ThumbToolTipPlacement" Value="BottomRight" />
                </MultiTrigger.Conditions>
                <Setter Property="ToolTipStyle" Value="{StaticResource BottomToolTipStyle}" />
            </MultiTrigger>

            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Orientation" Value="Vertical" />
                    <Condition Property="ThumbToolTipPlacement" Value="TopLeft" />
                </MultiTrigger.Conditions>
                <Setter Property="ToolTipStyle" Value="{StaticResource LeftToolTipStyle}" />
            </MultiTrigger>

            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Orientation" Value="Vertical" />
                    <Condition Property="ThumbToolTipPlacement" Value="BottomRight" />
                </MultiTrigger.Conditions>
                <Setter Property="ToolTipStyle" Value="{StaticResource RightToolTipStyle}" />
            </MultiTrigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="TickStroke" Value="{StaticResource BorderAlt}"/>
                <Setter Property="MinorTickStroke" Value="{StaticResource BorderAlt}"/>
                <Setter Property="ActiveMinorTickStroke" Value="{StaticResource BorderAlt}"/>
                <Setter Property="ActiveTickStroke" Value="{StaticResource BorderAlt}"/>
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfRangeSliderStyle}" TargetType="Input:SfRangeSlider" />

</ResourceDictionary>
