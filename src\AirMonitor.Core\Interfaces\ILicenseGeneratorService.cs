using AirMonitor.Core.Models;

namespace AirMonitor.Core.Interfaces;

/// <summary>
/// 许可证生成服务接口
/// 专门用于License注册机的许可证生成功能
/// </summary>
public interface ILicenseGeneratorService
{
    /// <summary>
    /// 生成许可证
    /// </summary>
    /// <param name="licenseInfo">许可证信息</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <returns>生成结果</returns>
    Task<LicenseGenerationResult> GenerateLicenseAsync(LicenseInfo licenseInfo, string privateKey);

    /// <summary>
    /// 生成许可证文件
    /// </summary>
    /// <param name="licenseInfo">许可证信息</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <returns>生成结果</returns>
    Task<LicenseGenerationResult> GenerateLicenseFileAsync(LicenseInfo licenseInfo, string filePath, string privateKey);

    /// <summary>
    /// 批量生成许可证
    /// </summary>
    /// <param name="licenseInfos">许可证信息列表</param>
    /// <param name="outputDirectory">输出目录</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <param name="progressCallback">进度回调</param>
    /// <returns>批量生成结果</returns>
    Task<BatchGenerationResult> BatchGenerateLicensesAsync(
        IEnumerable<LicenseInfo> licenseInfos,
        string outputDirectory,
        string privateKey,
        IProgress<BatchGenerationProgress>? progressCallback = null);

    /// <summary>
    /// 从模板生成许可证
    /// </summary>
    /// <param name="template">许可证模板</param>
    /// <param name="customerName">客户名称</param>
    /// <param name="customerEmail">客户邮箱</param>
    /// <param name="hardwareFingerprint">硬件指纹</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <returns>生成结果</returns>
    Task<LicenseGenerationResult> GenerateFromTemplateAsync(
        LicenseTemplate template,
        string customerName,
        string customerEmail,
        string hardwareFingerprint,
        string privateKey);

    /// <summary>
    /// 验证许可证文件
    /// </summary>
    /// <param name="filePath">许可证文件路径</param>
    /// <param name="publicKey">RSA公钥</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateLicenseFileAsync(string filePath, string publicKey);

    /// <summary>
    /// 从Excel文件导入批量生成数据
    /// </summary>
    /// <param name="excelFilePath">Excel文件路径</param>
    /// <returns>许可证信息列表</returns>
    Task<List<LicenseInfo>> ImportFromExcelAsync(string excelFilePath);

    /// <summary>
    /// 导出批量生成模板到Excel
    /// </summary>
    /// <param name="excelFilePath">Excel文件路径</param>
    /// <returns>是否导出成功</returns>
    Task<bool> ExportTemplateToExcelAsync(string excelFilePath);

    /// <summary>
    /// 获取许可证信息摘要
    /// </summary>
    /// <param name="filePath">许可证文件路径</param>
    /// <param name="publicKey">RSA公钥</param>
    /// <returns>许可证摘要信息</returns>
    Task<LicenseSummary?> GetLicenseSummaryAsync(string filePath, string publicKey);
}

/// <summary>
/// 许可证生成结果
/// </summary>
public class LicenseGenerationResult
{
    /// <summary>
    /// 是否生成成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 生成的许可证内容
    /// </summary>
    public string? LicenseContent { get; set; }

    /// <summary>
    /// 许可证文件路径
    /// </summary>
    public string? FilePath { get; set; }

    /// <summary>
    /// 许可证信息
    /// </summary>
    public LicenseInfo? LicenseInfo { get; set; }

    /// <summary>
    /// 生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static LicenseGenerationResult Success(string? licenseContent = null, string? filePath = null, LicenseInfo? licenseInfo = null)
    {
        return new LicenseGenerationResult
        {
            IsSuccess = true,
            LicenseContent = licenseContent,
            FilePath = filePath,
            LicenseInfo = licenseInfo
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static LicenseGenerationResult Failure(string errorMessage)
    {
        return new LicenseGenerationResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 批量生成结果
/// </summary>
public class BatchGenerationResult
{
    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 成功的许可证列表
    /// </summary>
    public List<LicenseGenerationResult> SuccessResults { get; set; } = new();

    /// <summary>
    /// 失败的许可证列表
    /// </summary>
    public List<LicenseGenerationResult> FailureResults { get; set; } = new();

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 耗时
    /// </summary>
    public TimeSpan Duration => EndTime - StartTime;

    /// <summary>
    /// 是否全部成功
    /// </summary>
    public bool IsAllSuccess => FailureCount == 0;
}

/// <summary>
/// 批量生成进度
/// </summary>
public class BatchGenerationProgress
{
    /// <summary>
    /// 当前处理的索引
    /// </summary>
    public int CurrentIndex { get; set; }

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 当前处理的许可证ID
    /// </summary>
    public string CurrentLicenseId { get; set; } = string.Empty;

    /// <summary>
    /// 进度百分比
    /// </summary>
    public double ProgressPercentage => TotalCount > 0 ? (double)CurrentIndex / TotalCount * 100 : 0;

    /// <summary>
    /// 状态消息
    /// </summary>
    public string StatusMessage { get; set; } = string.Empty;
}

/// <summary>
/// 许可证摘要信息
/// </summary>
public class LicenseSummary
{
    /// <summary>
    /// 许可证ID
    /// </summary>
    public string LicenseId { get; set; } = string.Empty;

    /// <summary>
    /// 许可证类型
    /// </summary>
    public string LicenseType { get; set; } = string.Empty;

    /// <summary>
    /// 客户名称
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 剩余天数
    /// </summary>
    public int RemainingDays { get; set; }

    /// <summary>
    /// 授权功能数量
    /// </summary>
    public int FeatureCount { get; set; }

    /// <summary>
    /// 签发日期
    /// </summary>
    public DateTime IssuedDate { get; set; }

    /// <summary>
    /// 过期日期
    /// </summary>
    public DateTime ExpiryDate { get; set; }
}
