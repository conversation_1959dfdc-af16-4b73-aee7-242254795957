<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib"
                    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:scheduler="clr-namespace:Syncfusion.UI.Xaml.Scheduler;assembly=Syncfusion.SfScheduler.WPF">
    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="SyncfusionTimelineViewControlStyle" TargetType="scheduler:TimelineViewControl">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness">
            <Setter.Value>
                <Thickness>0,0,0,0.4</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:TimelineViewControl">
                    <Border  BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <ScrollViewer
                            x:Name="PART_HeaderScrollViewer" 
                            HorizontalScrollBarVisibility="Hidden"
                            IsDeferredScrollingEnabled="False"
                           VerticalScrollBarVisibility="Auto">
                                <Grid x:Name="PART_ViewHeaderGrid">
                                    <Grid.RowDefinitions>
                                        <RowDefinition />
                                        <RowDefinition />
                                    </Grid.RowDefinitions>
                                    <scheduler:ViewHeaderRowPanel x:Name="PART_ViewHeaderRowPanel" />
                                    <scheduler:TimelineViewTimeRulerPanel x:Name="PART_TimelineViewTimeRulerPanel" Grid.Row="1"/>
                                </Grid>

                                <ScrollViewer.Resources>
                                    <Style TargetType="{x:Type ScrollBar}">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="{x:Type ScrollBar}">
                                                    <Border Background="Transparent" />
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </ScrollViewer.Resources>
                            </ScrollViewer>
                            
                            <ScrollViewer 
                            x:Name="PART_TimelineScrollViewer" Grid.Row="1"
                            HorizontalScrollBarVisibility="Auto"
                            IsDeferredScrollingEnabled="False" VerticalScrollBarVisibility="Auto">
                                <scheduler:TimelineViewPanel x:Name="PART_TimelineViewPanel" />
                            </ScrollViewer>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionTimelineViewControlStyle}" TargetType="scheduler:TimelineViewControl" />

    <Style x:Key="SyncfusionDayViewControlStyle" TargetType="scheduler:DayViewControl">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" >
            <Setter.Value>
                <Thickness>0,0,0,0.4</Thickness>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:DayViewControl">
                    <Border    BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid
                        Background="{TemplateBinding Background}"  >
                            <Grid.RowDefinitions>
                                <RowDefinition Height="0" MinHeight="{Binding Path=AllDayPanelHeight, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <scheduler:AllDayAppointmentViewControl x:Name="PART_AllDayControl" Grid.Row="0" Grid.ColumnSpan="2"/>

                            <ScrollViewer Grid.Row="1"
                            x:Name="PART_TimeRulerScrollViewer"
                            HorizontalScrollBarVisibility="Disabled"
                            IsDeferredScrollingEnabled="False"
                            VerticalScrollBarVisibility="Hidden">

                                <ScrollViewer.Resources>
                                    <Style TargetType="{x:Type ScrollBar}">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="{x:Type ScrollBar}">
                                                    <Border Background="Transparent" />
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </ScrollViewer.Resources>

                                <scheduler:DayViewTimeRulerPanel x:Name="PART_DayViewTimeRulerPanel" />
                            </ScrollViewer>

                            <ScrollViewer
                            x:Name="PART_TimeSlotScrollViewer"
                            Grid.Row="1"
                            Grid.Column="1"
                            HorizontalScrollBarVisibility="Auto"
                            IsDeferredScrollingEnabled="False"
                            VerticalScrollBarVisibility="Auto">

                                <scheduler:DayViewPanel x:Name="PART_DayViewPanel" Background="Transparent" />
                            </ScrollViewer>

                        </Grid>
                    </Border>

                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionDayViewControlStyle}" TargetType="scheduler:DayViewControl"/>

    <Style x:Key="SyncfusionTimeSlotCellStyle" TargetType="scheduler:TimeSlotCell">
        <Style.Triggers>
            <Trigger Property="IsHitTestVisible" Value="False">
                <Setter Property="Background" Value="{StaticResource ContentBackground}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="{StaticResource ContentBackground}" />
            </Trigger>
        </Style.Triggers>
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="UseLayoutRounding" Value="False" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:TimeSlotCell">
                    <Border 
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="SelectionStates">
                                <VisualState x:Name="Unselected" />
                                <VisualState x:Name="Selected">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames BeginTime="00:00:00"
                                                                       Storyboard.TargetName="PART_SelectionBorder"
                                                                       Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="00:00:00" Value="{x:Static Visibility.Visible}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames BeginTime="0" Storyboard.TargetName="PART_Grid"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource ContentBackgroundHovered}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid x:Name="PART_Grid"
                        Background="{TemplateBinding Background}">
                            <Border x:Name="PART_SelectionBorder"                                     
                                    Background="{StaticResource ContentBackgroundSelected}"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    BorderThickness="0"
                                    CornerRadius="0" Visibility="Collapsed" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger  Property="skinmanager:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                            <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>

                </ControlTemplate>
            </Setter.Value>
        </Setter>

    </Style>
    <Style BasedOn="{StaticResource SyncfusionTimeSlotCellStyle}" TargetType="scheduler:TimeSlotCell" />

    <Style x:Key="SyncfusionTimeRulerCellStyle" TargetType="scheduler:TimeRulerCell">
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.CaptionText}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="UseLayoutRounding" Value="False" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:TimeRulerCell">
                    <Border 
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid
                        Background="{TemplateBinding Background}">
                            <ContentPresenter
                            TextBlock.FontFamily="{TemplateBinding FontFamily}"
                            TextBlock.FontSize="{TemplateBinding FontSize}"
                            TextBlock.FontWeight="{TemplateBinding FontWeight}"
                            TextBlock.Foreground="{TemplateBinding Foreground}"
                            ContentTemplate="{TemplateBinding ContentTemplate}"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionTimeRulerCellStyle}" TargetType="scheduler:TimeRulerCell" />

    <DataTemplate x:Key="TimeRegionControlTemplate">
        <Border VerticalAlignment="Stretch" x:Name="PART_SpecialTimeRegionBorder"
                HorizontalAlignment="Stretch" 
                Background="{Binding Background}"
                Opacity="0.5">
            <TextBlock  x:Name="PART_TimeRegionTextBlock"
                        Text="{Binding Text}"
                        Foreground="{Binding Foreground}"
                        TextAlignment="Center"
                        TextWrapping="Wrap"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Stretch"/>
        </Border>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=Background}" Value="{x:Null}">
                <Setter TargetName="PART_SpecialTimeRegionBorder" Property="Background" Value="{StaticResource ContentBackgroundAlt2}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=Foreground}" Value="{x:Null}">
                <Setter TargetName="PART_TimeRegionTextBlock" Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=Text}" Value="{x:Null}">
                <Setter TargetName="PART_TimeRegionTextBlock" Property="Text" Value="{x:Static system:String.Empty}" />
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <Style x:Key="SyncfusionSpecialTimeRegionControlStyle" TargetType="scheduler:SpecialTimeRegionControl">
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
            </Trigger>
        </Style.Triggers>
        <Setter Property="ContentTemplate" Value="{StaticResource TimeRegionControlTemplate}" />
        <Setter Property="AllowDrop" Value="True"/>
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt2}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForegroundAlt1}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:SpecialTimeRegionControl">
                    <ContentPresenter
                            Content="{TemplateBinding DataContext}"
                            ContentTemplate="{TemplateBinding ContentTemplate}"  />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionSpecialTimeRegionControlStyle}" TargetType="scheduler:SpecialTimeRegionControl" />

    <Style x:Key="SyncfusionTimeIndicatorControlStyle" TargetType="scheduler:TimeIndicatorControl">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:TimeIndicatorControl">
                    <Grid x:Name="PART_CurrentTimeIndicator" Width="{Binding ActualWidth,RelativeSource={RelativeSource TemplatedParent}}">
                        <Rectangle Height="2"  Width="{Binding ActualWidth,RelativeSource={RelativeSource TemplatedParent}}" Stroke="{StaticResource PrimaryBackground}" StrokeThickness="0.9"/>
                        <Ellipse  Fill="{StaticResource PrimaryBackground}" Height="10" Width="10" HorizontalAlignment="Left"/>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionTimeIndicatorControlStyle}" TargetType="scheduler:TimeIndicatorControl" />

    <Style x:Key="SyncfusionTimeIndicatorStyle" TargetType="scheduler:TimeIndicator">
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="UseLayoutRounding" Value="False" />
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryColorForeground}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="scheduler:TimeIndicator">
                    <ContentPresenter
                            TextBlock.FontFamily="{TemplateBinding FontFamily}"
                            TextBlock.FontWeight="{TemplateBinding FontWeight}"
                            TextBlock.Foreground="{TemplateBinding Foreground}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionTimeIndicatorStyle}" TargetType="scheduler:TimeIndicator"/>

</ResourceDictionary>
