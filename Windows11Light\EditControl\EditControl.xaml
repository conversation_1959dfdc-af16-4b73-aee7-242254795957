 <ResourceDictionary 
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" 
    xmlns:syncfusion="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
    xmlns:syncfusionShared="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF"
	
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:local="clr-namespace:Syncfusion.Windows.Edit;assembly=Syncfusion.Edit.WPF"
    xmlns:Sync_Resources="clr-namespace:Syncfusion.Windows.Edit.Localization;assembly=Syncfusion.Edit.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/CheckBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/StatusBar.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ComboBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ListBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatPrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/Ribbon/Ribbon.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <BooleanToVisibilityConverter x:Key="VisibilityConverter" />

    <local:TabSelectionToTextConverter x:Key="tabTextConverter" />

    <local:TabSelectionConverter x:Key="tabSelectionConverter"/>
    <local:OrBasedVisibilityConverter x:Key="orConverter" />

    <local:FindResultToTextConverter x:Key="FindResultConverter" />

    <local:TabVisibilityToVisibilityConverter x:Key="TabVisibilityConverter" />

    <local:ThicknessToDoubleConverter x:Key="ThicknessConverter"/>

    <Style x:Key="SyncfusionLineItemStyle" TargetType="{x:Type local:LineItem}">
        <Setter Property="Cursor" Value="IBeam" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="IsHitTestVisible" Value="False" />
    </Style>

    <Style BasedOn="{StaticResource SyncfusionLineItemStyle}" TargetType="{x:Type local:LineItem}"/>

    <Style x:Key="EditControlFocusVisualStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="0,0,0,0"
                               StrokeThickness="0"
                               Stroke="Transparent"
                               StrokeDashArray="1 2"
                               SnapsToDevicePixels="true" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="SyncfusionEditCloseButtonStyle"
        BasedOn="{StaticResource WPFGlyphButtonStyle}"
        TargetType="{x:Type Button}">
        <Setter Property="Content">
            <Setter.Value>
                <Path
                    x:Name="path"
                    Width="12"
                    Height="12"
                    Margin="3"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Stroke="{StaticResource SecondaryForeground}"
                    StrokeThickness="1">
                    <Path.Data>
                        <PathGeometry>M5.23533 5.70801L1.08982 9.85352C0.992166 9.95117 0.874979 10 0.73826 10C0.595031 10 0.474588 9.9528 0.376932 9.8584C0.282531 9.76074 0.23533 9.6403 0.23533 9.49707C0.23533 9.36035 0.284158 9.24316 0.381814 9.14551L4.52732 5L0.381814 0.854492C0.284158 0.756836 0.23533 0.638021 0.23533 0.498047C0.23533 0.429688 0.248351 0.364583 0.274393 0.302734C0.300434 0.240885 0.336242 0.188802 0.381814 0.146484C0.427387 0.100911 0.481098 0.0651042 0.542947 0.0390625C0.604796 0.0130208 0.6699 0 0.73826 0C0.874979 0 0.992166 0.0488281 1.08982 0.146484L5.23533 4.29199L9.38084 0.146484C9.47849 0.0488281 9.59731 0 9.73728 0C9.80564 0 9.86912 0.0130208 9.92771 0.0390625C9.98956 0.0651042 10.0433 0.100911 10.0888 0.146484C10.1344 0.192057 10.1702 0.245768 10.1963 0.307617C10.2223 0.366211 10.2353 0.429688 10.2353 0.498047C10.2353 0.638021 10.1865 0.756836 10.0888 0.854492L5.94334 5L10.0888 9.14551C10.1865 9.24316 10.2353 9.36035 10.2353 9.49707C10.2353 9.56543 10.2223 9.63053 10.1963 9.69238C10.1702 9.75423 10.1344 9.80794 10.0888 9.85352C10.0465 9.89909 9.99444 9.9349 9.9326 9.96094C9.87075 9.98698 9.80564 10 9.73728 10C9.59731 10 9.47849 9.95117 9.38084 9.85352L5.23533 5.70801Z</PathGeometry>
                    </Path.Data>
                </Path>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="UpArrowButtonStyle" 
           BasedOn="{StaticResource WPFGlyphButtonStyle}"
           TargetType="{x:Type Button}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border Padding="4"
                        x:Name="PART_Border"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Background="{TemplateBinding Background}"
                        Effect="{TemplateBinding Effect}"
                        CornerRadius="1">
                        <Path x:Name="Arrow"
                                      Data="M3,8L7.3,2 12,8z"
                                      HorizontalAlignment="Center"
                                      Width="15"
                                      Stroke="{StaticResource ContentForeground}"
                                      StrokeThickness="1"
                                      Fill="{StaticResource ContentForeground}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Arrow" Property="Stroke" Value="{StaticResource IconColorHovered}"/>
                            <Setter TargetName="Arrow" Property="Fill" Value="{StaticResource IconColorHovered}"/>
                            <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Arrow" Property="Stroke" Value="{StaticResource IconColorSelected}"/>
                            <Setter TargetName="Arrow" Property="Fill" Value="{StaticResource IconColorSelected}"/>
                            <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundPressed}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="Arrow" Property="Stroke" Value="{StaticResource IconColorDisabled}"/>
                            <Setter TargetName="Arrow" Property="Fill" Value="{StaticResource IconColorDisabled}"/>
                            <Setter Property="Background" TargetName="PART_Border" Value="Transparent"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="DownArrowButtonStyle"
           BasedOn="{StaticResource WPFGlyphButtonStyle}"
           TargetType="{x:Type Button}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border Padding="4"
                        x:Name="PART_Border"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Background="{TemplateBinding Background}"
                        Effect="{TemplateBinding Effect}"
                        CornerRadius="1">

                        <Path x:Name="Arrow"
                                      Data="M3,1L7.3,7 12,1z"
                                      HorizontalAlignment="Center"
                                      Width="15"
                                      Stroke="{StaticResource ContentForeground}"
                                      StrokeThickness="1"
                                      Fill="{StaticResource ContentForeground}"/>
                    </Border>

                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Arrow" Property="Stroke" Value="{StaticResource IconColorHovered}"/>
                            <Setter TargetName="Arrow" Property="Fill" Value="{StaticResource IconColorHovered}"/>
                            <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Arrow" Property="Stroke" Value="{StaticResource IconColorSelected}"/>
                            <Setter TargetName="Arrow" Property="Fill" Value="{StaticResource IconColorSelected}"/>
                            <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundPressed}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="Arrow" Property="Stroke" Value="{StaticResource IconColorDisabled}"/>
                            <Setter TargetName="Arrow" Property="Fill" Value="{StaticResource IconColorDisabled}"/>
                            <Setter Property="Background" TargetName="PART_Border" Value="Transparent"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="TextBlockStyle" 
	       BasedOn="{StaticResource WPFTextBlockStyle}"
           TargetType="{x:Type TextBlock}">
        <Style.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="Text" Value="" />
                    <Condition Property="Text" Value="{x:Null}" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter Property="Visibility" Value="Collapsed" />
                </MultiTrigger.Setters>
            </MultiTrigger>
        </Style.Triggers>
    </Style>

    <ControlTemplate x:Key="SyncfusionContextPromptControlTemplate" TargetType="{x:Type local:ContextPromptControl}">
        <Border  Background="{StaticResource PopupBackground}" Padding="2" BorderBrush="{StaticResource BorderAlt}" CornerRadius="3" BorderThickness="1">
            <Grid >
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <StackPanel Orientation="Horizontal" Width="Auto" Grid.Column="0">
                    <Button x:Name="UpArrow" VerticalAlignment="Top" Style="{StaticResource UpArrowButtonStyle}" />
                    <TextBlock  HorizontalAlignment="Center" x:Name="NavigationText" Width="Auto" Foreground="{StaticResource ContentForeground}" Style="{StaticResource TextBlockStyle}"/>
                    <Button x:Name="DownArrow" VerticalAlignment="Top" Style="{StaticResource DownArrowButtonStyle}"/>
                </StackPanel>
                <StackPanel Orientation="Vertical"  Grid.Column="1">
                    <TextBlock x:Name="Description" Foreground="{StaticResource ContentForeground}"/>
                </StackPanel>
            </Grid>
        </Border>
    </ControlTemplate>

    <Style x:Key="SyncfusionPinnedToggleButtonStyle" TargetType="{x:Type ToggleButton}" BasedOn="{StaticResource WPFGlyphToggleButtonStyle}">
        <Setter Property="Content">
            <Setter.Value>
                        <Path Name="autohidepath" 
                              Stretch="Fill" 
                              Margin="4"
                              Fill="{StaticResource ContentForeground}" 
                              Data="M 315.04,110.56C 366.347,110.56 417.652,110.56 468.96,110.56C 468.96,120.854 468.96,131.147 468.96,141.44C 468.96,151.68 468.96,161.92 468.96,172.16C 468.96,192.694 468.96,213.228 468.96,233.76C 468.96,244.054 468.96,254.347 468.96,264.64C 468.96,274.88 468.96,285.121 468.96,295.36C 479.253,295.36 489.546,295.36 499.84,295.36C 499.84,305.653 499.84,315.946 499.84,326.241C 469.065,326.241 438.293,326.241 407.52,326.241C 407.147,346.454 407.466,367.36 407.36,387.841C 407.36,398.08 407.36,408.32 407.36,418.561C 407.36,428.854 407.36,439.147 407.36,449.44C 397.12,449.44 386.88,449.44 376.64,449.44C 376.64,439.147 376.64,428.854 376.64,418.561C 376.64,408.32 376.64,398.08 376.64,387.841C 376.64,367.307 376.64,346.773 376.64,326.241C 345.814,326.241 314.986,326.241 284.16,326.241C 284.16,315.946 284.16,305.653 284.16,295.36C 294.454,295.36 304.746,295.36 315.04,295.36C 315.04,285.121 315.04,274.88 315.04,264.64C 315.04,254.347 315.04,244.054 315.04,233.76C 315.04,213.227 315.04,192.693 315.04,172.16C 315.04,161.92 315.04,151.68 315.04,141.44C 315.04,131.147 315.04,120.854 315.04,110.56 Z M 345.76,172.16C 345.76,192.694 345.76,213.228 345.76,233.76C 345.76,244.054 345.76,254.347 345.76,264.64C 345.76,274.88 345.76,285.121 345.76,295.36C 366.293,295.36 386.827,295.36 407.36,295.36C 407.36,285.121 407.36,274.88 407.36,264.64C 407.36,254.347 407.36,244.054 407.36,233.76C 407.36,213.227 407.36,192.693 407.36,172.16C 407.36,161.92 407.36,151.68 407.36,141.44C 386.827,141.44 366.293,141.44 345.76,141.44C 345.76,151.68 345.76,161.92 345.76,172.16 Z " 
                              Height="11" 
                              Width="9"
                              VerticalAlignment="Center" 
                              HorizontalAlignment="Center"/>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionUnPinnedToggleButtonStyle" TargetType="{x:Type ToggleButton}" BasedOn="{StaticResource WPFGlyphToggleButtonStyle}">
        <Setter Property="Content">
            <Setter.Value>
                        <Path Name="autohidepath" 
                              Stretch="Fill" 
                              Margin="4" 
                              Fill="{StaticResource ContentForeground}"
                              Data="M 315.04,110.56C 366.347,110.56 417.652,110.56 468.96,110.56C 468.96,120.854 468.96,131.147 468.96,141.44C 468.96,151.68 468.96,161.92 468.96,172.16C 468.96,192.694 468.96,213.228 468.96,233.76C 468.96,244.054 468.96,254.347 468.96,264.64C 468.96,274.88 468.96,285.121 468.96,295.36C 479.253,295.36 489.546,295.36 499.84,295.36C 499.84,305.653 499.84,315.946 499.84,326.241C 469.065,326.241 438.293,326.241 407.52,326.241C 407.147,346.454 407.466,367.36 407.36,387.841C 407.36,398.08 407.36,408.32 407.36,418.561C 407.36,428.854 407.36,439.147 407.36,449.44C 397.12,449.44 386.88,449.44 376.64,449.44C 376.64,439.147 376.64,428.854 376.64,418.561C 376.64,408.32 376.64,398.08 376.64,387.841C 376.64,367.307 376.64,346.773 376.64,326.241C 345.814,326.241 314.986,326.241 284.16,326.241C 284.16,315.946 284.16,305.653 284.16,295.36C 294.454,295.36 304.746,295.36 315.04,295.36C 315.04,285.121 315.04,274.88 315.04,264.64C 315.04,254.347 315.04,244.054 315.04,233.76C 315.04,213.227 315.04,192.693 315.04,172.16C 315.04,161.92 315.04,151.68 315.04,141.44C 315.04,131.147 315.04,120.854 315.04,110.56 Z M 345.76,172.16C 345.76,192.694 345.76,213.228 345.76,233.76C 345.76,244.054 345.76,254.347 345.76,264.64C 345.76,274.88 345.76,285.121 345.76,295.36C 366.293,295.36 386.827,295.36 407.36,295.36C 407.36,285.121 407.36,274.88 407.36,264.64C 407.36,254.347 407.36,244.054 407.36,233.76C 407.36,213.227 407.36,192.693 407.36,172.16C 407.36,161.92 407.36,151.68 407.36,141.44C 386.827,141.44 366.293,141.44 345.76,141.44C 345.76,151.68 345.76,161.92 345.76,172.16 Z " 
                              Height="11"  
                              Width="9"
                              VerticalAlignment="Center" 
                              HorizontalAlignment="Center"
                              RenderTransformOrigin="0.44,0.454">
                            <Path.RenderTransform>
                                <RotateTransform Angle="90" />
                            </Path.RenderTransform>
                        </Path>
            </Setter.Value>
        </Setter>
    </Style>

    <ContextMenu x:Key="SyncfusionContextMenu">
        <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=UndoText}" 
                  Command="{x:Static local:EditCommands.Undo}" 
                  CommandTarget="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}">
            <MenuItem.Icon>
                <Path Fill="{StaticResource ContentForeground}" Data="M0 0.5V5.5C0 5.63542 0.0494795 5.7526 0.148438 5.85156C0.247396 5.95052 0.364583 6 0.5 6H5.5C5.63542 6 5.7526 5.95052 5.85156 5.85156C5.95052 5.7526 6 5.63542 6 5.5C6 5.36458 5.95052 5.2474 5.85156 5.14844C5.7526 5.04948 5.63542 5 5.5 5H1.75L5.4375 1.92188C5.8125 1.60938 6.22656 1.3776 6.67969 1.22656C7.1276 1.07552 7.59375 1 8.07812 1C8.42708 1 8.76562 1.04948 9.09375 1.14844C9.42188 1.24219 9.73177 1.3776 10.0234 1.55469C10.3151 1.72656 10.5833 1.9349 10.8281 2.17969C11.0677 2.41927 11.276 2.6849 11.4531 2.97656C11.625 3.26823 11.7604 3.57812 11.8594 3.90625C11.9531 4.23438 12 4.57292 12 4.92188C12 5.54688 11.8802 6.1276 11.6406 6.66406C11.401 7.20052 11.0391 7.67188 10.5547 8.07812L2.17969 15.1172C2.0599 15.2214 2 15.349 2 15.5C2 15.6354 2.04948 15.7526 2.14844 15.8516C2.2474 15.9505 2.36458 16 2.5 16C2.61979 16 2.72656 15.9609 2.82031 15.8828L11.1953 8.84375C11.7786 8.35417 12.2266 7.78125 12.5391 7.125C12.8464 6.46875 13 5.76042 13 5C13 4.30729 12.8698 3.65885 12.6094 3.05469C12.349 2.44531 11.9922 1.91406 11.5391 1.46094C11.0859 1.00781 10.5573 0.651042 9.95312 0.390625C9.34375 0.130208 8.69271 0 8 0C7.41667 0 6.85156 0.0989583 6.30469 0.296875C5.7526 0.494792 5.2526 0.78125 4.80469 1.15625L1 4.32031V0.5C1 0.364583 0.950521 0.247396 0.851562 0.148438C0.752604 0.0494792 0.635417 0 0.5 0C0.364583 0 0.247396 0.0494792 0.148438 0.148438C0.0494795 0.247396 0 0.364583 0 0.5Z"/>
            </MenuItem.Icon>
        </MenuItem>
        <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=RedoText}" 
                  Command="{x:Static local:EditCommands.Redo}" 
                  CommandTarget="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}">
            <MenuItem.Icon>
                <Path Fill="{StaticResource ContentForeground}" Data="M13 0.5V5.5C13 5.63542 12.9505 5.7526 12.8516 5.85156C12.7526 5.95052 12.6354 6 12.5 6H7.5C7.36458 6 7.2474 5.95052 7.14844 5.85156C7.04948 5.7526 7 5.63542 7 5.5C7 5.36458 7.04948 5.2474 7.14844 5.14844C7.2474 5.04948 7.36458 5 7.5 5H11.25L7.5625 1.92188C7.1875 1.60938 6.77344 1.3776 6.32031 1.22656C5.8724 1.07552 5.40625 1 4.92188 1C4.57292 1 4.23438 1.04948 3.90625 1.14844C3.57812 1.24219 3.26823 1.3776 2.97656 1.55469C2.6849 1.72656 2.41667 1.9349 2.17188 2.17969C1.93229 2.41927 1.72396 2.6849 1.54688 2.97656C1.375 3.26823 1.23958 3.57812 1.14062 3.90625C1.04688 4.23438 1 4.57292 1 4.92188C1 5.54688 1.11979 6.1276 1.35938 6.66406C1.59896 7.20052 1.96094 7.67188 2.44531 8.07812L10.8203 15.1172C10.9401 15.2214 11 15.349 11 15.5C11 15.6354 10.9505 15.7526 10.8516 15.8516C10.7526 15.9505 10.6354 16 10.5 16C10.3802 16 10.2734 15.9609 10.1797 15.8828L1.80469 8.84375C1.22135 8.35417 0.773438 7.78125 0.460938 7.125C0.153646 6.46875 0 5.76042 0 5C0 4.30729 0.130208 3.65885 0.390625 3.05469C0.651042 2.44531 1.00781 1.91406 1.46094 1.46094C1.91406 1.00781 2.44271 0.651042 3.04688 0.390625C3.65625 0.130208 4.30729 0 5 0C5.58333 0 6.14844 0.0989583 6.69531 0.296875C7.2474 0.494792 7.7474 0.78125 8.19531 1.15625L12 4.32031V0.5C12 0.364583 12.0495 0.247396 12.1484 0.148438C12.2474 0.0494792 12.3646 0 12.5 0C12.6354 0 12.7526 0.0494792 12.8516 0.148438C12.9505 0.247396 13 0.364583 13 0.5Z"/>
            </MenuItem.Icon>
        </MenuItem>
        <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=CutText}" 
                  Command="{x:Static local:EditCommands.Cut}" 
                  CommandTarget="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}">
            <MenuItem.Icon>
                <Grid x:Name="Cut">
                    <Path  Fill="{StaticResource ContentForeground}">
                        <Path.Data>
                            <PathGeometry>M10.2656 10.0938C10.0156 10.1562 9.76562 10.2552 9.51562 10.3906L8.74219 11.0234C8.48698 11.3151 8.29948 11.6328 8.17969 11.9766C8.0599 12.3203 8 12.6823 8 13.0625C8 13.4688 8.08073 13.8516 8.24219 14.2109C8.40365 14.5651 8.61979 14.875 8.89062 15.1406C9.16667 15.4062 9.48698 15.6172 9.85156 15.7734C10.2161 15.9245 10.599 16 11 16C11.4167 16 11.8073 15.9219 12.1719 15.7656C12.5365 15.6094 12.8542 15.3958 13.125 15.125C13.3958 14.8542 13.6094 14.5365 13.7656 14.1719C13.9219 13.8073 14 13.4167 14 13C14 12.599 13.9219 12.2161 13.7656 11.8516C13.6146 11.487 13.4062 11.1693 13.1406 10.8984C12.875 10.6224 12.5625 10.4036 12.2031 10.2422C11.849 10.0807 11.4688 10 11.0625 10C10.7812 10 10.5156 10.0312 10.2656 10.0938ZM12.8359 12.2031C12.9453 12.4375 13 12.6901 13 12.9609C13 13.2422 12.9479 13.5078 12.8438 13.7578C12.7396 14.0026 12.5964 14.2188 12.4141 14.4062C12.237 14.5885 12.026 14.7344 11.7812 14.8438C11.5417 14.9479 11.2812 15 11 15C10.724 15 10.4635 14.9479 10.2188 14.8438C9.97917 14.7344 9.76823 14.5911 9.58594 14.4141C9.40885 14.2318 9.26562 14.0208 9.15625 13.7812C9.05208 13.5365 9 13.276 9 13C9 12.7292 9.05208 12.4714 9.15625 12.2266C9.26562 11.9818 9.40885 11.7708 9.58594 11.5938C9.76823 11.4115 9.97917 11.2682 10.2188 11.1641C10.4635 11.0547 10.724 11 11 11C11.2656 11 11.5182 11.0521 11.7578 11.1562C12.0026 11.2604 12.2161 11.401 12.3984 11.5781C12.5807 11.7552 12.7266 11.9635 12.8359 12.2031Z</PathGeometry>
                        </Path.Data>
                    </Path>
                    <Path Fill="{StaticResource ContentForeground}">
                        <Path.Data>
                            <PathGeometry>M4.48438 10.3906L5.25781 11.0234L7 8.34375L8.74219 11.0234L9.51562 10.3906L2.92188 0.226562C2.875 0.153646 2.8125 0.0989583 2.73438 0.0625C2.65625 0.0208333 2.57552 0 2.49219 0C2.35156 0 2.23438 0.0520833 2.14062 0.15625C2.04688 0.255208 2 0.372396 2 0.507812C2 0.611979 2.02604 0.700521 2.07812 0.773438L6.40625 7.42969L4.48438 10.3906ZM8.19531 6.50781L7.59375 5.59375L11.0781 0.226562C11.125 0.153646 11.1875 0.0989583 11.2656 0.0625C11.3438 0.0208333 11.4245 0 11.5078 0C11.6484 0 11.7656 0.0520833 11.8594 0.15625C11.9531 0.255208 12 0.372396 12 0.507812C12 0.611979 11.974 0.700521 11.9219 0.773438L8.19531 6.50781Z</PathGeometry>
                        </Path.Data>
                    </Path>
                    <Path Fill="{StaticResource ContentForeground}">
                        <Path.Data>
                            <PathGeometry>M0.234375 11.8203C0.078125 12.1745 0 12.5469 0 12.9375C0 13.3594 0.0755208 13.7578 0.226562 14.1328C0.377604 14.5026 0.585938 14.8281 0.851562 15.1094C1.1224 15.3854 1.4401 15.6042 1.80469 15.7656C2.16927 15.9219 2.56771 16 3 16C3.40104 16 3.78385 15.9245 4.14844 15.7734C4.51302 15.6172 4.83073 15.4062 5.10156 15.1406C5.3776 14.875 5.59635 14.5651 5.75781 14.2109C5.91927 13.8516 6 13.4688 6 13.0625C6 12.6823 5.9401 12.3203 5.82031 11.9766C5.70052 11.6328 5.51302 11.3151 5.25781 11.0234L4.48438 10.3906C4.23958 10.2552 3.98958 10.1562 3.73438 10.0938C3.48438 10.0312 3.21875 10 2.9375 10C2.54688 10 2.17188 10.0807 1.8125 10.2422C1.45833 10.3984 1.14583 10.612 0.875 10.8828C0.609375 11.1484 0.395833 11.4609 0.234375 11.8203ZM4.83594 12.2031C4.94531 12.4375 5 12.6901 5 12.9609C5 13.2422 4.94792 13.5078 4.84375 13.7578C4.73958 14.0026 4.59635 14.2188 4.41406 14.4062C4.23698 14.5885 4.02604 14.7344 3.78125 14.8438C3.54167 14.9479 3.28125 15 3 15C2.72396 15 2.46354 14.9479 2.21875 14.8438C1.97917 14.7344 1.76823 14.5911 1.58594 14.4141C1.40885 14.2318 1.26562 14.0208 1.15625 13.7812C1.05208 13.5365 1 13.276 1 13C1 12.7292 1.05208 12.4714 1.15625 12.2266C1.26562 11.9818 1.40885 11.7708 1.58594 11.5938C1.76823 11.4115 1.97917 11.2682 2.21875 11.1641C2.46354 11.0547 2.72396 11 3 11C3.26562 11 3.51823 11.0521 3.75781 11.1562C4.0026 11.2604 4.21615 11.401 4.39844 11.5781C4.58073 11.7552 4.72656 11.9635 4.83594 12.2031Z</PathGeometry>
                        </Path.Data>
                    </Path>
                </Grid>
            </MenuItem.Icon>
        </MenuItem>
        <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=CopyText}" 
                  Command="{x:Static local:EditCommands.Copy}" 
                  CommandTarget="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}">
            <MenuItem.Icon>
                <Path Fill="{StaticResource ContentForeground}">
                    <Path.Data>
                        <PathGeometry>M3.96094 14C3.70052 14 3.45052 13.9479 3.21094 13.8438C2.97656 13.7344 2.76823 13.5911 2.58594 13.4141C2.40885 13.2318 2.26562 13.0234 2.15625 12.7891C2.05208 12.5495 2 12.2995 2 12.0391V1.96094C2 1.70052 2.05208 1.45312 2.15625 1.21875C2.26562 0.979167 2.40885 0.770833 2.58594 0.59375C2.76823 0.411458 2.97656 0.268229 3.21094 0.164062C3.45052 0.0546875 3.70052 0 3.96094 0H10.0391C10.2995 0 10.5469 0.0546875 10.7812 0.164062C11.0208 0.268229 11.2292 0.411458 11.4062 0.59375C11.5885 0.770833 11.7318 0.979167 11.8359 1.21875C11.9453 1.45312 12 1.70052 12 1.96094V12.0391C12 12.2995 11.9453 12.5495 11.8359 12.7891C11.7318 13.0234 11.5885 13.2318 11.4062 13.4141C11.2292 13.5911 11.0208 13.7344 10.7812 13.8438C10.5469 13.9479 10.2995 14 10.0391 14H3.96094ZM10 13C10.1406 13 10.2708 12.974 10.3906 12.9219C10.5104 12.8698 10.6146 12.7995 10.7031 12.7109C10.7969 12.6172 10.8698 12.5104 10.9219 12.3906C10.974 12.2708 11 12.1406 11 12V2C11 1.86458 10.974 1.73698 10.9219 1.61719C10.8698 1.49219 10.7969 1.38542 10.7031 1.29688C10.6146 1.20312 10.5078 1.13021 10.3828 1.07812C10.263 1.02604 10.1354 1 10 1H4C3.85938 1 3.72917 1.02604 3.60938 1.07812C3.48958 1.13021 3.38281 1.20312 3.28906 1.29688C3.20052 1.38542 3.13021 1.48958 3.07812 1.60938C3.02604 1.72917 3 1.85938 3 2V12C3 12.1406 3.02604 12.2734 3.07812 12.3984C3.13021 12.5182 3.20052 12.6224 3.28906 12.7109C3.3776 12.7995 3.48177 12.8698 3.60156 12.9219C3.72656 12.974 3.85938 13 4 13H10ZM3.5 16C3.01562 16 2.5599 15.9089 2.13281 15.7266C1.71094 15.5443 1.34115 15.2943 1.02344 14.9766C0.705729 14.6589 0.455729 14.2891 0.273438 13.8672C0.0911458 13.4401 0 12.9844 0 12.5V4C0 3.64062 0.0885417 3.3099 0.265625 3.00781C0.447917 2.70573 0.692708 2.46354 1 2.28125V12.5C1 12.8438 1.0651 13.1693 1.19531 13.4766C1.32552 13.7786 1.5026 14.0443 1.72656 14.2734C1.95573 14.4974 2.22135 14.6745 2.52344 14.8047C2.83073 14.9349 3.15625 15 3.5 15H9.71875C9.53646 15.3073 9.29427 15.5521 8.99219 15.7344C8.6901 15.9115 8.35938 16 8 16H3.5Z</PathGeometry>
                    </Path.Data>
                </Path>
            </MenuItem.Icon>
        </MenuItem>
        <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=PasteText}" 
                  Command="{x:Static local:EditCommands.Paste}" 
                  CommandTarget="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}">
            <MenuItem.Icon>
                <Grid>
                <Path Fill="{StaticResource ContentForeground}">
                    <Path.Data>
                        <PathGeometry>M0.921875 15.8828C1.10417 15.9609 1.29688 16 1.5 16H4.5C4.63542 16 4.7526 15.9505 4.85156 15.8516C4.95052 15.7526 5 15.6354 5 15.5C5 15.3646 4.95052 15.2474 4.85156 15.1484C4.7526 15.0495 4.63542 15 4.5 15H1.5C1.36458 15 1.2474 14.9505 1.14844 14.8516C1.04948 14.7526 1 14.6354 1 14.5V2.5C1 2.36458 1.04948 2.2474 1.14844 2.14844C1.2474 2.04948 1.36458 2 1.5 2H3.08594C3.13802 2.15104 3.21094 2.28906 3.30469 2.41406C3.39844 2.53385 3.50781 2.63802 3.63281 2.72656C3.75781 2.8151 3.89323 2.88281 4.03906 2.92969C4.1849 2.97656 4.33854 3 4.5 3H7.5C7.66146 3 7.8151 2.97656 7.96094 2.92969C8.10677 2.88281 8.24219 2.8151 8.36719 2.72656C8.49219 2.63802 8.60156 2.53385 8.69531 2.41406C8.78906 2.28906 8.86198 2.15104 8.91406 2H10.5C10.6146 2 10.7057 2.02344 10.7734 2.07031C10.8411 2.11198 10.8906 2.16927 10.9219 2.24219C10.9583 2.3099 10.9818 2.38802 10.9922 2.47656C11.0026 2.5651 11.0078 2.65625 11.0078 2.75C11.0078 2.82292 11.0052 2.90365 11 2.99219C10.9948 3.07552 10.9922 3.16146 10.9922 3.25C10.9922 3.34375 10.9974 3.4375 11.0078 3.53125C11.0234 3.61979 11.0469 3.70052 11.0781 3.77344C11.1146 3.84115 11.1667 3.89583 11.2344 3.9375C11.3021 3.97917 11.3906 4 11.5 4C11.6354 4 11.7526 3.95052 11.8516 3.85156C11.9505 3.7526 12 3.63542 12 3.5V2.5C12 2.29688 11.9609 2.10417 11.8828 1.92188C11.8047 1.73958 11.6953 1.58073 11.5547 1.44531C11.4193 1.30469 11.2604 1.19531 11.0781 1.11719C10.8958 1.03906 10.7031 1 10.5 1H8.91406C8.86198 0.848958 8.78906 0.713542 8.69531 0.59375C8.60156 0.46875 8.49219 0.361979 8.36719 0.273438C8.24219 0.184896 8.10677 0.117188 7.96094 0.0703125C7.8151 0.0234375 7.66146 0 7.5 0H4.5C4.33854 0 4.1849 0.0234375 4.03906 0.0703125C3.89323 0.117188 3.75781 0.184896 3.63281 0.273438C3.50781 0.361979 3.39844 0.46875 3.30469 0.59375C3.21094 0.713542 3.13802 0.848958 3.08594 1H1.5C1.29688 1 1.10417 1.03906 0.921875 1.11719C0.739583 1.19531 0.578125 1.30469 0.4375 1.44531C0.302083 1.58073 0.195312 1.73958 0.117188 1.92188C0.0390625 2.10417 0 2.29688 0 2.5V14.5C0 14.7031 0.0390625 14.8958 0.117188 15.0781C0.195312 15.2604 0.302083 15.4219 0.4375 15.5625C0.578125 15.6979 0.739583 15.8047 0.921875 15.8828ZM7.85156 1.85156C7.7526 1.95052 7.63542 2 7.5 2H4.5C4.36458 2 4.2474 1.95052 4.14844 1.85156C4.04948 1.7526 4 1.63542 4 1.5C4 1.36458 4.04948 1.2474 4.14844 1.14844C4.2474 1.04948 4.36458 1 4.5 1H7.5C7.63542 1 7.7526 1.04948 7.85156 1.14844C7.95052 1.2474 8 1.36458 8 1.5C8 1.63542 7.95052 1.7526 7.85156 1.85156Z</PathGeometry>
                    </Path.Data>
                </Path>
                    <Path  Fill="{StaticResource ContentForeground}">
                    <Path.Data>
                        <PathGeometry>M7.5 16C7.29688 16 7.10417 15.9609 6.92188 15.8828C6.73958 15.8047 6.57812 15.6979 6.4375 15.5625C6.30208 15.4219 6.19531 15.2604 6.11719 15.0781C6.03906 14.8958 6 14.7031 6 14.5V6.5C6 6.29688 6.03906 6.10417 6.11719 5.92188C6.19531 5.73958 6.30208 5.58073 6.4375 5.44531C6.57812 5.30469 6.73958 5.19531 6.92188 5.11719C7.10417 5.03906 7.29688 5 7.5 5H12.5C12.7031 5 12.8958 5.03906 13.0781 5.11719C13.2604 5.19531 13.4193 5.30469 13.5547 5.44531C13.6953 5.58073 13.8047 5.73958 13.8828 5.92188C13.9609 6.10417 14 6.29688 14 6.5V14.5C14 14.7031 13.9609 14.8958 13.8828 15.0781C13.8047 15.2604 13.6953 15.4219 13.5547 15.5625C13.4193 15.6979 13.2604 15.8047 13.0781 15.8828C12.8958 15.9609 12.7031 16 12.5 16H7.5ZM12.8516 14.8516C12.7526 14.9505 12.6354 15 12.5 15H7.5C7.36458 15 7.2474 14.9505 7.14844 14.8516C7.04948 14.7526 7 14.6354 7 14.5V6.5C7 6.36458 7.04948 6.2474 7.14844 6.14844C7.2474 6.04948 7.36458 6 7.5 6H12.5C12.6354 6 12.7526 6.04948 12.8516 6.14844C12.9505 6.2474 13 6.36458 13 6.5V14.5C13 14.6354 12.9505 14.7526 12.8516 14.8516Z</PathGeometry>
                    </Path.Data>
                </Path>
                </Grid>
            </MenuItem.Icon>
        </MenuItem>
        <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=SelectallText}" 
                  Command="{x:Static local:EditCommands.SelectAll}" 
                  CommandTarget="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}">
        </MenuItem>
        <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=OutliningText}">
            <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=ExpandallText}" 
                      Command="{x:Static local:EditCommands.ExpandAll}" 
                      CommandTarget="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}">
            </MenuItem>
            <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=CollapseallText}" 
                      Command="{x:Static local:EditCommands.CollapseAll}" 
                      CommandTarget="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}">
            </MenuItem>
        </MenuItem>
        <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=IncreaseindentText}" 
                  Command="{x:Static local:EditCommands.IncreaseIndent}" 
                  CommandTarget="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}">
            <MenuItem.Icon>
                <Grid>
                    <Path Fill="{StaticResource ContentForeground}">
                        <Path.Data>
                            <PathGeometry>M0.5 0C0.223858 0 0 0.223858 0 0.5C0 0.776142 0.223858 1 0.5 1H15.5C15.7761 1 16 0.776142 16 0.5C16 0.223858 15.7761 0 15.5 0H0.5ZM8 4.5C8 4.77614 8.22386 5 8.5 5H15.5C15.7761 5 16 4.77614 16 4.5C16 4.22386 15.7761 4 15.5 4H8.5C8.22386 4 8 4.22386 8 4.5ZM8.5 8C8.22386 8 8 8.22386 8 8.5C8 8.77614 8.22386 9 8.5 9H15.5C15.7761 9 16 8.77614 16 8.5C16 8.22386 15.7761 8 15.5 8H8.5ZM0.5 12C0.223858 12 0 12.2239 0 12.5C0 12.7761 0.223858 13 0.5 13H15.5C15.7761 13 16 12.7761 16 12.5C16 12.2239 15.7761 12 15.5 12H0.5Z</PathGeometry>
                        </Path.Data>
                    </Path>
                    <Path  Fill="{StaticResource ContentForeground}">
                        <Path.Data>
                            <PathGeometry>M3.85355 4.14645C3.65829 3.95118 3.34171 3.95118 3.14645 4.14645C2.95118 4.34171 2.95118 4.65829 3.14645 4.85355L4.29289 6H0.5C0.223858 6 0 6.22386 0 6.5C0 6.77614 0.223858 7 0.5 7H4.29289L3.14645 8.14645C2.95118 8.34171 2.95118 8.65829 3.14645 8.85355C3.34171 9.04882 3.65829 9.04882 3.85355 8.85355L5.85355 6.85355C6.04882 6.65829 6.04882 6.34171 5.85355 6.14645L3.85355 4.14645Z</PathGeometry>
                        </Path.Data>
                    </Path>
                </Grid>
            </MenuItem.Icon>
        </MenuItem>
        <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=DecreaseindentText}" 
                  Command="{x:Static local:EditCommands.DecreaseIndent}" 
                  CommandTarget="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}">
            <MenuItem.Icon>
                <Grid>
                    <Path Fill="{StaticResource ContentForeground}">
                        <Path.Data>
                            <PathGeometry>M0.5 0C0.223858 0 7.45058e-09 0.223858 7.45058e-09 0.5C7.45058e-09 0.776142 0.223858 1 0.5 1H15.5C15.7761 1 16 0.776142 16 0.5C16 0.223858 15.7761 0 15.5 0H0.5ZM8 4.5C8 4.77614 8.22386 5 8.5 5H15.5C15.7761 5 16 4.77614 16 4.5C16 4.22386 15.7761 4 15.5 4H8.5C8.22386 4 8 4.22386 8 4.5ZM8.5 8C8.22386 8 8 8.22386 8 8.5C8 8.77614 8.22386 9 8.5 9H15.5C15.7761 9 16 8.77614 16 8.5C16 8.22386 15.7761 8 15.5 8H8.5ZM0.5 12C0.223858 12 7.45058e-09 12.2239 7.45058e-09 12.5C7.45058e-09 12.7761 0.223858 13 0.5 13H15.5C15.7761 13 16 12.7761 16 12.5C16 12.2239 15.7761 12 15.5 12H0.5Z</PathGeometry>
                        </Path.Data>
                    </Path>
                </Grid>
            </MenuItem.Icon>
        </MenuItem>
        <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=CommentlinesText}" 
                  Command="{x:Static local:EditCommands.CommentSelection}" 
                  CommandTarget="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}">
            <MenuItem.Icon>
                <Grid>
                    <Path Data="M1 0.5C0.723858 0.5 0.5 0.723858 0.5 1C0.5 1.27614 0.723858 1.5 1 1.5H2C2.27614 1.5 2.5 1.27614 2.5 1C2.5 0.723858 2.27614 0.5 2 0.5H1Z" Fill="{StaticResource ContentForeground}"/>
                    <Path Data="M4 0.5C3.72386 0.5 3.5 0.723858 3.5 1C3.5 1.27614 3.72386 1.5 4 1.5H16C16.2761 1.5 16.5 1.27614 16.5 1C16.5 0.723858 16.2761 0.5 16 0.5H4Z" Fill="{StaticResource ContentForeground}"/>
                    <Path Data="M5.5 4C5.5 3.72386 5.72386 3.5 6 3.5H16C16.2761 3.5 16.5 3.72386 16.5 4C16.5 4.27614 16.2761 4.5 16 4.5H6C5.72386 4.5 5.5 4.27614 5.5 4Z" Fill="{StaticResource ContentForeground}"/>
                    <Path Data="M6 6.5C5.72386 6.5 5.5 6.72386 5.5 7C5.5 7.27614 5.72386 7.5 6 7.5H16C16.2761 7.5 16.5 7.27614 16.5 7C16.5 6.72386 16.2761 6.5 16 6.5H6Z" Fill="{StaticResource ContentForeground}"/>
                    <Path Data="M9.5 10C9.5 9.72386 9.72386 9.5 10 9.5H16C16.2761 9.5 16.5 9.72386 16.5 10C16.5 10.2761 16.2761 10.5 16 10.5H10C9.72386 10.5 9.5 10.2761 9.5 10Z" Fill="{StaticResource ContentForeground}"/>
                    <Path Data="M4 12.5C3.72386 12.5 3.5 12.7239 3.5 13C3.5 13.2761 3.72386 13.5 4 13.5H16C16.2761 13.5 16.5 13.2761 16.5 13C16.5 12.7239 16.2761 12.5 16 12.5H4Z" Fill="{StaticResource ContentForeground}"/>
                </Grid>
            </MenuItem.Icon>
        </MenuItem>
        <MenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=UncommentlinesText}" 
                  Command="{x:Static local:EditCommands.UncommentSelection}" 
                  CommandTarget="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}}">
            <MenuItem.Icon>
                <Grid>
                    <Path Data="M1.96424 1.18583C2.06679 0.929436 1.94209 0.638451 1.68569 0.535894C1.4293 0.433338 1.13832 0.558046 1.03576 0.814438L0.0357615 3.31445C-0.0134879 3.43757 -0.0118093 3.57521 0.0404278 3.6971C0.0926651 3.81899 0.191182 3.91513 0.314306 3.96438L2.81431 4.96437C3.0707 5.06693 3.36168 4.94222 3.46424 4.68583C3.56679 4.42944 3.44209 4.13845 3.18569 4.03589L1.69713 3.44047C3.91023 2.40628 5.50208 2.51327 6.507 3.00012C7.6738 3.5654 8.13267 4.66272 8.0068 5.41794C7.96141 5.69033 8.14542 5.94794 8.4178 5.99334C8.69019 6.03874 8.9478 5.85473 8.9932 5.58234C9.20066 4.33756 8.45953 2.83489 6.94299 2.10017C5.62432 1.46131 3.77508 1.41508 1.45836 2.45052L1.96424 1.18583Z" Fill="{StaticResource ContentForeground}"/>
                    <Path Data="M2.5 7C2.5 6.72386 2.72386 6.5 3 6.5H13C13.2761 6.5 13.5 6.72386 13.5 7C13.5 7.27614 13.2761 7.5 13 7.5H3C2.72386 7.5 2.5 7.27614 2.5 7Z" Fill="{StaticResource ContentForeground}"/>
                    <Path Data="M2.5 10C2.5 9.72386 2.72386 9.5 3 9.5H13C13.2761 9.5 13.5 9.72386 13.5 10C13.5 10.2761 13.2761 10.5 13 10.5H3C2.72386 10.5 2.5 10.2761 2.5 10Z" Fill="{StaticResource ContentForeground}"/>
                    <Path Data="M7 12.5C6.72386 12.5 6.5 12.7239 6.5 13C6.5 13.2761 6.72386 13.5 7 13.5H13C13.2761 13.5 13.5 13.2761 13.5 13C13.5 12.7239 13.2761 12.5 13 12.5H7Z" Fill="{StaticResource ContentForeground}"/>
                    <Path Data="M0.5 16C0.5 15.7239 0.723858 15.5 1 15.5H13C13.2761 15.5 13.5 15.7239 13.5 16C13.5 16.2761 13.2761 16.5 13 16.5H1C0.723858 16.5 0.5 16.2761 0.5 16Z" Fill="{StaticResource ContentForeground}"/>
                </Grid>
                
            </MenuItem.Icon>
        </MenuItem>
    </ContextMenu>

    <Style x:Key="SyncfusionFindReplaceControlStyle" TargetType="{x:Type local:FindReplaceControl}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource EditControlFocusVisualStyle}" />
        <Setter Property="MinWidth" Value="300" />
        <Setter Property="MinHeight" Value="378" />
        <Setter Property="FocusManager.IsFocusScope" Value="True" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="Cycle" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:FindReplaceControl}">
                    <Grid x:Name="PART_MainGrid" FocusManager.FocusedElement="{Binding ElementName=PART_FindWhat}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Border CornerRadius="8">
                            <Grid x:Name="PART_TitleGrid"
                                  Margin="8,12,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="3*" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="3*" />
                                    <ColumnDefinition Width="4*" />
                                </Grid.ColumnDefinitions>
                                <Grid x:Name="PART_TitleContentGrid">
                                    <syncfusion:RibbonButton x:Name="PART_RibbonFind" 
                                                             Height="24" 
                                                             BorderThickness="0"
                                                             Label="" 
                                                             Grid.Column="2" 
                                                             Padding="0,2" 
                                                             VerticalAlignment="Stretch"
                                                             HorizontalAlignment="Stretch" 
                                                             SmallIcon="{x:Null}" 
                                                             IsToggle="True" 
                                                             ClickMode="Press" 
                                                             IsHitTestVisible="False">
                                        <syncfusion:RibbonButton.IsSelected>
                                            <MultiBinding Converter="{StaticResource tabSelectionConverter}">
                                                <Binding Path="IsFindTabActive" />
                                                <Binding Path="IsFindSymbolTabActive" />
                                            </MultiBinding>
                                        </syncfusion:RibbonButton.IsSelected>
                                    </syncfusion:RibbonButton>
                                    <syncfusion:SplitButton x:Name="PART_FindButton" 
                                                            SizeForm="Small" 
                                                            SmallIcon="{x:Null}" 
                                                            HorizontalAlignment="Stretch" 
                                                            VerticalAlignment="Stretch"  
                                                            Height="24" 
                                                            Width="120">
                                        <syncfusion:SplitButton.Label>
                                            <MultiBinding Converter="{StaticResource tabTextConverter}">
                                                <Binding Path="IsFindTabActive" />
                                                <Binding Path="IsFindSymbolTabActive" />
                                                <Binding Path="Text" RelativeSource="{RelativeSource Self}" />
                                            </MultiBinding>
                                        </syncfusion:SplitButton.Label>
                                        <syncfusion:RibbonMenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=QuickfindText}" x:Name="PART_FindMenu" Style="{StaticResource SyncfusionRibbonMenuItemStyle}" />
                                        <syncfusion:RibbonMenuItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=FindsymbolText}" x:Name="PART_FindSymbolMenu" Style="{StaticResource SyncfusionRibbonMenuItemStyle}" />
                                        <syncfusion:SplitButton.ToolTip>
                                            <ToolTip Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=SwitchtoquickfindtooltipText}">
                                            </ToolTip>
                                        </syncfusion:SplitButton.ToolTip>
                                    </syncfusion:SplitButton>
                                </Grid>
                                <Separator Grid.Column="1" Margin="4" Width="1" />
                                <Grid Grid.Column="2" 
                                      FocusVisualStyle="{StaticResource EditControlFocusVisualStyle}">
                                    <syncfusion:RibbonButton x:Name="PART_ReplaceButton" 
                                                             Height="30" 
                                                             BorderThickness="0"
                                                             Width="130" 
                                                             Label="" 
                                                             FocusVisualStyle="{StaticResource EditControlFocusVisualStyle}" 
                                                             Grid.Column="2" 
                                                             VerticalAlignment="Stretch" 
                                                             HorizontalAlignment="Stretch" 
                                                             SmallIcon="{x:Null}" 
                                                             IsSelected="{Binding IsReplaceTabActive}" 
                                                             IsToggle="False" 
                                                             ClickMode="Press" 
                                                             CornerRadius="4"
                                                             Style="{StaticResource SyncfusionQATMenuItemRibbonButtonStyle}">
                                        <syncfusion:RibbonButton.ToolTip>
                                            <ToolTip Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=SwitchtoquickreplacetooltipText}" />
                                        </syncfusion:RibbonButton.ToolTip>
                                    </syncfusion:RibbonButton>
                                    <TextBlock Name="textBlock5" 
                                               Text="{Sync_Resources:EditLocalizationResourceExtension ResourceName=QuickreplaceText}" 
                                               FocusVisualStyle="{StaticResource EditControlFocusVisualStyle}" 
                                               VerticalAlignment="Center" 
                                               FontFamily="{TemplateBinding FontFamily}" 
                                               FontSize="{TemplateBinding FontSize}" 
                                               TextAlignment="Center" 
                                               IsHitTestVisible="False" />
                                </Grid>
                            </Grid>
                        </Border>
                        <Label x:Name="PART_FindWhat_Label" 
                               Grid.Row="1" 
                               Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=FindwhatText}" 
                               VerticalAlignment="Center" 
                               Margin="8,12,8,4" 
                               Grid.ColumnSpan="3" 
                               Target="{Binding ElementName=PART_FindWhat}" 
                               Padding="0" />
                        <syncfusion:AutoComplete x:Name="PART_FindWhat" 
                                                 CustomSource="{Binding FindHistory}"  
                                                 Text="{Binding FindText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                                 Grid.Row="2" 
                                                 MinHeight="{StaticResource Windows11Light.MinHeight}"
                                                 Margin="8,0" 
                                                 Grid.ColumnSpan="3" />

                        <Label Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=ReplacewithText}" 
                               Grid.Row="3" 
                               VerticalAlignment="Center" 
                               Margin="8,12,8,4" 
                               Grid.ColumnSpan="3" 
                               Visibility="{Binding IsReplaceTabActive, Converter={StaticResource VisibilityConverter}}" 
                               Target="{Binding ElementName=PART_ReplaceWith}" 
                               Padding="0" />
                        <syncfusion:AutoComplete x:Name="PART_ReplaceWith" 
                                                 MinHeight="{StaticResource Windows11Light.MinHeight}"
                                                 IsHistory="True" 
                                                 CustomSource="{Binding ReplaceHistory}"
                                                 Text="{Binding ReplaceText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                                 Visibility="{Binding IsReplaceTabActive, Converter={StaticResource VisibilityConverter}}" 
                                                 Grid.Row="4" 
                                                 Margin="8,0" 
                                                 Grid.ColumnSpan="3" />
                        <Label Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=LookinText}" 
                               Grid.Row="5" 
                               VerticalAlignment="Center" 
                               Margin="8,12,8,4"
                               Grid.ColumnSpan="3" 
                               Target="{Binding ElementName=PART_LookIn}" 
                               Padding="0" />
                        <ComboBox x:Name="PART_LookIn" 
                                  Grid.Row="6" 
                                  Margin="8,0" 
                                  VerticalContentAlignment="Center" 
                                  MinHeight="25"
                                  Grid.ColumnSpan="3">
                            <ComboBoxItem Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=CurrentdocumentText}" IsSelected="True" />
                            <ComboBoxItem Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=SelectionText}" IsSelected="{Binding Path=IsSelectionSelected, Mode=TwoWay}" />
                        </ComboBox>

                        <Label Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=FindoptionText}" Grid.Row="7" Grid.ColumnSpan="3" FontWeight="Bold" Margin="8,12,8,3" Padding="0" />

                        <Grid x:Name="PART_FindOptionsGrid" Grid.Row="8" Grid.ColumnSpan="3" Margin="7,0,8,0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <RadioButton x:Name="PART_WholeWord" 
                                         Visibility="{Binding IsFindSymbolTabActive, Converter={StaticResource VisibilityConverter}}" 
                                         Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=WholewordText}" 
                                         IsChecked="{Binding Path=IsWholeWordChecked, Mode=TwoWay}" 
                                         VerticalContentAlignment="Center"
                                         VerticalAlignment="Center"
                                         HorizontalAlignment="Left" 
                                         Margin="0,4" 
                                         Padding="5,0,0,0"  />
                            <RadioButton x:Name="PART_Prefix" 
                                         Visibility="{Binding IsFindSymbolTabActive, Converter={StaticResource VisibilityConverter}}" 
                                         Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=PrefixText}" 
                                         Grid.Row="1" 
                                         IsChecked="{Binding Path=IsPrefix, Mode=TwoWay}" 
                                         VerticalContentAlignment="Center" 
                                         VerticalAlignment="Center" 
                                         Margin="0,4" 
                                         Padding="5,0,0,0" 
                                         HorizontalAlignment="Left" />
                            <RadioButton x:Name="PART_Substring" 
                                         Visibility="{Binding IsFindSymbolTabActive, Converter={StaticResource VisibilityConverter}}" 
                                         Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=SubstringText}" 
                                         Grid.Row="2" 
                                         IsChecked="{Binding Path=IsSubstring, Mode=TwoWay}" 
                                         VerticalContentAlignment="Center" 
                                         VerticalAlignment="Center" Margin="0,4" 
                                         Padding="5,0,0,0" 
                                         HorizontalAlignment="Left" />
                            <CheckBox x:Name="PART_MatchCase" 
                                      Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=MatchcaseText}" 
                                      Grid.Row="3" 
                                      IsChecked="{Binding Path=IsMatchCase, Mode=TwoWay}"
                                      VerticalContentAlignment="Center" 
                                      VerticalAlignment="Center" 
                                      Margin="0,4" 
                                      Padding="5,0,0,0" 
                                      HorizontalAlignment="Left" />
                            <CheckBox x:Name="PART_MatchWholeWord"
                                      Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=MatchwholewordText}" 
                                      IsChecked="{Binding Path=IsMatchWholeWord, Mode=TwoWay}"
                                      Grid.Row="4" 
                                      Margin="0,4"  
                                      VerticalAlignment="Top" 
                                      Padding="5,0,0,0">
                                <CheckBox.Visibility>
                                    <MultiBinding Converter="{StaticResource orConverter}">
                                        <Binding Path="IsFindTabActive" />
                                        <Binding Path="IsReplaceTabActive" />
                                    </MultiBinding>
                                </CheckBox.Visibility>
                            </CheckBox>
                            <CheckBox x:Name="PART_SearchUp"
                                      Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=SearchupText}" 
                                      IsChecked="{Binding Path=IsSearchUp, Mode=TwoWay}" 
                                      Grid.Row="5" 
                                      Margin="0,4"  
                                      VerticalAlignment="Top" 
                                      Padding="5,0,0,0">
                                <CheckBox.Visibility>
                                    <MultiBinding Converter="{StaticResource orConverter}">
                                        <Binding Path="IsFindTabActive" />
                                        <Binding Path="IsReplaceTabActive" />
                                    </MultiBinding>
                                </CheckBox.Visibility>
                            </CheckBox>
                            <CheckBox x:Name="PART_SearchHidden"
                                      Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=SearchhiddenText}" 
                                      IsChecked="{Binding Path=IsIncludeHiddenText, Mode=TwoWay}" 
                                      Grid.Row="6" 
                                      Margin="0,4"  
                                      VerticalAlignment="Center" 
                                      VerticalContentAlignment="Center" 
                                      Padding="5,0,0,0">
                                <CheckBox.Visibility>
                                    <MultiBinding Converter="{StaticResource orConverter}">
                                        <Binding Path="IsFindTabActive" />
                                        <Binding Path="IsReplaceTabActive" />
                                    </MultiBinding>
                                </CheckBox.Visibility>
                            </CheckBox>
                        </Grid>
                        <Separator Grid.Row="9" Height="1" Margin="0,7,0,0">
                        </Separator>
                        <StackPanel Grid.Row="10" 
                                    Orientation="Horizontal" 
                                    Margin="8,4" 
                                    Grid.ColumnSpan="3" 
                                    HorizontalAlignment="Right">
                            <Button x:Name="PART_Find"
                                     
                                    Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=FindnextText}"
                                    Height="30" 
                                    Width="75" 
                                    Padding="5,3,5,3" 
                                    Margin="8,0,0,0" 
                                    Command="{Binding FindNextCommand}" 
                                    CommandTarget="{Binding Editor}"  
                                    IsDefault="True">
                                <Button.Visibility>
                                    <MultiBinding Converter="{StaticResource orConverter}">
                                        <Binding Path="IsFindTabActive" />
                                        <Binding Path="IsReplaceTabActive" />
                                    </MultiBinding>
                                </Button.Visibility>
                            </Button>
                            <Button x:Name="PART_FindAll"
                                     
                                    Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=FindallText}"
                                    Height="30" 
                                    Width="75" 
                                    Padding="5,3,5,3" 
                                    Margin="8,0,0,0" 
                                    Command="{Binding FindAllCommand}" 
                                    CommandTarget="{Binding Editor}"  
                                    Visibility="{Binding IsFindSymbolTabActive, Converter={StaticResource VisibilityConverter}}" />
                            <Button x:Name="PART_Replace"
                                     
                                    Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=ReplaceText}" 
                                    Height="30" 
                                    Width="75" 
                                    Padding="5,3,5,3" 
                                    Margin="8,0,0,0" 
                                    Command="{Binding ReplaceCommand}" 
                                    CommandTarget="{Binding Editor}"   
                                    Visibility="{Binding IsReplaceTabActive, Converter={StaticResource VisibilityConverter}}" />
                            <Button x:Name="PART_ReplaceAll"
                                     
                                    Content="{Sync_Resources:EditLocalizationResourceExtension ResourceName=ReplaceallText}" 
                                    Height="30" 
                                    Width="84" 
                                    Padding="5,3,5,3" 
                                    Margin="8,0,0,0" 
                                    Command="{Binding ReplaceAllCommand}" 
                                    CommandTarget="{Binding Editor}"  
                                    Visibility="{Binding IsReplaceTabActive, Converter={StaticResource VisibilityConverter}}" />
                        </StackPanel>
                        <StatusBar Grid.Row="11" 
                                   Name="PART_StatusBar" 
                                   Grid.ColumnSpan="3" 
                                   BorderThickness="{StaticResource Windows11Light.BorderThickness0100}">
                            <StatusBarItem>
                                <TextBlock Name="PART_StatusInfo" FontSize="{TemplateBinding FontSize}" Margin="5,0" Text="{Binding StatusMessage}" />
                            </StatusBarItem>
                        </StatusBar>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" TargetName="PART_Find"  Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter Property="MinHeight" TargetName="PART_FindAll"  Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter Property="MinHeight" TargetName="PART_Replace" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter Property="MinHeight" TargetName="PART_ReplaceAll" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter Property="MinHeight" TargetName="PART_RibbonFind" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter Property="MinHeight" TargetName="PART_FindButton" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter Property="MinHeight" TargetName="PART_ReplaceButton" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionFindReplaceControlStyle}" TargetType="{x:Type local:FindReplaceControl}"/>

    <DataTemplate DataType="{x:Type local:IIntellisenseItem}" x:Key="IntellisenseItemTemplate">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" MaxWidth="16" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <TextBlock Margin="3,0,0,0" Text="{Binding Text}" Grid.Column="1" VerticalAlignment="Center" />
        </Grid>
    </DataTemplate>

    <Style x:Key="SyncfusionEditControlStyle" TargetType="{x:Type local:EditControl}">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="Background" Value="{StaticResource ContentBackground}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="LineNumberAreaBackground" Value="{StaticResource ContentBackgroundAlt2}"/>
        <Setter Property="LineNumberTextForeground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="SelectionBackground" Value="{StaticResource PrimaryBackground}"/>
        <Setter Property="SelectionForeground" Value="{StaticResource PrimaryForeground}"/>
        <Setter Property="CollapsedTextForeground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="FocusVisualStyle"  Value="{x:Null}" />
        <Setter Property="IsTabStop" Value="True" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Cursor" Value="IBeam" />
        <Setter Property="FindResultsTabVisibility" Value="Auto" />
        <Setter Property="IntellisenseItemTemplate" Value="{StaticResource IntellisenseItemTemplate}" />
        <Setter Property="IntellisenseBoxStyle" Value="{StaticResource WPFListBoxStyle}" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="ContextMenu" Value="{StaticResource SyncfusionContextMenu}"/>
        <Setter Property="ShowDefaultContextMenu" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:EditControl}">
                    <Border x:Name="PART_RootBorder" Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Border.Resources>
                            <Style TargetType="ToggleButton" x:Key="ExpandButtonStyle">
                                <Setter Property="FrameworkElement.Focusable" Value="False" />
                                <Setter Property="FrameworkElement.Width" Value="19" />
                                <Setter Property="FrameworkElement.Height" Value="15" />
                                <Setter Property="Cursor" Value="Arrow" />
                                <Setter Property="SnapsToDevicePixels" Value="True" />
                                <Setter Property="Control.Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="ToggleButton">
                                            <Border x:Name="expandButton" VerticalAlignment="Center"  HorizontalAlignment="Center" Background="{StaticResource ContentBackground}">
                                                <Border x:Name="innerborder" Height="{Binding ActualWidth, RelativeSource={RelativeSource Self}}" Background="{StaticResource ContentBackground}" IsHitTestVisible="True"
                                                    BorderBrush="{StaticResource BorderAlt1}"
                                                    CornerRadius="1"
                                                    BorderThickness="{Binding Path=BorderThickness, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                                    SnapsToDevicePixels="True">
                                                    <Path Margin="1" Data="M0,2L0,3 2,3 2,5 3,5 3,3 5,3 5,2 3,2 3,0 2,0 2,2z" Fill="{StaticResource IconColor}" x:Name="ExpandPath" />
                                                </Border>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="ToggleButton.IsChecked" Value="True">
                                                    <Setter Property="Path.Data" TargetName="ExpandPath" Value="M0,2L0,3 5,3 5,2z" />
                                                    <Setter Property="Fill" TargetName="ExpandPath" Value="{StaticResource IconColor}" />
                                                    <Setter Property="Background" TargetName="innerborder" Value="{StaticResource ContentBackgroundAlt1}" />
                                                    <Setter Property="BorderBrush" TargetName="innerborder" Value="{StaticResource BorderAlt1}" />
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>

                            <Style TargetType="ToggleButton" x:Key="LineButtonStyle">
                                <Setter Property="FrameworkElement.Focusable" Value="False" />
                                <Setter Property="Cursor" Value="Arrow" />
                                <Setter Property="IsHitTestVisible" Value="False" />
                                <Setter Property="SnapsToDevicePixels" Value="True" />
                                <Setter Property="Control.Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="ToggleButton">
                                            <Grid SnapsToDevicePixels="True" Background="{StaticResource ContentBackground}">
                                                <Line SnapsToDevicePixels="True" Y1="0" Y2="{Binding Path=Height, RelativeSource={RelativeSource Mode=TemplatedParent}}" Stroke="{StaticResource BorderAlt}"
                                                StrokeThickness="{Binding Path=BorderThickness, RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource ThicknessConverter}}" HorizontalAlignment="Center" VerticalAlignment="Stretch" />
                                            </Grid>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>

                            <Style TargetType="ToggleButton" x:Key="EmpyLineButtonStyle">
                                <Setter Property="FrameworkElement.Focusable" Value="False" />
                                <Setter Property="Cursor" Value="Arrow" />
                                <Setter Property="IsHitTestVisible" Value="False" />
                                <Setter Property="SnapsToDevicePixels" Value="True" />
                                <Setter Property="Control.Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="ToggleButton">
                                            <Grid Width="19" Background="{StaticResource ContentBackground}">
                                                <Line Y1="0" Y2="{Binding Path=Height, RelativeSource={RelativeSource Mode=TemplatedParent}}" Stroke="Transparent" StrokeThickness="1" HorizontalAlignment="Center" VerticalAlignment="Stretch" />
                                            </Grid>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>

                            <Style TargetType="ToggleButton" x:Key="EndLineButtonStyle">
                                <Setter Property="FrameworkElement.Focusable" Value="False" />
                                <Setter Property="Cursor" Value="Arrow" />
                                <Setter Property="IsHitTestVisible" Value="False" />
                                <Setter Property="SnapsToDevicePixels" Value="True" />
                                <Setter Property="VerticalAlignment" Value="Top" />
                                <Setter Property="Control.Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="ToggleButton">
                                            <Grid SnapsToDevicePixels="True" Background="{StaticResource ContentBackground}" Height="{Binding Path=Height, RelativeSource={RelativeSource Mode=TemplatedParent}}">
                                                <Grid.Resources>
                                                    <local:EndLineToLineHeightConverter x:Key="lineHeightConverter" />
                                                </Grid.Resources>

                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="0.5*" />
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="0.5*" />
                                                </Grid.RowDefinitions>
                                                <Line Y1="0" Stroke="{StaticResource BorderAlt}"
                                                StrokeThickness="{Binding Path=BorderThickness, RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource ThicknessConverter}}" HorizontalAlignment="Center">
                                                    <Line.Y2>
                                                        <MultiBinding Converter="{StaticResource lineHeightConverter}" ConverterParameter="Top">
                                                            <Binding Path="Height" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                                            <Binding Path="Tag" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                                        </MultiBinding>
                                                    </Line.Y2>
                                                </Line>
                                                <Line X1="2.75" X2="7" Stroke="{StaticResource BorderAlt}"
                                                StrokeThickness="{Binding Path=BorderThickness, RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource ThicknessConverter}}" HorizontalAlignment="Center" VerticalAlignment="Bottom" Grid.Row="1" />
                                                <Line Y1="0" Stroke="{StaticResource BorderAlt}"
                                                StrokeThickness="{Binding Path=BorderThickness, RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource ThicknessConverter}}" HorizontalAlignment="Center" Grid.Row="2">
                                                    <Line.Y2>
                                                        <MultiBinding Converter="{StaticResource lineHeightConverter}" ConverterParameter="Bottom">
                                                            <Binding Path="Height" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                                            <Binding Path="Tag" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                                        </MultiBinding>
                                                    </Line.Y2>
                                                </Line>
                                            </Grid>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Border.Resources>
                        <Grid x:Name="PART_Grid" SnapsToDevicePixels="True">
                            <Grid.Resources>
                                <SolidColorBrush x:Key="FindMatchTextBackgroundBrush" Color="{StaticResource WarningForeground.Color}"/>
                                <SolidColorBrush x:Key="FindMatchTextForegroundBrush" Color="{StaticResource ContentForeground.Color}"/>
                                <SolidColorBrush x:Key="SelectionInactiveBackgroundBrush" Color="{StaticResource PrimaryColorLight2.Color}"/>
                            </Grid.Resources>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid SnapsToDevicePixels="True">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="5" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <Border Width="{Binding Path=ActualLineNumberAreaWidth, RelativeSource={RelativeSource TemplatedParent}}" 
                                        Background="{TemplateBinding LineNumberAreaBackground}" 
                                        Grid.Column="0" 
                                        HorizontalAlignment="Stretch" 
                                        VerticalAlignment="Stretch"
                                        Visibility="{Binding Path=ShowLineNumber, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource VisibilityConverter}}" />
                                <Border Width="{Binding Path=OutliningAreaWidth, RelativeSource={RelativeSource TemplatedParent}}" 
                                        Background="{StaticResource ContentBackground}" 
                                        Grid.Column="2" 
                                        HorizontalAlignment="Stretch" 
                                        VerticalAlignment="Stretch"
                                        Visibility="{Binding Path=EnableOutlining, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource VisibilityConverter}}" />
                            </Grid>
                            <ScrollViewer CanContentScroll="True"  
                                          x:Name="PART_Scroll" 
                                          FocusVisualStyle="{x:Null}"  
                                          HorizontalScrollBarVisibility="{TemplateBinding HorizontalScrollBarVisibility}" 
                                          VerticalScrollBarVisibility="{TemplateBinding VerticalScrollBarVisibility}" 
                                          SnapsToDevicePixels="True">
                                <local:EditScrollControl x:Name="PART_ScrollControl" 
                                                         SnapsToDevicePixels="True" />
                            </ScrollViewer>
                            <Popup x:Name="PART_IntellisensePopup" 
                                   Placement="Relative" 
                                   AllowsTransparency="True" 
                                   MaxHeight="{TemplateBinding local:EditControl.IntellisensePopupHeight}" 
                                   Width="{TemplateBinding local:EditControl.IntellisensePopupWidth}">
                                <Border BorderThickness="{StaticResource Windows11Light.BorderThickness2}" 
                                        Background="{StaticResource ContentBackground}" 
                                        BorderBrush="{StaticResource BorderAlt}" 
                                        CornerRadius="0"
                                        Opacity="{Binding ElementName=PART_IntellisenseBox, Path=Opacity}">
                                    <ListBox x:Name="PART_IntellisenseBox" 
                                             ItemContainerStyle="{StaticResource WPFListBoxItemStyle}" 
                                             Style="{TemplateBinding local:EditControl.IntellisenseBoxStyle}" 
                                             ItemTemplate="{TemplateBinding local:EditControl.IntellisenseItemTemplate}">
                                    </ListBox>
                                </Border>
                            </Popup>
                            <Popup x:Name="PART_ContextPopup" Placement="Relative" AllowsTransparency="True" StaysOpen="False" Height="Auto" Width="Auto">
                                <local:ContextPromptControl x:Name="Part_contextPromptControl" Template="{StaticResource SyncfusionContextPromptControlTemplate}"/>
                            </Popup>
                            <StatusBar Grid.Row="2" Name="PART_StatusBar"
                                       HorizontalAlignment="Stretch" 
                                       VerticalAlignment="Stretch" 
                                       BorderThickness="{StaticResource Windows11Light.BorderThickness0100}" 
                                       Visibility="{Binding Path=StatusBarSettings.Visibility, RelativeSource={RelativeSource TemplatedParent}}">
                                <StatusBarItem DockPanel.Dock="Right" 
                                               Visibility="{Binding Path=StatusBarSettings.ShowEncoding, RelativeSource={RelativeSource TemplatedParent}}">
                                    <TextBlock Name="EncodingBlock" 
                                               Margin="5,0,5,0" 
                                               Text="{Binding Path=StatusBarSettings.EncodingName, RelativeSource={RelativeSource TemplatedParent}}" />
                                </StatusBarItem>
                                <Separator  DockPanel.Dock="Right" 
                                            Visibility="{Binding Path=StatusBarSettings.ShowEncoding, RelativeSource={RelativeSource TemplatedParent}}" />
                                <StatusBarItem DockPanel.Dock="Right" 
                                               Visibility="{Binding Path=StatusBarSettings.ShowColumnNumber, RelativeSource={RelativeSource TemplatedParent}}">
                                    <TextBlock Name="ColumnIndexBlock" 
                                               Margin="5,0,5,0"
                                               Text="{Binding Path=StatusBarSettings.ColumnNumber, RelativeSource={RelativeSource TemplatedParent}}" />
                                </StatusBarItem>
                                <Separator  DockPanel.Dock="Right" 
                                            Visibility="{Binding Path=StatusBarSettings.ShowColumnNumber, RelativeSource={RelativeSource TemplatedParent}}" />
                                <StatusBarItem DockPanel.Dock="Right" 
                                               Visibility="{Binding Path=StatusBarSettings.ShowLineNumber, RelativeSource={RelativeSource TemplatedParent}}">
                                    <TextBlock Name="LineNumberBlock" 
                                               Margin="5,0,5,0" 
                                               Text="{Binding Path=StatusBarSettings.LineNumber, RelativeSource={RelativeSource TemplatedParent}}" />
                                </StatusBarItem>
                                <Separator  DockPanel.Dock="Right" 
                                            Visibility="{Binding Path=StatusBarSettings.ShowLineNumber, RelativeSource={RelativeSource TemplatedParent}}"/>
                                <StatusBarItem Visibility="{Binding Path=StatusBarSettings.ShowFilePath, RelativeSource={RelativeSource TemplatedParent}}">
                                    <TextBlock Name="FilePathBlock" 
                                               Text="{Binding Path=StatusBarSettings.FilePath, RelativeSource={RelativeSource TemplatedParent}}"/>
                                </StatusBarItem>
                            </StatusBar>
                            <Grid Grid.Row="1" x:Name="PART_FindAllReferencesGrid" Cursor="Arrow">
                                <Grid.Visibility>
                                    <MultiBinding Converter="{StaticResource TabVisibilityConverter}">
                                        <Binding Path="FindResultsTabVisibility" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="ItemsSource" ElementName="PART_FindAllReferencesList" />
                                        <Binding Path="IsFindResultsTabClosed" RelativeSource="{RelativeSource TemplatedParent}" />
                                    </MultiBinding>
                                </Grid.Visibility>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Border x:Name="PART_FindAllReferencesTitleBar" 
                                        BorderBrush="{StaticResource BorderAlt}" 
                                        BorderThickness="{StaticResource Windows11Light.BorderThickness1}" 
                                        Background="{StaticResource ContentBackground}">
                                    <Grid Margin="2">
                                        <TextBlock Text="{Sync_Resources:EditLocalizationResourceExtension ResourceName=FindSymbolResultText}" 
                                                   Foreground="{StaticResource ContentForeground}" 
                                                   Margin="2" 
                                                   FontSize="{x:Static SystemFonts.MenuFontSize}" 
                                                   FontWeight="Bold" />
                                        <Button Style="{StaticResource SyncfusionEditCloseButtonStyle}" 
                                                HorizontalAlignment="Right" 
                                                x:Name="PART_CloseButton" 
                                                ToolTip="Close" 
                                                ToolTipService.InitialShowDelay="1" 
                                                Margin="0,2,2,2" />
                                        <ToggleButton HorizontalAlignment="Right" 
                                                      x:Name="PART_PinButton" 
                                                      IsChecked ="True"  
                                                      Style="{StaticResource SyncfusionPinnedToggleButtonStyle}" 
                                                      ToolTip="Auto Hide" 
                                                      Margin="0,2,25,2" 
                                                      ToolTipService.InitialShowDelay="1" />
                                    </Grid>
                                </Border>
                                <ListBox x:Name="PART_FindAllReferencesList" 
                                         ScrollViewer.VerticalScrollBarVisibility="Auto"  
                                         Grid.Row="1" 
                                         SelectedIndex="0" 
                                         Background="Transparent" 
                                         BorderBrush="Transparent" 
                                         BorderThickness="{StaticResource Windows11Light.BorderThickness}" 
                                         Height="{Binding FindResultsTabHeight, RelativeSource={RelativeSource TemplatedParent}}">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Converter={StaticResource FindResultConverter}}" />
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>
                            </Grid>

                            <TabControl x:Name="PART_FindAllReferencesTab" 
                                        BorderThickness="{StaticResource Windows11Light.BorderThickness}" 
                                        MaxHeight="{StaticResource Windows11Light.MinHeight}" 
                                        FontSize="{x:Static SystemFonts.MenuFontSize}" 
                                        Grid.Row="2" 
                                        Cursor="Arrow">
                                <TabControl.Visibility>
                                    <MultiBinding Converter="{StaticResource TabVisibilityConverter}">
                                        <Binding Path="FindResultsTabVisibility" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="ItemsSource" ElementName="PART_FindAllReferencesList" />
                                        <Binding Path="IsFindResultsTabClosed" RelativeSource="{RelativeSource TemplatedParent}" />
                                    </MultiBinding>
                                </TabControl.Visibility>
                                <TabItem Header="{Sync_Resources:EditLocalizationResourceExtension ResourceName=FindSymbolResultText}" Name="PART_FindAllReferencesTabItem">
                                </TabItem>
                            </TabControl>
                            <Border x:Name="TouchStart" 
                                    Background="Transparent"
                                    Height="30" 
                                    Width="30" 
                                    Visibility="Collapsed">
                                <Ellipse Fill="{TemplateBinding Background}" 
                                         StrokeThickness="1.5" 
                                         Stroke="{TemplateBinding Foreground}" 
                                         Width="15" 
                                         Height="15" 
                                         HorizontalAlignment="Center" 
                                         VerticalAlignment="Top"/>
                            </Border>
                            <Border x:Name="TouchEnd" 
                                    Background="Transparent" 
                                    Height="30" 
                                    Width="30" 
                                    Visibility="Collapsed">
                                <Ellipse Fill="{TemplateBinding Background}" 
                                         HorizontalAlignment="Center" 
                                         Width="15" 
                                         Height="15" 
                                         VerticalAlignment="Top" 
                                         StrokeThickness="1.5" 
                                         Stroke="{TemplateBinding Foreground}"/>
                            </Border>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger SourceName="PART_PinButton" Property="IsChecked" Value="false">
                            <Setter Property="Style" TargetName="PART_PinButton" Value="{StaticResource SyncfusionUnPinnedToggleButtonStyle}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionEditControlStyle}" TargetType="{x:Type local:EditControl}" />
</ResourceDictionary>
