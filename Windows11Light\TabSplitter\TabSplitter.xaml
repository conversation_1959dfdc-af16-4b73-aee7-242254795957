<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Microsoft_Windows_Luna="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Luna"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:system="clr-namespace:System;assembly=mscorlib"
    
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:tools_controls="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphPrimaryToggleButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style
        x:Key="TabSplitterButtonStyle"
        BasedOn="{StaticResource WPFGlyphPrimaryToggleButtonStyle}"
        TargetType="{x:Type ToggleButton}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border
                        Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="1">
                        
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            RecognizesAccessKey="True"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource HoveredForeground}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ContentBackgroundSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource SelectedForeground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                            <Setter TargetName="border" Property="BorderBrush" Value="Transparent" />
                            <Setter Property="Foreground" Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style x:Key="SyncfusionTabSplitterItemStyle" TargetType="{x:Type tools_controls:TabSplitterItem}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Margin" Value="0" />
        <Setter Property="BorderThickness" Value="0,0,0,2" />
        <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="Control.VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:TabSplitterItem}">
                    <Border
                        Name="TabSplitterItemBorder"
                        Margin="{TemplateBinding Margin}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <DockPanel x:Name="PART_DockPanel" LastChildFill="True">
                            <Image
                                x:Name="PART_Image"
                                VerticalAlignment="Center"
                                DockPanel.Dock="Left"
                                SnapsToDevicePixels="True"
                                Source="{TemplateBinding tools_controls:TabSplitter.Image}"
                                Stretch="None" />
                            <ContentPresenter
                                x:Name="TabSplitterItemContent"
                                Margin="6,4,10,2"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                Content="{TemplateBinding HeaderedContentControl.Header}"
                                ContentSource="Header"
                                ContentTemplate="{TemplateBinding HeaderedContentControl.HeaderTemplate}"
                                RecognizesAccessKey="True"
                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                                TextElement.FontFamily="{TemplateBinding FontFamily}"
                                TextElement.FontSize="{TemplateBinding FontSize}"
                                TextElement.FontWeight="{TemplateBinding FontWeight}"
                                TextElement.Foreground="{TemplateBinding Foreground}">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </DockPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <DataTrigger Binding="{Binding Path=(Panel.ZIndex), RelativeSource={RelativeSource Self}}" Value="9999">
                            <Setter Property="FrameworkElement.Margin" Value="0" />
                        </DataTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource HoveredForeground}" />
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="BorderBrush" Value="Transparent" />
                        </Trigger>

                        <DataTrigger Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBackground}" />
                        </DataTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="BorderBrush" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTabSplitterItemStyle}" TargetType="{x:Type tools_controls:TabSplitterItem}" />

    <Style x:Key="SyncfusionSplitterPageStyle" TargetType="{x:Type tools_controls:SplitterPage}">
        <Setter Property="FrameworkElement.FocusVisualStyle">
            <Setter.Value>
                <Style TargetType="IFrameworkInputElement">
                    <Setter Property="Control.Template" Value="{x:Null}" />
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="Height" Value="26" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt2}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="ToolTip" Value="{Binding Path=Header, RelativeSource={RelativeSource Mode=Self}}" />
        <Setter Property="Margin" Value="0,1,0,0" />
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:SplitterPage}">
                    <tools_controls:VS2008SplitterItemBorder
                        x:Name="SplitterPageBorder"
                        Height="{TemplateBinding Height}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        ToolTip="{TemplateBinding ToolTip}">
                        <DockPanel x:Name="PART_DockPanel" LastChildFill="True">
                            <Image
                                x:Name="PART_Image"
                                Width="11"
                                Height="11"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                DockPanel.Dock="Left"
                                SnapsToDevicePixels="True"
                                Source="{TemplateBinding Image}" />
                            <ContentPresenter
                                x:Name="Content"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Content="{TemplateBinding HeaderedContentControl.Header}"
                                ContentSource="Header"
                                ContentTemplate="{TemplateBinding HeaderedContentControl.HeaderTemplate}"
                                RecognizesAccessKey="True"
                                SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}"
                                TextBlock.FontFamily="{TemplateBinding FontFamily}"
                                TextBlock.FontSize="{TemplateBinding FontSize}"
                                TextBlock.FontWeight="{TemplateBinding FontWeight}"
                                TextBlock.Foreground="{TemplateBinding Foreground}">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </DockPanel>
                    </tools_controls:VS2008SplitterItemBorder>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Top">
                            <Setter Property="Margin" Value="0,2,0,-2" />
                            <Setter TargetName="Content" Property="Margin" Value="4,0,8,0" />
                            <Setter TargetName="PART_DockPanel" Property="VerticalAlignment" Value="Center" />
                        </DataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Top" />
                                <Condition Binding="{Binding Path=IsSelectedPage, RelativeSource={RelativeSource Self}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Margin" Value="0,2,0,-2" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(Panel.ZIndex), RelativeSource={RelativeSource Self}}" Value="9999" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Top" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Margin" Value="-12,1,0,-1" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(Panel.ZIndex), RelativeSource={RelativeSource Self}}" Value="9999" />
                                <Condition Binding="{Binding Path=IsSelectedPage, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Top" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Margin" Value="-12,2,0,-2" />
                        </MultiDataTrigger>

                        <DataTrigger Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Bottom">
                            <Setter TargetName="Content" Property="Margin" Value="4,0,4,0" />
                            <Setter TargetName="PART_Image" Property="Margin" Value="14,0,0,0" />
                            <Setter TargetName="PART_DockPanel" Property="Margin" Value="0,0,0,2" />
                            <Setter TargetName="PART_DockPanel" Property="VerticalAlignment" Value="Center" />
                            <Setter Property="FlowDirection" Value="LeftToRight" />
                            <Setter Property="Margin" Value="0,2,-12,-1" />
                            <Setter TargetName="PART_DockPanel" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="-180" />
                                </Setter.Value>
                            </Setter>
                        </DataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(Panel.ZIndex), RelativeSource={RelativeSource Self}}" Value="9999" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Bottom" />
                                <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=IsSelectedPage, RelativeSource={RelativeSource Self}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Margin" Value="0,1,-13,-1" />
                            <Setter TargetName="PART_Image" Property="Margin" Value="8,0,0,0" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Bottom" />
                                <Condition Binding="{Binding Path=BottomPanelHeight, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}" Value="0" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Image" Property="Margin" Value="14,0,0,0" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(Panel.ZIndex), RelativeSource={RelativeSource Self}}" Value="10000" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Bottom" />
                                <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=IsSelectedPage, RelativeSource={RelativeSource Self}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Image" Property="Margin" Value="8,0,0,0" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(Panel.ZIndex), RelativeSource={RelativeSource Self}}" Value="9999" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Bottom" />
                                <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Image" Property="Margin" Value="8,0,0,0" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(Panel.ZIndex), RelativeSource={RelativeSource Self}}" Value="9999" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Bottom" />
                                <Condition Binding="{Binding Path=BottomPanelHeight, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}" Value="0" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Image" Property="Margin" Value="8,0,0,0" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=IsSelectedPage, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Bottom" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Margin" Value="-1,2,-13,-2" />
                            <Setter TargetName="PART_Image" Property="Margin" Value="8,0,0,0" />
                        </MultiDataTrigger>

                        <Trigger Property="Image" Value="{x:Null}">
                            <Setter TargetName="PART_Image" Property="Visibility" Value="Collapsed" />
                        </Trigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=Image, RelativeSource={RelativeSource Self}}" Value="{x:Null}" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Bottom" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="Content" Property="Margin" Value="14,0,4,0" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(Panel.ZIndex), RelativeSource={RelativeSource Self}}" Value="9999" />
                                <Condition Binding="{Binding Path=Image, RelativeSource={RelativeSource Self}}" Value="{x:Null}" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Bottom" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="Content" Property="Margin" Value="8,0,4,0" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(Panel.ZIndex), RelativeSource={RelativeSource Self}}" Value="10000" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Bottom" />
                                <Condition Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=IsSelectedPage, RelativeSource={RelativeSource Self}}" Value="False" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="Content" Property="Margin" Value="8,0,4,0" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=IsSelectedPage, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=Image, RelativeSource={RelativeSource Self}}" Value="{x:Null}" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Bottom" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="Content" Property="Margin" Value="8,0,4,0" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Right" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Image" Property="Margin" Value="4,0,0,5" />
                            <Setter TargetName="Content" Property="Content" Value=" " />
                            <Setter Property="Margin" Value="0,1,-12,-1" />
                            <Setter Property="Width" Value="48.5" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=IsSelectedPage, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Right" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="Content" Property="Content" Value=" " />
                            <Setter Property="Margin" Value="0,0,-12,0" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Left" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Image" Property="Margin" Value="4,0,0,5" />
                            <Setter Property="Margin" Value="0,1,-13,0" />
                            <Setter TargetName="Content" Property="Content" Value=" " />
                            <Setter Property="Width" Value="48.5" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=IsSelectedPage, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Left" />
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Margin" Value="0,0,-14,1" />
                            <Setter TargetName="Content" Property="Content" Value=" " />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Left" />
                                <Condition Binding="{Binding Path=RightPanelWidth, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}" Value="0" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="Content" Property="Content" Value=" " />
                            <Setter Property="Margin" Value="0,1,-13,0" />
                        </MultiDataTrigger>

                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=IsSelectedPage, RelativeSource={RelativeSource Self}}" Value="True" />
                                <Condition Binding="{Binding Path=(tools_controls:SplitterPage.TabStripPlacement), RelativeSource={RelativeSource Self}}" Value="Left" />
                                <Condition Binding="{Binding Path=RightPanelWidth, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}" Value="0" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="Content" Property="Content" Value=" " />
                            <Setter Property="Margin" Value="0,-1,-13,1" />
                        </MultiDataTrigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelectedPage" Value="False" />
                                <Condition Property="IsMouseOver" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter Property="Foreground" Value="{Binding Path=MouseOverForeground, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}" />
                            <Setter Property="Background" Value="{Binding Path=MouseOverBackground, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}" />
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </MultiTrigger>

                        <DataTrigger Binding="{Binding Path=IsSelectedPage, RelativeSource={RelativeSource Self}}" Value="True">
                            <Setter Property="Foreground" Value="{Binding Path=SelectedForeground, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}" />
                            <Setter Property="Background" Value="{Binding Path=SelectedBackground, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}" />
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </DataTrigger>

                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="BorderBrush" Value="Transparent" />
                        </Trigger>

                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSplitterPageStyle}" TargetType="{x:Type tools_controls:SplitterPage}" />

    <Style x:Key="SyncfusionTabPanelAdvStyle" TargetType="{x:Type tools_controls:TabPanelAdv}">
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Background" Value="{Binding Background, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt4}" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness0001}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:TabPanelAdv}">
                    <Border
                        x:Name="TabPanelBorder"
                        Padding="{TemplateBinding Padding}"
                        HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                        VerticalAlignment="{TemplateBinding VerticalAlignment}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                        <DockPanel x:Name="TabPanel" LastChildFill="True">
                            <Button
                                Name="PART_CloseButton"
                                Width="16"
                                Height="16"
                                Margin="0,0,8,0"
                                AllowDrop="False"
                                Command="tools_controls:TabSplitterCommands.CloseCurrentTabSplitterItem"
                                DockPanel.Dock="Right"
                                Focusable="False"
                                Style="{StaticResource WPFGlyphButtonStyle}"
                                Visibility="Visible">
                                <Path
                                    x:Name="CloseButtonPath"
                                    Width="8"
                                    Height="8"
                                    Margin="0.5,0,0,0"
                                    Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                    Stretch="Fill" >
                                    <Path.Data>
                                        <PathGeometry>M0.70800017,0 L4.498001,3.7964015 8.2880024,0 8.9960006,0.70600033 5.2044057,4.5039992 8.9960006,8.3019981 8.2880024,9.0079994 4.498001,5.2115974 0.70800017,9.0079994 0,8.3019981 3.7915958,4.5039992 0,0.70600033 z</PathGeometry>
                                    </Path.Data>
                                </Path>
                            </Button>
                            <Button
                                Name="PART_MenuButton"
                                Width="16"
                                Height="16"
                                Margin="0,0,8,0"
                                AllowDrop="False"
                                Command="tools_controls:TabSplitterCommands.OpenContextMenu"
                                DockPanel.Dock="Right"
                                Focusable="False"
                                Style="{StaticResource WPFGlyphButtonStyle}"
                                Visibility="Visible">
                                <Path
                                    Name="MenuButtonPath"
                                    Width="8"
                                    Height="4"
                                    Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                    SnapsToDevicePixels="True"
                                    Stretch="Fill" >
                                    <Path.Data>
                                        <PathGeometry>M0.74499548,0 L5.0119957,4.7700001 9.2630047,0.017000169 10.008001,0.68400005 5.0119957,6.2700001 0,0.66699985 z</PathGeometry>
                                    </Path.Data>
                                </Path>
                            </Button>
                            <ContentPresenter
                                x:Name="PART_TabItems"
                                Content="{TemplateBinding Content}"
                                ContentSource="TabItems"
                                DockPanel.Dock="Right" />
                        </DockPanel>
                    </Border>
                </ControlTemplate>

            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionTabSplitterStyle" TargetType="{x:Type tools_controls:TabSplitter}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="SelectedBackground" Value="{StaticResource ContentBackground}" />
        <Setter Property="SelectedForeground" Value="{StaticResource PrimaryColorForeground}" />
        <Setter Property="MouseOverBackground" Value="{StaticResource ContentBackgroundHovered}" />
        <Setter Property="MouseOverForeground" Value="{StaticResource HoveredForeground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt4}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Control.Padding" Value="4" />
        <Setter Property="AllowDrop" Value="False" />
        <Setter Property="MinHeight" Value="150" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type tools_controls:TabSplitter}">
                    <Border
                        x:Name="TabSplitterBorder"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="8">
                        <Grid
                            x:Name="TabSplitterGrid"
                            Background="{TemplateBinding Background}"
                            ClipToBounds="False"
                            KeyboardNavigation.TabNavigation="Local"
                            SnapsToDevicePixels="True">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Name="ColumnDefinition0" />
                                <ColumnDefinition Name="ColumnDefinition1" Width="0" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Name="RowDefinition0" Height="Auto" />
                                <RowDefinition Name="RowDefinition1" Height="*" />
                            </Grid.RowDefinitions>
                            <tools_controls:LayoutPanel
                                x:Name="HeaderPanel"
                                Grid.Row="0"
                                Grid.Column="0"
                                Height="24"
                                HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                Panel.ZIndex="1"
                                Focusable="False">
                                <tools_controls:TabPanelAdv
                                    x:Name="PART_TabPanel"
                                    Height="24"
                                    HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                    VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                    DockPanel.Dock="Right"
                                    Focusable="False"
                                    Style="{StaticResource SyncfusionTabPanelAdvStyle}">
                                    <tools_controls:TabSplitterItemPanel
                                        x:Name="PART_TabLayoutPanel"
                                        Margin="0,0,2,0"
                                        HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                        AllowDrop="False"
                                        IsItemsHost="True"
                                        KeyboardNavigation.TabIndex="1" />
                                </tools_controls:TabPanelAdv>
                            </tools_controls:LayoutPanel>
                            <shared:Border3D
                                Name="ContentPanel"
                                Grid.Row="1"
                                Grid.Column="0"
                                AllowDrop="False"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                CornerRadius="2"
                                KeyboardNavigation.DirectionalNavigation="Contained"
                                KeyboardNavigation.TabIndex="2"
                                KeyboardNavigation.TabNavigation="Local">
                                <Border
                                    x:Name="PART_ContentPanelInnerBorder"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    CornerRadius="2">
                                    <Grid Name="ResizedContentGrid" Background="{TemplateBinding Background}">

                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <tools_controls:LayoutPanel
                                            x:Name="PART_TopPanel"
                                            
                                            Grid.Row="0"
                                            Grid.Column="1"
                                            Background="{TemplateBinding Background}">
                                            <ScrollContentPresenter
                                                x:Name="PART_TopScrollContent"
                                                Margin="{Binding Path=Padding, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}"
                                                Content="{TemplateBinding TopSelectedContent}"
                                                ContentSource="TopSelectedContent"
                                                TextBlock.FontFamily="{TemplateBinding FontFamily}"
                                                TextBlock.FontSize="{TemplateBinding FontSize}"
                                                TextBlock.FontWeight="{TemplateBinding FontWeight}"
                                                TextBlock.Foreground="{StaticResource ContentForeground}" />
                                        </tools_controls:LayoutPanel>
                                        <tools_controls:CustomGridSplitter
                                            x:Name="PART_Splitter"
                                            Grid.Row="1"
                                            Grid.Column="1"
                                            Panel.ZIndex="1"
                                            Background="{StaticResource ContentBackgroundAlt1}"
                                            BorderBrush="{StaticResource BorderAlt}"
                                            ResizeDirection="Rows"
                                            ShowsPreview="True">
                                            <tools_controls:CustomGridSplitter.Style>
                                                <Style TargetType="{x:Type tools_controls:CustomGridSplitter}">
                                                    <Setter Property="MinHeight" Value="30" />
                                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                                    <Setter Property="VerticalAlignment" Value="Center" />
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="{x:Type tools_controls:CustomGridSplitter}">
                                                                <tools_controls:LayoutPanel x:Name="PART_SplitHeaderPanel" Panel.ZIndex="1">
                                                                    <Border
                                                                        x:Name="PART_SplitBorder"
                                                                        Background="{TemplateBinding Background}"
                                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                                        BorderThickness="0,1,0,1">
                                                                        <tools_controls:SplitHeaderPanel>
                                                                            <ItemsControl x:Name="PART_TopPages" DockPanel.Dock="Left">
                                                                                <ItemsControl.ItemsPanel>
                                                                                    <ItemsPanelTemplate>
                                                                                        <tools_controls:PagesLayoutPanel>
                                                                                            <tools_controls:PagesLayoutPanel.LayoutTransform>
                                                                                                <RotateTransform Angle="-180" />
                                                                                            </tools_controls:PagesLayoutPanel.LayoutTransform>
                                                                                        </tools_controls:PagesLayoutPanel>
                                                                                    </ItemsPanelTemplate>
                                                                                </ItemsControl.ItemsPanel>
                                                                            </ItemsControl>
                                                                            <Button
                                                                                x:Name="PART_SplitButton"
                                                                                Width="18"
                                                                                Height="18"
                                                                                DockPanel.Dock="Left"
                                                                                Focusable="False"
                                                                                Style="{StaticResource WPFGlyphButtonStyle}"
                                                                                ToolTip="Swap Panes">
                                                                                <Path
                                                                                    x:Name="SwapButtonPath"
                                                                                    Width="14"
                                                                                    Height="14"
                                                                                    HorizontalAlignment="Center"
                                                                                    VerticalAlignment="Center"
                                                                                    Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                                                                    Stretch="Fill" >
                                                                                    <Path.Data>
                                                                                        <PathGeometry>M3.75 1.77734V9.125C3.75 9.22656 3.71289 9.31445 3.63867 9.38867C3.56445 9.46289 3.47656 9.5 3.375 9.5C3.27344 9.5 3.18555 9.46289 3.11133 9.38867C3.03711 9.31445 3 9.22656 3 9.125V1.77734L0.638672 4.13867C0.564453 4.21289 0.476562 4.25 0.375 4.25C0.273438 4.25 0.185547 4.21289 0.111328 4.13867C0.0371094 4.06445 0 3.97656 0 3.875C0 3.77344 0.0371094 3.68555 0.111328 3.61133L3.09375 0.628906C3.13672 0.585938 3.17969 0.554687 3.22266 0.535156C3.26562 0.511719 3.31641 0.5 3.375 0.5C3.43359 0.5 3.48438 0.511719 3.52734 0.535156C3.57031 0.554687 3.61328 0.585938 3.65625 0.628906L6.63867 3.61133C6.71289 3.68555 6.75 3.77344 6.75 3.875C6.75 3.97656 6.71289 4.06445 6.63867 4.13867C6.56445 4.21289 6.47656 4.25 6.375 4.25C6.27344 4.25 6.18555 4.21289 6.11133 4.13867L3.75 1.77734ZM12 6.125C12 6.22656 11.9629 6.31445 11.8887 6.38867L8.90625 9.37109C8.86328 9.41406 8.82031 9.44727 8.77734 9.4707C8.73828 9.49023 8.6875 9.5 8.625 9.5C8.5625 9.5 8.50977 9.49023 8.4668 9.4707C8.42773 9.44727 8.38672 9.41406 8.34375 9.37109L5.36133 6.38867C5.28711 6.31445 5.25 6.22656 5.25 6.125C5.25 6.02344 5.28711 5.93555 5.36133 5.86133C5.43555 5.78711 5.52344 5.75 5.625 5.75C5.72656 5.75 5.81445 5.78711 5.88867 5.86133L8.25 8.2168V0.875C8.25 0.773438 8.28711 0.685547 8.36133 0.611328C8.43555 0.537109 8.52344 0.5 8.625 0.5C8.72656 0.5 8.81445 0.537109 8.88867 0.611328C8.96289 0.685547 9 0.773438 9 0.875V8.2168L11.3613 5.86133C11.4355 5.78711 11.5234 5.75 11.625 5.75C11.7266 5.75 11.8145 5.78711 11.8887 5.86133C11.9629 5.93555 12 6.02344 12 6.125Z</PathGeometry>
                                                                                    </Path.Data>
                                                                                </Path>
                                                                            </Button>
                                                                            <ItemsControl x:Name="PART_BottomPages" DockPanel.Dock="Left">
                                                                                <ItemsControl.ItemsPanel>
                                                                                    <ItemsPanelTemplate>
                                                                                        <tools_controls:PagesLayoutPanel />
                                                                                    </ItemsPanelTemplate>
                                                                                </ItemsControl.ItemsPanel>
                                                                            </ItemsControl>
                                                                            <DockPanel
                                                                                x:Name="PART_SplitterMunuPanel"
                                                                                Height="{TemplateBinding Height}"
                                                                                HorizontalAlignment="Right"
                                                                                DockPanel.Dock="Right">
                                                                                <Button
                                                                                    x:Name="PART_VerticalButton"
                                                                                    Width="16"
                                                                                    Height="16"
                                                                                    MinHeight="16"
                                                                                    Margin="0,0,8,0"
                                                                                    Focusable="False"
                                                                                    SnapsToDevicePixels="True"
                                                                                    Style="{StaticResource WPFGlyphButtonStyle}"
                                                                                    ToolTip="Vertical Split">
                                                                                    <Path
                                                                                        x:Name="VerticalButtonPath"
                                                                                        Width="10"
                                                                                        Height="10"
                                                                                        HorizontalAlignment="Center"
                                                                                        VerticalAlignment="Center"
                                                                                        Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                                                                        Stretch="Fill" >
                                                                                        <Path.Data>
                                                                                            <PathGeometry>M7,4 L8,4 8,10 7,10 z M1,1 L1,14 14,14 14,1 z M0,0 L15,0 15,15 0,15 z</PathGeometry>
                                                                                        </Path.Data>
                                                                                    </Path>
                                                                                </Button>
                                                                                <Button
                                                                                    x:Name="PART_HorizontalButton"
                                                                                    Width="16"
                                                                                    Height="16"
                                                                                    MinHeight="16"
                                                                                    Margin="0,0,8,0"
                                                                                    Focusable="False"
                                                                                    SnapsToDevicePixels="True"
                                                                                    Style="{StaticResource WPFGlyphButtonStyle}"
                                                                                    ToolTip="Horizontal Split">
                                                                                    <Path
                                                                                        x:Name="HorizontalButtonPath"
                                                                                        Width="10"
                                                                                        Height="10"
                                                                                        HorizontalAlignment="Center"
                                                                                        VerticalAlignment="Center"
                                                                                        Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                                                                        Stretch="Fill" >
                                                                                        <Path.Data>
                                                                                            <PathGeometry>M4.9999999,7 L10,7 10,8 4.9999999,8 z M1,1 L1,14 14,14 14,1 z M0,0 L15,0 15,15 0,15 z</PathGeometry>
                                                                                        </Path.Data>
                                                                                    </Path>
                                                                                </Button>
                                                                                <ToggleButton
                                                                                    x:Name="PART_Expand_ColapseButtons"
                                                                                    Width="16"
                                                                                    Height="16"
                                                                                    Margin="0,0,8,0"
                                                                                    Background="Transparent"
                                                                                    BorderBrush="Transparent"
                                                                                    Command="tools_controls:TabSplitterCommands.CollapseBottomSelectedItem"
                                                                                    Focusable="False"
                                                                                    SnapsToDevicePixels="True"
                                                                                    Style="{StaticResource TabSplitterButtonStyle}"
                                                                                    ToolTip="Collapse Pane">
                                                                                    <Path
                                                                                        x:Name="ExpandCollpaseButtonPath"
                                                                                        Width="8"
                                                                                        Height="7"
                                                                                        HorizontalAlignment="Center"
                                                                                        VerticalAlignment="Center"
                                                                                        Stroke="{Binding Foreground, ElementName=PART_Expand_ColapseButtons}"
                                                                                        
                                                                                        Stretch="Fill" >
                                                                                        <Path.Data>
                                                                                            <PathGeometry>M0.51822312,8.0003228 C0.64597274,8.0049481 0.77197249,8.0584444 0.86597217,8.1594386 L7.010959,14.766036 13.133946,8.1834368 C13.322945,7.9814495 13.638944,7.9704499 13.840944,8.1574387 14.042943,8.3464274 14.053944,8.6624083 13.865944,8.8643956 L7.3769583,15.84097 C7.2819585,15.941964 7.1499585,15.99996 7.010959,15.99996 6.8719591,15.99996 6.7399596,15.941964 6.6449598,15.84097 L0.13397388,8.8413968 C-0.054025713,8.6394095 -0.043025735,8.3224282 0.15997384,8.1344399 0.26097361,8.039946 0.39047332,7.9956984 0.51822312,8.0003228 z M0.51819904,0.00032785836 C0.64594544,0.004951029 0.77194465,0.058445077 0.86595381,0.15945207 L7.0109295,6.7660436 13.133932,0.1834374 C13.322927,-0.019553091 13.638905,-0.029562246 13.840931,0.15743816 14.042927,0.34645239 14.053913,0.66241211 13.865925,0.8644262 L7.3769258,7.84099 C7.2819246,7.941936 7.1499366,7.9999766 7.0109295,7.9999766 6.8719219,7.9999766 6.7399339,7.941936 6.6449326,7.84099 L0.13396077,0.84141744 C-0.054057553,0.63940329 -0.043040733,0.32240603 0.15996168,0.13442916 0.26095946,0.03995255 0.39045287,-0.0042952346 0.51819904,0.00032785836 z</PathGeometry>
                                                                                        </Path.Data>
                                                                                    </Path>
                                                                                </ToggleButton>
                                                                            </DockPanel>
                                                                            <Border
                                                                                Name="PART_HorizContainerBorder"
                                                                                HorizontalAlignment="Center"
                                                                                VerticalAlignment="Center"
                                                                                SnapsToDevicePixels="True">
                                                                                <Border
                                                                                    Name="ResizedOptionLine"
                                                                                    Width="16"
                                                                                    Height="4"
                                                                                    HorizontalAlignment="Center"
                                                                                    VerticalAlignment="Center"
                                                                                    Background="Transparent"
                                                                                    BorderBrush="{StaticResource IconColorDisabled}"
                                                                                    BorderThickness="0,1,0,1" />
                                                                            </Border>
                                                                        </tools_controls:SplitHeaderPanel>
                                                                    </Border>
                                                                    <Canvas
                                                                        x:Name="DragPreviewLine"
                                                                        Height="3"
                                                                        Visibility="Collapsed">
                                                                        <Line
                                                                            x:Name="Line1"
                                                                            HorizontalAlignment="Stretch"
                                                                            Fill="{StaticResource PrimaryBackground}"
                                                                            Stroke="{StaticResource PrimaryBackground}"
                                                                            StrokeDashArray="1,1"
                                                                            StrokeThickness="1"
                                                                            X2="{TemplateBinding ActualWidth}" />
                                                                        <Line
                                                                            x:Name="Line2"
                                                                            HorizontalAlignment="Stretch"
                                                                            Fill="{StaticResource PrimaryBackground}"
                                                                            Stroke="{StaticResource PrimaryBackground}"
                                                                            StrokeDashArray="1,1"
                                                                            StrokeThickness="1"
                                                                            X1="1"
                                                                            X2="{TemplateBinding ActualWidth}"
                                                                            Y1="1"
                                                                            Y2="1" />
                                                                        <Line
                                                                            x:Name="Line3"
                                                                            HorizontalAlignment="Stretch"
                                                                            Fill="{StaticResource PrimaryBackground}"
                                                                            Stroke="{StaticResource PrimaryBackground}"
                                                                            StrokeDashArray="1,1"
                                                                            StrokeThickness="1"
                                                                            X2="{TemplateBinding ActualWidth}"
                                                                            Y1="2"
                                                                            Y2="2" />
                                                                    </Canvas>
                                                                </tools_controls:LayoutPanel>
                                                                <ControlTemplate.Triggers>

                                                                    <Trigger Property="ResizeDirection" Value="Columns">
                                                                        <Setter Property="MinWidth" Value="30" />
                                                                        <Setter TargetName="PART_BottomPages" Property="Margin" Value="0" />
                                                                        <Setter TargetName="PART_SplitterMunuPanel" Property="Height" Value="{Binding Path=ActualWidth, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:CustomGridSplitter}}}" />
                                                                        <Setter TargetName="PART_SplitHeaderPanel" Property="LayoutTransform">
                                                                            <Setter.Value>
                                                                                <RotateTransform Angle="90" />
                                                                            </Setter.Value>
                                                                        </Setter>
                                                                        <Setter TargetName="Line1" Property="X2" Value="{Binding Path=ActualHeight, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:CustomGridSplitter}}}" />
                                                                        <Setter TargetName="Line2" Property="X2" Value="{Binding Path=ActualHeight, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:CustomGridSplitter}}}" />
                                                                        <Setter TargetName="Line3" Property="X2" Value="{Binding Path=ActualHeight, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type tools_controls:CustomGridSplitter}}}" />
                                                                        <Setter TargetName="PART_VerticalButton" Property="LayoutTransform">
                                                                            <Setter.Value>
                                                                                <RotateTransform Angle="90" />
                                                                            </Setter.Value>
                                                                        </Setter>
                                                                        <Setter TargetName="PART_HorizontalButton" Property="LayoutTransform">
                                                                            <Setter.Value>
                                                                                <RotateTransform Angle="90" />
                                                                            </Setter.Value>
                                                                        </Setter>
                                                                        <Setter TargetName="PART_VerticalButton" Property="Background" Value="Transparent" />
                                                                        <Setter TargetName="PART_VerticalButton" Property="BorderBrush" Value="Transparent" />
                                                                        <Setter TargetName="PART_HorizontalButton" Property="Background" Value="Transparent" />
                                                                        <Setter TargetName="PART_HorizontalButton" Property="BorderBrush" Value="Transparent" />
                                                                    </Trigger>
                                                                    <Trigger Property="ResizeDirection" Value="Rows">
                                                                        <Setter TargetName="PART_BottomPages" Property="Margin" Value="-6,0,0,0" />
                                                                        <Setter TargetName="PART_HorizontalButton" Property="Background" Value="Transparent" />
                                                                        <Setter TargetName="PART_HorizontalButton" Property="BorderBrush" Value="Transparent" />
                                                                        <Setter TargetName="PART_VerticalButton" Property="Background" Value="Transparent" />
                                                                        <Setter TargetName="PART_VerticalButton" Property="BorderBrush" Value="Transparent" />
                                                                    </Trigger>
                                                                    <DataTrigger Binding="{Binding BottomPanelHeight, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}" Value="0">
                                                                        <Setter TargetName="PART_HorizontalButton" Property="Background" Value="Transparent" />
                                                                        <Setter TargetName="PART_HorizontalButton" Property="BorderBrush" Value="Transparent" />
                                                                    </DataTrigger>
                                                                    <DataTrigger Binding="{Binding RightPanelWidth, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}" Value="0">
                                                                        <Setter TargetName="PART_VerticalButton" Property="Background" Value="Transparent" />
                                                                        <Setter TargetName="PART_VerticalButton" Property="BorderBrush" Value="Transparent" />
                                                                    </DataTrigger>
                                                                    <Trigger Property="IsEnabled" Value="false">
                                                                        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}" />
                                                                        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                                                                    </Trigger>
                                                                </ControlTemplate.Triggers>
                                                            </ControlTemplate>

                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </tools_controls:CustomGridSplitter.Style>
                                        </tools_controls:CustomGridSplitter>
                                        <tools_controls:LayoutPanel
                                            x:Name="PART_BottomPanel"
                                            Grid.Row="2"
                                            Grid.Column="1"
                                            Background="{Binding Background, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}">
                                            <ScrollContentPresenter
                                                x:Name="PART_BottomScrollContent"
                                                Margin="{Binding Path=Padding, RelativeSource={RelativeSource AncestorType={x:Type tools_controls:TabSplitter}}}"
                                                Content="{TemplateBinding BottomSelectedContent}"
                                                ContentSource="BottomSelectedContent"
                                                TextBlock.FontFamily="{TemplateBinding FontFamily}"
                                                TextBlock.FontSize="{TemplateBinding FontSize}"
                                                TextBlock.FontWeight="{TemplateBinding FontWeight}"
                                                TextBlock.Foreground="{StaticResource ContentForeground}" />
                                        </tools_controls:LayoutPanel>
                                    </Grid>
                                </Border>
                            </shared:Border3D>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" TargetName="PART_TabLayoutPanel" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="MinHeight" TargetName="HeaderPanel" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter Property="MinHeight" TargetName="PART_TabPanel" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="UIElement.IsEnabled" Value="False">
                            <Setter Property="TextElement.Foreground" Value="{StaticResource DisabledForeground}" />
                        </Trigger>
                        <DataTrigger Binding="{Binding Path=SelectedItem.(tools_controls:TabSplitterItem.Orientation), RelativeSource={RelativeSource Self}}" Value="Vertical">

                            <Setter TargetName="PART_TopPanel" Property="Grid.Row" Value="1" />
                            <Setter TargetName="PART_TopPanel" Property="Grid.Column" Value="0" />

                            <Setter TargetName="PART_Splitter" Property="Grid.Row" Value="1" />
                            <Setter TargetName="PART_Splitter" Property="Grid.Column" Value="1" />

                            <Setter TargetName="PART_BottomPanel" Property="Grid.Row" Value="1" />
                            <Setter TargetName="PART_BottomPanel" Property="Grid.Column" Value="2" />

                            <Setter TargetName="PART_Splitter" Property="ResizeDirection" Value="Columns" />
                            <Setter TargetName="PART_Splitter" Property="Grid.ColumnSpan" Value="1" />
                        </DataTrigger>
                        <Trigger Property="HasItems" Value="False">
                            <Setter TargetName="PART_Splitter" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundDisabled}" />
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource FlatKeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionTabSplitterStyle}" TargetType="{x:Type tools_controls:TabSplitter}" />

</ResourceDictionary>
