<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Input="clr-namespace:Syncfusion.Windows.Controls.Input;assembly=Syncfusion.SfInput.WPF"
    xmlns:Microsoft_Windows_Themes="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero"
    xmlns:Sync_Resource="clr-namespace:Syncfusion.Windows.Controls.Input.Resources;assembly=Syncfusion.SfInput.WPF"
    xmlns:input_controls="clr-namespace:Syncfusion.Windows.Controls;assembly=Syncfusion.SfInput.WPF"
    xmlns:converter="clr-namespace:Syncfusion.Windows.Converters;assembly=Syncfusion.SfInput.WPF"
    xmlns:shared_Brushconverter="clr-namespace:Syncfusion.Windows.Converters;assembly=Syncfusion.Shared.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:Syncfusion.Windows.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Primitives;assembly=Syncfusion.SfInput.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/FlatPrimaryButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphButton.xaml" />
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <BooleanToVisibilityConverter x:Key="BooleanVisibilityConverter" />

    <converter:InverseBooleanToVisibilityConverter x:Key="InverseBooleanVisibilityConverter" />

    <shared_Brushconverter:BrushToColorConverter x:Key="BrushConverter" />

    <Sync_Resource:ResourceWrapper x:Key="ResourceWrapperKey" />

    <Style x:Key="SyncfusionSfDateSelectorTransitionContentControlStyle" TargetType="local:TransitionContentControl">
        <Setter Property="Height" Value="50" />
        <Setter Property="Margin" Value="10,0,0,0" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Transition">
            <Setter.Value>
                <local:FadeTransition />
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfDateSelectorLoopingSelectorItemStyle" TargetType="input_controls:LoopingSelectorItem">
        <Setter Property="AlternativeBackground" Value="Transparent" />
        <Setter Property="AlternativeBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="AccentBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="KeyboardNavigation.DirectionalNavigation" Value="None" />
        <Setter Property="Margin" Value="0"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type input_controls:LoopingSelectorItem}">
                    <Border
                        x:Name="root" 
                        CornerRadius="{StaticResource Windows11Light.CornerRadius4}" 
                        SnapsToDevicePixels="True"
                        BorderThickness="{TemplateBinding BorderThickness}" 
                        BorderBrush="{TemplateBinding BorderBrush}"
                        Opacity="1">
                        <Border.RenderTransform>
                            <TranslateTransform x:Name="Transform" />
                        </Border.RenderTransform>
                        <Grid x:Name="LoopingSelectorGrid">
                            <Border 
                                SnapsToDevicePixels="True"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}" 
                                Background="Transparent"
                                Opacity="0" HorizontalAlignment="Stretch" VerticalAlignment="Stretch"
                                x:Name="Accent_Border"/>
                            <Border 
                                SnapsToDevicePixels="True"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}" 
                                Background="{TemplateBinding Background}"
                                HorizontalAlignment="Stretch" VerticalAlignment="Stretch"
                                x:Name="Part_Border"/>
                            <ContentControl
                                x:Name="contentPresenter"
                                Content="{Binding}"
                                Background="Transparent"
                                ContentTemplate="{Binding ItemTemplate, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                ContentTemplateSelector="{Binding ItemTemplateSelector, RelativeSource={RelativeSource Mode=TemplatedParent}}" />
                        </Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="Expanded">
                                    <Storyboard>
                                        <DoubleAnimation
                                            Storyboard.TargetName="root"
                                            Storyboard.TargetProperty="Opacity"
                                            To="1"
                                            Duration="0" />
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Selected">                                    
                                    <Storyboard>
                                        <DoubleAnimation
                                            Storyboard.TargetName="root"
                                            Storyboard.TargetProperty="Opacity"
                                            To="1"
                                            Duration="0" />
                                        <DoubleAnimation
                                            Storyboard.TargetName="Accent_Border"
                                            Storyboard.TargetProperty="Opacity"
                                            To="1"
                                            Duration="0" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PopupHoveredBackground}" />
                            <Setter Property="BorderBrush" Value="{StaticResource PopupHoveredBackground}" />
                            <Setter Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource LoopingSelectorItem.Pressed.Foreground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="BorderBrush" Value="{StaticResource PopupDisabledBackground}"/>
                            <Setter Property="Background" Value="{StaticResource PopupDisabledBackground}" />
                            <Setter Property="Foreground" Value="{StaticResource PopupDisabledForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSfDateSelectorLoopingSelectorStyle" TargetType="input_controls:LoopingSelector">
        <Setter Property="ItemHeight" Value="80" />
        <Setter Property="ItemWidth" Value="80" />
        <Setter Property="Width" Value="80" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="IsManipulationEnabled" Value="True" />
        <Setter Property="IsHitTestVisible" Value="True" />
        <!--<Setter Property="ManipulationMode" Value="All"/>-->
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="ItemContainerStyle" Value="{StaticResource SyncfusionSfDateSelectorLoopingSelectorItemStyle}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border x:Name="LoopingSelectorBorder" Background="{TemplateBinding Background}">
                        <Canvas x:Name="ItemsPanel">
                            <Canvas.RenderTransform>
                                <TransformGroup>
                                    <TranslateTransform x:Name="CenteringTransform" />
                                    <TranslateTransform x:Name="PanningTransform" />
                                </TransformGroup>
                            </Canvas.RenderTransform>
                        </Canvas>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <DataTemplate x:Key="DefaultDayCellTemplate">
        <TextBlock
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            FontSize="12"
            Style="{x:Null}"
            Text="{Binding DayNumber}" />
    </DataTemplate>

    <DataTemplate x:Key="DefaultMonthCellTemplate">
        <TextBlock
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            FontSize="12"
            Style="{x:Null}"
            Text="{Binding MonthNumber}" />
    </DataTemplate>

    <DataTemplate x:Key="DefaultYearCellTemplate">
        <TextBlock
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            FontSize="12"
            Style="{x:Null}"
            Text="{Binding YearNumber}" />
    </DataTemplate>

    <Style x:Key="SyncfusionSfDateSelectorStyle" TargetType="{x:Type Input:SfDateSelector}">
        <Setter Property="AccentBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="SelectedForeground" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="BorderBrush" Value="{StaticResource Border}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness2}" />
        <Setter Property="IsTabStop" Value="True" />
        <Setter Property="DayCellTemplate" Value="{StaticResource DefaultDayCellTemplate}" />
        <Setter Property="HeaderStyle" Value="{StaticResource SyncfusionSfDateSelectorTransitionContentControlStyle}" />
        <Setter Property="Height" Value="300" />
        <Setter Property="MonthCellTemplate" Value="{StaticResource DefaultMonthCellTemplate}" />
        <Setter Property="ShowDoneButton" Value="True" />
        <Setter Property="ShowCancelButton" Value="True" />
        <Setter Property="SelectorStyle" Value="{StaticResource SyncfusionSfDateSelectorLoopingSelectorStyle}" />
        <Setter Property="YearCellTemplate" Value="{StaticResource DefaultYearCellTemplate}" />
        <Setter Property="SelectorItemHeight" Value="40" />
        <Setter Property="SelectorItemWidth" Value="40" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Input:SfDateSelector">
                    <Border
                        x:Name="DateSelectorBorder" 
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        CornerRadius="{StaticResource Windows11Light.CornerRadius4}" 
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Background="{TemplateBinding Background}"
                        SnapsToDevicePixels="true">

                        <Grid x:Name="PART_Root" Background="Transparent">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid
                                x:Name="HeaderGrid"
                                Grid.Row="0"
                                VerticalAlignment="Top"
                                Background="Transparent">
                                <local:TransitionContentControl
                                    x:Name="ContentControl"
                                    Content="{Binding Header, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                    ContentTemplate="{Binding HeaderTemplate, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                    IsTabStop="False"
                                    Style="{TemplateBinding HeaderStyle}" />
                            </Grid>
                            <Border
                                x:Name="SelectorBorder"
                                Grid.Row="1"
                                HorizontalAlignment="Center"
                                Background="Transparent">
                                <Grid x:Name="SelectorGrid">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <Border 
                                        x:Name="SelectionHighlighter"
                                        Height="{TemplateBinding SelectorItemHeight}"
                                        CornerRadius="4"
                                        Margin="2,0"
                                        Grid.ColumnSpan="3"
                                        VerticalAlignment="Center"
                                        HorizontalAlignment="Stretch"
                                        Visibility="Visible"
                                        Background="{Binding AccentBrush, Converter={StaticResource BrushConverter}, ConverterParameter=AccentBrushnull, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        BorderBrush="{StaticResource PopupSelectedBackground}"/>
                                    <input_controls:LoopingSelector
                                        x:Name="PART_Month"
                                        Width="{Binding SelectorItemWidth, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Height="{Binding Height, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        HorizontalAlignment="Center"
                                        Margin="2,2,3,2"
                                        AccentBrush="{Binding AccentBrush, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        IsTabStop="False"
                                        ItemHeight="{Binding SelectorItemHeight, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        ItemTemplate="{Binding MonthCellTemplate, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        ItemTemplateSelector="{Binding MonthCellTemplateSelector, RelativeSource={RelativeSource TemplatedParent}}"
                                        ItemWidth="{Binding SelectorItemWidth, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        SelectedForeground="{Binding SelectedForeground, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Style="{Binding SelectorStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" />
                                    <Rectangle
                                        VerticalAlignment="Stretch"
                                        HorizontalAlignment="Right"
                                        Width="1"
                                        Visibility="Visible"
                                        Fill="{StaticResource Border}"/>
                                    <input_controls:LoopingSelector
                                        x:Name="PART_Date"
                                        Grid.Column="1"
                                        Width="{Binding SelectorItemWidth, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Height="{Binding Height, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Margin="2,2,3,2"
                                        HorizontalAlignment="Center"
                                        AccentBrush="{Binding AccentBrush, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        IsTabStop="False"
                                        ItemHeight="{Binding SelectorItemHeight, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        ItemTemplate="{Binding DayCellTemplate, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        ItemTemplateSelector="{Binding DayCellTemplateSelector, RelativeSource={RelativeSource TemplatedParent}}"
                                        ItemWidth="{Binding SelectorItemWidth, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        SelectedForeground="{Binding SelectedForeground, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Style="{Binding SelectorStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" />
                                    <Rectangle
                                        Grid.Column="1"
                                        VerticalAlignment="Stretch"
                                        HorizontalAlignment="Right"
                                        Width="1"
                                        Visibility="Visible"
                                        Fill="{StaticResource Border}"/>
                                    <input_controls:LoopingSelector
                                        x:Name="PART_Year"
                                        Grid.Column="2"
                                        Width="{Binding SelectorItemWidth, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Height="{Binding Height, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Margin="2"
                                        HorizontalAlignment="Center"
                                        AccentBrush="{Binding AccentBrush, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        IsTabStop="False"
                                        ItemHeight="{Binding SelectorItemHeight, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        ItemTemplate="{Binding YearCellTemplate, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        ItemWidth="{Binding SelectorItemWidth, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        SelectedForeground="{Binding SelectedForeground, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Style="{Binding SelectorStyle, RelativeSource={RelativeSource Mode=TemplatedParent}}" />
                                </Grid>
                            </Border>
                            <Border
                                x:Name="FooterBorder"
                                Grid.Row="2"
                                SnapsToDevicePixels="True"
                                BorderBrush="{StaticResource BorderAlt}"
                                BorderThickness="0,1,0,0" >
                                <Grid x:Name="FooterGrid">
                                    <Grid.RowDefinitions>
                                        <RowDefinition x:Name="FooterButtonRowDefinition" MinHeight="32" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="1" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Button
                                        x:Name="PART_DoneButton"
                                        Grid.Column="0"
                                        Margin="4 4 2 4"
                                        Style="{StaticResource WPFGlyphButtonStyle}"
                                        Visibility="{Binding ShowDoneButton, Converter={StaticResource BooleanVisibilityConverter}, RelativeSource={RelativeSource Mode=TemplatedParent}}">
                                        <Button.Content>
                                            <Grid Width="11" Height="7">
                                                <Path Data="M0.00195312 3.49805C0.00195312 3.36133 0.0507812 3.24414 0.148438 3.14648C0.246094 3.04883 0.363281 3 0.5 3C0.636719 3 0.753906 3.04883 0.851562 3.14648L3.5 5.79492L9.14844 0.146484C9.24609 0.0488281 9.36328 0 9.5 0C9.57031 0 9.63477 0.0136719 9.69336 0.0410156C9.75586 0.0644531 9.80859 0.0996094 9.85156 0.146484C9.89844 0.189453 9.93555 0.242187 9.96289 0.304688C9.99023 0.363281 10.0039 0.427734 10.0039 0.498047C10.0039 0.634766 9.95312 0.753906 9.85156 0.855469L3.85156 6.85547C3.75391 6.95312 3.63672 7.00195 3.5 7.00195C3.36328 7.00195 3.24609 6.95312 3.14844 6.85547L0.148438 3.85547C0.0507812 3.75781 0.00195312 3.63867 0.00195312 3.49805Z" Fill="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=Grid}, Path=(TextElement.Foreground)}"/>
                                                <Path Data="M0.00195312 3.49805C0.00195312 3.36133 0.0507812 3.24414 0.148438 3.14648C0.246094 3.04883 0.363281 3 0.5 3C0.636719 3 0.753906 3.04883 0.851562 3.14648L3.5 5.79492L9.14844 0.146484C9.24609 0.0488281 9.36328 0 9.5 0C9.57031 0 9.63477 0.0136719 9.69336 0.0410156C9.75586 0.0644531 9.80859 0.0996094 9.85156 0.146484C9.89844 0.189453 9.93555 0.242187 9.96289 0.304688C9.99023 0.363281 10.0039 0.427734 10.0039 0.498047C10.0039 0.634766 9.95312 0.753906 9.85156 0.855469L3.85156 6.85547C3.75391 6.95312 3.63672 7.00195 3.5 7.00195C3.36328 7.00195 3.24609 6.95312 3.14844 6.85547L0.148438 3.85547C0.0507812 3.75781 0.00195312 3.63867 0.00195312 3.49805Z" Fill="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=Grid}, Path=(TextElement.Foreground)}"/>
                                            </Grid>
                                        </Button.Content>
                                    </Button>
                                    <Button
                                        x:Name="PART_CancelButton"
                                        Grid.Column="2"
                                        Margin="2 4 4 4"
                                        Style="{StaticResource WPFGlyphButtonStyle}"
                                        Visibility="{Binding ShowCancelButton, Converter={StaticResource BooleanVisibilityConverter}, RelativeSource={RelativeSource Mode=TemplatedParent}}">
                                        <Button.Content>
                                            <Grid Width="10" Height="8">
                                                <Path Data="M5 4.70898L1.85352 7.85547C1.75586 7.95312 1.63867 8.00195 1.50195 8.00195C1.36133 8.00195 1.24219 7.95312 1.14453 7.85547C1.04688 7.75781 0.998047 7.63867 0.998047 7.49805C0.998047 7.36133 1.04688 7.24414 1.14453 7.14648L4.29102 4L1.14453 0.853516C1.04688 0.755859 0.998047 0.636719 0.998047 0.496094C0.998047 0.425781 1.01172 0.361328 1.03906 0.302734C1.06641 0.240234 1.10156 0.1875 1.14453 0.144531C1.19141 0.0976562 1.24414 0.0625 1.30273 0.0390625C1.36523 0.0117187 1.43164 -0.00195312 1.50195 -0.00195312C1.63867 -0.00195312 1.75586 0.046875 1.85352 0.144531L5 3.29102L8.14648 0.144531C8.24414 0.046875 8.36133 -0.00195312 8.49805 -0.00195312C8.63867 -0.00195312 8.75781 0.046875 8.85547 0.144531C8.95312 0.242188 9.00195 0.361328 9.00195 0.501953C9.00195 0.638672 8.95312 0.755859 8.85547 0.853516L5.70898 4L8.85547 7.14648C8.95312 7.24414 9.00195 7.36328 9.00195 7.50391C9.00195 7.57422 8.98828 7.64062 8.96094 7.70312C8.93359 7.76172 8.89648 7.81445 8.84961 7.86133C8.80664 7.9043 8.75391 7.93945 8.69141 7.9668C8.63281 7.99023 8.56836 8.00195 8.49805 8.00195C8.36133 8.00195 8.24414 7.95312 8.14648 7.85547L5 4.70898Z" Fill="{TemplateBinding Foreground}"/>
                                                <Path Data="M5 4.70898L1.85352 7.85547C1.75586 7.95312 1.63867 8.00195 1.50195 8.00195C1.36133 8.00195 1.24219 7.95312 1.14453 7.85547C1.04688 7.75781 0.998047 7.63867 0.998047 7.49805C0.998047 7.36133 1.04688 7.24414 1.14453 7.14648L4.29102 4L1.14453 0.853516C1.04688 0.755859 0.998047 0.636719 0.998047 0.496094C0.998047 0.425781 1.01172 0.361328 1.03906 0.302734C1.06641 0.240234 1.10156 0.1875 1.14453 0.144531C1.19141 0.0976562 1.24414 0.0625 1.30273 0.0390625C1.36523 0.0117187 1.43164 -0.00195312 1.50195 -0.00195312C1.63867 -0.00195312 1.75586 0.046875 1.85352 0.144531L5 3.29102L8.14648 0.144531C8.24414 0.046875 8.36133 -0.00195312 8.49805 -0.00195312C8.63867 -0.00195312 8.75781 0.046875 8.85547 0.144531C8.95312 0.242188 9.00195 0.361328 9.00195 0.501953C9.00195 0.638672 8.95312 0.755859 8.85547 0.853516L5.70898 4L8.85547 7.14648C8.95312 7.24414 9.00195 7.36328 9.00195 7.50391C9.00195 7.57422 8.98828 7.64062 8.96094 7.70312C8.93359 7.76172 8.89648 7.81445 8.84961 7.86133C8.80664 7.9043 8.75391 7.93945 8.69141 7.9668C8.63281 7.99023 8.56836 8.00195 8.49805 8.00195C8.36133 8.00195 8.24414 7.95312 8.14648 7.85547L5 4.70898Z" Fill="{TemplateBinding Foreground}"/>
                                            </Grid>
                                        </Button.Content>
                                    </Button>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>                        
                        <Trigger Property="Header" Value="{x:Null}">
                            <Setter TargetName="HeaderGrid" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="PART_Root" Property="Background" Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter TargetName="DateSelectorBorder" Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="ShowDoneButton" Value="False"/>
                                <Condition Property="ShowCancelButton" Value="False"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Visibility" Value="Collapsed" TargetName="FooterBorder"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfDateSelectorStyle}" TargetType="{x:Type Input:SfDateSelector}" />
</ResourceDictionary>
