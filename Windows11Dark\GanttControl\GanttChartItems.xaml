<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:system="clr-namespace:System;assembly=mscorlib" 
                    xmlns:ganttcontrol="clr-namespace:Syncfusion.Windows.Controls.Gantt;assembly=Syncfusion.Gantt.Wpf">

    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ToolTip.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="ganttcontrol:GanttNode">
        <Setter Property="Background" Value="{StaticResource PrimaryBackground}"/>
        <Setter Property="HorizontalAlignment" Value="Left"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ganttcontrol:GanttNode">
                    <Canvas Height="11">
                        <Ellipse x:Name="PART_LeftTouchPoint"
                                 Cursor="Hand" 
                                 Height="10" 
                                 Width="10" 
                                 Canvas.Left="-20"
                                 Canvas.Top="0.5"
                                 Fill="{StaticResource ContentForeground}"
                                 Visibility="Hidden"/>
                        <Ellipse x:Name="PART_RightTouchPoint"
                                 Cursor="Hand" 
                                 Height="10"
                                 Width="10"
                                 Canvas.Right="-20"
                                 Canvas.Top="0.5"
                                 Fill="{StaticResource ContentForeground}"
                                 Visibility="Hidden"/>
                        <Border Name="PART_Border"
                                Height="11"
                                Width="{TemplateBinding Width}"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                SnapsToDevicePixels="True"
                                BorderThickness="0,0.4,0,0.4"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                Background="{TemplateBinding Background}">
                            <Grid Height="{TemplateBinding Height}"
                                  Width="{TemplateBinding Width}"
                                  Margin="-0.4">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="10*" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="10*" />
                                </Grid.ColumnDefinitions>
                                <ToolTipService.ToolTip>
                                    <ToolTip x:Name="PART_MouseOverTooltip"
                                             Content="{TemplateBinding DataContext}"
                                             ContentTemplate="{TemplateBinding ToolTipTemplate}"
                                             Style="{StaticResource WPFToolTipStyle}">
                                    </ToolTip>
                                </ToolTipService.ToolTip>
                                <Thumb Cursor="SizeAll" x:Name="PART_DragDropThumb"  Grid.Column="0" Grid.ColumnSpan="3">
                                    <Thumb.Template>
                                        <ControlTemplate>
                                            <Border Background="Transparent"/>
                                        </ControlTemplate>
                                    </Thumb.Template>
                                </Thumb>
                                <Border x:Name="ProgressBorder" 
                                     Height="2"
                                     BorderThickness="0" 
                                     SnapsToDevicePixels="True"
                                     HorizontalAlignment="Left" 
                                     VerticalAlignment="Center"
                                     Width="{TemplateBinding ProgressWidth}" 
                                     Background="{TemplateBinding ProgressIndicatorBackground}"
                                     Grid.Column="0" 
                                     Grid.ColumnSpan="3">
                                    <Thumb Cursor="SizeWE" x:Name="PART_ProgressThumb" Height="3" VerticalAlignment="Center" HorizontalAlignment="Right">
                                        <Thumb.Template>
                                            <ControlTemplate>
                                                <Border HorizontalAlignment="Right" Width="5" BorderThickness="0" Background="{Binding ElementName=ProgressBorder,Path=Background}"/>
                                            </ControlTemplate>
                                        </Thumb.Template>
                                    </Thumb>
                                </Border>

                                <Thumb Cursor="ScrollE"  MaxWidth="6" Grid.Column="2" HorizontalAlignment="Right" x:Name="PART_RightThumb" SnapsToDevicePixels="True" >
                                    <Thumb.Template>
                                        <ControlTemplate>
                                            <Border Background="Transparent" HorizontalAlignment="Right" BorderBrush="Transparent" BorderThickness="0">
                                                <Path Data="M0,0 L5,0 5,12 0,12 0,9.75 2.75,9.75 2.75,2.25 0,2.25 z"
                                                   Stretch="Fill"
                                                   Fill="{StaticResource IconColor}"
                                                   HorizontalAlignment="Right"
                                                   StrokeThickness="0.5"
                                                   SnapsToDevicePixels="True"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Thumb.Template>
                                </Thumb>

                                <Thumb Cursor="ScrollW" MaxWidth="6" Grid.Column="0" HorizontalAlignment="left" x:Name="PART_LeftThumb" SnapsToDevicePixels="True">
                                    <Thumb.Template>
                                        <ControlTemplate>
                                            <Border Background="Transparent" HorizontalAlignment="Left" BorderBrush="Transparent" BorderThickness="0">
                                                <Path Data="M0,0 L5,0 5,2.25 2.25,2.25 2.25,9.75 5,9.75 5,12 0,12 z"
                                                      Stretch="Fill"
                                                      HorizontalAlignment="Left"
                                                      SnapsToDevicePixels="True"
                                                      StrokeThickness="0.5"
                                                      Fill="{StaticResource IconColor}">
                                                </Path>
                                            </Border>
                                        </ControlTemplate>
                                    </Thumb.Template>
                                </Thumb>
                            </Grid>
                        </Border>
                    </Canvas>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Path=ProgressIndicatorBackground, RelativeSource={RelativeSource TemplatedParent}}" Value="{x:Null}">
                            <Setter TargetName="ProgressBorder" Property="Background" Value="{StaticResource PrimaryForeground}"/>
                        </DataTrigger>
                        <Trigger Property="FlowDirection" Value="RightToLeft">
                            <Setter TargetName="PART_RightThumb" Property="Cursor" Value="ScrollW"/>
                            <Setter TargetName="PART_LeftThumb" Property="Cursor" Value="ScrollE"/>
                        </Trigger>
                        <Trigger Property="FlowDirection" Value="LeftToRight">
                            <Setter TargetName="PART_RightThumb" Property="Cursor" Value="ScrollE"/>
                            <Setter TargetName="PART_LeftThumb" Property="Cursor" Value="ScrollW"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="ganttcontrol:HeaderNode">
        <Setter Property="Background" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource ContentForeground}"/>
        <Setter Property="MaxHeight" Value="24"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ganttcontrol:HeaderNode">
                    <Canvas Width="{TemplateBinding Width}" Height="11.435">
                        <Ellipse x:Name="PART_LeftTouchPoint"
                                 Cursor="Hand" 
                                 Height="10" 
                                 Width="10"
                                 Canvas.Left="-25"
                                 Fill="{StaticResource ContentForeground}"
                                 Visibility="Hidden"/>
                        <Ellipse x:Name="PART_RightTouchPoint"
                                 Height="10" 
                                 Width="10" 
                                 Canvas.Right="-25" 
                                 Fill="{StaticResource ContentForeground}"
                                 Visibility="Hidden"/>
                        <Border Name="PART_HeaderBorder" 
                                Background="Transparent" 
                                Tag="{Binding RelativeSource={RelativeSource TemplatedParent}}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="0" 
                                Width="{TemplateBinding Width}">
                            <Grid Width="{TemplateBinding Width}"
                                  VerticalAlignment="Center">
                                <ToolTipService.ToolTip>
                                    <ToolTip Content="{TemplateBinding DataContext}"
                                             ContentTemplate="{TemplateBinding ToolTipTemplate}"
                                             Style="{StaticResource WPFToolTipStyle}">
                                    </ToolTip>
                                </ToolTipService.ToolTip>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <Rectangle HorizontalAlignment="Left" 
                                           Grid.Column="1"
                                           Height="6.4" 
                                           VerticalAlignment="Top"
                                           Fill="{TemplateBinding Background}"
                                           Width="{TemplateBinding Width}" 
                                           Stroke="{StaticResource ContentForeground}"/>
                                <Path Data="M0.3,0.3 L9.834909,0.30036073 9.8351226,5.9832297 5.0695471,10.734966 0.32096295,5.9863821 z"
                                      HorizontalAlignment="Left"
                                      Margin="-5.068,0,0,0"
                                      Height="11.435"
                                      Stretch="Fill"
                                      Fill="{TemplateBinding Background}"
                                      VerticalAlignment="Top"
                                      Width="10.135"
                                      Stroke="{TemplateBinding BorderBrush}">
                                </Path>
                                <Path Data="M0.3,0.3 L9.834909,0.30036073 9.8351226,5.9832297 5.0695471,10.734966 0.32096295,5.9863821 z"
                                      HorizontalAlignment="Right"
                                      Margin="0,0,-5.068,0"
                                      Height="11.435"
                                      Stretch="Fill"
                                      Fill="{TemplateBinding Background}"
                                      VerticalAlignment="Top"
                                      Width="10.135"
                                      Stroke="{TemplateBinding BorderBrush}"/>
                            </Grid>
                        </Border>
                    </Canvas>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="ganttcontrol:MileStone">
        <Setter Property="Background" Value="{StaticResource PrimaryBackground}"/>
        <Setter Property="MaxHeight" Value="24" />
        <Setter Property="MinWidth" Value="17"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ganttcontrol:MileStone">
                    <Canvas Height="15">
                        <Ellipse x:Name="PART_LeftTouchPoint"
                                 Cursor="Hand" 
                                 Height="10"
                                 Width="10"
                                 Canvas.Left="-20"
                                 Canvas.Top="2"
                                 Fill="{StaticResource ContentForeground}"
                                 Visibility="Hidden"/>
                        <Ellipse x:Name="PART_RightTouchPoint" 
                                 Cursor="Hand"
                                 Height="10"
                                 Width="10" 
                                 Canvas.Right="-18"
                                 Canvas.Top="2"
                                 Fill="{StaticResource ContentForeground}"
                                 Visibility="Hidden"/>
                        <Border Name="PART_MileStoneBorder"
                                SnapsToDevicePixels="True"
                                Background="Transparent"
                                Tag="{Binding RelativeSource={RelativeSource TemplatedParent}}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="0">
                            <Grid SnapsToDevicePixels="True">
                                <ToolTipService.ToolTip>
                                    <ToolTip x:Name="PART_MouseOverTooltip"
                                             Content="{TemplateBinding DataContext}"
                                             ContentTemplate="{TemplateBinding ToolTipTemplate}"
                                             Style="{StaticResource WPFToolTipStyle}">
                                    </ToolTip>
                                </ToolTipService.ToolTip>
                                <Path Data="M97.580734,0 L195.16147,97.580734 L97.580734,195.16147 L0,97.580734 z"
                                      Stretch="Fill"
                                      Height="15"  
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      Width="15"
                                      Fill="{StaticResource IconColor}">
                                </Path>
                            </Grid>
                        </Border>
                    </Canvas>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
