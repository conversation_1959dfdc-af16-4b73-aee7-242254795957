<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF" 
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    >

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Hyperlink.xaml"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="RichTextBox.Static.Border" Color="#FFABAdB3"/>

    <SolidColorBrush x:Key="RichTextBox.MouseOver.Border" Color="#FF7EB4EA"/>

    <SolidColorBrush x:Key="RichTextBox.Focused.Border" Color="#FF569DE5"/>

    <LinearGradientBrush x:Key="RichTextBoxBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="RichTextBoxBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="RichTextBoxBorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="{x:Type TextBoxBase}" TargetType="{x:Type TextBoxBase}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}"/>
        <Setter Property="BorderBrush" Value="{StaticResource RichTextBoxBorderBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        <Setter Property="SelectionBrush"
                Value="{StaticResource PrimaryBackground}"/>
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="AllowDrop" Value="true"/>
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBoxBase}">
                    <Border x:Name="border" 
                            BorderBrush="{TemplateBinding BorderBrush}" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            Background="{TemplateBinding Background}" 
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                            SnapsToDevicePixels="True">
                        <ScrollViewer x:Name="PART_ContentHost" 
                                      Focusable="false" 
                                      Background="Transparent"
                                      HorizontalScrollBarVisibility="Hidden" 
                                      VerticalScrollBarVisibility="Hidden"
                                      Margin="0,8,10,8"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt5}"/>
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="CaretBrush" Value="{StaticResource ContentForeground}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource RichTextBoxBorderBrushHovered}"/>
                        </Trigger>
                        <Trigger Property="IsReadOnly" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt6}"/>
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource ContentBackground}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource RichTextBoxBorderBrushFocused}"/>
                            <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.BorderThickness1112}"/>
                            <Setter Property="Padding" Value="0,0,0,-1"/>
                        </Trigger>                        
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}"/>
                            <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt6}"/>
                            <Setter Property="Foreground" Value="{StaticResource DisabledForeground}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="WPFRichTextBoxStyle" TargetType="{x:Type RichTextBox}">
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt4}"/>
        <Setter Property="BorderBrush" Value="{StaticResource RichTextBoxBorderBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Dark.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Dark.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Dark.FontWeightNormal}"/>
        
        <Style.Resources>
            <Style x:Key="{x:Type FlowDocument}" TargetType="{x:Type FlowDocument}">
                <Setter Property="OverridesDefaultStyle" Value="true"/>
            </Style>
            <Style x:Key="{x:Type Hyperlink}" BasedOn="{StaticResource WPFHyperlinkStyle}" TargetType="{x:Type Hyperlink}"></Style>
        </Style.Resources>
        <Style.BasedOn>
            <StaticResource ResourceKey="{x:Type TextBoxBase}"/>
        </Style.BasedOn>
    </Style>

    <Style BasedOn="{StaticResource WPFRichTextBoxStyle}" TargetType="{x:Type RichTextBox}"/>
</ResourceDictionary>
