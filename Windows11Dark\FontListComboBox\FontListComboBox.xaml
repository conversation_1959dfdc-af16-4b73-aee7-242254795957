<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:utilsOuter="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:converters="clr-namespace:Syncfusion.Windows.Tools;assembly=Syncfusion.Tools.WPF"
                    xmlns:syncfusion="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphDropdownExpander.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="FontListComboBox.Static.Border" Color="#9E9E9E"/>

    <SolidColorBrush x:Key="FontListComboBox.MouseOver.Border" Color="#757575"/>

    <SolidColorBrush x:Key="FontListComboBox.Pressed.Border" Color="#0279FF"/>

    <SolidColorBrush x:Key="FontListComboBox.Focused.Border" Color="#0279FF"/>

    <LinearGradientBrush x:Key="FontListComboBox.Static.BorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="FontListComboBox.Static.BorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource BorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource Border.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="FontListComboBox.Static.BorderBrushFocused" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2Gradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource BorderAlt2.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <converters:PopupHeightToThumbOffsetConverter x:Key="PopupHeightToThumbOffsetConverter" />
    <converters:FontToStringConverter x:Key="FontToStringConverter" />

    <Style x:Key="SyncfusionBottomThumbStyle" 
           TargetType="{x:Type syncfusion:BottomThumb}">
        <Setter Property="Background" 
                Value="Transparent" />
        <Setter Property="Foreground" 
                Value="{StaticResource BottomThumb.Static.Fill}" />
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type syncfusion:BottomThumb}">
                    <Border Name="Border" 
                            SnapsToDevicePixels="True" 
                            Height="{TemplateBinding Height}" 
                            Width="{TemplateBinding Width}" 
                            Background="{TemplateBinding Background}"
                            BorderThickness="{StaticResource Windows11Dark.BorderThickness0100}" 
                            BorderBrush="{TemplateBinding BorderBrush}">
                        <WrapPanel HorizontalAlignment="Center"
                                   VerticalAlignment="Center">
                            <Border Name="B1" 
                                    Background="{StaticResource IconColor}" 
                                    Width="3"
                                    Height="3"
                                    Margin="3,2,3,2">
                            </Border>
                            <Border Name="B2"
                                    Background="{StaticResource IconColor}" 
                                    Width="3"
                                    Height="3"
                                    Margin="3,2,3,2">
                            </Border>
                            <Border Name="B3" 
                                    Background="{StaticResource IconColor}"
                                    Width="3"
                                    Height="3"
                                    Margin="3,2,3,2">
                            </Border>
                            <Border Name="B4" 
                                    Background="{StaticResource IconColor}"
                                    Width="3" 
                                    Height="3"
                                    Margin="3,2,3,2">
                            </Border>
                        </WrapPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="UIElement.IsEnabled" 
                                 Value="False">
                            <Setter TargetName="Border"
                                    Property="Background" 
                                    Value="{StaticResource PopupBackground}" />
                            <Setter TargetName="Border" 
                                    Property="BorderBrush" 
                                    Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="B1"
                                    Property="Background" 
                                    Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="B2"
                                    Property="Background" 
                                    Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="B3"
                                    Property="Background" 
                                    Value="{StaticResource IconColorDisabled}" />
                            <Setter TargetName="B4"
                                    Property="Background" 
                                    Value="{StaticResource IconColorDisabled}" />
                        </Trigger>
                        <Trigger Property="UIElement.IsEnabled" Value="True">
                            <Setter TargetName="Border"
                                    Property="Background" 
                                    Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter TargetName="Border" 
                                    Property="BorderBrush" 
                                    Value="{StaticResource BorderAlt}" />
                            <Setter TargetName="B1"
                                    Property="Background" 
                                    Value="{StaticResource IconColor}" />
                            <Setter TargetName="B2"
                                    Property="Background" 
                                    Value="{StaticResource IconColor}" />
                            <Setter TargetName="B3"
                                    Property="Background" 
                                    Value="{StaticResource IconColor}" />
                            <Setter TargetName="B4"
                                    Property="Background" 
                                    Value="{StaticResource IconColor}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionBottomThumbStyle}" TargetType="{x:Type syncfusion:BottomThumb}" />

    <Style x:Key="SyncfusionFontListComboBoxStyle"
           TargetType="{x:Type syncfusion:FontListComboBox}">
        <Setter Property="HorizontalContentAlignment"
                Value="Left" />
        <Setter Property="VerticalContentAlignment" 
                Value="Center" />
        <Setter Property="Background" 
                Value="{StaticResource ContentBackgroundAlt4}" />
        <Setter Property="Foreground" 
                Value="{StaticResource ContentForeground}" />
        <Setter Property="ItemsForeground" 
                Value="{StaticResource ContentForeground}" />
        <Setter Property="BorderBrush"
                Value="{StaticResource FontListComboBox.Static.BorderBrush}" />
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant1}" />
        <Setter Property="FontFamily" 
                Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Setter Property="FontSize" 
                Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" 
                Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Padding" Value="2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type syncfusion:FontListComboBox}">
                        <Grid>
                        <Border Name="border"
                                Padding="{TemplateBinding Padding}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                Background="{TemplateBinding Background}"
                                CornerRadius="4">
                            <Grid Grid.IsSharedSizeScope="True">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="8*" />
                                    <ColumnDefinition SharedSizeGroup="ComboBoxButton" Width="*" />
                                </Grid.ColumnDefinitions>
                                <ContentPresenter Name="PART_Text"
                                                  Grid.Column="0"
                                                  Margin="4 0 0 0"
                                                  HorizontalAlignment="{TemplateBinding Control.HorizontalContentAlignment}"
                                                  VerticalAlignment="{TemplateBinding Control.VerticalContentAlignment}"
                                                  SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}" 
                                                  Content="{Binding Path=SelectedFontFamily, Converter={StaticResource FontToStringConverter},
                                                  Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}}">
                                    <ContentPresenter.Resources>
                                        <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                    </ContentPresenter.Resources>
                                </ContentPresenter>

                                <ToggleButton x:Name="Part_ToggleButton" Grid.Column="1"
                                              Margin="0,0,2,0"
                                              IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay,RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}}"
                                              Style="{StaticResource WPFGlyphDropdownExpanderStyle}">
                                </ToggleButton>
                            </Grid>
                        </Border>
                        <Popup Name="PART_Popup"
                                   Placement="Bottom"                                   
			                       TextElement.Foreground="{Binding Path=ItemsForeground, Mode=TwoWay,
                                                          RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}}"
                                   Height="{Binding Path=PopupDropDownHeight, Mode=TwoWay,
                                            RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}}"
                                   
                                   AllowsTransparency="True"
			                       StaysOpen="True"
                                   IsOpen="{Binding Path=IsDropDownOpen, Mode=TwoWay,
                                   RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}}"
                                   PopupAnimation="Fade" 
                                   SnapsToDevicePixels="True">
                            <Border BorderBrush="{StaticResource BorderAlt}" 
                                    Background="{StaticResource PopupBackground}"
                                    BorderThickness="{StaticResource Windows11Dark.BorderThickness1}"
                                    CornerRadius="4"
                                    Effect="{StaticResource Default.ShadowDepth4}">
                                <Border.Margin>
                                    <Thickness>16,1,16,16</Thickness>
                                </Border.Margin>
                                <DockPanel
                                           LastChildFill="True" 
                                           Background="{StaticResource PopupBackground}"
                                           Height="{Binding ElementName=bottomThumb, Path=Offset}" 
                                           SnapsToDevicePixels="True">
                                    <syncfusion:BottomThumb Name="bottomThumb"
                                                           Height="14"
                                                           DockPanel.Dock="Bottom" 
                                                           Offset="{Binding Path=PopupDropDownHeight, Mode=TwoWay, Converter={ StaticResource PopupHeightToThumbOffsetConverter},
                                                           ConverterParameter=8,  RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}}" 
                                                           Visibility="Visible"/>

                                    <syncfusion:FontListBox x:Name="FontListBoxElement"
                                                               DockPanel.Dock="Top" 
                                                               BorderBrush="Transparent" 
                                                               SnapsToDevicePixels="True" 
                                                                Background="{StaticResource PopupBackground}"
                                                               ScrollViewer.VerticalScrollBarVisibility="Auto"
                                                               Width="{Binding Path=Width, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}, UpdateSourceTrigger=PropertyChanged}"
                                                               SelectedFontFamily="{Binding Path=SelectedFontFamily, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}, UpdateSourceTrigger=PropertyChanged}"
                                                               FocusedFontFamily="{Binding Path=FocusedFontFamily, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}, UpdateSourceTrigger=PropertyChanged}"
                                                               FontsSource="{Binding Path=FontsSource, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}, UpdateSourceTrigger=PropertyChanged}"
                                                               ThemeFonts="{Binding Path=ThemeFonts, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}, UpdateSourceTrigger=PropertyChanged}"
                                                               RecentlyUsedFonts="{Binding Path=RecentlyUsedFonts, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}, UpdateSourceTrigger=PropertyChanged}"
                                                               HasFocus="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}, UpdateSourceTrigger=PropertyChanged}"
                                                               DisplayFontNamesInSystemFont="{Binding Path=DisplayFontNamesInSystemFont, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}, UpdateSourceTrigger=PropertyChanged}"
                                                               GroupHeaderStyle="{Binding Path=GroupHeaderStyle, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}, UpdateSourceTrigger=PropertyChanged}"
                                                               ItemTemplate="{Binding Path=ItemTemplate, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}, UpdateSourceTrigger=PropertyChanged}"
                                                               ItemContainerStyle="{Binding Path=ItemContainerStyle, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}, UpdateSourceTrigger=PropertyChanged}"/>
                                </DockPanel>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" 
                                  Value="True">
                            <Setter Property="Foreground"
                                    Value="{StaticResource ContentForeground}" />
                            <Setter Property="BorderBrush" 
                                    Value="{StaticResource FontListComboBox.Static.BorderBrushHovered}" />
                            <Setter Property="Background" 
                                    Value="{StaticResource ContentBackgroundAlt5}" />
                        </Trigger>
                        <Trigger Property="IsFocused" 
                                 Value="True">
                            <Setter Property="Background" 
                                    Value="{StaticResource ContentBackgroundAlt5}" />
                            <Setter Property="BorderBrush" 
                                    Value="{StaticResource FontListComboBox.Static.BorderBrushFocused}" />
                            <Setter Property="Foreground"
                                     Value="{StaticResource ContentForeground}" />
                            <Setter Property="BorderThickness" 
                                    Value="{StaticResource Windows11Dark.BorderThickness1112}" />
                        </Trigger>
                        <Trigger Property="UIElement.IsEnabled" 
                                 Value="False">
                            <Setter Property="Foreground"
                                    Value="{StaticResource DisabledForeground}" />
                            <Setter Property="Background"
                                    Value="{StaticResource ContentBackgroundAlt6}" />
                            <Setter Property="BorderBrush" 
                                    Value="{StaticResource BorderAlt}" />
                        </Trigger>                        
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                            <Setter TargetName="Part_ToggleButton" Property="VerticalAlignment" Value="Center"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="utilsOuter:SkinStorage.VisualStyle"
                                           Value="False" />
                                <Condition Property="IsDropDownOpen"
                                           Value="False" />
                                <Condition Property="IsMouseOver"
                                           SourceName="border"
                                           Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" 
                                    Value="{StaticResource ContentBackgroundAlt5}" />
                            <Setter Property="BorderBrush" 
                                    Value="{StaticResource FontListComboBox.Static.BorderBrushHovered}" />
                            <Setter Property="Foreground"
                                     Value="{StaticResource ContentForeground}" />
                        </MultiTrigger>
                        <MultiTrigger>

                            <MultiTrigger.Conditions>
                                <Condition Property="IsDropDownOpen" Value="True"/>
                                <Condition Property="IsOpen" SourceName="PART_Popup" Value="True"/>
                            </MultiTrigger.Conditions>
                        
                            <Setter TargetName="PART_Text" 
                                    Property="Content"  
                                    Value="{Binding Path=FocusedFontFamily, Converter={StaticResource FontToStringConverter}, Mode=TwoWay, RelativeSource={RelativeSource AncestorType={x:Type syncfusion:FontListComboBox}}}" />
                            <Setter Property="Background"
                                    Value="{StaticResource ContentBackground}" />
                            <Setter Property="BorderBrush"
                                    Value="{StaticResource FontListComboBox.Static.BorderBrushFocused}" />
                            <Setter Property="Foreground"
                                     Value="{StaticResource ContentForeground}" />
                            <Setter Property="BorderThickness" 
                                    Value="{StaticResource Windows11Dark.BorderThickness1112}" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionFontListComboBoxStyle}" TargetType="{x:Type syncfusion:FontListComboBox}" />

</ResourceDictionary>
