using System.Globalization;
using System.Windows.Data;
using AirMonitor.Core.Models;

namespace AirMonitor.LicenseGenerator.Views;

/// <summary>
/// 许可证到剩余天数转换器
/// </summary>
public class LicenseToRemainingDaysConverter : IValueConverter
{
    public static readonly LicenseToRemainingDaysConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is LicenseInfo license)
        {
            var remainingDays = license.GetRemainingDays();
            if (remainingDays == -1)
            {
                return "永久";
            }
            else if (remainingDays > 0)
            {
                return $"{remainingDays} 天";
            }
            else
            {
                return "已过期";
            }
        }
        return "未知";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
