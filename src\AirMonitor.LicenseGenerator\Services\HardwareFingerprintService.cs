using System.Management;
using System.Net.NetworkInformation;
using System.Security.Cryptography;
using System.Text;
using AirMonitor.Core.Constants;
using AirMonitor.Core.Interfaces;
using AirMonitor.Core.Models;
using AirMonitor.Core.Utilities;

namespace AirMonitor.LicenseGenerator.Services;

/// <summary>
/// 硬件指纹服务实现
/// 获取和管理硬件指纹信息
/// </summary>
public class HardwareFingerprintService : IHardwareFingerprintService
{
    /// <summary>
    /// 获取当前机器的硬件指纹
    /// </summary>
    /// <returns>硬件指纹字符串</returns>
    public async Task<string> GetFingerprintAsync()
    {
        return await Task.Run(() => GetFingerprint());
    }

    /// <summary>
    /// 获取当前机器的硬件指纹（同步版本）
    /// </summary>
    /// <returns>硬件指纹字符串</returns>
    public string GetFingerprint()
    {
        try
        {
            var cpuId = GetCpuIdInternal();
            var motherboardId = GetMotherboardIdInternal();
            var fingerprint = $"CPU-{cpuId}-MB-{motherboardId}";
            
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(fingerprint));
            return Convert.ToHexString(hash)[..LicenseConstants.HardwareFingerprintLength].ToUpperInvariant();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"获取硬件指纹失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 获取详细的硬件指纹信息
    /// </summary>
    /// <returns>硬件指纹信息对象</returns>
    public async Task<HardwareFingerprintInfo> GetDetailedFingerprintAsync()
    {
        return await Task.Run(() =>
        {
            try
            {
                var info = new HardwareFingerprintInfo
                {
                    CpuId = GetCpuIdInternal(),
                    MotherboardId = GetMotherboardIdInternal(),
                    MacAddress = GetMacAddressInternal(),
                    DiskId = GetDiskIdInternal(),
                    ComputerName = GetComputerName(),
                    UserName = GetUserName(),
                    OSInfo = GetOSInfoInternal(),
                    GeneratedAt = DateTime.UtcNow,
                    IsValid = true
                };

                info.Fingerprint = GenerateFingerprintFromInfo(info);
                return info;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"获取详细硬件指纹失败: {ex.Message}", ex);
            }
        });
    }

    /// <summary>
    /// 获取CPU序列号
    /// </summary>
    /// <returns>CPU序列号</returns>
    public async Task<string> GetCpuIdAsync()
    {
        return await Task.Run(() => GetCpuIdInternal());
    }

    /// <summary>
    /// 获取主板序列号
    /// </summary>
    /// <returns>主板序列号</returns>
    public async Task<string> GetMotherboardIdAsync()
    {
        return await Task.Run(() => GetMotherboardIdInternal());
    }

    /// <summary>
    /// 获取MAC地址
    /// </summary>
    /// <returns>MAC地址</returns>
    public async Task<string> GetMacAddressAsync()
    {
        return await Task.Run(() => GetMacAddressInternal());
    }

    /// <summary>
    /// 获取硬盘序列号
    /// </summary>
    /// <returns>硬盘序列号</returns>
    public async Task<string> GetDiskIdAsync()
    {
        return await Task.Run(() => GetDiskIdInternal());
    }

    /// <summary>
    /// 获取计算机名称
    /// </summary>
    /// <returns>计算机名称</returns>
    public string GetComputerName()
    {
        return Environment.MachineName;
    }

    /// <summary>
    /// 获取用户名
    /// </summary>
    /// <returns>用户名</returns>
    public string GetUserName()
    {
        return Environment.UserName;
    }

    /// <summary>
    /// 获取操作系统信息
    /// </summary>
    /// <returns>操作系统信息</returns>
    public async Task<string> GetOSInfoAsync()
    {
        return await Task.Run(() => GetOSInfoInternal());
    }

    /// <summary>
    /// 验证硬件指纹是否匹配
    /// </summary>
    /// <param name="targetFingerprint">目标指纹</param>
    /// <returns>true表示匹配，false表示不匹配</returns>
    public async Task<bool> ValidateFingerprintAsync(string targetFingerprint)
    {
        try
        {
            var currentFingerprint = await GetFingerprintAsync();
            return string.Equals(currentFingerprint, targetFingerprint, StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 生成硬件指纹
    /// </summary>
    /// <param name="cpuId">CPU序列号</param>
    /// <param name="motherboardId">主板序列号</param>
    /// <param name="macAddress">MAC地址（可选）</param>
    /// <returns>生成的硬件指纹</returns>
    public async Task<string> GenerateFingerprintAsync(string cpuId, string motherboardId, string? macAddress = null)
    {
        return await Task.Run(() =>
        {
            try
            {
                var fingerprint = string.IsNullOrWhiteSpace(macAddress) 
                    ? $"CPU-{cpuId}-MB-{motherboardId}"
                    : $"CPU-{cpuId}-MB-{motherboardId}-MAC-{macAddress}";
                
                using var sha256 = SHA256.Create();
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(fingerprint));
                return Convert.ToHexString(hash)[..LicenseConstants.HardwareFingerprintLength].ToUpperInvariant();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"生成硬件指纹失败: {ex.Message}", ex);
            }
        });
    }

    /// <summary>
    /// 从远程机器获取硬件指纹（通过网络或其他方式）
    /// </summary>
    /// <param name="targetMachine">目标机器标识</param>
    /// <returns>远程机器的硬件指纹信息</returns>
    public async Task<HardwareFingerprintInfo?> GetRemoteFingerprintAsync(string targetMachine)
    {
        // 注意：这是一个占位实现，实际应用中需要根据具体需求实现
        // 可能通过网络服务、文件共享或其他方式获取远程机器信息
        await Task.Delay(100); // 模拟异步操作
        
        // 返回null表示暂不支持远程获取
        return null;
    }

    /// <summary>
    /// 导出硬件指纹信息到文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="fingerprintInfo">硬件指纹信息</param>
    /// <returns>是否导出成功</returns>
    public async Task<bool> ExportFingerprintAsync(string filePath, HardwareFingerprintInfo fingerprintInfo)
    {
        try
        {
            return await JsonHelper.SerializeToFileAsync(fingerprintInfo, filePath);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 从文件导入硬件指纹信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>硬件指纹信息</returns>
    public async Task<HardwareFingerprintInfo?> ImportFingerprintAsync(string filePath)
    {
        try
        {
            return await JsonHelper.DeserializeFromFileAsync<HardwareFingerprintInfo>(filePath);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 检查硬件环境是否发生变化
    /// </summary>
    /// <param name="originalFingerprint">原始指纹</param>
    /// <returns>变化检测结果</returns>
    public async Task<HardwareChangeResult> DetectHardwareChangesAsync(string originalFingerprint)
    {
        try
        {
            var currentFingerprint = await GetFingerprintAsync();
            var hasChanged = !string.Equals(currentFingerprint, originalFingerprint, StringComparison.OrdinalIgnoreCase);

            var result = new HardwareChangeResult
            {
                HasChanged = hasChanged,
                CurrentFingerprint = currentFingerprint,
                OriginalFingerprint = originalFingerprint,
                DetectedAt = DateTime.UtcNow
            };

            if (hasChanged)
            {
                // 检测具体变化的组件
                var originalInfo = await GetDetailedFingerprintAsync();
                result.ChangedComponents.Add("硬件指纹不匹配");
                result.Details = $"原始指纹: {originalFingerprint}, 当前指纹: {currentFingerprint}";
            }
            else
            {
                result.Details = "硬件环境未发生变化";
            }

            return result;
        }
        catch (Exception ex)
        {
            return new HardwareChangeResult
            {
                HasChanged = true,
                Details = $"检测硬件变化时发生错误: {ex.Message}",
                ChangedComponents = { "检测失败" }
            };
        }
    }

    #region 私有方法

    /// <summary>
    /// 获取CPU序列号内部实现
    /// </summary>
    private string GetCpuIdInternal()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor");
            foreach (ManagementObject obj in searcher.Get())
            {
                var processorId = obj["ProcessorId"]?.ToString();
                if (!string.IsNullOrWhiteSpace(processorId))
                {
                    return processorId;
                }
            }
        }
        catch
        {
            // 忽略异常，使用备用方法
        }

        // 备用方法：使用环境信息
        return Environment.ProcessorCount.ToString() + Environment.MachineName.ToMD5()[..8];
    }

    /// <summary>
    /// 获取主板序列号内部实现
    /// </summary>
    private string GetMotherboardIdInternal()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard");
            foreach (ManagementObject obj in searcher.Get())
            {
                var serialNumber = obj["SerialNumber"]?.ToString();
                if (!string.IsNullOrWhiteSpace(serialNumber) && serialNumber != "None")
                {
                    return serialNumber;
                }
            }
        }
        catch
        {
            // 忽略异常，使用备用方法
        }

        // 备用方法：使用机器名和用户名
        return (Environment.MachineName + Environment.UserName).ToMD5()[..8];
    }

    /// <summary>
    /// 获取MAC地址内部实现
    /// </summary>
    private string GetMacAddressInternal()
    {
        try
        {
            var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
            foreach (var ni in networkInterfaces)
            {
                if (ni.NetworkInterfaceType == NetworkInterfaceType.Ethernet ||
                    ni.NetworkInterfaceType == NetworkInterfaceType.Wireless80211)
                {
                    if (ni.OperationalStatus == OperationalStatus.Up)
                    {
                        var macAddress = ni.GetPhysicalAddress().ToString();
                        if (!string.IsNullOrWhiteSpace(macAddress) && macAddress != "000000000000")
                        {
                            return macAddress;
                        }
                    }
                }
            }
        }
        catch
        {
            // 忽略异常
        }

        return "UNKNOWN";
    }

    /// <summary>
    /// 获取硬盘序列号内部实现
    /// </summary>
    private string GetDiskIdInternal()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_DiskDrive WHERE MediaType='Fixed hard disk media'");
            foreach (ManagementObject obj in searcher.Get())
            {
                var serialNumber = obj["SerialNumber"]?.ToString()?.Trim();
                if (!string.IsNullOrWhiteSpace(serialNumber))
                {
                    return serialNumber;
                }
            }
        }
        catch
        {
            // 忽略异常
        }

        return "UNKNOWN";
    }

    /// <summary>
    /// 获取操作系统信息内部实现
    /// </summary>
    private string GetOSInfoInternal()
    {
        try
        {
            return $"{Environment.OSVersion.Platform} {Environment.OSVersion.Version}";
        }
        catch
        {
            return "UNKNOWN";
        }
    }

    /// <summary>
    /// 从硬件信息生成指纹
    /// </summary>
    private string GenerateFingerprintFromInfo(HardwareFingerprintInfo info)
    {
        var fingerprint = $"CPU-{info.CpuId}-MB-{info.MotherboardId}";
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(fingerprint));
        return Convert.ToHexString(hash)[..LicenseConstants.HardwareFingerprintLength].ToUpperInvariant();
    }

    #endregion
}
