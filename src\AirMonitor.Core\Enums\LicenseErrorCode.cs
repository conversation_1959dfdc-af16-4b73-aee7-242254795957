namespace AirMonitor.Core.Enums;

/// <summary>
/// 许可证错误代码枚举
/// 定义许可证验证过程中可能出现的错误类型
/// </summary>
public enum LicenseErrorCode
{
    /// <summary>
    /// 验证成功
    /// </summary>
    Success = 0,

    /// <summary>
    /// 许可证文件不存在
    /// </summary>
    FileNotFound = 1001,

    /// <summary>
    /// 许可证文件格式错误
    /// </summary>
    InvalidFormat = 1002,

    /// <summary>
    /// 数字签名验证失败
    /// </summary>
    InvalidSignature = 1003,

    /// <summary>
    /// 硬件指纹不匹配
    /// </summary>
    HardwareMismatch = 1004,

    /// <summary>
    /// 许可证已过期
    /// </summary>
    Expired = 1005,

    /// <summary>
    /// 试用期已过期
    /// </summary>
    TrialExpired = 1006,

    /// <summary>
    /// 产品版本不匹配
    /// </summary>
    VersionMismatch = 1007,

    /// <summary>
    /// 许可证已被撤销
    /// </summary>
    Revoked = 1008,

    /// <summary>
    /// 解密失败
    /// </summary>
    DecryptionFailed = 1009,

    /// <summary>
    /// 未知错误
    /// </summary>
    Unknown = 9999
}
