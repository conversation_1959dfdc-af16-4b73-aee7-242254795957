<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="ToggleButton.Checked.Border" Color="LightGray"/>

    <LinearGradientBrush x:Key="ToggleButtonBorderBrushChecked" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource PrimaryButtonBorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource PrimaryButtonBorder.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="WPFGlyphPrimaryToggleButtonStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="FocusVisualStyle"  Value="{x:Null}"/>
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Dark.ThemeBorderThicknessVariant1}"/>
        <Setter Property="IsTabStop" Value="false"/>
        <Setter Property="Focusable" Value="false"/>
        <Setter Property="ClickMode" Value="Press"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border Name="border"  
                            Background="{TemplateBinding Background}"       
                            BorderBrush="{TemplateBinding BorderBrush}"  
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Dark.CornerRadius4}">
                        <ContentPresenter x:Name="contentPresenter" Margin="{TemplateBinding Padding}" 
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          RecognizesAccessKey="True" 
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode"
                                 Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter Property="MinWidth"  Value="{StaticResource TouchMode.MinSize}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default"/>
                                <Condition Property="IsFocused" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background"  TargetName="border"  Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="BorderBrush"  TargetName="border"  Value="{StaticResource BorderAlt3}" />
                            <Setter Property="Foreground"   Value="{StaticResource IconColorHovered}" />
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver"   Value="True">
                            <Setter Property="BorderBrush"  TargetName="border"   Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Background"  TargetName="border" Value="{StaticResource ContentBackgroundHovered}" />
                            <Setter Property="Foreground"  Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger Property="IsPressed"   Value="True">
                            <Setter Property="Background"  TargetName="border"  Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter Property="BorderBrush"  TargetName="border" Value="{StaticResource ContentBackgroundPressed}" />
                            <Setter Property="Foreground"  Value="{StaticResource IconColorSelected}" />
                        </Trigger>
                        <Trigger Property="IsChecked"  Value="True">
                            <Setter Property="Background"  TargetName="border" Value="{StaticResource PrimaryBackground}" />
                            <Setter Property="BorderBrush"  TargetName="border"   Value="{StaticResource ToggleButtonBorderBrushChecked}" />
                            <Setter Property="Foreground"  Value="{StaticResource PrimaryForeground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background"  TargetName="border" Value="Transparent" />
                            <Setter Property="BorderBrush"  TargetName="border" Value="Transparent" />
                            <Setter Property="Foreground"  Value="{StaticResource IconColorDisabled}" />
                            <Setter Property="Opacity" Value="1" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary>
