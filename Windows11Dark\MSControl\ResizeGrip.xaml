<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="WPFResizeGripStyle" TargetType="{x:Type ResizeGrip}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="MinWidth" Value="{StaticResource {x:Static SystemParameters.VerticalScrollBarWidthKey}}"/>
        <Setter Property="MinHeight" Value="{StaticResource {x:Static SystemParameters.HorizontalScrollBarHeightKey}}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ResizeGrip}">
                    <Grid Background="{TemplateBinding Background}" 
                          SnapsToDevicePixels="true">
                        <TextBlock Margin="0,0,2,2"
                                   Text="&#xe705;"
                                   Foreground="{StaticResource IconColorDisabled}"
                                   FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Dark.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                   HorizontalAlignment="Right"
                                   VerticalAlignment="Bottom"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource WPFResizeGripStyle}" TargetType="{x:Type ResizeGrip}"/>
</ResourceDictionary>
