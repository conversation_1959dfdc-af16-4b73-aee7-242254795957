<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:pdfviewer="clr-namespace:Syncfusion.Windows.PdfViewer;assembly=Syncfusion.PdfViewer.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light" />
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphRepeatButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Slider.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphToggleButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Menu.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ComboBox.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ProgressBar.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Window.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ToolTip.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TreeView.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Hyperlink.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/UpDown/UpDown.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!--Notificationbar-->

    <!--Documentview-->

    <!--Search Bar-->

    <!--LoadingIndicator-->

    <!--Font dialogue box-->

    <!--Mark for Redaction-->

    <!--Document Toolbar-->

    <!--Thumbnail-->

    <!--Bookmark label-->

    <!--Organize page-->

    <!--PdfViewerControl fonts-->
    
    <!--Layer-->

    <Style x:Key="SyncfusionGlyphToggleButtonStyle" TargetType="ToggleButton" BasedOn="{StaticResource WPFGlyphToggleButtonStyle}">
        <Setter Property="IsTabStop" Value="True"/>
        <Setter Property="Focusable" Value="True"/>
    </Style>
    <Style x:Key="SyncfusionGlyphButtonStyle" TargetType="Button" BasedOn="{StaticResource WPFGlyphButtonStyle}">
        <Setter Property="BorderThickness" Value="0"/>
    </Style>
    <ControlTemplate x:Key="SyncfusionColorButtonTemplate" TargetType="{x:Type Button}">
        <Border x:Name="border"                             
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            SnapsToDevicePixels="true">
            <ContentPresenter x:Name="contentPresenter" 
                                          Focusable="False"
                                          Margin="{TemplateBinding Padding}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" 
                                          RecognizesAccessKey="True">
                <ContentPresenter.Resources>
                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                </ContentPresenter.Resources>
            </ContentPresenter>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Foreground" Value="Transparent"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="True">
                <Setter Property="Foreground" Value="Transparent"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Foreground" Value="Transparent"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    <Style x:Key="SyncfusionColorButtonStyle" TargetType="Button" BasedOn="{StaticResource WPFGlyphButtonStyle}">
        <Setter Property="Template" Value="{StaticResource SyncfusionColorButtonTemplate}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt3}"/>
    </Style>

    <Style x:Key="CustomStampMenuItemStyle" TargetType="{x:Type MenuItem}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type MenuItem}">
                    <Border
                        x:Name="templateRoot"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                        Effect="{StaticResource Default.ShadowDepth4}"
                        SnapsToDevicePixels="True"
                        Width="{TemplateBinding Width}"
                        HorizontalAlignment="{TemplateBinding HorizontalAlignment}">
                        <Grid x:Name="Grid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="10" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="20" />
                            </Grid.ColumnDefinitions>
                            <ContentPresenter
                                x:Name="Icon"
                                Width="Auto"
                                Height="16"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Content="{TemplateBinding Icon}"
                                ContentSource="Icon"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                            <Border
                                x:Name="GlyphPanel"
                                Width="22"
                                Height="22"
                                Margin="-1,0,0,0"
                                VerticalAlignment="Center"
                                Visibility="Hidden">
                                <Path
                                    x:Name="Glyph"
                                    Width="9"
                                    Height="11"
                                    Data="F1M10,1.2L4.7,9.1 4.5,9.1 0,5.2 1.3,3.5 4.3,6.1 8.3,0 10,1.2z"
                                    FlowDirection="LeftToRight" />
                            </Border>
                            <TextBlock
                                x:Name="ContentPresenter"
                                Grid.Column="2"
                                Margin="{TemplateBinding Padding}"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Text="{TemplateBinding Header}"
                                Foreground="{StaticResource PopupForeground}"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                            <Path
                                x:Name="RightArrow"
                                Grid.Column="3"
                                Height="9"
                                Width="6"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Fill="{StaticResource IconColor}">
                                <Path.Margin>
                                    <Thickness>-5,0,10,0</Thickness>
                                </Path.Margin>
                                <Path.Data>
                                    <PathGeometry>M0 8.49805C0 8.36133 0.0488281 8.24414 0.146484 8.14648L3.29297 5L0.146484 1.85352C0.0488281 1.75586 0 1.63867 0 1.50195C0 1.36523 0.046875 1.24805 0.140625 1.15039C0.238281 1.04883 0.357422 0.998047 0.498047 0.998047C0.634766 0.998047 0.751953 1.04688 0.849609 1.14453L4.35352 4.64844C4.45117 4.74609 4.5 4.86328 4.5 5C4.5 5.13672 4.45117 5.25391 4.35352 5.35156L0.849609 8.85547C0.751953 8.95312 0.634766 9.00195 0.498047 9.00195C0.357422 9.00195 0.238281 8.95312 0.140625 8.85547C0.046875 8.75391 0 8.63477 0 8.49805Z</PathGeometry>
                                </Path.Data>
                            </Path>
                            <Popup
                                x:Name="PART_Popup"
                                AllowsTransparency="True"
                                Focusable="False"
                                PlacementTarget="{Binding ElementName=templateRoot}"
                                Placement="Left"
                                MaxWidth="300"
                                Width="300"
                                Height="400"
                                IsOpen="{TemplateBinding IsHighlighted}">
                                <Border
                                    x:Name="SubMenuBorder"
                                    VerticalAlignment="Top"
                                    HorizontalAlignment="Left"
                                    Background="{StaticResource PopupBackground}"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                                    Effect="{StaticResource Default.ShadowDepth4}"
                                    BorderThickness="1">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition />
                                            <RowDefinition />
                                        </Grid.RowDefinitions>
                                        <ScrollViewer
                                            x:Name="SubMenuScrollViewer"
                                            Grid.Row="0"
                                            MaxHeight="400"
                                            VerticalScrollBarVisibility="Auto"
                                            Style="{StaticResource WPFMenuScrollViewer}">
                                            <Grid x:Name="Grid1">
                                                <ItemsPresenter
                                                    x:Name="ItemsPresenter"
                                                    Grid.IsSharedSizeScope="True"
                                                    KeyboardNavigation.DirectionalNavigation="Cycle"
                                                    KeyboardNavigation.TabNavigation="Cycle"
                                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                            </Grid>
                                        </ScrollViewer>
                                        <Rectangle
                                            Name="AddStampBorder"
                                            Height=".5"
                                            HorizontalAlignment="Stretch"
                                            VerticalAlignment="Bottom"
                                            Visibility="Hidden"
                                            Fill="{StaticResource BorderAlt}">
                                        </Rectangle>
                                        <MenuItem
                                            Grid.Row="1"
                                            Height="28"
                                            x:Name="PART_Browse" 
                                            Header="Browse"
                                            Style="{StaticResource WPFMenuItemStyle}">
                                            <MenuItem.Icon>
                                                <Path
                                                    x:Name="BrowseIcon"
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"
                                                    Data="M8,0 L8,6 L14,6 L14,8 L8,8 L8,14 L6,14 L6,7.999 L-5.68434189e-14,8 L-5.68434189e-14,6 L6,5.999 L6,0 L8,0 Z"
                                                    Fill="{TemplateBinding Foreground}"/>
                                            </MenuItem.Icon>
                                        </MenuItem>
                                    </Grid>
                                </Border>
                            </Popup>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsHighlighted" Value="True">
                            <Setter TargetName="templateRoot" Property="Background" Value="{StaticResource PopupHoveredBackground}" />
                            <Setter TargetName="templateRoot" Property="BorderBrush" Value="{StaticResource PopupHoveredBackground}" />
                            <Setter TargetName="ContentPresenter" Property="Foreground" Value="{StaticResource PopupHoveredForeground}" />
                            <Setter TargetName="RightArrow" Property="Fill" Value="{StaticResource IconColorHovered}" />
                        </Trigger>
                        <Trigger Property="HasItems" Value="True">
                            <Setter TargetName="AddStampBorder" Property="Visibility" Value="Visible"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ThumbnailBorderStyle" TargetType="{x:Type Border}">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="2,2,2,2" />
        <Style.Triggers>
            <DataTrigger Binding="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Grid}}, Path=IsMouseOver}" Value="true">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt1}" />
            </DataTrigger>
            <Trigger Property="Focusable" Value="true">
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBackground}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="OrganizePageBorderStyle" TargetType="{x:Type Border}">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="2,2,2,2" />
        <Setter Property="Background" Value="Transparent" />
        <Style.Triggers>
            <DataTrigger Binding="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Grid}}, Path=IsMouseOver}" Value="true">
                <Setter Property="BorderBrush" Value="{StaticResource BorderAlt1}" />
            </DataTrigger>
            <Trigger Property="Focusable" Value="true">
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBackground}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="LayerGridStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="true">
                <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
            </Trigger>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Background" Value="{StaticResource ContentBackgroundHovered}" />
                <Setter Property="Foreground" Value="{StaticResource HoveredForeground}" />
            </Trigger>
            <Trigger Property="Focusable" Value="true">
                <Setter Property="Background" Value="{StaticResource ContentBackgroundSelected}" />
                <Setter Property="Foreground" Value="{StaticResource SelectedForeground}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="{StaticResource DisabledForeground}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="LayerPathStyle" TargetType="{x:Type Path}">
        <Setter Property="Fill" Value="{StaticResource ContentForeground}" />
    </Style>

    <Style x:Key="MenuIconStyle" TargetType="{x:Type Path}">
        <Setter Property="Fill" Value="{StaticResource IconColor}" />
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" Value="{StaticResource IconColorDisabled}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionPdfViewerControlStyle" TargetType="{x:Type pdfviewer:PdfViewerControl}">
        <Setter Property="TextSelectionBrushColor" Value="#009EFF" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:PdfViewerControl}">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <StackPanel
                            x:Name="container"
                            Width="{TemplateBinding Width}"
                            Height="{TemplateBinding Height}">
                            <pdfviewer:DocumentToolbar
                                x:Name="PART_Toolbar"
                                Width="Auto"
                                Height="Auto" />
                            <pdfviewer:RedactionToolbar
                                x:Name="PART_RedactToolbar"
                                Width="Auto"
                                Height="40" />
                            <Grid
                                Name="PART_Grid"
                                Width="{TemplateBinding Width}"
                                Height="{TemplateBinding Height}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="40" />
                                    <ColumnDefinition Width="0" />
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="0"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition />
                                    <RowDefinition />
                                </Grid.RowDefinitions>
                                <pdfviewer:OutlinePane 
                                    x:Name="PART_OutlinePane" 
                                    Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="0"/>
                                <pdfviewer:ThumbnailPane
                                    x:Name="PART_ThumbnailPanel"
                                    Grid.Row="1"
                                    Grid.Column="1" />
                                <pdfviewer:BookmarkLabel 
                                    x:Name="PART_BookmarkLabel" 
                                    Grid.Row="0"
                                    Grid.Column="1"
                                    Height="30" />
                                <pdfviewer:BookmarkPane
                                    x:Name="PART_Tree"
                                    Grid.Row="1"
                                    Grid.Column="1"
                                    ScrollViewer.VerticalScrollBarVisibility="Visible" />
                                <pdfviewer:LayerPane 
                                    x:Name="PART_LayerPane" 
                                    Grid.Row="1"
                                    Grid.Column="1" />
                                <pdfviewer:CommentsPane
                                    x:Name="PART_CommentPanels"
                                    Grid.Row="1"
                                    Grid.Column="3" />
                                <pdfviewer:CommentsLabel
                                    x:Name="PART_CommentsLabel"
                                    Grid.Row="0"
                                    Grid.Column="3"
                                    Height="30" />
                                <pdfviewer:PdfDocumentView
                                    x:Name="PART_DocumentView"
                                    Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="2"
                                    ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                    ScrollViewer.VerticalScrollBarVisibility="Auto" />

                                <pdfviewer:NotificationBar
                                    x:Name="PART_NotificationBar"
                                    Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="2"
                                    Width="278"
                                    Height="Auto"
                                    Margin="0,8,8,8"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Top" />
                                <StackPanel
                                    x:Name="PART_FontPopUp"
                                    Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="2"
                                    Visibility="Collapsed">
                                    <pdfviewer:FontPropertiesDialog
                                        x:Name="PART_FreeText"
                                        Width="Auto"
                                        Height="Auto"
                                        Margin="700,-2,0,0"
                                        VerticalAlignment="Top" />
                                </StackPanel>
                                <pdfviewer:TextSearchBar
                                    x:Name="PART_TextSearchBar"
                                    Grid.Column="2"
                                    Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Width="Auto"
                                    Height="Auto"
                                    Margin="5,5,25,25"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Top" />

                                <StackPanel
                                    x:Name="PART_AddSignaturePanel"
                                    Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="2"
                                    Visibility="Collapsed">
                                    <pdfviewer:SignaturePanel
                                        x:Name="PART_AddSignatureDialog"
                                        Width="Auto"
                                        Height="Auto"
                                        Margin="700,-2,0,0"
                                        VerticalAlignment="Top" />
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionCopyProgressIndicatorStyle" TargetType="{x:Type pdfviewer:CopyProgressIndicator}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:CopyProgressIndicator}">
                    <Border
                        Name="SearchProgressBarBorder"
                        Background="{StaticResource PopupBackground}"
                        BorderBrush="{StaticResource BorderAlt1}"
                        BorderThickness="1"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                        Effect="{StaticResource Default.ShadowDepth4}">

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="9*" />
                                <ColumnDefinition Width="1*" />
                            </Grid.ColumnDefinitions>
                            <ProgressBar
                                Name="PART_ProgressBar"
                                Grid.Column="0"
                                Width="208"
                                Height="3"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Style="{StaticResource WPFProgressBarStyle}" />
                            <Button
                                Name="PART_CloseButton"
                                Grid.Column="1"
                                Width="12"
                                Height="12"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                BorderBrush="Transparent"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                <Path
                                    Width="10"
                                    Height="10"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Top"
                                    Data="M6.17773 5.5625L10.2451 9.62988C10.3317 9.71647 10.375 9.81901 10.375 9.9375C10.375 10.056 10.3317 10.1585 10.2451 10.2451C10.1585 10.3317 10.056 10.375 9.9375 10.375C9.81901 10.375 9.71647 10.3317 9.62988 10.2451L5.5625 6.17773L1.49512 10.2451C1.40853 10.3317 1.30599 10.375 1.1875 10.375C1.06901 10.375 0.966471 10.3317 0.879883 10.2451C0.793294 10.1585 0.75 10.056 0.75 9.9375C0.75 9.81901 0.793294 9.71647 0.879883 9.62988L4.94727 5.5625L0.879883 1.49512C0.793294 1.40853 0.75 1.30599 0.75 1.1875C0.75 1.06901 0.793294 0.966471 0.879883 0.879883C0.966471 0.793294 1.06901 0.75 1.1875 0.75C1.30599 0.75 1.40853 0.793294 1.49512 0.879883L5.5625 4.94727L9.62988 0.879883C9.71647 0.793294 9.81901 0.75 9.9375 0.75C10.056 0.75 10.1585 0.793294 10.2451 0.879883C10.3317 0.966471 10.375 1.06901 10.375 1.1875C10.375 1.30599 10.3317 1.40853 10.2451 1.49512L6.17773 5.5625Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                    Stretch="Fill" />
                            </Button>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionDocumentViewStyle" TargetType="{x:Type pdfviewer:DocumentView}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:DocumentView}">
                    <Grid Name="homegrid" Background="{StaticResource ContentBackgroundAlt2}">
                        <ItemsControl
                            Name="PART_ItemsControl"
                            ItemsSource="{Binding}"
                            ScrollViewer.HorizontalScrollBarVisibility="Auto"
                            ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ItemsControl.Template>
                                <ControlTemplate TargetType="{x:Type ItemsControl}">
                                    <ScrollViewer
                                        Name="PART_ScrollViewer"
                                        CanContentScroll="True"
                                        FocusVisualStyle="{x:Null}">
                                        <ItemsPresenter />
                                    </ScrollViewer>
                                </ControlTemplate>
                            </ItemsControl.Template>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <ItemsControl ItemsSource="{Binding}">
                                        <ItemsControl.Template>
                                            <ControlTemplate TargetType="{x:Type ItemsControl}">
                                                <ItemsPresenter />
                                            </ControlTemplate>
                                        </ItemsControl.Template>
                                    </ItemsControl>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <pdfviewer:CustomVPanel IsItemsHost="True" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                        </ItemsControl>

                        <pdfviewer:PdfProgressIndicator
                            x:Name="PART_ProgressIndicator"
                            Width="127"
                            Height="46"
                            Margin="5,5,25,25"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Bottom"
                            PageBeingInitialized="{Binding PageBeingInitialized, UpdateSourceTrigger=PropertyChanged, RelativeSource={RelativeSource Mode=Self}}"
                            PageCount="{Binding PageCount, UpdateSourceTrigger=PropertyChanged, RelativeSource={RelativeSource Mode=Self}}"
                            Visibility="Hidden" />

                        <pdfviewer:PdfLoadingIndicator x:Name="PART_Loader" Visibility="Hidden" />

                        <pdfviewer:CopyProgressIndicator
                            x:Name="PART_CopyProgressIndicator"
                            Width="262"
                            Height="46"
                            Margin="5,5,25,25"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Bottom"
                            Visibility="Collapsed" />

                        <pdfviewer:TextSearchProgressIndicator
                            x:Name="PART_SearchProgresIndicator"
                            Margin="5,5,25,25"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Bottom"
                            Visibility="Collapsed" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionPdfLoadingIndicatorStyle" TargetType="{x:Type pdfviewer:PdfLoadingIndicator}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:PdfLoadingIndicator}">
                    <Grid>
                        <Viewbox
                            Width="50"
                            Height="50"
                            Margin="108,100,92,100"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            RenderTransformOrigin="0.52,0.477">
                            <Canvas
                                Width="45"
                                Height="45"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Top">
                                <TextBlock
                                    Name="PART_Loadertext"
                                    Margin="0,62"
                                    HorizontalAlignment="Center"
                                    FontSize="12"
                                    Foreground="{StaticResource PrimaryBackground}"
                                    Text="{Binding LoadingMessage, UpdateSourceTrigger=PropertyChanged, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=UserControl}}" />

                                <Path
                                    Name="PART_PathA"
                                    Data="M50,27.5 C50,24.23333 45,24.23333 45,27.5 C45,30.83333 50,30.83333 50,27.5"
                                    Fill="{StaticResource PrimaryBackground}"
                                    RenderTransformOrigin="0.5,0.83333">
                                    <Path.RenderTransform>
                                        <RotateTransform x:Name="RotateA" Angle="0" />
                                    </Path.RenderTransform>
                                    <Path.Triggers>
                                        <EventTrigger RoutedEvent="Path.Loaded">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <DoubleAnimationUsingKeyFrames
                                                        RepeatBehavior="Forever"
                                                        Storyboard.TargetName="RotateA"
                                                        Storyboard.TargetProperty="Angle">
                                                        <LinearDoubleKeyFrame KeyTime="0:0:0" Value="360" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.0" Value="0" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.5" Value="0" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.55" Value="0" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                    </Path.Triggers>
                                </Path>
                                <Path
                                    Name="PART_PathB"
                                    Data="M50,27.5 C50,24.23333 45,24.23333 45,27.5 C45,30.83333 50,30.83333 50,27.5"
                                    Fill="{StaticResource PrimaryBackground}"
                                    RenderTransformOrigin="0.5,0.83333">
                                    <Path.RenderTransform>
                                        <RotateTransform x:Name="RotateB" Angle="13" />
                                    </Path.RenderTransform>
                                    <Path.Triggers>
                                        <EventTrigger RoutedEvent="Path.Loaded">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <DoubleAnimationUsingKeyFrames
                                                        RepeatBehavior="Forever"
                                                        Storyboard.TargetName="RotateB"
                                                        Storyboard.TargetProperty="Angle">
                                                        <LinearDoubleKeyFrame KeyTime="0:0:0" Value="0" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:0.1" Value="0" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.1" Value="-347" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.5" Value="-360" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.55" Value="-360" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                    </Path.Triggers>
                                </Path>
                                <Path
                                    Name="PART_PathC"
                                    Data="M50,27.5 C50,24.23333 45,24.23333 45,27.5 C45,30.83333 50,30.83333 50,27.5"
                                    Fill="{StaticResource PrimaryBackground}"
                                    RenderTransformOrigin="0.5,0.83333">
                                    <Path.RenderTransform>
                                        <RotateTransform x:Name="RotateC" Angle="26" />
                                    </Path.RenderTransform>
                                    <Path.Triggers>
                                        <EventTrigger RoutedEvent="Path.Loaded">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <DoubleAnimationUsingKeyFrames
                                                        RepeatBehavior="Forever"
                                                        Storyboard.TargetName="RotateC"
                                                        Storyboard.TargetProperty="Angle">
                                                        <LinearDoubleKeyFrame KeyTime="0:0:0" Value="0" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:0.2" Value="0" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.2" Value="-334" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.5" Value="-360" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.55" Value="-360" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                    </Path.Triggers>
                                </Path>

                                <Path
                                    Name="PART_PathD"
                                    Data="M50,27.5 C50,24.23333 45,24.23333 45,27.5 C45,30.83333 50,30.83333 50,27.5"
                                    Fill="{StaticResource PrimaryBackground}"
                                    RenderTransformOrigin="0.5,0.83333">
                                    <Path.RenderTransform>
                                        <RotateTransform x:Name="RotateD" Angle="39" />
                                    </Path.RenderTransform>
                                    <Path.Triggers>
                                        <EventTrigger RoutedEvent="Path.Loaded">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <DoubleAnimationUsingKeyFrames
                                                        RepeatBehavior="Forever"
                                                        Storyboard.TargetName="RotateD"
                                                        Storyboard.TargetProperty="Angle">
                                                        <LinearDoubleKeyFrame KeyTime="0:0:0" Value="0" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:0.3" Value="0" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.3" Value="-321" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.5" Value="-360" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.55" Value="-360" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                    </Path.Triggers>
                                </Path>

                                <Path
                                    Name="PART_PathE"
                                    Data="M50,27.5 C50,24.23333 45,24.23333 45,27.5 C45,30.83333 50,30.83333 50,27.5"
                                    Fill="{StaticResource PrimaryBackground}"
                                    RenderTransformOrigin="0.5,0.83333">
                                    <Path.RenderTransform>
                                        <RotateTransform x:Name="RotateE" Angle="52" />
                                    </Path.RenderTransform>
                                    <Path.Triggers>
                                        <EventTrigger RoutedEvent="Path.Loaded">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <DoubleAnimationUsingKeyFrames
                                                        RepeatBehavior="Forever"
                                                        Storyboard.TargetName="RotateE"
                                                        Storyboard.TargetProperty="Angle">
                                                        <LinearDoubleKeyFrame KeyTime="0:0:0" Value="0" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:0.4" Value="0" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.4" Value="-308" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.5" Value="-360" />
                                                        <LinearDoubleKeyFrame KeyTime="0:0:1.55" Value="-360" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                    </Path.Triggers>
                                </Path>
                            </Canvas>
                        </Viewbox>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionPdfProgressIndicatorStyle" TargetType="{x:Type pdfviewer:PdfProgressIndicator}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:PdfProgressIndicator}">
                    <Border
                        Background="{StaticResource PopupBackground}"
                        BorderBrush="{StaticResource BorderAlt1}"
                        BorderThickness="2"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                        Effect="{StaticResource Default.ShadowDepth4}">
                        <Grid Margin="12,2,0,0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="15" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <TextBlock
                                Name="PART_LoadingPage"
                                Grid.Row="0"
                                Margin="10,0,0,0"
                                FontSize="10"
                                Foreground="{StaticResource PrimaryBackground}"
                                Text="Loading page" />
                            <StackPanel Grid.Row="1" Orientation="Horizontal">
                                <TextBlock
                                    Name="PART_PageBeingInitializedLabel"
                                    Margin="10,0,0,0"
                                    FontSize="14"
                                    FontWeight="Bold"
                                    Foreground="{StaticResource PrimaryBackground}"
                                    Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=PageBeingInitialized}" />

                                <TextBlock
                                    Name="PART_of"
                                    Margin="2,0"
                                    FontSize="14"
                                    Foreground="{StaticResource PrimaryBackground}"
                                    Text=" of " />
                                <TextBlock
                                    Name="PART_PageCountLabel"
                                    FontSize="14"
                                    FontWeight="Bold"
                                    Foreground="{StaticResource PrimaryBackground}"
                                    Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=PageCount}" />
                            </StackPanel>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionNotificationBarStyle" TargetType="{x:Type pdfviewer:NotificationBar}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:NotificationBar}">
                    <Border
                        Width="Auto"
                        Padding="8,8,8,8"
                        Background="{StaticResource PopupBackground}"
                        BorderBrush="{StaticResource BorderAlt}"
                        BorderThickness="1"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                        Effect="{StaticResource Default.ShadowDepth4}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Padding="16,8,0,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                FontSize="14"
                                FontWeight="Bold"
                                Foreground="{StaticResource ContentForeground}"
                                Text="Notification" />
                            <Button
                                Name="PART_button1"
                                Grid.Row="0"
                                Width="14"
                                Height="14"
                                Margin="0,0,15,0"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                <Path
                                    Width="11"
                                    Height="11"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Top"
                                    Data="M6.17773 5.5625L10.2451 9.62988C10.3317 9.71647 10.375 9.81901 10.375 9.9375C10.375 10.056 10.3317 10.1585 10.2451 10.2451C10.1585 10.3317 10.056 10.375 9.9375 10.375C9.81901 10.375 9.71647 10.3317 9.62988 10.2451L5.5625 6.17773L1.49512 10.2451C1.40853 10.3317 1.30599 10.375 1.1875 10.375C1.06901 10.375 0.966471 10.3317 0.879883 10.2451C0.793294 10.1585 0.75 10.056 0.75 9.9375C0.75 9.81901 0.793294 9.71647 0.879883 9.62988L4.94727 5.5625L0.879883 1.49512C0.793294 1.40853 0.75 1.30599 0.75 1.1875C0.75 1.06901 0.793294 0.966471 0.879883 0.879883C0.966471 0.793294 1.06901 0.75 1.1875 0.75C1.30599 0.75 1.40853 0.793294 1.49512 0.879883L5.5625 4.94727L9.62988 0.879883C9.71647 0.793294 9.81901 0.75 9.9375 0.75C10.056 0.75 10.1585 0.793294 10.2451 0.879883C10.3317 0.966471 10.375 1.06901 10.375 1.1875C10.375 1.30599 10.3317 1.40853 10.2451 1.49512L6.17773 5.5625Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                    Stretch="Fill" />
                            </Button>
                            <Label
                                Name="PART_label1"
                                Grid.Row="1"
                                Width="Auto"
                                Margin="0,8,0,8"
                                Padding="16,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center">
                                Label
                            </Label>
                            <TextBlock
                                Name="PART_LinkLabel"
                                Grid.Row="2"
                                Padding="16,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                FontSize="{StaticResource Windows11Light.BodyTextStyle}">
                                <Hyperlink Style="{StaticResource WPFHyperlinkStyle}">
                                    Copy exception details to the clipboard
                                </Hyperlink>
                            </TextBlock>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionTextSearchBarStyle" TargetType="{x:Type pdfviewer:TextSearchBar}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:TextSearchBar}">
                    <Border
                        Width="310"
                        Height="90"
                        Padding="8px"
                        Background="{StaticResource PopupBackground}"
                        BorderBrush="{StaticResource BorderAlt}"
                        BorderThickness="2"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                        Effect="{StaticResource Default.ShadowDepth4}">
                        <Grid
                            Name="searchBarGrid"
                            Width="285"
                            Height="67">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="8*" />
                                <ColumnDefinition Width="4.25*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="7*" />
                                <RowDefinition Height="7*" />
                            </Grid.RowDefinitions>
                            <TextBox
                                Name="PART_TextSearch"
                                Grid.Column="0"
                                Width="Auto"
                                Height="24"
                                Style="{StaticResource WPFTextBoxStyle}" />
                            <StackPanel Orientation="Horizontal" Grid.Column="1">
                                <Button
                                Name="PART_ButtonPrevious"
                                Grid.Column="1"
                                Width="24"
                                Height="24"
                                Margin="8,0,0,0"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                    <Button.ToolTip>
                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                            Previous
                                        </ToolTip>
                                    </Button.ToolTip>
                                    <Path
                                    Width="11"
                                    Height="7"
                                    Data="M0.5 4.875C0.5 4.77344 0.537109 4.68555 0.611328 4.61133L4.73633 0.486328C4.81055 0.412109 4.89844 0.375 5 0.375C5.10156 0.375 5.18945 0.412109 5.26367 0.486328L9.38867 4.61133C9.46289 4.68555 9.5 4.77344 9.5 4.875C9.5 4.97656 9.46289 5.06445 9.38867 5.13867C9.31445 5.21289 9.22656 5.25 9.125 5.25C9.02344 5.25 8.93555 5.21289 8.86133 5.13867L5 1.2832L1.13867 5.13867C1.06445 5.21289 0.976562 5.25 0.875 5.25C0.773438 5.25 0.685547 5.21289 0.611328 5.13867C0.537109 5.06445 0.5 4.97656 0.5 4.875Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}" 
                                        Stretch="Fill"/>
                                </Button>
                                <Button
                                Name="PART_ButtonNext"
                                Grid.Column="2"
                                Width="24"
                                Height="24"
                                Margin="8,0,0,0"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                    <Button.ToolTip>
                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                            Next
                                        </ToolTip>
                                    </Button.ToolTip>
                                    <Path
                                    Width="11"
                                    Height="7"
                                    Data="M0.5 1.125C0.5 1.02344 0.537109 0.935547 0.611328 0.861328C0.685547 0.787109 0.773438 0.75 0.875 0.75C0.976562 0.75 1.06445 0.787109 1.13867 0.861328L5 4.7168L8.86133 0.861328C8.93555 0.787109 9.02344 0.75 9.125 0.75C9.22656 0.75 9.31445 0.787109 9.38867 0.861328C9.46289 0.935547 9.5 1.02344 9.5 1.125C9.5 1.22656 9.46289 1.31445 9.38867 1.38867L5.26367 5.51367C5.18945 5.58789 5.10156 5.625 5 5.625C4.89844 5.625 4.81055 5.58789 4.73633 5.51367L0.611328 1.38867C0.537109 1.31445 0.5 1.22656 0.5 1.125Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                    Stretch="Fill"/>
                                </Button>
                                <Button
                                Name="PART_ButtonClose"
                                Grid.Column="3"
                                Width="24"
                                Height="24"
                                Margin="8,0,0,0"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                    <Button.ToolTip>
                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                            Close Search Bar
                                        </ToolTip>
                                    </Button.ToolTip>
                                    <Path
                                    Width="9"
                                    Height="9"
                                    Data="M6.17773 5.5625L10.2451 9.62988C10.3317 9.71647 10.375 9.81901 10.375 9.9375C10.375 10.056 10.3317 10.1585 10.2451 10.2451C10.1585 10.3317 10.056 10.375 9.9375 10.375C9.81901 10.375 9.71647 10.3317 9.62988 10.2451L5.5625 6.17773L1.49512 10.2451C1.40853 10.3317 1.30599 10.375 1.1875 10.375C1.06901 10.375 0.966471 10.3317 0.879883 10.2451C0.793294 10.1585 0.75 10.056 0.75 9.9375C0.75 9.81901 0.793294 9.71647 0.879883 9.62988L4.94727 5.5625L0.879883 1.49512C0.793294 1.40853 0.75 1.30599 0.75 1.1875C0.75 1.06901 0.793294 0.966471 0.879883 0.879883C0.966471 0.793294 1.06901 0.75 1.1875 0.75C1.30599 0.75 1.40853 0.793294 1.49512 0.879883L5.5625 4.94727L9.62988 0.879883C9.71647 0.793294 9.81901 0.75 9.9375 0.75C10.056 0.75 10.1585 0.793294 10.2451 0.879883C10.3317 0.966471 10.375 1.06901 10.375 1.1875C10.375 1.30599 10.3317 1.40853 10.2451 1.49512L6.17773 5.5625Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                    StrokeThickness="2"
                                        Stretch="Fill"/>
                                </Button>
                            </StackPanel>
                            <CheckBox
                                Name="Check"
                                Grid.Row="1"
                                Margin="0,4,0,0" />
                            <Label
                                Name ="MatchCaseLabel"
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="16,6,4,4"
                                Content="Match Case" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="MatchCaseLabel" Property="Margin" Value="16,8,0,0"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionTextSearchProgressIndicatorStyle" TargetType="{x:Type pdfviewer:TextSearchProgressIndicator}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:TextSearchProgressIndicator}">
                    <Border
                        Name="SearchProgressBarBorder"
                        Background="{StaticResource PopupBackground}"
                        BorderBrush="{StaticResource BorderAlt1}"
                        BorderThickness="1"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                        Effect="{StaticResource Default.ShadowDepth4}">
                        <Grid Width="251" Height="45">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="230*" />
                                <ColumnDefinition Width="34*" />
                            </Grid.ColumnDefinitions>
                            <ProgressBar
                                Name="PART_SearchProgressBar"
                                Width="208"
                                Height="3"
                                Margin="12,12,0,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Top"
                                Style="{StaticResource WPFProgressBarStyle}" />
                            <Button
                                Name="PART_CloseButton"
                                Grid.Column="1"
                                Width="12"
                                Height="12"
                                Margin="6,8,0,18"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Top"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                <Button.BorderBrush>
                                    <SolidColorBrush />
                                </Button.BorderBrush>
                                <Path
                                    Width="10"
                                    Height="10"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Top"
                                    Data="M6.17773 5.5625L10.2451 9.62988C10.3317 9.71647 10.375 9.81901 10.375 9.9375C10.375 10.056 10.3317 10.1585 10.2451 10.2451C10.1585 10.3317 10.056 10.375 9.9375 10.375C9.81901 10.375 9.71647 10.3317 9.62988 10.2451L5.5625 6.17773L1.49512 10.2451C1.40853 10.3317 1.30599 10.375 1.1875 10.375C1.06901 10.375 0.966471 10.3317 0.879883 10.2451C0.793294 10.1585 0.75 10.056 0.75 9.9375C0.75 9.81901 0.793294 9.71647 0.879883 9.62988L4.94727 5.5625L0.879883 1.49512C0.793294 1.40853 0.75 1.30599 0.75 1.1875C0.75 1.06901 0.793294 0.966471 0.879883 0.879883C0.966471 0.793294 1.06901 0.75 1.1875 0.75C1.30599 0.75 1.40853 0.793294 1.49512 0.879883L5.5625 4.94727L9.62988 0.879883C9.71647 0.793294 9.81901 0.75 9.9375 0.75C10.056 0.75 10.1585 0.793294 10.2451 0.879883C10.3317 0.966471 10.375 1.06901 10.375 1.1875C10.375 1.30599 10.3317 1.40853 10.2451 1.49512L6.17773 5.5625Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                    Stretch="Fill" />
                            </Button>
                            <Label
                                Name="PART_ProgressLabel"
                                Grid.Column="0"
                                Width="170"
                                Height="29"
                                Margin="6,16,0,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Top"
                                Content="Searching"
                                Foreground="{StaticResource PrimaryBackground}" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="PART_CloseButton" Property="MinHeight" Value="18"/>
                            <Setter TargetName="PART_ProgressLabel" Property="Margin" Value="6,20,0,0"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionFontPropertiesDialogStyle" TargetType="{x:Type pdfviewer:FontPropertiesDialog}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:FontPropertiesDialog}">
                    <Border
                        Width="180"
                        Height="100"
                        Padding="8,8,8,8"
                        Background="{StaticResource PopupBackground}"
                        BorderBrush="{StaticResource BorderAlt}"
                        BorderThickness="{StaticResource Windows11Light.BorderThickness1}"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                        Effect="{StaticResource Default.ShadowDepth4}">
                        <Grid Name="freeTextGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="3*" />
                                <ColumnDefinition Width="2*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="3*" />
                                <RowDefinition Height="2*" />
                                <RowDefinition Height="4*" />
                            </Grid.RowDefinitions>
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                VerticalAlignment="Top"
                                FontSize="14"
                                FontWeight="SemiBold"
                                Foreground="{StaticResource ContentForeground}"
                                Text="Font" />
                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,0,2"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Bottom"
                                Foreground="{StaticResource ContentForeground}"
                                Text="Font size" />
                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0,0,0,2"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Bottom"
                                Foreground="{StaticResource ContentForeground}"
                                Text="Font color" />
                            <ComboBox
                                Name="FontSizeMenu"
                                Grid.Row="2"
                                Grid.Column="0"
                                Width="80"
                                Height="24"
                                Margin="0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                IsEditable="True"
                                Style="{StaticResource WPFComboBoxStyle}" />
                            <Button
                                Grid.Column="1"
                                Grid.Row="2"
                                Name="FontColorButton"
                                Width="24"
                                Height="24"
                                BorderThickness="1"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Style="{StaticResource SyncfusionColorButtonStyle}"
                                Margin="0">
                            </Button>
                            <Button
                                Name="Part_FreeTextClose"
                                Grid.Row="0"
                                Grid.Column="1"
                                Width="24"
                                Height="24"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Top"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                <Button.ToolTip>
                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                        Close Font PopUp
                                    </ToolTip>
                                </Button.ToolTip>
                                <Path
                                    Canvas.Left="0"
                                    Canvas.Top="3.095"
                                    Width="11"
                                    Height="11"
                                    Data="M6.17773 5.5625L10.2451 9.62988C10.3317 9.71647 10.375 9.81901 10.375 9.9375C10.375 10.056 10.3317 10.1585 10.2451 10.2451C10.1585 10.3317 10.056 10.375 9.9375 10.375C9.81901 10.375 9.71647 10.3317 9.62988 10.2451L5.5625 6.17773L1.49512 10.2451C1.40853 10.3317 1.30599 10.375 1.1875 10.375C1.06901 10.375 0.966471 10.3317 0.879883 10.2451C0.793294 10.1585 0.75 10.056 0.75 9.9375C0.75 9.81901 0.793294 9.71647 0.879883 9.62988L4.94727 5.5625L0.879883 1.49512C0.793294 1.40853 0.75 1.30599 0.75 1.1875C0.75 1.06901 0.793294 0.966471 0.879883 0.879883C0.966471 0.793294 1.06901 0.75 1.1875 0.75C1.30599 0.75 1.40853 0.793294 1.49512 0.879883L5.5625 4.94727L9.62988 0.879883C9.71647 0.793294 9.81901 0.75 9.9375 0.75C10.056 0.75 10.1585 0.793294 10.2451 0.879883C10.3317 0.966471 10.375 1.06901 10.375 1.1875C10.375 1.30599 10.3317 1.40853 10.2451 1.49512L6.17773 5.5625Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                    Stretch="Fill" />
                            </Button>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="FontColorButton" Property="MinHeight" Value="18"/>
                            <Setter TargetName="Part_FreeTextClose" Property="MinHeight" Value="14"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionRedactionToolbarStyle" TargetType="{x:Type pdfviewer:RedactionToolbar}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:RedactionToolbar}">
                    <Border
                        Background="{StaticResource ContentBackgroundAlt1}"
                        BorderBrush="{StaticResource BorderAlt}"
                        BorderThickness="0,0,0,1"
                        KeyboardNavigation.TabNavigation="Cycle">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Label
                                x:Name="PART_TextLabel"
                                Grid.Column="0"
                                Margin="10,0,0,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                VerticalContentAlignment="Center"
                                Content="Redaction"
                                FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                FontSize="12"
                                FontWeight="{StaticResource Windows11Light.FontWeightMedium}" />
                            <StackPanel
                                Name="redactToolBar"
                                Grid.Column="2"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Stretch"
                                Orientation="Horizontal">
                                <ToggleButton
                                    Name="PART_MarkRedactButton"
                                    Height="28"
                                    Style="{StaticResource WPFGlyphTextToggleButtonStyle}">
                                    <ToggleButton.ToolTip>
                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                            Click to mark content for permanent removal.
                                        </ToolTip>
                                    </ToggleButton.ToolTip>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <Path
                                            Grid.Column="0"
                                            Width="15"
                                            Height="17"
                                            Margin="12,3,4,0"
                                            VerticalAlignment="Center"
                                            Data="M1.93103 1C1.39779 1 0.965517 1.44772 0.965517 2V14C0.965517 14.5523 1.39779 15 1.93103 15H4.82759C5.09421 15 5.31034 15.2239 5.31034 15.5C5.31034 15.7761 5.09421 16 4.82759 16H1.93103C0.864554 16 0 15.1046 0 14V2C0 0.895431 0.864553 0 1.93103 0H7.63418C8.12478 0 8.59698 0.193402 8.95489 0.540926L11.9414 3.44077C12.3308 3.81887 12.5517 4.34703 12.5517 4.89985V6.5C12.5517 6.77614 12.3356 7 12.069 7C11.8023 7 11.5862 6.77614 11.5862 6.5V5.5H8.68966C7.88979 5.5 7.24138 4.82843 7.24138 4V1H1.93103ZM11.281 4.17031C11.3778 4.26425 11.4537 4.37671 11.5057 4.5H8.68966C8.42303 4.5 8.2069 4.27614 8.2069 4V1.19492C8.23738 1.21818 8.26665 1.24339 8.29454 1.27046L11.281 4.17031ZM13.8966 9.35375C13.9655 9.17457 14 8.99012 14 8.8004C14 8.61858 13.9681 8.44862 13.9043 8.29051C13.8404 8.13241 13.7511 7.99539 13.6362 7.87945C13.5239 7.76087 13.3911 7.66864 13.2379 7.60277C13.0847 7.53426 12.9201 7.5 12.7439 7.5C12.5754 7.5 12.4108 7.53426 12.2499 7.60277C12.0916 7.66864 11.9525 7.7635 11.8325 7.88735L6.88476 12.9941C6.85412 13.0257 6.82859 13.0626 6.80817 13.1047C6.78774 13.1443 6.77115 13.1864 6.75838 13.2312L6.28352 15.1877C6.27841 15.2141 6.27586 15.2352 6.27586 15.251C6.27586 15.3195 6.30012 15.3788 6.34862 15.4289C6.39713 15.4763 6.45457 15.5 6.52095 15.5C6.53372 15.5 6.55286 15.4974 6.57839 15.4921L8.47401 15.002C8.51741 14.9888 8.55825 14.9717 8.59655 14.9506C8.6374 14.9295 8.67314 14.9032 8.70378 14.8715L13.5979 9.82016C13.7281 9.68577 13.8277 9.5303 13.8966 9.35375Z"
                                            Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                            FlowDirection="LeftToRight" />

                                        <Label
                                            x:Name="PART_MarkForRedactionLabel"
                                            Grid.Column="1"
                                            Margin="4,0,12,0"
                                            VerticalAlignment="Center"
                                            Content="Mark for Redaction"
                                            FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                            FontSize="12"
                                            FontWeight="{StaticResource Windows11Light.FontWeightMedium}"
                                            Foreground="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}" />
                                    </Grid>
                                </ToggleButton>
                                <ToggleButton
                                    Name="PART_ApplyRedactButton"
                                    Height="28"
                                    Margin="8,0,0,0"
                                    Style="{StaticResource WPFGlyphTextToggleButtonStyle}">
                                    <ToggleButton.ToolTip>
                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                            Click to permanently remove marked content.
                                        </ToolTip>
                                    </ToggleButton.ToolTip>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <Path
                                            Grid.Column="0"
                                            Width="17"
                                            Height="17"
                                            Margin="12,3,4,0"
                                            VerticalAlignment="Center"
                                            Data="M11.6652 0.750193C12.6569 -0.250064 14.2646 -0.250064 15.2562 0.750193C16.2479 1.75045 16.2479 3.37219 15.2562 4.37245L13.375 6.27007C13.0325 6.11249 12.6695 5.99219 12.2913 5.9143L14.538 3.648C15.133 3.04784 15.133 2.0748 14.538 1.47464C13.9431 0.87449 12.9784 0.87449 12.3834 1.47464L2.48976 11.4544C2.48166 11.4626 2.47385 11.4711 2.46633 11.4797C2.57142 11.4946 2.6977 11.5159 2.83337 11.546C3.16843 11.6203 3.67834 11.7719 4.01152 12.108C4.3447 12.444 4.49501 12.9584 4.56865 13.2964C4.59597 13.4218 4.61597 13.5392 4.63049 13.6396C4.6405 13.631 4.6502 13.6219 4.65955 13.6125L6.29433 11.9635C6.37155 12.345 6.49082 12.7111 6.64703 13.0566L5.37775 14.3369C5.20403 14.5122 4.99089 14.6424 4.75648 14.7166L0.792954 15.9708C0.312578 16.1228 -0.132979 15.655 0.0368468 15.1769L1.41462 11.2983C1.49053 11.0846 1.61244 10.8905 1.77156 10.73L11.6652 0.750193ZM3.64977 13.993C3.64751 13.9678 3.64406 13.9329 3.63904 13.8907C3.62747 13.7933 3.60799 13.6602 3.57664 13.5163C3.50722 13.1977 3.40361 12.9437 3.29332 12.8324C3.18303 12.7212 2.93118 12.6166 2.61534 12.5466C2.47268 12.515 2.34067 12.4953 2.24415 12.4837C2.19623 12.4779 2.15792 12.4742 2.13245 12.4719L2.10451 12.4697L2.09866 12.4693C2.09185 12.4689 2.0849 12.4683 2.07817 12.4676L1.26866 14.7465L3.64977 13.993ZM13.2162 9.68075C13.3717 9.44535 13.3087 9.1273 13.0753 8.97037C12.8419 8.81343 12.5266 8.87704 12.3711 9.11244L10.6833 11.6661L9.5978 10.5712C9.39947 10.3711 9.07793 10.3711 8.8796 10.5712C8.68127 10.7712 8.68127 11.0956 8.8796 11.2956L10.3155 12.744C10.5875 13.0183 11.0402 12.9731 11.2535 12.6504L13.2162 9.68075ZM11.2701 6.32301C8.74581 6.32301 6.69948 8.38715 6.69948 10.9334C6.69948 13.4796 8.74581 15.5438 11.2701 15.5438C13.7943 15.5438 15.8407 13.4796 15.8407 10.9334C15.8407 8.38715 13.7943 6.32301 11.2701 6.32301ZM7.71517 10.9334C7.71517 8.95298 9.30675 7.34754 11.2701 7.34754C13.2334 7.34754 14.825 8.95298 14.825 10.9334C14.825 12.9138 13.2334 14.5192 11.2701 14.5192C9.30675 14.5192 7.71517 12.9138 7.71517 10.9334Z"
                                            Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                            FlowDirection="LeftToRight" />

                                        <Label
                                            x:Name="PART_ApplyRedactionLabel"
                                            Grid.Column="1"
                                            Margin="4,0,12,0"
                                            VerticalAlignment="Center"
                                            Content="Apply"
                                            FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                            FontSize="12"
                                            FontWeight="{StaticResource Windows11Light.FontWeightMedium}"
                                            Foreground="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}" />
                                    </Grid>
                                </ToggleButton>
                                <ToggleButton
                                    Name="PART_RedactPropertiesButton"
                                    Height="28"
                                    Margin="8,0,0,0"
                                    Style="{StaticResource WPFGlyphTextToggleButtonStyle}">
                                    <ToggleButton.ToolTip>
                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                            Click to view redaction tool properties.
                                        </ToolTip>
                                    </ToggleButton.ToolTip>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <Path
                                            Grid.Column="0"
                                            Width="17"
                                            Height="17"
                                            Margin="12,3,4,0"
                                            VerticalAlignment="Center"
                                            Data="M14.9126 0.747576C13.938 -0.249192 12.358 -0.249192 11.3834 0.747576L1.66825 10.6841C1.50388 10.8522 1.38004 11.0571 1.30653 11.2827L0.0325026 15.1919C-0.123568 15.6707 0.321874 16.1263 0.79009 15.9667L4.61219 14.6636C4.83271 14.5885 5.03309 14.4618 5.19745 14.2937L6.46162 13.0007C6.30868 12.6557 6.19217 12.2901 6.11711 11.9092L4.82739 13.2283L4.80386 13.182C4.60637 12.796 4.31296 12.286 3.97207 11.9374C3.63118 11.5887 3.13256 11.2886 2.75514 11.0866L2.70987 11.0626L12.0892 1.4695C12.674 0.871438 13.622 0.871438 14.2068 1.4695C14.7915 2.06756 14.7915 3.03721 14.2068 3.63527L12.0011 5.89113C12.3736 5.9679 12.731 6.08706 13.0683 6.24349L14.9126 4.35719C15.8872 3.36042 15.8872 1.74434 14.9126 0.747576ZM3.9194 13.6553C3.94467 13.7047 3.96868 13.7529 3.9913 13.7991L1.23202 14.7399L2.15178 11.9177C2.19701 11.9408 2.24406 11.9654 2.29236 11.9912C2.65517 12.1854 3.03885 12.4267 3.26623 12.6593C3.4936 12.8918 3.72957 13.2843 3.9194 13.6553ZM10.0114 10.8952C10.0114 10.3314 10.4584 9.87428 11.0097 9.87428C11.561 9.87428 12.0079 10.3314 12.0079 10.8952C12.0079 11.4591 11.561 11.9162 11.0097 11.9162C10.4584 11.9162 10.0114 11.4591 10.0114 10.8952ZM11.0097 5.79047C11.2853 5.79047 11.5088 6.01902 11.5088 6.30095V7.35809C12.1007 7.44468 12.6447 7.683 13.102 8.03331L13.833 7.28559C14.028 7.08624 14.344 7.08624 14.5389 7.28559C14.7338 7.48495 14.7338 7.80816 14.5389 8.00752L13.8078 8.75523C14.1503 9.22295 14.3833 9.77938 14.468 10.3848H15.5016C15.7772 10.3848 16.0007 10.6133 16.0007 10.8952C16.0007 11.1772 15.7772 11.4057 15.5016 11.4057H14.468C14.3833 12.0111 14.1503 12.5675 13.8078 13.0352L14.5389 13.783C14.7338 13.9823 14.7338 14.3055 14.5389 14.5049C14.344 14.7042 14.028 14.7042 13.833 14.5049L13.102 13.7572C12.6447 14.1075 12.1007 14.3458 11.5088 14.4324V15.4895C11.5088 15.7715 11.2853 16 11.0097 16C10.734 16 10.5106 15.7715 10.5106 15.4895V14.4324C9.91866 14.3458 9.37463 14.1075 8.91733 13.7572L8.18627 14.5049C7.99136 14.7042 7.67534 14.7042 7.48043 14.5049C7.28551 14.3055 7.28551 13.9823 7.48043 13.783L8.21148 13.0352C7.86899 12.5675 7.63597 12.0111 7.55131 11.4057H6.51771C6.24207 11.4057 6.01861 11.1772 6.01861 10.8952C6.01861 10.6133 6.24207 10.3848 6.51771 10.3848H7.55131C7.63597 9.77938 7.86899 9.22295 8.21148 8.75523L7.48043 8.00752C7.28551 7.80816 7.28551 7.48495 7.48043 7.28559C7.67534 7.08624 7.99136 7.08624 8.18627 7.28559L8.91733 8.03331C9.37463 7.683 9.91866 7.44468 10.5106 7.35809V6.30095C10.5106 6.01902 10.734 5.79047 11.0097 5.79047ZM8.51413 10.8952C8.51413 9.48559 9.63142 8.34285 11.0097 8.34285C12.3879 8.34285 13.5052 9.48559 13.5052 10.8952C13.5052 12.3049 12.3879 13.4476 11.0097 13.4476C9.63142 13.4476 8.51413 12.3049 8.51413 10.8952Z"
                                            Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                            FlowDirection="LeftToRight" />

                                        <Label
                                            x:Name="PART_PropertiesLabel"
                                            Grid.Column="1"
                                            Margin="4,0,12,0"
                                            VerticalAlignment="Center"
                                            Content="Properties"
                                            FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                            FontSize="12"
                                            FontWeight="{StaticResource Windows11Light.FontWeightMedium}"
                                            Foreground="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}" />
                                    </Grid>
                                </ToggleButton>
                                <ToggleButton
                                    Name="PART_ImportData"
                                    Height="26"
                                    Margin="8,0,0,0"
                                    Style="{StaticResource WPFGlyphTextToggleButtonStyle}">
                                    <ToggleButton.ToolTip>
                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                            Import data into a Form file
                                        </ToolTip>
                                    </ToggleButton.ToolTip>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <Path
                                            Grid.Column="0"
                                            Width="15"
                                            Height="9"
                                            Margin="12,0,4,0"
                                            VerticalAlignment="Center"
                                            Data="M8.67075 0.123718C8.87857 -0.0581225 9.19445 -0.0370637 9.37629 0.170755L12.8056 4.08994C13.0096 4.32309 13.0096 4.67693 12.8056 4.91007L9.37629 8.82926C9.19445 9.03708 8.87857 9.05814 8.67075 8.8763C8.46293 8.69445 8.44187 8.37857 8.62371 8.17076L11.3981 5.00001L0.5 5.00001C0.223858 5.00001 0 4.77615 0 4.50001C2.98023e-08 4.22386 0.223858 4.00001 0.5 4.00001L11.3981 4.00001L8.62371 0.829259C8.44187 0.621441 8.46293 0.305559 8.67075 0.123718ZM14.5 6.83638e-06C14.7761 6.83638e-06 15 0.223864 15 0.500007V8.50001C15 8.77615 14.7761 9.00001 14.5 9.00001C14.2239 9.00001 14 8.77615 14 8.50001V0.500007C14 0.223864 14.2239 6.83638e-06 14.5 6.83638e-06Z"
                                            Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                            FlowDirection="LeftToRight" />

                                        <Label
                                            x:Name="PART_FormImportLabel"
                                            Grid.Column="1"
                                            Margin="4,0,12,0"
                                            Padding="0"
                                            VerticalAlignment="Center"
                                            Content="Import"
                                            FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                            FontSize="12"
                                            FontWeight="{StaticResource Windows11Light.FontWeightMedium}"
                                            Foreground="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}" />
                                    </Grid>
                                </ToggleButton>

                                <ToggleButton
                                    Name="PART_ExportData"
                                    Height="26"
                                    Margin="8,0,0,0"
                                    Style="{StaticResource WPFGlyphTextToggleButtonStyle}">
                                    <ToggleButton.ToolTip>
                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                            Export data from a Form file
                                        </ToolTip>
                                    </ToggleButton.ToolTip>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <Path
                                            Grid.Column="0"
                                            Width="15"
                                            Height="9"
                                            Margin="12,0,4,0"
                                            VerticalAlignment="Center"
                                            Data="M10.6707 0.123718C10.8786 -0.0581225 11.1944 -0.0370637 11.3763 0.170755L14.8056 4.08994C15.0096 4.32309 15.0096 4.67693 14.8056 4.91007L11.3763 8.82926C11.1944 9.03708 10.8786 9.05814 10.6707 8.8763C10.4629 8.69445 10.4419 8.37857 10.6237 8.17076L13.3981 5.00001L2.5 5.00001C2.22386 5.00001 2 4.77615 2 4.50001C2 4.22386 2.22386 4.00001 2.5 4.00001L13.3981 4.00001L10.6237 0.829259C10.4419 0.621441 10.4629 0.305559 10.6707 0.123718ZM0.5 6.83638e-06C0.776142 6.83638e-06 1 0.223864 1 0.500007V8.50001C1 8.77615 0.776142 9.00001 0.5 9.00001C0.223858 9.00001 0 8.77615 0 8.50001V0.500007C0 0.223864 0.223858 6.83638e-06 0.5 6.83638e-06Z"
                                            Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                            FlowDirection="LeftToRight" />

                                        <Label
                                            x:Name="PART_FormExportLabel"
                                            Grid.Column="1"
                                            Margin="4,0,12,0"
                                            Padding="0"
                                            VerticalAlignment="Center"
                                            Content="Export"
                                            FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                            FontSize="12"
                                            FontWeight="{StaticResource Windows11Light.FontWeightMedium}"
                                            Foreground="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}" />
                                    </Grid>
                                </ToggleButton>
                            </StackPanel>

                            <Button
                                Name="PART_CloseRedactToolbar"
                                Grid.Column="4"
                                Width="24"
                                Height="24"
                                Margin="0,0,8,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                IsEnabled="True"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                <Path
                                    Canvas.Left="0"
                                    Canvas.Top="3.095"
                                    Width="12"
                                    Height="12"
                                    Data="M6.17773 5.5625L10.2451 9.62988C10.3317 9.71647 10.375 9.81901 10.375 9.9375C10.375 10.056 10.3317 10.1585 10.2451 10.2451C10.1585 10.3317 10.056 10.375 9.9375 10.375C9.81901 10.375 9.71647 10.3317 9.62988 10.2451L5.5625 6.17773L1.49512 10.2451C1.40853 10.3317 1.30599 10.375 1.1875 10.375C1.06901 10.375 0.966471 10.3317 0.879883 10.2451C0.793294 10.1585 0.75 10.056 0.75 9.9375C0.75 9.81901 0.793294 9.71647 0.879883 9.62988L4.94727 5.5625L0.879883 1.49512C0.793294 1.40853 0.75 1.30599 0.75 1.1875C0.75 1.06901 0.793294 0.966471 0.879883 0.879883C0.966471 0.793294 1.06901 0.75 1.1875 0.75C1.30599 0.75 1.40853 0.793294 1.49512 0.879883L5.5625 4.94727L9.62988 0.879883C9.71647 0.793294 9.81901 0.75 9.9375 0.75C10.056 0.75 10.1585 0.793294 10.2451 0.879883C10.3317 0.966471 10.375 1.06901 10.375 1.1875C10.375 1.30599 10.3317 1.40853 10.2451 1.49512L6.17773 5.5625Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                    Stretch="Fill" />
                            </Button>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionDocumentToolbarStyle" TargetType="{x:Type pdfviewer:DocumentToolbar}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:DocumentToolbar}">
                    <StackPanel>
                        <Border Background="{StaticResource ContentBackgroundAlt1}"
                                BorderBrush="{StaticResource BorderAlt}"
                                BorderThickness="0,0,0,1px"
                                KeyboardNavigation.TabNavigation="Cycle">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="auto"></ColumnDefinition>
                                    <ColumnDefinition Width="*"></ColumnDefinition>
                                    <ColumnDefinition Width="auto"></ColumnDefinition>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0"
                                            Orientation="Horizontal">
                                    <ToggleButton Name="PART_FileToggleButton"
                                                  Width="34"
                                                  Height="24"
                                                  Margin="8,0,0,0"
                                                  FlowDirection="LeftToRight"
                                                  IsEnabled="True"
                                                  Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                        <StackPanel Orientation="Horizontal">
                                            <Path Width="12"
                                                  Height="16"
                                                  Margin="0,2,0,0"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"
                                                  Data="M7 3.5V1H1.5C1.22386 1 1 1.22386 1 1.5V14.5C1 14.7761 1.22386 15 1.5 15H10.5C10.7761 15 11 14.7761 11 14.5V5H8.5C7.67157 5 7 4.32843 7 3.5ZM8 1.41421V3.5C8 3.77614 8.22386 4 8.5 4H10.5858L8 1.41421ZM1.5 0C0.671573 0 0 0.671573 0 1.5V14.5C0 15.3284 0.671573 16 1.5 16H10.5C11.3284 16 12 15.3284 12 14.5V4.62132C12 4.2235 11.842 3.84196 11.5607 3.56066L8.43934 0.43934C8.15804 0.158035 7.7765 0 7.37868 0H1.5Z"
                                                  Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                  FlowDirection="LeftToRight"
                                                  Stretch="Uniform" />
                                            <Path Width="10"
                                                  Height="5"
                                                  Margin="4,3,0,0"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"
                                                  Data="M0 0.538462C0 0.392628 0.0494792 0.266426 0.148438 0.159856C0.247396 0.0532853 0.364583 0 0.5 0C0.635417 0 0.752604 0.0532853 0.851562 0.159856L6 5.69591L11.1484 0.159856C11.2474 0.0532853 11.3646 0 11.5 0C11.6354 0 11.7526 0.0532853 11.8516 0.159856C11.9505 0.266426 12 0.392628 12 0.538462C12 0.684295 11.9505 0.810497 11.8516 0.917067L6.35156 6.84014C6.2526 6.94671 6.13542 7 6 7C5.86458 7 5.7474 6.94671 5.64844 6.84014L0.148438 0.917067C0.0494792 0.810497 0 0.684295 0 0.538462Z"
                                                  Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                  Stretch="Uniform" />
                                        </StackPanel>
                                        <ToggleButton.ContextMenu>
                                            <ContextMenu Style="{StaticResource WPFContextMenuStyle}">
                                                <MenuItem Name="PART_OpenMenuItem"
                                                          Height="28"
                                                          FlowDirection="LeftToRight"
                                                          IsEnabled="True">
                                                    <MenuItem.Icon>
                                                        <Path HorizontalAlignment="Center"
                                                              Height="15"
                                                              Width="15"
                                                              Data="M1.15829 1.1451C1.06937 1.26157 1 1.44431 1 1.66667V9.21281L1.62142 5.95664C1.74734 5.2968 2.24256 4.66667 2.97656 4.66667H12.4684V4C12.4684 3.77765 12.399 3.59491 12.3101 3.47843C12.2216 3.3624 12.1349 3.33333 12.0778 3.33333H7.10311C6.68402 3.33333 6.31985 3.11455 6.07592 2.79502L4.81642 1.1451C4.7263 1.02705 4.63742 1 4.58411 1H1.3906C1.33355 1 1.24686 1.02906 1.15829 1.1451ZM13.4684 4.66667V4C13.4684 3.57802 13.3384 3.17743 13.105 2.87165C12.8712 2.56544 12.5126 2.33333 12.0778 2.33333H7.10311C7.04979 2.33333 6.96091 2.30629 6.87079 2.18823L5.61129 0.538318C5.36737 0.218787 5.0032 0 4.58411 0H1.3906C0.955791 0 0.597172 0.232104 0.363417 0.538318C0.129995 0.844095 0 1.24469 0 1.66667V13.3333C0 13.7553 0.129995 14.1559 0.363417 14.4617C0.597172 14.7679 0.955791 15 1.3906 15H12.5231C12.835 15 13.0823 14.8332 13.2354 14.6325C13.3125 14.5316 13.3711 14.4168 13.4102 14.2943C13.5154 14.1141 13.5894 13.9135 13.6282 13.71L14.9641 6.71002C15.0566 6.22523 14.9661 5.73791 14.7404 5.35936C14.5162 4.98307 14.1219 4.66667 13.609 4.66667H13.4684ZM12.2731 14C12.3564 14 12.5736 13.9014 12.6459 13.5226L13.9818 6.52256C14.0298 6.27102 13.9781 6.03352 13.8814 5.87134C13.7834 5.7069 13.6754 5.66667 13.609 5.66667H2.97656C2.89323 5.66667 2.676 5.76523 2.60369 6.1441L1.26779 13.1441C1.21978 13.3957 1.27152 13.6331 1.36818 13.7953C1.46619 13.9598 1.57422 14 1.64066 14H12.2731Z"
                                                              Stretch="Uniform"
                                                              Style="{StaticResource MenuIconStyle}" />
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                                <MenuItem Name="PART_SaveMenuItem"
                                                          Height="28"
                                                          FlowDirection="LeftToRight"
                                                          IsEnabled="False">
                                                    <MenuItem.Icon>
                                                        <Path HorizontalAlignment="Center"
                                                              Height="15"
                                                              Width="15"
                                                              Data="M1.5 1C1.22386 1 1 1.22386 1 1.5V13.5C1 13.7761 1.22386 14 1.5 14H2L2 9.5C2 8.67157 2.67157 8 3.5 8L11.5 8C12.3284 8 13 8.67157 13 9.5V14H13.5C13.7761 14 14 13.7761 14 13.5V3.45993C14 3.31397 13.9362 3.1753 13.8254 3.08031L11.5388 1.12037C11.4482 1.0427 11.3328 1 11.2134 1H11V3.5C11 4.32843 10.3284 5 9.5 5H5.5C4.67157 5 4 4.32843 4 3.5V1H1.5ZM5 1V3.5C5 3.77614 5.22386 4 5.5 4H9.5C9.77614 4 10 3.77614 10 3.5V1H5ZM12 14V9.5C12 9.22386 11.7761 9 11.5 9L3.5 9C3.22386 9 3 9.22386 3 9.5L3 14H12ZM0 1.5C0 0.671574 0.671573 0 1.5 0H11.2134C11.5715 0 11.9177 0.128088 12.1896 0.361115L14.4762 2.32105C14.8087 2.60602 15 3.02205 15 3.45993V13.5C15 14.3284 14.3284 15 13.5 15H1.5C0.671574 15 0 14.3284 0 13.5V1.5Z"
                                                              Stretch="Uniform"
                                                              Style="{StaticResource MenuIconStyle}" />
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                                <MenuItem Name="PART_SaveAsMenuItem"
                                                          Height="28"
                                                          FlowDirection="LeftToRight"
                                                          IsEnabled="True">
                                                    <MenuItem.Icon>
                                                        <Path HorizontalAlignment="Center"
                                                              Height="14"
                                                              Width="14"
                                                              Data="M1.5 1C1.22386 1 1 1.22386 1 1.5V12.5047C1 12.7809 1.22386 13.0047 1.5 13.0047H1.77795L1.77795 8.93128C1.77795 8.10285 2.44952 7.43128 3.27795 7.43128L9.30486 7.43128L8.34785 8.43128L3.27795 8.43128C3.0018 8.43128 2.77795 8.65514 2.77795 8.93128L2.77795 13.0047H6.13616L6.19091 14.0047H1.5C0.671574 14.0047 0 13.3332 0 12.5047V1.5C0 0.671574 0.671572 0 1.5 0H9.97048C10.3398 0 10.6961 0.136246 10.9712 0.382642L12.2421 1.52091L12.9464 2.15172C13.2641 2.43627 13.4457 2.84259 13.4457 3.26908V5.58124C13.1179 5.4981 12.7808 5.4652 12.4457 5.48254V3.26908C12.4457 3.12692 12.3852 2.99148 12.2793 2.89663L11.575 2.26582L10.3041 1.12755C10.2124 1.04542 10.0936 1 9.97048 1H9.88977V3.21564C9.88977 4.04407 9.2182 4.71564 8.38977 4.71564H5.05591C4.22748 4.71564 3.55591 4.04407 3.55591 3.21564V1H1.5ZM4.55591 1H8.88977V3.21564C8.88977 3.49178 8.66592 3.71564 8.38977 3.71564H5.05591C4.77977 3.71564 4.55591 3.49178 4.55591 3.21564V1ZM13.6106 6.84566C13.0899 6.30151 12.2456 6.30151 11.7248 6.84566L7.87817 10.8651C7.7583 10.9904 7.67165 11.146 7.62673 11.3168L7.1993 12.9421C7.10286 13.3089 7.42528 13.6457 7.77623 13.545L9.33166 13.0983C9.49513 13.0514 9.6441 12.9609 9.76398 12.8356L13.6106 8.81617C14.1314 8.27203 14.1314 7.3898 13.6106 6.84566Z"
                                                              Stretch="Uniform"
                                                              Style="{StaticResource MenuIconStyle}" />
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                                <MenuItem Name="PART_PrintMenuItem"
                                                          Height="28"
                                                          FlowDirection="LeftToRight"
                                                          IsEnabled="True">
                                                    <MenuItem.Icon>
                                                        <Path HorizontalAlignment="Center"
                                                              Height="15"
                                                              Width="15"
                                                              Data="M3.00122 1.66667C3.00122 0.821279 3.60326 0 4.50122 0H10.5012C11.3992 0 12.0012 0.821279 12.0012 1.66667V2.33333H12.5012C13.9515 2.33333 15.0012 3.67695 15.0012 5.16667V11C15.0012 11.8454 14.3992 12.6667 13.5012 12.6667H12.0012V13.3333C12.0012 14.1787 11.3992 15 10.5012 15H4.50122C3.60326 15 3.00122 14.1787 3.00122 13.3333V12.6667H1.50122C0.603261 12.6667 0.0012207 11.8454 0.0012207 11V5.16667C0.0012207 3.67695 1.05098 2.33333 2.50122 2.33333H3.00122V1.66667ZM4.00122 2.33333H11.0012V1.66667C11.0012 1.22339 10.7078 1 10.5012 1H4.50122C4.29461 1 4.00122 1.22339 4.00122 1.66667V2.33333ZM3.00122 11.6667V9.83333C3.00122 8.98795 3.60326 8.16667 4.50122 8.16667H10.5012C11.3992 8.16667 12.0012 8.98795 12.0012 9.83333V11.6667H13.5012C13.7078 11.6667 14.0012 11.4433 14.0012 11V5.16667C14.0012 4.07906 13.2601 3.33333 12.5012 3.33333H2.50122C1.74233 3.33333 1.00122 4.07906 1.00122 5.16667V11C1.00122 11.4433 1.29461 11.6667 1.50122 11.6667H3.00122ZM4.50122 9.16667C4.29461 9.16667 4.00122 9.39006 4.00122 9.83333V13.3333C4.00122 13.7766 4.29461 14 4.50122 14H10.5012C10.7078 14 11.0012 13.7766 11.0012 13.3333V9.83333C11.0012 9.39006 10.7078 9.16667 10.5012 9.16667H4.50122Z"
                                                              Stretch="Uniform"
                                                              Style="{StaticResource MenuIconStyle}" />
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                            </ContextMenu>
                                        </ToggleButton.ContextMenu>
                                    </ToggleButton>
                                </StackPanel>
                                <Grid Grid.Column="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="auto"></ColumnDefinition>
                                        <ColumnDefinition Width="*"></ColumnDefinition>
                                        <ColumnDefinition Width="auto"></ColumnDefinition>
                                    </Grid.ColumnDefinitions>
                                    <RepeatButton x:Name="PART_PrimaryLeftScrollButton"
                                                  Width="24"
                                                  Grid.Column="0"
                                                  Style="{StaticResource WPFGlyphRepeatButtonStyle}"
                                                  Background="{StaticResource ContentBackgroundAlt1}">
                                        <RepeatButton.Margin>
                                            <Thickness>-2,-3,0,-2</Thickness>
                                        </RepeatButton.Margin>
                                        <RepeatButton.Effect>
                                            <DropShadowEffect BlurRadius="16"
                                                              ShadowDepth="4.5"
                                                              Direction="0"
                                                              Opacity=".62" />
                                        </RepeatButton.Effect>
                                        <Path Width="6"
                                              Height="11"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Data="M5.5 10L1 5.5L5.5 1"
                                              Stroke="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type RepeatButton}}}"
                                              StrokeThickness="1" />
                                    </RepeatButton>
                                    <ScrollViewer x:Name="PART_PrimaryToolbar_Scrollviewer"
                                                  Grid.Column="1"
                                                  HorizontalScrollBarVisibility="Hidden"
                                                  VerticalScrollBarVisibility="Disabled">
                                        <StackPanel Height="40"
                                                    Orientation="Horizontal"
                                                    HorizontalAlignment="Center">
                                            <StackPanel Orientation="Horizontal">
                                                <Button Name="PART_ButtonGoToFirstPage"
                                                        Width="24"
                                                        Height="24"
                                                        Margin="8,0,0,0"
                                                        IsEnabled="False"
                                                        Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                                    <Button.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Click to go to first page in the document.
                                                        </ToolTip>
                                                    </Button.ToolTip>
                                                    <Path Width="14"
                                                          Height="14"
                                                          HorizontalAlignment="Left"
                                                          VerticalAlignment="Top"
                                                          Data="M14 1.50696C14 1.12828 13.5442 0.846518 13.1647 1.08573L3.66119 7.07745C3.34276 7.2782 3.34276 7.71914 3.66119 7.9199L13.1647 13.9116C13.5442 14.1508 14 13.8691 14 13.4904V1.50696ZM12.6314 0.239818C13.6318 -0.390908 15 0.288549 15 1.50696V13.4904C15 14.7088 13.6318 15.3883 12.6314 14.7575L3.12787 8.76581C2.18721 8.17275 2.1872 6.82459 3.12787 6.23153L12.6314 0.239818ZM0.5 0.455614C0.776142 0.455614 1 0.679471 1 0.955614L1 14.0417C1 14.3179 0.776144 14.5417 0.5 14.5417C0.223859 14.5417 1.19209e-06 14.3179 1.19209e-06 14.0417L0 0.955614C0 0.679472 0.223858 0.455614 0.5 0.455614Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                          Stretch="Fill" />
                                                </Button>

                                                <Button Name="PART_ButtonGoToPreviousPage"
                                                        Width="24"
                                                        Height="24"
                                                        Margin="8,0,0,0"
                                                        IsEnabled="False"
                                                        Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                                    <Button.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Click to go to previous page in the document.
                                                        </ToolTip>
                                                    </Button.ToolTip>
                                                    <Path Width="11"
                                                          Height="14"
                                                          HorizontalAlignment="Left"
                                                          VerticalAlignment="Top"
                                                          Data="M10 1.01786L1.00152 6.98905C1.00149 6.98907 1.00147 6.98909 1.00144 6.9891C1.00131 6.98954 1.00117 6.99006 1.00102 6.99067C1.00044 6.99311 1 6.99633 1 7C1 7.00367 1.00044 7.00689 1.00102 7.00933C1.00117 7.00994 1.00131 7.01046 1.00144 7.0109C1.00147 7.01091 1.00149 7.01093 1.00152 7.01095L10 12.9821L10 1.01786ZM9.9989 13.0041C9.99889 13.0042 9.9989 13.0041 9.99891 13.0041L9.9989 13.0041ZM0.448601 6.15582C-0.149534 6.55273 -0.149534 7.44727 0.448601 7.84418L9.47801 13.8359C10.1335 14.2709 11 13.7903 11 12.9917L11 1.00828C11 0.209744 10.1335 -0.270868 9.47801 0.164101L0.448601 6.15582Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                          Stretch="Fill" />
                                                </Button>

                                                <TextBox Name="PART_TextCurrentPageIndex"
                                                         Width="30"
                                                         Height="24"
                                                         Margin="13,0,0,0"
                                                         Style="{StaticResource WPFTextBoxStyle}"
                                                         TextAlignment="Center">
                                                    <TextBox.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Current page index
                                                        </ToolTip>
                                                    </TextBox.ToolTip>
                                                </TextBox>
                                                <TextBlock Name="PART_PageSeparator"
                                                           Margin="3,0,0,0"
                                                           VerticalAlignment="Center"
                                                           FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                                           FontSize="9pt"
                                                           Foreground="{StaticResource ContentForegroundAlt1}"
                                                           Text="/" />
                                                <TextBlock Name="PART_LableTotalPageCount"
                                                           Margin="3,0,0,0"
                                                           VerticalAlignment="Center"
                                                           FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                                           FontSize="9pt"
                                                           Foreground="{StaticResource ContentForegroundAlt1}"
                                                           Text="100">
                                                    <TextBlock.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Total page count
                                                        </ToolTip>
                                                    </TextBlock.ToolTip>
                                                </TextBlock>

                                                <Button Name="PART_ButtonGoToNextPage"
                                                        Width="24"
                                                        Height="24"
                                                        Margin="8,0,0,0"
                                                        IsEnabled="True"
                                                        Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                                    <Button.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Click to go to next page in the document.
                                                        </ToolTip>
                                                    </Button.ToolTip>
                                                    <Path Width="11"
                                                          Height="14"
                                                          HorizontalAlignment="Left"
                                                          VerticalAlignment="Top"
                                                          Data="M1 12.9821L9.99848 7.01095C9.99851 7.01093 9.99853 7.01091 9.99856 7.0109C9.99869 7.01046 9.99883 7.00994 9.99898 7.00933C9.99956 7.00689 10 7.00367 10 7C10 6.99633 9.99956 6.99312 9.99898 6.99067C9.99883 6.99007 9.99869 6.98954 9.99856 6.9891C9.99853 6.98909 9.99851 6.98907 9.99848 6.98905L1 1.01786L1 12.9821ZM10.5514 7.84418C11.1495 7.44728 11.1495 6.55273 10.5514 6.15582L1.52199 0.164101C0.866504 -0.270868 0 0.209742 0 1.00828V12.9917C0 13.7903 0.866504 14.2709 1.52199 13.8359L10.5514 7.84418Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                          Stretch="Fill" />
                                                </Button>

                                                <Button Name="PART_ButtonGoToLastPage"
                                                        Width="24"
                                                        Height="24"
                                                        Margin="8,0,0,0"
                                                        IsEnabled="True"
                                                        Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                                    <Button.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Click to go to last page in the document.
                                                        </ToolTip>
                                                    </Button.ToolTip>
                                                    <Path Width="14"
                                                          Height="14"
                                                          HorizontalAlignment="Left"
                                                          VerticalAlignment="Top"
                                                          Data="M1.83526 1.08573C1.45584 0.846518 1 1.12828 1 1.50696L1 13.4904C1 13.8691 1.45584 14.1508 1.83525 13.9116L11.3388 7.9199C11.6572 7.71914 11.6572 7.2782 11.3388 7.07745L1.83526 1.08573ZM5.36442e-07 1.50696C5.96046e-07 0.288546 1.36817 -0.390907 2.36858 0.239818L11.8721 6.23153C12.8128 6.82459 12.8128 8.17275 11.8721 8.76581L2.36858 14.7575C1.36817 15.3883 -5.96046e-08 14.7088 0 13.4904L5.36442e-07 1.50696ZM14.5 0.455615C14.7761 0.455615 15 0.679473 15 0.955615V14.0417C15 14.3179 14.7761 14.5417 14.5 14.5417C14.2239 14.5417 14 14.3179 14 14.0417V0.955615C14 0.679473 14.2239 0.455615 14.5 0.455615Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                          Stretch="Fill" />
                                                </Button>
                                                <Rectangle Width="1.6"
                                                           Name="PART_CursorToolsSeparator"
                                                           Height="21.599"
                                                           Margin="8,0,0,0"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"
                                                           Fill="{StaticResource PopupBorder}" />
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal">
                                                <ToggleButton Name="PART_SelectTool"
                                                              Width="24"
                                                              Height="24"
                                                              Margin="8,0,0,0"
                                                              IsChecked="True"
                                                              Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                                    <ToggleButton.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Selection tool for text
                                                        </ToolTip>
                                                    </ToggleButton.ToolTip>
                                                    <Path Width="13"
                                                          Height="15"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Data="M4.72699 9.76025C5.01696 9.82626 5.2625 10.0179 5.39696 10.2831L7.25376 13.9458C7.25638 13.951 7.27199 13.9738 7.31336 13.9897C7.35473 14.0056 7.38164 13.9991 7.38704 13.997L9.30487 13.2571L7.62996 9.95323C7.47864 9.65475 7.48666 9.30038 7.6513 9.00905C7.81594 8.71772 8.11539 8.52805 8.44914 8.50371L11.4734 8.28311L2.44705 1.03193C2.42745 1.01618 2.40916 1.00768 2.39344 1.00347L1.60053 11.57C1.60769 11.5774 1.62022 11.588 1.63961 11.5982C1.67051 11.6144 1.69697 11.6177 1.70912 11.6172C1.70968 11.6172 1.71018 11.6172 1.71063 11.6171L3.89671 9.94162C4.13274 9.76071 4.43703 9.69424 4.72699 9.76025ZM11.7144 9.26819C12.6099 9.20286 12.9153 8.15869 12.2056 7.58859L3.07333 0.252329C2.42262 -0.270414 1.46104 0.0652087 1.40325 0.835243L0.601854 11.5149C0.537464 12.373 1.6254 12.9424 2.3168 12.4125L4.50503 10.7353L6.36183 14.398C6.61155 14.8906 7.2317 15.1288 7.74697 14.93L9.76413 14.1518C10.2794 13.953 10.4947 13.3925 10.2449 12.8999L8.52189 9.50106L11.7144 9.26819Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                          FlowDirection="LeftToRight"
                                                          Stretch="Fill" />
                                                </ToggleButton>

                                                <ToggleButton Name="PART_HandTool"
                                                              Width="24"
                                                              Height="24"
                                                              Margin="8,0,0,0"
                                                              Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                                    <ToggleButton.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Click to pan around the document
                                                        </ToolTip>
                                                    </ToggleButton.ToolTip>
                                                    <Path Width="13"
                                                          Height="15"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Data="M6.1479 1.5C6.1479 1.22386 6.37176 1 6.6479 1H6.79941C7.07555 1 7.29941 1.22386 7.29941 1.5V7.82181V8.32181H7.79941H7.79946H8.29946V7.82181V7.50253C8.29947 7.50126 8.29948 7.49998 8.29948 7.49871V2.57289C8.30153 2.29851 8.52459 2.07671 8.79946 2.07671H8.95097C9.22711 2.07671 9.45096 2.30057 9.45096 2.57671V3.63678H9.45098V8.57544C9.45098 8.85158 9.67484 9.07544 9.95098 9.07544C10.2271 9.07544 10.451 8.85158 10.451 8.57544V3.64097C10.4575 3.3707 10.6789 3.15347 10.9508 3.15347H11.1023C11.3785 3.15347 11.6023 3.37732 11.6023 3.65347V7.82181V8.32181H11.6025V10.7487C11.6025 12.0864 10.8629 13.2744 9.67669 13.6451C9.01877 13.8508 8.34954 13.9975 7.79949 13.9975C7.17738 13.9975 6.39101 13.7541 5.64371 13.4356C5.06084 13.1871 4.61811 12.7383 4.3377 12.176L4.31212 12.1247L4.27558 12.0805C4.22999 12.0254 4.18818 11.9654 4.15087 11.9008L3.71776 12.1506L4.15087 11.9008L1.66534 7.59175C1.52728 7.35242 1.60934 7.04617 1.84857 6.90793L1.98017 6.83188L1.72999 6.39897L1.98017 6.83188C2.21912 6.69379 2.52447 6.77561 2.66237 7.01468L3.06337 7.70987L3.99648 9.32753V7.46004V2.57671C3.99648 2.30057 4.22034 2.07671 4.49648 2.07671H4.64799C4.92192 2.07671 5.14446 2.29707 5.14794 2.57007L5.15489 2.57003C5.15034 2.59714 5.14797 2.625 5.14797 2.65341V7.49871C5.14797 7.77485 5.37183 7.99871 5.64797 7.99871C5.92411 7.99871 6.14797 7.77485 6.14797 7.49871V2.65341C6.14797 2.6228 6.14522 2.59284 6.13995 2.56375L6.1479 2.5637V1.5ZM12.6025 7.32181H12.6023V3.65347C12.6023 2.82504 11.9308 2.15347 11.1023 2.15347H10.9508C10.7623 2.15347 10.5819 2.18825 10.4157 2.25174C10.2672 1.57955 9.6678 1.07671 8.95097 1.07671H8.79946C8.61086 1.07671 8.43039 1.11152 8.26412 1.17506C8.11566 0.502853 7.51626 0 6.79941 0H6.6479C5.93107 0 5.33168 0.502826 5.1832 1.17501C5.01697 1.1115 4.83654 1.07671 4.64799 1.07671H4.49648C3.66805 1.07671 2.99648 1.74829 2.99648 2.57671V5.97531C2.54532 5.70749 1.96655 5.68478 1.47982 5.96605L1.34822 6.0421C0.631081 6.45652 0.385261 7.37394 0.799114 8.09141L3.28465 12.4004C3.33995 12.4963 3.40129 12.5869 3.46798 12.6719C3.84438 13.4011 4.44509 14.0117 5.25156 14.3555C6.02783 14.6864 6.96785 14.9975 7.79949 14.9975C8.49952 14.9975 9.28197 14.8162 9.97502 14.5996C11.6658 14.0711 12.6025 12.4251 12.6025 10.7487V7.82181V7.32181Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                          FlowDirection="LeftToRight"
                                                          Stretch="Fill" />
                                                </ToggleButton>

                                                <Rectangle Width="1.6"
                                                           Name="PART_ZoomToolsSeparator"
                                                           Height="21.599"
                                                           Margin="8,0,0,0"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"
                                                           Fill="{StaticResource PopupBorder}" />
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal">
                                                <Button Name="PART_ButtonZoomOut"
                                                        Width="24"
                                                        Height="24"
                                                        Margin="12,0,0,0"
                                                        IsEnabled="True"
                                                        Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                                    <Button.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Click to decrease the magnification of the entire page.
                                                        </ToolTip>
                                                    </Button.ToolTip>
                                                    <Path Width="16"
                                                          Height="16"
                                                          HorizontalAlignment="Left"
                                                          VerticalAlignment="Top"
                                                          Data="M10.5 7C10.5 7.27614 10.2761 7.5 10 7.5L4 7.5C3.72386 7.5 3.5 7.27614 3.5 7C3.5 6.72386 3.72386 6.5 4 6.5L10 6.5C10.2761 6.5 10.5 6.72386 10.5 7ZM7 13C10.3137 13 13 10.3137 13 7C13 3.68629 10.3137 1 7 1C3.68629 1 1 3.68629 1 7C1 10.3137 3.68629 13 7 13ZM7 14C10.866 14 14 10.866 14 7C14 3.13401 10.866 0 7 0C3.13401 0 0 3.13401 0 7C0 10.866 3.13401 14 7 14Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                          Stretch="Fill" />
                                                </Button>

                                                <Button Name="PART_ButtonZoomIn"
                                                        Width="24"
                                                        Height="24"
                                                        Margin="6,0,0,0"
                                                        IsEnabled="True"
                                                        Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                                    <Button.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Click to increase the magnification of the entire page.
                                                        </ToolTip>
                                                    </Button.ToolTip>
                                                    <Path Width="16"
                                                          Height="16"
                                                          HorizontalAlignment="Left"
                                                          VerticalAlignment="Top"
                                                          Data="M7.5 1C3.91015 1 1 3.91015 1 7.5C1 11.0899 3.91015 14 7.5 14C11.0899 14 14 11.0899 14 7.5C14 3.91015 11.0899 1 7.5 1ZM0 7.5C0 3.35786 3.35786 0 7.5 0C11.6421 0 15 3.35786 15 7.5C15 11.6421 11.6421 15 7.5 15C3.35786 15 0 11.6421 0 7.5ZM7.5 4C7.77614 4 8 4.22386 8 4.5V7H10.5C10.7761 7 11 7.22386 11 7.5C11 7.77614 10.7761 8 10.5 8H8V10.5C8 10.7761 7.77614 11 7.5 11C7.22386 11 7 10.7761 7 10.5V8H4.5C4.22386 8 4 7.77614 4 7.5C4 7.22386 4.22386 7 4.5 7H7V4.5C7 4.22386 7.22386 4 7.5 4Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                          Stretch="Fill" />
                                                </Button>
                                                <ComboBox Name="PART_ComboBoxCurrentZoomLevel"
                                                          Width="68"
                                                          Height="24"
                                                          Margin="12,0,0,0"
                                                          VerticalAlignment="Center"
                                                          IsEditable="True"
                                                          SelectedIndex="2"
                                                          Style="{StaticResource WPFComboBoxStyle}"
                                                          Text="100%"
                                                          MaxDropDownHeight="400">
                                                    <ComboBox.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Current zoom level
                                                        </ToolTip>
                                                    </ComboBox.ToolTip>
                                                    <ComboBoxItem Margin="3,0,3,0"
                                                                  Width="150"
                                                                  Height="25">
                                                        <TextBlock VerticalAlignment="Center"
                                                                   Margin="2">50%</TextBlock>
                                                    </ComboBoxItem>
                                                    <ComboBoxItem Margin="3,0,3,0"
                                                                  Width="150"
                                                                  Height="25">
                                                        <TextBlock VerticalAlignment="Center"
                                                                   Margin="2">75%</TextBlock>
                                                    </ComboBoxItem>
                                                    <ComboBoxItem Margin="3,0,3,0"
                                                                  Width="150"
                                                                  Height="25">
                                                        <TextBlock VerticalAlignment="Center"
                                                                   Margin="2">100%</TextBlock>
                                                    </ComboBoxItem>
                                                    <ComboBoxItem Margin="3,0,3,0"
                                                                  Width="150"
                                                                  Height="25">
                                                        <TextBlock VerticalAlignment="Center"
                                                                   Margin="2">125%</TextBlock>
                                                    </ComboBoxItem>
                                                    <ComboBoxItem Margin="3,0,3,0"
                                                                  Width="150"
                                                                  Height="25">
                                                        <TextBlock VerticalAlignment="Center"
                                                                   Margin="2">150%</TextBlock>
                                                    </ComboBoxItem>
                                                    <ComboBoxItem Margin="3,0,3,0"
                                                                  Width="150"
                                                                  Height="25">
                                                        <TextBlock VerticalAlignment="Center"
                                                                   Margin="2">200%</TextBlock>
                                                    </ComboBoxItem>
                                                    <ComboBoxItem Margin="3,0,3,0"
                                                                  Width="150"
                                                                  Height="25">
                                                        <TextBlock VerticalAlignment="Center"
                                                                   Margin="2">400%</TextBlock>
                                                    </ComboBoxItem>
                                                    <Separator Name="PART_ComboBoxZoomModeSeperator"/>
                                                    <ComboBoxItem Name="PART_FitPage"
                                                                  Margin="3,0,3,0"
                                                                  Width="150"
                                                                  Height="25"
                                                                  IsEnabled="True">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="auto"></ColumnDefinition>
                                                                <ColumnDefinition Width="125"></ColumnDefinition>
                                                                <ColumnDefinition Width="auto"></ColumnDefinition>
                                                            </Grid.ColumnDefinitions>
                                                            <Path Width="14"
                                                                  Height="12"
                                                                  Margin="3,1,0,0"
                                                                  Grid.Column="0"
                                                                  HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center"
                                                                  Data="M0.335938 1.51315C0.335938 0.769685 0.932891 0.166992 1.66927 0.166992H12.3359C13.0723 0.166992 13.6693 0.769686 13.6693 1.51315V10.4875C13.6693 11.231 13.0723 11.8337 12.3359 11.8337H1.66927C0.932892 11.8337 0.335938 11.231 0.335938 10.4875V1.51315ZM1.66927 1.06443C1.42381 1.06443 1.22483 1.26533 1.22483 1.51315V10.4875C1.22483 10.7353 1.42381 10.9362 1.66927 10.9362H12.3359C12.5814 10.9362 12.7804 10.7353 12.7804 10.4875V1.51315C12.7804 1.26533 12.5814 1.06443 12.3359 1.06443H1.66927ZM2.55816 3.75674C2.55816 3.01328 3.15511 2.41058 3.89149 2.41058H4.78038C5.02584 2.41058 5.22483 2.61148 5.22483 2.8593C5.22483 3.10712 5.02584 3.30802 4.78038 3.30802H3.89149C3.64603 3.30802 3.44705 3.50892 3.44705 3.75674V4.65417C3.44705 4.90199 3.24806 5.10289 3.0026 5.10289C2.75714 5.10289 2.55816 4.90199 2.55816 4.65417V3.75674ZM8.78038 2.8593C8.78038 2.61148 8.97937 2.41058 9.22483 2.41058H10.1137C10.8501 2.41058 11.447 3.01328 11.447 3.75674V4.65417C11.447 4.90199 11.2481 5.10289 11.0026 5.10289C10.7571 5.10289 10.5582 4.90199 10.5582 4.65417V3.75674C10.5582 3.50892 10.3592 3.30802 10.1137 3.30802H9.22483C8.97937 3.30802 8.78038 3.10712 8.78038 2.8593ZM3.0026 6.89776C3.24806 6.89776 3.44705 7.09866 3.44705 7.34648V8.24391C3.44705 8.49173 3.64603 8.69263 3.89149 8.69263H4.78038C5.02584 8.69263 5.22483 8.89353 5.22483 9.14135C5.22483 9.38917 5.02584 9.59007 4.78038 9.59007H3.89149C3.15511 9.59007 2.55816 8.98738 2.55816 8.24391V7.34648C2.55816 7.09866 2.75714 6.89776 3.0026 6.89776ZM11.0026 6.89776C11.2481 6.89776 11.447 7.09866 11.447 7.34648V8.24391C11.447 8.98738 10.8501 9.59007 10.1137 9.59007H9.22483C8.97937 9.59007 8.78038 9.38917 8.78038 9.14135C8.78038 8.89353 8.97937 8.69263 9.22483 8.69263H10.1137C10.3592 8.69263 10.5582 8.49173 10.5582 8.24391V7.34648C10.5582 7.09866 10.7571 6.89776 11.0026 6.89776Z"
                                                                  Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}"
                                                                  Stretch="Fill" />
                                                            <TextBlock Grid.Column="1"
                                                                       Margin="10,0,0,0"
                                                                       VerticalAlignment="Center">Fit to Page</TextBlock>
                                                        </Grid>
                                                    </ComboBoxItem>
                                                    <ComboBoxItem Name="PART_FitWidth"
                                                                  Margin="3,0,3,0"
                                                                  Width="150"
                                                                  Height="25"
                                                                  IsEnabled="True">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="auto"></ColumnDefinition>
                                                                <ColumnDefinition Width="125"></ColumnDefinition>
                                                                <ColumnDefinition Width="auto"></ColumnDefinition>
                                                            </Grid.ColumnDefinitions>
                                                            <Path Width="14"
                                                                  Height="12"
                                                                  Margin="3,1,0,0"
                                                                  Grid.Column="0"
                                                                  HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center"
                                                                  Data="M0.335938 1.51315C0.335938 0.769685 0.932891 0.166992 1.66927 0.166992H12.3359C13.0723 0.166992 13.6693 0.769686 13.6693 1.51315V10.4875C13.6693 11.231 13.0723 11.8337 12.3359 11.8337H1.66927C0.932892 11.8337 0.335938 11.231 0.335938 10.4875V1.51315ZM1.66927 1.06443C1.42381 1.06443 1.22483 1.26533 1.22483 1.51315V10.4875C1.22483 10.7353 1.42381 10.9362 1.66927 10.9362H12.3359C12.5814 10.9362 12.7804 10.7353 12.7804 10.4875V1.51315C12.7804 1.26533 12.5814 1.06443 12.3359 1.06443H1.66927ZM2.55816 3.75674C2.55816 3.01328 3.15511 2.41058 3.89149 2.41058H4.78038C5.02584 2.41058 5.22483 2.61148 5.22483 2.8593C5.22483 3.10712 5.02584 3.30802 4.78038 3.30802H3.89149C3.64603 3.30802 3.44705 3.50892 3.44705 3.75674V8.24391C3.44705 8.49173 3.64603 8.69263 3.89149 8.69263H4.78038C5.02584 8.69263 5.22483 8.89353 5.22483 9.14135C5.22483 9.38917 5.02584 9.59007 4.78038 9.59007H3.89149C3.15511 9.59007 2.55816 8.98738 2.55816 8.24391V3.75674ZM8.78038 2.8593C8.78038 2.61148 8.97937 2.41058 9.22483 2.41058H10.1137C10.8501 2.41058 11.447 3.01328 11.447 3.75674V8.24391C11.447 8.98738 10.8501 9.59007 10.1137 9.59007H9.22483C8.97937 9.59007 8.78038 9.38917 8.78038 9.14135C8.78038 8.89353 8.97937 8.69263 9.22483 8.69263H10.1137C10.3592 8.69263 10.5582 8.49173 10.5582 8.24391V3.75674C10.5582 3.50892 10.3592 3.30802 10.1137 3.30802H9.22483C8.97937 3.30802 8.78038 3.10712 8.78038 2.8593Z"
                                                                  Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}"
                                                                  Stretch="Fill" />
                                                            <TextBlock Grid.Column="1"
                                                                       Margin="10,0,0,0"
                                                                       VerticalAlignment="Center">Fit to Width</TextBlock>
                                                        </Grid>
                                                    </ComboBoxItem>
                                                    <Separator Name="PART_ComboBoxMarqueeZoomSeperator"/>
                                                    <ComboBoxItem Name="PART_MarqueeZoom"
                                                                  Margin="3,0,3,5"
                                                                  Width="150"
                                                                  Height="25">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="auto"></ColumnDefinition>
                                                                <ColumnDefinition Width="125"></ColumnDefinition>
                                                                <ColumnDefinition Width="auto"></ColumnDefinition>
                                                            </Grid.ColumnDefinitions>
                                                            <Path Width="14"
                                                                  Height="14"
                                                                  Margin="3,1,0,0"
                                                                  Grid.Column="0"
                                                                  HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center"
                                                                  Data="M4.00664 1.14109H3.56826V1.54584C3.56826 1.76898 3.38737 1.94988 3.16422 1.94988C2.94108 1.94988 2.76018 1.76898 2.76018 1.54584V0.737856C2.76018 0.514226 2.94147 0.333008 3.16503 0.333008H4.00664C4.22979 0.333008 4.41069 0.513903 4.41069 0.737048C4.41069 0.960194 4.22979 1.14109 4.00664 1.14109ZM5.68988 0.333008C5.46673 0.333008 5.28584 0.513903 5.28584 0.737048C5.28584 0.960194 5.46673 1.14109 5.68988 1.14109H7.37311C7.59625 1.14109 7.77715 0.960194 7.77715 0.737048C7.77715 0.513903 7.59625 0.333008 7.37311 0.333008H5.68988ZM9.05634 0.333008C8.8332 0.333008 8.6523 0.513903 8.6523 0.737048C8.6523 0.960194 8.8332 1.14109 9.05634 1.14109H10.7396C10.9627 1.14109 11.1436 0.960194 11.1436 0.737048C11.1436 0.513903 10.9627 0.333008 10.7396 0.333008H9.05634ZM12.4228 0.333008C12.1997 0.333008 12.0188 0.513903 12.0188 0.737048C12.0188 0.960194 12.1997 1.14109 12.4228 1.14109H12.8612V1.57947C12.8612 1.80262 13.0421 1.98351 13.2652 1.98351C13.4884 1.98351 13.6693 1.80262 13.6693 1.57947V0.737856C13.6693 0.513942 13.4877 0.333008 13.2644 0.333008H12.4228ZM3.56826 3.1618C3.56826 2.93865 3.38737 2.75776 3.16422 2.75776C2.94108 2.75776 2.76018 2.93865 2.76018 3.1618V4.77775C2.76018 4.96697 2.89024 5.1258 3.06583 5.16973C3.00946 5.31446 2.96018 5.46358 2.91801 5.6171C2.86255 5.81674 2.82171 6.02047 2.79548 6.2283C2.7728 6.27879 2.76018 6.33478 2.76018 6.39371V6.79765V7.20169C2.76018 7.26125 2.77306 7.31779 2.7962 7.36869C2.83734 7.69137 2.91371 8.00818 3.02533 8.31912C3.2021 8.80733 3.45673 9.24925 3.78922 9.64488L0.455887 12.9782C0.375921 13.0582 0.335938 13.1529 0.335938 13.2623C0.335938 13.3717 0.375921 13.4664 0.455887 13.5464C0.535853 13.6264 0.63055 13.6663 0.739978 13.6663C0.849406 13.6663 0.944103 13.6264 1.02407 13.5464L4.3574 10.2131C4.75303 10.5455 5.19494 10.8002 5.68316 10.9769C6.17558 11.1537 6.68274 11.2421 7.20462 11.2421C7.30971 11.2421 7.41381 11.2386 7.51694 11.2316C7.54641 11.2385 7.57712 11.2421 7.60866 11.2421H8.31563C8.48589 11.2421 8.63155 11.1368 8.69103 10.9878C8.92218 10.9058 9.14319 10.8061 9.35407 10.6886C9.33566 10.7348 9.32553 10.7853 9.32553 10.8381C9.32553 11.0612 9.50643 11.2421 9.72957 11.2421H11.1435C11.3667 11.2421 11.5476 11.0612 11.5476 10.8381C11.5476 10.6149 11.3667 10.434 11.1435 10.434H9.76176C9.97166 10.287 10.1673 10.1229 10.3486 9.94159C10.6179 9.67223 10.8494 9.37341 11.043 9.04513C11.2366 8.71264 11.386 8.357 11.4912 7.97821C11.5965 7.59942 11.6491 7.2059 11.6491 6.79765C11.6491 6.38941 11.5965 5.99589 11.4912 5.6171C11.386 5.23831 11.2366 4.88478 11.043 4.55649C10.8494 4.224 10.6179 3.92308 10.3486 3.65371C10.0792 3.38435 9.77828 3.15287 9.44579 2.95927C9.1175 2.76567 8.76397 2.61626 8.38518 2.51104C8.00639 2.40582 7.61287 2.35321 7.20462 2.35321C6.79217 2.35321 6.39654 2.40582 6.01776 2.51104C5.63897 2.61626 5.28543 2.76567 4.95715 2.95927C4.62887 3.15287 4.33005 3.38435 4.06068 3.65371C3.87938 3.83502 3.71524 4.02967 3.56826 4.23766V3.1618ZM3.56826 6.79765C3.56826 7.30271 3.66296 7.77619 3.85235 8.21811C4.04175 8.66003 4.30058 9.04513 4.62887 9.37341C4.95715 9.7017 5.34225 9.96053 5.78417 10.1499C6.22609 10.3393 6.69957 10.434 7.20462 10.434C7.70967 10.434 8.18316 10.3393 8.62508 10.1499C9.067 9.96053 9.4521 9.7017 9.78038 9.37341C10.1087 9.04513 10.3675 8.66003 10.5569 8.21811C10.7463 7.77619 10.841 7.30271 10.841 6.79765C10.841 6.29681 10.7442 5.82543 10.5506 5.38351C10.3612 4.94159 10.1024 4.55649 9.77407 4.22821C9.44579 3.89993 9.06069 3.64109 8.61877 3.45169C8.17685 3.25809 7.70547 3.16129 7.20462 3.16129C6.70378 3.16129 6.2324 3.25809 5.79048 3.45169C5.34856 3.64109 4.96346 3.89993 4.63518 4.22821C4.3069 4.55649 4.04595 4.94159 3.85235 5.38351C3.66296 5.82543 3.56826 6.29681 3.56826 6.79765ZM13.6693 3.2627C13.6693 3.03956 13.4884 2.85866 13.2652 2.85866C13.0421 2.85866 12.8612 3.03956 12.8612 3.2627V4.94594C12.8612 5.16908 13.0421 5.34998 13.2652 5.34998C13.4884 5.34998 13.6693 5.16908 13.6693 4.94594V3.2627ZM13.6693 6.62917C13.6693 6.40602 13.4884 6.22513 13.2652 6.22513C13.0421 6.22513 12.8612 6.40602 12.8612 6.62917V8.3124C12.8612 8.53555 13.0421 8.71644 13.2652 8.71644C13.4884 8.71644 13.6693 8.53555 13.6693 8.3124V6.62917ZM13.6693 9.99563C13.6693 9.77249 13.4884 9.59159 13.2652 9.59159C13.0421 9.59159 12.8612 9.77249 12.8612 9.99563V10.434H12.5575C12.3343 10.434 12.1534 10.6149 12.1534 10.8381C12.1534 11.0612 12.3343 11.2421 12.5575 11.2421H13.2644C13.4883 11.2421 13.6693 11.0605 13.6693 10.8372V9.99563Z"
                                                                  Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}"
                                                                  FlowDirection="LeftToRight"
                                                                  Stretch="Fill" />
                                                            <TextBlock Grid.Column="1"
                                                                       Margin="10,0,0,0"
                                                                       VerticalAlignment="Center">Zoom Select Area</TextBlock>
                                                        </Grid>
                                                    </ComboBoxItem>
                                                </ComboBox>
                                                
                                                <Rectangle x:Name="PART_AnnotationToolsSeparator"
                                                           Width="1.6"
                                                           Height="21.599"
                                                           Margin="8,0,0,0"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"
                                                           Fill="{StaticResource PopupBorder}" />
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal">
                                                <Button Name="PART_ButtonSignature"
                                                        Width="24"
                                                        Height="24"
                                                        Margin="8,0,0,0"
                                                        Style="{StaticResource SyncfusionGlyphButtonStyle}"
                                                        ToolTipService.ShowOnDisabled="False">
                                                    <Button.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Sign document by drawing a signature.
                                                        </ToolTip>
                                                    </Button.ToolTip>
                                                    <Path Width="13"
                                                          Height="15"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Data="M5.26544 0.719818C5.41194 0.485731 5.33312 0.190726 5.08939 0.0609055C4.84566 -0.0689148 4.52932 0.0156102 4.38282 0.249697L3.21531 2.1153C2.87999 2.65111 2.93817 3.29331 3.30935 3.74062L1.71234 4.79583C1.32836 5.04953 1.07916 5.44774 1.03299 5.88137L0.771683 8.33547L0.7703 8.34846L0.769658 8.36147L0.599435 11.8093C0.542319 12.9662 1.87242 13.6747 2.92984 13.0506L6.08129 11.1907L6.09319 11.1836L6.10469 11.176L8.27774 9.74023C8.66171 9.48652 8.91092 9.08831 8.95709 8.65468L9.14273 6.91117C9.79911 7.0682 10.5263 6.79483 10.8949 6.20577L12.0624 4.34017C12.2089 4.10608 12.1301 3.81107 11.8864 3.68125C11.6426 3.55143 11.3263 3.63596 11.1798 3.87005L10.0123 5.73565C9.86579 5.96974 9.54945 6.05426 9.30572 5.92444L9.2509 5.89525L9.25103 5.89407L8.97133 5.74509L4.4365 3.32965L4.24505 3.22768C4.02421 3.0906 3.95739 2.80998 4.09793 2.58542L5.26544 0.719818ZM4.18465 4.32702L8.16101 6.44499L7.93247 8.59139C7.91708 8.73594 7.83401 8.86867 7.70602 8.95324L5.54447 10.3815L2.56077 12.1424L5.33862 7.66447C5.6037 7.64431 5.84982 7.51339 5.98865 7.29155C6.21881 6.92377 6.06696 6.44536 5.64947 6.22299C5.23199 6.00063 4.70697 6.1185 4.47681 6.48628C4.33695 6.70977 4.33815 6.97412 4.45432 7.19704L1.63174 11.7471L1.79769 8.38577L2.05761 5.94467C2.073 5.80012 2.15607 5.66739 2.28406 5.58282L4.18465 4.32702ZM10.1082 13.7062C10.3903 13.6151 10.6588 13.4801 10.8361 13.375C11.0626 13.2751 11.3452 13.1923 11.5335 13.1928C11.5475 13.1928 11.5593 13.1933 11.5692 13.1941C11.5692 13.2014 11.5689 13.2099 11.5683 13.2196C11.5509 13.4872 11.7668 13.7099 12.0507 13.7169C12.3345 13.724 12.5787 13.5128 12.5961 13.2452C12.6153 12.9492 12.536 12.6604 12.2962 12.4573C12.0725 12.2679 11.7908 12.2241 11.5723 12.2235C11.1422 12.2223 10.6723 12.3873 10.3867 12.5175L10.3569 12.5311L10.3291 12.5481C10.2072 12.6223 10.0024 12.7266 9.80451 12.7905C9.70592 12.8224 9.62737 12.8382 9.57239 12.842C9.5547 12.8432 9.54299 12.8429 9.5362 12.8425C9.21845 12.6827 8.85319 12.7417 8.62377 12.8007C8.56127 12.8168 8.49916 12.8355 8.43821 12.8559C8.36208 12.4442 8.20089 11.9498 7.8846 11.5078C7.76299 11.3379 7.58955 11.2171 7.37642 11.1656C7.17474 11.1169 6.97692 11.1395 6.81054 11.1837C6.48876 11.2692 6.16638 11.4674 5.8883 11.6734C5.31831 12.0957 4.73643 12.7023 4.3752 13.1268C4.19445 13.3393 4.22588 13.6435 4.4454 13.8063C4.66493 13.9692 4.98941 13.929 5.17017 13.7165C5.50612 13.3217 6.02904 12.7816 6.50758 12.4271C6.75331 12.245 6.94089 12.1472 7.05732 12.1161C7.35968 12.5666 7.45652 13.1149 7.46771 13.4455C7.48218 13.8733 7.97338 14.1415 8.3897 13.925C8.53681 13.8484 8.7105 13.7743 8.86272 13.7352C8.93823 13.7157 8.99302 13.709 9.02758 13.7088C9.0339 13.7087 9.03877 13.7089 9.04235 13.7091C9.23682 13.8068 9.44504 13.8201 9.61134 13.8087C9.78632 13.7968 9.95835 13.7547 10.1082 13.7062ZM11.5662 13.1549C11.5657 13.1528 11.5654 13.1518 11.5653 13.1519C11.5653 13.1519 11.5653 13.1522 11.5655 13.1528C11.5656 13.1533 11.5659 13.154 11.5662 13.1549Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                          FlowDirection="LeftToRight"
                                                          Stretch="Fill" />
                                                </Button>

                                                <ToggleButton Name="PART_Annotations"
                                                              Width="24"
                                                              Height="24"
                                                              Margin="8,0,0,0"
                                                              Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                                    <ToggleButton.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Show Annotations
                                                        </ToolTip>
                                                    </ToggleButton.ToolTip>
                                                    <Path Width="15"
                                                          Height="16"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Data="M7.91087 0.828697L8.22322 0.438262L8.22322 0.438262L7.91087 0.828697ZM11.937 4.04963L11.6247 4.44007L11.6247 4.44007L11.937 4.04963ZM4 16H4.5V15H4V16ZM3.5 7C3.22386 7 3 7.22386 3 7.5C3 7.77614 3.22386 8 3.5 8V7ZM9.5 8C9.77614 8 10 7.77614 10 7.5C10 7.22386 9.77614 7 9.5 7V8ZM3.5 9C3.22386 9 3 9.22386 3 9.5C3 9.77614 3.22386 10 3.5 10V9ZM8.5 10C8.77614 10 9 9.77614 9 9.5C9 9.22386 8.77614 9 8.5 9V10ZM3.5 11C3.22386 11 3 11.2239 3 11.5C3 11.7761 3.22386 12 3.5 12V11ZM6.5 12C6.77614 12 7 11.7761 7 11.5C7 11.2239 6.77614 11 6.5 11V12ZM7.87868 15.5356V16.0356C8.01129 16.0356 8.13847 15.9829 8.23224 15.8892L7.87868 15.5356ZM6.46447 14.1214L6.11092 13.7679C6.01715 13.8616 5.96447 13.9888 5.96447 14.1214H6.46447ZM6.46447 15.5356H5.96447C5.96447 15.8118 6.18833 16.0356 6.46447 16.0356V15.5356ZM11.0608 9.52513L10.7072 9.17157L10.7072 9.17157L11.0608 9.52513ZM12.475 10.9393L12.8285 11.2929L12.8285 11.2929L12.475 10.9393ZM1 14V2H0V14H1ZM7.59852 1.21913L11.6247 4.44007L12.2494 3.6592L8.22322 0.438262L7.59852 1.21913ZM2 1H6.97383V0H2V1ZM4 15H2V16H4V15ZM12 5.22094V6H13V5.22094H12ZM8.22322 0.438262C7.86859 0.154562 7.42797 0 6.97383 0V1C7.2009 1 7.42121 1.07728 7.59852 1.21913L8.22322 0.438262ZM0 14C0 15.1046 0.89543 16 2 16V15C1.44772 15 1 14.5523 1 14H0ZM11.6247 4.44007C11.8619 4.62984 12 4.91715 12 5.22094H13C13 4.61337 12.7238 4.03874 12.2494 3.6592L11.6247 4.44007ZM1 2C1 1.44772 1.44772 1 2 1V0C0.89543 0 0 0.895431 0 2H1ZM3.5 8H9.5V7H3.5V8ZM3.5 10H8.5V9H3.5V10ZM3.5 12H6.5V11H3.5V12ZM7.87868 15.0356H6.46447V16.0356H7.87868V15.0356ZM6.96447 15.5356V14.1214H5.96447V15.5356H6.96447ZM6.81802 14.475L11.4143 9.87868L10.7072 9.17157L6.11092 13.7679L6.81802 14.475ZM11.4143 9.87868L12.4749 8.81811L11.7678 8.111L10.7072 9.17157L11.4143 9.87868ZM13.182 9.52521L12.1214 10.5858L12.8285 11.2929L13.8891 10.2323L13.182 9.52521ZM12.1214 10.5858L7.52513 15.1821L8.23224 15.8892L12.8285 11.2929L12.1214 10.5858ZM10.7072 9.87868L12.1214 11.2929L12.8285 10.5858L11.4143 9.17157L10.7072 9.87868ZM13.182 8.81811C13.3772 9.01337 13.3772 9.32995 13.182 9.52521L13.8891 10.2323C14.4749 9.64653 14.4749 8.69679 13.8891 8.111L13.182 8.81811ZM13.8891 8.111C13.3033 7.52521 12.3536 7.52521 11.7678 8.111L12.4749 8.81811C12.6701 8.62284 12.9867 8.62284 13.182 8.81811L13.8891 8.111Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                          FlowDirection="LeftToRight"
                                                          Stretch="Fill" />
                                                </ToggleButton>
                                            </StackPanel>
                                        </StackPanel>
                                    </ScrollViewer>
                                    <RepeatButton x:Name="PART_PrimaryRightScrollButton"
                                                  Width="24"
                                                  Grid.Column="2"
                                                  Style="{StaticResource WPFGlyphRepeatButtonStyle}"
                                                  Background="{StaticResource ContentBackgroundAlt1}">
                                        <RepeatButton.Margin>
                                            <Thickness>0,-3,-2,-2</Thickness>
                                        </RepeatButton.Margin>
                                        <RepeatButton.Effect>
                                            <DropShadowEffect BlurRadius="16"
                                                              ShadowDepth="4.5"
                                                              Direction="180"
                                                              Opacity=".62" />
                                        </RepeatButton.Effect>
                                        <Path Width="6"
                                              Height="11"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Data="M1 10L5.5 5.5L1 1"
                                              Stroke="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type RepeatButton}}}"
                                              StrokeThickness="1" />
                                    </RepeatButton>
                                </Grid>
                                <StackPanel Name="PART_TextSearchStack"
                                            Grid.Column="2"
                                            Orientation="Horizontal">
                                    <Button Name="PART_ButtonTextSearch"
                                            Width="24"
                                            Height="24"
                                            Margin="0,0,8,0"
                                            Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                        <Button.ToolTip>
                                            <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                Click to search text
                                            </ToolTip>
                                        </Button.ToolTip>
                                        <Path Width="16"
                                              Height="15"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Data="M6.70254 1C3.88589 1 1.60254 3.28335 1.60254 6.1C1.60254 8.91665 3.88589 11.2 6.70254 11.2C9.51919 11.2 11.8025 8.91665 11.8025 6.1C11.8025 3.28335 9.51919 1 6.70254 1ZM0.602539 6.1C0.602539 2.73106 3.3336 0 6.70254 0C10.0715 0 12.8025 2.73106 12.8025 6.1C12.8025 7.60447 12.2579 8.98173 11.355 10.0454L15.4561 14.1464C15.6514 14.3417 15.6514 14.6583 15.4561 14.8536C15.2608 15.0488 14.9442 15.0488 14.749 14.8536L10.6479 10.7525C9.58427 11.6554 8.20701 12.2 6.70254 12.2C3.3336 12.2 0.602539 9.46894 0.602539 6.1Z"
                                              Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                              FlowDirection="LeftToRight"
                                              Stretch="Fill" />
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </Border>
                        <Border Name="Part_AnnotationToolbar"
                                Height="40"
                                Background="{StaticResource ContentBackgroundAlt1}"
                                BorderBrush="{StaticResource BorderAlt}"
                                BorderThickness="0,1px,0,1px"
                                KeyboardNavigation.TabNavigation="Cycle">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"></ColumnDefinition>
                                    <ColumnDefinition Width="auto"></ColumnDefinition>
                                </Grid.ColumnDefinitions>
                                <Grid Grid.Column="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="auto"></ColumnDefinition>
                                        <ColumnDefinition Width="*"></ColumnDefinition>
                                        <ColumnDefinition Width="auto"></ColumnDefinition>
                                    </Grid.ColumnDefinitions>
                                    <RepeatButton x:Name="PART_SecondaryLeftScrollButton"
                                                  Width="24"
                                                  Grid.Column="0"
                                                  Style="{StaticResource WPFGlyphRepeatButtonStyle}"
                                                  Background="{StaticResource ContentBackgroundAlt1}">
                                        <RepeatButton.Margin>
                                            <Thickness>-2,-1,0,-2</Thickness>
                                        </RepeatButton.Margin>
                                        <RepeatButton.Effect>
                                            <DropShadowEffect BlurRadius="16"
                                                              ShadowDepth="4.5"
                                                              Direction="0"
                                                              Opacity=".62" />
                                        </RepeatButton.Effect>
                                        <Path Width="6"
                                              Height="11"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Data="M5.5 10L1 5.5L5.5 1"
                                              Stroke="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type RepeatButton}}}"
                                              StrokeThickness="1" />
                                    </RepeatButton>
                                    <ScrollViewer Name="PART_SecondaryToolbar_Scrollviewer"
                                                  Grid.Column="1"
                                                  HorizontalScrollBarVisibility="Hidden"
                                                  VerticalScrollBarVisibility="Disabled">
                                        <StackPanel Orientation="Horizontal"
                                                    HorizontalAlignment="Center">
                                            <StackPanel Orientation="Horizontal">
                                                <ToggleButton Name="PART_TextMarkup"
                                                              Width="34"
                                                              Height="24"
                                                              Margin="8,0,0,0"
                                                              Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                                    <ToggleButton.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Text Markup Annotation
                                                        </ToolTip>
                                                    </ToggleButton.ToolTip>
                                                    <StackPanel Orientation="Horizontal">
                                                        <Path Width="16"
                                                              Height="14"
                                                              Margin="0,2,0,0"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Data="M0.000488281 0.5C0.000488281 0.223858 0.224346 0 0.500488 0H6.41621C6.69235 0 6.91621 0.223858 6.91621 0.5C6.91621 0.776142 6.69235 1 6.41621 1H3.95835V8.49698C3.95835 8.77312 3.73449 8.99698 3.45835 8.99698C3.18221 8.99698 2.95835 8.77312 2.95835 8.49698V1H0.500488C0.224346 1 0.000488281 0.776142 0.000488281 0.5ZM11.6198 1.41915C12.3932 0.635068 13.6499 0.635069 14.4233 1.41915C15.1929 2.19941 15.1929 3.46164 14.4233 4.24189L7.85083 10.9055C7.67138 11.0874 7.44824 11.2202 7.20246 11.2904L4.81883 11.9715C4.70829 12.003 4.60075 12.0085 4.5003 11.9927C4.48192 11.9948 4.46323 11.9958 4.4443 11.9958H0.500488C0.224346 11.9958 0.000488281 11.772 0.000488281 11.4958C0.000488281 11.2197 0.224346 10.9958 0.500488 10.9958H3.92811L4.67223 8.69166C4.74426 8.46862 4.86728 8.2653 5.03189 8.09841L11.6198 1.41915ZM5.01816 10.8745L6.92775 10.3289C7.00685 10.3063 7.07965 10.2633 7.13888 10.2033L12.4026 4.86659L11.0133 3.4581L5.74385 8.80063C5.68948 8.85576 5.64818 8.92362 5.62384 8.99897L5.01816 10.8745ZM11.7156 2.74607L13.1049 4.15456L13.7113 3.53967C14.0969 3.1488 14.0969 2.51225 13.7113 2.12137C13.3296 1.73433 12.7135 1.73433 12.3318 2.12137L11.7156 2.74607Z"
                                                              Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                              FlowDirection="LeftToRight"
                                                              Stretch="Fill" />
                                                        <Path Width="10"
                                                              Height="5"
                                                              Margin="4,3,0,0"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Data="M0 0.538462C0 0.392628 0.0494792 0.266426 0.148438 0.159856C0.247396 0.0532853 0.364583 0 0.5 0C0.635417 0 0.752604 0.0532853 0.851562 0.159856L6 5.69591L11.1484 0.159856C11.2474 0.0532853 11.3646 0 11.5 0C11.6354 0 11.7526 0.0532853 11.8516 0.159856C11.9505 0.266426 12 0.392628 12 0.538462C12 0.684295 11.9505 0.810497 11.8516 0.917067L6.35156 6.84014C6.2526 6.94671 6.13542 7 6 7C5.86458 7 5.7474 6.94671 5.64844 6.84014L0.148438 0.917067C0.0494792 0.810497 0 0.684295 0 0.538462Z"
                                                              Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                              Stretch="Fill" />
                                                    </StackPanel>
                                                    <ToggleButton.ContextMenu>
                                                        <ContextMenu Style="{StaticResource WPFContextMenuStyle}">
                                                            <MenuItem Name="PART_HighlightMenuItem"
                                                                      Header="Highlight">
                                                                <MenuItem.Icon>
                                                                    <Path Canvas.Left="30.975"
                                                                          Canvas.Top="95.474"
                                                                          Width="14"
                                                                          Height="14"
                                                                          Margin="8,0,8,0"
                                                                          Data="M0.5 0C0.776142 0 1 0.223858 1 0.5V4.34723C1 4.5841 1.20533 4.80903 1.5 4.80903H13.5C13.7947 4.80903 14 4.5841 14 4.34723V0.5H15V4.34723C15 4.99529 14.5747 5.52674 14 5.72527V7.23264C14 8.05815 13.3099 8.69445 12.5 8.69445H12V10.4854C12 11.0496 11.6688 11.5535 11.1639 11.7963L4.75869 14.8766C3.9764 15.2528 3 14.7207 3 13.8066V8.69445H2.5C1.6901 8.69445 1 8.05815 1 7.23264L1 5.72527C0.42531 5.52674 0 4.99529 0 4.34723V0.5C0 0.223858 0.223858 0 0.5 0ZM2 5.80903L2 7.23264C2 7.46952 2.20533 7.69445 2.5 7.69445H12.5C12.7947 7.69445 13 7.46952 13 7.23264V5.80903H2ZM4 8.69445V13.8066C4 13.8693 4.03143 13.9269 4.09696 13.9659C4.1633 14.0053 4.24673 14.0132 4.3253 13.9754L10.7305 10.8951C10.9032 10.8121 11 10.6499 11 10.4854V8.69445H4Z"
                                                                          Fill="{StaticResource IconColor}"
                                                                          FlowDirection="LeftToRight"
                                                                          Stretch="Fill" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                            <MenuItem Name="PART_UnderlineMenuItem"
                                                                      Header="Underline">
                                                                <MenuItem.Icon>
                                                                    <Path Canvas.Left="56.313"
                                                                          Canvas.Top="96.479"
                                                                          Width="10"
                                                                          Height="14"
                                                                          Margin="8,0,8,0"
                                                                          Data="M0.5 0C0.776142 0 1 0.223858 1 0.5V7.5C1 9.98528 3.01472 12 5.5 12C7.98528 12 10 9.98528 10 7.5V0.5C10 0.223858 10.2239 0 10.5 0C10.7761 0 11 0.223858 11 0.5V7.5C11 10.5376 8.53757 13 5.5 13C2.46243 13 0 10.5376 0 7.5V0.5C0 0.223858 0.223858 0 0.5 0ZM0 14.5C0 14.2239 0.223858 14 0.5 14H10.5C10.7761 14 11 14.2239 11 14.5C11 14.7761 10.7761 15 10.5 15H0.5C0.223858 15 0 14.7761 0 14.5Z"
                                                                          Fill="{StaticResource IconColor}"
                                                                          Stretch="Fill" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                            <MenuItem Name="PART_StrikethroughMenuItem"
                                                                      Header="Strikethrough">
                                                                <MenuItem.Icon>
                                                                    <Path Canvas.Left="80.375"
                                                                          Canvas.Top="92.854"
                                                                          Width="14"
                                                                          Height="14"
                                                                          Margin="8,0,8,0"
                                                                          Data="M3.22656 6C2.40885 5.36458 2 4.53125 2 3.5C2 3.23958 2.04167 2.99219 2.125 2.75781C2.20833 2.51823 2.32031 2.29427 2.46094 2.08594C2.60156 1.8724 2.76302 1.67708 2.94531 1.5C3.13281 1.32292 3.32812 1.16406 3.53125 1.02344C4.03125 0.679688 4.58333 0.424479 5.1875 0.257812C5.79167 0.0859375 6.39583 0 7 0C7.38021 0 7.75781 0.0286458 8.13281 0.0859375C8.50781 0.143229 8.875 0.236979 9.23438 0.367188C9.47396 0.455729 9.71875 0.559896 9.96875 0.679688C10.2188 0.799479 10.4609 0.9375 10.6953 1.09375C10.9349 1.25 11.1562 1.42188 11.3594 1.60938C11.5677 1.79167 11.7474 1.98958 11.8984 2.20312C11.9349 2.25521 11.9609 2.30208 11.9766 2.34375C11.9922 2.38021 12 2.43229 12 2.5C12 2.63542 11.9505 2.7526 11.8516 2.85156C11.7526 2.95052 11.6354 3 11.5 3C11.4062 3 11.3307 2.98438 11.2734 2.95312C11.2161 2.91667 11.1589 2.86458 11.1016 2.79688C10.9714 2.65104 10.8411 2.51562 10.7109 2.39062C10.5807 2.26562 10.4375 2.14323 10.2812 2.02344C10.0521 1.85156 9.80469 1.70052 9.53906 1.57031C9.27865 1.4401 9.00781 1.33333 8.72656 1.25C8.44531 1.16667 8.15885 1.10417 7.86719 1.0625C7.57552 1.02083 7.28646 1 7 1C6.48958 1 5.98177 1.06771 5.47656 1.20312C4.97656 1.33854 4.51562 1.55469 4.09375 1.85156C3.96354 1.94531 3.83333 2.05208 3.70312 2.17188C3.57292 2.28646 3.45573 2.41406 3.35156 2.55469C3.2474 2.69531 3.16146 2.84635 3.09375 3.00781C3.03125 3.16406 3 3.32812 3 3.5C3 3.86458 3.06771 4.17969 3.20312 4.44531C3.34375 4.71094 3.52604 4.94271 3.75 5.14062C3.97917 5.33333 4.23698 5.5 4.52344 5.64062C4.8151 5.77604 5.11198 5.89583 5.41406 6H3.22656ZM2 11.5C2 11.3594 2.04948 11.2422 2.14844 11.1484C2.2526 11.0495 2.3724 11 2.50781 11C2.59635 11 2.67188 11.0208 2.73438 11.0625C2.80208 11.099 2.86198 11.151 2.91406 11.2188C3.04427 11.3854 3.16146 11.5365 3.26562 11.6719C3.36979 11.8021 3.47917 11.9219 3.59375 12.0312C3.70833 12.1406 3.83594 12.2422 3.97656 12.3359C4.1224 12.4245 4.29688 12.513 4.5 12.6016C4.88542 12.7682 5.29427 12.8776 5.72656 12.9297C6.16406 12.9766 6.58854 13 7 13C7.25 13 7.52083 12.9818 7.8125 12.9453C8.10938 12.9089 8.40625 12.8516 8.70312 12.7734C9 12.6901 9.28646 12.5859 9.5625 12.4609C9.83854 12.3307 10.0833 12.1745 10.2969 11.9922C10.5104 11.8047 10.6797 11.5885 10.8047 11.3438C10.9349 11.0938 11 10.8125 11 10.5C11 10.0469 10.8984 9.66927 10.6953 9.36719C10.4974 9.0651 10.2109 8.79948 9.83594 8.57031C9.78906 8.54427 9.70312 8.5 9.57812 8.4375C9.45833 8.375 9.33073 8.3125 9.19531 8.25C9.0651 8.18229 8.9401 8.125 8.82031 8.07812C8.70052 8.02604 8.6224 8 8.58594 8H0.5C0.364583 8 0.247396 7.95052 0.148438 7.85156C0.0494792 7.7526 0 7.63542 0 7.5C0 7.36458 0.0494792 7.2474 0.148438 7.14844C0.247396 7.04948 0.364583 7 0.5 7H13.5C13.6354 7 13.7526 7.04948 13.8516 7.14844C13.9505 7.2474 14 7.36458 14 7.5C14 7.63542 13.9505 7.7526 13.8516 7.85156C13.7526 7.95052 13.6354 8 13.5 8H10.7734C11.5911 8.63542 12 9.46875 12 10.5C12 10.9219 11.9245 11.3021 11.7734 11.6406C11.6224 11.974 11.4193 12.2708 11.1641 12.5312C10.9141 12.7917 10.6198 13.0156 10.2812 13.2031C9.94792 13.3854 9.59635 13.5365 9.22656 13.6562C8.85677 13.776 8.47917 13.8646 8.09375 13.9219C7.71354 13.974 7.34896 14 7 14C6.51042 14 6.02083 13.9714 5.53125 13.9141C5.04167 13.8516 4.56771 13.7214 4.10938 13.5234C3.70312 13.3464 3.32292 13.1068 2.96875 12.8047C2.61458 12.5026 2.32031 12.1615 2.08594 11.7812C2.02865 11.6927 2 11.599 2 11.5Z"
                                                                          Fill="{StaticResource IconColor}"
                                                                          Stretch="Fill" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                            <MenuItem Name="PART_SquigglyMenuItem"
                                                                      Header="Squiggly">
                                                                <MenuItem.Icon>
                                                                    <Path Canvas.Left="30.975"
                                                                          Canvas.Top="95.474"
                                                                          Width="15"
                                                                          Height="15"
                                                                          Margin="8,0,8,0"
                                                                          Data="M1.40004 0.5C1.40004 0.223858 1.6239 0 1.90004 0H13.1C13.3762 0 13.6 0.223858 13.6 0.5C13.6 0.776142 13.3762 1 13.1 1H8.00004V10.7082C8.00004 10.9844 7.77618 11.2082 7.50004 11.2082C7.2239 11.2082 7.00004 10.9844 7.00004 10.7082V1H1.90004C1.6239 1 1.40004 0.776142 1.40004 0.5ZM3.74728 14.4802C3.48159 14.7709 3.10319 15.0247 2.59013 14.9832C1.93798 14.9304 1.46109 14.6116 1.07883 14.2145C0.786729 13.9111 0.522108 13.5296 0.271429 13.1682C0.210821 13.0808 0.151027 12.9946 0.0918564 12.911C-0.0676244 12.6856 -0.0141588 12.3735 0.211275 12.2141C0.436709 12.0546 0.748744 12.108 0.908225 12.3335C0.981844 12.4375 1.05122 12.5373 1.11766 12.6329C1.36291 12.9857 1.56816 13.2809 1.79927 13.521C2.07346 13.8058 2.33714 13.9595 2.67082 13.9865C2.76561 13.9941 2.86385 13.9645 3.00918 13.8055C3.16805 13.6317 3.31243 13.3796 3.49311 13.0577L3.50711 13.0328C3.66922 12.7438 3.86611 12.3928 4.11151 12.1252C4.3779 11.8348 4.75719 11.5824 5.27055 11.6267C5.73086 11.6664 6.05485 11.9253 6.28269 12.2043C6.49495 12.4641 6.66054 12.7874 6.79702 13.0538L6.80878 13.0768C6.96013 13.3721 7.08119 13.6036 7.21938 13.7728C7.3481 13.9304 7.4472 13.9784 7.54301 13.9867C7.64552 13.9955 7.74407 13.9643 7.88466 13.8161C8.04073 13.6517 8.18394 13.41 8.36371 13.0958C8.37109 13.0829 8.37853 13.0699 8.38604 13.0568C8.54613 12.7767 8.73895 12.4393 8.97613 12.1746C9.23498 11.8858 9.59832 11.6248 10.0971 11.6248C10.6056 11.6248 10.971 11.9043 11.2261 12.2005C11.4572 12.4689 11.6472 12.8095 11.8053 13.0931C11.8159 13.1121 11.8264 13.1308 11.8367 13.1493C12.0173 13.4722 12.159 13.7125 12.3092 13.8666C12.4379 13.9987 12.5135 14.0107 12.5982 13.9941C12.8511 13.9447 13.0714 13.7661 13.3236 13.4333C13.436 13.285 13.5412 13.1248 13.6563 12.9497C13.669 12.9303 13.6819 12.9107 13.6949 12.8909C13.8204 12.7002 13.9598 12.4913 14.1144 12.304C14.2902 12.091 14.6053 12.0608 14.8183 12.2366C15.0313 12.4124 15.0614 12.7275 14.8857 12.9405C14.7698 13.0809 14.6575 13.2474 14.5303 13.4406C14.5174 13.4602 14.5044 13.4801 14.4911 13.5002C14.3789 13.6711 14.2547 13.8603 14.1207 14.0371C13.8239 14.4289 13.4119 14.8539 12.7903 14.9755C12.2722 15.0769 11.874 14.8529 11.593 14.5646C11.3336 14.2984 11.1297 13.9339 10.9639 13.6373L10.9534 13.6186C10.7761 13.3014 10.6312 13.0422 10.4683 12.853C10.3106 12.6699 10.1976 12.6248 10.0971 12.6248C9.98705 12.6248 9.87322 12.672 9.72085 12.842C9.55795 13.0238 9.41237 13.2767 9.23166 13.5925L9.22541 13.6034C9.06123 13.8903 8.86135 14.2397 8.61001 14.5045C8.33998 14.789 7.96227 15.0266 7.45707 14.983C6.99676 14.9433 6.67277 14.6843 6.44492 14.4054C6.23267 14.1456 6.06708 13.8223 5.9306 13.5558L5.91883 13.5329C5.76749 13.2375 5.64642 13.006 5.50824 12.8369C5.37952 12.6793 5.28042 12.6312 5.18461 12.623C5.09026 12.6148 4.99302 12.6436 4.84847 12.8012C4.69002 12.9739 4.54579 13.2253 4.36516 13.5471C4.36023 13.5559 4.35526 13.5648 4.35027 13.5737C4.18837 13.8623 3.99187 14.2126 3.74728 14.4802Z"
                                                                          Fill="{StaticResource IconColor}"
                                                                          Stretch="Fill" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                        </ContextMenu>
                                                    </ToggleButton.ContextMenu>
                                                </ToggleButton>
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal">
                                                <ToggleButton Name="PART_FreeText"
                                                              Width="24"
                                                              Height="24"
                                                              Margin="8,0,0,0"
                                                              Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                                    <ToggleButton.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Free text box
                                                        </ToolTip>
                                                    </ToggleButton.ToolTip>
                                                    <Path Name="PART_FreeTextPathWindows11" 
                                                          Width="16"
                                                          Height="15"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Data="M2.12699 0.0365088C2.49555 -0.036803 2.87758 0.00082323 3.22476 0.14463C3.57193 0.288436 3.86867 0.531963 4.07745 0.844416C4.19188 1.01567 4.27706 1.20341 4.33069 1.4H11.8646C11.9183 1.20341 12.0034 1.01567 12.1179 0.844416C12.3266 0.531963 12.6234 0.288436 12.9706 0.14463C13.3177 0.000823259 13.6998 -0.0368031 14.0683 0.0365088C14.4369 0.109821 14.7754 0.290778 15.0412 0.556497C15.3069 0.822216 15.4878 1.16076 15.5611 1.52933C15.6345 1.89789 15.5968 2.27992 15.453 2.6271C15.3092 2.97428 15.0657 3.27102 14.7532 3.47979C14.582 3.59422 14.3942 3.6794 14.1977 3.73303V11.267C14.3942 11.3206 14.582 11.4058 14.7532 11.5202C15.0657 11.729 15.3092 12.0257 15.453 12.3729C15.5968 12.7201 15.6345 13.1021 15.5611 13.4707C15.4878 13.8392 15.3069 14.1778 15.0412 14.4435C14.7754 14.7092 14.4369 14.8902 14.0683 14.9635C13.6998 15.0368 13.3177 14.9992 12.9706 14.8554C12.6234 14.7116 12.3266 14.468 12.1179 14.1556C12.0034 13.9843 11.9182 13.7966 11.8646 13.6H4.33069C4.27706 13.7966 4.19188 13.9843 4.07745 14.1556C3.86867 14.468 3.57194 14.7115 3.22476 14.8554C2.87758 14.9992 2.49555 15.0368 2.12699 14.9635C1.75842 14.8902 1.41987 14.7092 1.15415 14.4435C0.888434 14.1778 0.707477 13.8392 0.634165 13.4707C0.560853 13.1021 0.59848 12.7201 0.742286 12.3729C0.886093 12.0257 1.12962 11.729 1.44207 11.5202C1.61333 11.4058 1.80107 11.3206 1.99766 11.267V3.73303C1.80107 3.6794 1.61333 3.59422 1.44207 3.47979C1.12962 3.27102 0.886093 2.97428 0.742286 2.6271C0.59848 2.27992 0.560853 1.89789 0.634165 1.52933C0.707477 1.16076 0.888435 0.822217 1.15415 0.556497C1.41987 0.290778 1.75842 0.109821 2.12699 0.0365088ZM2.99766 12.1667C2.99766 12.406 3.19167 12.6 3.43099 12.6H12.7643C13.0036 12.6 13.1977 12.406 13.1977 12.1667V2.83333C13.1977 2.59401 13.0036 2.4 12.7643 2.4H3.43099C3.19167 2.4 2.99766 2.59401 2.99766 2.83333V12.1667ZM3.25317 1.41092C2.60373 1.49128 2.08894 2.00607 2.00858 2.65551C2.00492 2.65314 2.00127 2.65075 1.99764 2.64832C1.84964 2.54943 1.73428 2.40887 1.66617 2.24441C1.59805 2.07996 1.58022 1.899 1.61495 1.72442C1.64968 1.54984 1.73539 1.38947 1.86126 1.2636C1.98713 1.13774 2.14749 1.05202 2.32208 1.01729C2.49666 0.982567 2.67762 1.00039 2.84207 1.06851C3.00653 1.13663 3.14709 1.25198 3.24598 1.39999C3.2484 1.40362 3.2508 1.40726 3.25317 1.41092ZM12.9421 1.41092C13.5916 1.49128 14.1064 2.00607 14.1867 2.65551C14.1904 2.65314 14.194 2.65075 14.1977 2.64832C14.3457 2.54943 14.461 2.40887 14.5291 2.24441C14.5973 2.07996 14.6151 1.899 14.5804 1.72442C14.5456 1.54984 14.4599 1.38947 14.3341 1.2636C14.2082 1.13774 14.0478 1.05202 13.8732 1.01729C13.6987 0.982567 13.5177 1.00039 13.3532 1.06851C13.1888 1.13663 13.0482 1.25198 12.9493 1.39999C12.9469 1.40362 12.9445 1.40726 12.9421 1.41092ZM14.1867 12.3445C14.1064 12.9939 13.5916 13.5087 12.9421 13.5891C12.9445 13.5927 12.9469 13.5964 12.9493 13.6C13.0482 13.748 13.1888 13.8634 13.3532 13.9315C13.5177 13.9996 13.6987 14.0174 13.8732 13.9827C14.0478 13.948 14.2082 13.8623 14.3341 13.7364C14.4599 13.6105 14.5456 13.4502 14.5804 13.2756C14.6151 13.101 14.5973 12.92 14.5291 12.7556C14.461 12.5911 14.3457 12.4506 14.1977 12.3517C14.194 12.3493 14.1904 12.3469 14.1867 12.3445ZM3.25317 13.5891C2.60373 13.5087 2.08894 12.9939 2.00858 12.3445C2.00492 12.3468 2.00127 12.3492 1.99764 12.3517C1.84964 12.4506 1.73428 12.5911 1.66617 12.7556C1.59805 12.92 1.58022 13.101 1.61495 13.2756C1.64968 13.4501 1.73539 13.6105 1.86126 13.7364C1.98713 13.8622 2.14749 13.948 2.32208 13.9827C2.49666 14.0174 2.67762 13.9996 2.84207 13.9315C3.00653 13.8634 3.14709 13.748 3.24598 13.6C3.2484 13.5964 3.2508 13.5927 3.25317 13.5891ZM6.23099 5.2C5.99167 5.2 5.79766 5.39401 5.79766 5.63333V6.09999C5.79766 6.37614 5.5738 6.59999 5.29766 6.59999C5.02151 6.59999 4.79766 6.37614 4.79766 6.09999V5.63333C4.79766 4.84172 5.43938 4.2 6.23099 4.2H9.96432C10.7559 4.2 11.3977 4.84172 11.3977 5.63333V6.09999C11.3977 6.37614 11.1738 6.59999 10.8977 6.59999C10.6215 6.59999 10.3977 6.37614 10.3977 6.09999V5.63333C10.3977 5.39401 10.2036 5.2 9.96432 5.2H8.59766V9.79999H9.49766C9.7738 9.79999 9.99766 10.0238 9.99766 10.3C9.99766 10.5761 9.7738 10.8 9.49766 10.8H6.69766C6.42151 10.8 6.19766 10.5761 6.19766 10.3C6.19766 10.0238 6.42151 9.79999 6.69766 9.79999H7.59766V5.2H6.23099Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                          Stretch="Fill" />
                                                </ToggleButton>
                                            </StackPanel>
                                            <ToggleButton Name="PART_Ink"
                                                          Width="32"
                                                          Height="24"
                                                          Margin="8,0,0,0"
                                                          Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                                <ToggleButton.ToolTip>
                                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                        Draw free form
                                                    </ToolTip>
                                                </ToggleButton.ToolTip>
                                                <StackPanel Orientation="Horizontal">
                                                    <Path Name="PART_InkPathWindows11"
                                                          Width="15"
                                                          Height="15"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Data="M10.886 0.711919C11.8272 -0.237306 13.355 -0.237306 14.2962 0.711919C15.2351 1.65879 15.2351 3.19224 14.2962 4.13911L5.01156 13.5028C4.84594 13.6698 4.64237 13.7943 4.41805 13.8653L3.99234 14H10.3824C10.6585 14 10.8824 14.2239 10.8824 14.5C10.8824 14.7761 10.6585 15 10.3824 15H0.836082C0.815723 15 0.795649 14.9988 0.775927 14.9964C0.267892 15.0335 -0.135686 14.5234 0.0430684 14.0202L1.24734 10.6307C1.31946 10.4277 1.43539 10.243 1.58709 10.09L10.886 0.711919ZM1.16651 13.8452L4.11641 12.9119C4.18585 12.8899 4.24939 12.8512 4.30147 12.7987L12.0696 4.96439C12.0215 4.94056 11.9765 4.90851 11.9366 4.86826L10.0548 2.97046L2.29719 10.7941C2.24948 10.8422 2.21265 10.9007 2.18964 10.9655L1.16651 13.8452ZM10.7589 2.26034L12.6467 4.16416C12.6845 4.20229 12.7149 4.24498 12.7379 4.29044L13.5861 3.43501C14.1384 2.87796 14.1384 1.97307 13.5861 1.41602C13.0361 0.861326 12.1461 0.861326 11.5961 1.41602L10.7589 2.26034Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                          FlowDirection="LeftToRight"
                                                          Stretch="Fill" />
                                                    <Path Width="10"
                                                          Height="5"
                                                          Margin="4,3,0,0"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Data="M0 0.538462C0 0.392628 0.0494792 0.266426 0.148438 0.159856C0.247396 0.0532853 0.364583 0 0.5 0C0.635417 0 0.752604 0.0532853 0.851562 0.159856L6 5.69591L11.1484 0.159856C11.2474 0.0532853 11.3646 0 11.5 0C11.6354 0 11.7526 0.0532853 11.8516 0.159856C11.9505 0.266426 12 0.392628 12 0.538462C12 0.684295 11.9505 0.810497 11.8516 0.917067L6.35156 6.84014C6.2526 6.94671 6.13542 7 6 7C5.86458 7 5.7474 6.94671 5.64844 6.84014L0.148438 0.917067C0.0494792 0.810497 0 0.684295 0 0.538462Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                          Stretch="Fill" />
                                                </StackPanel>
                                            </ToggleButton>

                                            <ToggleButton Name="PART_InkEraser"
                                                          Width="24"
                                                          Height="24"
                                                          Margin="8,0,0,0"
                                                          Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                                <ToggleButton.ToolTip>
                                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                        Click to erase a part of the ink annotation (freehand drawings)
                                                    </ToolTip>
                                                </ToggleButton.ToolTip>
                                                <StackPanel Orientation="Horizontal">
                                                    <Path Width="15"
                                                          Height="15"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Data="M8.46284 0.438871C9.048 -0.14629 9.99673 -0.146291 10.5819 0.438871L14.5616 4.41859C15.1468 5.00376 15.1468 5.95249 14.5616 6.53765L7.1059 13.9934H9.41871C9.69485 13.9934 9.91871 14.2172 9.91871 14.4934C9.91871 14.7695 9.69485 14.9934 9.41871 14.9934H5.61982C5.19076 15.0337 4.74762 14.8897 4.41908 14.5611L0.43936 10.5814C-0.145802 9.99624 -0.145802 9.04751 0.43936 8.46235L8.46284 0.438871ZM5.55986 13.9934H5.42511C5.41701 13.9934 5.40895 13.9936 5.40094 13.9939C5.30036 13.9782 5.20369 13.9315 5.12619 13.854L1.14647 9.8743C0.951829 9.67966 0.951829 9.36409 1.14647 9.16946L3.02076 7.29516C3.0367 7.31752 3.05469 7.33886 3.07475 7.35892L7.56756 11.8517C7.61456 11.8987 7.66859 11.9344 7.72627 11.9588L5.83103 13.854C5.75445 13.9306 5.65915 13.9771 5.55986 13.9934ZM8.38173 11.3033L13.8545 5.83054C14.0491 5.63591 14.0491 5.32034 13.8545 5.1257L9.87479 1.14598C9.68015 0.951341 9.36458 0.951341 9.16994 1.14598L3.7181 6.59782C3.74046 6.61376 3.7618 6.63175 3.78186 6.65181L8.27466 11.1446C8.32166 11.1916 8.35735 11.2457 8.38173 11.3033ZM10.9155 14.4934C10.9155 14.2172 11.1394 13.9934 11.4155 13.9934H13.4123C13.6885 13.9934 13.9123 14.2172 13.9123 14.4934C13.9123 14.7695 13.6885 14.9934 13.4123 14.9934H11.4155C11.1394 14.9934 10.9155 14.7695 10.9155 14.4934Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                          FlowDirection="LeftToRight" />
                                                </StackPanel>
                                            </ToggleButton>
                                            <StackPanel Orientation="Horizontal">
                                                <ToggleButton Name="PART_Shapes"
                                                              Width="34"
                                                              Height="24"
                                                              Margin="8,0,0,0"
                                                              Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                                    <ToggleButton.ToolTip>
                                                        <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                            Drawing Tools
                                                        </ToolTip>
                                                    </ToggleButton.ToolTip>
                                                    <StackPanel Orientation="Horizontal">
                                                        <Path Width="15"
                                                              Height="15"
                                                              Margin="0,2,0,0"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Data="M12.5 0.5C13.6046 0.5 14.5 1.39543 14.5 2.5V9.5C14.5 10.6046 13.6046 11.5 12.5 11.5H10.084C9.3124 13.2659 7.55032 14.5 5.5 14.5C2.73858 14.5 0.5 12.2614 0.5 9.5C0.5 7.44968 1.7341 5.6876 3.5 4.91604V2.5C3.5 1.39543 4.39543 0.5 5.5 0.5H12.5ZM4.5 4.60002V2.5C4.5 1.94772 4.94771 1.5 5.5 1.5H12.5C13.0523 1.5 13.5 1.94772 13.5 2.5V9.5C13.5 10.0523 13.0523 10.5 12.5 10.5H10.4C10.4656 10.1769 10.5 9.84247 10.5 9.5C10.5 6.73858 8.26142 4.5 5.5 4.5C5.15753 4.5 4.82311 4.53443 4.5 4.60002ZM9.5 9.5C9.5 7.29086 7.70914 5.5 5.5 5.5C3.29086 5.5 1.5 7.29086 1.5 9.5C1.5 11.7091 3.29086 13.5 5.5 13.5C7.70914 13.5 9.5 11.7091 9.5 9.5Z"
                                                              Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                              FlowDirection="LeftToRight"
                                                              Stretch="Fill" />
                                                        <Path Width="10"
                                                              Height="5"
                                                              Margin="4,3,0,0"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Data="M0 0.538462C0 0.392628 0.0494792 0.266426 0.148438 0.159856C0.247396 0.0532853 0.364583 0 0.5 0C0.635417 0 0.752604 0.0532853 0.851562 0.159856L6 5.69591L11.1484 0.159856C11.2474 0.0532853 11.3646 0 11.5 0C11.6354 0 11.7526 0.0532853 11.8516 0.159856C11.9505 0.266426 12 0.392628 12 0.538462C12 0.684295 11.9505 0.810497 11.8516 0.917067L6.35156 6.84014C6.2526 6.94671 6.13542 7 6 7C5.86458 7 5.7474 6.94671 5.64844 6.84014L0.148438 0.917067C0.0494792 0.810497 0 0.684295 0 0.538462Z"
                                                              Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                              Stretch="Fill" />
                                                    </StackPanel>

                                                    <ToggleButton.ContextMenu>
                                                        <ContextMenu Style="{StaticResource WPFContextMenuStyle}">
                                                            <MenuItem Name="PART_LineMenuItem"
                                                                      Header="Line">
                                                                <MenuItem.Icon>
                                                                    <Path Canvas.Left="30.975"
                                                                          Canvas.Top="95.474"
                                                                          Width="14"
                                                                          Height="14"
                                                                          Margin="8,0,8,0"
                                                                          Data="M12.8536 0.146447C13.0488 0.341709 13.0488 0.658291 12.8536 0.853553L0.853553 12.8536C0.658291 13.0488 0.341709 13.0488 0.146447 12.8536C-0.0488155 12.6583 -0.0488155 12.3417 0.146447 12.1464L12.1464 0.146447C12.3417 -0.0488155 12.6583 -0.0488155 12.8536 0.146447Z"
                                                                          Fill="{StaticResource IconColor}"
                                                                          Stretch="Fill" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                            <MenuItem Name="PART_RectangleMenuItem"
                                                                      Header="Rectangle">
                                                                <MenuItem.Icon>
                                                                    <Path Canvas.Left="56.313"
                                                                          Canvas.Top="96.479"
                                                                          Width="16"
                                                                          Height="12"
                                                                          Margin="8,0,8,0"
                                                                          Data="M14.5 1H1.5C1.30398 1 1 1.20122 1 1.63636V10.3636C1 10.7988 1.30398 11 1.5 11H14.5C14.696 11 15 10.7988 15 10.3636V1.63636C15 1.20122 14.696 1 14.5 1ZM1.5 0C0.671573 0 0 0.732625 0 1.63636V10.3636C0 11.2674 0.671573 12 1.5 12H14.5C15.3284 12 16 11.2674 16 10.3636V1.63636C16 0.732625 15.3284 0 14.5 0H1.5Z"
                                                                          Fill="{StaticResource IconColor}"
                                                                          Stretch="Fill" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                            <MenuItem Name="PART_CircleMenuItem"
                                                                      Header="Circle">
                                                                <MenuItem.Icon>
                                                                    <Path Canvas.Left="80.375"
                                                                          Canvas.Top="92.854"
                                                                          Width="15"
                                                                          Height="15"
                                                                          Margin="8,0,8,0"
                                                                          Data="M7.5 1C3.91015 1 1 3.91015 1 7.5C1 11.0899 3.91015 14 7.5 14C11.0899 14 14 11.0899 14 7.5C14 3.91015 11.0899 1 7.5 1ZM0 7.5C0 3.35786 3.35786 0 7.5 0C11.6421 0 15 3.35786 15 7.5C15 11.6421 11.6421 15 7.5 15C3.35786 15 0 11.6421 0 7.5Z"
                                                                          Fill="{StaticResource IconColor}"
                                                                          Stretch="Fill" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                            <MenuItem Name="PART_ArrowMenuItem"
                                                                      Header="Arrow">
                                                                <MenuItem.Icon>
                                                                    <Path Canvas.Left="30.975"
                                                                          Canvas.Top="95.474"
                                                                          Width="14"
                                                                          Height="14"
                                                                          Margin="8,0,8,0"
                                                                          Data="M8 0H12.5C12.7761 0 13 0.223858 13 0.5V5C13 5.27614 12.7761 5.5 12.5 5.5C12.2239 5.5 12 5.27614 12 5V1.70711L0.853553 12.8536C0.658291 13.0488 0.341709 13.0488 0.146447 12.8536C-0.0488155 12.6583 -0.0488155 12.3417 0.146447 12.1464L11.2929 1H8C7.72386 1 7.5 0.776142 7.5 0.5C7.5 0.223858 7.72386 0 8 0Z"
                                                                          Fill="{StaticResource IconColor}"
                                                                          Stretch="Fill" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                            <MenuItem Name="PART_PolygonMenuItem"
                                                                      Header="Polygon">
                                                                <MenuItem.Icon>
                                                                    <Path Canvas.Left="80.375"
                                                                          Canvas.Top="92.854"
                                                                          Width="12"
                                                                          Height="14"
                                                                          Margin="8,0,8,0"
                                                                          Data="M10.7519 3.82228L6.25194 1.10547C6.09625 1.01148 5.90375 1.01148 5.74807 1.10547L1.24806 3.82228C1.09446 3.91502 1 4.08522 1 4.26927V9.73073C1 9.91478 1.09446 10.085 1.24806 10.1777L5.74806 12.8945C5.90375 12.9885 6.09625 12.9885 6.25194 12.8945L10.7519 10.1777C10.9055 10.085 11 9.91478 11 9.73073V4.26927C11 4.08522 10.9055 3.91502 10.7519 3.82228ZM6.75581 0.211479C6.28876 -0.0704925 5.71124 -0.070493 5.24419 0.211478L0.744194 2.92829C0.283367 3.20651 0 3.71711 0 4.26927V9.73073C0 10.2829 0.283366 10.7935 0.744193 11.0717L5.24419 13.7885C5.71124 14.0705 6.28876 14.0705 6.75581 13.7885L11.2558 11.0717C11.7166 10.7935 12 10.2829 12 9.73073V4.26927C12 3.71711 11.7166 3.20651 11.2558 2.92829L6.75581 0.211479Z"
                                                                          Fill="{StaticResource IconColor}"
                                                                          Stretch="Fill" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                            <MenuItem Name="PART_PolylineMenuItem"
                                                                      Header="Polyline">
                                                                <MenuItem.Icon>
                                                                    <Path Canvas.Left="80.375"
                                                                          Canvas.Top="92.854"
                                                                          Width="14"
                                                                          Height="12"
                                                                          Margin="8,0,8,0"
                                                                          Data="M1.01149 5.92894C0.921308 6.07966 0.921308 6.2682 1.01149 6.41891L3.64493 10.82C3.7787 11.0436 4.06667 11.1166 4.28978 10.9836L10.3036 7.39787C10.5279 7.26417 10.8173 7.33872 10.9502 7.56437C11.083 7.79003 11.009 8.08134 10.7847 8.21504L4.7709 11.8008C4.10158 12.1999 3.23767 11.9807 2.83635 11.31L0.202911 6.90889C-0.0676372 6.45674 -0.0676368 5.89112 0.202911 5.43897L0.602008 5.68081L0.202911 5.43897L3.04462 0.689794C3.30075 0.261745 3.76098 0 4.25749 0L9.97463 0C10.5001 0 10.9823 0.292825 11.2272 0.760621L13.9455 5.95255C14.067 6.1846 13.9786 6.47183 13.748 6.59409C13.5174 6.71636 13.232 6.62736 13.1105 6.39531L10.3921 1.20337C10.3105 1.04744 10.1498 0.949835 9.97463 0.949835L4.25749 0.949835C4.09199 0.949835 3.93858 1.03708 3.8532 1.17977L1.01149 5.92894Z"
                                                                          Fill="{StaticResource IconColor}"
                                                                          Stretch="Fill" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                            <MenuItem Name="PART_CloudMenuItem"
                                                                      Header="Cloud">
                                                                <MenuItem.Icon>
                                                                    <Path Canvas.Left="80.375"
                                                                          Canvas.Top="92.854"
                                                                          Width="16"
                                                                          Height="11"
                                                                          Data="M8 1C5.90893 1 4.26452 2.55103 4.21543 4.41289C4.20617 4.76433 3.91394 4.99247 3.62225 5.00141C2.13434 5.04698 1 6.17819 1 7.5C1 8.84878 2.18218 10 3.71429 10H12.2857C13.8178 10 15 8.84878 15 7.5C15 6.17819 13.8657 5.04698 12.3778 5.00141C12.0861 4.99247 11.7938 4.76433 11.7846 4.41289C11.7355 2.55103 10.0911 1 8 1ZM3.24014 4.02841C3.4923 1.73897 5.55502 0 8 0C10.445 0 12.5077 1.73897 12.7599 4.02841C14.5621 4.24614 16 5.69132 16 7.5C16 9.46493 14.304 11 12.2857 11H3.71429C1.69599 11 0 9.46493 0 7.5C0 5.69132 1.43788 4.24614 3.24014 4.02841Z"
                                                                          Fill="{StaticResource IconColor}"
                                                                          Stretch="Fill" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                            <MenuItem Name="PART_CallOutMenuItem"
                                                                      Header="Text Callout">
                                                                <MenuItem.Icon>
                                                                    <Path Canvas.Left="30.975"
                                                                          Canvas.Top="95.474"
                                                                          Width="16"
                                                                          Height="11"
                                                                          Margin="8,0,8,0"
                                                                          Data="M4.38516 0.725926C4.60252 0.378498 4.9843 0 5.5148 0H14.4978C15.4243 0 15.9959 0.894347 15.9959 1.75V9.25C15.9959 10.1057 15.4243 11 14.4978 11H5.5148C5.15013 11 4.76889 10.8973 4.47326 10.6294C4.16925 10.3538 4.0167 9.95876 4.0167 9.5V5.22979C3.93826 5.16139 3.81879 5.07932 3.65201 4.99457C3.41901 4.87618 3.12379 4.76792 2.7901 4.68999C2.35465 4.58831 1.87412 4.54286 1.40118 4.58248L1.88469 5.18801C2.057 5.4038 2.02175 5.71842 1.80596 5.89072C1.59017 6.06303 1.27555 6.02778 1.10325 5.81199L0.112203 4.57083C0.112202 4.57083 0.112201 4.57083 0.1122 4.57083C-0.037402 4.38347 -0.0373982 4.11653 0.1122 3.92917L1.10325 2.68801C1.27555 2.47222 1.59017 2.43697 1.80596 2.60928C2.02175 2.78158 2.057 3.0962 1.88469 3.31199L1.67974 3.56866C2.14898 3.56281 2.6054 3.61996 3.0175 3.71619C3.38269 3.80147 3.72305 3.91967 4.0167 4.05961V2C4.0167 1.5579 4.15648 1.09144 4.38516 0.725926ZM5.23292 1.25631C5.10035 1.4682 5.0167 1.75174 5.0167 2V9.5C5.0167 9.7316 5.08758 9.83653 5.14482 9.88841C5.21043 9.94788 5.32824 10 5.5148 10H14.4978C14.6737 10 14.9959 9.77506 14.9959 9.25V1.75C14.9959 1.22494 14.6737 1 14.4978 1H5.5148C5.49407 1 5.3768 1.02632 5.23292 1.25631ZM6.48356 4.25C6.48356 3.97386 6.70741 3.75 6.98356 3.75H13.4712C13.7474 3.75 13.9712 3.97386 13.9712 4.25C13.9712 4.52614 13.7474 4.75 13.4712 4.75H6.98356C6.70741 4.75 6.48356 4.52614 6.48356 4.25ZM6.48356 6.75C6.48356 6.47386 6.70741 6.25 6.98356 6.25H13.4712C13.7474 6.25 13.9712 6.47386 13.9712 6.75C13.9712 7.02614 13.7474 7.25 13.4712 7.25H6.98356C6.70741 7.25 6.48356 7.02614 6.48356 6.75Z"
                                                                          Fill="{StaticResource IconColor}"
                                                                          Stretch="Fill" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                        </ContextMenu>
                                                    </ToggleButton.ContextMenu>
                                                </ToggleButton>
                                            </StackPanel>

                                            <ToggleButton Name="PART_Stamp"
                                                          Width="36"
                                                          Height="24"
                                                          Margin="8,0,0,0"
                                                          Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                                <ToggleButton.ToolTip>
                                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                        Click to add stamp
                                                    </ToolTip>
                                                </ToggleButton.ToolTip>
                                                <StackPanel Orientation="Horizontal">
                                                    <Path Name="PART_StampPathWindows11" 
                                                          Width="14"
                                                          Height="14"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Data="M7.09672 0C7.96001 0 8.7863 0.3752 9.42203 1.08464C10.2592 2.28926 10.3213 3.91996 9.59195 5.16924C8.94565 6.44642 8.56617 7.85706 8.47813 9.29321L8.46221 9.65271V11H8.59766H13.0977C13.3738 11 13.5977 11.2239 13.5977 11.5C13.5977 11.7761 13.3738 12 13.0977 12H1.09766C0.821514 12 0.597656 11.7761 0.597656 11.5C0.597656 11.2239 0.821514 11 1.09766 11H5.73124L5.73142 9.66604C5.68867 8.09309 5.30165 6.55285 4.61565 5.19515C3.86686 3.92217 3.92921 2.2866 4.8071 1.03966C5.40714 0.375199 6.23344 0 7.09672 0ZM7.60628 9.63938L7.6061 11H6.58734V9.65271L6.57157 9.28287C6.47844 7.68383 6.06364 6.13457 5.35424 4.73266C4.78037 3.75523 4.82698 2.53269 5.45726 1.63317C5.86133 1.19086 6.4655 0.916523 7.09672 0.916523C7.72794 0.916523 8.33212 1.19086 8.77085 1.6767C9.36197 2.53367 9.40852 3.75465 8.85247 4.70828C8.07523 6.24241 7.65293 7.92305 7.60628 9.63938ZM3.09766 13C2.82151 13 2.59766 13.2239 2.59766 13.5C2.59766 13.7761 2.82151 14 3.09766 14H11.0977C11.3738 14 11.5977 13.7761 11.5977 13.5C11.5977 13.2239 11.3738 13 11.0977 13H3.09766Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                          Stretch="Fill" />
                                                    <Path Width="10"
                                                          Height="5"
                                                          Margin="4,3,0,0"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Data="M0 0.538462C0 0.392628 0.0494792 0.266426 0.148438 0.159856C0.247396 0.0532853 0.364583 0 0.5 0C0.635417 0 0.752604 0.0532853 0.851562 0.159856L6 5.69591L11.1484 0.159856C11.2474 0.0532853 11.3646 0 11.5 0C11.6354 0 11.7526 0.0532853 11.8516 0.159856C11.9505 0.266426 12 0.392628 12 0.538462C12 0.684295 11.9505 0.810497 11.8516 0.917067L6.35156 6.84014C6.2526 6.94671 6.13542 7 6 7C5.86458 7 5.7474 6.94671 5.64844 6.84014L0.148438 0.917067C0.0494792 0.810497 0 0.684295 0 0.538462Z"
                                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                          Stretch="Fill" />
                                                </StackPanel>
                                                <ToggleButton.ContextMenu>
                                                    <ContextMenu Placement="MousePoint"
                                                                 Style="{StaticResource WPFContextMenuStyle}">
                                                        <MenuItem x:Name="PART_StandardMenu"
                                                                  Header="Standard Business"
                                                                  Width="180"
                                                                  Style="{StaticResource WPFMenuItemStyle}">
                                                            <MenuItem.Margin>
                                                                <Thickness>-10,0,0,0</Thickness>
                                                            </MenuItem.Margin>
                                                            <MenuItem.Resources>
                                                                <Style TargetType="{x:Type Popup}">
                                                                    <Setter Property="MaxHeight"
                                                                            Value="400" />
                                                                </Style>
                                                            </MenuItem.Resources>
                                                        </MenuItem>
                                                        <MenuItem x:Name="PART_CustomStamp"
                                                                  Header="Custom Stamps"
                                                                  Width="180"
                                                                  Style="{StaticResource CustomStampMenuItemStyle}">
                                                            <MenuItem.Resources>
                                                                <Style TargetType="{x:Type Popup}">
                                                                    <Setter Property="MaxHeight"
                                                                            Value="400" />
                                                                </Style>
                                                            </MenuItem.Resources>
                                                        </MenuItem>
                                                    </ContextMenu>
                                                </ToggleButton.ContextMenu>
                                            </ToggleButton>
                                            <ToggleButton Name="PART_StickyNote"
                                                          Width="24"
                                                          Height="24"
                                                          Margin="8,0,0,0"
                                                          Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                                <ToggleButton.ToolTip>
                                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                        Add sticky note
                                                    </ToolTip>
                                                </ToggleButton.ToolTip>
                                                <Path Name="PART_StickyNotePathWindows11" 
                                                      Width="15"
                                                      Height="16"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      Data="M0 1.51677C0 0.686951 0.663767 0 1.5 0H13.5C14.3362 0 15 0.686951 15 1.51677V10.1593C15 10.9891 14.3362 11.6761 13.5 11.6761H8.10229L3.80344 14.8549C3.30205 15.2257 2.625 14.8495 2.625 14.2555V11.6761H1.5C0.663767 11.6761 0 10.9891 0 10.1593V1.51677ZM1.5 1C1.23166 1 1 1.22349 1 1.51677V10.1593C1 10.4526 1.23166 10.6761 1.5 10.6761H2.885C3.3015 10.6761 3.625 11.017 3.625 11.4201V13.7432L7.5771 10.8207C7.70363 10.7271 7.85712 10.6761 8.01554 10.6761H13.5C13.7683 10.6761 14 10.4526 14 10.1593V1.51677C14 1.22349 13.7683 1 13.5 1H1.5ZM3 4.56707C3 4.29093 3.22386 4.06707 3.5 4.06707H11.5C11.7761 4.06707 12 4.29093 12 4.56707C12 4.84321 11.7761 5.06707 11.5 5.06707H3.5C3.22386 5.06707 3 4.84321 3 4.56707ZM3 6.60061C3 6.32446 3.22386 6.10061 3.5 6.10061H8.5C8.77614 6.10061 9 6.32446 9 6.60061C9 6.87675 8.77614 7.10061 8.5 7.10061H3.5C3.22386 7.10061 3 6.87675 3 6.60061Z"
                                                      Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                      FlowDirection="LeftToRight"
                                                      Stretch="Fill" />
                                            </ToggleButton>
                                            <ToggleButton Name="PART_Fill"
                                                          Width="24"
                                                          Height="24"
                                                          Margin="8,0,0,0"
                                                          Style="{StaticResource SyncfusionGlyphToggleButtonStyle}"
                                                          ToolTipService.ShowOnDisabled="true">
                                                <ToggleButton.ToolTip>
                                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                        Color Picker
                                                    </ToolTip>
                                                </ToggleButton.ToolTip>
                                                <Path Width="15"
                                                      Height="15"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      Data="M6.7069 1C6.48793 1 6.29522 1.17821 6.29522 1.41644V2.07975C6.29522 2.47306 6.13649 2.85063 5.8532 3.13115L1.4047 7.53621H11.2132C11.2479 7.37745 11.2026 7.20545 11.0772 7.08127L9.77651 5.79332L10.5094 5.09239L11.8101 6.38034C12.3994 6.96394 12.3994 7.89953 11.8101 8.48313L6.88078 13.3643C6.27675 13.9624 5.28612 13.9624 4.68208 13.3643L0.442019 9.16561C-0.14734 8.58201 -0.147339 7.64643 0.442019 7.06282L5.1203 2.43021C5.21473 2.33671 5.26764 2.21085 5.26764 2.07975V1.41644C5.26764 0.642395 5.90363 5.96046e-08 6.7069 0C7.51018 5.96046e-08 8.14617 0.642395 8.14617 1.41644V4.97499C8.14617 5.25113 7.91613 5.47499 7.63238 5.47499C7.34862 5.47499 7.11859 5.25113 7.11859 4.97499V1.41644C7.11859 1.17821 6.92588 1 6.7069 1ZM10.3157 8.53621H1.24715L5.41498 12.6633C5.61633 12.8627 5.94654 12.8627 6.14788 12.6633L10.3157 8.53621ZM12.1456 9.36423C12.3906 9.01765 12.9165 9.01765 13.1615 9.36423L14.576 11.3653C15.6502 12.8848 14.5642 15 12.6535 15C10.7429 15 9.65685 12.8849 10.731 11.3653L12.1456 9.36423ZM12.6535 10.41L11.5776 11.932C10.9538 12.8146 11.6032 14 12.6535 14C13.7039 14 14.3533 12.8146 13.7294 11.932L12.6535 10.41Z"
                                                      Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                                      Stretch="Fill" />
                                            </ToggleButton>
                                            <Button Name="PART_ButtonTextBoxFont"
                                                    Width="24"
                                                    Height="24"
                                                    Margin="6,0,0,0"
                                                    IsEnabled="False"
                                                    Style="{StaticResource SyncfusionGlyphButtonStyle}"
                                                    ToolTipService.ShowOnDisabled="True">
                                                <Button.ToolTip>
                                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                        Text Properties
                                                    </ToolTip>
                                                </Button.ToolTip>
                                                <Path Width="17"
                                                      Height="11"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      Data="M8.95015 11L7.63214 7.60948H3.29497L1.99229 11H0.597656L4.87352 0H6.1149L10.3754 11H8.95015ZM5.99229 3.06834C5.96164 2.98652 5.91056 2.83821 5.83904 2.62343C5.76752 2.40865 5.696 2.18875 5.62448 1.96374C5.56317 1.7285 5.51209 1.54951 5.47122 1.42678C5.42013 1.63133 5.36394 1.841 5.30264 2.05579C5.25155 2.26034 5.19536 2.44956 5.13405 2.62343C5.08297 2.7973 5.03699 2.94561 4.99612 3.06834L3.75474 6.38215H7.21835L5.99229 3.06834ZM13.845 2C14.7816 2 15.4746 2.23243 15.9238 2.6973C16.373 3.16216 16.5977 3.9027 16.5977 4.91892V10.8378H15.6801L15.4364 9.60541H15.379C15.1592 9.91892 14.9298 10.1838 14.6908 10.4C14.4615 10.6054 14.1938 10.7568 13.888 10.8541C13.5917 10.9514 13.2285 11 12.7984 11C12.3396 11 11.9238 10.9081 11.5511 10.7243C11.1879 10.5405 10.9011 10.2595 10.6908 9.88108C10.4806 9.49189 10.3754 9.00541 10.3754 8.42162C10.3754 7.55676 10.6765 6.89189 11.2787 6.42703C11.8808 5.95135 12.8079 5.69189 14.06 5.64865L15.3647 5.6V5.08108C15.3647 4.35676 15.2261 3.85405 14.9489 3.57297C14.6717 3.29189 14.2799 3.15135 13.7733 3.15135C13.3719 3.15135 12.9895 3.22162 12.6263 3.36216C12.2631 3.49189 11.9238 3.64324 11.6084 3.81622L11.2213 2.74595C11.5558 2.54054 11.9525 2.36757 12.4113 2.22703C12.8701 2.07568 13.348 2 13.845 2ZM14.2177 6.63784C13.2619 6.68108 12.5977 6.85405 12.2249 7.15676C11.8617 7.45946 11.6801 7.88649 11.6801 8.43784C11.6801 8.92432 11.8091 9.28108 12.0672 9.50811C12.3348 9.73513 12.6741 9.84865 13.0851 9.84865C13.7351 9.84865 14.2751 9.64865 14.7052 9.24865C15.1353 8.83784 15.3503 8.21081 15.3503 7.36757V6.58919L14.2177 6.63784Z"
                                                      Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                      FlowDirection="LeftToRight"
                                                      Stretch="Fill" />
                                            </Button>
                                        </StackPanel>
                                    </ScrollViewer>
                                    <RepeatButton x:Name="PART_SecondaryRightScrollButton"
                                                  Width="24"
                                                  Grid.Column="2"
                                                  Style="{StaticResource WPFGlyphRepeatButtonStyle}"
                                                  Background="{StaticResource ContentBackgroundAlt1}">
                                        <RepeatButton.Margin>
                                            <Thickness>0,-1,-2,-2</Thickness>
                                        </RepeatButton.Margin>
                                        <RepeatButton.Effect>
                                            <DropShadowEffect BlurRadius="16"
                                                              ShadowDepth="4.5"
                                                              Direction="180"
                                                              Opacity=".62" />
                                        </RepeatButton.Effect>
                                        <Path Width="6"
                                              Height="11"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Data="M1 10L5.5 5.5L1 1"
                                              Stroke="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type RepeatButton}}}"
                                              StrokeThickness="1" />
                                    </RepeatButton>
                                </Grid>
                                <StackPanel Orientation="Horizontal" Grid.Column="1">
                                    <ToggleButton Name="PART_CommentsPanel"
                                        Width="24"
                                        Height="24"
                                        Margin="0,0,8,0"
                                        HorizontalAlignment="Right"
                                        IsEnabled="True"
                                        Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                        <ToggleButton.ToolTip>
                                            <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                Expand comment section
                                            </ToolTip>
                                        </ToggleButton.ToolTip>
                                        <Path Canvas.Left="0"
                                          Canvas.Top="3.095"
                                              Width="18"
                                              Height="14"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Data="M11.5 1L11.5 13M3 13H15C16.1046 13 17 12.1046 17 11V3C17 1.89543 16.1046 1 15 1H3C1.89543 1 1 1.89543 1 3V11C1 12.1046 1.89543 13 3 13Z"
                                              Stroke="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}" />
                                    </ToggleButton>
                                    <Button Name="PART_CloseAnnotationToolbar"
                                        Grid.Column="4"
                                        Width="24"
                                        Height="24"
                                        Margin="0,0,8,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        IsEnabled="True"
                                        Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                        <Button.ToolTip>
                                            <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                Click to close annotations toolbar
                                            </ToolTip>
                                        </Button.ToolTip>
                                    <Path Canvas.Left="0"
                                          Canvas.Top="3.095"
                                          Width="12"
                                          Height="12"
                                          Data="M6.17773 5.5625L10.2451 9.62988C10.3317 9.71647 10.375 9.81901 10.375 9.9375C10.375 10.056 10.3317 10.1585 10.2451 10.2451C10.1585 10.3317 10.056 10.375 9.9375 10.375C9.81901 10.375 9.71647 10.3317 9.62988 10.2451L5.5625 6.17773L1.49512 10.2451C1.40853 10.3317 1.30599 10.375 1.1875 10.375C1.06901 10.375 0.966471 10.3317 0.879883 10.2451C0.793294 10.1585 0.75 10.056 0.75 9.9375C0.75 9.81901 0.793294 9.71647 0.879883 9.62988L4.94727 5.5625L0.879883 1.49512C0.793294 1.40853 0.75 1.30599 0.75 1.1875C0.75 1.06901 0.793294 0.966471 0.879883 0.879883C0.966471 0.793294 1.06901 0.75 1.1875 0.75C1.30599 0.75 1.40853 0.793294 1.49512 0.879883L5.5625 4.94727L9.62988 0.879883C9.71647 0.793294 9.81901 0.75 9.9375 0.75C10.056 0.75 10.1585 0.793294 10.2451 0.879883C10.3317 0.966471 10.375 1.06901 10.375 1.1875C10.375 1.30599 10.3317 1.40853 10.2451 1.49512L6.17773 5.5625Z"
                                          Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                          Stretch="Fill" />
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </Border>
                        
                    </StackPanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionOutlinePaneStyle" TargetType="{x:Type pdfviewer:OutlinePane}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:OutlinePane}">
                    <Border
                        Background="{StaticResource ContentBackgroundAlt1}"
                        BorderBrush="{StaticResource BorderAlt}"
                        BorderThickness="0,0,1px,0">
                        <StackPanel Name="PART_OutlineStackPanel">
                            <ToggleButton
                                Name="PART_ThumbnailButton"
                                Width="24"
                                Height="24"
                                Margin="0,8,0,0"
                                VerticalAlignment="Top"
                                IsEnabled="False"
                                Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                <ToggleButton.ToolTip>
                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                        Click to view thumbnails
                                    </ToolTip>
                                </ToggleButton.ToolTip>
                                <Path
                                    Width="14"
                                    Height="16"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="M3.93478 1C3.681 1 3.56522 1.17456 3.56522 1.28448V12.2672C3.56522 12.3772 3.681 12.5517 3.93478 12.5517H12.6304C12.8842 12.5517 13 12.3772 13 12.2672V4.53017H10.8913C10.1846 4.53017 9.52174 4.00228 9.52174 3.24569V1H3.93478ZM10.5217 1.62448V3.24569C10.5217 3.35561 10.6375 3.53017 10.8913 3.53017H12.6341L10.5217 1.62448ZM2.56522 1.28448C2.56522 0.527894 3.22807 0 3.93478 0H9.66155C10.0091 0 10.3515 0.124089 10.6114 0.358521L13.5802 3.03691C13.8418 3.27288 14 3.60462 14 3.96287V12.2672C14 13.0238 13.3371 13.5517 12.6304 13.5517H3.93478C3.22807 13.5517 2.56522 13.0238 2.56522 12.2672V1.28448ZM0.5 3.82758C0.776142 3.82758 1 4.05144 1 4.32758V14.5345C1 14.7557 1.20605 15 1.54348 15H9.32609C9.60223 15 9.82609 15.2239 9.82609 15.5C9.82609 15.7761 9.60223 16 9.32609 16H1.54348C0.728307 16 0 15.3797 0 14.5345V4.32758C0 4.05144 0.223858 3.82758 0.5 3.82758Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                    FlowDirection="LeftToRight"
                                    Stretch="Fill" />
                            </ToggleButton>

                            <ToggleButton
                                Name="PART_BookmarkButton"
                                Width="24"
                                Height="24"
                                Margin="0,8,0,0"
                                VerticalAlignment="Top"
                                IsEnabled="False"
                                Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                <ToggleButton.ToolTip>
                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                        Click to view bookmark
                                    </ToolTip>
                                </ToggleButton.ToolTip>
                                <Path
                                    Width="10"
                                    Height="14"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="M11 15L6 11.1111L1 15V2.55556C1 2.143 1.15051 1.74733 1.41842 1.45561C1.68633 1.16389 2.04969 1 2.42857 1H9.57143C9.95031 1 10.3137 1.16389 10.5816 1.45561C10.8495 1.74733 11 2.143 11 2.55556V15Z"
                                    Stroke="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                    FlowDirection="LeftToRight"
                                    Stretch="Fill" />
                            </ToggleButton>

                            <ToggleButton
                                Name="PART_LayerButton"
                                Width="24"
                                Height="24"
                                Margin="0,8,0,0"
                                VerticalAlignment="Top"
                                IsEnabled="False"
                                Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                <ToggleButton.ToolTip>
                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                        Click to view the layers
                                    </ToolTip>
                                </ToggleButton.ToolTip>
                                <Path
                                    Width="12"
                                    Height="15"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="M5.21435 0.231529C5.69508 -0.0771764 6.30492 -0.0771763 6.78565 0.231529L11.3021 3.13182C12.2326 3.72938 12.2326 5.12392 11.3021 5.72147L6.78565 8.62177C6.30492 8.93047 5.69508 8.93047 5.21435 8.62177L0.697904 5.72147C-0.232635 5.12392 -0.232634 3.72938 0.697905 3.13182L5.21435 0.231529ZM6.24531 1.07297C6.09379 0.975676 5.90621 0.975676 5.75469 1.07297L1.23825 3.97327C0.920584 4.17726 0.920584 4.67604 1.23825 4.88003L5.75469 7.78032C5.90621 7.87762 6.09379 7.87762 6.24531 7.78032L10.7618 4.88003C11.0794 4.67604 11.0794 4.17726 10.7618 3.97327L6.24531 1.07297ZM0.173919 7.74229C0.326679 7.51225 0.637001 7.4496 0.867042 7.60236L5.74855 10.844C5.90313 10.9466 6.09687 10.9466 6.25145 10.844L11.133 7.60236C11.363 7.4496 11.6733 7.51225 11.8261 7.74229C11.9788 7.97233 11.9162 8.28266 11.6862 8.43542L6.80464 11.677C6.31486 12.0022 5.68514 12.0022 5.19536 11.677L0.313849 8.43542C0.0838081 8.28266 0.0211593 7.97233 0.173919 7.74229ZM0.173919 10.8214C0.326679 10.5913 0.637001 10.5287 0.867042 10.6814L5.74855 13.923C5.90313 14.0257 6.09687 14.0257 6.25145 13.923L11.133 10.6814C11.363 10.5287 11.6733 10.5913 11.8261 10.8214C11.9788 11.0514 11.9162 11.3617 11.6862 11.5145L6.80464 14.7561C6.31486 15.0813 5.68514 15.0813 5.19536 14.7561L0.313849 11.5145C0.0838081 11.3617 0.0211593 11.0514 0.173919 10.8214Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                    FlowDirection="LeftToRight"
                                    Stretch="Fill" />
                            </ToggleButton>

                            <ToggleButton
                                Name="PART_OrganizePages"
                                Width="24"
                                Height="24"
                                Margin="0,8,0,0"
                                VerticalAlignment="Top"
                                IsEnabled="False"
                                Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                <ToggleButton.ToolTip>
                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                        Rotate, rearrange, or delete pages
                                    </ToolTip>
                                </ToggleButton.ToolTip>
                                <Path
                                    Width="16"
                                    Height="16"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="M15 1H9V8V9V11V13V15H15V1ZM8 1V8V9V11V13V15V16H9H15C15.5523 16 16 15.5523 16 15V1C16 0.447715 15.5523 0 15 0H9C8.44772 0 8 0.447715 8 1ZM6 16V15H4V16H6ZM2 16V15H1V13.5H0V15C0 15.5523 0.447716 16 1 16H2ZM0 10.5H1L1 9H2V8H1C0.447715 8 0 8.44772 0 9V10.5ZM4 8V9H6V8H4ZM7.30697 2.3947C7.52494 2.22516 7.56421 1.91103 7.39468 1.69305C7.22514 1.47508 6.911 1.43581 6.69303 1.60535L3.10394 4.39686L2.35791 3.65083C2.21434 3.50725 1.99841 3.46431 1.81083 3.54201L1.80988 3.5424C1.62229 3.62011 1.5 3.80316 1.5 4.00619V5.99002C1.5 6.27169 1.72834 6.50002 2.01 6.50002H3.97586C4.43021 6.50002 4.65777 5.95068 4.33648 5.6294L3.81655 5.10947L7.30697 2.3947Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                    FlowDirection="LeftToRight"
                                    Stretch="Fill" />
                            </ToggleButton>

                            <ToggleButton
                                Name="PART_RedactButton"
                                Width="24"
                                Height="24"
                                Margin="0,10,0,0"
                                VerticalAlignment="Top"
                                IsEnabled="False"
                                Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                <ToggleButton.ToolTip>
                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                        Click to perform redaction.
                                    </ToolTip>
                                </ToggleButton.ToolTip>
                                <Path
                                    Width="16"
                                    Height="16"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="M15.256 0.744838C14.2629 -0.248279 12.6528 -0.248279 11.6596 0.744838L1.71685 10.6876C1.55058 10.8539 1.42511 11.0564 1.35029 11.2794L0.0323527 15.2061C-0.125897 15.6776 0.323301 16.1268 0.794807 15.9685L4.72151 14.6506C4.94443 14.5758 5.14698 14.4503 5.31325 14.284L15.256 4.34124C16.2492 3.34812 16.2492 1.73796 15.256 0.744838ZM12.3667 1.45195C12.9693 0.849352 13.9463 0.849352 14.5489 1.45195C15.1515 2.05454 15.1515 3.03154 14.5489 3.63413L5.28131 12.9018C5.10001 12.4713 4.8251 11.926 4.45755 11.5584C4.08392 11.1848 3.5266 10.9069 3.09295 10.7257L12.3667 1.45195ZM4.38846 13.3592C4.43166 13.4649 4.4699 13.5658 4.5028 13.6571C4.47143 13.6756 4.4381 13.6909 4.40331 13.7026L1.23488 14.766L2.29831 11.5976C2.309 11.5657 2.3227 11.5351 2.33917 11.5061C2.4356 11.5405 2.54341 11.5812 2.65677 11.6275C3.07971 11.8004 3.51015 12.0252 3.75045 12.2655C3.99074 12.5058 4.21559 12.9363 4.38846 13.3592ZM14.0009 14H7.00088L5.00088 16H12.0009H13.0009H14.0009C14.5532 16 15.0009 15.5523 15.0009 15C15.0009 14.4477 14.5532 14 14.0009 14Z"
                                    Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                    FlowDirection="LeftToRight"
                                    Stretch="Fill" />
                            </ToggleButton>

                            <ToggleButton
                                Name="PART_FormData"
                                Width="24"
                                Height="24"
                                Margin="0,8,0,0"
                                VerticalAlignment="Top"
                                IsEnabled="False"
                                Style="{StaticResource SyncfusionGlyphToggleButtonStyle}">
                                <ToggleButton.ToolTip>
                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                        Form Data
                                    </ToolTip>
                                </ToggleButton.ToolTip>
                                <Path
                                    Width="14"
                                    Height="16"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="M1.50098 1C1.16003 1 1.00098 1.22852 1.00098 1.375V13.625C1.00098 13.7715 1.16003 14 1.50098 14H11.501C11.8419 14 12.001 13.7715 12.001 13.625V5.23744C12.001 5.16116 11.9669 5.07208 11.8788 4.99501L7.46462 1.13257C7.3744 1.05363 7.23933 1 7.08676 1H1.50098ZM0.000976562 1.375C0.000976562 0.554982 0.737352 0 1.50098 0H7.08676C7.46463 0 7.83826 0.130743 8.12312 0.379993L12.5373 4.24243C12.8243 4.49355 13.001 4.84958 13.001 5.23744V13.625C13.001 14.445 12.2646 15 11.501 15H1.50098C0.737352 15 0.000976562 14.445 0.000976562 13.625V1.375ZM3.00098 11C3.00098 10.7239 3.22483 10.5 3.50098 10.5H9.50098C9.77712 10.5 10.001 10.7239 10.001 11C10.001 11.2761 9.77712 11.5 9.50098 11.5H3.50098C3.22483 11.5 3.00098 11.2761 3.00098 11Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type ToggleButton}}}"
                                    FlowDirection="LeftToRight"
                                    Stretch="Fill" />
                            </ToggleButton>
                        </StackPanel>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionBookmarkLabelStyle" TargetType="{x:Type pdfviewer:BookmarkLabel}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:BookmarkLabel}">
                    <Border
                        Background="Transparent"
                        BorderBrush="{StaticResource BorderAlt}"
                        BorderThickness="0,0,1px,1px">
                        <Grid x:Name="PART_LabelGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Label
                                x:Name="PART_Label"
                                Grid.Column="0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Content="Bookmarks"
                                FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                FontSize="12"
                                FontWeight="{StaticResource Windows11Light.FontWeightMedium}" />
                            <Button
                                Name="PART_RemoveLayer"
                                Grid.Column="1"
                                Width="24"
                                Height="24"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                IsEnabled="True"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                <Button.ToolTip>
                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                        Click to delete a layer
                                    </ToolTip>
                                </Button.ToolTip>
                                <Path
                                    Canvas.Left="0"
                                    Canvas.Top="3.095"
                                    Width="12"
                                    Height="12"
                                    Data="M11.5723 2.08008C11.6074 2.0293 11.625 1.96094 11.625 1.875C11.625 1.77344 11.5879 1.68555 11.5137 1.61133C11.4395 1.53711 11.3516 1.5 11.25 1.5H7.83984C7.80078 1.28516 7.72266 1.08594 7.60547 0.902344C7.49219 0.71875 7.35156 0.560547 7.18359 0.427734C7.01953 0.291016 6.83594 0.185547 6.63281 0.111328C6.42969 0.0371094 6.21875 0 6 0C5.78125 0 5.57031 0.0371094 5.36719 0.111328C5.16406 0.185547 4.97852 0.291016 4.81055 0.427734C4.64648 0.560547 4.50586 0.71875 4.38867 0.902344C4.27539 1.08594 4.19922 1.28516 4.16016 1.5H0.75C0.648438 1.5 0.560547 1.53711 0.486328 1.61133C0.412109 1.68555 0.375 1.77344 0.375 1.875C0.375 1.95703 0.390625 2.02344 0.421875 2.07422C0.457031 2.125 0.5 2.16406 0.550781 2.19141C0.605469 2.21875 0.666016 2.23633 0.732422 2.24414C0.798828 2.25195 0.865234 2.25586 0.931641 2.25586H1.04883C1.08789 2.25195 1.12695 2.25 1.16602 2.25L2.13867 10.6699C2.16211 10.8574 2.2168 11.0332 2.30273 11.1973C2.38867 11.3574 2.49805 11.498 2.63086 11.6191C2.76367 11.7363 2.91602 11.8301 3.08789 11.9004C3.25977 11.9668 3.43945 12 3.62695 12H8.37305C8.56055 12 8.74023 11.9668 8.91211 11.9004C9.08398 11.8301 9.23633 11.7363 9.36914 11.6191C9.50195 11.498 9.61133 11.3574 9.69727 11.1973C9.7832 11.0332 9.83789 10.8574 9.86133 10.6699L10.834 2.25C10.8652 2.25 10.9004 2.25195 10.9395 2.25586H11.0566C11.127 2.25586 11.1953 2.25195 11.2617 2.24414C11.3281 2.23633 11.3887 2.2207 11.4434 2.19727C11.498 2.16992 11.541 2.13086 11.5723 2.08008ZM6 1.5H4.93945C4.97852 1.38672 5.0332 1.28516 5.10352 1.19531C5.17383 1.10156 5.25586 1.02148 5.34961 0.955078C5.44336 0.888672 5.54492 0.837891 5.6543 0.802734C5.76367 0.767578 5.87891 0.75 6 0.75C6.12109 0.75 6.23633 0.767578 6.3457 0.802734C6.45508 0.837891 6.55664 0.888672 6.65039 0.955078C6.74414 1.02148 6.82617 1.10156 6.89648 1.19531C6.9668 1.28516 7.02148 1.38672 7.06055 1.5H7H6ZM2.88281 10.5879L1.92188 2.25H10.0781L9.11719 10.5879C9.10547 10.6816 9.07812 10.7695 9.03516 10.8516C8.99219 10.9336 8.9375 11.0039 8.87109 11.0625C8.80469 11.1211 8.72852 11.168 8.64258 11.2031C8.55664 11.2344 8.4668 11.25 8.37305 11.25H3.62695C3.43164 11.25 3.26367 11.1895 3.12305 11.0684C2.98633 10.9434 2.90625 10.7832 2.88281 10.5879ZM4.5 4.875V8.625C4.5 8.72656 4.53711 8.81445 4.61133 8.88867C4.68555 8.96289 4.77344 9 4.875 9C4.97656 9 5.06445 8.96289 5.13867 8.88867C5.21289 8.81445 5.25 8.72656 5.25 8.625V4.875C5.25 4.77344 5.21289 4.68555 5.13867 4.61133C5.06445 4.53711 4.97656 4.5 4.875 4.5C4.77344 4.5 4.68555 4.53711 4.61133 4.61133C4.53711 4.68555 4.5 4.77344 4.5 4.875ZM6.75 4.875V8.625C6.75 8.72656 6.78711 8.81445 6.86133 8.88867C6.93555 8.96289 7.02344 9 7.125 9C7.22656 9 7.31445 8.96289 7.38867 8.88867C7.46289 8.81445 7.5 8.72656 7.5 8.625V4.875C7.5 4.77344 7.46289 4.68555 7.38867 4.61133C7.31445 4.53711 7.22656 4.5 7.125 4.5C7.02344 4.5 6.93555 4.53711 6.86133 4.61133C6.78711 4.68555 6.75 4.77344 6.75 4.875Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                    Stretch="Fill" />
                            </Button>

                            <Button
                                Name="PART_AddLayer"
                                Grid.Column="2"
                                Width="24"
                                Height="24"
                                Margin="8,0,0,0"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                IsEnabled="True"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                <Button.ToolTip>
                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                        Click to add a layer
                                    </ToolTip>
                                </Button.ToolTip>
                                <Path
                                    Canvas.Left="0"
                                    Canvas.Top="3.095"
                                    Width="13"
                                    Height="13"
                                    Data="M12.25 6.5625C12.25 6.68099 12.2067 6.78353 12.1201 6.87012C12.0335 6.95671 11.931 7 11.8125 7H7V11.8125C7 11.931 6.95671 12.0335 6.87012 12.1201C6.78353 12.2067 6.68099 12.25 6.5625 12.25C6.44401 12.25 6.34147 12.2067 6.25488 12.1201C6.16829 12.0335 6.125 11.931 6.125 11.8125V7H1.3125C1.19401 7 1.09147 6.95671 1.00488 6.87012C0.918294 6.78353 0.875 6.68099 0.875 6.5625C0.875 6.44401 0.918294 6.34147 1.00488 6.25488C1.09147 6.16829 1.19401 6.125 1.3125 6.125H6.125V1.3125C6.125 1.19401 6.16829 1.09147 6.25488 1.00488C6.34147 0.918294 6.44401 0.875 6.5625 0.875C6.68099 0.875 6.78353 0.918294 6.87012 1.00488C6.95671 1.09147 7 1.19401 7 1.3125V6.125H11.8125C11.931 6.125 12.0335 6.16829 12.1201 6.25488C12.2067 6.34147 12.25 6.44401 12.25 6.5625Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                    Stretch="Fill" />
                            </Button>

                            <Button
                                Name="PART_Close"
                                Grid.Column="3"
                                Width="24"
                                Height="24"
                                Margin="8,0,8,0"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                IsEnabled="True"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                <Button.ToolTip>
                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                        CloseBookmark
                                    </ToolTip>
                                </Button.ToolTip>
                                <Path
                                    Canvas.Left="0"
                                    Canvas.Top="3.095"
                                    Width="11"
                                    Height="11"
                                    Data="M6.17773 5.5625L10.2451 9.62988C10.3317 9.71647 10.375 9.81901 10.375 9.9375C10.375 10.056 10.3317 10.1585 10.2451 10.2451C10.1585 10.3317 10.056 10.375 9.9375 10.375C9.81901 10.375 9.71647 10.3317 9.62988 10.2451L5.5625 6.17773L1.49512 10.2451C1.40853 10.3317 1.30599 10.375 1.1875 10.375C1.06901 10.375 0.966471 10.3317 0.879883 10.2451C0.793294 10.1585 0.75 10.056 0.75 9.9375C0.75 9.81901 0.793294 9.71647 0.879883 9.62988L4.94727 5.5625L0.879883 1.49512C0.793294 1.40853 0.75 1.30599 0.75 1.1875C0.75 1.06901 0.793294 0.966471 0.879883 0.879883C0.966471 0.793294 1.06901 0.75 1.1875 0.75C1.30599 0.75 1.40853 0.793294 1.49512 0.879883L5.5625 4.94727L9.62988 0.879883C9.71647 0.793294 9.81901 0.75 9.9375 0.75C10.056 0.75 10.1585 0.793294 10.2451 0.879883C10.3317 0.966471 10.375 1.06901 10.375 1.1875C10.375 1.30599 10.3317 1.40853 10.2451 1.49512L6.17773 5.5625Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                    Stretch="Fill" />
                            </Button>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionCommentsLabelStyle" TargetType="{x:Type pdfviewer:CommentsLabel}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:CommentsLabel}">
                    <Border BorderBrush="{StaticResource BorderAlt}"
                            BorderThickness="0,0,1,1"
                            Background="Transparent">
                        <Grid x:Name="PART_commentGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Label
                                x:Name="PART_CommentLabel"
                                Grid.Column="0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Content="Comments"
                                FontSize="12"
                                FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                FontWeight="{StaticResource Windows11Light.FontWeightMedium}"/>
                            <Button
                                Name="PART_ExportAndImport"
                                Grid.Column="1"
                                Width="24"
                                Height="24"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                IsEnabled="True"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                <Button.ToolTip>
                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                        Click to expand comments section
                                    </ToolTip>
                                </Button.ToolTip>
                                <Path Name="PART_ExportAndImportPath"
                                      Width="3"
                                      Height="11"
                                      HorizontalAlignment="Center"
                                      Data="M1.09375 0C1.24414 6.57378e-09 1.38542 0.0296224 1.51758 0.0888672C1.64974 0.148112 1.76367 0.225586 1.85938 0.321289C1.95964 0.421549 2.03939 0.53776 2.09863 0.669922C2.15788 0.802083 2.1875 0.943359 2.1875 1.09375C2.1875 1.24414 2.15788 1.38542 2.09863 1.51758C2.03939 1.64974 1.95964 1.76367 1.85938 1.85938C1.76367 1.95964 1.64974 2.03939 1.51758 2.09863C1.38542 2.15788 1.24414 2.1875 1.09375 2.1875C0.94336 2.1875 0.802084 2.15788 0.669922 2.09863C0.537761 2.03939 0.42155 1.95964 0.321289 1.85938C0.225586 1.76367 0.148112 1.64974 0.0888676 1.51758C0.0296228 1.38542 3.75901e-07 1.24414 3.82475e-07 1.09375C3.89049e-07 0.943359 0.0296228 0.802083 0.0888676 0.669922C0.148112 0.53776 0.225586 0.421549 0.321289 0.321289C0.42155 0.225586 0.537761 0.148112 0.669922 0.0888672C0.802084 0.0296224 0.94336 -6.57378e-09 1.09375 0ZM1.09375 4.375C1.24414 4.375 1.38542 4.40462 1.51758 4.46387C1.64974 4.52311 1.76367 4.60059 1.85938 4.69629C1.95964 4.79655 2.03939 4.91276 2.09863 5.04492C2.15788 5.17708 2.1875 5.31836 2.1875 5.46875C2.1875 5.61914 2.15788 5.76042 2.09863 5.89258C2.03939 6.02474 1.95964 6.13867 1.85938 6.23438C1.76367 6.33464 1.64974 6.41439 1.51758 6.47363C1.38542 6.53288 1.24414 6.5625 1.09375 6.5625C0.943359 6.5625 0.802083 6.53288 0.669922 6.47363C0.537761 6.41439 0.42155 6.33464 0.321289 6.23438C0.225586 6.13867 0.148112 6.02474 0.0888674 5.89258C0.0296226 5.76042 1.84664e-07 5.61914 1.91238e-07 5.46875C1.97811e-07 5.31836 0.0296226 5.17708 0.0888674 5.04492C0.148112 4.91276 0.225586 4.79655 0.321289 4.69629C0.42155 4.60059 0.537761 4.52311 0.669922 4.46387C0.802084 4.40462 0.94336 4.375 1.09375 4.375ZM1.09375 8.75C1.24414 8.75 1.38542 8.77962 1.51758 8.83887C1.64974 8.89811 1.76367 8.97559 1.85938 9.07129C1.95964 9.17155 2.03939 9.28776 2.09863 9.41992C2.15788 9.55208 2.1875 9.69336 2.1875 9.84375C2.1875 9.99414 2.15788 10.1354 2.09863 10.2676C2.03939 10.3997 1.95964 10.5137 1.85938 10.6094C1.76367 10.7096 1.64974 10.7894 1.51758 10.8486C1.38542 10.9079 1.24414 10.9375 1.09375 10.9375C0.943359 10.9375 0.802083 10.9079 0.669922 10.8486C0.53776 10.7894 0.421549 10.7096 0.321289 10.6094C0.225586 10.5137 0.148112 10.3997 0.0888672 10.2676C0.0296224 10.1354 -6.57378e-09 9.99414 0 9.84375C6.57378e-09 9.69336 0.0296224 9.55208 0.0888672 9.41992C0.148112 9.28776 0.225586 9.17155 0.321289 9.07129C0.421549 8.97559 0.53776 8.89811 0.669922 8.83887C0.802083 8.77962 0.943359 8.75 1.09375 8.75Z"
                                      Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                      Stretch="Fill" />
                            </Button>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionBookmarkPaneStyle" TargetType="{x:Type pdfviewer:BookmarkPane}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:BookmarkPane}">
                    <Border
                        Background="Transparent"
                        BorderBrush="{StaticResource BorderAlt}"
                        BorderThickness="0,0,1,0">
                        <DockPanel LastChildFill="True">
                            <Grid Margin="0,8,0,0">
                                <ScrollViewer
                                    x:Name="PART_Scroller"
                                    HorizontalScrollBarVisibility="Auto"
                                    VerticalScrollBarVisibility="Auto">
                                    <TreeView x:Name="PART_TreeView" Style="{StaticResource WPFTreeViewStyle}" />
                                </ScrollViewer>
                            </Grid>
                        </DockPanel>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionCommentsPanleStyle" TargetType="{x:Type pdfviewer:CommentsPane}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:CommentsPane}">
                    <Border Background="Transparent"
                            BorderBrush="{StaticResource BorderAlt}" 
                            BorderThickness="0,0,1,0">
                        <DockPanel LastChildFill="True">
                            <Grid>
                                <ScrollViewer HorizontalScrollBarVisibility="Hidden" VerticalScrollBarVisibility="Hidden">
                                    <Grid x:Name="Part_CommentsGrid">
                                        <StackPanel/>
                                    </Grid>
                                </ScrollViewer>
                            </Grid>
                        </DockPanel>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionPageOrganizerPaneStyle" TargetType="{x:Type pdfviewer:PageOrganizerPane}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:PageOrganizerPane}">
                    <Border
                        VerticalAlignment="Center"
                        Background="{StaticResource ContentBackground}"
                        BorderBrush="{StaticResource BorderAlt}"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                        Effect="{StaticResource Default.ShadowDepth4}"
                        BorderThickness="1">
                        <StackPanel VerticalAlignment="Center" Background="Transparent">
                            <Button
                                x:Name="AntiClockWise"
                                Width="24"
                                Height="24"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                <Path
                                    Width="16"
                                    Height="16"
                                    Margin="2,0,0,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="M0.289062 10.1328C0.0963542 9.45052 0 8.73958 0 8C0 7.86458 0.0494792 7.7474 0.148438 7.64844C0.247396 7.54948 0.364583 7.5 0.5 7.5C0.635417 7.5 0.752604 7.54948 0.851562 7.64844C0.950521 7.7474 1 7.86458 1 8C1 8.63542 1.07812 9.24219 1.23438 9.82031C1.38542 10.3984 1.6224 10.9635 1.94531 11.5156C2.26302 12.0573 2.64062 12.5443 3.07812 12.9766C3.51042 13.4089 3.98698 13.776 4.50781 14.0781C5.02344 14.375 5.57812 14.6042 6.17188 14.7656C6.76042 14.9219 7.36979 15 8 15C8.64583 15 9.26823 14.9167 9.86719 14.75C10.4609 14.5833 11.0182 14.349 11.5391 14.0469C12.0547 13.7448 12.526 13.3802 12.9531 12.9531C13.3802 12.526 13.7448 12.0547 14.0469 11.5391C14.349 11.0182 14.5833 10.4609 14.75 9.86719C14.9167 9.26823 15 8.64583 15 8C15 7.35417 14.9167 6.73438 14.75 6.14062C14.5833 5.54167 14.349 4.98438 14.0469 4.46875C13.7396 3.94792 13.375 3.47656 12.9531 3.05469C12.526 2.6276 12.0547 2.26302 11.5391 1.96094C11.0182 1.65365 10.4609 1.41667 9.86719 1.25C9.26823 1.08333 8.64583 1 8 1C7.43229 1 6.875 1.07031 6.32812 1.21094C5.78125 1.34635 5.26042 1.54427 4.76562 1.80469C4.27083 2.0599 3.8125 2.3724 3.39062 2.74219C2.96354 3.11198 2.58594 3.53125 2.25781 4H3H4H5.5C5.63542 4 5.7526 4.04948 5.85156 4.14844C5.95052 4.2474 6 4.36458 6 4.5C6 4.63542 5.95052 4.7526 5.85156 4.85156C5.7526 4.95052 5.63542 5 5.5 5H4H3H1.5C1.39062 5 1.30208 4.97917 1.23438 4.9375C1.16667 4.89062 1.11458 4.83333 1.07812 4.76562C1.04167 4.69271 1.01823 4.61458 1.00781 4.53125C0.997396 4.44271 0.992188 4.35417 0.992188 4.26562C0.992188 3.63542 0.994792 3.00781 1 2.38281V0.5C1 0.364583 1.04948 0.247396 1.14844 0.148438C1.2474 0.0494792 1.36458 0 1.5 0C1.63542 0 1.7526 0.0494792 1.85156 0.148438C1.95052 0.247396 2 0.364583 2 0.5V2.71094C2.38542 2.28385 2.80729 1.90104 3.26562 1.5625C3.71875 1.22396 4.20052 0.940104 4.71094 0.710938C5.22135 0.481771 5.7526 0.307292 6.30469 0.1875C6.85677 0.0625 7.42188 0 8 0C8.73958 0 9.45052 0.0963542 10.1328 0.289062C10.8151 0.476562 11.4531 0.744792 12.0469 1.09375C12.6354 1.4375 13.1745 1.85417 13.6641 2.34375C14.1484 2.82812 14.5651 3.36719 14.9141 3.96094C15.2578 4.54948 15.526 5.1849 15.7188 5.86719C15.9062 6.54948 16 7.26042 16 8C16 8.73958 15.9062 9.45052 15.7188 10.1328C15.526 10.8151 15.2578 11.4531 14.9141 12.0469C14.5651 12.6406 14.1484 13.1797 13.6641 13.6641C13.1797 14.1484 12.6406 14.5651 12.0469 14.9141C11.4531 15.2578 10.8151 15.526 10.1328 15.7188C9.45052 15.9062 8.73958 16 8 16C7.26042 16 6.54948 15.9062 5.86719 15.7188C5.1849 15.526 4.54948 15.2578 3.96094 14.9141C3.36719 14.5651 2.82812 14.1484 2.34375 13.6641C1.85417 13.1745 1.4375 12.6354 1.09375 12.0469C0.744792 11.4531 0.476562 10.8151 0.289062 10.1328Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}" />
                            </Button>

                            <Button
                                x:Name="ClockWise"
                                Width="24"
                                Height="24"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                <Path
                                    Width="16"
                                    Height="16"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="M15.7109 10.1328C15.9036 9.45052 16 8.73958 16 8C16 7.86458 15.9505 7.7474 15.8516 7.64844C15.7526 7.54948 15.6354 7.5 15.5 7.5C15.3646 7.5 15.2474 7.54948 15.1484 7.64844C15.0495 7.7474 15 7.86458 15 8C15 8.63542 14.9219 9.24219 14.7656 9.82031C14.6146 10.3984 14.3776 10.9635 14.0547 11.5156C13.737 12.0573 13.3594 12.5443 12.9219 12.9766C12.4896 13.4089 12.013 13.776 11.4922 14.0781C10.9766 14.375 10.4219 14.6042 9.82812 14.7656C9.23958 14.9219 8.63021 15 8 15C7.35417 15 6.73177 14.9167 6.13281 14.75C5.53906 14.5833 4.98177 14.349 4.46094 14.0469C3.94531 13.7448 3.47396 13.3802 3.04688 12.9531C2.61979 12.526 2.25521 12.0547 1.95312 11.5391C1.65104 11.0182 1.41667 10.4609 1.25 9.86719C1.08333 9.26823 1 8.64583 1 8C1 7.35417 1.08333 6.73438 1.25 6.14062C1.41667 5.54167 1.65104 4.98438 1.95312 4.46875C2.26042 3.94792 2.625 3.47656 3.04688 3.05469C3.47396 2.6276 3.94531 2.26302 4.46094 1.96094C4.98177 1.65365 5.53906 1.41667 6.13281 1.25C6.73177 1.08333 7.35417 1 8 1C8.56771 1 9.125 1.07031 9.67188 1.21094C10.2188 1.34635 10.7396 1.54427 11.2344 1.80469C11.7292 2.0599 12.1875 2.3724 12.6094 2.74219C13.0365 3.11198 13.4141 3.53125 13.7422 4H13H12H10.5C10.3646 4 10.2474 4.04948 10.1484 4.14844C10.0495 4.2474 10 4.36458 10 4.5C10 4.63542 10.0495 4.7526 10.1484 4.85156C10.2474 4.95052 10.3646 5 10.5 5H12H13H14.5C14.6094 5 14.6979 4.97917 14.7656 4.9375C14.8333 4.89062 14.8854 4.83333 14.9219 4.76562C14.9583 4.69271 14.9818 4.61458 14.9922 4.53125C15.0026 4.44271 15.0078 4.35417 15.0078 4.26562C15.0078 3.63542 15.0052 3.00781 15 2.38281V0.5C15 0.364583 14.9505 0.247396 14.8516 0.148438C14.7526 0.0494792 14.6354 0 14.5 0C14.3646 0 14.2474 0.0494792 14.1484 0.148438C14.0495 0.247396 14 0.364583 14 0.5V2.71094C13.6146 2.28385 13.1927 1.90104 12.7344 1.5625C12.2812 1.22396 11.7995 0.940104 11.2891 0.710938C10.7786 0.481771 10.2474 0.307292 9.69531 0.1875C9.14323 0.0625 8.57812 0 8 0C7.26042 0 6.54948 0.0963542 5.86719 0.289062C5.1849 0.476562 4.54688 0.744792 3.95312 1.09375C3.36458 1.4375 2.82552 1.85417 2.33594 2.34375C1.85156 2.82812 1.4349 3.36719 1.08594 3.96094C0.742188 4.54948 0.473958 5.1849 0.28125 5.86719C0.09375 6.54948 0 7.26042 0 8C0 8.73958 0.09375 9.45052 0.28125 10.1328C0.473958 10.8151 0.742188 11.4531 1.08594 12.0469C1.4349 12.6406 1.85156 13.1797 2.33594 13.6641C2.82031 14.1484 3.35938 14.5651 3.95312 14.9141C4.54688 15.2578 5.1849 15.526 5.86719 15.7188C6.54948 15.9062 7.26042 16 8 16C8.73958 16 9.45052 15.9062 10.1328 15.7188C10.8151 15.526 11.4505 15.2578 12.0391 14.9141C12.6328 14.5651 13.1719 14.1484 13.6562 13.6641C14.1458 13.1745 14.5625 12.6354 14.9062 12.0469C15.2552 11.4531 15.5234 10.8151 15.7109 10.1328Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}" />
                            </Button>

                            <Button
                                x:Name="Delete"
                                Width="24"
                                Height="24"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                <Path
                                    Width="16"
                                    Height="16"
                                    Margin="2,0,0,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="M15.4297 2.77344C15.4766 2.70573 15.5 2.61458 15.5 2.5C15.5 2.36458 15.4505 2.2474 15.3516 2.14844C15.2526 2.04948 15.1354 2 15 2H10.4531C10.401 1.71354 10.2969 1.44792 10.1406 1.20312C9.98958 0.958333 9.80208 0.747396 9.57812 0.570312C9.35938 0.388021 9.11458 0.247396 8.84375 0.148438C8.57292 0.0494792 8.29167 0 8 0C7.70833 0 7.42708 0.0494792 7.15625 0.148438C6.88542 0.247396 6.63802 0.388021 6.41406 0.570312C6.19531 0.747396 6.00781 0.958333 5.85156 1.20312C5.70052 1.44792 5.59896 1.71354 5.54688 2H1C0.864583 2 0.747396 2.04948 0.648438 2.14844C0.549479 2.2474 0.5 2.36458 0.5 2.5C0.5 2.60938 0.520833 2.69792 0.5625 2.76562C0.609375 2.83333 0.666667 2.88542 0.734375 2.92188C0.807292 2.95833 0.888021 2.98177 0.976562 2.99219C1.0651 3.0026 1.15365 3.00781 1.24219 3.00781H1.39844C1.45052 3.0026 1.5026 3 1.55469 3L2.85156 14.2266C2.88281 14.4766 2.95573 14.7109 3.07031 14.9297C3.1849 15.1432 3.33073 15.3307 3.50781 15.4922C3.6849 15.6484 3.88802 15.7734 4.11719 15.8672C4.34635 15.9557 4.58594 16 4.83594 16H11.1641C11.4141 16 11.6536 15.9557 11.8828 15.8672C12.112 15.7734 12.3151 15.6484 12.4922 15.4922C12.6693 15.3307 12.8151 15.1432 12.9297 14.9297C13.0443 14.7109 13.1172 14.4766 13.1484 14.2266L14.4453 3C14.487 3 14.5339 3.0026 14.5859 3.00781H14.7422C14.8359 3.00781 14.9271 3.0026 15.0156 2.99219C15.1042 2.98177 15.1849 2.96094 15.2578 2.92969C15.3307 2.89323 15.388 2.84115 15.4297 2.77344ZM8 2H6.58594C6.63802 1.84896 6.71094 1.71354 6.80469 1.59375C6.89844 1.46875 7.00781 1.36198 7.13281 1.27344C7.25781 1.1849 7.39323 1.11719 7.53906 1.07031C7.6849 1.02344 7.83854 1 8 1C8.16146 1 8.3151 1.02344 8.46094 1.07031C8.60677 1.11719 8.74219 1.1849 8.86719 1.27344C8.99219 1.36198 9.10156 1.46875 9.19531 1.59375C9.28906 1.71354 9.36198 1.84896 9.41406 2H9H8ZM9 3H13.4375L12.1562 14.1172C12.1406 14.2422 12.1042 14.3594 12.0469 14.4688C11.9896 14.5781 11.9167 14.6719 11.8281 14.75C11.7396 14.8281 11.638 14.8906 11.5234 14.9375C11.4089 14.9792 11.2891 15 11.1641 15H4.83594C4.57552 15 4.35156 14.9193 4.16406 14.7578C3.98177 14.5911 3.875 14.3776 3.84375 14.1172L2.5625 3H8H9ZM6 6.5V11.5C6 11.6354 6.04948 11.7526 6.14844 11.8516C6.2474 11.9505 6.36458 12 6.5 12C6.63542 12 6.7526 11.9505 6.85156 11.8516C6.95052 11.7526 7 11.6354 7 11.5V6.5C7 6.36458 6.95052 6.2474 6.85156 6.14844C6.7526 6.04948 6.63542 6 6.5 6C6.36458 6 6.2474 6.04948 6.14844 6.14844C6.04948 6.2474 6 6.36458 6 6.5ZM9 6.5V11.5C9 11.6354 9.04948 11.7526 9.14844 11.8516C9.2474 11.9505 9.36458 12 9.5 12C9.63542 12 9.7526 11.9505 9.85156 11.8516C9.95052 11.7526 10 11.6354 10 11.5V6.5C10 6.36458 9.95052 6.2474 9.85156 6.14844C9.7526 6.04948 9.63542 6 9.5 6C9.36458 6 9.2474 6.04948 9.14844 6.14844C9.04948 6.2474 9 6.36458 9 6.5Z"
                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}" />
                            </Button>
                        </StackPanel>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionThumbnailPaneStyle" TargetType="{x:Type pdfviewer:ThumbnailPane}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:ThumbnailPane}">
                    <Border
                        Background="Transparent"
                        BorderBrush="{StaticResource BorderAlt}"
                        CornerRadius="{StaticResource Windows11Light.CornerRadius8}"
                        Effect="{StaticResource Default.ShadowDepth4}"
                        BorderThickness="0,0,1,1">
                        <DockPanel LastChildFill="True">
                            <Grid x:Name="Thumb_ParentGrid">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="32" />
                                    <RowDefinition Height="9.5*" />
                                    <RowDefinition Height="40" />
                                </Grid.RowDefinitions>
                                <Border
                                    Height="32"
                                    Background="Transparent"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    BorderThickness="0,0,1,1">
                                    <Grid>
                                        <Label
                                            x:Name="PART_TitleLabel"
                                            Grid.Row="0"
                                            Margin="8,0,0,0"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Center"
                                            VerticalContentAlignment="Center"
                                            Content="Organize Pages"
                                            FontFamily="{StaticResource Windows11Light.ThemeFontFamily}"
                                            FontSize="12"
                                            FontWeight="{StaticResource Windows11Light.FontWeightMedium}" />
                                        <StackPanel
                                            x:Name="Thumb_StackPanel"
                                            Grid.Row="0"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Orientation="Horizontal">
                                            <Button
                                                x:Name="ZoomOut"
                                                Width="24"
                                                Height="24"
                                                Margin="8"
                                                VerticalAlignment="Center"
                                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                                <Button.ToolTip>
                                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                        Reduce page thumbnails
                                                    </ToolTip>
                                                </Button.ToolTip>
                                                <Path
                                                    Canvas.Left="0"
                                                    Canvas.Top="3.095"
                                                    Width="12"
                                                    Height="12"
                                                    Data="M11.25 2.25V9.75C11.25 9.95703 11.2109 10.1523 11.1328 10.3359C11.0547 10.5156 10.9473 10.6738 10.8105 10.8105C10.6738 10.9473 10.5137 11.0547 10.3301 11.1328C10.1504 11.2109 9.95703 11.25 9.75 11.25H2.25C2.04297 11.25 1.84766 11.2109 1.66406 11.1328C1.48438 11.0547 1.32617 10.9473 1.18945 10.8105C1.05273 10.6738 0.945312 10.5156 0.867188 10.3359C0.789062 10.1523 0.75 9.95703 0.75 9.75V2.25C0.75 2.04297 0.789062 1.84961 0.867188 1.66992C0.945312 1.48633 1.05273 1.32617 1.18945 1.18945C1.32617 1.05273 1.48438 0.945312 1.66406 0.867188C1.84766 0.789063 2.04297 0.75 2.25 0.75H9.75C9.95703 0.75 10.1504 0.789063 10.3301 0.867188C10.5137 0.945312 10.6738 1.05273 10.8105 1.18945C10.9473 1.32617 11.0547 1.48633 11.1328 1.66992C11.2109 1.84961 11.25 2.04297 11.25 2.25ZM1.5 9.75C1.5 9.82422 1.50781 9.89062 1.52344 9.94922L5.10352 6.36914C5.36133 6.11133 5.67188 5.98242 6.03516 5.98242C6.21094 5.98242 6.37891 6.01758 6.53906 6.08789C6.69922 6.1543 6.8418 6.25 6.9668 6.375L10.4766 9.9375C10.4922 9.88672 10.5 9.82422 10.5 9.75V2.25C10.5 2.14844 10.4805 2.05273 10.4414 1.96289C10.4023 1.86914 10.3477 1.78906 10.2773 1.72266C10.2109 1.65234 10.1309 1.59766 10.0371 1.55859C9.94727 1.51953 9.85156 1.5 9.75 1.5H2.25C2.14844 1.5 2.05078 1.51953 1.95703 1.55859C1.86719 1.59766 1.78711 1.65234 1.7168 1.72266C1.65039 1.78906 1.59766 1.86914 1.55859 1.96289C1.51953 2.05273 1.5 2.14844 1.5 2.25V9.75ZM6.75 4.125C6.75 3.97266 6.7793 3.82812 6.83789 3.69141C6.89648 3.55469 6.97656 3.43555 7.07812 3.33398C7.18359 3.22852 7.30469 3.14648 7.44141 3.08789C7.57812 3.0293 7.72266 3 7.875 3C8.02734 3 8.17188 3.0293 8.30859 3.08789C8.44531 3.14648 8.56445 3.22852 8.66602 3.33398C8.77148 3.43555 8.85352 3.55469 8.91211 3.69141C8.9707 3.82812 9 3.97266 9 4.125C9 4.27734 8.9707 4.42188 8.91211 4.55859C8.85352 4.69531 8.77148 4.81641 8.66602 4.92188C8.56445 5.02344 8.44531 5.10352 8.30859 5.16211C8.17188 5.2207 8.02734 5.25 7.875 5.25C7.72266 5.25 7.57812 5.2207 7.44141 5.16211C7.30469 5.10352 7.18359 5.02344 7.07812 4.92188C6.97656 4.81641 6.89648 4.69531 6.83789 4.55859C6.7793 4.42188 6.75 4.27734 6.75 4.125ZM7.5 4.125C7.5 4.22656 7.53711 4.31445 7.61133 4.38867C7.68555 4.46289 7.77344 4.5 7.875 4.5C7.97656 4.5 8.06445 4.46289 8.13867 4.38867C8.21289 4.31445 8.25 4.22656 8.25 4.125C8.25 4.02344 8.21289 3.93555 8.13867 3.86133C8.06445 3.78711 7.97656 3.75 7.875 3.75C7.77344 3.75 7.68555 3.78711 7.61133 3.86133C7.53711 3.93555 7.5 4.02344 7.5 4.125ZM9.75 10.5C9.82031 10.5 9.88477 10.4922 9.94336 10.4766L6.43359 6.90234C6.37891 6.84766 6.31641 6.80664 6.24609 6.7793C6.17969 6.74805 6.10742 6.73242 6.0293 6.73242C5.87305 6.73242 5.74023 6.78711 5.63086 6.89648L2.05078 10.4766C2.10938 10.4922 2.17578 10.5 2.25 10.5H9.75Z"
                                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                    Stretch="Fill" />
                                            </Button>
                                            <Slider
                                                x:Name="Thumb_Slider"
                                                Width="225"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                AutoToolTipPlacement="TopLeft"
                                                IsMoveToPointEnabled="True"
                                                Maximum="100"
                                                Minimum="0"
                                                Style="{StaticResource WPFSliderStyle}" >
                                            </Slider>

                                            <Button
                                                x:Name="ZoomIn"
                                                Width="24"
                                                Height="24"
                                                Margin="8"
                                                VerticalAlignment="Center"
                                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                                <Button.ToolTip>
                                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                        Enlarge page thumbnails
                                                    </ToolTip>
                                                </Button.ToolTip>
                                                <Path
                                                    Canvas.Left="0"
                                                    Canvas.Top="3.095"
                                                    Width="14"
                                                    Height="14"
                                                    Data="M14 2V12C14 12.276 13.9479 12.5365 13.8438 12.7812C13.7396 13.0208 13.5964 13.2318 13.4141 13.4141C13.2318 13.5964 13.0182 13.7396 12.7734 13.8438C12.5339 13.9479 12.276 14 12 14H2C1.72396 14 1.46354 13.9479 1.21875 13.8438C0.979167 13.7396 0.768229 13.5964 0.585938 13.4141C0.403646 13.2318 0.260417 13.0208 0.15625 12.7812C0.0520833 12.5365 0 12.276 0 12V2C0 1.72396 0.0520833 1.46615 0.15625 1.22656C0.260417 0.981771 0.403646 0.768229 0.585938 0.585938C0.768229 0.403646 0.979167 0.260417 1.21875 0.15625C1.46354 0.0520833 1.72396 0 2 0H12C12.276 0 12.5339 0.0520833 12.7734 0.15625C13.0182 0.260417 13.2318 0.403646 13.4141 0.585938C13.5964 0.768229 13.7396 0.981771 13.8438 1.22656C13.9479 1.46615 14 1.72396 14 2ZM1 12C1 12.099 1.01042 12.1875 1.03125 12.2656L5.80469 7.49219C6.14844 7.14844 6.5625 6.97656 7.04688 6.97656C7.28125 6.97656 7.50521 7.02344 7.71875 7.11719C7.93229 7.20573 8.1224 7.33333 8.28906 7.5L12.9688 12.25C12.9896 12.1823 13 12.099 13 12V2C13 1.86458 12.974 1.73698 12.9219 1.61719C12.8698 1.49219 12.7969 1.38542 12.7031 1.29688C12.6146 1.20312 12.5078 1.13021 12.3828 1.07812C12.263 1.02604 12.1354 1 12 1H2C1.86458 1 1.73438 1.02604 1.60938 1.07812C1.48958 1.13021 1.38281 1.20312 1.28906 1.29688C1.20052 1.38542 1.13021 1.49219 1.07812 1.61719C1.02604 1.73698 1 1.86458 1 2V12ZM8 4.5C8 4.29688 8.03906 4.10417 8.11719 3.92188C8.19531 3.73958 8.30208 3.58073 8.4375 3.44531C8.57812 3.30469 8.73958 3.19531 8.92188 3.11719C9.10417 3.03906 9.29688 3 9.5 3C9.70312 3 9.89583 3.03906 10.0781 3.11719C10.2604 3.19531 10.4193 3.30469 10.5547 3.44531C10.6953 3.58073 10.8047 3.73958 10.8828 3.92188C10.9609 4.10417 11 4.29688 11 4.5C11 4.70312 10.9609 4.89583 10.8828 5.07812C10.8047 5.26042 10.6953 5.42188 10.5547 5.5625C10.4193 5.69792 10.2604 5.80469 10.0781 5.88281C9.89583 5.96094 9.70312 6 9.5 6C9.29688 6 9.10417 5.96094 8.92188 5.88281C8.73958 5.80469 8.57812 5.69792 8.4375 5.5625C8.30208 5.42188 8.19531 5.26042 8.11719 5.07812C8.03906 4.89583 8 4.70312 8 4.5ZM9 4.5C9 4.63542 9.04948 4.7526 9.14844 4.85156C9.2474 4.95052 9.36458 5 9.5 5C9.63542 5 9.7526 4.95052 9.85156 4.85156C9.95052 4.7526 10 4.63542 10 4.5C10 4.36458 9.95052 4.2474 9.85156 4.14844C9.7526 4.04948 9.63542 4 9.5 4C9.36458 4 9.2474 4.04948 9.14844 4.14844C9.04948 4.2474 9 4.36458 9 4.5ZM12 13C12.0938 13 12.1797 12.9896 12.2578 12.9688L7.57812 8.20312C7.50521 8.13021 7.42188 8.07552 7.32812 8.03906C7.23958 7.9974 7.14323 7.97656 7.03906 7.97656C6.83073 7.97656 6.65365 8.04948 6.50781 8.19531L1.73438 12.9688C1.8125 12.9896 1.90104 13 2 13H12Z"
                                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                    Stretch="Fill" />
                                            </Button>

                                            <Button
                                                x:Name="AntiClockWise"
                                                Width="24"
                                                Height="24"
                                                Margin="10,0,0,0"
                                                VerticalAlignment="Center"
                                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                                <Button.ToolTip>
                                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                        Rotate counterclockwise
                                                    </ToolTip>
                                                </Button.ToolTip>
                                                <Path
                                                    Width="16"
                                                    Height="16"
                                                    Margin="2,0,0,0"
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"
                                                    Data="M0.289062 10.1328C0.0963542 9.45052 0 8.73958 0 8C0 7.86458 0.0494792 7.7474 0.148438 7.64844C0.247396 7.54948 0.364583 7.5 0.5 7.5C0.635417 7.5 0.752604 7.54948 0.851562 7.64844C0.950521 7.7474 1 7.86458 1 8C1 8.63542 1.07812 9.24219 1.23438 9.82031C1.38542 10.3984 1.6224 10.9635 1.94531 11.5156C2.26302 12.0573 2.64062 12.5443 3.07812 12.9766C3.51042 13.4089 3.98698 13.776 4.50781 14.0781C5.02344 14.375 5.57812 14.6042 6.17188 14.7656C6.76042 14.9219 7.36979 15 8 15C8.64583 15 9.26823 14.9167 9.86719 14.75C10.4609 14.5833 11.0182 14.349 11.5391 14.0469C12.0547 13.7448 12.526 13.3802 12.9531 12.9531C13.3802 12.526 13.7448 12.0547 14.0469 11.5391C14.349 11.0182 14.5833 10.4609 14.75 9.86719C14.9167 9.26823 15 8.64583 15 8C15 7.35417 14.9167 6.73438 14.75 6.14062C14.5833 5.54167 14.349 4.98438 14.0469 4.46875C13.7396 3.94792 13.375 3.47656 12.9531 3.05469C12.526 2.6276 12.0547 2.26302 11.5391 1.96094C11.0182 1.65365 10.4609 1.41667 9.86719 1.25C9.26823 1.08333 8.64583 1 8 1C7.43229 1 6.875 1.07031 6.32812 1.21094C5.78125 1.34635 5.26042 1.54427 4.76562 1.80469C4.27083 2.0599 3.8125 2.3724 3.39062 2.74219C2.96354 3.11198 2.58594 3.53125 2.25781 4H3H4H5.5C5.63542 4 5.7526 4.04948 5.85156 4.14844C5.95052 4.2474 6 4.36458 6 4.5C6 4.63542 5.95052 4.7526 5.85156 4.85156C5.7526 4.95052 5.63542 5 5.5 5H4H3H1.5C1.39062 5 1.30208 4.97917 1.23438 4.9375C1.16667 4.89062 1.11458 4.83333 1.07812 4.76562C1.04167 4.69271 1.01823 4.61458 1.00781 4.53125C0.997396 4.44271 0.992188 4.35417 0.992188 4.26562C0.992188 3.63542 0.994792 3.00781 1 2.38281V0.5C1 0.364583 1.04948 0.247396 1.14844 0.148438C1.2474 0.0494792 1.36458 0 1.5 0C1.63542 0 1.7526 0.0494792 1.85156 0.148438C1.95052 0.247396 2 0.364583 2 0.5V2.71094C2.38542 2.28385 2.80729 1.90104 3.26562 1.5625C3.71875 1.22396 4.20052 0.940104 4.71094 0.710938C5.22135 0.481771 5.7526 0.307292 6.30469 0.1875C6.85677 0.0625 7.42188 0 8 0C8.73958 0 9.45052 0.0963542 10.1328 0.289062C10.8151 0.476562 11.4531 0.744792 12.0469 1.09375C12.6354 1.4375 13.1745 1.85417 13.6641 2.34375C14.1484 2.82812 14.5651 3.36719 14.9141 3.96094C15.2578 4.54948 15.526 5.1849 15.7188 5.86719C15.9062 6.54948 16 7.26042 16 8C16 8.73958 15.9062 9.45052 15.7188 10.1328C15.526 10.8151 15.2578 11.4531 14.9141 12.0469C14.5651 12.6406 14.1484 13.1797 13.6641 13.6641C13.1797 14.1484 12.6406 14.5651 12.0469 14.9141C11.4531 15.2578 10.8151 15.526 10.1328 15.7188C9.45052 15.9062 8.73958 16 8 16C7.26042 16 6.54948 15.9062 5.86719 15.7188C5.1849 15.526 4.54948 15.2578 3.96094 14.9141C3.36719 14.5651 2.82812 14.1484 2.34375 13.6641C1.85417 13.1745 1.4375 12.6354 1.09375 12.0469C0.744792 11.4531 0.476562 10.8151 0.289062 10.1328Z"
                                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}" />
                                            </Button>

                                            <Button
                                                x:Name="ClockWise"
                                                Width="24"
                                                Height="24"
                                                Margin="8,0,0,0"
                                                VerticalAlignment="Center"
                                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                                <Button.ToolTip>
                                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                        Rotate clockwise
                                                    </ToolTip>
                                                </Button.ToolTip>
                                                <Path
                                                    Width="16"
                                                    Height="16"
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"
                                                    Data="M15.7109 10.1328C15.9036 9.45052 16 8.73958 16 8C16 7.86458 15.9505 7.7474 15.8516 7.64844C15.7526 7.54948 15.6354 7.5 15.5 7.5C15.3646 7.5 15.2474 7.54948 15.1484 7.64844C15.0495 7.7474 15 7.86458 15 8C15 8.63542 14.9219 9.24219 14.7656 9.82031C14.6146 10.3984 14.3776 10.9635 14.0547 11.5156C13.737 12.0573 13.3594 12.5443 12.9219 12.9766C12.4896 13.4089 12.013 13.776 11.4922 14.0781C10.9766 14.375 10.4219 14.6042 9.82812 14.7656C9.23958 14.9219 8.63021 15 8 15C7.35417 15 6.73177 14.9167 6.13281 14.75C5.53906 14.5833 4.98177 14.349 4.46094 14.0469C3.94531 13.7448 3.47396 13.3802 3.04688 12.9531C2.61979 12.526 2.25521 12.0547 1.95312 11.5391C1.65104 11.0182 1.41667 10.4609 1.25 9.86719C1.08333 9.26823 1 8.64583 1 8C1 7.35417 1.08333 6.73438 1.25 6.14062C1.41667 5.54167 1.65104 4.98438 1.95312 4.46875C2.26042 3.94792 2.625 3.47656 3.04688 3.05469C3.47396 2.6276 3.94531 2.26302 4.46094 1.96094C4.98177 1.65365 5.53906 1.41667 6.13281 1.25C6.73177 1.08333 7.35417 1 8 1C8.56771 1 9.125 1.07031 9.67188 1.21094C10.2188 1.34635 10.7396 1.54427 11.2344 1.80469C11.7292 2.0599 12.1875 2.3724 12.6094 2.74219C13.0365 3.11198 13.4141 3.53125 13.7422 4H13H12H10.5C10.3646 4 10.2474 4.04948 10.1484 4.14844C10.0495 4.2474 10 4.36458 10 4.5C10 4.63542 10.0495 4.7526 10.1484 4.85156C10.2474 4.95052 10.3646 5 10.5 5H12H13H14.5C14.6094 5 14.6979 4.97917 14.7656 4.9375C14.8333 4.89062 14.8854 4.83333 14.9219 4.76562C14.9583 4.69271 14.9818 4.61458 14.9922 4.53125C15.0026 4.44271 15.0078 4.35417 15.0078 4.26562C15.0078 3.63542 15.0052 3.00781 15 2.38281V0.5C15 0.364583 14.9505 0.247396 14.8516 0.148438C14.7526 0.0494792 14.6354 0 14.5 0C14.3646 0 14.2474 0.0494792 14.1484 0.148438C14.0495 0.247396 14 0.364583 14 0.5V2.71094C13.6146 2.28385 13.1927 1.90104 12.7344 1.5625C12.2812 1.22396 11.7995 0.940104 11.2891 0.710938C10.7786 0.481771 10.2474 0.307292 9.69531 0.1875C9.14323 0.0625 8.57812 0 8 0C7.26042 0 6.54948 0.0963542 5.86719 0.289062C5.1849 0.476562 4.54688 0.744792 3.95312 1.09375C3.36458 1.4375 2.82552 1.85417 2.33594 2.34375C1.85156 2.82812 1.4349 3.36719 1.08594 3.96094C0.742188 4.54948 0.473958 5.1849 0.28125 5.86719C0.09375 6.54948 0 7.26042 0 8C0 8.73958 0.09375 9.45052 0.28125 10.1328C0.473958 10.8151 0.742188 11.4531 1.08594 12.0469C1.4349 12.6406 1.85156 13.1797 2.33594 13.6641C2.82031 14.1484 3.35938 14.5651 3.95312 14.9141C4.54688 15.2578 5.1849 15.526 5.86719 15.7188C6.54948 15.9062 7.26042 16 8 16C8.73958 16 9.45052 15.9062 10.1328 15.7188C10.8151 15.526 11.4505 15.2578 12.0391 14.9141C12.6328 14.5651 13.1719 14.1484 13.6562 13.6641C14.1458 13.1745 14.5625 12.6354 14.9062 12.0469C15.2552 11.4531 15.5234 10.8151 15.7109 10.1328Z"
                                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}" />
                                            </Button>

                                            <Button
                                                x:Name="Delete"
                                                Width="24"
                                                Height="24"
                                                Margin="8,0,0,0"
                                                VerticalAlignment="Center"
                                                Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                                <Button.ToolTip>
                                                    <ToolTip Style="{StaticResource WPFToolTipStyle}">
                                                        Delete pages
                                                    </ToolTip>
                                                </Button.ToolTip>
                                                <Path
                                                    Width="16"
                                                    Height="16"
                                                    Margin="2,0,0,0"
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"
                                                    Data="M15.4297 2.77344C15.4766 2.70573 15.5 2.61458 15.5 2.5C15.5 2.36458 15.4505 2.2474 15.3516 2.14844C15.2526 2.04948 15.1354 2 15 2H10.4531C10.401 1.71354 10.2969 1.44792 10.1406 1.20312C9.98958 0.958333 9.80208 0.747396 9.57812 0.570312C9.35938 0.388021 9.11458 0.247396 8.84375 0.148438C8.57292 0.0494792 8.29167 0 8 0C7.70833 0 7.42708 0.0494792 7.15625 0.148438C6.88542 0.247396 6.63802 0.388021 6.41406 0.570312C6.19531 0.747396 6.00781 0.958333 5.85156 1.20312C5.70052 1.44792 5.59896 1.71354 5.54688 2H1C0.864583 2 0.747396 2.04948 0.648438 2.14844C0.549479 2.2474 0.5 2.36458 0.5 2.5C0.5 2.60938 0.520833 2.69792 0.5625 2.76562C0.609375 2.83333 0.666667 2.88542 0.734375 2.92188C0.807292 2.95833 0.888021 2.98177 0.976562 2.99219C1.0651 3.0026 1.15365 3.00781 1.24219 3.00781H1.39844C1.45052 3.0026 1.5026 3 1.55469 3L2.85156 14.2266C2.88281 14.4766 2.95573 14.7109 3.07031 14.9297C3.1849 15.1432 3.33073 15.3307 3.50781 15.4922C3.6849 15.6484 3.88802 15.7734 4.11719 15.8672C4.34635 15.9557 4.58594 16 4.83594 16H11.1641C11.4141 16 11.6536 15.9557 11.8828 15.8672C12.112 15.7734 12.3151 15.6484 12.4922 15.4922C12.6693 15.3307 12.8151 15.1432 12.9297 14.9297C13.0443 14.7109 13.1172 14.4766 13.1484 14.2266L14.4453 3C14.487 3 14.5339 3.0026 14.5859 3.00781H14.7422C14.8359 3.00781 14.9271 3.0026 15.0156 2.99219C15.1042 2.98177 15.1849 2.96094 15.2578 2.92969C15.3307 2.89323 15.388 2.84115 15.4297 2.77344ZM8 2H6.58594C6.63802 1.84896 6.71094 1.71354 6.80469 1.59375C6.89844 1.46875 7.00781 1.36198 7.13281 1.27344C7.25781 1.1849 7.39323 1.11719 7.53906 1.07031C7.6849 1.02344 7.83854 1 8 1C8.16146 1 8.3151 1.02344 8.46094 1.07031C8.60677 1.11719 8.74219 1.1849 8.86719 1.27344C8.99219 1.36198 9.10156 1.46875 9.19531 1.59375C9.28906 1.71354 9.36198 1.84896 9.41406 2H9H8ZM9 3H13.4375L12.1562 14.1172C12.1406 14.2422 12.1042 14.3594 12.0469 14.4688C11.9896 14.5781 11.9167 14.6719 11.8281 14.75C11.7396 14.8281 11.638 14.8906 11.5234 14.9375C11.4089 14.9792 11.2891 15 11.1641 15H4.83594C4.57552 15 4.35156 14.9193 4.16406 14.7578C3.98177 14.5911 3.875 14.3776 3.84375 14.1172L2.5625 3H8H9ZM6 6.5V11.5C6 11.6354 6.04948 11.7526 6.14844 11.8516C6.2474 11.9505 6.36458 12 6.5 12C6.63542 12 6.7526 11.9505 6.85156 11.8516C6.95052 11.7526 7 11.6354 7 11.5V6.5C7 6.36458 6.95052 6.2474 6.85156 6.14844C6.7526 6.04948 6.63542 6 6.5 6C6.36458 6 6.2474 6.04948 6.14844 6.14844C6.04948 6.2474 6 6.36458 6 6.5ZM9 6.5V11.5C9 11.6354 9.04948 11.7526 9.14844 11.8516C9.2474 11.9505 9.36458 12 9.5 12C9.63542 12 9.7526 11.9505 9.85156 11.8516C9.95052 11.7526 10 11.6354 10 11.5V6.5C10 6.36458 9.95052 6.2474 9.85156 6.14844C9.7526 6.04948 9.63542 6 9.5 6C9.36458 6 9.2474 6.04948 9.14844 6.14844C9.04948 6.2474 9 6.36458 9 6.5Z"
                                                    Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}" />
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </Border>

                                <Button
                                    Name="PART_Close"
                                    Grid.Row="0"
                                    Width="24"
                                    Height="24"
                                    Margin="4px"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center"
                                    IsEnabled="True"
                                    Style="{StaticResource SyncfusionGlyphButtonStyle}">
                                    <Path
                                        Width="11"
                                        Height="11"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Data="M6.17773 5.5625L10.2451 9.62988C10.3317 9.71647 10.375 9.81901 10.375 9.9375C10.375 10.056 10.3317 10.1585 10.2451 10.2451C10.1585 10.3317 10.056 10.375 9.9375 10.375C9.81901 10.375 9.71647 10.3317 9.62988 10.2451L5.5625 6.17773L1.49512 10.2451C1.40853 10.3317 1.30599 10.375 1.1875 10.375C1.06901 10.375 0.966471 10.3317 0.879883 10.2451C0.793294 10.1585 0.75 10.056 0.75 9.9375C0.75 9.81901 0.793294 9.71647 0.879883 9.62988L4.94727 5.5625L0.879883 1.49512C0.793294 1.40853 0.75 1.30599 0.75 1.1875C0.75 1.06901 0.793294 0.966471 0.879883 0.879883C0.966471 0.793294 1.06901 0.75 1.1875 0.75C1.30599 0.75 1.40853 0.793294 1.49512 0.879883L5.5625 4.94727L9.62988 0.879883C9.71647 0.793294 9.81901 0.75 9.9375 0.75C10.056 0.75 10.1585 0.793294 10.2451 0.879883C10.3317 0.966471 10.375 1.06901 10.375 1.1875C10.375 1.30599 10.3317 1.40853 10.2451 1.49512L6.17773 5.5625Z"
                                        Fill="{Binding Path=Foreground, RelativeSource={RelativeSource AncestorType={x:Type Button}}}" />
                                </Button>

                                <Grid Grid.Row="1" Margin="0,2,0,0">
                                    <Canvas x:Name="PART_Canvas" />
                                    <ScrollViewer
                                        x:Name="Thumb_ScrollViewer"
                                        HorizontalScrollBarVisibility="Auto"
                                        VerticalScrollBarVisibility="Auto">
                                        <Border
                                            Background="Transparent"
                                            BorderBrush="{StaticResource BorderAlt}">
                                            <Grid Name="Thumb_Grid" />
                                        </Border>
                                    </ScrollViewer>
                                </Grid>
                                <Border
                                    Grid.Row="2"
                                    Background="Transparent"
                                    BorderBrush="{StaticResource BorderAlt}"
                                    BorderThickness="0,1,0,0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="0.01*" />
                                            <ColumnDefinition Width="0.495*" />
                                            <ColumnDefinition Width="0.495*" />
                                        </Grid.ColumnDefinitions>
                                        <CheckBox
                                            x:Name="Part_MultiPageSelectionCheckBox"
                                            Grid.Column="1"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Center"
                                            Content="Enable multiple page selection"
                                            FontSize="13" />
                                        <StackPanel
                                            x:Name="Thumb_ReorderingPanel"
                                            Grid.Column="2"
                                            HorizontalAlignment="Right"
                                            VerticalAlignment="Center"
                                            Orientation="Horizontal" />
                                    </Grid>
                                </Border>
                            </Grid>
                        </DockPanel>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionLayerPaneStyle" TargetType="{x:Type pdfviewer:LayerPane}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:LayerPane}">
                    <Border BorderBrush="{StaticResource BorderAlt}" BorderThickness="0,0,1,0">
                        <DockPanel LastChildFill="True">
                            <Grid>
                                <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
                                    <Grid x:Name="Part_LayerGrid" />
                                </ScrollViewer>
                            </Grid>
                        </DockPanel>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionPdfDocumentViewStyle" TargetType="{x:Type pdfviewer:PdfDocumentView}">
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type pdfviewer:PdfDocumentView}">
                    <DockPanel x:Name="PART_PanelMain" LastChildFill="True" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionBookmarkLabelStyle}" TargetType="{x:Type pdfviewer:BookmarkLabel}" />
    <Style BasedOn="{StaticResource SyncfusionBookmarkPaneStyle}" TargetType="{x:Type pdfviewer:BookmarkPane}" />
    <Style BasedOn="{StaticResource SyncfusionCommentsLabelStyle}" TargetType="{x:Type pdfviewer:CommentsLabel}" />
    <Style BasedOn="{StaticResource SyncfusionCommentsPanleStyle}" TargetType="{x:Type pdfviewer:CommentsPane}" />
    <Style BasedOn="{StaticResource SyncfusionCopyProgressIndicatorStyle}" TargetType="{x:Type pdfviewer:CopyProgressIndicator}" />
    <Style BasedOn="{StaticResource SyncfusionDocumentToolbarStyle}" TargetType="{x:Type pdfviewer:DocumentToolbar}" />
    <Style BasedOn="{StaticResource SyncfusionDocumentViewStyle}" TargetType="{x:Type pdfviewer:DocumentView}" />
    <Style BasedOn="{StaticResource SyncfusionFontPropertiesDialogStyle}" TargetType="{x:Type pdfviewer:FontPropertiesDialog}" />
    <Style BasedOn="{StaticResource SyncfusionLayerPaneStyle}" TargetType="{x:Type pdfviewer:LayerPane}" />
    <Style BasedOn="{StaticResource SyncfusionNotificationBarStyle}" TargetType="{x:Type pdfviewer:NotificationBar}" />
    <Style BasedOn="{StaticResource SyncfusionOutlinePaneStyle}" TargetType="{x:Type pdfviewer:OutlinePane}" />
    <Style BasedOn="{StaticResource SyncfusionPageOrganizerPaneStyle}" TargetType="{x:Type pdfviewer:PageOrganizerPane}" />
    <Style BasedOn="{StaticResource SyncfusionPdfDocumentViewStyle}" TargetType="{x:Type pdfviewer:PdfDocumentView}" />
    <Style BasedOn="{StaticResource SyncfusionPdfLoadingIndicatorStyle}" TargetType="{x:Type pdfviewer:PdfLoadingIndicator}" />
    <Style BasedOn="{StaticResource SyncfusionPdfProgressIndicatorStyle}" TargetType="{x:Type pdfviewer:PdfProgressIndicator}" />
    <Style BasedOn="{StaticResource SyncfusionPdfViewerControlStyle}" TargetType="{x:Type pdfviewer:PdfViewerControl}" />
    <Style BasedOn="{StaticResource SyncfusionRedactionToolbarStyle}" TargetType="{x:Type pdfviewer:RedactionToolbar}" />
    <Style BasedOn="{StaticResource SyncfusionTextSearchBarStyle}" TargetType="{x:Type pdfviewer:TextSearchBar}" />
    <Style BasedOn="{StaticResource SyncfusionTextSearchProgressIndicatorStyle}" TargetType="{x:Type pdfviewer:TextSearchProgressIndicator}" />
    <Style BasedOn="{StaticResource SyncfusionThumbnailPaneStyle}" TargetType="{x:Type pdfviewer:ThumbnailPane}" />
</ResourceDictionary>
