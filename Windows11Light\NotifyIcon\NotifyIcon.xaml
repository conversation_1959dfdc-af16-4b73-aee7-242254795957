<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF.Classic"
    xmlns:converter="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Tools.WPF"
    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ToolTip.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GlyphButton.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="NotifyIcon..CloseButton.Pressed.Border" Color="#c8c8c8" />
    
    <DropShadowEffect x:Key="NotifyIcon.Shadow.Effect" BlurRadius="16" ShadowDepth="8" Direction="270" Color="{StaticResource DropShadow.Static.Background}" Opacity=".62" RenderingBias="Performance" />

    <local:BalloonTipHeaderTemplateSelector x:Key="SyncfusionBalloonTipHeaderTemplateSelector" />
    <local:BalloonTipContentTemplateSelector x:Key="SyncfusionBalloonTipContentTemplateSelector" />
    <converter:BrushToColorConverter x:Key="BrushToColorConverter" />
    <converter:HeaderVisibilityToVisibilityConverter x:Key="HeaderVisibilityToVisibilityConverter" />

    <ControlTemplate x:Key="SyncfusionNotifyIconToolTipTemplate" TargetType="{x:Type ToolTip}">
        <Border Background="{StaticResource TooltipBackground}"
                BorderThickness="{StaticResource Windows11Light.BorderThickness}"
                BorderBrush="{StaticResource TooltipBorder}"
                Padding="5,3,5,3"
                SnapsToDevicePixels="True"
                CornerRadius="8"
                Effect="{StaticResource Default.ShadowDepth4}">
            <ContentPresenter ContentTemplate="{Binding Path=ToolTipTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:NotifyIcon}}}"
                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                <ContentPresenter.Resources>
                    <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                </ContentPresenter.Resources>
            </ContentPresenter>
        </Border>
    </ControlTemplate>

    <DataTemplate x:Key="SyncfusionNotifyIconToolTipContentTemplate">
        <TextBlock
                Name="PART_Text"
                Text="{Binding Path=Text, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:NotifyIcon}}}"
                TextWrapping="Wrap" />
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=Text, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:NotifyIcon}}}" Value="{x:Null}">
                <Setter TargetName="PART_Text" Property="Text" Value="{Binding Path=Text, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:NotifyIcon}}}" />
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <DataTemplate x:Key="BalloonTipContentIconTemplate">
        <Grid 
            Width="20"
            Height="20"
            Margin="0,0,5,0">
            <Path
                    x:Name="Cancel"
                    Width="16"
                    Height="16"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Fill="{StaticResource ErrorForeground}"
                    Stretch="Fill"
                    Visibility="Collapsed">
                <Path.Data>
                    <PathGeometry>M0.265625 5.50781C0.0885417 6.14323 0 6.80729 0 7.5C0 8.19271 0.0885417 8.85938 0.265625 9.5C0.442708 10.1406 0.692708 10.7396 1.01562 11.2969C1.34375 11.849 1.73438 12.3542 2.1875 12.8125C2.64583 13.2656 3.15104 13.6562 3.70312 13.9844C4.26042 14.3073 4.85938 14.5573 5.5 14.7344C6.14062 14.9115 6.80729 15 7.5 15C8.1875 15 8.85156 14.9115 9.49219 14.7344C10.1328 14.5521 10.7292 14.2995 11.2812 13.9766C11.8385 13.6484 12.3438 13.2578 12.7969 12.8047C13.2552 12.3464 13.6458 11.8411 13.9688 11.2891C14.2969 10.7318 14.5495 10.1354 14.7266 9.5C14.9089 8.85938 15 8.19271 15 7.5C15 6.80729 14.9115 6.14323 14.7344 5.50781C14.5573 4.86719 14.3047 4.26823 13.9766 3.71094C13.6536 3.15365 13.263 2.64844 12.8047 2.19531C12.3516 1.73698 11.8464 1.34635 11.2891 1.02344C10.7318 0.695312 10.1328 0.442708 9.49219 0.265625C8.85677 0.0885417 8.19271 0 7.5 0C6.80729 0 6.14062 0.0885417 5.5 0.265625C4.86458 0.442708 4.26823 0.695312 3.71094 1.02344C3.15365 1.34635 2.64583 1.73698 2.1875 2.19531C1.73438 2.64844 1.34375 3.15365 1.01562 3.71094C0.692708 4.26823 0.442708 4.86719 0.265625 5.50781ZM13.7656 5.77344C13.9219 6.32552 14 6.90104 14 7.5C14 8.09896 13.9219 8.67708 13.7656 9.23438C13.6146 9.78646 13.3984 10.3047 13.1172 10.7891C12.8359 11.2682 12.4974 11.7057 12.1016 12.1016C11.7057 12.4974 11.2656 12.8359 10.7812 13.1172C10.3021 13.3984 9.78385 13.6172 9.22656 13.7734C8.67448 13.9245 8.09896 14 7.5 14C6.90104 14 6.32292 13.9245 5.76562 13.7734C5.21354 13.6172 4.69531 13.3984 4.21094 13.1172C3.73177 12.8359 3.29427 12.4974 2.89844 12.1016C2.5026 11.7057 2.16406 11.2682 1.88281 10.7891C1.60156 10.3047 1.38281 9.78646 1.22656 9.23438C1.07552 8.67708 1 8.09896 1 7.5C1 6.90104 1.07552 6.32552 1.22656 5.77344C1.38281 5.21615 1.60156 4.69792 1.88281 4.21875C2.16927 3.73438 2.50781 3.29688 2.89844 2.90625C3.29427 2.51042 3.73177 2.17188 4.21094 1.89062C4.69531 1.60417 5.21354 1.38542 5.76562 1.23438C6.32292 1.07812 6.90104 1 7.5 1C8.09896 1 8.67448 1.07812 9.22656 1.23438C9.78385 1.38542 10.3021 1.60417 10.7812 1.89062C11.2656 2.17188 11.7031 2.51042 12.0938 2.90625C12.4896 3.29688 12.8281 3.73438 13.1094 4.21875C13.3958 4.69792 13.6146 5.21615 13.7656 5.77344ZM4.85355 4.14645C4.65829 3.95118 4.34171 3.95118 4.14645 4.14645C3.95118 4.34171 3.95118 4.65829 4.14645 4.85355L6.79289 7.5L4.14645 10.1464C3.95118 10.3417 3.95118 10.6583 4.14645 10.8536C4.34171 11.0488 4.65829 11.0488 4.85355 10.8536L7.5 8.20711L10.1464 10.8536C10.3417 11.0488 10.6583 11.0488 10.8536 10.8536C11.0488 10.6583 11.0488 10.3417 10.8536 10.1464L8.20711 7.5L10.8536 4.85355C11.0488 4.65829 11.0488 4.34171 10.8536 4.14645C10.6583 3.95118 10.3417 3.95118 10.1464 4.14645L7.5 6.79289L4.85355 4.14645Z</PathGeometry>
                </Path.Data>
            </Path>
            <Path
                    x:Name="Warning"
                    Width="16"
                    Height="14"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Fill="{StaticResource WarningForeground}"
                    Stretch="Fill"
                    Visibility="Collapsed" >
                <Path.Data>
                    <PathGeometry>M1.5 15.0156C1.29688 15.0156 1.10417 14.9766 0.921875 14.8984C0.744792 14.8203 0.585938 14.7135 0.445312 14.5781C0.309896 14.4427 0.200521 14.2865 0.117188 14.1094C0.0390625 13.9271 0 13.7344 0 13.5312C0 13.2917 0.0520833 13.0625 0.15625 12.8438L6.03906 1.07812C6.16406 0.828125 6.34896 0.627604 6.59375 0.476562C6.83854 0.325521 7.10156 0.25 7.38281 0.25C7.66406 0.25 7.92708 0.325521 8.17188 0.476562C8.41667 0.627604 8.60156 0.828125 8.72656 1.07812L14.6094 12.8438C14.7135 13.0625 14.7656 13.2917 14.7656 13.5312C14.7656 13.7344 14.724 13.9271 14.6406 14.1094C14.5625 14.2865 14.4557 14.4427 14.3203 14.5781C14.1849 14.7135 14.026 14.8203 13.8438 14.8984C13.6615 14.9766 13.4688 15.0156 13.2656 15.0156H1.5ZM13.2656 14.0156C13.3906 14.0156 13.5052 13.9688 13.6094 13.875C13.7135 13.7812 13.7656 13.6693 13.7656 13.5391C13.7656 13.4453 13.7474 13.362 13.7109 13.2891L7.82812 1.52344C7.78646 1.4349 7.72396 1.36719 7.64062 1.32031C7.5625 1.27344 7.47656 1.25 7.38281 1.25C7.28906 1.25 7.20052 1.27604 7.11719 1.32812C7.03906 1.375 6.97917 1.4401 6.9375 1.52344L1.05469 13.2891C1.01823 13.362 1 13.4427 1 13.5312C1 13.6667 1.04948 13.7812 1.14844 13.875C1.2526 13.9688 1.36979 14.0156 1.5 14.0156H13.2656ZM6.88281 9.5V5.5C6.88281 5.36458 6.93229 5.2474 7.03125 5.14844C7.13021 5.04948 7.2474 5 7.38281 5C7.51823 5 7.63542 5.04948 7.73438 5.14844C7.83333 5.2474 7.88281 5.36458 7.88281 5.5V9.5C7.88281 9.63542 7.83333 9.7526 7.73438 9.85156C7.63542 9.95052 7.51823 10 7.38281 10C7.2474 10 7.13021 9.95052 7.03125 9.85156C6.93229 9.7526 6.88281 9.63542 6.88281 9.5ZM6.63281 11.5C6.63281 11.2917 6.70573 11.1146 6.85156 10.9688C6.9974 10.8229 7.17448 10.75 7.38281 10.75C7.59115 10.75 7.76823 10.8229 7.91406 10.9688C8.0599 11.1146 8.13281 11.2917 8.13281 11.5C8.13281 11.7083 8.0599 11.8854 7.91406 12.0312C7.76823 12.1771 7.59115 12.25 7.38281 12.25C7.17448 12.25 6.9974 12.1771 6.85156 12.0312C6.70573 11.8854 6.63281 11.7083 6.63281 11.5Z</PathGeometry>
                </Path.Data>
            </Path>
            <Path
                    x:Name="Info_Line"
                    Width="16"
                    Height="16"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Fill="{StaticResource ContentForeground}"
                    Stretch="Fill"
                    Visibility="Collapsed" >
                <Path.Data>
                    <PathGeometry>M0 7.5C0 6.80729 0.0885417 6.14323 0.265625 5.50781C0.442708 4.86719 0.692708 4.26823 1.01562 3.71094C1.34375 3.15365 1.73438 2.64844 2.1875 2.19531C2.64583 1.73698 3.15365 1.34635 3.71094 1.02344C4.26823 0.695312 4.86458 0.442708 5.5 0.265625C6.14062 0.0885417 6.80729 0 7.5 0C8.19271 0 8.85677 0.0885417 9.49219 0.265625C10.1328 0.442708 10.7318 0.695312 11.2891 1.02344C11.8464 1.34635 12.3516 1.73698 12.8047 2.19531C13.263 2.64844 13.6536 3.15365 13.9766 3.71094C14.3047 4.26823 14.5573 4.86719 14.7344 5.50781C14.9115 6.14323 15 6.80729 15 7.5C15 8.19271 14.9089 8.85938 14.7266 9.5C14.5495 10.1354 14.2969 10.7318 13.9688 11.2891C13.6458 11.8411 13.2552 12.3464 12.7969 12.8047C12.3438 13.2578 11.8385 13.6484 11.2812 13.9766C10.7292 14.2995 10.1328 14.5521 9.49219 14.7344C8.85156 14.9115 8.1875 15 7.5 15C6.80729 15 6.14062 14.9115 5.5 14.7344C4.85938 14.5573 4.26042 14.3073 3.70312 13.9844C3.15104 13.6562 2.64583 13.2656 2.1875 12.8125C1.73438 12.3542 1.34375 11.849 1.01562 11.2969C0.692708 10.7396 0.442708 10.1406 0.265625 9.5C0.0885417 8.85938 0 8.19271 0 7.5ZM14 7.5C14 6.90104 13.9219 6.32552 13.7656 5.77344C13.6146 5.21615 13.3958 4.69792 13.1094 4.21875C12.8281 3.73438 12.4896 3.29688 12.0938 2.90625C11.7031 2.51042 11.2656 2.17188 10.7812 1.89062C10.3021 1.60417 9.78385 1.38542 9.22656 1.23438C8.67448 1.07812 8.09896 1 7.5 1C6.90104 1 6.32292 1.07812 5.76562 1.23438C5.21354 1.38542 4.69531 1.60417 4.21094 1.89062C3.73177 2.17188 3.29427 2.51042 2.89844 2.90625C2.50781 3.29688 2.16927 3.73438 1.88281 4.21875C1.60156 4.69792 1.38281 5.21615 1.22656 5.77344C1.07552 6.32552 1 6.90104 1 7.5C1 8.09896 1.07552 8.67708 1.22656 9.23438C1.38281 9.78646 1.60156 10.3047 1.88281 10.7891C2.16406 11.2682 2.5026 11.7057 2.89844 12.1016C3.29427 12.4974 3.73177 12.8359 4.21094 13.1172C4.69531 13.3984 5.21354 13.6172 5.76562 13.7734C6.32292 13.9245 6.90104 14 7.5 14C8.09896 14 8.67448 13.9245 9.22656 13.7734C9.78385 13.6172 10.3021 13.3984 10.7812 13.1172C11.2656 12.8359 11.7057 12.4974 12.1016 12.1016C12.4974 11.7057 12.8359 11.2682 13.1172 10.7891C13.3984 10.3047 13.6146 9.78646 13.7656 9.23438C13.9219 8.67708 14 8.09896 14 7.5ZM6.75 4.5C6.75 4.29167 6.82292 4.11458 6.96875 3.96875C7.11458 3.82292 7.29167 3.75 7.5 3.75C7.70833 3.75 7.88542 3.82292 8.03125 3.96875C8.17708 4.11458 8.25 4.29167 8.25 4.5C8.25 4.70833 8.17708 4.88542 8.03125 5.03125C7.88542 5.17708 7.70833 5.25 7.5 5.25C7.29167 5.25 7.11458 5.17708 6.96875 5.03125C6.82292 4.88542 6.75 4.70833 6.75 4.5ZM7 10.5V6.5C7 6.36458 7.04948 6.2474 7.14844 6.14844C7.2474 6.04948 7.36458 6 7.5 6C7.63542 6 7.7526 6.04948 7.85156 6.14844C7.95052 6.2474 8 6.36458 8 6.5V10.5C8 10.6354 7.95052 10.7526 7.85156 10.8516C7.7526 10.9505 7.63542 11 7.5 11C7.36458 11 7.2474 10.9505 7.14844 10.8516C7.04948 10.7526 7 10.6354 7 10.5Z</PathGeometry>
                </Path.Data>
            </Path>
            <Image
                    Name="PART_ContentImage"
                    Source="{x:Null}"
                    Visibility="Collapsed" />
        </Grid>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding Path=BalloonTipIcon, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:BalloonTip}}}" Value="Error">
                <Setter TargetName="Cancel" Property="Visibility" Value="Visible" />
                <Setter TargetName="Warning" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="Info_Line" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContentImage" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=BalloonTipIcon, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:BalloonTip}}}" Value="Warning">
                <Setter TargetName="Cancel" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="Warning" Property="Visibility" Value="Visible" />
                <Setter TargetName="Info_Line" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContentImage" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=BalloonTipIcon, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:BalloonTip}}}" Value="Info">
                <Setter TargetName="Cancel" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="Warning" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="Info_Line" Property="Visibility" Value="Visible" />
                <Setter TargetName="PART_ContentImage" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Path=BalloonTipIcon, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:BalloonTip}}}" Value="Custom">
                <Setter TargetName="Cancel" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="Warning" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="Info_Line" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="PART_ContentImage" Property="Visibility" Value="Visible" />
                <Setter TargetName="PART_ContentImage" Property="Source" Value="{Binding Path=Icon, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:NotifyIcon}}}" />
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <Style x:Key="SyncfusionBallonTipHeaderCloseButtonStyle"
        BasedOn="{StaticResource WPFGlyphButtonStyle}"
        TargetType="{x:Type Button}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        x:Name="PART_Border"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Background="{TemplateBinding Background}"
                        Effect="{TemplateBinding Effect}"
                        CornerRadius="1">
                        <Path Name="BallonTipHeaderCloseButtonPath"
                    Width="10"
                    Height="10"
                    Margin="3"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Stroke="{StaticResource ContentForeground}"
                    StrokeThickness="1" >
                            <Path.Data>
                                <PathGeometry>M5.6392 5L9.86506 9.22585C9.95502 9.31581 10 9.42235 10 9.54545C10 9.66856 9.95502 9.77509 9.86506 9.86506C9.77509 9.95502 9.66856 10 9.54545 10C9.42235 10 9.31581 9.95502 9.22585 9.86506L5 5.6392L0.774148 9.86506C0.684186 9.95502 0.577652 10 0.454545 10C0.331439 10 0.224905 9.95502 0.134943 9.86506C0.0449811 9.77509 0 9.66856 0 9.54545C0 9.42235 0.0449811 9.31581 0.134943 9.22585L4.3608 5L0.134943 0.774148C0.0449811 0.684186 0 0.577652 0 0.454545C0 0.331439 0.0449811 0.224905 0.134943 0.134943C0.224905 0.0449811 0.331439 0 0.454545 0C0.577652 0 0.684186 0.0449811 0.774148 0.134943L5 4.3608L9.22585 0.134943C9.31581 0.0449811 9.42235 0 9.54545 0C9.66856 0 9.77509 0.0449811 9.86506 0.134943C9.95502 0.224905 10 0.331439 10 0.454545C10 0.577652 9.95502 0.684186 9.86506 0.774148L5.6392 5Z</PathGeometry>
                            </Path.Data>
                        </Path>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver"  Value="True">
                            <Setter TargetName="BallonTipHeaderCloseButtonPath" Property="Stroke" Value="{StaticResource IconColorHovered}"/>
                            <Setter TargetName="PART_Border" Property="Background"  Value="{StaticResource ContentBackgroundHovered}"/>
                            <Setter TargetName="PART_Border" Property="BorderBrush"  Value="{StaticResource ContentBackgroundHovered}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="BallonTipHeaderCloseButtonPath" Property="Stroke" Value="{StaticResource IconColorSelected}"/>
                            <Setter TargetName="PART_Border" Property="Background" Value="{StaticResource ContentBackgroundPressed}"/>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource ContentBackgroundPressed}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="BallonTipHeaderCloseButtonPath" Property="Stroke" Value="{StaticResource IconColorDisabled}"/>
                            <Setter TargetName="PART_Border" Property="Background" Value="Transparent"/>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="Transparent"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SyncfusionBalloonTipHeaderStyle" TargetType="{x:Type local:BalloonTipHeader}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:BalloonTipHeader}">
                    <Border
                        Name="PART_Border"
                        Padding="12,7,0,7"
                        CornerRadius="8"
                        Background="{TemplateBinding Background}"
                        TextBlock.Foreground="{TemplateBinding Foreground}">
                        <DockPanel Height="18" LastChildFill="True">
                            <Button
                                Name="PART_CloseButton"
                                VerticalAlignment="Top"
                                DockPanel.Dock="Right"
                                Width="25"
                                Height="25"
                                Style="{StaticResource SyncfusionBallonTipHeaderCloseButtonStyle}">
                                <Button.Margin>
                                    <Thickness>0,-7,0,0</Thickness>
                                </Button.Margin>
                            </Button>
                            <Border
                                Name="PART_HeaderIconBorder"
                                Margin="1,1,3,1"
                                DockPanel.Dock="Left">
                                <Image Width="16" Source="{TemplateBinding HeaderImage}" />
                            </Border>
                            <ContentPresenter
                                VerticalAlignment="Center"
                                Content="{Binding Path=Header, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:BalloonTip}}}"
                                ContentTemplate="{Binding Path=HeaderTemplate, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:BalloonTip}}}"
                                ContentTemplateSelector="{Binding Path=HeaderTemplateSelector, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:BalloonTip}}}"
                                TextBlock.FontWeight="{StaticResource Windows11Light.FontWeightMedium}"
                                TextBlock.FontSize="{StaticResource Windows11Light.BodyTextStyle}">
                                <ContentPresenter.Resources>
                                    <Style BasedOn="{x:Null}" TargetType="TextBlock"/>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </DockPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="PART_Border" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                            <Setter TargetName="PART_CloseButton" Property="MinHeight" Value="12" />
                        </Trigger>
                        <DataTrigger Binding="{Binding Path=HeaderImage, RelativeSource={RelativeSource Self}}" Value="{x:Null}">
                            <Setter TargetName="PART_HeaderIconBorder" Property="Visibility" Value="Collapsed" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Path=BalloonTipShape, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:BalloonTip}}}" Value="RoundedRectangle">
                            <Setter TargetName="PART_CloseButton" Property="Height" Value="16" />
                            <Setter TargetName="PART_CloseButton" Property="Width" Value="16" />
                            <Setter TargetName="PART_CloseButton" Property="Margin" Value="0,0,5,0" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionBalloonTipHeaderStyle}" TargetType="{x:Type local:BalloonTipHeader}" />

    <Style x:Key="SyncfusionBalloonTipStyle" TargetType="{x:Type local:BalloonTip}">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="HeaderBackground" Value="{StaticResource PopupBackground}" />
        <Setter Property="HeaderForeground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}" />
        <Setter Property="IconTemplate" Value="{StaticResource BalloonTipContentIconTemplate}" />
        <Setter Property="HeaderTemplateSelector" Value="{StaticResource SyncfusionBalloonTipHeaderTemplateSelector}" />
        <Setter Property="ContentTemplateSelector" Value="{StaticResource SyncfusionBalloonTipContentTemplateSelector}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:BalloonTip}">
                    <local:BalloonTipBorder
                        x:Name="PART_Border"
                        Margin="8"
                        Background="{Binding Path=Background, RelativeSource={RelativeSource TemplatedParent}}"
                        BorderBrush="{Binding Path=BorderBrush, RelativeSource={RelativeSource TemplatedParent}}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="8"
                        Effect="{StaticResource Default.ShadowDepth4}"
                        Opacity="{TemplateBinding Opacity}">
                        <DockPanel
                            Name="PART_DockPanel"
                            Margin="-8"
                            LastChildFill="True">
                            <local:BalloonTipHeader
                                x:Name="PART_Header"
                                Background="{Binding Path=HeaderBackground, RelativeSource={RelativeSource TemplatedParent}}"
                                DockPanel.Dock="Top"
                                Foreground="{Binding Path=HeaderForeground, RelativeSource={RelativeSource TemplatedParent}}"
                                HeaderImage="{TemplateBinding HeaderImage}"
                                Visibility="{TemplateBinding BalloonTipHeaderVisibility,
                                                             Converter={StaticResource HeaderVisibilityToVisibilityConverter}}" />
                            <Border
                                Name="PART_ContentBorder"
                                Padding="12,0,12,0"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch"
                                CornerRadius="8"
                                Background="{Binding Path=Background, RelativeSource={RelativeSource TemplatedParent}}"
                                TextBlock.Foreground="{Binding Path=Foreground, RelativeSource={RelativeSource TemplatedParent}}">
                                <Border.Margin>
                                    <Thickness>0,0,0,8</Thickness>
                                </Border.Margin>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>

                                    <ContentPresenter
                                        Name="PART_IconColumn"
                                        Grid.Column="0"
										VerticalAlignment="Top"
                                        ContentTemplate="{TemplateBinding IconTemplate}" />
                                    <ContentPresenter
                                        Grid.Column="1"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Content="{TemplateBinding Content}"
                                        ContentTemplate="{TemplateBinding ContentTemplate}"
                                        ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}" >
                                        <ContentPresenter.Resources>
                                            <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}"/>
                                        </ContentPresenter.Resources>
                                    </ContentPresenter>
                                    <Border
                                        Name="PART_DownBorder"
                                        Grid.Row="1"
                                        Grid.ColumnSpan="2"
                                        Height="5"
                                        Background="{Binding Path=Background, RelativeSource={RelativeSource TemplatedParent}}"
                                        Visibility="{TemplateBinding BalloonTipHeaderVisibility,
                                                                     Converter={StaticResource HeaderVisibilityToVisibilityConverter}}" />
                                </Grid>
                            </Border>
                        </DockPanel>
                    </local:BalloonTipBorder>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter TargetName="PART_ContentBorder" Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger Property="BalloonTipHeaderVisibility" Value="Visible">
                            <Setter Property="Background" Value="{StaticResource PopupBackground}" />
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>
                        <Trigger Property="BalloonTipIcon" Value="None">
                            <Setter TargetName="PART_IconColumn" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="BalloonTipShape" Value="Balloon">
                            <Setter TargetName="PART_DownBorder" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="BalloonTipShape" Value="RoundedRectangle">
                            <Setter TargetName="PART_ContentBorder" Property="CornerRadius" Value="12" />
                            <Setter TargetName="PART_Header" Property="Border.CornerRadius" Value="12, 12, 0, 0" />
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Path=BalloonTipShape, RelativeSource={RelativeSource TemplatedParent}}" Value="RoundedRectangle" />
                                <Condition Binding="{Binding Path=BalloonTipHeaderVisibility, RelativeSource={RelativeSource TemplatedParent}}" Value="Visible" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_DownBorder" Property="CornerRadius" Value="0, 0, 12, 12" />
                            <Setter TargetName="PART_ContentBorder" Property="CornerRadius" Value="0, 0, 12, 12" />
                        </MultiDataTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="BalloonTipShape" Value="Balloon" />
                                <Condition Property="TaskBarDirection" Value="Left" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Border" Property="LayoutTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="-1" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_DockPanel" Property="LayoutTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="-1" />
                                </Setter.Value>
                            </Setter>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="BalloonTipShape" Value="Balloon" />
                                <Condition Property="TaskBarDirection" Value="Top" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Border" Property="LayoutTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleY="-1" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_DockPanel" Property="LayoutTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleY="-1" />
                                </Setter.Value>
                            </Setter>
                        </MultiTrigger>
                        <Trigger Property="BalloonTipShape" Value="Balloon">
                            <Setter Property="Border.CornerRadius" Value="0"  />
                        </Trigger>
                        <Trigger Property="BalloonTipShape" Value="Rectangle">
                            <Setter Property="Border.CornerRadius" Value="0" />
                        </Trigger>
                        <Trigger Property="BalloonTipShape" Value="RoundedRectangle">
                            <Setter Property="Border.CornerRadius" Value="12" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionBalloonTipStyle}" TargetType="{x:Type local:BalloonTip}" />

    <Style x:Key="SyncfusionNotifyIconStyle" TargetType="{x:Type local:NotifyIcon}">
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="Background" Value="{StaticResource PopupBackground}" />
        <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
        <Setter Property="HeaderBackground" Value="{StaticResource PopupBackground}" />
        <Setter Property="HeaderForeground" Value="{StaticResource ContentForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}" />
        <Setter Property="IconTemplate" Value="{StaticResource BalloonTipContentIconTemplate}" />
        <Setter Property="ToolTipTemplate" Value="{StaticResource SyncfusionNotifyIconToolTipContentTemplate}" />
        <Setter Property="HeaderTemplateSelector" Value="{StaticResource SyncfusionBalloonTipHeaderTemplateSelector}" />
        <Setter Property="ContentTemplateSelector" Value="{StaticResource SyncfusionBalloonTipContentTemplateSelector}" />
        <Setter Property="BalloonTipShape" Value="Rectangle"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:NotifyIcon}">
                    <Border Name="PART_Border">
                        <Border.ToolTip>
                            <ToolTip
                                Name="tooltip"
                                IsOpen="{Binding Path=IsToolTipOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                Placement="Absolute"
                                Template="{StaticResource SyncfusionNotifyIconToolTipTemplate}" />
                        </Border.ToolTip>
                        <shared:NonStickingPopup
                            Name="PART_BalloonTipPopup"
                            AllowsTransparency="True"
                            IsOpen="{Binding ElementName=PART_BalloonTip, Path=IsOpen}"
                            Placement="Absolute"
                            StaysOpen="True">
                            <local:BalloonTip
                                x:Name="PART_BalloonTip"
                                Width="{TemplateBinding Width}"
                                Height="{TemplateBinding Height}"
                                Background="{TemplateBinding Background}"
                                BalloonTipAnimationEffect="{TemplateBinding BalloonTipAnimationEffect}"
                                BalloonTipHeaderVisibility="{TemplateBinding BalloonTipHeaderVisibility}"
                                BalloonTipIcon="{TemplateBinding BalloonTipIcon}"
                                BalloonTipLocation="{TemplateBinding BalloonTipLocation}"
                                BalloonTipShape="{TemplateBinding BalloonTipShape}"
                                BalloonTipSize="{TemplateBinding BalloonTipSize}"
                                BalloonTipText="{TemplateBinding BalloonTipText}"
                                BalloonTipTitle="{TemplateBinding BalloonTipTitle}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Content="{TemplateBinding Content}"
                                ContentTemplate="{TemplateBinding ContentTemplate}"
                                ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}"
                                CustomAnimatedProperty="{TemplateBinding CustomAnimatedProperty}"
                                CustomHidingAnimation="{TemplateBinding CustomHidingAnimation}"
                                CustomShowingAnimation="{TemplateBinding CustomShowingAnimation}"
                                Foreground="{TemplateBinding Foreground}"
                                Header="{TemplateBinding Header}"
                                HeaderBackground="{TemplateBinding HeaderBackground}"
                                HeaderForeground="{TemplateBinding HeaderForeground}"
                                HeaderImage="{TemplateBinding HeaderImage}"
                                HeaderTemplate="{TemplateBinding HeaderTemplate}"
                                HeaderTemplateSelector="{TemplateBinding HeaderTemplateSelector}"
                                HideBalloonTipTime="{TemplateBinding HideBalloonTipTime}"
                                IconTemplate="{TemplateBinding IconTemplate}"
                                ShowBalloonTipTime="{TemplateBinding ShowBalloonTipTime}" />
                        </shared:NonStickingPopup>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <Trigger Property="BalloonTipHeaderVisibility" Value="Visible">
                            <Setter Property="Background" Value="{StaticResource PopupBackground}" />
                            <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
                            <Setter Property="Foreground" Value="{StaticResource ContentForeground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionNotifyIconStyle}" TargetType="{x:Type local:NotifyIcon}" />

</ResourceDictionary>
