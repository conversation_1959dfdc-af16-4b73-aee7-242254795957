<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
					
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:local="clr-namespace:Syncfusion.UI.Xaml.Charts;assembly=Syncfusion.SfChart.WPF">
    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>
    
    <Style x:Key="SyncfusionZoomingToolBarStyle" TargetType="local:ZoomingToolBar" >
        <Setter Property="Background" Value="{StaticResource ContentBackgroundAlt1}"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}" />
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <StackPanel  Orientation="Horizontal" />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template" >
            <Setter.Value>
                <ControlTemplate TargetType="local:ZoomingToolBar">
                    <Border  Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}"
                             CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
							 Effect="{StaticResource Default.ShadowDepth2}">
                        <ItemsPresenter  />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionZoomInStyle" TargetType="local:ZoomIn">
        <Setter  Property="IconBackground" Value="{StaticResource IconColor.Color}" />
        <Setter Property="EnableColor"
                Value="{StaticResource IconColor.Color}"></Setter>
        <Setter Property="DisableColor"  Value="{StaticResource IconColorDisabled.Color}"></Setter>
        <Setter Property="Template" >           
            <Setter.Value>
                <ControlTemplate TargetType="local:ZoomIn">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="PointerOver">
                                    <Storyboard>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="path" 
                                                        Storyboard.TargetProperty="Foreground.Color" 
                                                        To="{StaticResource IconColorHovered.Color}"/>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="border" 
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" 
                                                        To="{StaticResource ContentBackgroundHovered.Color}"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="path" 
                                                        Storyboard.TargetProperty="Foreground.Color" 
                                                        To="{StaticResource IconColorSelected.Color}"/>
                                        <ColorAnimation Duration="0" Storyboard.TargetName="border" 
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="{StaticResource ContentBackgroundSelected.Color}" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="border" Height="{Binding ToolBarIconHeight}" Width="{Binding ToolBarIconWidth}"  Margin="{Binding ToolBarIconMargin}" Background="Transparent"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}">
                            <TextBlock x:Name="path" 
                                       Text="&#xe71b;"
                                       Margin="2" 
                                       FontSize="18"
                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center">
                                <TextBlock.Foreground>
                                    <SolidColorBrush x:Name="borderColor" Color="{Binding IconBackground}" />
                                </TextBlock.Foreground>
                            </TextBlock>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionZoomOutStyle" TargetType="local:ZoomOut">
        <Setter  Property="IconBackground"
                 Value="{StaticResource IconColor.Color}" />
        <Setter Property="EnableColor"
                Value="{StaticResource IconColor.Color}"></Setter>
        <Setter Property="DisableColor"
                Value="{StaticResource IconColorDisabled.Color}"></Setter>
        <Setter Property="Template" >
            <Setter.Value>
                <ControlTemplate TargetType="local:ZoomOut">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="PointerOver">
                                    <Storyboard>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="path" 
                                                        Storyboard.TargetProperty="Foreground.Color" 
                                                        To="{StaticResource IconColorHovered.Color}"/>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="border" 
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" 
                                                        To="{StaticResource ContentBackgroundHovered.Color}"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="path" 
                                                        Storyboard.TargetProperty="Foreground.Color" 
                                                        To="{StaticResource IconColorSelected.Color}"/>
                                        <ColorAnimation Duration="0" Storyboard.TargetName="border" 
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="{StaticResource ContentBackgroundSelected.Color}" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="border" Height="{Binding ToolBarIconHeight}" Width="{Binding ToolBarIconWidth}"  Margin="{Binding ToolBarIconMargin}" Background="Transparent"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}">
                            <TextBlock x:Name="path" 
                                       Text="&#xe71c;"
                                       Margin="2" 
                                       FontSize="18"
                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center">
                                <TextBlock.Foreground>
                                    <SolidColorBrush x:Name="borderColor" Color="{Binding IconBackground}" />
                                </TextBlock.Foreground>
                            </TextBlock>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionZoomResetStyle" TargetType="local:ZoomReset">
        <Setter  Property="IconBackground"
                 Value="{StaticResource IconColor.Color}" />
        <Setter Property="EnableColor"
                Value="{StaticResource IconColor.Color}"></Setter>
        <Setter Property="DisableColor"
                Value="{StaticResource IconColorDisabled.Color}"></Setter>
        <Setter Property="Template" >
            <Setter.Value>
                <ControlTemplate TargetType="local:ZoomReset">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="PointerOver">
                                    <Storyboard>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="path" 
                                                        Storyboard.TargetProperty="Foreground.Color" 
                                                        To="{StaticResource IconColorHovered.Color}"/>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="border" 
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" 
                                                        To="{StaticResource ContentBackgroundHovered.Color}"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="path" 
                                                        Storyboard.TargetProperty="Foreground.Color" 
                                                        To="{StaticResource IconColorSelected.Color}"/>
                                        <ColorAnimation Duration="0" Storyboard.TargetName="border" 
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="{StaticResource ContentBackgroundSelected.Color}" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="border" Height="{Binding ToolBarIconHeight}" Width="{Binding ToolBarIconWidth}"  Margin="{Binding ToolBarIconMargin}" Background="Transparent"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}">
                            <TextBlock x:Name="path" 
                                       Text="&#xe840;"
                                       Margin="2" 
                                       FontSize="18"
                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center">
                                <TextBlock.Foreground>
                                    <SolidColorBrush x:Name="borderColor" Color="{Binding IconBackground}" />
                                </TextBlock.Foreground>
                            </TextBlock>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionZoomPanStyle" TargetType="local:ZoomPan">
        <Setter  Property="IconBackground" Value="{StaticResource IconColor.Color}" />
        <Setter Property="EnableColor"
                Value="{StaticResource IconColor.Color}"></Setter>
        <Setter Property="DisableColor"
                Value="{StaticResource IconColorDisabled.Color}"></Setter>
        <Setter Property="Template" >
            <Setter.Value>
                <ControlTemplate TargetType="local:ZoomPan">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="PointerOver">
                                    <Storyboard>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="path" 
                                                        Storyboard.TargetProperty="Foreground.Color" 
                                                        To="{StaticResource IconColorHovered.Color}"/>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="border" 
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" 
                                                        To="{StaticResource ContentBackgroundHovered.Color}"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="path" 
                                                        Storyboard.TargetProperty="Foreground.Color" 
                                                        To="{StaticResource IconColorSelected.Color}"/>
                                        <ColorAnimation Duration="0" Storyboard.TargetName="border" 
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="{StaticResource ContentBackgroundSelected.Color}" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="border" Height="{Binding ToolBarIconHeight}" Width="{Binding ToolBarIconWidth}"  Margin="{Binding ToolBarIconMargin}" Background="Transparent"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}">
                            <TextBlock x:Name="path" 
                                       Text="&#xe722;"
                                       Margin="2" 
                                       FontSize="18"
                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center">
                                <TextBlock.Foreground>
                                    <SolidColorBrush x:Name="borderColor" Color="{Binding IconBackground}" />
                                </TextBlock.Foreground>
                            </TextBlock>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SyncfusionSelectionZoomStyle" TargetType="local:SelectionZoom" >
        <Setter  Property="IconBackground" Value="{StaticResource IconColor.Color}" />
        <Setter Property="EnableColor"
                Value="{StaticResource IconColor.Color}"></Setter>
        <Setter Property="DisableColor"
                Value="{StaticResource IconColorDisabled.Color}"></Setter>
        <Setter Property="Template" >
            <Setter.Value>
                <ControlTemplate TargetType="local:SelectionZoom">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="PointerOver">
                                    <Storyboard>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="path" 
                                                        Storyboard.TargetProperty="Foreground.Color" 
                                                        To="{StaticResource IconColorHovered.Color}"/>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="border" 
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" 
                                                        To="{StaticResource ContentBackgroundHovered.Color}"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimation Duration="0" 
                                                        Storyboard.TargetName="path" 
                                                        Storyboard.TargetProperty="Foreground.Color" 
                                                        To="{StaticResource IconColorSelected.Color}"/>
                                        <ColorAnimation Duration="0" Storyboard.TargetName="border" 
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="{StaticResource ContentBackgroundSelected.Color}" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="border" Height="{Binding ToolBarIconHeight}" Width="{Binding ToolBarIconWidth}"  Margin="{Binding ToolBarIconMargin}" Background="Transparent"
                                CornerRadius="{StaticResource Windows11Light.CornerRadius4}">
                            <TextBlock x:Name="path" 
                                       Text="&#xe7ee;"
                                       Margin="2" 
                                       FontSize="18"
                                       FontFamily="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/Common/FontFamily/#Windows11ThemeControlIcons"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center">
                                <TextBlock.Foreground>
                                    <SolidColorBrush x:Name="borderColor" Color="{Binding IconBackground}" />
                                </TextBlock.Foreground>
                            </TextBlock>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="local:ZoomingToolBar" BasedOn="{StaticResource SyncfusionZoomingToolBarStyle}"></Style>

    <Style TargetType="local:ZoomIn" BasedOn="{StaticResource SyncfusionZoomInStyle}"></Style>

    <Style TargetType="local:ZoomOut" BasedOn="{StaticResource SyncfusionZoomOutStyle}"></Style>

    <Style TargetType="local:ZoomReset" BasedOn="{StaticResource SyncfusionZoomResetStyle}"></Style>

    <Style TargetType="local:ZoomPan" BasedOn="{StaticResource SyncfusionZoomPanStyle}"></Style>

    <Style TargetType="local:SelectionZoom" BasedOn="{StaticResource SyncfusionSelectionZoomStyle}"></Style>
</ResourceDictionary>
