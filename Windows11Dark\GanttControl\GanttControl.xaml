<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"  
                    
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:skinmanager="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:system="clr-namespace:System;assembly=mscorlib" 
                    xmlns:ganttcontrol="clr-namespace:Syncfusion.Windows.Controls.Gantt;assembly=Syncfusion.Gantt.Wpf">

    <ResourceDictionary.MergedDictionaries>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <skinmanager:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/GanttControl/GanttGrid.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/GanttControl/GanttSchedule.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/GanttControl/GanttChart.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/GanttControl/GanttChartItems.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GridSplitter.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- Gantt control -->

    <!-- Gantt Schedule -->

    <!-- Gantt Chart -->

    <!-- Resource view -->

    <ganttcontrol:CollectionToStringConverter x:Key="collectionToStringConverter"/>
    <ganttcontrol:BoolToVisibilityConverter x:Key="boolToVisibilityConverter"/>

    <Style x:Key="SyncfusionGanttControlStyle" TargetType="ganttcontrol:GanttControl">
        <Setter Property="Background" Value="{StaticResource ContentBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="ScheduleBackground" Value="{StaticResource ContentBackground}" />
        <Setter Property="ScheduleBorderBrush" Value="{StaticResource BorderAlt}" />
        <Setter Property="ScheduleBorderThickness" Value="0,0,0,1" />
        <Setter Property="ScheduleForeground" Value="{StaticResource ContentForeground}" />
        <Setter Property="NonWorkingHoursBackground" Value="{StaticResource ContentBackgroundAlt3}"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="ResourceContainerTemplate">
            <Setter.Value>
                <DataTemplate>
                    <Grid Background="Transparent">
                        <TextBlock Text="{TemplateBinding Content}" 
                                   Foreground="{StaticResource ContentForeground}" 
                                   FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                   FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                   VerticalAlignment="Center" 
                                   FontWeight="{StaticResource Windows11Dark.FontWeightMedium}" />
                    </Grid>
                </DataTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ganttcontrol:GanttControl}">
                    <Border BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}" 
                            Background="{TemplateBinding Background}"
                            ClipToBounds="True" 
                            SnapsToDevicePixels="True">
                        <Grid x:Name="mainGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="{TemplateBinding GridWidth}"/>
                                <ColumnDefinition Width="5"/>
                                <ColumnDefinition Width="{TemplateBinding ChartWidth}"/>
                            </Grid.ColumnDefinitions>
                            <ganttcontrol:GanttGrid x:Name="PART_GanttGrid" 
                                                    HeaderBackground ="{TemplateBinding GridHeaderBackground}"
                                                    HeaderForeground="{TemplateBinding GridHeaderForeground}"
                                                    ShowAddNewColumn="{TemplateBinding ShowAddNewColumn}"
                                                    ShowDateWithTime ="{TemplateBinding ShowDateWithTime}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="0,0,1,0"
                                                    Grid.IsSharedSizeScope="True"
                                                    Grid.Column="0" 
                                                    Margin="0" />
                            <GridSplitter x:Name="PART_GanttGridSplitter"
                                          Grid.Column="1"
                                          Width="5" 
                                          Style="{StaticResource WPFGridSplitterStyle}"
                                          ShowsPreview="True"
                                          HorizontalAlignment="Stretch"
                                          VerticalAlignment="Stretch">
                            </GridSplitter>
                            <ScrollViewer x:Name="PART_ScheduleViewScrollViewer"
                                          Grid.Column="2" 
                                          VerticalScrollBarVisibility="Disabled"
                                          HorizontalScrollBarVisibility="Auto">
                                <Grid Margin="-1,-1,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>
                                    <ganttcontrol:GanttSchedule x:Name="PART_GantSchedule"
                                                                Grid.Row="0" 
                                                                ClipToBounds="True"
                                                                HorizontalAlignment="Left"
                                                                VerticalAlignment="Top"
                                                                SnapsToDevicePixels="True"
                                                                IsUIVirtualizationEnabled="{TemplateBinding UseOnDemandSchedule}"
                                                                BorderBrush="{TemplateBinding ScheduleBorderBrush}"
                                                                BorderThickness="{TemplateBinding ScheduleBorderThickness}"
                                                                Foreground="{TemplateBinding ScheduleForeground}"
                                                                Background="{TemplateBinding ScheduleBackground}"
                                                                FontSize="{StaticResource Windows11Dark.BodyTextStyle}"
                                                                FontFamily="{StaticResource Windows11Dark.ThemeFontFamily}"
                                                                FontWeight="{StaticResource Windows11Dark.FontWeightNormal}"/>
                                    <ganttcontrol:GanttChart x:Name="PART_GanttChart" 
                                                             Grid.Row="1"  
                                                             TaskNodeBackground="{TemplateBinding TaskNodeBackground}"
                                                             HighlightItemBrush="{TemplateBinding HighlightItemBrush}"
                                                             ConnectorStroke="{TemplateBinding ConnectorStroke}"
                                                             ToolTipTemplate="{TemplateBinding ToolTipTemplate}"
                                                             ResourceNameVisibility="{TemplateBinding ResourceNameVisibility}"
                                                             HorizontalAlignment="Left"
                                                             SnapsToDevicePixels="True"
                                                             CurrentDateLine="{TemplateBinding CurrentDateLine}"
                                                             StickCurrentDateLineTo="{TemplateBinding StickCurrentDateLineTo}"
                                                             NonWorkingHoursBackground="{TemplateBinding NonWorkingHoursBackground}"
                                                             ProgressIndicatorBackground="{TemplateBinding ProgressIndicatorBackground}"
                                                             ShowResizingTooltip="{TemplateBinding ShowResizingTooltip}"
                                                             ShowNonWorkingHoursBackground="{TemplateBinding ShowNonWorkingHoursBackground}"
                                                             ShowChartLines="{TemplateBinding ShowChartLines}"
                                                             ShowGridLinesOnZooming="{TemplateBinding ShowGridLinesOnZooming}"
                                                             ResourceNamePlacement="{TemplateBinding ResourceNamePlacement}"
                                                             Width="{Binding ElementName=PART_GantSchedule, Path=Width}"
                                                             Background="{TemplateBinding Background}"
                                                             ResourceNameForeground="{StaticResource ContentForeground}"
                                                             RowHeight="{TemplateBinding RowHeight}"/>
                                </Grid>
                            </ScrollViewer>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style BasedOn="{StaticResource SyncfusionGanttControlStyle}" TargetType="ganttcontrol:GanttControl" />
    
    <DataTemplate x:Key="TooltipTemplate">
        <Border x:Name="PART_TooltipBorder" 
                Visibility="{Binding Converter={StaticResource boolToVisibilityConverter}}"
                Background="{Binding Background}"
                BorderBrush="{Binding BorderBrush}"
                BorderThickness="{Binding BorderThickness}"
                CornerRadius="{Binding CornerRadius}">
            <TextBlock x:Name="PART_TooltipTextBlock"
                       Background="Transparent"
                       FontSize="{Binding FontSize}"
                       FontFamily="{Binding FontFamily}"
                       FontWeight="{Binding FontWeight}"
                       Text="{Binding Path=CellValue,Converter={StaticResource collectionToStringConverter}}" />
        </Border>
    </DataTemplate>

</ResourceDictionary>
