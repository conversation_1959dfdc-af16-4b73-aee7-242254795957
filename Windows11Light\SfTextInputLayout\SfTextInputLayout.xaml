<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sync="clr-namespace:Syncfusion.UI.Xaml.TextInputLayout;assembly=Syncfusion.SfTextInputLayout.WPF"
                    
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">
    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>
    
    <BooleanToVisibilityConverter x:Key="VisibilityConverter" />

    <SolidColorBrush x:Key="TextInputLayout.Static.Border" Color="YellowGreen"></SolidColorBrush>
    <SolidColorBrush x:Key="TextInputLayout.Focused.Border" Color="DarkMagenta"></SolidColorBrush>

    <SolidColorBrush x:Key="TextInputLayout.MouseOver.BorderGradientStop1" Color="#FF707070"/>
    <SolidColorBrush x:Key="TextInputLayout.MouseOver.BorderGradientStop2" Color="#FF707070"/>

    <LinearGradientBrush x:Key="TextInputLayoutBorderStatic"
                         MappingMode="Absolute"
                         StartPoint="0,0"
                         EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1"
                            CenterY="0.5" />
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5"
                          Color="{StaticResource BorderGradient.Color}" />
            <GradientStop Offset="1.0"
                          Color="{StaticResource Border.Color}" />
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="TextInputLayoutBorderFocused"
                         MappingMode="Absolute"
                         StartPoint="0,0"
                         EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1"
                            CenterY="0.5" />
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="1.0"
                          Color="{StaticResource BorderAlt2Gradient.Color}" />
            <GradientStop Offset="1.0"
                          Color="{StaticResource BorderAlt2.Color}" />
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style  x:Key="SyncfusionSfTextInputLayoutStyle"
            TargetType="sync:SfTextInputLayout">
        <Setter Property="OutlineCornerRadius"
                Value="4"></Setter>
        <Setter Property="BorderThickness"
                Value="{StaticResource Windows11Light.BorderThickness1}"></Setter>
        <Setter Property="FocusedStrokeThickness"
                Value="{StaticResource Windows11Light.StrokeThickness2}"></Setter>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"></Setter>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Light.ThemeFontFamily}"></Setter>
        <Setter Property="Foreground"
                Value="{StaticResource PlaceholderForeground}"></Setter>
        <Setter Property="FocusedForeground"
                Value="{StaticResource ContentForeground}"></Setter>
        <Setter Property="ErrorForeground"
                Value="{StaticResource ErrorForeground}"></Setter>
        <Setter Property="IsTabStop"
                Value="False" />
        <Setter Property="ContainerBackground"
                Value="{StaticResource ContentBackgroundAlt4}"></Setter>
        <Setter Property="BorderBrush"
                Value="{StaticResource TextInputLayoutBorderStatic}"></Setter>
        <Setter Property="FocusedBorderBrush"
                Value="{StaticResource TextInputLayoutBorderFocused}"></Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type sync:SfTextInputLayout}">
                    <StackPanel>
                        <Grid x:Name="ContentGrid"
                              Background="Transparent">
                            <Grid.RowDefinitions>
                                <RowDefinition />
                                <RowDefinition Height="Auto" />
                                <RowDefinition />
                                <RowDefinition />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="0" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="0" />
                                <ColumnDefinition Width="0" />
                            </Grid.ColumnDefinitions>

                            <TextBlock x:Name="PART_HintTextBlock"
                                       Panel.ZIndex="1"
                                       Grid.Row="0"
                                       Grid.RowSpan="4"
                                       Grid.Column="1"                                       
                                       FontFamily="{TemplateBinding FontFamily}"
                                       IsHitTestVisible="False"
                                       Text="{TemplateBinding Hint}"
                                       Visibility="{TemplateBinding HintVisibility}"
                                       HorizontalAlignment="Left"
                                       VerticalAlignment="Center">
                            </TextBlock>

                            <Border x:Name="PART_ToggleButtonBorder" Grid.RowSpan="4" 
                                    Visibility="Collapsed" 
                                    Grid.Column="1" Panel.ZIndex="2"
                                    HorizontalAlignment="Right"
                                    IsHitTestVisible="False">
                                <Path VerticalAlignment="Center" x:Name="Arrow"
                                      Width="9" Height="7" Stretch="Fill">
                                    <Path.Data>
                                        <PathGeometry>M0.74499548,0 L5.0119957,4.7700001 9.2630047,0.017000169 10.008001,0.68400005 5.0119957,6.2700001 0,0.66699985 z</PathGeometry>
                                    </Path.Data>
                                </Path>
                            </Border>

                            <Grid x:Name="PART_BottomGrid"
                                  Grid.Row="4"
                                  Grid.Column="1"
                                  Grid.ColumnSpan="2">

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>

                                <TextBlock x:Name="PART_HelperTextBlock"
                                           Grid.Row="0"
                                           Grid.Column="0"
                                           Padding="0,4,0,0"
                                           FontSize="{TemplateBinding FontSize}"
                                           FontFamily="{TemplateBinding FontFamily}"
                                           Foreground="{StaticResource ContentForegroundAlt1}"
                                           Text="{TemplateBinding HelperText}"
                                           Visibility="{TemplateBinding HelperTextVisibility}"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Center" />

                                <TextBlock x:Name="PART_ErrorTextBlock"
                                           Grid.Row="0"
                                           Grid.Column="0"
                                           Padding="0,4,0,0"
                                           HorizontalAlignment="Left"
                                           FontSize="{TemplateBinding FontSize}"
                                           FontFamily="{TemplateBinding FontFamily}"
                                           VerticalAlignment="Center"
                                           Foreground="{TemplateBinding ErrorForeground}"
                                           Text="{TemplateBinding ErrorText}"
                                           Visibility="{TemplateBinding HasError, Converter={StaticResource VisibilityConverter}}" />

                                <TextBlock x:Name="PART_CounterTextBlock"
                                           Grid.Row="0"
                                           Visibility="{TemplateBinding CharCountVisibility}"
                                           FontSize="{TemplateBinding FontSize}"
                                           FontFamily="{TemplateBinding FontFamily}"
                                           Grid.Column="1"
                                           Foreground="{StaticResource ContentForeground}"
                                           HorizontalAlignment="Right"
                                           VerticalAlignment="Center"
                                           TextAlignment="Right" />
                            </Grid>

                            <Rectangle x:Name="PART_BaseLine"
                                       Panel.ZIndex="1"
                                       Grid.Row="3"
                                       Grid.Column="0"
                                       StrokeThickness="0"
                                       Grid.ColumnSpan="3"
                                       Height="1"
                                       VerticalAlignment="Center" />
                        </Grid>
                    </StackPanel>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=Self},Path=IsEnabled}"
                                     Value="False">
                            <Setter Property="ContainerBackground"
                                    Value="{StaticResource ContentBackgroundAlt6}"></Setter>
                            <Setter Property="BorderBrush"
                                    Value="{StaticResource SecondaryBorderDisabled}"></Setter>
                            <Setter Property="Foreground"
                                    Value="{StaticResource DisabledForeground}"></Setter>
                            <Setter TargetName="PART_HelperTextBlock"
                                    Property="Foreground"
                                    Value="Transparent"></Setter>
                            <Setter TargetName="PART_CounterTextBlock"
                                    Property="Foreground"
                                    Value="Transparent"></Setter>
                            <Setter TargetName="Arrow" Property="Fill" 
                                    Value="{StaticResource SecondaryBorderDisabled}" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=Self},Path=IsKeyboardFocusWithin}" Value="True">
                            <Setter Property="ContainerBackground"
                                    Value="{StaticResource ContentBackground}"></Setter>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="sync:SfTextInputLayout" BasedOn="{StaticResource SyncfusionSfTextInputLayoutStyle}"/>
</ResourceDictionary>
