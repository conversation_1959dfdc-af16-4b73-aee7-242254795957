<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	
    xmlns:local="clr-namespace:Syncfusion.Windows.Tools.Controls;assembly=Syncfusion.Shared.WPF"
    xmlns:skin="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    >
    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>

    <SolidColorBrush x:Key="ButtonAdv.Static.Border" Color="#FAFAFA" />

    <SolidColorBrush x:Key="ButtonAdv.MouseOver.Border" Color="#E0E0E0" />

    <LinearGradientBrush x:Key="ButtonAdvBorderBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource SecondaryBorderGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource SecondaryBorder.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ButtonAdvBorderBrushHovered" MappingMode="Absolute" StartPoint="0,0" EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5"/>
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource SecondaryBorderHoveredGradient.Color}"/>
            <GradientStop Offset="1.0" Color="{StaticResource SecondaryBorderHovered.Color}"/>
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <DataTemplate x:Key="LargeIconTemplate">
        <Image x:Name="LargeImage"
            Width="{Binding Width, RelativeSource={RelativeSource TemplatedParent}}"
            Height="{Binding Height, RelativeSource={RelativeSource TemplatedParent}}"
            Source="{Binding Tag, RelativeSource={RelativeSource TemplatedParent}}" />
    </DataTemplate>

    <DataTemplate x:Key="SmallIconTemplate">
        <Image x:Name="smallimage"
            Width="{Binding Width, RelativeSource={RelativeSource TemplatedParent}}"
            Height="{Binding Height, RelativeSource={RelativeSource TemplatedParent}}"
            Source="{Binding Tag, RelativeSource={RelativeSource TemplatedParent}}" />
    </DataTemplate>

    <ControlTemplate x:Key="SyncfusionButtonAdvControlTemplate" TargetType="local:ButtonAdv">
        <Border
            Name="ItemBorder1"
            MinHeight="{StaticResource Windows11Light.MinHeight}"
            Background="{TemplateBinding Background}"
            BorderBrush="{TemplateBinding BorderBrush}"
            BorderThickness="{TemplateBinding BorderThickness}"
            CornerRadius="{TemplateBinding CornerRadius}"
            
            SnapsToDevicePixels="True">
            <Border.Resources>
                <local:HorizontalContentToTextAlignmentConverter x:Key="TextAlignmentConverter" />
            </Border.Resources>
            <StackPanel
                    Margin="2,0,2,0"
                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                <ContentPresenter x:Name="LargeIconContent" ContentTemplate="{TemplateBinding IconTemplate}" Content="{Binding}" 
                                                          ContentTemplateSelector="{TemplateBinding IconTemplateSelector}"
                                                          Validation.ErrorTemplate="{x:Null}"/>
                <TextBlock
                        x:Name="PART_LargeText"
						Text="{TemplateBinding Label}"
                        HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                        VerticalAlignment="{TemplateBinding VerticalAlignment}"
                        Foreground="{TemplateBinding Foreground}"
						Margin="2,0,2,1">
                </TextBlock>
            </StackPanel>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default"/>
                    <Condition Property="IsFocused" Value="True"/>
                </MultiTrigger.Conditions>
                <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                <Setter TargetName="PART_LargeText" Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
            </MultiTrigger>

            <Trigger Property="IsMouseOver" Value="true">
                <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource ButtonAdvBorderBrushHovered}" />
                <Setter TargetName="PART_LargeText" Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                <Setter TargetName="PART_LargeText" Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
            </Trigger>
            <Trigger Property="IsMultiLine" Value="True">
                <Setter Property="TextBlock.TextWrapping" Value="Wrap" TargetName="PART_LargeText"/>
            </Trigger>
            <Trigger Property="IsMultiLine" Value="False">
                <Setter Property="TextBlock.TextWrapping" Value="NoWrap" TargetName="PART_LargeText"/>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsCheckable" Value="True" />
                    <Condition Property="IsChecked" Value="True" />
                    <Condition Property="IsEnabled" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                    <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                    <Setter TargetName="PART_LargeText" Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                    <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                </MultiTrigger.Setters>
            </MultiTrigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="LargeIconContent" Property="Opacity" Value="0.38" />
                <Setter TargetName="ItemBorder1" Property="Border.Background" Value="{StaticResource SecondaryBackgroundDisabled}" />
                <Setter TargetName="ItemBorder1" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderDisabled}" />
                <Setter TargetName="PART_LargeText" Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IconTemplate" Value="{x:Null}"/>
                    <Condition Property="IconTemplateSelector" Value="{x:Null}"/>
                </MultiTrigger.Conditions>
                <Setter Property="ContentTemplate" TargetName="LargeIconContent" Value="{StaticResource LargeIconTemplate}"/>
                <Setter Property="Tag" TargetName="LargeIconContent" Value="{Binding LargeIcon, RelativeSource={RelativeSource TemplatedParent}}"/>
            </MultiTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="SyncfusionButtonAdvStyle" TargetType="{x:Type local:ButtonAdv}">
        <Setter Property="SmallIcon" Value="/Syncfusion.Shared.WPF;component//Controls/ButtonControls/Images/WordArt16.png" />
        <Setter Property="LargeIcon" Value="/Syncfusion.Shared.WPF;component//Controls/ButtonControls/Images/WordArt32.png" />
        <Setter Property="Label" Value="Button" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="BorderThickness" Value="{StaticResource Windows11Light.BorderThickness1}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="Background" Value="{StaticResource SecondaryBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource ButtonAdvBorderBrush}" />
        <Setter Property="Foreground" Value="{StaticResource SecondaryForeground}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Light.ThemeFontFamily}" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Light.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Light.FontWeightNormal}" />
        <Setter Property="CornerRadius" Value="4" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:ButtonAdv">
                    <Border
                        Name="ItemBorder"
                        MinHeight="{StaticResource Windows11Light.MinHeight}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}"
                        
                        SnapsToDevicePixels="True">
                        <Border.Resources>
                            <local:HorizontalContentToTextAlignmentConverter x:Key="TextAlignmentConverter" />
                        </Border.Resources>
                        <Grid Margin="{TemplateBinding Padding}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <ContentPresenter x:Name="SmallIconContent" ContentTemplate="{TemplateBinding IconTemplate}" Content="{Binding}" Margin="2"
                                                          ContentTemplateSelector="{TemplateBinding IconTemplateSelector}"
                                                          Validation.ErrorTemplate="{x:Null}"/>
                            <AccessText
                                    x:Name="PART_NormalText"
                                    Grid.Column="1"
                                    Margin="2,0,2,0"
                                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                    FontFamily="{TemplateBinding FontFamily}"
                                    FontSize="{TemplateBinding FontSize}"
                                    Foreground="{TemplateBinding Foreground}"
                                    Text="{TemplateBinding Label}"
                                    TextAlignment="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=HorizontalContentAlignment, Converter={StaticResource TextAlignmentConverter}}" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IconHeight" Value="0" />
                                <Condition Property="IconWidth" Value="0" />
                                <Condition Property="sfskin:SfSkinManager.SizeMode" Value="Touch" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_NormalText" Property="Border.Margin" Value="0" />
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default"/>
                                <Condition Property="IsFocused" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource BorderAlt3}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderThickness" Value="0" />
                            <Setter TargetName="PART_NormalText" Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                        </MultiTrigger>

                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundHovered}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource ButtonAdvBorderBrushHovered}" />
                            <Setter TargetName="PART_NormalText" Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}" />
                        </Trigger>
                        <Trigger Property="IsMultiLine" Value="True">
                            <Setter TargetName="PART_NormalText" Property="TextBlock.TextWrapping" Value="Wrap" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                            <Setter TargetName="PART_NormalText" Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsCheckable" Value="True" />
                                <Condition Property="IsChecked" Value="True" />
                                <Condition Property="IsEnabled" Value="True" />
                            </MultiTrigger.Conditions>
                            <MultiTrigger.Setters>
                                <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundSelected}" />
                                <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderSelected}" />
                                <Setter TargetName="PART_NormalText" Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                                <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}" />
                            </MultiTrigger.Setters>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="SmallIconContent" Property="Opacity" Value="0.38" />
                            <Setter TargetName="ItemBorder" Property="Border.Background" Value="{StaticResource SecondaryBackgroundDisabled}" />
                            <Setter TargetName="ItemBorder" Property="Border.BorderBrush" Value="{StaticResource SecondaryBorderDisabled}" />
                            <Setter TargetName="PART_NormalText" Property="TextElement.Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IconTemplate" Value="{x:Null}"/>
                                <Condition Property="IconTemplateSelector" Value="{x:Null}"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="ContentTemplate" TargetName="SmallIconContent" Value="{StaticResource SmallIconTemplate}"/>
                            <Setter Property="Tag" TargetName="SmallIconContent" Value="{Binding SmallIcon, RelativeSource={RelativeSource TemplatedParent}}"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="SizeMode" Value="Large">
                <Setter Property="Template" Value="{StaticResource SyncfusionButtonAdvControlTemplate}" />
            </Trigger>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionButtonAdvStyle}" TargetType="{x:Type local:ButtonAdv}" />

</ResourceDictionary>
