<ResourceDictionary 
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:Syncfusion.Windows.Controls.Notification;assembly=Syncfusion.SfHubTile.WPF"
    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
    </ResourceDictionary.MergedDictionaries>

    <DataTemplate x:Key="ImageTileContentTemplate">
        <Grid>
            <Rectangle Fill="{Binding Background}" />
            <Rectangle Fill="White" Opacity="{Binding Opacity}" />
            <Image
                Width="{Binding ImageWidth}"
                Height="{Binding ImageHeight}"
                HorizontalAlignment="{Binding HorizontalImageAlignment}"
                VerticalAlignment="{Binding VerticalImageAlignment}"
                Source="{Binding Image}"
                Stretch="UniformToFill" />
        </Grid>
    </DataTemplate>

    <Style x:Key="HubTileHeaderStyle" TargetType="ContentPresenter">
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Bottom" />
        <Setter Property="TextElement.Foreground" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="Margin" Value="10,5" />
        <Setter Property="TextElement.FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="TextElement.FontWeight" Value="{StaticResource Windows11Dark.FontWeightMedium}" />
        <Setter Property="TextElement.FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="TextElement.Foreground" Value="{StaticResource PrimaryForeground}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="HubTileTitleStyle" TargetType="ContentControl">
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="Foreground" Value="{StaticResource PrimaryForeground}" />
        <Setter Property="Margin" Value="5" />
        <Setter Property="FontSize" Value="{StaticResource Windows11Dark.BodyTextStyle}" />
        <Setter Property="FontWeight" Value="{StaticResource Windows11Dark.FontWeightNormal}" />
        <Setter Property="FontFamily" Value="{StaticResource Windows11Dark.ThemeFontFamily}" />
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="{StaticResource PrimaryForegroundDisabled}" />
            </Trigger>
        </Style.Triggers>
       
    </Style>

    <Style x:Key="SyncfusionSfHubTileStyle" TargetType="local:SfHubTile">
        <Setter Property="Padding" Value="4" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Stretch" />
        <Setter Property="Background" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBackground}" />
        <Setter Property="HeaderStyle" Value="{StaticResource HubTileHeaderStyle}" />
        <Setter Property="TitleStyle" Value="{StaticResource HubTileTitleStyle}" />
        <Setter Property="Width" Value="150" />
        <Setter Property="Height" Value="150" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:SfHubTile">
                    <Grid x:Name="PART_Root">
                        <Rectangle
                            x:Name="PointerOveRect"
                            Margin="-2"
                            RadiusX="8"
                            RadiusY="8"
                            Fill="Transparent" />
                        <Border
                            x:Name="Part_Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            CornerRadius="8"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <Grid Margin="{TemplateBinding Padding}">
                                <Grid x:Name="PART_SlideRoot">
                                    <Grid x:Name="PART_LayoutRoot">
                                        <Border x:Name="MaskBorder" Background="{TemplateBinding Background}" CornerRadius="{Binding ElementName=Part_Border,Path=CornerRadius}"/>
                                        <ContentPresenter
                                                Content="{TemplateBinding Header}"
                                                ContentTemplate="{TemplateBinding HeaderTemplate}"
                                                ContentTemplateSelector="{TemplateBinding HeaderTemplateSelector}"
                                                Style="{TemplateBinding HeaderStyle}" >
                                            <ContentPresenter.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="TextBlock" />
                                            </ContentPresenter.Resources>
                                        </ContentPresenter>
                                        <Grid HorizontalAlignment="Center" VerticalAlignment="Center">
                                            <Image Source="{TemplateBinding ImageSource}" Stretch="None" />
                                        </Grid>

                                        <ContentControl
                                                HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                Content="{TemplateBinding Title}"
                                                Style="{TemplateBinding TitleStyle}">
                                            <ContentControl.Resources>
                                                <Style BasedOn="{x:Null}" TargetType="TextBlock" />
                                            </ContentControl.Resources>
                                        </ContentControl>
                                    </Grid>
                                    <ContentControl
                                        x:Name="PART_HubTileContent"
                                        HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Content="{TemplateBinding SecondaryContent}"
                                        ContentTemplate="{TemplateBinding SecondaryContentTemplate}"
                                        Visibility="Collapsed" >
                                        <ContentControl.OpacityMask>
                                            <VisualBrush Visual="{Binding Source={x:Reference MaskBorder}}" />
                                        </ContentControl.OpacityMask>
                                    </ContentControl>
                                </Grid>
                            </Grid>
                        </Border>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="PointerOver" />
                                <VisualState x:Name="PointerPressed" />
                                <VisualState x:Name="Disabled" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="PointerOveRect" Property="Fill" Value="{StaticResource PrimaryButtonBorder}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Part_Border" Property="BorderBrush" Value="{StaticResource PrimaryBackground}" />
                            <Setter TargetName="Part_Border" Property="Background" Value="{StaticResource PrimaryBackground}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionSfHubTileStyle}" TargetType="{x:Type local:SfHubTile}" />

</ResourceDictionary>
