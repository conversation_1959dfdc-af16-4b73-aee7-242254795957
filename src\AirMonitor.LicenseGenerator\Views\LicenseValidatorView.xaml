<UserControl x:Class="AirMonitor.LicenseGenerator.Views.LicenseValidatorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AirMonitor.LicenseGenerator.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <Grid Margin="{StaticResource LargePadding}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 文件选择区域 -->
        <GroupBox Grid.Row="0" Header="许可证文件" Style="{StaticResource ModernGroupBoxStyle}">
            <StackPanel>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox Grid.Column="0" Text="{Binding LicenseFilePath}" 
                             Style="{StaticResource ModernTextBoxStyle}" 
                             IsReadOnly="True"
                             ToolTip="许可证文件路径"/>
                    <Button Grid.Column="1" Content="选择文件" Command="{Binding SelectFileCommand}"
                            Style="{StaticResource SecondaryButtonStyle}" Margin="8,0,0,0"/>
                    <Button Grid.Column="2" Content="验证" Command="{Binding ValidateLicenseCommand}"
                            Style="{StaticResource PrimaryButtonStyle}" Margin="8,0,0,0"/>
                </Grid>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,16,0,0">
                    <Button Content="清除结果" Command="{Binding ClearResultCommand}" 
                            Style="{StaticResource SecondaryButtonStyle}"/>
                </StackPanel>
            </StackPanel>
        </GroupBox>

        <!-- 验证结果区域 -->
        <Grid Grid.Row="1" Margin="0,16,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 验证状态 -->
            <GroupBox Grid.Column="0" Header="验证状态" Style="{StaticResource ModernGroupBoxStyle}">
                <StackPanel>
                    <!-- 验证结果指示器 -->
                    <Border Background="{Binding IsValid, Converter={x:Static local:BooleanToColorConverter.Instance}}"
                            CornerRadius="{StaticResource MediumCornerRadius}"
                            Padding="{StaticResource MediumPadding}"
                            Margin="0,0,0,16">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="{Binding IsValid, Converter={x:Static local:BooleanToStatusIconConverter.Instance}}" 
                                       FontSize="24" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding IsValid, Converter={x:Static local:BooleanToStatusTextConverter.Instance}}" 
                                       FontSize="16" FontWeight="Medium" Foreground="White" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- 验证详情 -->
                    <TextBlock Text="验证详情:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <TextBox Text="{Binding ValidationResult.GetFormattedError, Mode=OneWay}" 
                             Style="{StaticResource ModernTextBoxStyle}"
                             Height="150" 
                             AcceptsReturn="True" 
                             VerticalScrollBarVisibility="Auto"
                             IsReadOnly="True"/>
                </StackPanel>
            </GroupBox>

            <!-- 许可证信息 -->
            <GroupBox Grid.Column="2" Header="许可证信息" Style="{StaticResource ModernGroupBoxStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <Grid Visibility="{Binding LicenseInfo, Converter={x:Static local:NullToVisibilityConverter.Instance}}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="许可证ID:" FontWeight="Medium" Margin="0,0,8,8"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding LicenseInfo.LicenseId}" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="产品名称:" FontWeight="Medium" Margin="0,0,8,8"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding LicenseInfo.ProductName}" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="产品版本:" FontWeight="Medium" Margin="0,0,8,8"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding LicenseInfo.ProductVersion}" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="客户名称:" FontWeight="Medium" Margin="0,0,8,8"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding LicenseInfo.CustomerName}" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="许可证类型:" FontWeight="Medium" Margin="0,0,8,8"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding LicenseInfo.LicenseType}" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="5" Grid.Column="0" Text="签发日期:" FontWeight="Medium" Margin="0,0,8,8"/>
                            <TextBlock Grid.Row="5" Grid.Column="1" Text="{Binding LicenseInfo.IssuedDate, StringFormat=yyyy-MM-dd}" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="6" Grid.Column="0" Text="过期日期:" FontWeight="Medium" Margin="0,0,8,8"/>
                            <TextBlock Grid.Row="6" Grid.Column="1" Text="{Binding LicenseInfo.ExpiryDate, StringFormat=yyyy-MM-dd}" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="7" Grid.Column="0" Text="剩余天数:" FontWeight="Medium" Margin="0,0,8,8"/>
                            <TextBlock Grid.Row="7" Grid.Column="1" Text="{Binding LicenseInfo, Converter={x:Static local:LicenseToRemainingDaysConverter.Instance}}" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="8" Grid.Column="0" Text="硬件指纹:" FontWeight="Medium" Margin="0,0,8,8"/>
                            <TextBlock Grid.Row="8" Grid.Column="1" Text="{Binding LicenseInfo.HardwareFingerprint}" 
                                       FontFamily="Consolas" FontSize="10" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="9" Grid.Column="0" Text="授权功能:" FontWeight="Medium" Margin="0,0,8,8" VerticalAlignment="Top"/>
                            <ItemsControl Grid.Row="9" Grid.Column="1" ItemsSource="{Binding LicenseInfo.AuthorizedFeatures}" Margin="0,0,0,8">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}" Margin="0,2"/>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </Grid>

                        <!-- 无许可证信息时的提示 -->
                        <TextBlock Text="请选择并验证许可证文件以查看详细信息" 
                                   HorizontalAlignment="Center" 
                                   VerticalAlignment="Center"
                                   Foreground="{StaticResource TextSecondaryBrush}"
                                   Visibility="{Binding LicenseInfo, Converter={x:Static local:NullToVisibilityConverter.Instance}, ConverterParameter=Inverse}"/>
                    </StackPanel>
                </ScrollViewer>
            </GroupBox>
        </Grid>
    </Grid>
</UserControl>
