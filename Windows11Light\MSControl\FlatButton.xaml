<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                                       
                    xmlns:shared="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Light"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Light"/>
    </ResourceDictionary.MergedDictionaries>
    
    <SolidColorBrush x:Key="Button.Static.Border" Color="#c8c8c8"/>

    <SolidColorBrush x:Key="Button.MouseOver.Border" Color="#179bd7"/>

    <SolidColorBrush x:Key="Button.Static.BorderGradientStop1" Color="#FF707070" />
    <SolidColorBrush x:Key="Button.Static.BorderGradientStop2" Color="#FF707070" />

    <LinearGradientBrush x:Key="ButtonBorderBrush"
                         MappingMode="Absolute"
                         StartPoint="0,0"
                         EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5" />
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource Button.Static.BorderGradientStop2}" />
            <GradientStop Offset="1.0" Color="{StaticResource Button.Static.BorderGradientStop1}" />
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ButtonBorderBrushHovered"
                         MappingMode="Absolute"
                         StartPoint="0,0"
                         EndPoint="0,2">
        <LinearGradientBrush.RelativeTransform>
            <ScaleTransform ScaleY="-1" CenterY="0.5" />
        </LinearGradientBrush.RelativeTransform>
        <LinearGradientBrush.GradientStops>
            <GradientStop Offset="0.5" Color="{StaticResource SecondaryBorderHoveredGradient.Color}" />
            <GradientStop Offset="1.0" Color="{StaticResource SecondaryBorderHovered.Color}" />
        </LinearGradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="WPFFlatButtonStyle"
           TargetType="{x:Type Button}">
        <Setter Property="FocusVisualStyle" 
                Value="{x:Null}"/>
        <Setter Property="Background" 
                Value="Transparent"/>
        <Setter Property="BorderThickness" 
                Value="{StaticResource Windows11Light.BorderThickness}"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource ButtonBorderBrushHovered}"/>
        <Setter Property="Foreground"
                Value="{StaticResource SecondaryForeground}"/>
        <Setter Property="FontFamily"
                Value="{StaticResource Windows11Light.ThemeFontFamily}"/>
        <Setter Property="FontSize"
                Value="{StaticResource Windows11Light.BodyTextStyle}"/>
        <Setter Property="FontWeight"
                Value="{StaticResource Windows11Light.FontWeightNormal}"/>
        <Setter Property="HorizontalContentAlignment" 
                Value="Center"/>
        <Setter Property="VerticalContentAlignment" 
                Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="border"                             
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Windows11Light.CornerRadius4}"
                            SnapsToDevicePixels="true">
                        <ContentPresenter x:Name="contentPresenter" 
                                          Focusable="False"
                                          Margin="{TemplateBinding Padding}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" 
                                          RecognizesAccessKey="True">
                            <ContentPresenter.Resources>
                                <Style BasedOn="{x:Null}" TargetType="{x:Type TextBlock}" />
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="sfskin:SfSkinManager.SizeMode" Value="Touch">
                            <Setter Property="MinHeight" Value="{StaticResource TouchMode.MinHeight}"/>
                        </Trigger>
                        <Trigger Property="IsDefaulted" Value="true">
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt3}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="Default"/>
                                <Condition Property="IsFocused" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="border" Value="{StaticResource SecondaryBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource BorderAlt3}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource SecondaryForegroundHovered}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                            <Setter Property="BorderThickness" TargetName="border" Value="{StaticResource Windows11Light.BorderThickness1}"/>
                        </MultiTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource SecondaryBackgroundHovered}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource ButtonBorderBrushHovered}" />
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource SecondaryForegroundHovered}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundHovered}"/>
                            <Setter Property="BorderThickness" TargetName="border" Value="{StaticResource Windows11Light.BorderThickness1}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource SecondaryBackgroundSelected}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource SecondaryBorderSelected}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource SecondaryForegroundSelected}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundSelected}"/>
                            <Setter Property="BorderThickness" TargetName="border" Value="{StaticResource Windows11Light.BorderThickness1}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource SecondaryBackgroundDisabled}"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource SecondaryBorderDisabled}"/>
                            <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="{StaticResource SecondaryForegroundDisabled}"/>
                            <Setter Property="Foreground" Value="{StaticResource SecondaryForegroundDisabled}"/>
                            <Setter Property="BorderThickness" TargetName="border" Value="{StaticResource Windows11Light.BorderThickness1}"/>
                            <Setter Property="Opacity" Value="1"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="sfskin:SkinManagerHelper.FocusVisualKind" Value="HighVisibility">
                <Setter Property="FocusVisualStyle" Value="{StaticResource KeyboardFocusVisualStyle}"/>
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary>
