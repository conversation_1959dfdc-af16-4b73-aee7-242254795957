<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    
                    xmlns:resources="clr-namespace:Syncfusion.Windows.Shared;assembly=Syncfusion.Shared.WPF"
                    xmlns:sfskin="clr-namespace:Syncfusion.SfSkinManager;assembly=Syncfusion.SfSkinManager.WPF"
                    xmlns:PrintPreview_printing="clr-namespace:Syncfusion.Windows.Shared.Printing;assembly=Syncfusion.Shared.WPF">

    <ResourceDictionary.MergedDictionaries>
        <sfskin:ResourceDictionaryProvider DictionaryName="Common" ThemeName="Windows11Dark"/>
        <sfskin:ResourceDictionaryProvider DictionaryName="Brushes" ThemeName="Windows11Dark"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/TextBlock.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Slider.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ScrollViewer.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphRepeatButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/ComboBox.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/ChromelessWindow/ChromelessWindow.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/PrimaryButton.xaml"/>
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/GlyphButton.xaml" />
        <ResourceDictionary Source="/Syncfusion.Themes.Windows11Dark.WPF;component/MSControl/Button.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <resources:BooleanToVisibilityConverterEx x:Key="BooleanToVisibilityConverter" />
    <PrintPreview_printing:PageSizeTextConverter x:Key="PageSizeTextConverter"/>
    <PrintPreview_printing:PrintQueueNameToVisibilityConverter x:Key="PrintQueueNameToVisibilityConverter"/>

    <CornerRadius x:Key="PrintPreview.PrintPreviewWindow.CopyCountCopyCountBox.Static.CornerRadius">2</CornerRadius>

    <Style x:Key="PrintWindowStyle" TargetType="{x:Type resources:ChromelessWindow}" BasedOn="{StaticResource SyncfusionChromelessWindowStyle}">
        <Setter Property="Height" 
                Value="650" />
        <Setter Property="Width"
                Value="840" />
        <Setter Property="MinHeight"
                Value="400" />
        <Setter Property="MinWidth"
                Value="500" />
        <Setter Property="Title" 
                Value="{resources:SharedLocalizationResourceExtension ResourceName=PrintPreview}" />
    </Style>

    <Style x:Key="SyncfusionPrintPageControlStyle" TargetType="{x:Type PrintPreview_printing:PrintPageControl}">
        <Setter Property="Background" 
                Value="Transparent" />
        <Setter Property="BorderBrush" 
                Value="Transparent" />
        <Setter Property="BorderThickness"
                Value="{StaticResource Windows11Dark.BorderThickness1}" />
        <Setter Property="Focusable" 
                Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type PrintPreview_printing:PrintPageControl}">
                    <Viewbox x:Name="PART_Viewbox">
                        <Border Width="{Binding PageWidth}"
                                Height="{Binding PageHeight}"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                SnapsToDevicePixels="True">
                            <Border Margin="{Binding PageMargin}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="*" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <ContentControl x:Name="Part_PageHeader" 
                                                    Height="{Binding PageHeaderHeight}"
                                                    ContentTemplate="{Binding PageHeaderTemplate}" 
                                                    Focusable="False"/>
                                    <Border x:Name="Part_ScalingBorder"
                                            Grid.Row="1">
                                        <ContentPresenter x:Name="Part_Panel"
                                                          Focusable="False" />
                                    </Border>
                                    <ContentControl x:Name="Part_PageFooter"
                                                    Grid.Row="2"
                                                    Focusable="False"
                                                    Height="{Binding PageFooterHeight}"
                                                    ContentTemplate="{Binding PageFooterTemplate}" />
                                </Grid>
                            </Border>
                        </Border>
                    </Viewbox>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionPrintPageControlStyle}" TargetType="{x:Type PrintPreview_printing:PrintPageControl}" />    
    
    <Style x:Key="Print_ComboBoxStyle"  TargetType="{x:Type ComboBox}" BasedOn="{StaticResource WPFComboBoxStyle}">
        <Setter Property="Popup.Placement"
                Value="Custom"/>
        <Setter Property="Popup.CustomPopupPlacementCallback"
                Value="{x:Static PrintPreview_printing:DropDownCustomPopupPlacement.Callback}"/>
        <Setter Property="Height" 
                Value="43"/>
        <Setter Property="Width"
                Value="220"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility"
                Value="Auto" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility"
                Value="Auto" />
        <Setter Property="Padding"
                Value="3,3,10,3" />
    </Style>

    <DataTemplate x:Key="OrientationTemplate">
        <Border>
            <Grid Height="43">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*" />
                    <ColumnDefinition Width="8*" />
                </Grid.ColumnDefinitions>
                <Grid Width="24" 
                      Height="24"
                      Margin="5,3,5,5">
                    <Path Data="{Binding Path=ImagePath}"                   
                          RenderTransformOrigin="0.5,0.5"        
                          Opacity=".54"
                          Fill="{StaticResource ContentForeground}"
                          Stretch="Uniform" />
                </Grid>
                <TextBlock Grid.Column="1" 
                           Margin="5,0,0,4" 
                           HorizontalAlignment="Left"
                           VerticalAlignment="Center"
                           Text="{Binding Path=OrientationName}"
                           Style="{StaticResource WPFTextBlockStyle}"/>
            </Grid>
        </Border>
    </DataTemplate>

    <DataTemplate x:Key="PageRangeSelectionTemplate">
        <Border>
            <Grid Height="43">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*" />
                    <ColumnDefinition Width="8*" />
                </Grid.ColumnDefinitions>
                <Grid Width="24" 
                      Height="24"
                      Margin="5,3,5,5">
                    <Path Data="{Binding Path=ImagePath}"                 
                          RenderTransformOrigin="0.5,0.5"
                          Opacity="1"                          
                          Fill="{StaticResource ContentForeground}"
                          Stretch="Uniform" />
                </Grid>
                <Grid Grid.Column="1"
                      Margin="5,4,0,6">
                    <Grid.RowDefinitions>
                        <RowDefinition  Height="17"/>
                        <RowDefinition />
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" 
                               HorizontalAlignment="Left"
                               VerticalAlignment="Center" 
                               Text="{Binding Path=PageRangeSelectionName}"
                               Style="{StaticResource WPFTextBlockStyle}"/>
                    <Grid Grid.Row="1" >
                        <StackPanel Orientation="Horizontal"  >
                            <TextBlock  HorizontalAlignment="Left" 
                                        VerticalAlignment="Bottom">
                                <TextBlock.Style>
                                    <Style TargetType="{x:Type TextBlock}" BasedOn="{StaticResource WPFTextBlockStyle}">
                                        <Setter Property="Text" 
                                                Value="{resources:SharedLocalizationResourceExtension ResourceName=PrintPreview_AllPages_ToolTip}"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Path=PageRangeSelectionName}" Value="{resources:SharedLocalizationResourceExtension ResourceName=PrintPreview_UserPages}" >
                                                <Setter Property="Text" 
                                                        Value="{resources:SharedLocalizationResourceExtension ResourceName=PrintPreview_UserPages_ToolTip}"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </StackPanel>
                    </Grid>
                </Grid>
            </Grid>
        </Border>
    </DataTemplate>

    <DataTemplate x:Key="CollatedTemplate">
        <Border>
            <Grid Height="43">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*" />
                    <ColumnDefinition Width="8*" />
                </Grid.ColumnDefinitions>
                <Grid Width="24"
                      Height="24"
                      Margin="5,3,5,5">
                    <Path Data="{Binding Path=ImagePath}"                            
                          Fill="{StaticResource ContentForeground}"
                          Opacity="1"  
                          RenderTransformOrigin="0.5,0.5"
                          Stretch="Uniform" />
                </Grid>
                <Grid Grid.Column="1"
                      Margin="5,4,0,6">
                    <Grid.RowDefinitions>
                        <RowDefinition  Height="17"/>
                        <RowDefinition />
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0"  
                               HorizontalAlignment="Left" 
                               VerticalAlignment="Center"
                               Text="{Binding Path=CollationName}"
                               Style="{StaticResource WPFTextBlockStyle}"/>
                    <Grid Grid.Row="1">
                        <StackPanel Orientation="Horizontal" >
                            <TextBlock HorizontalAlignment="Left" 
                                       VerticalAlignment="Bottom">
                                <TextBlock.Style>
                                    <Style TargetType="{x:Type TextBlock}" BasedOn="{StaticResource WPFTextBlockStyle}">
                                        <Setter Property="Text"
                                                Value="1,2,3   1,2,3   1,2,3"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Path=CollationName}" Value="Uncollated" >
                                                <Setter Property="Text"
                                                        Value="1,1,1   2,2,2   3,3,3"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </StackPanel>
                    </Grid>
                </Grid>
            </Grid>
        </Border>
    </DataTemplate>

    <DataTemplate x:Key="ScaleOptionTemplate">
        <Border Background="Transparent">
            <Grid Height="43">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Grid Width="24"
                      Height="24"
                      Margin="5,3,5,5">
                    <Path Data="{Binding Path=ImagePath}"                            
                          Fill="{StaticResource ContentForeground}"
                          Opacity="1"  
                          Stretch="Uniform" >
                    </Path>
                </Grid>
                <TextBlock Grid.Column="1"
                           Margin="5,0,0,4"
                           HorizontalAlignment="Left"
                           VerticalAlignment="Center"
                           Text="{Binding Path=ScaleName}"
                           Style="{StaticResource WPFTextBlockStyle}"/>
            </Grid>
        </Border>
    </DataTemplate>

    <DataTemplate x:Key="MarginTemplate">
        <Border Background="Transparent">
            <Grid Height="auto">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Grid Width="24" 
                      Height="24">
                    <Grid.Margin>
                        <Thickness Left="5" Top="3" Right="5" Bottom="5"/>
                    </Grid.Margin>
                    <Path Data="{Binding Path=ImagePath}"                            
                          Fill="{StaticResource ContentForeground}"
                          Opacity="1"  
                          Stretch="Uniform" />
                </Grid>
                <Grid Grid.Column="1" 
                      Margin="5,0,0,0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="auto"/>
                        <RowDefinition />
                    </Grid.RowDefinitions>
                    <TextBlock HorizontalAlignment="Left"
                               VerticalAlignment="Center"
                               Text="{Binding Path=MarginName}" 
                               Style="{StaticResource WPFTextBlockStyle}">
                        <TextBlock.Margin>
                                <Thickness Left="0" Top="0" Right="0" Bottom="0"/>
                            </TextBlock.Margin>
                    </TextBlock>
                    <Grid Grid.Row="1" >
                        <Grid.RowDefinitions>
                            <RowDefinition Height="auto" />
                            <RowDefinition Height="auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <StackPanel x:Name="_LeftMargin"
                                    Orientation="Horizontal">
                            <StackPanel.Margin>
                                <Thickness Left="0" Top="2" Right="0" Bottom="0"/>
                            </StackPanel.Margin>
                            <TextBlock HorizontalAlignment="Left" 
                                       VerticalAlignment="Top"
                                       Text="{resources:SharedLocalizationResourceExtension ResourceName=Left}" 
                                       Style="{StaticResource WPFTextBlockStyle}"/>
                            <TextBlock HorizontalAlignment="Left" 
                                       VerticalAlignment="Top"
                                       Text="{Binding Path=Thickness.Left, StringFormat=: {0} cm,Mode=TwoWay}"  
                                       Style="{StaticResource WPFTextBlockStyle}"/>
                        </StackPanel>
                        <StackPanel x:Name="_RightMargin"
                                    Grid.Column="1"
                                    Orientation="Horizontal">
                            <StackPanel.Margin>
                                <Thickness Left="4" Top="2" Right="0" Bottom="0"/>
                            </StackPanel.Margin>
                            <TextBlock HorizontalAlignment="Left" 
                                       VerticalAlignment="Top"
                                       Text="{resources:SharedLocalizationResourceExtension ResourceName=Right}"
                                       Style="{StaticResource WPFTextBlockStyle}"/>
                            <TextBlock HorizontalAlignment="Left" 
                                       VerticalAlignment="Top"
                                       Text="{Binding Path=Thickness.Right, StringFormat=: {0} cm,Mode=TwoWay}" 
                                       Style="{StaticResource WPFTextBlockStyle}"/>
                        </StackPanel>
                        <StackPanel x:Name="_TopMargin"
                                    Grid.Row="1" 
                                    Orientation="Horizontal">
                            <StackPanel.Margin>
                                <Thickness Left="0" Top="0" Right="0" Bottom="0"/>
                            </StackPanel.Margin>
                            <TextBlock HorizontalAlignment="Left" 
                                       VerticalAlignment="Top"
                                       Text="{resources:SharedLocalizationResourceExtension ResourceName=Top}" 
                                       Style="{StaticResource WPFTextBlockStyle}"/>
                            <TextBlock HorizontalAlignment="Left" 
                                       VerticalAlignment="Top"
                                       Text="{Binding Path=Thickness.Top, StringFormat=: {0} cm,Mode=TwoWay}" 
                                       Style="{StaticResource WPFTextBlockStyle}"/>
                        </StackPanel>
                        <StackPanel x:Name="_BottomMargin"
                                    Grid.Row="1"
                                    Grid.Column="1"
                                    Orientation="Horizontal">
                            <StackPanel.Margin>
                                <Thickness Left="4" Top="0" Right="0" Bottom="0"/>
                            </StackPanel.Margin>
                            <TextBlock HorizontalAlignment="Left" 
                                       VerticalAlignment="Top"
                                       Text="{resources:SharedLocalizationResourceExtension ResourceName=Bottom}"
                                       Style="{StaticResource WPFTextBlockStyle}"/>
                            <TextBlock HorizontalAlignment="Left" 
                                       VerticalAlignment="Top"
                                       Text="{Binding Path=Thickness.Bottom, StringFormat=: {0} cm,Mode=TwoWay}"
                                       Style="{StaticResource WPFTextBlockStyle}"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </Grid>
        </Border>
        <DataTemplate.Triggers>
            <DataTrigger Value="True" Binding="{Binding Path=IsSelected, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ComboBoxItem}}">
                <Setter TargetName="_TopMargin" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="_BottomMargin" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
            <DataTrigger Value="{x:Null}" Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ComboBoxItem}}">
                <Setter TargetName="_TopMargin" Property="Visibility" Value="Collapsed" />
                <Setter TargetName="_BottomMargin" Property="Visibility" Value="Collapsed" />
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <DataTemplate x:Key="PageSizeTemplate">
        <Border Background="Transparent">
            <Grid Height="43">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*" />
                    <ColumnDefinition Width="8*" />
                </Grid.ColumnDefinitions>
                <Grid Width="24"
                      Height="24"
                      Margin="5,3,5,5"
                      VerticalAlignment="Top">
                    <Path Data="{Binding Path=ImagePath.Data}"                           
                          Width="{Binding Path=ImagePath.Width}"
                          Height="{Binding Path=ImagePath.Height}"                            
                          Fill="{StaticResource ContentForeground}"
                          Opacity="1"  
                          HorizontalAlignment="Left"
                          VerticalAlignment="Center"
                          Stretch="Fill" />
                </Grid>
                <Grid Grid.Column="1" Margin="5,0,0,4">
                    <Grid.RowDefinitions>
                        <RowDefinition />
                        <RowDefinition />
                    </Grid.RowDefinitions>
                    <TextBlock HorizontalAlignment="Left"
                               VerticalAlignment="Center"
                               Text="{Binding Path=PageSizeName}"
                               Style="{StaticResource WPFTextBlockStyle}"/>
                    <TextBlock Grid.Row="1"
                               HorizontalAlignment="Left"
                               VerticalAlignment="Center"
                               Style="{StaticResource WPFTextBlockStyle}">
                        <TextBlock.Text>
                            <MultiBinding Converter="{StaticResource PageSizeTextConverter}">
                                <MultiBinding.Bindings>
                                    <Binding Path="Size" />
                                    <Binding Path="PageSizeUnit"/>
                                </MultiBinding.Bindings>
                            </MultiBinding>
                        </TextBlock.Text>
                    </TextBlock>
                </Grid>
            </Grid>
        </Border>
    </DataTemplate>

    <DataTemplate x:Key="PrintersListItemTemplate">
        <Border x:Name="Border">
            <Grid Height="43">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="35" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid x:Name="Printer_Grid"                      
                      Margin="3,3,2,3"
                      HorizontalAlignment="Left"
                      VerticalAlignment="Center">
                    <Grid x:Name="Printer"
                          Visibility="{Binding PrintQueue.Name,Converter={StaticResource PrintQueueNameToVisibilityConverter},ConverterParameter=Printer}" 
                          HorizontalAlignment="Left" 
                          VerticalAlignment="Top"
                          Height="24"  
                          Width="24">
                        <Path Data="M5.25 16.25H2.25C1.42157 16.25 0.75 15.5784 0.75 14.75V9.5C0.75 7.42893 2.42893 5.75 4.5 5.75H5.25M18.75 16.25H21.75C22.5784 16.25 23.25 15.5784 23.25 14.75V9.5C23.25 7.42893 21.5711 5.75 19.5 5.75H18.75M18.75 16.25V14C18.75 12.7574 17.7426 11.75 16.5 11.75H7.5C6.25736 11.75 5.25 12.7574 5.25 14V18.5C5.25 19.7426 6.25736 20.75 7.5 20.75H16.5C17.7426 20.75 18.75 19.7426 18.75 18.5V16.25ZM18.75 5.75V3.5C18.75 2.25736 17.7426 1.25 16.5 1.25H7.5C6.25736 1.25 5.25 2.25736 5.25 3.5V5.75M18.75 5.75H5.25"
                              Stroke="{StaticResource IconColor}"
                              Stretch="Fill"/>
                    </Grid>
                    <Grid x:Name="Fax"
                          Visibility="{Binding PrintQueue.Name,Converter={StaticResource PrintQueueNameToVisibilityConverter},ConverterParameter=Fax}"
                          HorizontalAlignment="Left" 
                          VerticalAlignment="Top"
                          Height="24"
                          Width="24">
                        <Path Data="M18.75 5.5H19.5C21.5711 5.5 23.25 7.17893 23.25 9.25V14.5C23.25 15.3284 22.5784 16 21.75 16H5.25H2.25C1.42157 16 0.75 15.3284 0.75 14.5V9.25C0.75 7.17893 2.42893 5.5 4.5 5.5H5.25M18.75 5.5V3.25C18.75 2.00736 17.7426 1 16.5 1H7.5C6.25736 1 5.25 2.00736 5.25 3.25V5.5M18.75 5.5H5.25"
                              Stroke="{StaticResource IconColor}"
                              Stretch="Fill"/>
                    </Grid>
                    <Grid x:Name="Tick" 
                          Visibility="{Binding IsDefault, Converter={StaticResource BooleanToVisibilityConverter}}"
                          HorizontalAlignment="Right" 
                          VerticalAlignment="Bottom"
                          Height="14" 
                          Width="14"
                          Margin="10,10,0,0">
                        <Path Data="M8,0 C12.418,0 16,3.5820001 16,8.0000002 16,12.418 12.418,16 8,16 3.582,16 0,12.418 0,8.0000002 0,3.5820001 3.582,0 8,0 z" 
                              Fill="{StaticResource PrimaryForeground}"
                              Stretch="Fill"/>
                        <Path Data="M10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20ZM16.1785 7.84518C16.8294 7.1943 16.8294 6.13903 16.1785 5.48816C15.5276 4.83728 14.4724 4.83728 13.8215 5.48816L8.33333 10.9763L6.17851 8.82149C5.52764 8.17061 4.47236 8.17061 3.82149 8.82149C3.17061 9.47236 3.17061 10.5276 3.82149 11.1785L7.15482 14.5118C7.8057 15.1627 8.86097 15.1627 9.51184 14.5118L16.1785 7.84518Z"
                              Fill="{StaticResource IconColor}"
                              Stretch="Fill"/>
                    </Grid>
                </Grid>
                <TextBlock Grid.Column="1" 
                           Margin="1,0,0,1"
                           HorizontalAlignment="Left"
                           VerticalAlignment="Center" 
                           Text="{Binding  PrintQueue.Name}"
                           Style="{StaticResource WPFTextBlockStyle}"/>
            </Grid>
        </Border>
    </DataTemplate>

    <Style x:Key="SyncfusionPrintOptionsControlStyle" TargetType="{x:Type PrintPreview_printing:PrintOptionsControl}">
        <Setter Property="Focusable"
                Value="False"/>
        <Setter Property="BorderBrush"
                Value="{StaticResource BorderAlt}"/>
        <Setter Property="BorderThickness"
                Value="0,0,1,0"/>
        <Setter Property="Background"
                Value="{StaticResource ContentBackground}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type PrintPreview_printing:PrintOptionsControl}">
                    <Border x:Name="PART_Border" 
                            BorderBrush="{TemplateBinding BorderBrush}" 
                            Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <ScrollViewer HorizontalScrollBarVisibility="Disabled"
                                      VerticalScrollBarVisibility="Auto"
                                      Style="{StaticResource WPFScrollViewerStyle}">
                            <Grid HorizontalAlignment="Center">
                                <StackPanel>
                                    <StackPanel  Orientation="Horizontal">
                                        <Button x:Name="Part_QuickPrintButton"
                                                Height="80"
                                                Width="80"
                                                HorizontalAlignment="Left"
                                                VerticalAlignment="Center"                                                
                                                Command="{Binding QuickPrintCommand}"                                                
                                                Style="{StaticResource WPFButtonStyle}"
                                                Tag="Print"
                                                Margin="0,12,17,20">
                                            <Grid
                                                SnapsToDevicePixels="True">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition />
                                                    <RowDefinition />
                                                </Grid.RowDefinitions>
                                                <Path x:Name="path"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center" 
                                                      Width="Auto" 
                                                      Height="Auto"                                                      
                                                      Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}" 
                                                      Stretch="Fill" 
                                                      SnapsToDevicePixels="True"
                                                       Margin="24.1,2,24.1,8">                                                    
                                                    <Path.Data>
                                                        <PathGeometry>M4 3.5C4 2.11929 5.11929 1 6.5 1H17.5C18.8807 1 20 2.11929 20 3.5V5.5H20.5C22.433 5.5 24 7.067 24 9V16.5C24 17.6046 23.1046 18.5 22 18.5H20V20.5C20 21.8807 18.8807 23 17.5 23H6.5C5.11929 23 4 21.8807 4 20.5V18.5H2C0.895431 18.5 0 17.6046 0 16.5V9C0 7.067 1.567 5.5 3.5 5.5H4V3.5ZM5 5.5H19V3.5C19 2.67157 18.3284 2 17.5 2H6.5C5.67157 2 5 2.67157 5 3.5V5.5ZM4 17.5V15.5C4 14.1193 5.11929 13 6.5 13H17.5C18.8807 13 20 14.1193 20 15.5V17.5H22C22.5523 17.5 23 17.0523 23 16.5V9C23 7.61929 21.8807 6.5 20.5 6.5H3.5C2.11929 6.5 1 7.61929 1 9V16.5C1 17.0523 1.44772 17.5 2 17.5H4ZM6.5 14C5.67157 14 5 14.6716 5 15.5V20.5C5 21.3284 5.67157 22 6.5 22H17.5C18.3284 22 19 21.3284 19 20.5V15.5C19 14.6716 18.3284 14 17.5 14H6.5Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                                <TextBlock x:Name="textBlock"
                                                           Grid.Row="1"
                                                           Height="14"           
                                                           Foreground="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"
                                                           SnapsToDevicePixels="True"
                                                           Text="{resources:SharedLocalizationResourceExtension ResourceName=Print}" />
                                            </Grid>
                                        </Button>
                                        <TextBlock x:Name="Part_Copies"
                                                   Width="39" 
                                                   Height="16"
                                                   HorizontalAlignment="Left" 
                                                   VerticalAlignment="Top"
                                                   Margin="0,22,0,0"
                                                   Text="{Binding Source={resources:SharedLocalizationResourceExtension ResourceName=PrintPreview_Copies}, StringFormat={}{0}:}" 
                                                   Style="{StaticResource WPFTextBlockStyle}">
                                            </TextBlock>
                                        <Grid x:Name="Part_CopyCount_Grid" 
                                              Width="60"
                                              Height="24"
                                              HorizontalAlignment="Left"
                                              VerticalAlignment="Top"
                                              Margin="5,18,0,0">
                                            <Border x:Name="Border"
                                                    Width="{TemplateBinding Width}"
                                                    SnapsToDevicePixels="True"
                                                    BorderThickness="1"
                                                    CornerRadius="{StaticResource PrintPreview.PrintPreviewWindow.CopyCountCopyCountBox.Static.CornerRadius}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    Background="{TemplateBinding Background}">
                                                <Grid  ClipToBounds="True">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBox x:Name="Part_CopyTextBox" 
                                                             Grid.Column="0" 
                                                             Grid.RowSpan="2"  
                                                             BorderThickness="{StaticResource Windows11Dark.BorderThickness}"
                                                             Width="{TemplateBinding Width}"
                                                             Height="{TemplateBinding Height}"
                                                             HorizontalContentAlignment="Left"
                                                             VerticalContentAlignment="Center"
                                                             Style="{StaticResource WPFTextBoxStyle}"
                                                             Text="{Binding CopiesCount,Mode=TwoWay}"/>
                                                    <RepeatButton x:Name="Copy_upbutton"
                                                                  Grid.Row="0" 
                                                                  Grid.Column="1"
                                                                  Width="15"
                                                                  Focusable="{TemplateBinding Focusable}"
                                                                  IsTabStop="False" 
                                                                  Margin=".5,1,.5,0"
                                                                  Style="{StaticResource WPFGlyphRepeatButtonStyle}">
                                                        <Path x:Name="Copy_upbuttonpath"
                                                              Height="5"
                                                              Width="9"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                                              Stretch="None"
                                                              RenderTransformOrigin=".5,.5"
                                                              Margin="-1">
                                                            <Path.Data>
                                                                <PathGeometry>M0.503906 4.5C0.640625 4.5 0.757813 4.45117 0.855469 4.35352L4.00195 1.20703L7.14844 4.35352C7.24609 4.45117 7.36328 4.5 7.5 4.5C7.63672 4.5 7.75391 4.45313 7.85156 4.35938C7.95312 4.26172 8.00391 4.14258 8.00391 4.00195C8.00391 3.86523 7.95508 3.74805 7.85742 3.65039L4.35352 0.146484C4.25586 0.0488281 4.13867 -5.97617e-09 4.00195 0C3.86523 5.97617e-09 3.74805 0.0488281 3.65039 0.146484L0.146484 3.65039C0.0488281 3.74805 -5.97617e-09 3.86523 0 4.00195C6.14691e-09 4.14258 0.0488281 4.26172 0.146484 4.35938C0.248047 4.45312 0.367188 4.5 0.503906 4.5Z</PathGeometry>
                                                            </Path.Data>
                                                            <Path.RenderTransform>
                                                                <TransformGroup>
                                                                    <ScaleTransform />
                                                                    <SkewTransform />
                                                                    <RotateTransform Angle="0" />
                                                                    <TranslateTransform />
                                                                </TransformGroup>
                                                            </Path.RenderTransform>
                                                        </Path>
                                                    </RepeatButton>
                                                    <RepeatButton x:Name="Copy_downbutton"
                                                                  Grid.Row="2" 
                                                                  Grid.Column="1" 
                                                                  Width="15"
                                                                  IsTabStop="False"  
                                                                  Focusable="{TemplateBinding Focusable}"
                                                                  Margin=".5,0,.5,.5"
                                                                  Style="{StaticResource WPFGlyphRepeatButtonStyle}">
                                                        <Path x:Name="Copy_downbuttonpath"
                                                              Height="5" 
                                                              Width="9" 
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"                                                              
                                                              Stretch="None" 
                                                              Margin="-1">
                                                            <Path.Data>
                                                                <PathGeometry>M7.5 0C7.36328 0 7.24609 0.0488281 7.14844 0.146484L4.00195 3.29297L0.855469 0.146484C0.757812 0.0488281 0.640625 0 0.503906 0C0.367188 0 0.25 0.046875 0.152344 0.140625C0.0507812 0.238281 0 0.357422 0 0.498047C0 0.634766 0.0488281 0.751953 0.146484 0.849609L3.65039 4.35352C3.74805 4.45117 3.86523 4.5 4.00195 4.5C4.13867 4.5 4.25586 4.45117 4.35352 4.35352L7.85742 0.849609C7.95508 0.751953 8.00391 0.634766 8.00391 0.498047C8.00391 0.357422 7.95508 0.238281 7.85742 0.140625C7.75586 0.046875 7.63672 0 7.5 0Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </RepeatButton>
                                                </Grid>
                                            </Border>
                                        </Grid>
                                    </StackPanel>
                                    <TextBlock x:Name="Part_PrinterTitle" 
                                               HorizontalAlignment="Left"
                                               Text="{resources:SharedLocalizationResourceExtension ResourceName=PrintPreview_Printer}" 
                                               Style="{StaticResource WPFTextBlockStyle}"
                                               FontSize="16">
                                        <TextBlock.Margin>
                                            <Thickness Left="0" Top="2" Right="0" Bottom="4"/>
                                        </TextBlock.Margin>
                                    </TextBlock>
                                    <ComboBox x:Name="PART_PrintersComboBox"
                                              HorizontalAlignment="Left"
                                              ItemsSource="{Binding Printers}"
                                              ItemTemplate="{StaticResource PrintersListItemTemplate}"
                                              Style="{StaticResource Print_ComboBoxStyle}"
                                              SelectedItem="{Binding SelectedPrinter}">
                                        <ComboBox.Margin>
                                            <Thickness Left="0" Top="4" Right="0" Bottom="4"/>
                                        </ComboBox.Margin>
                                    </ComboBox>
                                    <TextBlock x:Name="Part_SettingsTitle"                                                
                                               HorizontalAlignment="Left"
                                               Text="{resources:SharedLocalizationResourceExtension ResourceName=Settings}" 
                                               Style="{StaticResource WPFTextBlockStyle}"
                                               FontSize="16">
                                        <TextBlock.Margin>
                                            <Thickness Left="0" Top="8" Right="0" Bottom="4"/>
                                        </TextBlock.Margin>
                                    </TextBlock>
                                    <ComboBox x:Name="PART_PageRangeSelection"
                                              HorizontalAlignment="Left"
                                              SelectedValue="{Binding SelectedPrintPageRange, Mode=TwoWay}"
                                              SelectedValuePath="PageRangeSelection"
                                              ItemsSource="{Binding PrintPageRangeSelectionOptions}"
                                              ItemTemplate="{StaticResource PageRangeSelectionTemplate}"
                                              Style="{StaticResource Print_ComboBoxStyle}">
                                        <ComboBox.Margin>
                                            <Thickness Left="0" Top="4" Right="0" Bottom="4"/>
                                        </ComboBox.Margin>
                                    </ComboBox>
                                    <StackPanel Orientation="Horizontal">
                                        <StackPanel.Margin>
                                            <Thickness Left="0" Top="4" Right="0" Bottom="4"/>
                                        </StackPanel.Margin>
                                        <TextBlock x:Name="Part_Pages"
                                                   HorizontalAlignment="Left" 
                                                   VerticalAlignment="Center" 
                                                   Text="{Binding Source={resources:SharedLocalizationResourceExtension ResourceName=PrintPreview_Pages}, StringFormat={}{0}:}"
                                                   Style="{StaticResource WPFTextBlockStyle}">
                                        </TextBlock>
                                        <Grid x:Name="Part_FromPage_Grid" 
                                              Width="60"
                                              Height="24" 
                                              HorizontalAlignment="Left" 
                                              VerticalAlignment="Top"
                                              Margin="5,0,8,0" >
                                            <Border x:Name="FromPage_Border"   
                                                    Width="{TemplateBinding Width}"
                                                    SnapsToDevicePixels="True"
                                                    BorderThickness="1" 
                                                    CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                                    BorderBrush="{TemplateBinding BorderBrush}" 
                                                    Background="{TemplateBinding Background}">
                                                <Grid ClipToBounds="True">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBox x:Name="Part_FromPageTextBox"
                                                             Grid.Column="0" 
                                                             Grid.RowSpan="2"                                                              
                                                             BorderThickness="{StaticResource Windows11Dark.BorderThickness}"
                                                             Width="{TemplateBinding Width}"
                                                             Height="{TemplateBinding Height}" 
                                                             HorizontalContentAlignment="Left"
                                                             VerticalContentAlignment="Center"
                                                             Text="{Binding FromPage,Mode=TwoWay}"                                                            
                                                             Style="{StaticResource WPFTextBoxStyle}"/>
                                                    <RepeatButton x:Name="FromPage_upbutton"
                                                                  Grid.Row="0"
                                                                  Grid.Column="1"  
                                                                  Width="15"
                                                                  IsTabStop="False" 
                                                                  Margin=".5,1,.5,0"
                                                                  Focusable="{TemplateBinding Focusable}"
                                                                  Style="{StaticResource WPFGlyphRepeatButtonStyle}" >
                                                        <Path x:Name="FromPage_upbuttonpath"
                                                              Height="5" 
                                                              Width="9"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                                              Stretch="None"
                                                              RenderTransformOrigin=".5,.5"
                                                              Margin="-1">
                                                            <Path.Data>
                                                                <PathGeometry>M0.503906 4.5C0.640625 4.5 0.757813 4.45117 0.855469 4.35352L4.00195 1.20703L7.14844 4.35352C7.24609 4.45117 7.36328 4.5 7.5 4.5C7.63672 4.5 7.75391 4.45313 7.85156 4.35938C7.95312 4.26172 8.00391 4.14258 8.00391 4.00195C8.00391 3.86523 7.95508 3.74805 7.85742 3.65039L4.35352 0.146484C4.25586 0.0488281 4.13867 -5.97617e-09 4.00195 0C3.86523 5.97617e-09 3.74805 0.0488281 3.65039 0.146484L0.146484 3.65039C0.0488281 3.74805 -5.97617e-09 3.86523 0 4.00195C6.14691e-09 4.14258 0.0488281 4.26172 0.146484 4.35938C0.248047 4.45312 0.367188 4.5 0.503906 4.5Z</PathGeometry>
                                                            </Path.Data>
                                                            <Path.RenderTransform>
                                                                <TransformGroup>
                                                                    <ScaleTransform />
                                                                    <SkewTransform />
                                                                    <RotateTransform Angle="0" />
                                                                    <TranslateTransform />
                                                                </TransformGroup>
                                                            </Path.RenderTransform>
                                                        </Path>
                                                    </RepeatButton>
                                                    <RepeatButton x:Name="FromPage_downbutton"
                                                                  Grid.Row="2" 
                                                                  Grid.Column="1"
                                                                  Width="15"
                                                                  Focusable="{TemplateBinding Focusable}"
                                                                  IsTabStop="False"
                                                                  Margin=".5,0,.5,.5" 
                                                                  Style="{StaticResource WPFGlyphRepeatButtonStyle}">
                                                        <Path x:Name="FromPage_downbuttonpath"
                                                              Height="5" 
                                                              Width="9"
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                                              Stretch="None"
                                                              Margin="-1">
                                                            <Path.Data>
                                                                <PathGeometry>M7.5 0C7.36328 0 7.24609 0.0488281 7.14844 0.146484L4.00195 3.29297L0.855469 0.146484C0.757812 0.0488281 0.640625 0 0.503906 0C0.367188 0 0.25 0.046875 0.152344 0.140625C0.0507812 0.238281 0 0.357422 0 0.498047C0 0.634766 0.0488281 0.751953 0.146484 0.849609L3.65039 4.35352C3.74805 4.45117 3.86523 4.5 4.00195 4.5C4.13867 4.5 4.25586 4.45117 4.35352 4.35352L7.85742 0.849609C7.95508 0.751953 8.00391 0.634766 8.00391 0.498047C8.00391 0.357422 7.95508 0.238281 7.85742 0.140625C7.75586 0.046875 7.63672 0 7.5 0Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </RepeatButton>
                                                </Grid>
                                            </Border>
                                        </Grid>
                                        <TextBlock x:Name="Part_To" 
                                                   HorizontalAlignment="Left" 
                                                   VerticalAlignment="Center" 
                                                   Text="{resources:SharedLocalizationResourceExtension ResourceName=PrintPreview_To}" 
                                                   Style="{StaticResource WPFTextBlockStyle}">
                                        </TextBlock>
                                        <Grid x:Name="Part_ToPage_Grid"
                                              Height="24"
                                              Width="60"
                                              HorizontalAlignment="Left" 
                                              VerticalAlignment="Top"
                                              Margin="5,0,8,0" >
                                            <Border x:Name="ToPage_Border"  
                                                    Width="{TemplateBinding Width}"  
                                                    BorderThickness="1" 
                                                    CornerRadius="{StaticResource Windows11Dark.CornerRadius4}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    Background="{TemplateBinding Background}"                                                    
                                                    SnapsToDevicePixels="True">
                                                <Grid  ClipToBounds="True">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"></ColumnDefinition>
                                                        <ColumnDefinition Width="Auto">
                                                        </ColumnDefinition>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition></RowDefinition>
                                                        <RowDefinition></RowDefinition>
                                                    </Grid.RowDefinitions>
                                                    <TextBox x:Name="Part_ToPageTextBox"
                                                             Grid.Column="0"
                                                             Grid.RowSpan="2"                                                             
                                                             BorderThickness="{StaticResource Windows11Dark.BorderThickness}"
                                                             Width="{TemplateBinding Width}"
                                                             Height="{TemplateBinding Height}"
                                                             HorizontalContentAlignment="Left" 
                                                             VerticalContentAlignment="Center"
                                                             Style="{StaticResource WPFTextBoxStyle}"
                                                             Text="{Binding ToPage,Mode=TwoWay}"/>
                                                    <RepeatButton x:Name="ToPage_upbutton" 
                                                                  Grid.Row="0" 
                                                                  Grid.Column="1"
                                                                  Width="15"
                                                                  Focusable="{TemplateBinding Focusable}" 
                                                                  IsTabStop="False"
                                                                  Margin=".5,1,.5,0" 
                                                                  Style="{StaticResource WPFGlyphRepeatButtonStyle}">
                                                        <Path x:Name="ToPage_upbuttonpath"
                                                              Height="5" 
                                                              Width="9"
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}" 
                                                              Stretch="None"
                                                              RenderTransformOrigin=".5,.5"
                                                              Margin="-1">
                                                            <Path.Data>
                                                                <PathGeometry>M0.503906 4.5C0.640625 4.5 0.757813 4.45117 0.855469 4.35352L4.00195 1.20703L7.14844 4.35352C7.24609 4.45117 7.36328 4.5 7.5 4.5C7.63672 4.5 7.75391 4.45313 7.85156 4.35938C7.95312 4.26172 8.00391 4.14258 8.00391 4.00195C8.00391 3.86523 7.95508 3.74805 7.85742 3.65039L4.35352 0.146484C4.25586 0.0488281 4.13867 -5.97617e-09 4.00195 0C3.86523 5.97617e-09 3.74805 0.0488281 3.65039 0.146484L0.146484 3.65039C0.0488281 3.74805 -5.97617e-09 3.86523 0 4.00195C6.14691e-09 4.14258 0.0488281 4.26172 0.146484 4.35938C0.248047 4.45312 0.367188 4.5 0.503906 4.5Z</PathGeometry>
                                                            </Path.Data>
                                                            <Path.RenderTransform>
                                                                <TransformGroup>
                                                                    <ScaleTransform />
                                                                    <SkewTransform />
                                                                    <RotateTransform Angle="0" />
                                                                    <TranslateTransform />
                                                                </TransformGroup>
                                                            </Path.RenderTransform>
                                                        </Path>
                                                    </RepeatButton>
                                                    <RepeatButton x:Name="ToPage_downbutton"
                                                                  Grid.Row="2" 
                                                                  Grid.Column="1"
                                                                  Width="15"
                                                                  Focusable="{TemplateBinding Focusable}"
                                                                  IsTabStop="False"  
                                                                  Margin=".5,0,.5,.5" 
                                                                  Style="{StaticResource WPFGlyphRepeatButtonStyle}">
                                                        <Path x:Name="ToPage_downbuttonpath"
                                                              Height="5" 
                                                              Width="9"
                                                              HorizontalAlignment="Center" 
                                                              VerticalAlignment="Center"
                                                              Fill="{Binding RelativeSource={RelativeSource Mode=Self}, Path=(TextBlock.Foreground)}"
                                                              Stretch="None"
                                                              Margin="-1">
                                                            <Path.Data>
                                                                <PathGeometry>M7.5 0C7.36328 0 7.24609 0.0488281 7.14844 0.146484L4.00195 3.29297L0.855469 0.146484C0.757812 0.0488281 0.640625 0 0.503906 0C0.367188 0 0.25 0.046875 0.152344 0.140625C0.0507812 0.238281 0 0.357422 0 0.498047C0 0.634766 0.0488281 0.751953 0.146484 0.849609L3.65039 4.35352C3.74805 4.45117 3.86523 4.5 4.00195 4.5C4.13867 4.5 4.25586 4.45117 4.35352 4.35352L7.85742 0.849609C7.95508 0.751953 8.00391 0.634766 8.00391 0.498047C8.00391 0.357422 7.95508 0.238281 7.85742 0.140625C7.75586 0.046875 7.63672 0 7.5 0Z</PathGeometry>
                                                            </Path.Data>
                                                        </Path>
                                                    </RepeatButton>
                                                </Grid>
                                            </Border>
                                        </Grid>
                                    </StackPanel>
                                    <ComboBox x:Name="PART_Collated"
                                              HorizontalAlignment="Left"
                                              SelectedValue="{Binding SelectedCollation, Mode=TwoWay}"
                                              SelectedValuePath="Collation"
                                              ItemsSource="{Binding PrintPageCollationOptions}"                                              
                                              ItemTemplate="{StaticResource CollatedTemplate}"
                                              Style="{StaticResource Print_ComboBoxStyle}">
                                        <ComboBox.Margin>
                                            <Thickness Left="0" Top="4" Right="0" Bottom="4"/>
                                        </ComboBox.Margin>
                                    </ComboBox>
                                    <ComboBox x:Name="PART_OrientationComboBox"
                                              HorizontalAlignment="Left"
                                              SelectedValue="{Binding PrintOrientation, Mode=TwoWay}"
                                              SelectedValuePath="Orientation"
                                              ItemsSource="{Binding OrientationOptions}"
                                              ItemTemplate="{StaticResource OrientationTemplate}"
                                              Style="{StaticResource Print_ComboBoxStyle}">
                                        <ComboBox.Margin>
                                            <Thickness Left="0" Top="4" Right="0" Bottom="4"/>
                                        </ComboBox.Margin>
                                    </ComboBox>
                                    <Grid>
                                        <ComboBox x:Name="PART_PapersComboBox"
                                                  HorizontalAlignment="Left"
                                                  ItemTemplate="{StaticResource PageSizeTemplate}"
                                                  ItemsSource="{Binding PageSizeOptions}"
                                                  Style="{StaticResource Print_ComboBoxStyle}"
                                                  SelectedItem="{Binding SelectedPageSize}">
                                            <ComboBox.Margin>
                                                <Thickness Left="0" Top="4" Right="0" Bottom="4"/>
                                            </ComboBox.Margin>
                                        </ComboBox>
                                        <Grid Width="Auto"
                                              Height="Auto">
                                            <TextBlock IsHitTestVisible="False"
                                                       VerticalAlignment="Center"
                                                       Margin="10,0,0,10">
                                                <TextBlock.Style>
                                                    <Style TargetType="{x:Type TextBlock}" BasedOn="{StaticResource WPFTextBlockStyle}">
                                                        <Setter Property="Text" 
                                                                Value="Choose any PageSize"/>
                                                        <Setter Property="Visibility"
                                                                Value="Hidden"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Path=Items.Count, ElementName=PART_PapersComboBox}" 
                                                                         Value="0">
                                                                <Setter Property="Text" 
                                                                        Value="Unable to connect"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Path=SelectedIndex, ElementName=PART_PapersComboBox}"
                                                                         Value="-1">
                                                                <Setter Property="Visibility"
                                                                        Value="Visible"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Grid>
                                    </Grid>
                                    <ComboBox x:Name="PART_MarginComboBox"
                                              HorizontalAlignment="Left"
                                              ItemTemplate="{StaticResource MarginTemplate}"
                                              ItemsSource="{Binding MarginOptions}"
                                              Style="{StaticResource Print_ComboBoxStyle}">
                                        <ComboBox.Margin>
                                            <Thickness Left="0" Top="4" Right="0" Bottom="4"/>
                                        </ComboBox.Margin>
                                    </ComboBox>
                                    <ComboBox x:Name="PART_ScaleOptionComboBox"
                                              HorizontalAlignment="Left"
                                              SelectedIndex="{Binding SelectedScaleIndex}"
                                              ItemTemplate="{StaticResource ScaleOptionTemplate}"
                                              ItemsSource="{Binding ScaleOptions}"
                                              Style="{StaticResource Print_ComboBoxStyle}">
                                        <ComboBox.Margin>
                                            <Thickness Left="0" Top="4" Right="0" Bottom="4"/>
                                        </ComboBox.Margin>
                                    </ComboBox>
                                </StackPanel>
                            </Grid>
                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionPrintOptionsControlStyle}" TargetType="{x:Type PrintPreview_printing:PrintOptionsControl}" />

    <Style x:Key="SyncfusionPrintPreviewAreaControlStyle" TargetType="{x:Type PrintPreview_printing:PrintPreviewAreaControl}">
        <Setter Property="Background"
                Value="{StaticResource ContentBackgroundAlt3}" />
        <Setter Property="Focusable"
                Value="False"/>
        <Setter Property="Margin"
                Value="5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type PrintPreview_printing:PrintPreviewAreaControl}">
                    <Border Margin="{TemplateBinding Margin}" 
                            Background="{TemplateBinding Background}">
                        <ScrollViewer CanContentScroll="True"
                                      HorizontalScrollBarVisibility="Auto"
                                      VerticalScrollBarVisibility="Auto"
                                      Style="{StaticResource WPFScrollViewerStyle}">
                            <PrintPreview_printing:PrintPreviewPanel x:Name="PART_PrintPreviewPanel"
                                                                     Background="{StaticResource ContentBackgroundAlt3}"/>
                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionPrintPreviewAreaControlStyle}" TargetType="{x:Type PrintPreview_printing:PrintPreviewAreaControl}" />

    <Style x:Key="SyncfusionPrintPreviewStyle" TargetType="{x:Type PrintPreview_printing:PrintPreview}">
        <Setter Property="Background" 
                Value="{StaticResource ContentBackground}" />
        <Setter Property="Focusable"
                Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type PrintPreview_printing:PrintPreview}">
                    <Grid Background="{StaticResource ContentBackground}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="250" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <PrintPreview_printing:PrintOptionsControl x:Name="PART_PrintOptionsControl"
                                                               DataContext="{Binding ElementName=PART_PrintOptionsControl}"/>
                        <Grid Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="40" />
                            </Grid.RowDefinitions>
                            <PrintPreview_printing:PrintPreviewAreaControl x:Name="PART_PrintPreviewAreaControl" />
                            <Grid Grid.Row="1"
                                  DataContext="{Binding ElementName=PART_PrintPreviewAreaControl}">
                                <Border BorderBrush="{StaticResource BorderAlt}"
                                        BorderThickness="0,1,0,0" Margin="0,2,0,0">
                                    <DockPanel Height="32" VerticalAlignment="Center"> 
                                        <StackPanel HorizontalAlignment="Left"
                                                VerticalAlignment="Center"
                                                DockPanel.Dock="Left"
                                                Orientation="Horizontal"
                                                Margin="15,0,0,0">
                                            <Button x:Name="FirstPageButton"
                                                Margin="2,0,0,0"
                                                Height="24"
                                                Width="24"
                                                Command="{Binding FirstCommand}"
                                                Style="{StaticResource WPFGlyphButtonStyle}">
                                                <Path Width="14"
                                                  Height="15"
                                                  Fill="{Binding Path=Foreground,RelativeSource={RelativeSource AncestorType={x:Type Button}}}">
                                                    <Path.Data>
                                                        <PathGeometry>M1 0.5C1 0.223858 0.776142 0 0.5 0C0.223858 0 0 0.223858 0 0.5V3V4V14.5C0 14.7761 0.223858 15 0.5 15C0.776142 15 1 14.7761 1 14.5V4V3V0.5ZM4.00152 7.48905L13 1.51786L13 13.4821L4.00144 7.5109L4.00102 7.50933C4.00044 7.50689 4 7.50367 4 7.5C4 7.49633 4.00044 7.49311 4.00102 7.49067L4.00152 7.48905ZM3.4486 8.34418C2.85047 7.94727 2.85047 7.05273 3.4486 6.65582L12.478 0.664101C13.1335 0.229132 14 0.709744 14 1.50828V13.4917C14 14.2903 13.1335 14.7709 12.478 14.3359L3.4486 8.34418Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Button>
                                            <Button x:Name="PerviousPageButton"
                                                Margin="5,0,0,0"
                                                Height="24"
                                                Width="24"
                                                Command="{Binding PreviousCommand}"
                                                Style="{StaticResource WPFGlyphButtonStyle}">
                                                <Path Height="14"
                                                      Width="11"
                                                      Stroke="{Binding Path=Foreground,RelativeSource={RelativeSource AncestorType={x:Type Button}}}">
                                                    <Path.Data>
                                                        <PathGeometry>M0.72506 7.42757C0.42498 7.22844 0.42498 6.77156 0.72506 6.57243L9.75446 0.580719C10.0613 0.377118 10.5 0.588873 10.5 1.00828L10.5 12.9917C10.5 13.4111 10.0613 13.6229 9.75446 13.4193L0.72506 7.42757Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Button>
                                            <TextBox x:Name="PART_TextBox"
                                                 Margin="2,0,0,0"
                                                 Width="24"
                                                 Height="16"    
                                                 BorderThickness="{StaticResource Windows11Dark.ThemeBorderThicknessVariant1}"
                                                 HorizontalAlignment="Center"
                                                 VerticalAlignment="Center"
                                                 HorizontalContentAlignment="Left"
                                                 VerticalContentAlignment="Center"
                                                 Text="{Binding PageIndex}"
                                                 Style="{StaticResource WPFTextBoxStyle}">
                                            </TextBox>
                                            <TextBlock x:Name="PART_TextBlock" 
                                                   HorizontalAlignment="Center"                                                   
                                                   VerticalAlignment="Center"
                                                   Width="24"
                                                   Text="{Binding TotalPages, StringFormat= /  {0}}"
                                                   Style="{StaticResource WPFTextBlockStyle}"
                                                   TextAlignment="Center"
                                                   Margin="2,0,0,0">
                                            </TextBlock>
                                            <Button x:Name="NextPageButton"
                                                Margin="5,0,0,0"
                                                Height="24"
                                                Width="24"
                                                Command="{Binding NextCommand}"
                                                Style="{StaticResource WPFGlyphButtonStyle}">
                                                <Path Height="14"
                                                      Width="11"
                                                      Stroke="{Binding Path=Foreground,RelativeSource={RelativeSource AncestorType={x:Type Button}}}">
                                                    <Path.Data>
                                                        <PathGeometry>M10.2749 6.57244C10.575 6.77156 10.575 7.22844 10.2749 7.42757L1.24553 13.4193C0.938713 13.6229 0.5 13.4111 0.5 12.9917L0.500001 1.00828C0.500001 0.588873 0.938712 0.377118 1.24554 0.580719L10.2749 6.57244Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Button>
                                            <Button x:Name="LastPageButton"
                                                Margin="5,0,0,0"
                                                Height="24"
                                                Width="24"
                                                Command="{Binding LastCommand}"
                                                Style="{StaticResource WPFGlyphButtonStyle}">
                                                <Path Width="14"
                                                  Height="15"
                                                  Fill="{Binding Path=Foreground,RelativeSource={RelativeSource AncestorType={x:Type Button}}}">
                                                    <Path.Data>
                                                        <PathGeometry>M13 0.5C13 0.223862 13.2239 0 13.5 0C13.7761 0 14 0.223862 14 0.5V3V4V14.5C14 14.7761 13.7761 15 13.5 15C13.2239 15 13 14.7761 13 14.5V4V3V0.5ZM9.99848 7.48906L1 1.51785L1 13.4821L9.99856 7.51089L9.99898 7.50932C9.99956 7.50688 10 7.50368 10 7.5L9.99979 7.49564C9.99961 7.49377 9.99932 7.4921 9.99898 7.49068L9.99848 7.48906ZM10.5514 8.34418C11.1495 7.94728 11.1495 7.05272 10.5514 6.65582L1.52199 0.664108C0.866505 0.229126 9.53674e-07 0.709747 9.53674e-07 1.50829L0 13.4917C0 14.2903 0.866505 14.7709 1.52199 14.3359L10.5514 8.34418Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Button>
                                        </StackPanel>
                                        <Grid HorizontalAlignment="Right" DockPanel.Dock="Right" Margin="0,0,15,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <TextBlock x:Name="PART_ZoomPercentage"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center" 
                                                   Text="{Binding ElementName=PART_ZoomSlider, Path=Value, StringFormat={}{0:#}%}"
                                                   Style="{StaticResource WPFTextBlockStyle}">
                                            </TextBlock>
                                            <Button x:Name="PART_PlusZoomButton"                                                
                                                Grid.Column="3"
                                                Width="24"
                                                Height="24"
                                                Focusable="False"
                                                HorizontalAlignment="Center"
                                                Style="{StaticResource WPFGlyphButtonStyle}">
                                                <Button.Margin>
                                                    <Thickness>0,0,0,0</Thickness>
                                                </Button.Margin>
                                                <Path x:Name="pluspath"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center" 
                                                  Width="15" 
                                                  Height="15"
                                                  Fill="{Binding Path=Foreground,RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                  Stretch="Fill" 
                                                  SnapsToDevicePixels="True">
                                                    <Path.Data>
                                                        <PathGeometry>M7.5 1C3.91015 1 1 3.91015 1 7.5C1 11.0899 3.91015 14 7.5 14C11.0899 14 14 11.0899 14 7.5C14 3.91015 11.0899 1 7.5 1ZM0 7.5C0 3.35786 3.35786 0 7.5 0C11.6421 0 15 3.35786 15 7.5C15 11.6421 11.6421 15 7.5 15C3.35786 15 0 11.6421 0 7.5ZM7.5 4C7.77614 4 8 4.22386 8 4.5V7H10.5C10.7761 7 11 7.22386 11 7.5C11 7.77614 10.7761 8 10.5 8H8V10.5C8 10.7761 7.77614 11 7.5 11C7.22386 11 7 10.7761 7 10.5V8H4.5C4.22386 8 4 7.77614 4 7.5C4 7.22386 4.22386 7 4.5 7H7V4.5C7 4.22386 7.22386 4 7.5 4Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Button>
                                            <Slider x:Name="PART_ZoomSlider"
                                                Grid.Column="2"
                                                Width="150"
                                                Height="24"
                                                Maximum="500"
                                                Minimum="10"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                HorizontalContentAlignment="Center"
                                                Orientation="Horizontal"
                                                Focusable="False"
                                                IsMoveToPointEnabled="True"
                                                Value="{Binding ZoomFactor}"
                                                Style="{StaticResource WPFSliderStyle}">
                                                <Slider.Margin>
                                                    <Thickness Left="0" Top="0" Right="0" Bottom="0"/>
                                                </Slider.Margin>
                                            </Slider>
                                            <Button x:Name="PART_MinusZoomButton"
                                                Grid.Column="1"
                                                Width="24"
                                                Height="24"
                                                Focusable="False"
                                                HorizontalAlignment="Center"
                                                Style="{StaticResource WPFGlyphButtonStyle}">
                                                <Button.Margin>
                                                    <Thickness>0,0,0,0</Thickness>
                                                </Button.Margin>
                                                <Path x:Name="minuspath"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center" 
                                                      Width="14" 
                                                      Height="14"
                                                      Fill="{Binding Path=Foreground,RelativeSource={RelativeSource AncestorType={x:Type Button}}}"
                                                      Stretch="Fill" 
                                                      SnapsToDevicePixels="True">
                                                    <Path.Data>
                                                        <PathGeometry>M10.5 7C10.5 7.27614 10.2761 7.5 10 7.5L4 7.5C3.72386 7.5 3.5 7.27614 3.5 7C3.5 6.72386 3.72386 6.5 4 6.5L10 6.5C10.2761 6.5 10.5 6.72386 10.5 7ZM7 13C10.3137 13 13 10.3137 13 7C13 3.68629 10.3137 1 7 1C3.68629 1 1 3.68629 1 7C1 10.3137 3.68629 13 7 13ZM7 14C10.866 14 14 10.866 14 7C14 3.13401 10.866 0 7 0C3.13401 0 0 3.13401 0 7C0 10.866 3.13401 14 7 14Z</PathGeometry>
                                                    </Path.Data>
                                                </Path>
                                            </Button>
                                        </Grid>
                                    </DockPanel>
                                </Border>
                            </Grid>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource SyncfusionPrintPreviewStyle}" TargetType="{x:Type PrintPreview_printing:PrintPreview}" />
    
</ResourceDictionary>
